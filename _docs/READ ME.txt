DAFARE:

VIDEOMATCH:
- DISTRIBUISCI MASCHERA su VideoMatch con alert quando c'è una maschera aggiornata e ti chiede di fare download
- report caricamento report partite su VMUpload ad ogni pubblica

SICS.TV

- VIDEO.JSP: caricamento video streaming da wowza
- VIDEO.JSP: implementare player video jwplayer ?? video.js ??

- mysics.tv verificare layout ipad
- metti offcanvas in versione mobile di mysics.tv con lista file che se clicco mi apre il video senza dover tornare alla pagina precedente
- prima pagina sempre in inglese

- informazione giocatore(ANNO,piede,altezza,ecc) grabber da transfermarkt
- ricerca giocatore in home con lista giocatori che corrispondono e poi selezione cosa voglio vedere(tiri,passaggi,assist,dribbling,ecc)
- icona che indica il tipo di tagging che differenzia noi da instat

- utente su database vm+protezione specifico per sicstv solo lettura (verificare scritture)
- cache viene svuotata ogni settimana facendo la rotazione dal più vecchio
- file in mysics.tv con scadenza di una settimana
- impostazione ogni quanto eliminare i file(1 settimana, 1 mese,3 mesi)
- dividere lega pro per girone


FATTO

11/8/2017
OK - notifica presenza nuovi video in mysicstv con uk-badge su voce menu
OK - inserita gestione palylist da vedere
OK - corretto errore multipartita che in alto metteva sempre juventus
OK - apertura multipartita che individua al squadra più frequente, la mette per prima e filtra già le azioni

10/8/2017
OK - ricerca giocatori e squadre in alto e tolto dai filtri a destra
OK - iniziata segnalazione nuovi video in mysicstv

9/8/2017
OK - icona esportazione in corso metto uno spinner e con il tooltip mostro la percentuale di avanzamento
OK - togli icona esportazione quando ha finito di esportare
OK - fix ricerca squadre che selezionava una suqadra alla volta
OK - fix ricerca suqadra con nome con spazio che non funzionava (PRO VERCELLI)

8/8/2017
OK - team-partite attivare paginazione

7/8/2017
OK - pagina team-"seriea" attivare paginazione
OK - avanzamento con durata clip in play

3/8/2017
OK - clip 1 di 3 quando mando in play
OK - cambia visualizzazione dati playlist in basso
OK - modalità cinema in mysicstv
OK - paginazione eventi
OK - reset mettere icona clean
OK - vedi errore font

2/8/2017
OK - team-tab tagliate/invertite ??
OK - partite disabilitare successiva precedente se non presenti
OK - verificare colori uk-badge su hover invertire colori back-front
OK - Ricerca bottoni selezionati devono rimanere arancioni e attivare badge come su avanzata
OK - icone mysicstv file-zip-o  youtube-play
OK - se non analizzata non metto il tabellino
OK - sistemare conteggio Vittorie: 0 Pareggi: 0 Sconfitte: 0 per tutte le partite non solo paginate
OK - tabellino adattare in base alle info presenti
OK - fix vittorie-pareggi-sconfitte IN team.jsp

1/8/2017
OK - playlist per desktop metto lista a destra e visualizzo a sinistra mentre mobiel lascia com'è
OK - paginazione lista partite

31/7/2017
OK - paginazione lista partite con uikit 25righe ??
OK - estratta stagione 
OK -logo su mobile a sx

28/7/2017
OK - tieni selezionate le azioni E METTI iconcina che indica che è quella in play
OK - tooltip su cinema e hd
OK - tooltip sulla descrizione visualizza la coda delle azioni 
OK - metti il nome dell'azione che stai riproducendo sopra al video
OK - metti nome partita di nuovo in alto
OK - togli hd cinema da mobile
OK - reti off dif in orizzontale

27/7/2017
OK - aggiungere riga con filtro selezionato (da data 20/07/2017, giocatore GAGLIARDINI, ...)
OK - verificare tempo caricamento pagina partita
OK - hover pulsanti giocatori/ricerca in grigio chiaro (come hover lista partite)
OK - in video ci sono le check per aprire multipartita ma non si può (c'è la stessa icona sia per multipartita che per esporta)

26/7/2017
OK - limitare accesso nel caso di modifica dell'url
OK - font diverso su competizioni disabilitate
OK - tooltip su pulsante apertura sottomenu partita (play, download...)
OK - tooltip su riquadro competizione (es. supercoppa italiana)

25/7/2017
OK - video.htm modificare icone in base a selezione tab azioni, partite, calendario
OK - video.htm visualizzare nome partita sopra il video SEMPRE
OK - video.htm icone (hd, cinema, tutto schermo) cambiare in button (come le altre icone) sopra div video e a destra del nome partita se multipartita
OK - scroll partite su homepage contestuale come su pagina partita
OK - hover su sottomenu partita (play, download...)
OK - video.htm pulsanti giocatori/ricerca selezionati in arancione
OK - video.htm correggere tooltip visualizzazione azione
OK - visualizza colonna autore solo se ci sono azioni personali
OK - metti in ordine i pulsanti dei fondamentali
OK - metti esito nella tabella
OK - verificare filtro per modulo
OK - video.htm  togliere contorno stile bottone da Vittorie: 29 Pareggi: 4 Sconfitte: 5
OK - video.htm  togliere hover arancione su bottone giocatore finchè non attiviamo statistiche giocatore
OK - icona loading su calendario modificare sfondo

20/7/2017
OK - due video in play
OK - corretto caricamento pagine video

19/7/2017
OK - gestione variabili di sistema
OK - download del ReportADD:
OK - verifica esportazione su quello caricato
OK - togli apicetti e sostituisci spazi nelle path con underscore

18/7/2017
OK - ospiti non vede il file personale
OK - autore come colonna nella lista azioni
OK - se una partita è taggata solo da un maschera personale faccio vedere solo i tasti di quella maschera

14/7/2017
OK - quando clicco sul pulsante mi seleziona tutti i tag
OK - visualizzare solo i bottoni che sono stati utilizzati nel tagging

13/7/2017
OK - task di esportazione rimanga visibile in tutte le pagine
ok - multipartita

12/7/2017
OK - togli nome playlist
OK - mysics.tv elenco

11/07/2017
OK - fix play video in hd anche al cambio video
OK - fix selezione esportazione in HD  o SD in base alla risoluzione più frequente 

06/07/2017
OK - modalità cinema
OK - completare cambio da jwPlayer a HTML5

05/07/2017
OK - i file temporanei utilizzati per le esportazioni li elimino dopo il buon esito dell'upload
OK - video HTML5

04/7/2017
OK - scritta in preparazione per quanto riguarda le azioni in coda
OK - stato esportazione in alto a destra con un iconcina che se cliccata mostra modale in cui visualizzo lo stato di ciascuna esportazione in corso

03/7/2017
OK - aggiungere colonna type nel db per differenziare i file mp4 dagli zip 
OK - data creazione in db con ora
OK - ora in mysics.tv 
OK - finestra esportazione mettera radio con : SD e HD
OK - aggiungere play in mysics.tv
OK - intestazione in area personale con data,nome,autore,download

29/6/2017
OK - progress bar in esporta vicino ad "Elaborazione  in corso..."
OK - esporta il montaggio video che mette in mysics.TV
OK - esporta che chiede: nome, qualità e (uniti o separati) ok ed elabora. finito  mette in mysics.TV che segnala quando ha finito con finestra modale
OK - statistiche squadra su team.jsp (rosa, v-n-p)?
OK - thread per fare l'export che mette in una coda ed elabora. Il numero di thread utilizzati come parametro

16/6/2017	
OK - vedi di non fare sempre query ma memorizza sul browser

12/6/2017
OK - inserire seleziona/deseleziona tutto sugli eventi
OK - tab competizioni/Serie a/calendario in competizione
OK - aggiungere tab ricerca con eventi principali 

9/6/2017
OK - rinomina eventi in avanzata
OK - play di tutto
OK - verifica partenza video sd o hd e ricorda ultima selezione

8/6/2017
OK - checkbox azioni partita
OK - download scelta hd o sd
OK - video nomepartita su una sola riga
OK - calendario e partite nelle varie pagine
OK - tabellino sistemare campetto

7/6/2017
OK - RICERCA per squadra, ecc
OK - ricerca incrociata
OK - togliere goalù
OK - nascondi multipartita e checkbox nascosti

OK - video.jsp vedere di gestire resize che mette sotto gli eventi quando si stringe troppo
OK - scroll che tiene parte sinistra ferma in video.jsp
OK - simbolo HD e taggato nella lista partite

OK - aggiungi prodotto sicsTV su pdata con pcode suo (600)
OK - copia utenti da VM a nuovo prodotto sics.Tv
OK - dopo aver fatto login dico che è scaduto e faccio logout
OK - metti messaggio quando mancano 7 giorni alla scadenza
OK - aggiungi colonna serialsicstv