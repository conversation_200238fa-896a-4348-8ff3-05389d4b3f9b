<VideoMatch>
  <EVENTS>
    <EVENT desc="Attacco" endesc="OFFENSIVE\nACTION" esdesc="OFFENSIVE\nACTION" cod="ATT" sh="65" vis="True" oneclick="False" associato="DIF" avversario="True" anticipo="0.0" durata="0.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Rimessa Laterale" endesc="THOW-IN" cod="ATT-100" associato="DIF-100" group="3" default="false" />
        <TAG desc="Rimessa Fondo" endesc="RIMESSA FONDO" cod="ATT-101" associato="DIF-101" group="3" default="false" />
        <TAG desc="<PERSON><PERSON><PERSON> vs Uomo" endesc="SET vs MTM" cod="ATT-102" associato="DIF-102" group="1" default="false" />
        <TAG desc="Giochi vs Zona" endesc="SET vs ZONE" cod="ATT-103" associato="DIF-103" group="1" default="false" />
        <TAG desc="Contropiede" endesc="FB" cod="ATT-104" associato="DIF-104" group="2" default="false" />
        <TAG desc="Attacco veloce" endesc="EARLY OFFENSE" cod="ATT-105" associato="DIF-105" group="2" default="false" />
        <TAG desc="Pressing tutto campo" endesc="FCP" cod="ATT-106" associato="DIF-106" group="2" default="false" />
        <TAG desc="Pressing a Zona" endesc="ZONE PRESS" cod="ATT-107" associato="DIF-107" group="2" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Difesa" endesc="DEFENSIVE\nACTION" esdesc="DIFESA" cod="DIF" sh="68" vis="True" oneclick="False" associato="ATT" avversario="True" anticipo="0.0" durata="0.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Rimessa Laterale" endesc="THOW-IN" cod="DIF-100" associato="ATT-100" group="3" default="false" />
        <TAG desc="Rimessa Fondo" endesc="RIMESSA FONDO" cod="DIF-101" associato="ATT-101" group="3" default="false" />
        <TAG desc="Difesa a Uomo" endesc="MTM" cod="DIF-102" associato="ATT-102" group="1" default="false" />
        <TAG desc="Zona" endesc="ZONA" cod="DIF-103" associato="ATT-103" group="1" default="false" />
        <TAG desc="Contropiede" endesc="FB" cod="DIF-104" associato="ATT-104" group="2" default="false" />
        <TAG desc="Attacco veloce" endesc="EARLY OFFENSE" cod="DIF-105" associato="ATT-105" group="2" default="false" />
        <TAG desc="Pressing tutto campo" endesc="FCP" cod="DIF-106" associato="ATT-106" group="2" default="false" />
        <TAG desc="Pressing a Zona" endesc="ZONE PRESS" cod="DIF-107" associato="ATT-107" group="2" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Rimessa" endesc="RIMESSA" esdesc="RIMESSA" cod="RIM" sh="" vis="True" oneclick="False" associato="RID" avversario="True" anticipo="0.0" durata="0.0" sics="False" backgroundColor="255x0x0x255" textColor="255x255x255x255" esito="2" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Laterale" endesc="LATERALE" cod="RIM-100" associato="RID-100" group="1" default="false" />
        <TAG desc="Fondo" endesc="FONDO" cod="RIM-101" associato="RID-101" group="1" default="false" />
        <TAG desc="0''-8''" endesc="0''-8''" cod="RIM-102" associato="RID-102" group="2" default="false" />
        <TAG desc="8''-18''" endesc="8''-16''" cod="RIM-103" associato="RID-103" group="2" default="false" />
        <TAG desc="18''-24''" endesc="16''-24''" cod="RIM-104" associato="RID-104" group="2" default="false" />
        <TAG desc="Uscita Time Out" endesc="USCITA TIME OUT" cod="RIM-105" associato="RID-105" group="1" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Rimessa D" endesc="RIMESSA D" esdesc="RIMESSA" cod="RID" sh="" vis="True" oneclick="False" associato="RIM" avversario="True" anticipo="0.0" durata="0.0" sics="False" backgroundColor="255x0x0x255" textColor="255x255x255x255" esito="2" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Laterale" endesc="LATERALE" cod="RID-100" associato="RIM-100" group="1" default="false" />
        <TAG desc="Fondo" endesc="FONDO" cod="RID-101" associato="RIM-101" group="1" default="false" />
        <TAG desc="0''-8''" endesc="0''-8''" cod="RID-102" associato="RIM-102" group="2" default="false" />
        <TAG desc="8''-18''" endesc="8''-16''" cod="RID-103" associato="RIM-103" group="2" default="false" />
        <TAG desc="18''-24''" endesc="16''-24''" cod="RID-104" associato="RIM-104" group="2" default="false" />
        <TAG desc="Uscita Time Out" endesc="USCITA TIME OUT" cod="RID-105" associato="RIM-105" group="1" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Tiro" endesc="SHOT" esdesc="TIRO" cod="TF" sh="" vis="True" oneclick="False" associato="TS" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="1" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Piazzato" endesc="SPOT" cod="TF-100" associato="TS-100" group="2" default="false" />
        <TAG desc="Palleggio arresto e tiro" endesc="JUMPER" cod="TF-101" associato="TS-101" group="2" default="false" />
        <TAG desc="Da sotto da penetrazione" endesc="LAY-UP" cod="TF-102" associato="TS-102" group="2" default="false" />
        <TAG desc="Uscita dai blocchi" endesc="OFF SCREEN" cod="TF-103" associato="TS-103" group="2" default="false" />
        <TAG desc="Schiacciata" endesc="DUNK" cod="TF-104" associato="TS-104" group="2" default="false" />
        <TAG desc="Contropiede" endesc="FB" cod="TF-105" associato="TS-105" group="2" default="false" />
        <TAG desc="Rimbalzo Offensivo" endesc="OFF REBS" cod="TF-106" associato="TS-106" group="2" default="false" />
        <TAG desc="0''-8''" endesc="0'-8'" cod="TF-107" associato="TS-107" group="3" default="false" />
        <TAG desc="8''-18''" endesc="8'-16'" cod="TF-108" associato="TS-108" group="3" default="false" />
        <TAG desc="18''-24''" endesc="16''-24''" cod="TF-109" associato="TS-109" group="3" default="false" />
        <TAG desc="Mano Dx" endesc="MANO DX" cod="TF-111" associato="TS-111" group="1" default="false" />
        <TAG desc="Mano Sx" endesc="MANO SX" cod="TF-112" associato="TS-112" group="1" default="false" />
        <TAG desc="Da sotto da passaggio" endesc="Lay-Up da Passaggio" cod="TF-113" associato="TS-113" group="2" default="false" />
        <TAG desc="Gancetto" endesc="BABY HOOK" cod="TF-114" associato="TS-114" group="2" default="false" />
        <TAG desc="Gancio Cielo" endesc="HOOK" cod="TF-115" associato="TS-115" group="2" default="false" />
        <TAG desc="Da Seconda Opportunità" endesc="Da Seconda Opportunità" cod="TF-116" associato="TS-116" group="4" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Tiro Libero" endesc="TIRO LIBERO" esdesc="TIRO LIBERO" cod="TLB" sh="" vis="True" oneclick="False" associato="TLBS" avversario="True" anticipo="4.0" durata="4.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Segnato" endesc="Segnato" cod="TLB-100" associato="TLBS-100" group="" default="false" />
        <TAG desc="Sbagliato" endesc="Sbagliato" cod="TLB-101" associato="TLBS-101" group="" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Rimbalzo" endesc="REB" esdesc="REB" cod="REB" sh="" vis="True" oneclick="False" associato="REBD" avversario="True" anticipo="4.0" durata="4.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Difensivo" endesc="DIFENSIVO" cod="REB-0" associato="" group="" default="false" />
        <TAG desc="Offensivo" endesc="OFFENSIVO" cod="REB-1" associato="REBD-1" group="" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Palla Persa" endesc="PP" esdesc="PP" cod="PP" sh="" vis="True" oneclick="False" associato="REC" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="1C1" endesc="1C1" cod="PP-100" associato="REC-100" group="1" default="false" />
        <TAG desc="Passaggio" endesc="PASSAGGIO" cod="PP-101" associato="REC-101" group="1" default="false" />
        <TAG desc="P&amp;R" endesc="P&amp;R" cod="PP-102" associato="REC-102" group="1" default="false" />
        <TAG desc="Aiuto" endesc="AIUTO" cod="PP-103" associato="REC-103" group="1" default="false" />
        <TAG desc="Rotazione" endesc="ROTAZIONE" cod="PP-104" associato="REC-104" group="1" default="false" />
        <TAG desc="8''" endesc="8''" cod="PP-105" associato="REC-105" group="2" default="false" />
        <TAG desc="24''" endesc="24''" cod="PP-106" associato="REC-106" group="2" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Assist" endesc="ASSIST" esdesc="ASSIST" cod="ASS" sh="" vis="True" oneclick="False" associato="ASSS" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="-1" closeother="false" posfield="2" posgoal="0" playerto="true" />
    <EVENT desc="P&amp;R" endesc="P&amp;R" esdesc="P&amp;R" cod="PR" sh="" vis="True" oneclick="False" associato="PRD" avversario="True" anticipo="8.0" durata="5.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="1" posgoal="0" playerto="true">
      <TAGS>
        <TAG desc="Laterale Dx" endesc="LATERALE DX" cod="PR-100" associato="PRD-100" group="1" default="false" />
        <TAG desc="Laterale Sx" endesc="LATERALE SX" cod="PR-101" associato="PRD-101" group="1" default="false" />
        <TAG desc="Centrale" endesc="CENTRALE" cod="PR-102" associato="PRD-102" group="1" default="false" />
        <TAG desc="Dal Fondo" endesc="DAL FONDO" cod="PR-103" associato="PRD-103" group="1" default="false" />
        <TAG desc="In transizione" endesc="DRAG" cod="PR-104" associato="PRD-104" group="1" default="false" />
        <TAG desc="Doppio in transizione" endesc="DOUBLE DRAG" cod="PR-105" associato="PRD-105" group="1" default="false" />
        <TAG desc="Mano Dx" endesc="MANO DX" cod="PR-106" associato="PRD-106" group="2" default="false" />
        <TAG desc="Mano Sx" endesc="MANO SX" cod="PR-107" associato="PRD-107" group="2" default="false" />
        <TAG desc="Palleggio arresto e tiro" endesc="JUMPER" cod="PR-108" associato="PRD-108" group="3" default="false" />
        <TAG desc="Penetrazione" endesc="SLICE" cod="PR-109" associato="PRD-109" group="3" default="false" />
        <TAG desc="Passaggio al bloccante" endesc="PASS TO SCREENER" cod="PR-110" associato="PRD-110" group="3" default="false" />
        <TAG desc="Roll" endesc="ROLL" cod="PR-111" associato="PRD-111" group="3" default="false" />
        <TAG desc="Pop" endesc="POP" cod="PR-112" associato="PRD-112" group="3" default="false" />
        <TAG desc="Roll corto" endesc="SHORT ROLL" cod="PR-113" associato="PRD-113" group="3" default="false" />
        <TAG desc="Scarico fuori" endesc="KICK OUT" cod="PR-114" associato="PRD-114" group="3" default="false" />
        <TAG desc="0''-8''" endesc="0''-8''" cod="PR-115" associato="PRD-115" group="4" default="false" />
        <TAG desc="8''-18''" endesc="8''-18''" cod="PR-116" associato="PRD-116" group="4" default="false" />
        <TAG desc="18''-24''" endesc="18''-24''" cod="PR-117" associato="PRD-117" group="4" default="false" />
        <TAG desc="Doppio alto" endesc="2 HIGH" cod="PR-118" associato="PRD-118" group="1" default="false" />
        <TAG desc="Inseguire" endesc="CHASE" cod="PR-119" associato="PRD-119" group="5" default="false" />
        <TAG desc="Forzare" endesc="FORCE" cod="PR-120" associato="PRD-120" group="5" default="false" />
        <TAG desc="Raddoppio" endesc="DOUBLE TEAM" cod="PR-121" associato="PRD-121" group="6" default="false" />
        <TAG desc="Cambio" endesc="SWITCH" cod="PR-122" associato="PRD-122" group="6" default="false" />
        <TAG desc="Fondo" endesc="FONDO" cod="PR-123" associato="PRD-123" group="5" default="false" />
        <TAG desc="Sotto" endesc="UNDER" cod="PR-124" associato="PRD-124" group="5" default="false" />
        <TAG desc="Contenimento" endesc="DROP" cod="PR-125" associato="PRD-125" group="6" default="false" />
        <TAG desc="Show" endesc="SHOW" cod="PR-126" associato="PRD-126" group="6" default="false" />
        <TAG desc="Lungo a contatto" endesc="CONTACT" cod="PR-127" associato="PRD-127" group="6" default="false" />
        <TAG desc="Consegnato" endesc="HO" cod="PR-128" associato="PRD-128" group="8" default="false" />
        <TAG desc="Lato Opposto" endesc="Lato Opposto" cod="PR-129" associato="PRD-129" group="7" default="false" />
        <TAG desc="Split" endesc="Split" cod="PR-130" associato="PRD-130" group="7" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Post Basso" endesc="LOW POST" esdesc="LOW POST" cod="LP" sh="" vis="True" oneclick="False" associato="LPD" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Lato Dx" endesc="LATO DX" cod="LP-100" associato="LPD-100" group="1" default="false" />
        <TAG desc="Lato Sx" endesc="LATO SX" cod="LP-101" associato="LPD-101" group="1" default="false" />
        <TAG desc="Palleggio in centro" endesc="DRIVE MIDDLE" cod="LP-102" associato="LPD-102" group="2" default="false" />
        <TAG desc="Palleggio sul fondo" endesc="DRIVE BASE LINE" cod="LP-103" associato="LPD-103" group="2" default="false" />
        <TAG desc="Mano Dx" endesc="MANO DX" cod="LP-104" associato="LPD-104" group="3" default="false" />
        <TAG desc="Mano Sx" endesc="MANO SX" cod="LP-105" associato="LPD-105" group="3" default="false" />
        <TAG desc="Forzare fondo" endesc="Forzare fondo" cod="LP-106" associato="LPD-106" group="4" default="false" />
        <TAG desc="Forzare centro" endesc="Forzare centro" cod="LP-107" associato="LPD-107" group="4" default="false" />
        <TAG desc="Muro" endesc="Muro" cod="LP-108" associato="LPD-108" group="4" default="false" />
        <TAG desc="Raddoppio" endesc="Raddoppio" cod="LP-109" associato="LPD-109" group="4" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Isolamento" endesc="ISO" esdesc="ISO" cod="ISO" sh="" vis="True" oneclick="False" associato="ISOD" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="1" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Destro" endesc="DESTRO" cod="ISO-102" associato="ISOD-102" group="" default="false" />
        <TAG desc="Sinistro" endesc="SINISTRO" cod="ISO-103" associato="ISOD-103" group="" default="false" />
        <TAG desc="Top" endesc="TOP" cod="ISO-104" associato="ISOD-104" group="" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Blocco Off" endesc="Blocco Off" esdesc="Blocco Off" cod="BLC" sh="" vis="True" oneclick="False" associato="BLCD" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Cieco" endesc="BACK SCREEN" cod="BLC-100" associato="BLCD-100" group="1" default="false" />
        <TAG desc="Stagger" endesc="STAGGER" cod="BLC-101" associato="BLCD-101" group="2" default="false" />
        <TAG desc="Doppia uscita" endesc="FLOPPY" cod="BLC-102" associato="BLCD-102" group="2" default="false" />
        <TAG desc="Loop" endesc="LOOP" cod="BLC-103" associato="BLCD-103" group="2" default="false" />
        <TAG desc="Zipper" endesc="ZIPPER" cod="BLC-104" associato="BLCD-104" group="1" default="false" />
        <TAG desc="Cross Screen" endesc="CROSS SCREEN" cod="BLC-105" associato="BLCD-105" group="1" default="false" />
        <TAG desc="Largo" endesc="WIDE SCREEN" cod="BLC-106" associato="BLCD-106" group="1" default="false" />
        <TAG desc="Bloccare il bloccante" endesc="SCREEN THE SCREENER" cod="BLC-107" associato="BLCD-107" group="1" default="false" />
        <TAG desc="Inseguire" endesc="CHASE" cod="BLC-108" associato="BLCD-108" group="3" default="false" />
        <TAG desc="Tagliare" endesc="CUT" cod="BLC-109" associato="BLCD-109" group="3" default="false" />
        <TAG desc="Cambio" endesc="SWITCH" cod="BLC-110" associato="BLCD-110" group="3" default="false" />
        <TAG desc="Aiuto e recupero" endesc="H&amp;R" cod="BLC-111" associato="BLCD-111" group="3" default="false" />
        <TAG desc="Negare" endesc="DENY" cod="BLC-112" associato="BLCD-112" group="3" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Stoppata" endesc="Stoppata" esdesc="Stoppata" cod="STOP" sh="" vis="True" oneclick="False" associato="STS" avversario="True" anticipo="4.0" durata="4.0" sics="False" backgroundColor="255x255x0x0" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false" />
    <EVENT desc="Tiro Subito" endesc="SHOT \nCONCEDED" esdesc="TIRO SUBITO" cod="TS" sh="" vis="True" oneclick="False" associato="TF" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="1" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Piazzato" endesc="SPOT" cod="TS-100" associato="TF-100" group="2" default="false" />
        <TAG desc="Palleggio arresto e tiro" endesc="JUMPER" cod="TS-101" associato="TF-101" group="2" default="false" />
        <TAG desc="Da sotto da penetrazione" endesc="Lay-Up da Penetrazione" cod="TS-102" associato="TF-102" group="2" default="false" />
        <TAG desc="Uscita dai blocchi" endesc="OFF SCREEN" cod="TS-103" associato="TF-103" group="2" default="false" />
        <TAG desc="Schiacciata" endesc="DUNK" cod="TS-104" associato="TF-104" group="2" default="false" />
        <TAG desc="Contropiede" endesc="FB" cod="TS-105" associato="TF-105" group="2" default="false" />
        <TAG desc="Rimbalzo Offensivo" endesc="OFF REBS" cod="TS-106" associato="TF-106" group="2" default="false" />
        <TAG desc="0''-8''" endesc="0'-8'" cod="TS-107" associato="TF-107" group="3" default="false" />
        <TAG desc="8''-18''" endesc="8'-16'" cod="TS-108" associato="TF-108" group="3" default="false" />
        <TAG desc="18''-24''" endesc="16''-24''" cod="TS-109" associato="TF-109" group="3" default="false" />
        <TAG desc="Mano Dx" endesc="MANO DX" cod="TS-111" associato="TF-111" group="1" default="false" />
        <TAG desc="Mano Sx" endesc="MANO SX" cod="TS-112" associato="TF-112" group="1" default="false" />
        <TAG desc="Da sotto da passaggio" endesc="Lay-Up da Passaggio" cod="TS-113" associato="TF-113" group="2" default="false" />
        <TAG desc="Gancetto" endesc="BABY HOOK" cod="TS-114" associato="TF-114" group="2" default="false" />
        <TAG desc="Gancio Cielo" endesc="HOOK" cod="TS-115" associato="TF-115" group="2" default="false" />
        <TAG desc="Da Seconda Opportunità" endesc="Da Seconda Opportunità" cod="TS-116" associato="TF-116" group="4" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Tiro Libero \nSubito" endesc="TIRO LIBERO SUBITO" esdesc="TIRO LIBERO SUBITO" cod="TLBS" sh="" vis="True" oneclick="False" associato="TLB" avversario="True" anticipo="4.0" durata="4.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Segnato" endesc="Segnato" cod="TLBS-100" associato="TLB-100" group="" default="false" />
        <TAG desc="Sbagliato" endesc="Sbagliato" cod="TLBS-101" associato="TLB-101" group="" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Rimbalzo Subito" endesc="REB Subito" esdesc="REB" cod="REBD" sh="" vis="True" oneclick="False" associato="REB" avversario="True" anticipo="4.0" durata="4.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Difensivo" endesc="DIFENSIVO" cod="REBD-0" associato="REB-0" group="" default="false" />
        <TAG desc="Offensivo" endesc="OFFENSIVO" cod="REBD-1" associato="REB-1" group="" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Recupero" endesc="Recupero" esdesc="RECUPERO" cod="REC" sh="" vis="True" oneclick="False" associato="PP" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="1C1" endesc="1C1" cod="REC-100" associato="PP-100" group="1" default="false" />
        <TAG desc="Intercetto" endesc="Intercetto" cod="REC-101" associato="PP-101" group="1" default="false" />
        <TAG desc="P&amp;R" endesc="P&amp;R" cod="REC-102" associato="PP-102" group="1" default="false" />
        <TAG desc="Aiuto" endesc="AIUTO" cod="REC-103" associato="PP-103" group="1" default="false" />
        <TAG desc="Rotazione" endesc="ROTAZIONE" cod="REC-104" associato="PP-104" group="1" default="false" />
        <TAG desc="8''" endesc="8''" cod="REC-105" associato="PP-105" group="2" default="false" />
        <TAG desc="24''" endesc="24''" cod="REC-106" associato="PP-106" group="2" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Assist \nSubito" endesc="Assist \nSubito" esdesc="ASSIST SUBITO" cod="ASSS" sh="" vis="True" oneclick="False" associato="ASS" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="-1" closeother="false" posfield="2" posgoal="0" playerto="true" />
    <EVENT desc="P&amp;R D" endesc="P&amp;R D" esdesc="P&amp;R D" cod="PRD" sh="" vis="True" oneclick="False" associato="PR" avversario="True" anticipo="5.0" durata="7.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="1" posgoal="0" playerto="true">
      <TAGS>
        <TAG desc="Laterale Dx" endesc="LATERALE DX" cod="PRD-100" associato="PR-100" group="1" default="false" />
        <TAG desc="Laterale Sx" endesc="LATERALE SX" cod="PRD-101" associato="PR-101" group="1" default="false" />
        <TAG desc="Centrale" endesc="CENTRALE" cod="PRD-102" associato="PR-102" group="1" default="false" />
        <TAG desc="Dal Fondo" endesc="DAL FONDO" cod="PRD-103" associato="PR-103" group="1" default="false" />
        <TAG desc="In transizione" endesc="DRAG" cod="PRD-104" associato="PR-104" group="1" default="false" />
        <TAG desc="Doppio in transizione" endesc="DOUBLE DRAG" cod="PRD-105" associato="PR-105" group="1" default="false" />
        <TAG desc="Mano Dx" endesc="MANO DX" cod="PRD-106" associato="PR-106" group="2" default="false" />
        <TAG desc="Mano Sx" endesc="MANO SX" cod="PRD-107" associato="PR-107" group="2" default="false" />
        <TAG desc="Palleggio arresto e tiro" endesc="JUMPER" cod="PRD-108" associato="PR-108" group="3" default="false" />
        <TAG desc="Penetrazione" endesc="SLICE" cod="PRD-109" associato="PR-109" group="3" default="false" />
        <TAG desc="Passaggio al bloccante" endesc="PASS TO SCREENER" cod="PRD-110" associato="PR-110" group="3" default="false" />
        <TAG desc="Roll" endesc="ROLL" cod="PRD-111" associato="PR-111" group="3" default="false" />
        <TAG desc="Pop" endesc="POP" cod="PRD-112" associato="PR-112" group="3" default="false" />
        <TAG desc="Roll corto" endesc="SHORT ROLL" cod="PRD-113" associato="PR-113" group="3" default="false" />
        <TAG desc="Scarico fuori" endesc="KICK OUT" cod="PRD-114" associato="PR-114" group="3" default="false" />
        <TAG desc="0''-8''" endesc="0''-8''" cod="PRD-115" associato="PR-115" group="4" default="false" />
        <TAG desc="8''-18''" endesc="8''-18''" cod="PRD-116" associato="PR-116" group="4" default="false" />
        <TAG desc="18''-24''" endesc="18''-24''" cod="PRD-117" associato="PR-117" group="4" default="false" />
        <TAG desc="Doppio alto" endesc="2 HIGH" cod="PRD-118" associato="PR-118" group="1" default="false" />
        <TAG desc="Inseguire" endesc="CHASE" cod="PRD-119" associato="PR-119" group="5" default="false" />
        <TAG desc="Forzare" endesc="FORCE" cod="PRD-120" associato="PR-120" group="5" default="false" />
        <TAG desc="Raddoppio" endesc="DOUBLE TEAM" cod="PRD-121" associato="PR-121" group="6" default="false" />
        <TAG desc="Cambio" endesc="SWITCH" cod="PRD-122" associato="PR-122" group="6" default="false" />
        <TAG desc="Fondo" endesc="FONDO" cod="PRD-123" associato="PR-123" group="5" default="false" />
        <TAG desc="Sotto" endesc="UNDER" cod="PRD-124" associato="PR-124" group="5" default="false" />
        <TAG desc="Contenimento" endesc="DROP" cod="PRD-125" associato="PR-125" group="6" default="false" />
        <TAG desc="Show" endesc="SHOW" cod="PRD-126" associato="PR-126" group="6" default="false" />
        <TAG desc="Lungo a contatto" endesc="CONTACT" cod="PRD-127" associato="PR-127" group="6" default="false" />
        <TAG desc="Consegnato" endesc="HO" cod="PRD-128" associato="PR-128" group="8" default="false" />
        <TAG desc="Lato Opposto" endesc="Lato Opposto" cod="PRD-129" associato="PR-129" group="7" default="false" />
        <TAG desc="Split" endesc="Split" cod="PRD-130" associato="PR-130" group="7" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Post Basso D" endesc="LOW POST D" esdesc="LOW POST" cod="LPD" sh="" vis="True" oneclick="False" associato="LP" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Lato Dx" endesc="LATO DX" cod="LPD-100" associato="LP-100" group="1" default="false" />
        <TAG desc="Lato Sx" endesc="LATO SX" cod="LPD-101" associato="LP-101" group="1" default="false" />
        <TAG desc="Palleggio in centro" endesc="DRIVE MIDDLE" cod="LPD-102" associato="LP-102" group="2" default="false" />
        <TAG desc="Palleggio sul fondo" endesc="DRIVE BASE LINE" cod="LPD-103" associato="LP-103" group="2" default="false" />
        <TAG desc="Mano Dx" endesc="MANO DX" cod="LPD-104" associato="LP-104" group="3" default="false" />
        <TAG desc="Mano Sx" endesc="MANO SX" cod="LPD-105" associato="LP-105" group="3" default="false" />
        <TAG desc="Forzare fondo" endesc="Forzare fondo" cod="LPD-106" associato="LP-106" group="4" default="false" />
        <TAG desc="Forzare centro" endesc="Forzare centro" cod="LPD-107" associato="LP-107" group="4" default="false" />
        <TAG desc="Muro" endesc="Muro" cod="LPD-108" associato="LP-108" group="4" default="false" />
        <TAG desc="Raddoppio" endesc="Raddoppio" cod="LPD-109" associato="LP-109" group="4" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Isolamento D" endesc="ISO D" esdesc="ISO" cod="ISOD" sh="" vis="True" oneclick="False" associato="ISO" avversario="True" anticipo="5.0" durata="5.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="1" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Destro" endesc="DESTRO" cod="ISOD-102" associato="ISO-102" group="" default="false" />
        <TAG desc="Sinistro" endesc="SINISTRO" cod="ISOD-103" associato="ISO-103" group="" default="false" />
        <TAG desc="Top" endesc="TOP" cod="ISOD-104" associato="ISO-104" group="" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Blocco D" endesc="Blocco D" esdesc="Blocco D" cod="BLCD" sh="" vis="True" oneclick="False" associato="BLC" avversario="True" anticipo="5.0" durata="6.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="2" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Cieco" endesc="Back Screen" cod="BLCD-100" associato="BLC-100" group="1" default="false" />
        <TAG desc="Stagger" endesc="Stagger" cod="BLCD-101" associato="BLC-101" group="2" default="false" />
        <TAG desc="Doppia uscita" endesc="Floppy" cod="BLCD-102" associato="BLC-102" group="2" default="false" />
        <TAG desc="Loop" endesc="Loop" cod="BLCD-103" associato="BLC-103" group="2" default="false" />
        <TAG desc="Zipper" endesc="Zipper" cod="BLCD-104" associato="BLC-104" group="1" default="false" />
        <TAG desc="Cross Screen" endesc="Cross Screen" cod="BLCD-105" associato="BLC-105" group="1" default="false" />
        <TAG desc="Largo" endesc="Wide Screen" cod="BLCD-106" associato="BLC-106" group="1" default="false" />
        <TAG desc="Bloccare il bloccante" endesc="Screen the Screener" cod="BLCD-107" associato="BLC-107" group="2" default="false" />
        <TAG desc="Inseguire" endesc="Chase" cod="BLCD-108" associato="BLC-108" group="3" default="false" />
        <TAG desc="Tagliare" endesc="Cut" cod="BLCD-109" associato="BLC-109" group="3" default="false" />
        <TAG desc="Cambio" endesc="Switch" cod="BLCD-110" associato="BLC-110" group="3" default="false" />
        <TAG desc="Aiuto e recupero" endesc="H&amp;R" cod="BLCD-111" associato="BLC-111" group="3" default="false" />
        <TAG desc="Negare" endesc="Deny" cod="BLCD-112" associato="BLC-112" group="3" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Stoppata \nSubita" endesc="Stoppata \nSubita" esdesc="Stoppata \nSubita" cod="STS" sh="" vis="True" oneclick="False" associato="STOP" avversario="True" anticipo="4.0" durata="4.0" sics="False" backgroundColor="255x0x255x0" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false" />
    <EVENT desc="Fallo" endesc="Fallo" esdesc="Fallo" cod="FAF" sh="" vis="True" oneclick="False" associato="FAS" avversario="True" anticipo="4.0" durata="4.0" sics="False" backgroundColor="255x255x255x128" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Difensivo" endesc="Difensivo" cod="FAF-100" associato="FAS-100" group="1" default="false" />
        <TAG desc="Offensivo" endesc="Offensivo" cod="FAF-101" associato="FAS-101" group="2" default="false" />
        <TAG desc="Tecnico" endesc="Tecnico" cod="FAF-102" associato="FAS-102" group="3" default="false" />
        <TAG desc="Antisportivo" endesc="Antisportivo" cod="FAF-103" associato="FAS-103" group="3" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Fallo \nSubito" endesc="Fallo \nSubito" esdesc="Fallo \nSubito" cod="FAS" sh="" vis="True" oneclick="False" associato="FAF" avversario="True" anticipo="4.0" durata="4.0" sics="False" backgroundColor="255x255x255x128" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Difensivo" endesc="Difensivo" cod="FAS-100" associato="FAF-100" group="1" default="false" />
        <TAG desc="Offensivo" endesc="Offensivo" cod="FAS-101" associato="FAF-101" group="2" default="false" />
        <TAG desc="Tecnico" endesc="Tecnico" cod="FAS-102" associato="FAF-102" group="3" default="false" />
        <TAG desc="Antisportivo" endesc="Antisportivo" cod="FAS-103" associato="FAF-103" group="3" default="false" />
      </TAGS>
    </EVENT>
    <EVENT desc="Sostituzione" endesc="Sostituzione" esdesc="Sostituzione" cod="SOS" sh="" vis="False" oneclick="False" associato="" avversario="True" anticipo="4.0" durata="3.0" sics="True" backgroundColor="255x204x204x204" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false" />
    <EVENT desc="Subentro" endesc="Subentro" esdesc="Subentro" cod="SUB" sh="" vis="False" oneclick="False" associato="" avversario="True" anticipo="4.0" durata="3.0" sics="True" backgroundColor="255x204x204x204" textColor="255x0x0x0" esito="-1" closeother="false" posfield="0" posgoal="0" playerto="false" />
    <EVENT desc="Espulsione" endesc="Espulsione" esdesc="Espulsione" cod="ESP" sh="" vis="False" oneclick="False" associato="" avversario="False" anticipo="4.0" durata="3.0" sics="True" backgroundColor="255x204x204x204" textColor="255x0x0x0" esito="-1" closeother="false" posfield="1" posgoal="0" playerto="false">
      <TAGS>
        <TAG desc="Gioco Violento" endesc="Gioco Violento" cod="ESP-0" associato="" group="1" default="false" />
        <TAG desc="Secondo Fallo Tecnico" endesc="Secondo Fallo Tecnico" cod="ESP-1" associato="" group="1" default="false" />
        <TAG desc="Secondo Fallo Antisportivo" endesc="Secondo Fallo Antisportivo" cod="ESP-2" associato="" group="1" default="false" />
      </TAGS>
    </EVENT>
  </EVENTS>
</VideoMatch>
