@-moz-keyframes blinker {
    0% {
        opacity: 1.0;
    }
    50% {
        opacity: 0.2;
    }
    100% {
        opacity: 1.0;
    }
}

@-webkit-keyframes blinker {
    0% {
        opacity: 1.0;
    }
    50% {
        opacity: 0.2;
    }
    100% {
        opacity: 1.0;
    }
}

@keyframes blinker {
    0% {
        opacity: 1.0;
    }
    50% {
        opacity: 0.2;
    }
    100% {
        opacity: 1.0;
    }
}

.sol-container {
	display: inline-block; /* prima era display: block;*/
}

.sol-container.sol-active .sol-inner-container {
	position: relative;
	border-color: rgba(82, 168, 236, 0.8);
	outline: 0;
	-webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(82,168,236,.6);
	-moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(82,168,236,.6);
	box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(82,168,236,.6);
}

.sol-container.sol-active.sol-selection-top .sol-inner-container {
	border-top-right-radius: 0;
	border-top-left-radius: 0;
}

.sol-container.sol-active.sol-selection-top .sol-selection-container {
	-webkit-box-shadow: 0 0 12px rgba(0,0,0,.175);
	box-shadow: 0 0 12px rgba(0,0,0,.175);
	border-bottom-left-radius: 0;
    border-top-right-radius: 0;
}

.sol-container.sol-active.sol-selection-bottom .sol-inner-container {
	border-bottom-right-radius: 0;
	border-bottom-left-radius: 0;
}

.sol-container.sol-active.sol-selection-bottom .sol-selection-container {
	-webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
	box-shadow: 0 6px 12px rgba(0,0,0,.175);
	border-top-left-radius: 0;
    border-bottom-right-radius: 0;
}

.sol-inner-container {
    border: 1px solid #ccc;
    background-color: #fff;
    height: 32px;
    line-height: 30px;

    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    position: relative;
    -webkit-transition: border linear .2s, box-shadow linear .2s;
    -moz-transition: border linear .2s, box-shadow linear .2s;
    -o-transition: border linear .2s, box-shadow linear .2s;
    transition: border linear .2s, box-shadow linear .2s;

    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
}

.sol-input-container {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 25px;
}

.sol-input-container>input {
    background-color: transparent;
	padding: 0 7px;
	margin: 0;
	width: 100%;
    height: 100%;

	border: none;
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
}

.sol-input-container>input:focus {
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
    outline: none;
}

.sol-container.sol-active .sol-input-container>input {
    border-bottom-right-radius: 0;
}

.sol-caret-container {
	position: absolute;
	display: inline-block;
	width: 25px;
    right: 0;
    top: 0;
    bottom: 0;
}

.sol-caret-container b {
	position: absolute;
	top: 13px;
	left: 8px;
}

.sol-caret-container b.caret {
    display: inline-block;
    width: 0;
    height: 0;
    vertical-align: middle;
    border-top: 4px solid;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent
}

.sol-selection-container {
	display: none;
	position: absolute;
	top: 100%;
	left: -1px;
	float: left;
	background: #fff;
	border: 1px solid #ddf;
	z-index: 99999;
	background-clip: padding-box;
	border: 1px solid rgba(0, 0, 0, .15);
	border-radius: 4px;
}

.sol-container.sol-active .sol-selection-container {
	display: block;
    position: fixed;
    left: inherit;
    top: inherit;
    z-index: 2;
}

.sol-option {
    padding: 5px 10px;
	display: block;
}

.sol-selection:not(.sol-keyboard-navigation) .sol-option:hover, .sol-option.keyboard-selection {
    background: #0088CC;
    color: #fff;
}

.sol-label {
	display: block;
    position: relative;
}

.sol-label-text {
    padding-left: 20px;
    line-height: 1.2em;
}

.sol-checkbox, .sol-radio {
    position: absolute;
	width: 13px !important;
	height: 13px !important;
	padding: 0 !important;
	margin: 0 !important;
	top: 4px;
}

.sol-optiongroup {
	background: #f7f7f7;
    padding-bottom: 1px;
}

.sol-optiongroup-label {
    color: #555;
    background: #eee;
	font-weight: bold;
    margin-bottom: 5px;
    padding: 0 5px;
    border-top: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
}

.sol-optiongroup.disabled {
	color: #999;
}

.sol-no-results, .sol-loading-data {
    color: #999;
    font-style: italic;
    text-align: center;
}

.sol-loading-data {
    -webkit-animation-name: blinker;
    -webkit-animation-duration: 1s;
    -webkit-animation-timing-function: linear;
    -webkit-animation-iteration-count: infinite;

    -moz-animation-name: blinker;
    -moz-animation-duration: 1s;
    -moz-animation-timing-function: linear;
    -moz-animation-iteration-count: infinite;

    animation-name: blinker;
    animation-duration: 1s;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
}

.sol-filtered-search {
    display: none;
}

.sol-action-buttons {
    line-height: 20px;
    color: #555;
    background: #eee;
    padding: 7px 10px;
    border-bottom: 1px solid #ccc;
    -webkit-border-top-right-radius: 4px;
	-moz-border-top-right-radius: 4px;
	border-top-right-radius: 4px;
}

.sol-action-buttons a {
    text-decoration: none;
    color: #0088CC;
    border-bottom: 1px solid transparent;
}

.sol-action-buttons a:hover {
    border-bottom: 1px solid #0088CC;
}

.sol-select-all {
    float: left;
}

.sol-deselect-all {
    float: right;
}

.sol-clearfix {
    clear: both;
}

.sol-selected-display-item {
    display: inline-table;
    border: 1px solid #ccc;
    background: #f7f7f9;
    font-size: 0.9em;
    border-radius: 4px;
    margin-right: 5px;
    margin-bottom: 5px;
    border-collapse: initial;
}

.sol-quick-delete {
    color: #777;
    display: table-cell;
    font-weight: bold;
    text-align: center;
    padding: 3px 5px;
    vertical-align: top;
}

.sol-selected-display-item-text {
    padding: 3px 5px;
    display: table-cell;
    vertical-align: top;
}

.sol-quick-delete + .sol-selected-display-item-text {
    padding-left: 0;
}

.sol-quick-delete:hover {
    color: #111;
    cursor: pointer;
}

.sol-current-selection {
    padding: 5px 0;
}

.sol-selection {
    overflow: auto;
    position: relative;
}

.sol-selection div:first-child.sol-optiongroup > .sol-optiongroup-label {
    border-top: none;
}
