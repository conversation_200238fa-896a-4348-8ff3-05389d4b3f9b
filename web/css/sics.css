* {
    -webkit-print-color-adjust: exact !important;   /* Chrome, Safari 6 – 15.3, Edge */
    /* color-adjust: exact !important; */           /* Firefox 48 – 96 */
    /* print-color-adjust: exact !important; */     /* Firefox 97+, Safari 15.4+ */
}

#sicsHeader{
    /*padding-top: 5px;
    padding-bottom: 5px;*/
    border: none;
    background:#ffffff;
}

#sicsHeader a:hover{
    background:#ffffff;
}

#subline{
    border-bottom: solid #f27e46 1px;
}

.orangecolor{
    background: rgba(48,48,48, .85);
}

#userNav{
    color:#FFFFFF;
}

#stats{
    background:#E6E6E6;
    padding: 6px;
}

#breadcrumb, .breadcrumb {
    color:#ffffff;
    background:#f27e46;
    padding: 8px 12px 6px 12px;
    margin: 0px;
    border-bottom: 1px #dddddd solid;
    border-right: 1px #dddddd solid;
}
#breadcrumb a, .breadcrumb a {
    color:#ffffff;
}
#breadcrumb ul, .breadcrumb ul {
    margin: 0;
}
#breadcrumb li, .breadcrumb li {
    color: #ffffff;
}
#breadcrumb a:hover, .breadcrumb a:hover {
    text-decoration: underline;
    color: #ffffff;
}

/*
.uk-breadcrumb > li:nth-child(n+2):before {
  display: inline-block;
  margin: 0 8px;
}
*/
.matchesCalendar{
    background:#f5f5f5;
    /*border-left: 1px #dddddd solid;*/
}

.competitionBoxX{
    background-color: #f8f8f8;
    border: 1px transparent solid;
    border-radius: 6px;
    cursor: pointer;
}

.competitionBox,.playerBox{
    background-color: #f5f5f5;
    border: 1px transparent solid;
    border-radius: 6px;
    cursor: pointer;
    padding: 10px;
    opacity: 0.75;
    /*padding: 1% 0.1% 0.1% 0.1%;*/
    /*margin: 1%;*/
    /*
    margin:4px;
    padding: 4px;
    */
    /*
    -webkit-box-shadow: 0px 1px 1px 0px rgba(0,0,0,0.1);
    -moz-box-shadow: 0px 1px 1px 0px rgba(0,0,0,0.1);
    box-shadow: 0px 1px 1px 0px rgba(0,0,0,0.1);
    */
}
.competitionBox:hover,.playerBox:hover {
    opacity: 0.8;
    filter: alpha(opacity=80);
    /*border: 1px solid #f27e46;*/
}

.competitionBoxDisabled {
    /*background-color: #fafafa;
    border: 1px #e0e0e0 solid;
    border-radius: 6px;*/
    opacity: 0.2;
    filter: alpha(opacity=20);
    background-color: #dddddd;
    border: 1px transparent solid;
    border-radius: 6px;
    padding: 10px;
}

.competitionBoxDisabledX {
    /*background-color: #fafafa;
    border: 1px #e0e0e0 solid;
    border-radius: 6px;*/
    opacity: 0.2;
    filter: alpha(opacity=20);
}

.deleteBoxDisabledX {
    /*background-color: #fafafa;
    border: 1px #e0e0e0 solid;
    border-radius: 6px;*/
    opacity: 0.2;
    filter: alpha(opacity=20);
}

.deleteBoxDisabledX:hover{
    background: #ececec;
}


@media (max-width: 959px) {
    .competitionBox p,.competitionBoxDisabled p,.playerBox span{
        /*white-space: nowrap;*/
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 9px;
        font-stretch: ultra-condensed;
        text-transform: uppercase;
    }
}
@media (min-width: 960px) {

    .competitionBox p,.competitionBoxDisabled p,.playerBox span{
        /*white-space: nowrap;*/
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 12px;
        text-transform: uppercase;
    }

}

.playerBox {
    text-align: center;
    transition: opacity 0.25s ease-in-out;
}

.playerBox:hover {
    opacity: 1 !important;
}

.playerBox span, .competitionBox span {
    margin: 8px;
    font-size: 14px;
    display: block;
}

.userOptions{
    color:#f27e46;
}

#dateBox,#dateBoxPlaylist{
    /*padding-left: 6px;
    padding-right: 6px;*/
    padding: 6px;
    border-style: solid;
    border-width: 1px;
    border-color: #f27e46;
    font-size: 11px;
    font-weight: 300;
    border-radius: 3px;
    width:28px;
}

.dateBoxPlaylistX{
    /*padding-left: 6px;
    padding-right: 6px;*/
    padding: 6px;
    border-style: solid;
    border-width: 1px;
    border-color: #f27e46;
    font-size: 11px;
    font-weight: 300;
    border-radius: 3px;
    width:28px;
}

.competitionDetail,.playlistDetail {
    font-size: 10px;
}
.competitionResult {
    font-size: 15px;
    font-weight: bold;
}
.competitionMinutes {
    font-size: 15px;
}

/*
#matchTable td,#eventTable td {
    border-bottom: 1px solid #f27e46;
}
*/
/*
#matchTable tr:hover {
    background: #f27e46;
    text-decoration: none;breadcrumb
    color: #f5f5f5;
}
*/
#eventTable {
    min-width: 500px;
}

.headerSubnavNoHover {
    color:#000000;
    font-size: 15px;
}
.headerSubnav {
    color:#000000;
    font-size: 15px;
}
.headerSubnav:hover {
    color: #ff6600;
    text-decoration: none;
    background: #ffffff;
}

.selAllSubnav {
    color:#f5f5f5;
    font-size: 13px;
}
.selAllSubnav:hover {
    color: #f27e46;
    text-decoration: none;
    background: #000000;
}

.headerMenu {
    background: transparent;
    /*background-image: -webkit-linear-gradient(top, #ffffff, #ffffff);
    background-image: -moz-linear-gradient(top, #ffffff, #ffffff);
    background-image: -ms-linear-gradient(top, #ffffff, #ffffff);
    background-image: -o-linear-gradient(top, #ffffff, #ffffff);
    background-image: linear-gradient(to bottom, #ffffff, #ffffff);*/
    -webkit-border-radius: 3;
    -moz-border-radius: 3;
    border-radius: 3px;

    color: #f5f5f5;
    font-size: 18px;
    padding: 3px 20px 3px 20px;
    border: solid #F7F7F7 1px;
    text-decoration: none;
}

.headerMenu:hover {
    background: #f27e46;
    text-decoration: none;
    color: #f5f5f5;
}

.clickRow{
    cursor: pointer;
}

.calendarNav{
    background-color: #f26522;
    float:left;
    border-radius: 5px;
    height: 30px;
    text-align: center;
    vertical-align: middle;
    color: white;
    line-height: 30px;
    cursor: pointer;
    padding: 3px 8px;
    margin: 5px;
}

.calendarRow {
    text-align: center;
    display: block;
    width: 350px;
    margin-left: 50px;
    padding: 10px 0px;
    word-spacing: 30px;
    border-bottom: solid 1px #E6E6E6;
    cursor: pointer;
}

.calendarTeam {
    display: inline-block;
    min-width:125px;
    word-spacing: normal;
}

#calendarNavigation {
    display: inline-block;
}

#calNavigation span {
    margin-top: 4px;
    font-weight: bold;
}

.tagEvento {
    margin-left: 5px;
    /*border: solid 1px #E6E6E6;*/
}

#sorgenteSicsButton,#sorgenteUserButton/*#timeFirstButton,#timeSecondButton*/ {
    margin: 5px;
    margin-left: 5px;
}

.checkMulti {
    display: inline-block;
    padding: 10px;
}

#btnMulti{
    float:right;
    border-radius: 5px;
    background-color: #f26522;
    text-align: center;
    padding: 5px;
    cursor: pointer;
}

.data  {
    margin: 5px;
}

span.data {
    font-size: 20px;
    text-align: center;
    max-width: 800px;
}
#sourceButton,#teamButton,#timeButton {
    float: left;
    margin: 0px 10px;
}

#configButton,#players,#actions {
    display: block;
    clear: both;
    /*margin: 0px 10px;*/
}

/*#sideNav {
    min-width: 500px;
        position: fixed;
        left: 750px;
        margin-right: 20px;
    float: right;
    width: 45%;
    border-left: 1px #dddddd solid;
    background-color: #eaeaea;
    width: 30%;
}*/

/*#bottomNav {
    border-top: 1px #dddddd solid;
    background-color: #eaeaea;
}
#bottomNav > ul > li > a {
    padding: 10px;
}
#bottomNav > ul > li.uk-active > a {
    background-color: #202020;
}*/
/*#bottomNavTab {
    background-color: #666666;
}*/

#filtroGiocatore {
    margin-top: 10px;
}

/*.spanPlayerFilter{
    margin: 5px;
    display: block;
}*/

.filter-select-label {
    display: inline-block;
    padding: 10px;
    /*width: 80px;
    font-size: 12px;*/
}

.filter-select {
    border-radius: 4px;
    border: solid 1px;
    height: 25px;
    text-align: center;
    /*margin-left: 161px;*/
}

/* per impostare la grandezza del font del autocomplete dei giocatori */
.ui-autocomplete li{
    font-size: 12px;
}

.selection-container-none {
    display: none;
}

.selection-container {
    margin: 2px;
}

select:not(.skip-hover) > option:hover {
    color: white;
    background-color: #f7f7f7;
}

.selection-element {
    display: inline-block;
    margin-right: 3px;
    clear: both;
    left: inherit;
    top: inherit;
    z-index: 10000;
    border: 1px solid #ccc;
    background: #ccc;
    border-radius: 4px;
    color: #fff;
}

.removeButton {
    float:right;
    margin-top: -3px;
    margin-right: 2px;
    margin-left: 2px;
}

.removeButton img {
    width: 10px;
    height: 10px;
}


.matchesColumnResult1,.playlistColumnResult1 {
    width:50px;
}

.matchesColumnResult2 {
    width:60px;
}

.matchesColumnResult3 {
    width:90px;
}

.playlistColumnResult2 {
    width:75px;
}

.matchesColumnTag {
    width:62px;
}

.iconButton {
    /*padding: 0px; 
    width: 20px;*/
}

#dateBoxMysicstv {
    margin-left: 6px;
    margin-bottom: 6px;
    padding: 6px;
    border-style: solid;
    border-width: 1px;
    border-color: #f27e46;
    font-size: 11px;
    font-weight: 300;
    border-radius: 3px;
    width:28px;
}
.playlistBox {
    background-color: #f5f5f5;
    /*
    padding:6px;
    margin:6px;
    */
}


.playlistBox a {
    text-decoration: none;
}

.playlistBox .caption {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    font-size: 12px;
    text-transform: uppercase;
}

.playlistBox .date {
    font-size: 10px;
    text-transform: uppercase;
    color: #aaaaab;
    text-align: left;
}

.playlistBox:hover {
    opacity: 0.8;
    filter: alpha(opacity=80);
    border: 1px solid #f27e46;
}

#arbitriTabellino {
    display:block;
    margin: 10px 5px;
    font-size: 12px;
}

#arbitriTabellino > span {
    display: block;
    clear: both;
}

#arbitriTabellino > span > label{
    text-align: justify;
}

#risultatoTabellino {
    text-align: center;
}

#logoTeamHome, #logoTeamAway {
    width: 50px;
    height: 50px;
}


#divFormazioniHome{
    float:left;
}

#divFormazioniAway{
    float:right;
}

#imgModuli{
    width: 650px;
}

#divOverflow {
    max-height: 800px;
}

#spanSelAll, #spanFilterGames, #spanTeamStatsDropdown {
    width: 190px;
    height: 90px;
    padding: 10px;
}

#spanSelAll ul, #spanFilterGames ul, #spanTeamStatsDropdown ul {
    margin: 0;
}

#spanSelAll ul li, #spanFilterGames ul li, #spanTeamStatsDropdown ul li {
    font-size: 14px;
}

.tabellino-score {
    width: 50px;
    height: 50px;
    text-align: center;
    vertical-align: middle;
    font-size: 30px;
    line-height: 50px;
    display: inline-block;
    background: #000000;
    color: #FFFFFF;
    margin: 0px 10px;
}

.spanScore {
    display:block;
    font-size: 30px;
}

.playerScore {
    font-size: 12px;
    display: block;
}

.formazioniTabellino {
    background:#f5f5f5;
    padding: 5px;
    text-align: center;
}

.formazioniTabellino > label {
    font-weight: bold;
}

.formazioniTabellino > table {
    text-align: left;
}

.tdScore {
    vertical-align: top;
    text-align: left;
}

.width-1-3 {
    display: inline-block;
    width: 32%;
    vertical-align: top;
}

.width-1-2 {
    display: inline-block;
    width: 49%;
    height:86px;
    vertical-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
}

.width-1-1-x {
    display: inline-block;
    width: 98%;
    vertical-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
}

.width-1-2-x{
    display: inline-block;
    width: 49%;
    vertical-align: top;
    text-overflow: ellipsis;
    overflow: hidden;
}

.width-1-2-x > *{
    width: 100%;
    margin-top: 3px;
}

.width-1-1-x > *{
    width: 100%;
    margin-top: 3px;
}

.width-1-2 > button{
    width: 100%;
    margin-top: 3px;
}

.width-1-3 > *{
    width: 100%;
    margin-top: 3px;
}

.width-1-4 {
    display: inline-block;
    width: 24%;
    vertical-align: top;
}

.width-1-4 > *{
    width: 100%;
    margin-top: 3px;
}

.width-1-4-x {
    display: inline-block;
    width: 24.5%;
    vertical-align: top;
}
.width-1-4-x > *{
    width: 100%;
    margin-top: 3px;
}

.buttonTag {
    margin-top: 1px;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    /*border: solid #999999 1px !important;*/
    margin-bottom: 0px;
    padding: 0;
}

.buttonTag:disabled {
    border-color: #f0f0f0;
}

.uk-button-success {
    font-weight: bold;
}
.uk-button-success:hover {
    background: #f2a046;
}

.uk-button-active > a > p {
    color: #ffffff;
}

.buttonTag:hover :not(.uk-button-success) {
    color: #444444;
    background: #f7f7f7;
    /*border: 1px solid #f27e46;*/
}

.buttonTag:hover:enabled :not(.uk-button-success) {
    color: #444444;
    background: #f7f7f7;
    border: 1px solid #f27e46;
}

/*.buttonTag:hover:enabled {
    color: #444444;
    background: #f7f7f7;
    border: 1px solid #f27e46;
}*/

.buttonTag label[disabled]  {
    color: #f0f0f0;
}
.buttonTag input[type=checkbox] {
    margin-top: -2px;
}

.squaredCheckboxArchive {
    width: 24px;
    height: 24px;
    position: relative;
    display: inline-block;
}


.squaredGearArchive {
    width: 49px;
    height: 49px;
    position: relative;
    display: inline-block;
    vertical-align: 50%;
    color: #f27e46;
    background:#333333bb;
    border-radius: 50px;
}

.squaredMenuArchive{
    width: 49px;
    height: 49px;
    position: relative;
    display: inline-block;
    vertical-align: 50%;
    color: #f27e46;
    background:#3333330f;
    border-radius: 10px;
}

.squaredCheckbox {
    width: 18px;
    height: 18px;
    /*margin: 2px auto;*/
    position: absolute;
}

.squaredCheckboxEvent {
    width: 18px;
    height: 18px;
    position: relative;
    display: inline-block;
}

.squaredCheckbox label,.squaredCheckboxEvent label {
    cursor: pointer;
    position: absolute;
    width: 18px;
    height: 18px;
    top: 0px;
    left: 2px;
    border-radius: 4px;
    border: 1px #999999 solid;

    /*-webkit-box-shadow: inset 0px 1px 1px white, 0px 1px 3px rgba(0,0,0,0.5);
    -moz-box-shadow: inset 0px 1px 1px white, 0px 1px 3px rgba(0,0,0,0.5);
    box-shadow: inset 0px 1px 1px white, 0px 1px 3px rgba(0,0,0,0.5);
    background: #f5f5f5;*/

    /*background: -webkit-linear-gradient(top, #f5f5f5 0%, #f5f5f5 40%, #f5f5f5 100%);
    background: -moz-linear-gradient(top, #f5f5f5 0%, #f5f5f5 40%, #f5f5f5 100%);
    background: -o-linear-gradient(top, #f5f5f5 0%, #f5f5f5 40%, #f5f5f5 100%);
    background: -ms-linear-gradient(top, #f5f5f5 0%, #f5f5f5 40%, #f5f5f5 100%);
    background: linear-gradient(top, #f5f5f5 0%, #f5f5f5 40%, #f5f5f5 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f5f5f5', endColorstr='#f5f5f5',GradientType=0 );*/
}

.squaredCheckboxArchive label{
    position: absolute;
    width: 24px;
    height: 24px;
    top: -5px;
    left: -5px;
    border-radius: 6px;
    border: 1px #666666 solid;
    background-color: #f5f5f5;
    opacity: 0.8;
    filter: alpha(opacity=80);
}

.squaredCheckboxArchive label:after{
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    opacity: 0;
    content: '';
    position: absolute;
    width: 10px;
    height: 4px;
    background: transparent;
    top: 7px;
    left: 6px;
    border: 3px solid #f27e46;
    border-top: none;
    border-right: none;
    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

.absolute{
    position:absolute;
    overflow: hidden;
    background: rgba(48,48,48, .85);
    border-radius: 10px;
    width: 100%; /* Full width */
    z-index: 10000;
}

.disabilitato{
    opacity:0.8;
    border-color: red;
    cursor:not-allowed;
}

.squaredCheckboxArchive input[type=checkbox]:disabled + label:after{
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
    filter: alpha(opacity=80);
    opacity: 0.8;
    content: 'X';
    border:none;
    top: 5px;
    left: 7px;
    color:red;
    -webkit-transform: rotate(0);
    -moz-transform: rotate(0);
    -o-transform: rotate(0);
    -ms-transform: rotate(0);
    transform: rotate(0);
}

.fix-del{
    overflow: hidden;
    background-color: rgba(48,48,48, .85);
    position: fixed; /* Set the navbar to fixed position */
    top: 0px; /* Position the navbar at the top of the page */
    border-radius: 0px;
    width: 100%; /* Full width */
    z-index: 10000;
    margin-top: 0px;
}

#divDeletePlGm{
    color: #f27e46;
}

#menuDelete button {
    float: right;
    display: block;

    opacity: 1.0;
}

.playlistSelected{
    border: 1px #f27e46 solid;
    background-color: #f27e46;
}

.squaredCheckbox label:after, .squaredCheckboxEvent label:after {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    opacity: 0;
    content: '';
    position: absolute;
    width: 7px;
    height: 3px;
    background: transparent;
    top: 6px;
    left: 5px;
    border: 1px solid #333;
    border-top: none;
    border-right: none;

    -webkit-transform: rotate(-45deg);
    -moz-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
}

/*
.squaredCheckbox label:hover::after,.squaredCheckboxEvent label:hover::after{
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
    filter: alpha(opacity=10);
    opacity: 0.1;
}
*/

.squaredCheckbox input[type=checkbox]:checked + label:after, .squaredCheckboxEvent input[type=checkbox]:checked + label:after{
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: alpha(opacity=100);
    opacity: 1;
}
.squaredCheckbox input[type=checkbox],.squaredCheckboxEvent input[type=checkbox]{
    display:none;
}

.squaredCheckboxArchive input[type=checkbox]:checked + label:after{
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: alpha(opacity=100);
    opacity: 1;
}
.squaredCheckboxArchive input[type=checkbox]{
    display:none;
}

.innera {
    overflow: auto;
    width: 100%;
    /*height: 600px;*/
}
.outer {
    position: relative;
    width: 100%;
}

.alert {
    color: #ff0000;
    font-size: 13px;
    text-align: center;
    padding: 10px 5px 5px 5px;
}

.scrollable-div {
    overflow-y: auto;
    max-height: 800px;
    height:	800px
}

table thead {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 5;
    background: #fff;
}

.statsTeam {
    display: block;
    margin-top: 5px;
    text-transform: uppercase;
    /*font-size: 24px;
     border: 1px solid;
     border-color: grey;
    margin: 5px;
    padding: 5px;
    border-radius: 5px;
    background-color: #f5f5f5;*/
}

#searchGoals,#searchATT,#searchDIF,#searchPIF,#searchPIC{
    /*display: block;*/
    width: 150px;
    padding: 2px;
    margin: 5px 5px 0px 0px;
}

.logoImg {
    width: 60px;
    height: 60px;
}

.imgTeamLogo {
    width: 120px;
    height: 120px;
    object-fit: contain;
    filter: drop-shadow(0 0 5px #adadad) drop-shadow(0 0 5px #adadad);
}

.imgPlayerLogo {
    object-fit: contain;
    filter: drop-shadow(0 0 5px #adadad) drop-shadow(0 0 5px #adadad);
}

.imgSmartSearch {
    object-fit: contain;
    filter: drop-shadow(0 0 10px #adadad88) drop-shadow(0 0 10px #adadad88);
}

.logoCompImg {
    width: 85px;
    height: auto;
    object-fit: contain;
    filter: drop-shadow(0 0 5px #adadad) drop-shadow(0 0 5px #adadad);
}

.logoCountryImg {
    width: 65px;
    height: auto;
    box-shadow: 0px 1px 10px 0px;
}

.logoInternationalCountryImg {
    width: 130px;
    height: auto;
}

.nav-date{
    background: lightgray;
    font-weight: bold;
}

.logoCompImgX {
    min-width: 0px;
    min-height: 0px;

    max-width: 240px;
    max-height: 135px;
}

.logoCompImgX2{
    width: 240px;
    height: 240px;
}

.titlePlayerStats {
    font-size: 20px;
    margin-top: 10px;
    background: gainsboro;
    width: 100%;
    display: inline-block;
    border-radius: 3px;
    padding-top: 4px;
    padding-bottom: 4px;
}

#exportInfo {
    float: right;
    border: 1px solid #f27e46;
    width: 300px;
    height: 40px;
    border-radius: 5px;
    font-size: 14px;
    color: #f27e46;
}

#exportStatus {
    height: 40px;
    vertical-align: middle;
    margin: 10px;
    font-size: 16px;
}

#dropdownExport, #dropdownPlaylist {
    width: 400px;
    margin-left: -200px;
    font-size: 14px;
    /*    color: #f27e46;*/
}

#dropdownExport li, #dropdownPlaylist li {
    margin: 5px;
}

#exportStatus {
    overflow-y: auto;
    height: 130px;
}

#btnHD,#btnCinema,#btnTact,#btnTACT,#btnInfo{
    margin: 5px 0px 5px 5px;
}

.text-center-middle {
    text-align: center;
    vertical-align: middle;
}

azioni .uk-table th, .uk-table td {
    padding-top: 4px;
    padding-bottom: 4px;
    font-size: 11px;
}

#descrBox .tag, #spanNameAction .tag {
    font-size: 11px;
}

.topBarVideo {
    font-size: 12px;
    line-height: 30px;
    margin-left: 10px;
}

.orangeiconcolor {
    color:#f27e46;
}

.selectItem {
    width: 100px;
    font-size: 12px;
}

#spanNameMatch {
    color: #f0f0f0;
}

#autocompleteTeamPlayer .uk-dropdown, #autocompleteTeamPlayerWatchlist .uk-dropdown {
    width: 300px;
}

#autocompleteTeamPlayer .uk-nav li > a:hover, #autocompleteTeamPlayerWatchlist .uk-nav li > a:hover, .uk-autocomplete-results > li.uk-hover > a:hover {
    background: #ececec;
}

.sCapitalize {
    text-transform: lowercase;
}

#teamStats, #playerStats {
    text-align: left;
    font-size: 12px;
    border: solid 1px gainsboro;
    width: 100%;
    border-radius: 5px;
    margin: auto;
    overflow: auto;
}

.teamFilterButtonMobile {
    margin: 2px;
    width: 32%;
    height: 20px;
}

.teamFilterButton {
    display: block;
    margin: 2px;
    width: 100%;
    height: 40px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.statsTeam {
    font-size: 16px;
}

.teamFilterButton i ,.teamQuickSearch i,.width-1-2 i, .teamAdvancedFilterButton i{
    float: left;
    font-size: 150%;
    margin-top: 6px;
}

.teamAdvancedFilterButton {
    display: block;
    margin: 2px;
    width: 50%;
    height: 40px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.teamQuickSearch {
    margin: 2px;
    width: 24%;
    height: 40px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 12px;
}

#teamGeneralFilter {
    width: 100%;
}


.panelDiv {
    width: 48%;
    display: inline-block;
    vertical-align: top;
}

.panelSicsDiv {
    width: 48%;
    margin-right: 4px;
}

#tablePlayersTeam {
    line-height: 30px;
}
.grayback{
    background-color: lightgray;
}

.label-config {
    display: inline-block;
    padding: 10px;
    background: #f27e46;
    color: #ffffff;
    text-align: center;
    vertical-align: middle;
    text-transform: none;
    /*border: 1px solid rgba(0, 0, 0, 0.2);*/
    border-bottom-color: rgba(0, 0, 0, 0.3);
    background-origin: border-box;
    background-image: -webkit-linear-gradient(top, #f27e46, #f27e46);
    background-image: linear-gradient(to bottom, #f27e46, #f27e46);
    border-radius: 2px;
    margin-top: 5px;
}

div[class^="actionFor"] {
    border: 1px solid #f27e46;
    border-radius: 2px;
    padding: 5px;
}

.spinner {
    background: transparent;
    text-align: center;
    /*   height: 400px;
       width: 400px; */
    clear: both;
    color: #f27e46;
    font-size: 15px;
    position: absolute;
    left: 50%;
    top: 50%;
    z-index:1;
}

.spinner-small {
    background: transparent;
    text-align: center;
    clear: both;
    color: #f27e46;
    font-size: 5px;
}

.loader {
    border: 16px solid #f3f3f3; /* Light grey */
    border-top: 16px solid #f27e46; /* Blue */
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
}

.loader-small {
    border: 5px solid #f3f3f3; /* Light grey */
    border-top: 5px solid #f27e46; /* Blue */
    border-radius: 50%;
    width: 10px;
    height: 10px;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.width-height-auto{
    width: auto;
    height:auto;
    max-width: 100%;
    max-height: 100%;
}

.fc .fc-highlight{

    background-color: #f27e4644;
}

.marteiii{
    height: calc(90vh);
}

.fc-more-popover {
    max-height: 40%;
    overflow-y: auto;
}

#menuCalendar{
    background:#dddddd;
    padding: 20px 12px 20px 12px;
    margin: 0px;
    border-bottom: 1px #dddddd solid;
    border-right: 1px #dddddd solid;
}

#menuCalendar button{
    padding: 5px;
}

#menuCalendar ul{
    margin: 0;
}

#menuCalendar li{
    margin:5px;
}

#menuCalendar li:hover{
    text-decoration: underline;
}

.video-player-tools {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.video-player-tools-child {
    flex-grow: 1;
    accent-color: rgb(226, 100, 0);
}

.input-text-calendar-search {
    border: solid #999999 1px;
    padding: 0;
    font-size: 12px;
}

.currentClip, .currentClipPlaylist {
    background-color: #f27e46 !important;
    color: white;
}

.deletedClip {
    background-color: #ff4a4a !important;
    color: white;
}

.currentClip span label, .currentClipPlaylist span label, .deletedClip span label {
    border: 1px white solid !important;
    background-color: white;
}

.currentClip a, .currentClipPlaylist a {
    color: white;
}

.playlistTv {
    margin-bottom: 5px;
    margin-top: -5px;
}

.playlistTvButton {
    min-width: 75px;
}

.playlistTvTitle {
    font-weight: bold;
    font-size: 11px !important;
    text-transform: uppercase;
}

.playlistTvRow {
    height: 60px;
}

.playlistTvTinyText {
    font-size: 10px;
}

#playlistTvButtonDiv {
    display: flex;
    flex-wrap: wrap;
}

.hideSeekBar::-webkit-media-controls-timeline {
    display: none;
}

.hideSeekBar::-webkit-media-controls {
    display: none;
}

.blackout {
    background-color: black;
}

.blackout video {
    display: none;
}

.dynamicWidth {
    flex-basis: calc(50% - 5px);
    margin-right: 10px; /* Spazio tra i div */
}

.dynamicWidth:last-child {
    margin-right: 0;
}

.dynamicModalWidth {
    max-width: 50%
}

/* Media query per dispositivi mobili */
@media (max-width: 767px) {
    .dynamicWidth {
        flex-basis: 100%;
        margin-right: 0;
    }

    .dynamicModalWidth {
        max-width: 100%
    }
}

/* Media query per tablet */
@media (max-width: 959px) {
    .dynamicModalWidth {
        max-width: 75%
    }
}

.left-buttons,
.right-buttons {
    flex: 1;
}

.left-buttons button,
.right-buttons button {
    display: block;
    width: 100%;
}

a.disabled {
    background-color: #fafafa !important;
    color: #cccccc !important;
    border-color: rgba(0, 0, 0, 0.2) !important;
    background-image: none !important;
    box-shadow: none !important;
    text-decoration: none !important;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.01) !important;
    cursor: not-allowed !important;
}

.modalClose {
    position: absolute;
    top: 5px;
    right: 20px;
    font-size: 30px;
    cursor: pointer;
}

.buttons-columnVisibility.dt-button-active {
    background-color: #f2a046 !important;
    color: white !important;
}

.uk-autocomplete .uk-dropdown {
    max-height: 90vh;
    overflow-y: scroll;
}

#autoCompleteSeasons > ul > li > a.selected {
    background-color: #ececec;
}

#autoCompleteSeasons > ul > li > a:hover {
    background-color: #ececec;
}

.orangeOnHover:hover {
    color: #f27e46 !important;
}

div.dt-button-collection.fixed>:last-child {
    max-height: 100% !important;
}

.player-report-new-div, .team-report-new-div {
    height: 27px;
    width: 27px;
    background: linear-gradient(45deg, transparent 50%, #f2a046 50%);
    float: right;
    margin-right: -12px;
    border-radius: 2px;
}

.player-report-new-text, .team-report-new-text {
    font-size: 9px;
    color: white;
    transform: rotate(45deg);
    position: absolute;
    margin-top: -4px;
    margin-left: 7px;
}

.video-playlist-new-div {
    height: 22px;
    width: 22px;
    background: linear-gradient(45deg, transparent 50%, #f2a046 50%);
    float: right;
    margin-right: -10px;
    margin-top: -1px;
    border-radius: 2px;
}

.video-playlist-new-text {
    font-size: 8px;
    color: white;
    transform: rotate(45deg);
    position: absolute;
    margin-top: -3px;
    margin-left: 5px;
}

.stats-p90-new-div {
    height: 22px;
    width: 22px;
    background: linear-gradient(45deg, transparent 50%, #f2a046 50%);
    float: right;
    margin-right: -12px;
    margin-top: -1px;
    border-radius: 2px;
}

.stats-p90-new-text {
    font-size: 8px;
    color: white;
    transform: rotate(45deg);
    position: absolute;
    margin-top: -5px;
    margin-left: 5px;
}

.video-tabellino-new-div {
    height: 22px;
    width: 22px;
    background: linear-gradient(45deg, transparent 50%, #f2a046 50%);
    float: right;
    margin-right: -12px;
    margin-top: -8px;
    border-radius: 2px;
}

.video-tabellino-new-text {
    font-size: 8px;
    color: white;
    transform: rotate(45deg);
    position: absolute;
    margin-top: 3px;
    margin-left: 5px;
}

.video-ricerca-new-div {
    height: 22px;
    width: 22px;
    background: linear-gradient(45deg, transparent 50%, #f2a046 50%);
    float: right;
    margin-right: 1px;
    margin-top: -30px;
    border-radius: 2px;
}

.video-ricerca-new-text {
    font-size: 8px;
    color: white;
    transform: rotate(45deg);
    position: absolute;
    margin-top: 3px;
    margin-left: 5px;
}

.competition-statistics-new-div {
    height: 29px;
    width: 29px;
    background: linear-gradient(45deg, transparent 50%, #f27e46 50%);
    float: right;
    margin-right: -12px;
    margin-top: -7px;
}

.competition-statistics-new-text {
    font-size: 10px;
    color: white;
    transform: rotate(45deg);
    position: absolute;
    margin-top: 5px;
    margin-left: 7px;
}

.playlist-new-div {
    height: 27px;
    width: 27px;
    background: linear-gradient(45deg, transparent 50%, #f27e46 50%);
    float: right;
    margin-top: -14px;
    margin-right: -7px;
}

.smart-search-new-div {
    height: 22px;
    width: 22px;
    background: linear-gradient(45deg, transparent 50%, #f2a046 50%);
    float: right;
    margin-left: -10px;
    margin-top: -5px;
    border-radius: 2px;
}

.smart-search-new-text {
    font-size: 8px;
    color: white;
    transform: rotate(45deg);
    position: absolute;
    margin-top: 3px;
    margin-left: 5px;
}

.record-new-div {
    height: 22px;
    width: 22px;
    background: linear-gradient(45deg, transparent 50%, #d32c46 50%);
    float: right;
    margin-left: -3px;
    margin-top: -1px;
    border-radius: 2px;
}

.record-new-text {
    font-size: 8px;
    color: white;
    transform: rotate(45deg);
    position: absolute;
    margin-top: -3px;
    margin-left: 5px;
}

.playlist-new-text {
    font-size: 9px;
    color: white;
    transform: rotate(45deg);
    position: absolute;
    margin-top: 4px;
    margin-left: 7px;
}

.favorites-a:hover {
    color: #f27e46 !important;
    background-color: #f5f5f5 !important;
}

.favorites-content {
    display: grid;
    grid-template-areas:
        'header header'
        'player team';
    column-gap: 5em;
    z-index: 999;
    background-color: #f5f5f5;
    position: absolute;
    top: 4.3%;
    right: 0%;
    border: 2px #e0e0e0 solid;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    padding: 5px;
    max-height: 45vh;
    overflow-y: scroll;
}

.shortcuts-content {
    z-index: 999;
    background-color: #f5f5f5;
    position: absolute;
    top: 4.3%;
    right: 0%;
    border: 2px #e0e0e0 solid;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
    padding: 5px;
    max-height: 75vh;
    overflow-y: scroll;
    min-width: 15%;
}

.shortcuts-content-manage {
    z-index: 999;
    background-color: #f5f5f5;
    position: absolute;
    top: 4.3%;
    right: 0%;
    border: 2px #e0e0e0 solid;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
    padding: 5px;
    max-height: 75vh;
    overflow-y: scroll;
    width: 15%;
}

.shortcut-hover > div:hover {
    background-color: #ffffff;
}

.shortcut-hover > div > a:hover {
    background-color: red;
}

.center-vertically {
    display: flex;
    align-items: center;
}

.shortcut-add {
    margin-top: 5px;
    width: 100%;
    background: #ffffff;
}

.shortcut-add > button {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}

.shortcut-add > div {
    padding: 10px;
    border: 1px solid #e0e0e0;
}

/* Video.js Stuff - Keep the control visible */
.vjs-has-started.vjs-user-inactive.vjs-playing .vjs-control-bar {
    visibility: visible !important;
    opacity: 1 !important;
    transition-duration: 0s !important;
}

/* Serve per mostrare tempo trascorso con tempo totale */
.video-js .vjs-current-time,
.video-js .vjs-time-divider,
.video-js .vjs-duration,
.vjs-live .vjs-time-control,
.vjs-live .vjs-time-divider,
.video-js .vjs-current-time,
.video-js .vjs-duration {
    display: block !important;
}

.video-pre-progress-bar-container {
    height: 20px;
    width: 50vh;
    color: #000000 !important;
    background-color: #f1f1f1 !important;
    border-radius: 16px;
}

.video-pre-progress-bar {
    height: 100%;
    background-color: #f27e46 !important;
    border-radius: 16px;
}

.video-pre-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 90vh;
    opacity: 1;
    transition: opacity 0.5s ease-in-out; /* Imposta la transizione sull'opacità */
    flex-direction: column;
}

.animazioneFadeOut {
    opacity: 0;
}

#headerSubnavNoHover:hover {
    color: #f27e46 !important;
}

.container-progress-bar {
    width: 100%;
    background-color: #e5e5e5 !important;
    border-radius: 0.4rem;
    float: right;
    display: flex;
}

.progress {
    /*            background-color: green;*/
    height: 1.5rem;
    padding: 0;
    border-radius: 0.4rem;
    display: flex;
    justify-content: flex-start;
}

.progress span {
    font-weight: 700;
    text-align: center;
}

.zoomOnHover:hover {
    zoom: 1.1;
    font-size: calc(100% - 10%) !important;
}

.zoomOnHover:hover > span {
    font-size: calc(100% - 10%) !important;
}

.vjs-custom-waiting .vjs-loading-spinner
{
    display: block;
}
.video-js.vjs-custom-waiting .vjs-loading-spinner:before,
.video-js.vjs-custom-waiting .vjs-loading-spinner:after
{
    /* I just copied the same animation as in the default css file */
    -webkit-animation: vjs-spinner-spin 1.1s cubic-bezier(0.6, 0.2, 0, 0.8) infinite, vjs-spinner-fade 1.1s linear infinite;
    animation: vjs-spinner-spin 1.1s cubic-bezier(0.6, 0.2, 0, 0.8) infinite, vjs-spinner-fade 1.1s linear infinite;
}

.center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.d3-tip {
    line-height: 1;
    font-weight: bold;
    padding: 8px;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    border-radius: 2px;
    pointer-events: none;
}

/*
    BEGIN CUSTOM RANGE PICKER 2 HANDLERS
*/
section.range-slider {
    position: relative;
    width: 200px;
    height: 35px;
    text-align: center;
}

section.range-slider input {
    pointer-events: none;
    position: absolute;
    overflow: hidden;
    left: 0;
    top: 15px;
    width: 200px;
    outline: none;
    height: 18px;
    margin: 0;
    padding: 0;
}

section.range-slider input::-webkit-slider-thumb {
    pointer-events: all;
    position: relative;
    z-index: 1;
    outline: 0;
}

section.range-slider input::-moz-range-thumb {
    pointer-events: all;
    position: relative;
    z-index: 10;
    -moz-appearance: none;
    width: 9px;
}

section.range-slider .slider-track {
    position: absolute;
    background-color: blue;
    height: 5px;
    top: 21px; /* Regola questa posizione in base all'altezza dello slider */
    z-index: 0;
}

section.range-slider .slider-track-min {
    position: absolute;
    background-color: white;
    height: 7px;
    top: 20px;
    z-index: 1;
    background-color: #efefef;
    border: #b2b2b2 1px solid;
    border-bottom-left-radius: 5px;
    border-top-left-radius: 5px;
}

section.range-slider .slider-track-max {
    position: absolute;
    background-color: white;
    height: 7px;
    top: 20px;
    z-index: 1;
    background-color: #efefef;
    border: #b2b2b2 1px solid;
    border-bottom-right-radius: 5px;
    border-top-right-radius: 5px;
}
/*
    END CUSTOM RANGE PICKER 2 HANDLERS
*/

.dataTables_wrapper.no-footer .dataTables_scrollBody{
    border-bottom: 0px !important
}

.paginate_button.active > a {
    color: #f27e46 !important;
}

.vjs-layout-medium {
    max-height: 14em;
}

.cursor-pointer {
    cursor: pointer;
}

.dz-preview.dz-file-preview, .dz-preview.dz-file-preview .dz-image {
    min-width: 20%;
    width: 200px !important;
}

.dz-preview.dz-file-preview .dz-progress {
    top: 70% !important;
    width: 150px !important;
    margin-left: -75px !important;
}

/*
    GESTIONE LINGUE, HOVER E OPACITA'
*/
.language-container img:not(.current-language) {
    opacity: 0.4;
}

.language-container img:not(.current-language):hover {
    opacity: 1;
}

.row {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    width: 100%;
}

.column {
    display: flex;
    flex-direction: column;
    flex-basis: 100%;
    flex: 1;
}

.gray-hover:hover {
    background-color: #dbdbdb;
}

.align-items-center {
    align-items: center;
}

.invert-color {
    filter: invert(1);
}

.uk-button-active-red:not(:disabled) {
    background-origin: border-box;
    color: #ffffff !important;
    outline: none !important;
    text-decoration: none !important;
    border: 1px solid #f25a46 !important;
    background: #f25a46 !important;
}

.uk-text-decoration-underline {
    text-decoration: underline;
}

.justify-content-center {
    justify-content: center;
}

.video-title {
    left: 0!important;
    width: 100%!important;
    background-color: rgba(0, 0, 0, 0.25)!important; /* Black background with opacity */
    color: white!important; /* Text color */
    font-size: 20px!important;
    bottom: 30px!important;
    font-family: 'Titillium Web'!important;
}

.zoomOnHover > p {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
}

#azioni > tbody > tr > td:not(:first-child) {
    cursor: pointer;
}

.ball-icon {
    top: 5px;
    background-color: transparent!important;
    height: 13px!important;
    width: 13px!important;
    background-image: url("/sicstv/images/football.svg")!important;
    background-size: contain;
    background-repeat: no-repeat;
}

.redcard-icon {
    top: 5px;
    background-color: transparent!important;
    height: 13px!important;
    width: 13px!important;
    background-image: url("/sicstv/images/red_card.svg")!important;
    background-size: contain;
    background-repeat: no-repeat;
}

.redcard-double-yellow-icon {
    top: 5px;
    background-color: transparent!important;
    height: 13px!important;
    width: 13px!important;
    background-image: url("/sicstv/images/red_card_double.svg")!important;
    background-size: contain;
    background-repeat: no-repeat;
}

.substitution-icon {
    top: 5px;
    background-color: transparent!important;
    height: 13px!important;
    width: 13px!important;
    background-image: url("/sicstv/images/substitution.svg")!important;
    background-size: contain;
    background-repeat: no-repeat;
}

.missed-penalty-icon {
    top: 5px;
    background-color: transparent!important;
    height: 13px!important;
    width: 13px!important;
    background-image: url("/sicstv/images/missed_penalty.svg")!important;
    background-size: contain;
    background-repeat: no-repeat;
}

.autogol-icon {
    top: 5px;
    background-color: transparent!important;
    height: 13px!important;
    width: 13px!important;
    background-image: url("/sicstv/images/autogol.svg")!important;
    background-size: contain;
    background-repeat: no-repeat;
}

.upper-icon {
    top: -16px!important;
}

.video-js .vjs-control-bar {
    height: 4em!important;
}

.vjs-playback-rate .vjs-playback-rate-value {
    line-height: unset!important;
}

.vjs-button > .vjs-icon-placeholder:before {
    line-height: unset!important;
}

.vjs-icon-play,
.video-js .vjs-play-control .vjs-icon-placeholder,
.video-js .vjs-big-play-button .vjs-icon-placeholder:before,
.vjs-icon-placeholder,
.vjs-current-time-display,
.vjs-time-divider,
.vjs-duration-display {
    line-height: 4em!important;
}

.vjs-volume-horizontal {
    height: 4em!important;
}

.video-js .vjs-volume-bar {
    margin: 1.75em 0.45em!important;
}

.vjs-playback-rate .vjs-playback-rate-value {
    margin-top: 30%;
}

.vjs-tip-inner, .vjs-tip-arrow {
    margin-bottom: 2em;
}

.vjs-marker:hover {
    transform: scale(1.2, 1.2)!important;
}

.uk-modal.uk-open > .uk-modal-dialog.draggable-bound {
    height: auto!important;
}