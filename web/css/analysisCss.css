body{
	background-color: #262626;
}

#buttons {
	width:1110px;
	height: 250px;
	margin: 0 auto;
	/*border: solid red;*/
}

#tabsContainer {
	max-width: 49%;
	float:left;
}

#tabs {
	height: 250px;
	background: #f26522;
	position: relative;
	top: 28px;
	margin: 0px;
	/*margin: 0 auto;*/
	/*border: solid red;*/
}

#formSeasonId {
	width: 100px;
}

.tabs {
	width:700px;
	height: 200px;
}

.button {
	width: 30%;
	height: 40px;
	border: 1px solid #5a5a5a;
	margin: 5px 5px 5px 5px;
	border-radius: 5px;
	background-color: #5a5a5a;
	float: left;
}

.selected {
	background-color: #40b5d3;
}

.unselected {
	background-color: #5a5a5a;
}

.textbutton {
	display: block;
	line-height: 10px;
	text-align: center;
	vertical-align: middle;
	margin: 15px auto;
	color: #ffffff;
	white-space: pre-wrap;      /* CSS3 */   
	white-space: -moz-pre-wrap; /* Firefox */    
	white-space: -pre-wrap;     /* Opera <7 */   
	white-space: -o-pre-wrap;   /* Opera 7 */    
	word-wrap: break-word;      /* IE */
}

#container {
	background-color: #262626;
}

#playersChoice {
	width: 954px;
	height: 310px;
	/*background-color: red;*/
	border: solid white;
	float:right;
}

#createButton {
	background-color: yellow;
	border-radius: 3px;
}

#selectPartita, #selectSquadra {
	display: block;
}

#sceltaPartita span {
	display: inline-block;
	margin: 5px 10px;
}

#selectSquadra{
	display: inline;
	width: 100px;
}

#selectPartita {
	overflow-y: scroll;
}

.sezioneAnalisi {
	height: 220px;
	clear: both;
	/*border: solid 1px white;*/
}

#aggregati {
	vertical-align: bottom;
}

#sceltaGiocatori {
	min-height: 130px;
	width: 580px;
}

#sceltaPartita {
	width: 500px;
	/*margin: 0px 10px;*/
	/*clear: both;*/
	float:left;
}

.sceltaAnalisi{
	width: 500px;
	margin: 5px 10px;
	/*clear: both;*/
	float:left;
}

.checkboxPlayer {
	/*display: block;*/
	float: left;
	width: 145px;
	margin: 3px 3px 3px 0px;
	/*clear: both;*/
}

.lblScelta {
	width: 145px;
}

.titleLabel {
	clear: both;
}

#containerGrafico {
	/*margin: 30px 0px 0px 0px;*/
	height: 310px;
}

#hiddenGraphic {
	height: 600px;
	width: 99%;
}

#expandGraphic {
	cursor: pointer;
	margin: 3px;
	padding: 3px;
}

#graphicDiv{
	width:50%;
	float: right;
}

#filterChoice {
	padding: 10px;
	clear:both;
}

#tipoIntervalloFiltro {
	width: 100px;
}

#filtriGiocatoriSelezionati {
	border-radius: 10px;
	border: solid white;
	margin: 5px 5px;
	float: right;
	width: 350px;
	height: 200px;
}

#partiteSelezionate {
	border-radius: 10px;
	border: solid white;
	margin: auto;
	float: right;
	width: 390px;
	height: 200px;
	display: none;
}

#filtroIntervallo {
	clear: both;
	float: left;
}

.giocatoreScelto { 
	width: 100px; 
	height: 20px; 
	padding: 0.5em; 
	line-height: 10px;
	text-align: center;
	vertical-align: middle;
	background-color: yellow;
	margin: 2px;
	float: left;
	color: black;
	border-radius: 5px;
	border: solid yellow;
}

.partitaScelta { 
	width: 70px; 
	height: 10px; 
	padding: 0.5em; 
	line-height: 10px;
	text-align: center;
	vertical-align: middle;
	background-color: #33ccff;
	margin: 5px;
	float: left;
	color: black;
	border-radius: 5px;
	border: solid #33ccff;
}

.removeGiocatoreIcon,.removePartitaIcon {
	float:right;
	margin-top: -15px;
	margin-right: -5px;
}

.removeGiocatoreIcon img,.removePartitaIcon img{
	width: 10px;
	height: 10px;
}

.removeButton {
	float:right;
	margin-top: -23px;
	margin-right: 5px;
}

.removeButton img {
	width: 10px;
	height: 10px;
}

.spinnerFasce {
	width: 30px;
}


#dialog label, #dialog input { display:block; }
#dialog label { margin-top: 0.5em; }
#dialog input, #dialog textarea { width: 95%; }
#tabs { margin-top: 1em; }
#tabs li .ui-icon-close { float: left; margin: 0.4em 0.2em 0 0; cursor: pointer; }
#add_tab { cursor: pointer; }

.tabsContent {
	overflow: auto;
	border-top-left-radius: 0px;
	padding: 0px;
}



.ui-icon-close-button {
	display: inline;
}
