@charset "UTF-8";:root,[data-color-theme=light] {
    --orange: #f27e46;
    --orange-rbg: 242,126,70;
}

.border-orange {
    --border-opacity: 1;
    border-color: rgba(var(--orange-rgb),var(--border-opacity))!important
}

.border-top-orange {
    --border-opacity: 1;
    border-top-color: rgba(var(--orange-rgb),var(--border-opacity))!important
}

.border-bottom-orange {
    --border-opacity: 1;
    border-bottom-color: rgba(var(--orange-rgb),var(--border-opacity))!important
}

.border-start-orange {
    --border-opacity: 1;
    border-left-color: rgba(var(--orange-rgb),var(--border-opacity))!important
}

.border-end-orange {
    --border-opacity: 1;
    border-right-color: rgba(var(--orange-rgb),var(--border-opacity))!important
}

.text-orange {
    --text-opacity: 1;
    color: rgba(var(--orange-rgb),var(--text-opacity))!important
}

.bg-orange {
    --bg-opacity: 1;
    background-color: rgba(var(--orange-rgb),var(--bg-opacity))!important
}

.form-control.border-orange {
    --focus-ring-box-shadow: 0 0 0 0.125rem rgba(var(--orange-rgb), 0.25)
}

.form-check-input-orange {
    --focus-ring-box-shadow: 0 0 0 0.125rem rgba(var(--orange-rgb), 0.25);
    --component-active-bg: var(--orange)
}

.btn-flat-orange {
    --btn-bg: rgba(var(--orange-rgb), .1);
    --btn-color: var(--orange);
    --btn-border-color: var(--orange);
    --btn-hover-color: var(--btn-color);
    --btn-hover-bg: rgba(var(--orange-rgb), .2);
    --btn-hover-border-color: var(--orange);
    --btn-focus-shadow-rgb: var(--orange-rgb);
    --btn-active-color: var(--orange);
    --btn-active-bg: rgba(var(--orange-rgb), .25);
    --btn-active-border-color: var(--orange);
    --btn-disabled-color: rgba(var(--orange-rgb), .65);
    --btn-disabled-bg: rgba(var(--orange-rgb), .05)
}

.link-orange {
    --link-opacity: 1;
    color: rgba(var(--orange-rgb),var(--link-opacity))
}

.link-orange:focus,.link-orange:hover {
    color: rgba(var(--orange-rgb),var(--link-opacity));
    -webkit-filter: brightness(1.15);
    filter: brightness(1.15)
}