<?xml version="1.0" encoding="UTF-8"?>
<web-app version="2.5"
         xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns:security="http://www.springframework.org/schema/security"
         xsi:schemaLocation="
		 http://www.springframework.org/schema/security http://www.springframework.org/schema/security/spring-security-3.0.xsd
		 http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd">

    <!--	<security:global-method-security pre-post-annotations="enabled"/> -->
	
    <!-- Localizzazione application context -->
    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>
            /WEB-INF/applicationContext.xml
            /WEB-INF/applicationContext-security.xml
        </param-value>
    </context-param>

    <!-- projectname -->
    <context-param>
        <param-name>webAppRootKey</param-name>
        <param-value>sicstv</param-value>
    </context-param>
	
    <!-- root xml dati -->
    <context-param>
        <param-name>webRootXmlDataFolder</param-name>
        <param-value>C:\Lavori\sicstv\web\xml\</param-value> 
        <!--param-value>/var/www/html/vmrepo/repo/</param-value-->
        <!--<param-value>http://server.sics.it/vmrepo/repo/</param-value>-->
    </context-param>
	
    <!-- root report dati -->
    <context-param>
        <param-name>webRootReportFolder</param-name>
        <param-value>C:\Lavori\sicstv\web\xml\0\report\</param-value> 
        <!--param-value>/var/www/html/vmrepo/repo/0/report/</param-value-->
    </context-param>
	
    <!-- root mobile dati -->
    <context-param>
        <param-name>webRootXmlMobileFolder</param-name>
        <param-value>/var/www/html/vmrepo/repo/mobile/</param-value>
    </context-param>
	
	
    <!-- root digitalStadium -->
    <context-param>
        <param-name>webRootDigitalStadiumDataFolder</param-name>
        <param-value>/var/www/html/vmrepo/repo/digiatalstadium/</param-value> 
    </context-param>
	
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>

    <!-- Dichiarazione della servlet-dispatcher -->
    <servlet>
        <servlet-name>dispatcher</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <!-- Mappa tutte le request che provengono da file htm -->
    <servlet-mapping>
        <servlet-name>dispatcher</servlet-name>
        <url-pattern>*.htm</url-pattern>
    </servlet-mapping>
	
    <filter>
        <filter-name>springSecurityFilterChain</filter-name>
        <filter-class>org.springframework.web.filter.DelegatingFilterProxy</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>springSecurityFilterChain</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <filter-name>SessionFilter</filter-name>
        <filter-class>sics.filter.SessionInactiveFilter</filter-class>
        <init-param>
            <param-name>customSessionExpiredErrorCode</param-name>
            <param-value>901</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>SessionFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
	
    <filter>
        <filter-name>encodingFilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
        <init-param>
            <param-name>forceEncoding</param-name>
            <param-value>true</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>encodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <session-config>
        <session-timeout>120</session-timeout>
    </session-config>

    <!-- Punto di ingresso applicazione -->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <error-page>
        <error-code>404</error-code>
        <location>/WEB-INF/pages/error/errorePagina.jsp</location>
    </error-page>

    <!-- Descrizione risorsa database -->
    <resource-ref>
        <description>Web DB Connection</description>
        <res-ref-name>jdbc/appDS</res-ref-name>
        <res-type>javax.sql.DataSource</res-type>
        <res-auth>Container</res-auth>
    </resource-ref>
	
    <resource-ref>
        <description>Web DB Connection</description>
        <res-ref-name>jdbc/appDSProtezione</res-ref-name>
        <res-type>javax.sql.DataSource</res-type>
        <res-auth>Container</res-auth>
    </resource-ref>

    <listener>
        <listener-class>sics.listener.SessionListener</listener-class>
    </listener>
    <listener>
        <listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
    </listener>
    <listener>
        <listener-class>org.springframework.security.web.session.HttpSessionEventPublisher</listener-class>
    </listener>
    <listener>
        <listener-class>
            sics.listener.SicsTvServletContextListener
        </listener-class>
    </listener>
        
    <!--listener>
            <listener-class>org.springframework.web.context.request.RequestContextListener</listener-class>
    </listener-->

</web-app>
