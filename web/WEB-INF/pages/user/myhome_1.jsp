<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">
        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/flat/flat.css?<%=System.currentTimeMillis()%>" rel="stylesheet">
        <script type="text/javascript">
            // logout ajax
            var isMyHome = true;
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);
        </script>
    </head>
    <body>
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>
        <div>
            <div class="uk-grid">
                <div id="competitions" class="uk-width-7-10 uk-hidden-small">
                    <div id="breadcrumb">
                        <ul class="uk-breadcrumb uk-margin-left">
                            <li style="text-transform: uppercase;"><spring:message code="menu.user.mysicstv"/><i id="showCurrentFilter"></i></li>
                        </ul>
                    </div>
                    <div class="uk-flex uk-margin-small-top">
                        <ul class="uk-flex uk-thumbnav uk-flex-center uk-width-1-1 uk-margin-small-top">
                            <c:choose>
                                <c:when test="${mListPlaylist.size()>0 || formCompetitionAll.size()>0}">
                                    <li class="competitionBox uk-margin-left" >
                                        <a class="uk-flex-center uk-text-center" href="/sicstv/user/personalArchive.htm">
                                            <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-preview.png" alt="<spring:message code="menu.user.mysicstv_archivio"/>">
                                            <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.mysicstv_archivio"/>"><spring:message code="menu.user.mysicstv_archivio"/></p>
                                        </a>
                                    </li>
                                </c:when>
                                <c:otherwise>
                                    <li class="competitionBoxDisabled uk-margin-left">
                                        <span class="uk-flex-center uk-text-center">
                                            <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-preview.png" alt="<spring:message code="menu.user.mysicstv_archivio"/>">
                                            <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="MySics.tv"><spring:message code="menu.user.mysicstv_archivio"/></p>
                                        </span>
                                    </li>
                                </c:otherwise>
                            </c:choose>
                            <c:choose>
                                <c:when test="${formCompetitionAll.size()>0}">
                                    <li class="competitionBox uk-margin-left">
                                        <a class="uk-flex-center uk-text-center" href="/sicstv/user/mycompetition.htm">
                                            <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-tag3.png" alt="<spring:message code="menu.user.mysicstv_archivio"/>">
                                            <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.incontri"/>"><spring:message code="menu.user.incontri"/></p>
                                        </a>
                                    </li>
                                </c:when>
                                <c:otherwise>
                                    <li class="competitionBoxDisabled uk-margin-left">
                                        <span class="uk-flex-center uk-text-center">
                                            <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-tag3.png" alt="<spring:message code="menu.user.mysicstv_archivio"/>">
                                            <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.incontri"/>"><spring:message code="menu.user.incontri"/></p>
                                        </span>
                                    </li>
                                </c:otherwise>
                            </c:choose>
                        <!--</ul>
                    </div>
                    <div class="uk-flex uk-margin-small-top">
                        <ul class="uk-flex uk-thumbnav uk-flex-center uk-width-1-1 uk-margin-small-top">-->
                            <c:choose>
                                <c:when test="${!curUser.getGuest()}">
                                    <li class="competitionBox uk-margin-left">
                                        <a class="uk-flex-center uk-text-center" href="/sicstv/user/sicstag.htm">
                                            <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-requestSICS.png" alt="<spring:message code="menu.user.requestSicsAnalisi"/>">
                                            <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.requestSicsAnalisi"/>"><spring:message code="menu.user.requestSicsAnalisi"/></p>
                                        </a>
                                    </li>
                                </c:when>
                                <c:otherwise>
                                    <li class="competitionBoxDisabled uk-margin-left">
                                        <span class="uk-flex-center uk-text-center">
                                            <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-requestSICS.png" alt="<spring:message code="menu.user.requestSicsAnalisi"/>">
                                            <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.requestSicsAnalisi"/>"><spring:message code="menu.user.requestSicsAnalisi"/></p>
                                        </span>
                                    </li>
                                </c:otherwise>
                            </c:choose>
                            <c:choose>
                                <c:when test="${!curUser.getGuest()}">
                                    <li class="competitionBox uk-margin-left">
                                        <a class="uk-flex-center uk-text-center" href="/sicstv/user/mycalendar.htm">
                                            <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-calendar.png" alt="<spring:message code="menu.user.mysicstv_calendar"/>">
                                            <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.mysicstv_calendar"/>"><spring:message code="menu.user.mysicstv_calendar"/></p>
                                        </a>
                                    </li>
                                </c:when>
                                <c:otherwise>
                                    <li class="competitionBoxDisabled uk-margin-left">
                                        <span class="uk-flex-center uk-text-center">
                                            <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-calendar.png" alt="<spring:message code="menu.user.mysicstv_calendar"/>">
                                            <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.mysicstv_calendar"/>"><spring:message code="menu.user.mysicstv_calendar"/></p>
                                        </span>
                                    </li>
                                </c:otherwise>
                            </c:choose>
                        </ul>
                    </div>
                </div>
                <div id="tabMenu" class="uk-width-3-10">
                    <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" id='calendarTab'>
                        <li id="tabMatches" class="uk-active uk-hidden-small" data-uk-tab="{connect:'#matchesCalendarContent'}">
                            <a><spring:message code="menu.user.resocontoServer"/></a>
                        </li>
                        <li id="tabMatches2" class="uk-active uk-visible-small" data-uk-tab="{connect:'#matchesCalendarContent'}">
                            <a>MYSICS.TV</a>
                        </li>
                    </ul>
                    <ul class="uk-flex uk-thumbnav uk-flex-center uk-width-1-1 uk-margin-small-top uk-visible-small">
                        <c:choose>
                            <c:when test="${mListPlaylist.size()>0 || formCompetitionAll.size()>0}">
                                <li class="competitionBox uk-margin-left" >
                                    <a class="uk-flex-center uk-text-center" href="/sicstv/user/personalArchive.htm">
                                        <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-preview.png" alt="<spring:message code="menu.user.mysicstv_archivio"/>">
                                        <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.mysicstv_archivio"/>"><spring:message code="menu.user.mysicstv_archivio"/></p>
                                    </a>
                                </li>
                            </c:when>
                            <c:otherwise>
                                <li class="competitionBoxDisabled uk-margin-left">
                                    <span class="uk-flex-center uk-text-center">
                                        <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-preview.png" alt="<spring:message code="menu.user.mysicstv_archivio"/>">
                                        <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="MySics.tv"><spring:message code="menu.user.mysicstv_archivio"/></p>
                                    </span>
                                </li>
                            </c:otherwise>
                        </c:choose>
                        <c:choose>
                            <c:when test="${formCompetitionAll.size()>0}">
                                <li class="competitionBox uk-margin-left">
                                    <a class="uk-flex-center uk-text-center" href="/sicstv/user/mycompetition.htm">
                                        <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-tag3.png" alt="<spring:message code="menu.user.mysicstv_archivio"/>">
                                        <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.incontri"/>"><spring:message code="menu.user.incontri"/></p>
                                    </a>
                                </li>
                            </c:when>
                            <c:otherwise>
                                <li class="competitionBoxDisabled uk-margin-left">
                                    <span class="uk-flex-center uk-text-center">
                                        <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-tag3.png" alt="<spring:message code="menu.user.mysicstv_archivio"/>">
                                        <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.incontri"/>"><spring:message code="menu.user.incontri"/></p>
                                    </span>
                                </li>
                            </c:otherwise>
                        </c:choose>
                        <c:choose>
                            <c:when test="${!curUser.getGuest()}">
                                <li class="competitionBox uk-margin-left">
                                    <a class="uk-flex-center uk-text-center" href="/sicstv/user/sicstag.htm">
                                        <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-requestSICS.png" alt="<spring:message code="menu.user.requestSicsAnalisi"/>">
                                        <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.requestSicsAnalisi"/>"><spring:message code="menu.user.requestSicsAnalisi"/></p>
                                    </a>
                                </li>
                            </c:when>
                            <c:otherwise>
                                <li class="competitionBoxDisabled uk-margin-left">
                                    <span class="uk-flex-center uk-text-center">
                                        <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-requestSICS.png" alt="<spring:message code="menu.user.requestSicsAnalisi"/>">
                                        <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.requestSicsAnalisi"/>"><spring:message code="menu.user.requestSicsAnalisi"/></p>
                                    </span>
                                </li>
                            </c:otherwise>
                        </c:choose>
                        <c:choose>
                            <c:when test="${!curUser.getGuest()}">
                                <li class="competitionBox uk-margin-left">
                                    <a class="uk-flex-center uk-text-center" href="/sicstv/user/mycalendar.htm">
                                        <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-calendar.png" alt="<spring:message code="menu.user.mysicstv_calendar"/>">
                                        <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.mysicstv_calendar"/>"><spring:message code="menu.user.mysicstv_calendar"/></p>
                                    </a>
                                </li>
                            </c:when>
                            <c:otherwise>
                                <li class="competitionBoxDisabled uk-margin-left">
                                    <span class="uk-flex-center uk-text-center">
                                        <img class="logoCompImgX2 uk-margin-top uk-margin-left uk-margin-right" src="/sicstv/images/folder-icon-calendar.png" alt="<spring:message code="menu.user.mysicstv_calendar"/>">
                                        <p class="uk-margin-bottom uk-margin-top-remove" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.mysicstv_calendar"/>"><spring:message code="menu.user.mysicstv_calendar"/></p>
                                    </span>
                                </li>
                            </c:otherwise>
                        </c:choose>
                    </ul>
                    <ul id="matchesCalendarContent" class="uk-switcher">
                        <li class="uk-margin-top">
                            <div id="matchesList" class="uk-margin-top uk-margin-left">
                                <table class="uk-table uk-table-striped uk-table-large" style="border-collapse: collapse;  height: 100%; width: 100%; box-sizing: content-box;">
                                    <tbody>
                                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:50px;">
                                            <td class="uk-text-bold uk-width-1-2" style="font-size: 1.2em;text-transform: uppercase;"><spring:message code="menu.user.spazioTotale"/></td>
                                            <td class="uk-text-center" style="font-size: 1.2em;">${totSpace} GB</td>
                                        </tr>
                                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:50px;">
                                            <td class="uk-text-bold uk-width-1-2" style="font-size: 1.2em;text-transform: uppercase;"><spring:message code="menu.user.spazioDisponibile"/></td>
                                            <td class="uk-text-center" style="font-size: 1.2em;">${availableSpace} GB</td>
                                        </tr>
                                        <c:if test="${mUser.getIsShare()==true}">
                                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:50px;">
                                                <td class="uk-text-bold uk-width-1-2" style="font-size: 1.2em;text-transform: uppercase;"><spring:message code="menu.user.spazioUtilizzato"/></td>
                                                <td class="uk-text-center" style="font-size: 1.2em;">${usedSpace} GB</td>
                                            </tr>
                                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:50px;">
                                                <td class="uk-text-bold uk-width-1-2" style="font-size: 1.2em;text-transform: uppercase;"><spring:message code="menu.user.spazioUtilizzatoPartite"/></td>
                                                <td class="uk-text-center" style="font-size: 1.2em;">${usedSpaceFixture} GB</td>
                                            </tr>
                                        </c:if>

                                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:50px;">
                                            <td class="uk-text-bold uk-width-1-2" style="font-size: 1.2em;text-transform: uppercase;"><spring:message code="menu.user.spazioUtilizzatoPlaylist"/></td>
                                            <td class="uk-text-center" style="font-size: 1.2em;">${usedSpacePlaylist} GB</td>
                                        </tr>
                                        <c:if test="${mUser.getIsShare()==true}">
                                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:50px;">
                                                <td class="uk-text-bold uk-width-1-2" style="font-size: 1.2em;text-transform: uppercase;"><spring:message code="menu.user.numeroFile"/></td>
                                                <td class="uk-text-center" style="font-size: 1.2em;">${totFile}</td>
                                            </tr>
                                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:50px;">
                                                <td class="uk-text-bold uk-width-1-2" style="font-size: 1.2em;text-transform: uppercase;"><spring:message code="menu.user.numeroPartite"/></td>
                                                <td class="uk-text-center" style="font-size: 1.2em;">${numFixture}</td>
                                            </tr>
                                        </c:if>
                                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:50px;">
                                            <td class="uk-text-bold uk-width-1-2" style="font-size: 1.2em;text-transform: uppercase;"><spring:message code="menu.user.numeroPlaylist"/></td>
                                            <td class="uk-text-center" style="font-size: 1.2em;">${numPlaylist}</td>
                                        </tr>
                                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:50px;">
                                            <td class="uk-text-bold uk-width-1-2" style="font-size: 1.2em;text-transform: uppercase;"><spring:message code="menu.user.downloadDisp"/></td>
                                            <c:if test="${(totDownload-numDownload) >=4}">
                                                <td class="uk-text-center uk-text-success">
                                                    ${numDownload} / ${totDownload}
                                                    <span data-uk-tooltip title="<spring:message code="menu.user.infoDownload"/>"> <i class="uk-icon-info-circle"></i></span>
                                                </td>
                                            </c:if>
                                            <c:if test="${(totDownload-numDownload)  < 4  && (downloadLimit-numDownload) > 0}">
                                                <td class="uk-text-center uk-text-warning">
                                                    ${numDownload} / ${totDownload}
                                                    <span data-uk-tooltip title="<spring:message code="menu.user.infoDownload"/>"> <i class="uk-icon-info-circle"></i></span>
                                                </td>
                                            </c:if>
                                            <c:if test="${(totDownload-numDownload) <= 0}">
                                                <td class="uk-text-center uk-text-danger">
                                                    ${numDownload} / ${totDownload}
                                                    <span data-uk-tooltip title="<spring:message code="menu.user.infoDownload"/>"> <i class="uk-icon-info-circle"></i></span>
                                                </td>
                                            </c:if>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </body>
</html>
