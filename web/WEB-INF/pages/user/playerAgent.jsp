<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/amcharts.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/css/flat/flat.css" rel="stylesheet">
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />

        <!-- D3.js Library for canvas/svg -->
        <script src="https://d3js.org/d3.v5.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.1/d3-tip.min.js"></script>
        <script src="/sicstv/js/sics-canvas-player.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/js/temperatureMap.js?<%=System.currentTimeMillis()%>"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>

        <script type="text/javascript">
            var dropzone;
            $(document).ready(function () {
                // Configura Dropzone
                Dropzone.autoDiscover = false;

                initSearchFilter();
            });

            function manageUploadLogo(agentId) {
                if ($(event.target).attr("disabled")) {
                    UIkit.notify("<spring:message code="upload.logo.permission.denied"/>", {status: 'danger', timeout: 1000});
                    return;
                }

                if (typeof dropzone === "undefined") {
                    dropzone = initializeLogoDropzone("uploadLogoDropzone");
                }

                dropzone.options.url = dropzone.options.baseUrl + "agentId=" + agentId;
                $("#upload-logo-modal").addClass("uk-open");
                $("#upload-logo-modal").removeClass("uk-hidden");
            }

            function closeUploadLogo() {
                $("#upload-logo-modal").removeClass("uk-open");
                $("#upload-logo-modal").addClass("uk-hidden");
            }

            function manageUploadDetails(agentId) {
                if ($(event.target).attr("disabled")) {
                    UIkit.notify("<spring:message code="upload.logo.permission.denied"/>", {status: 'danger', timeout: 1000});
                    return;
                }
                $("#upload-details-modal").addClass("uk-open");
                $("#upload-details-modal").removeClass("uk-hidden");
            }

            function closeUploadDetails() {
                $("#upload-details-modal").removeClass("uk-open");
                $("#upload-details-modal").addClass("uk-hidden");
            }

            function saveDetails() {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/saveAgentDetails.htm",
                    cache: false,
                    data: encodeURI("agentId=${mAgent.id}&name=" + $("#details-name").val() + "&street=" + $("#details-street").val() + "&location=" + $("#details-location").val() + "&phone=" + $("#details-phone").val() + "&email=" + $("#details-email").val() + "&website=" + $("#details-website").val() + "&countryId=" + $("#details-country").val()),
                    success: function () {
                        location.reload();
                    }
                });
            }
        </script>

        <style>
            .container {
                display: flex;
                width: 100%;
            }

            .box2 {
                width: 50%;
                height: 100%;
                float: left;
            }

            .box3-header {
                width: 33%;
                height: 50%;
                float: left;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .box4-header {
                width: 25%;
                height: 50%;
                float: left;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .box3 {
                width: 33%;
                height: 50%;
                float: left;
            }

            .box100 {
                width: 100%;
                height: auto;
                float: left;
            }

            .box97 {
                width: 97%;
                height: auto;
                float: left;
            }

            .box70 {
                width: 70%;
                height: auto;
                float: left;
            }

            .box30 {
                width: 30%;
                height: auto;
                float: left;
            }

            .large-div {
                vertical-align: middle
            }

            .tableHeader {
                text-align: center;
                height: 10px;
            }

            .tableRow {
                text-align: center;
            }
        </style>
    </head>

    <body>

        <%@ include file="header.jsp" %>
        <div id="breadcrumb">
            <c:if test="${mUser.groupsetId == 2}">
                <span onclick="manageUploadLogo(${mAgent.id})" class="uk-float-right uk-margin-remove" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code="upload.playeragent.photo"/>"><i class="uk-icon-upload" ${mUser.groupsetId != 2 ? "disabled" : ""}></i></span>
                <span onclick="manageUploadDetails(${mAgent.id})" class="uk-float-right uk-margin-right" data-uk-tooltip="{pos:'bottom-left'}" title="Update details"><i class="uk-icon-edit" ${mUser.groupsetId != 2 ? "disabled" : ""}></i></span>
                </c:if>
            <ul class="uk-breadcrumb uk-margin-left">
                <li style="text-transform:uppercase;">
                    <a href="/sicstv/user/home.htm?tab=playerAgentsTab"><spring:message code="player.agents"/></a>
                </li>
                <li style="text-transform:uppercase;">${mAgent.name}</li>
            </ul>
        </div>

        <div class="uk-margin-top uk-margin-left uk-margin-right uk-flex">
            <div class="uk-width-2-10">
                <div class="uk-flex justify-content-center align-items-center uk-flex-column">
                    <img height="150px" width="150px" class="imgPlayerLogo" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/agentlogo/${mAgent.photo}.png" onerror="this.src='/sicstv/images/briefcase.svg'">
                    <div class="uk-flex justify-content-center align-items-center uk-flex-column uk-margin-bottom">
                        <p class="uk-margin-bottom-remove" style="font-size: 16px;">
                            ${mAgent.name}
                        </p>
                        <c:if test="${mAgent.countryLogo != null}">
                            <img width="20" height="20" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mAgent.countryLogo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'">
                            ${mAgent.countryName}
                        </c:if>
                    </div>
                    <c:if test="${mAgent.street != null}">
                        <p class="uk-margin-top-remove uk-margin-bottom-remove" style="font-size: 12px;">
                            <b><spring:message code="player.agent.location"/>:</b> ${mAgent.street}
                            <c:if test="${mAgent.location != null}">
                                (${mAgent.location})
                            </c:if>
                        </p>
                    </c:if>
                    <c:if test="${mAgent.phone != null}">
                        <p class="uk-margin-top-remove uk-margin-bottom-remove" style="font-size: 12px;">
                            <b><spring:message code="player.agent.phone"/>:</b> ${mAgent.phone}
                        </p>
                    </c:if>
                    <c:if test="${mAgent.email != null}">
                        <p class="uk-margin-top-remove uk-margin-bottom-remove" style="font-size: 12px;">
                            <b><spring:message code="player.agent.email"/>:</b> ${mAgent.email}
                        </p>
                    </c:if>
                    <c:if test="${mAgent.website != null}">
                        <p class="uk-margin-top-remove uk-margin-bottom-remove" style="font-size: 12px;">
                            <b><spring:message code="player.agent.website"/>:</b> ${mAgent.website}
                        </p>
                    </c:if>
                </div>
            </div>
            <div class="uk-width-8-10">
                <c:if test="${!mGoalkeepers.isEmpty()}">
                    <span class="titlePlayerStats uk-text-center"><spring:message code="ruoli.portieri"/></span>
                    <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-10 uk-width-1-1 uk-margin-small-top" >
                        <c:forEach var="player" items="${mGoalkeepers}">
                            <li class="uk-margin-left" id="${player.id}" onclick="location.href = '/sicstv/user/player.htm?personal=false&playerId=${player.id}'" style="min-height: 200px;">
                                <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height: 150px;">
                                    <div class="competitionBox">
                                        <c:choose>
                                            <c:when test="${player.photo != null}">
                                                <img style="${extraCss}" class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${player.photo}.png" alt="${player.last_name} ${player.first_name}" class="uk-margin-top uk-margin-bottom" width="200">
                                            </c:when>
                                            <c:otherwise>
                                                <img style="width: 70px; height: 85px; ${extraCss}" class="logoCompImg" src="/sicstv/images/user_gray.png" alt="${player.last_name} ${player.first_name}" class="uk-margin-top uk-margin-bottom">
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                    <span class="uk-text-medium" style="margin: 0">#${player.numero}</span><br/>
                                    <span style="font-size: 11px; margin: 0"><b>${player.last_name}</b></span><br/>
                                    <span style="font-size: 11px; margin: 0">${player.first_name}</span><br/>
                                    <span style="font-size: 11px; margin: 0; margin-bottom: 5px">${player.getBornDateYear()}</span>
                                </a>
                            </li>
                        </c:forEach>
                    </ul>
                </c:if>
                <c:if test="${!mDefenders.isEmpty()}">
                    <span class="titlePlayerStats uk-text-center"><spring:message code="ruoli.difensori"/></span>
                    <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-10 uk-width-1-1 uk-margin-small-top" >
                        <c:forEach var="player" items="${mDefenders}">
                            <li class="uk-margin-left" id="${player.id}" onclick="location.href = '/sicstv/user/player.htm?personal=false&playerId=${player.id}'" style="min-height: 200px;">
                                <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height: 150px;">
                                    <div class="competitionBox">
                                        <c:choose>
                                            <c:when test="${player.photo != null}">
                                                <img style="${extraCss}" class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${player.photo}.png" alt="${player.last_name} ${player.first_name}" class="uk-margin-top uk-margin-bottom" width="200">
                                            </c:when>
                                            <c:otherwise>
                                                <img style="width: 70px; height: 85px; ${extraCss}" class="logoCompImg" src="/sicstv/images/user_gray.png" alt="${player.last_name} ${player.first_name}" class="uk-margin-top uk-margin-bottom">
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                    <span class="uk-text-medium" style="margin: 0">#${player.numero}</span><br/>
                                    <span style="font-size: 11px; margin: 0"><b>${player.last_name}</b></span><br/>
                                    <span style="font-size: 11px; margin: 0">${player.first_name}</span><br/>
                                    <span style="font-size: 11px; margin: 0; margin-bottom: 5px">${player.getBornDateYear()}</span>
                                </a>
                            </li>
                        </c:forEach>
                    </ul>
                </c:if>
                <c:if test="${!mMidfielders.isEmpty()}">
                    <span class="titlePlayerStats uk-text-center"><spring:message code="ruoli.centrocampisti"/></span>
                    <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-10 uk-width-1-1 uk-margin-small-top" >
                        <c:forEach var="player" items="${mMidfielders}">
                            <li class="uk-margin-left" id="${player.id}" onclick="location.href = '/sicstv/user/player.htm?personal=false&playerId=${player.id}'" style="min-height: 200px;">
                                <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height: 150px;">
                                    <div class="competitionBox">
                                        <c:choose>
                                            <c:when test="${player.photo != null}">
                                                <img style="${extraCss}" class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${player.photo}.png" alt="${player.last_name} ${player.first_name}" class="uk-margin-top uk-margin-bottom" width="200">
                                            </c:when>
                                            <c:otherwise>
                                                <img style="width: 70px; height: 85px; ${extraCss}" class="logoCompImg" src="/sicstv/images/user_gray.png" alt="${player.last_name} ${player.first_name}" class="uk-margin-top uk-margin-bottom">
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                    <span class="uk-text-medium" style="margin: 0">#${player.numero}</span><br/>
                                    <span style="font-size: 11px; margin: 0"><b>${player.last_name}</b></span><br/>
                                    <span style="font-size: 11px; margin: 0">${player.first_name}</span><br/>
                                    <span style="font-size: 11px; margin: 0; margin-bottom: 5px">${player.getBornDateYear()}</span>
                                </a>
                            </li>
                        </c:forEach>
                    </ul>
                </c:if>
                <c:if test="${!mStrikers.isEmpty()}">
                    <span class="titlePlayerStats uk-text-center"><spring:message code="ruoli.attaccanti"/></span>
                    <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-10 uk-width-1-1 uk-margin-small-top" >
                        <c:forEach var="player" items="${mStrikers}">
                            <li class="uk-margin-left" id="${player.id}" onclick="location.href = '/sicstv/user/player.htm?personal=false&playerId=${player.id}'" style="min-height: 200px;">
                                <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height: 150px;">
                                    <div class="competitionBox">
                                        <c:choose>
                                            <c:when test="${player.photo != null}">
                                                <img style="${extraCss}" class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${player.photo}.png" alt="${player.last_name} ${player.first_name}" class="uk-margin-top uk-margin-bottom" width="200">
                                            </c:when>
                                            <c:otherwise>
                                                <img style="width: 70px; height: 85px; ${extraCss}" class="logoCompImg" src="/sicstv/images/user_gray.png" alt="${player.last_name} ${player.first_name}" class="uk-margin-top uk-margin-bottom">
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                    <span class="uk-text-medium" style="margin: 0">#${player.numero}</span><br/>
                                    <span style="font-size: 11px; margin: 0"><b>${player.last_name}</b></span><br/>
                                    <span style="font-size: 11px; margin: 0">${player.first_name}</span><br/>
                                    <span style="font-size: 11px; margin: 0; margin-bottom: 5px">${player.getBornDateYear()}</span>
                                </a>
                            </li>
                        </c:forEach>
                    </ul>
                </c:if>
            </div>
        </div>

        <div id="upload-logo-modal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeUploadLogo();">&times;</span>
                    <h3><spring:message code="upload.playeragent.photo"/></h3>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <form action="/" class="dropzone" id="uploadLogoDropzone">
                            <div class="dz-message">
                                <h2><spring:message code="menu.upload.selectlogo"/></h2>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div id="upload-details-modal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 15%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeUploadDetails();">&times;</span>
                    <div class="container" style="min-height: 25vh; max-height: 90vh;">
                        <ul class="uk-nav uk-nav-dropdown">
                            <li>
                                <span style="font-size: 15px; font-weight: bold; text-transform: uppercase">Update Agent Details</span>
                            </li>
                            <li style="margin-top: 10px">
                                <div style="width: 20%; float: left">
                                    Name
                                </div>
                                <div style="width: 80%">
                                    <input type="text" id="details-name" maxlength="128" value="${mAgent.name}"/>
                                </div>
                            </li>
                            <li style="margin-top: 10px">
                                <div style="width: 20%; float: left">
                                    Street
                                </div>
                                <div style="width: 80%">
                                    <input type="text" id="details-street" maxlength="128" value="${mAgent.street}"/>
                                </div>
                            </li>
                            <li style="margin-top: 10px">
                                <div style="width: 20%; float: left">
                                    <spring:message code="player.agent.location"/>
                                </div>
                                <div style="width: 80%">
                                    <input type="text" id="details-location" maxlength="128" value="${mAgent.location}"/>
                                </div>
                            </li>
                            <li style="margin-top: 10px">
                                <div style="width: 20%; float: left">
                                    <spring:message code="player.agent.phone"/>
                                </div>
                                <div style="width: 80%">
                                    <input type="text" id="details-phone" maxlength="128" value="${mAgent.phone}"/>
                                </div>
                            </li>
                            <li style="margin-top: 10px">
                                <div style="width: 20%; float: left">
                                    <spring:message code="player.agent.email"/>
                                </div>
                                <div style="width: 80%">
                                    <input type="text" id="details-email" maxlength="128" value="${mAgent.email}"/>
                                </div>
                            </li>
                            <li style="margin-top: 10px">
                                <div style="width: 20%; float: left">
                                    <spring:message code="player.agent.website"/>
                                </div>
                                <div style="width: 80%">
                                    <input type="text" id="details-website" maxlength="128" value="${mAgent.website}"/>
                                </div>
                            </li>
                            <li style="margin-top: 10px">
                                <div style="width: 20%; float: left">
                                    Nazionalità
                                </div>
                                <div style="width: 80%">
                                    <span class="uk-form">
                                        <select id="details-country">
                                            <option value="0" <c:if test="${mAgent.countryId != null && mAgent.countryId == 0}">selected</c:if>></option>
                                            <c:if test="${mCountries != null}">
                                                <c:forEach var="country" items="${mCountries}">
                                                    <option value="${country.id}" <c:if test="${mAgent.countryId != null && mAgent.countryId == country.id}">selected</c:if>>${country.name}</option>
                                                </c:forEach>
                                            </c:if>
                                        </select>
                                    </span>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="uk-modal-footer uk-text-right uk-width-1-1">
                        <button style="min-width:80px;" onclick="closeUploadDetails();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                        <button style="min-width:80px;" onclick="saveDetails();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.conferma"/></button>
                    </div>
                </div>
            </div>
        </div>

    </body>
</html>