<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.lazyload/1.9.1/jquery.lazyload.min.js"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/slider.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/flat/flat.css?<%=System.currentTimeMillis()%>" rel="stylesheet">

        <script type="text/javascript">
            var pageStatus = [];
            var totMatches = [];
            var numMatches = [];
            var matchPerPage = 25;

            $(document).ready(function () {
                //jsRefreshMatches(1);
                initSearchFilter();
                //if ($(window).width() <= 768) {
                //$("#liCompetions").addClass("uk-active");
                //$("#tabMatches").removeClass("uk-active");
                //} else {
                //$("#tabMatches").addClass("uk-active");
                //$("#matches").addClass("uk-active");
                //$("#liCompetions").removeClass("uk-active");
                //}

                // check if need to click any tab
                // only if it's loaded from back/forward button
                if (window.performance) {
                    var navEntries = window.performance.getEntriesByType('navigation');
                    if ((navEntries.length > 0 && navEntries[0].type === 'back_forward') || (window.performance.navigation && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD)) {
                        let clickElement = sessionStorage.getItem("sicstv/600/click");
                        if (clickElement !== null && clickElement) {
                            if ($("#" + clickElement).length > 0) {
                                $("#" + clickElement).click();
                            }
                        }
                    } else {
                        removeTabClick();
                    }
                }

                var tab = new URL(document.URL).searchParams.get("tab");
                if (typeof tab !== "undefined" && tab) {
                    $("#" + tab).click();
                }
            });

            // logout ajax
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);

            function jsRefreshMatches(page) {

                refreshMatches("matchesList", "matches", page);
            }




            /*
             function jsDownload(id) {
             
             var strUrl = "/sicstv/user/download.htm?id=" + id;
             $.ajax({
             type: "GET",
             url: strUrl,
             cache: false,
             success: function (msg) {
             if (msg.substr(0, 4) === "true") {
             UIkit.notify(msg.substr(5), {status: 'danger'});
             } else {
             UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success'});
             window.location.replace(msg.substr(5));
             }
             }
             });
             }
             */

            function filterData() {
                readFilterMulti(false);
                filterMatches('matches', 1);
            }

            function saveTabClick(elementId) {
                sessionStorage.setItem("sicstv/600/click", elementId);

                if (elementId === "playerAgentsTab") {
                    if ($("#player-agents-container").length === 0) {
                        $("#playerAgents").html('<center><div class="loader"></div></center>');
                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/playerAgents.htm",
                            cache: false,
                            data: encodeURI("sort=1"),
                            success: function (result) {
                                $("#playerAgents").empty();
                                $("#playerAgents").append(result);
                            },
                            error: function () {
                                UIkit.notify("<spring:message code="generic.error"/>", {status: 'danger', timeout: 1000});
                            }
                        });
                    }
                }
            }

            function removeTabClick() {
                sessionStorage.removeItem("sicstv/600/click");
            }

        </script>
    </head>
    <body>

        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>

        <div>
            <div class="uk-grid">

                <div id="countries" class="uk-width-1-1">

                    <div id="breadcrumb">
                        <ul class="uk-breadcrumb uk-margin-left">
                            <li style="text-transform:uppercase;" id="liBreadcrumb"><spring:message code="menu.homepage"/> <i id="showCurrentFilter"></i></li>
                            <!--
                            <c:if test="${country}">
                                <li id="liBreadcrumb"><spring:message code="menu.user.countries"/> <i id="showCurrentFilter"></i></li>
                            </c:if>
                            <c:if test="${!country}">
                                <li id="liBreadcrumb"><spring:message code="menu.user.internationalCompetitions"/> <i id="showCurrentFilter"></i></li>
                            </c:if>
                            -->
                        </ul>
                    </div>

                    <div id="tabMenu" class="">
                        <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#matchesCalendarContent'}" id="calendarTab">
                            <li id="countriesTab" onclick="removeTabClick();" 
                                <c:if test="${country}">
                                    aria-expanded="true" class="uk-active"
                                </c:if>
                                <c:if test="${!country}">
                                    aria-expanded="false"
                                </c:if>
                                ><a><spring:message code="menu.user.countries" /></a></li>

                            <li id="otherCountriesTab" aria-expanded="false" onclick="saveTabClick('otherCountriesTab');"><a><spring:message code="menu.user.other.countries" /></a></li>
                            <li id="internationalCompetitionsTab"  onclick="saveTabClick('internationalCompetitionsTab');"
                                <c:if test="${country}">
                                    aria-expanded="false"
                                </c:if>
                                <c:if test="${!country}">
                                    aria-expanded="true" class="uk-active"
                                </c:if>
                                ><a><spring:message code="menu.user.internationalCompetitions" /></a></li>
                            <li id="playerAgentsTab" onclick="saveTabClick('playerAgentsTab');"><a><spring:message code="player.agents"/></a></li>
                        </ul>
                    </div>
                    <ul id="matchesCalendarContent" class="uk-switcher">
                        <li id="countriesLi">
                            <div id="countries">
                                <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-small-1-5 uk-grid-width-medium-1-6 uk-grid-width-large-1-7 uk-grid-width-xlarge-1-18 uk-width-1-1 uk-margin-small-top" >
                                    <c:forEach var="item" items="${formCountryAll}">
                                        <c:choose>
                                            <c:when test="${item.visible}">
                                                <li class="uk-margin-left" id="${item.id}" onclick="location.href = '/sicstv/user/country.htm?countryId=${item.id}'" style="min-height: 110px;">
                                                    <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height:80px;" href="/sicstv/user/country.htm?countryId=${item.id}">
                                                        <div class="competitionBox">
                                                            <img class="logoCountryImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'" alt="${item.name}">
                                                        </div>
                                                        <p style="margin-bottom: 0; margin-top: 3px; text-transform: uppercase;">${item.name}</p>
                                                    </a>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li class="uk-margin-left" id="${item.id}">
                                                    <span class="uk-text-center" style="margin-top: 5px; min-height: 110px;">
                                                        <div class="competitionBoxDisabled">
                                                            <img class="logoCountryImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'" alt="${item.name}">
                                                        </div>
                                                        <p style="margin-bottom: 0; margin-top: 3px; text-transform: uppercase; color: #bfbfbf">${item.name}</p>
                                                    </span>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:forEach>
                                </ul>
                            </div>
                        </li>

                        <li id="otherCountriesLi">
                            <div id="otherCountries">
                                <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-small-1-5 uk-grid-width-medium-1-6 uk-grid-width-large-1-7 uk-grid-width-xlarge-1-18 uk-width-1-1 uk-margin-small-top" >
                                    <c:forEach var="item" items="${formOtherCountryAll}">
                                        <c:choose>
                                            <c:when test="${item.visible}">
                                                <li class="uk-margin-left" id="${item.id}" onclick="location.href = '/sicstv/user/country.htm?countryId=${item.id}'" style="min-height: 110px;">
                                                    <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height:80px;" href="/sicstv/user/country.htm?countryId=${item.id}">
                                                        <div class="competitionBox">
                                                            <img class="logoCountryImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'" alt="${item.name}">
                                                        </div>
                                                        <p style="margin-bottom: 0; margin-top: 3px; text-transform: uppercase;">${item.name}</p>
                                                    </a>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li class="uk-margin-left" id="${item.id}">
                                                    <span class="uk-text-center" style="margin-top: 5px; min-height: 110px;">
                                                        <div class="competitionBoxDisabled">
                                                            <img class="logoCountryImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'" alt="${item.name}">
                                                        </div>
                                                        <p style="margin-bottom: 0; margin-top: 3px; text-transform: uppercase; color: #bfbfbf">${item.name}</p>
                                                    </span>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:forEach>
                                </ul>
                            </div>
                        </li>

                        <li id="internationalCompetitionLi">
                            <div id="internationalCompetition">
                                <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-3 uk-grid-width-small-1-4 uk-grid-width-medium-1-5 uk-grid-width-large-1-6 uk-grid-width-xlarge-1-8 uk-width-1-1 uk-margin-small-top" >
                                    <c:forEach var="item" items="${formIntCompAll}">
                                        <c:choose>
                                            <c:when test="${item.visible}">
                                                <li class="uk-margin-left" id="${item.id}" onclick="location.href = '/sicstv/user/country.htm?intCompId=${item.id}'">
                                                    <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height:80px;" href="/sicstv/user/country.htm?intCompId=${item.id}">
                                                        <div class="competitionBox">
                                                            <img class="logoInternationalCountryImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'" alt="${item.name}">
                                                        </div>
                                                        <p style="margin-bottom: 0; margin-top: 3px; text-transform: uppercase">${item.name}</p>
                                                    </a>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li class="uk-margin-left" id="${item.id}">
                                                    <span class="uk-text-center" style="margin-top: 5px; min-height: 110px;">
                                                        <div class="competitionBoxDisabled">
                                                            <img class="logoInternationalCountryImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'" alt="${item.name}">
                                                        </div>
                                                        <p style="margin-bottom: 0; margin-top: 3px; text-transform: uppercase; color: #bfbfbf">${item.name}</p>
                                                    </span>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:forEach>
                                </ul>
                            </div>
                        </li>

                        <li id="playerAgentsLi">
                            <div id="playerAgents">
                                <div class="uk-width-1-1 uk-height-1-1 uk-icon-circle-o-notch uk-icon-spin uk-icon-small orangeiconcolor">

                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </body>

</html>
