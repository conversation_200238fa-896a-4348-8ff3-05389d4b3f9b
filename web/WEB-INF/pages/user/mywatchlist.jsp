<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">
        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/flat/flat.css?<%=System.currentTimeMillis()%>" rel="stylesheet">

        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>

        <!-- Keep here: needed for teamStats.jsp -->
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/3.2.1/css/fixedColumns.dataTables.min.css"/>
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdn.datatables.net/fixedcolumns/4.3.0/js/dataTables.fixedColumns.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.colVis.min.js"></script>
        <!-- Keep here: needed for teamStats.jsp for buttons (export) -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
        <script src="https://cdn.datatables.net/colreorder/1.7.0/js/dataTables.colReorder.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <script type="text/javascript">
            var currentWatchlistId;
            <c:if test="${mWatchlist.isEmpty()}">
            window.location.href = "/sicstv/user/myhome.htm";
            </c:if>

            $(document).ready(function () {
                initSearchFilter();
                
                var watchlistId = new URL(document.URL).searchParams.get("watchlistId");
                if (typeof watchlistId !== 'undefined' && watchlistId !== null) {
                    if ($("#watchlistSelect option[value='" + parseInt(watchlistId) + "']").length > 0) { // se esiste ancora la watchlist
                        $("#watchlistSelect").val(parseInt(watchlistId));
                    } else {
                        $("#watchlistSelect").val($("#watchlistSelect option:first").val()); // altrimenti metto il primo della lista
                    }
                }

                var isGrouped = new URL(document.URL).searchParams.get("isGrouped");
                if (typeof isGrouped !== 'undefined' && isGrouped !== null && isGrouped === 'true') {
                    $("#watchlistGrouped").attr("checked", "checked");
                } else if (typeof isGrouped === "undefined" || isGrouped === null) {
                    $("#watchlistGrouped").attr("checked", "checked");
                    isGrouped = true;
                }

                if (${mWatchlist.size()} > 0) {
                    jsLoadTeamStatsFromWatchlist("watchlistContent", undefined, $("#watchlistSelect").val(), true, isGrouped);
                }

                currentWatchlistId = parseInt($("#watchlistSelect").val());
                
                $.UIkit.autocomplete($('#autocompleteTeamPlayerWatchlist'), {
                    'minLength': 3,
                    'delay': 750,
                    'hoverClass': 'uk-hover',
                    'source': function (release) {
                        $("#searchSpinnerWatchlist").removeClass("uk-hidden");
                        setTimeout(function() {
                            if (!lock) {
                                var array = [];
                                lock = true;
                                isTeam = false;
                                isPlayer = false;
                                isCompetition = false;
                                isCountry = false;
                                $.ajax({
                                    type: "GET",
                                    url: "/sicstv/user/searchPlayer.htm",
                                    dataType: "json",
                                    data: "pattern=" + $("#teamPlayerAutoWatchlist").val(),
                                    success: function (data) {
                                        var strf = $("#teamPlayerAutoWatchlist").val();
                                        var re = new RegExp(strf, "ig");
                                        $.each(data, function (arrKey, arrValue) {
                                            isPlayer = true;
                                            var str = arrValue.last_name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                            var knownName = "";
                                            if (arrValue.known_name.indexOf(arrValue.last_name) === -1) {
                                                knownName = " (" + arrValue.known_name.replace(re, "<b>" + strf + "</b>").toUpperCase() + ")";
                                            }
                                            arrValue.last_name = arrValue.last_name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                            arrValue.first_name = arrValue.first_name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                            var bornDate = arrValue.bornDate.split("-")[0];
                                            var obj = {id: arrValue.id, value: arrValue.known_name, seasons: arrValue.seasonList, lastSeason: arrValue.seasonName,  name: str, namel: arrValue.last_name, namef: arrValue.first_name, namek: knownName, borny: bornDate, team: false, player: true, competition: false, country: false};
                                            array.push(obj);
                                        });
                                        lock = false;
                                        release(array);
                                        $("#searchSpinnerWatchlist").addClass("uk-hidden");
                                    }
                                });
                            }
                        }, 100);
                    }
                });
            });

            function changeWatchlist() {
                var isGrouped = $("#watchlistGrouped").attr("checked");
                if (typeof isGrouped === "undefined") {
                    isGrouped = false;
                } else {
                    isGrouped = true;
                }

                var watchlistId = new URL(document.URL).searchParams.get("watchlistId");
                var isGroupedFromLink = new URL(document.URL).searchParams.get("isGrouped");
                var newLink = document.URL;

                if (typeof watchlistId === 'undefined' || watchlistId === null) {
                    newLink = newLink + "?watchlistId=" + $("#watchlistSelect").val();
                } else {
                    newLink = newLink.replace("?watchlistId=" + watchlistId, "?watchlistId=" + $("#watchlistSelect").val());
                }

                if (typeof isGroupedFromLink === 'undefined' || isGroupedFromLink === null) {
                    newLink = newLink + "&isGrouped=" + isGrouped;
                } else {
                    newLink = newLink.replace("&isGrouped=" + isGroupedFromLink, "&isGrouped=" + isGrouped);
                }

                window.location.replace(newLink);
            }

            function openManageList() {
                $("#watchlistListModal").addClass("uk-open");
                $("#watchlistListModal").removeClass("uk-hidden");
            }

            function closeManageList() {
                $("#watchlistListModal").removeClass("uk-open");
                $("#watchlistListModal").addClass("uk-hidden");
            }

            function manageRenameList(id, confirm) {
                if ($("#teamStatsConfirmRename" + id).length > 0) { // salvo modifica
                    $("#teamStatsConfirmRename" + id).remove();

                    if (confirm) {
                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/rinominaListaWatchlist.htm",
                            cache: false,
                            data: encodeURI("&watchlistId=" + id + "&name=" + $("#teamStatsFilterNameInput" + id).val()),
                            success: function (msg) {
                                if (msg === 'true') {
                                    UIkit.notify("<spring:message code='watchlist.update.success'/>", {status: 'success', timeout: 1000});
                                    window.location.reload();
                                } else {
                                    UIkit.notify("<spring:message code='watchlist.update.failed'/>", {status: 'warning', timeout: 1000});
                                }
                            },
                            error: function () {
                                UIkit.notify("<spring:message code='watchlist.update.failed'/>", {status: 'warning', timeout: 1000});
                            }
                        });
                    }
                    $("#teamStatsFilterName" + id).html($("#teamStatsFilterNameInput" + id).attr("defaultValue"));
                    $("#teamStatsRenameButton" + id).attr("title", "<spring:message code='playlist.tooltip.rename'/>");
                    $("#teamStatsRenameIcon" + id).attr("class", "uk-icon-pencil");
                } else {
                    var newButton = $('<button id="teamStatsConfirmRename' + id + '" class="uk-icon-hover uk-button uk-button-mini" onclick="manageRenameList(' + id + ', true)" title="Rinomina"><i class="uk-icon-check"></i></button>');
                    $("#teamStatsFilterButtons" + id).prepend(newButton);

                    var newInput = $('<input id="teamStatsFilterNameInput' + id + '" class="uk-input" type="text" aria-label="Input" maxlength="128" value="' + ($("#teamStatsFilterName" + id).html().replace(" (<spring:message code='menu.condivisa'/>)", "").trim()) + '" defaultValue="' + ($("#teamStatsFilterName" + id).html().trim()) + '">');
                    $("#teamStatsFilterName" + id).html(newInput);

                    $("#teamStatsRenameButton" + id).attr("title", "<spring:message code='playlist.tooltip.rename.undo'/>");
                    $("#teamStatsRenameIcon" + id).attr("class", "uk-icon-close");
                }
            }

            function deleteList(id) {
                if (window.confirm("<spring:message code="watchlist.delete.question"/>")) {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/cancellaWatchlist.htm",
                        cache: false,
                        data: encodeURI("&watchlistId=" + id),
                        success: function (msg) {
                            if (msg === 'true') {
                                UIkit.notify("<spring:message code="watchlist.delete.success"/>", {status: 'success', timeout: 1000});
                                window.location.reload();
                            } else {
                                UIkit.notify("<spring:message code="watchlist.delete.failed"/>", {status: 'danger', timeout: 1000});
                            }
                        },
                        error: function () {
                            UIkit.notify("<spring:message code="watchlist.delete.failed"/>", {status: 'danger', timeout: 1000});
                        }
                    });
                }
            }

            function openShareWatchlist(id) {
                closeManageList(); // dato che arrivo da un modale, prima chiudo quello e poi apro questo
                currentWatchlistId = id;

                var userIdToShareWith = [];
                $("#menuDelete").addClass("uk-hidden");
                $("#modalShareWatchlist").addClass("uk-open");
                $("#modalShareWatchlist").removeClass("uk-hidden");

                var table = document.getElementById("addUserShareTable");
                while (table.rows.length > 1) {
                    table.deleteRow(1);
                }
                table = document.getElementById("addUserAlreadySharedTable");
                while (table.rows.length > 1) {
                    table.deleteRow(1);
                }

            <c:forEach var="user" items="${mUserToShareList}">
                userIdToShareWith.push("${user.id}");
            </c:forEach>
                // qua tolgo quelli a cui ho già condiviso
            <c:forEach var="item" items="${mWatchlistSharedWithUser}">
                if (parseInt(${item.key}) == id) { // ho trovato la watchlist
                <c:forEach var="watchlist" items="${item.value}">
                    if (userIdToShareWith.includes("${watchlist.user.id}")) {
                        userIdToShareWith.splice(userIdToShareWith.indexOf("${watchlist.user.id}"), 1);
                    }
                </c:forEach>
                }
            </c:forEach>
                // ora aggiungo nella prima tabella le persone a cui POSSO condividere
            <c:forEach var="user" items="${mUserToShareList}">
                if (userIdToShareWith.includes("${user.id}")) {
                    addUserToShareTable('${user.id}', '${user.lastName} ${user.firstName}', '${user.email}', false);
                }
            </c:forEach>
                // ora aggiungo nella seconda tabella le persone a cui HO GIA condiviso
            <c:forEach var="item" items="${mWatchlistSharedWithUser}">
                if (parseInt(${item.key}) == id) { // ho trovato la watchlist
                <c:forEach var="watchlist" items="${item.value}">
                    addUserToAlreadySharedTable('${watchlist.user.id}', '${watchlist.user.lastName} ${watchlist.user.firstName}', '${watchlist.user.email}', ${watchlist.editable});
                </c:forEach>
                }
            </c:forEach>
            }

            function closeShareWatchlist() {
                $("#modalShareWatchlist").removeClass("uk-open");
                $("#modalShareWatchlist").addClass("uk-hidden");

                openManageList();
            }

            function addUserToShareTable(id, name, email, editable) {
                var table = document.getElementById("addUserShareTable");
                var row = table.insertRow(table.rows.length);
                row.className = "uk-table-middle uk-margin-large-bottom uk-margin-large-top " + id;
                var cell1 = row.insertCell(0);
                var cell2 = row.insertCell(1);
                var cell3 = row.insertCell(2);
                cell1.className = "uk-text-center matchesColumnResult1 clickRow uk-table-middle uk-margin-large-bottom uk-margin-large-top";
                cell1.innerHTML = '<button onclick="deleteTableRow(\'addUserShareTable\', ' + id + '); addUserToAlreadySharedTable(' + id + ', \'' + name + '\', \'' + email + '\', ' + editable + ')" class="uk-icon-hover uk-button uk-button-mini" title="<spring:message code="playlist.share.remove"/>"><i class="uk-icon-hover uk-icon-plus"></i></button>';
                cell2.innerHTML = name;
                cell3.innerHTML = email;
            }

            function addUserToAlreadySharedTable(id, name, email, editable) {
                var table = document.getElementById("addUserAlreadySharedTable");
                var row = table.insertRow(table.rows.length);
                row.className = "uk-table-middle uk-margin-large-bottom uk-margin-large-top " + id;
                row.setAttribute('userid', id);
                var cell1 = row.insertCell(0);
                var cell2 = row.insertCell(1);
                var cell3 = row.insertCell(2);
                var cell4 = row.insertCell(3);
                cell1.className = "uk-text-center matchesColumnResult1 clickRow uk-table-middle uk-margin-large-bottom uk-margin-large-top";
                cell1.innerHTML = '<button onclick="deleteTableRow(\'addUserAlreadySharedTable\', ' + id + '); addUserToShareTable(' + id + ', \'' + name + '\', \'' + email + '\', ' + editable + ')" class="uk-icon-hover uk-button uk-button-mini" title="<spring:message code="playlist.share.remove"/>"><i class="uk-icon-hover uk-icon-minus"></i></button>';
                cell2.innerHTML = name;
                cell3.innerHTML = email;
                cell4.innerHTML = '<span class="uk-form"><select class="canEdit' + id + '"><option value="view" ' + (editable ? 'selected="selected"' : '') + '><i class="uk-icon-eye uk-margin-right"></i><spring:message code="tooltip.visualizza"/></option><option value="edit" ' + (editable ? 'selected="selected"' : '') + '><i class="uk-icon-pencil uk-margin-right"></i><spring:message code="menu.modify"/></option></select></span>';
            }

            function deleteTableRow(tableId, userId) {
                var table = document.getElementById(tableId);
                $('.' + userId).remove();
            }

            function shareWatchlist() {
                var userIdList = [];
                $('#addUserAlreadySharedTable tr').each(function () {
                    var userId = $(this).attr("userid");
                    if (typeof userId !== 'undefined') { // l'header non ha l'attributo
                        userIdList.push(userId + ";" + ($('.canEdit' + userId).val() === 'edit'));
                    }
                });

                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/shareWatchlist.htm",
                    cache: false,
                    data: encodeURI("&id=" + currentWatchlistId + "&userIds=" + userIdList),
                    success: function (msg) {
                        if (msg === "true") {
                            UIkit.notify("<spring:message code="watchlist.share.success"/>", {status: 'success', timeout: 1000});
                            window.location.reload();
                        } else {
                            UIkit.notify("<spring:message code="watchlist.share.error"/>", {status: 'danger', timeout: 1000});
                        }
                    },
                    error: function () {
                        UIkit.notify("<spring:message code="watchlist.share.error"/>", {status: 'danger', timeout: 1000});
                    }
                });
            }
            
            // (!) LA STESSA FUNZIONE C'E' ANCHE NELLA PAGINA player.jsp (!)
            function addToWatchlist(playerId) {
                var watchlistId = $("#watchlistSelect").val();
                if (watchlistId === "" && typeof watchlistId === 'undefined') {
                    UIkit.notify("<spring:message code='watchlist.update.failed'/>", {status: 'warning', timeout: 1000});
                    return;
                }

                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/creaWatchlist.htm",
                    data: encodeURI("name=&playerId=" + playerId + "&watchlistId=" + (typeof watchlistId !== 'undefined' ? watchlistId : '')),
                    cache: false,
                    success: function (result) {
                        if (result === 'ok') {
                            UIkit.notify("<spring:message code='watchlist.update.success'/>", {status: 'success', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code='watchlist.update.failed'/>", {status: 'warning', timeout: 1000});
                        }
                    }
                });
            }
            
            // (!) LA STESSA FUNZIONE C'E' ANCHE NELLA PAGINA player.jsp (!)
            function removeFromWatchlist(element, playerId) {
                var watchlistId = $("#watchlistSelect").val();
                if (watchlistId === "" && typeof watchlistId === 'undefined') {
                    UIkit.notify("<spring:message code='watchlist.update.failed'/>", {status: 'warning', timeout: 1000});
                    return;
                }
                
                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/rimuoviPlayerDaWatchlist.htm",
                    data: encodeURI("playerId=" + playerId + "&watchlistId=" + (typeof watchlistId !== 'undefined' ? watchlistId : '')),
                    cache: false,
                    success: function (result) {
                        if (result === 'ok') {
                            UIkit.notify("<spring:message code='watchlist.update.success'/>", {status: 'success', timeout: 1000});
                            $(element).remove();
                        } else {
                            UIkit.notify("<spring:message code='watchlist.update.failed'/>", {status: 'warning', timeout: 1000});
                        }
                    }
                });
            }
            
            function openWatchlistPlayerManager() {
                $("#watchlistPlayerModalContainer").html("");   // svuoto prima
                var alreadyAdded = [];
                $("#teamStatsTablewatchlistPlayer td").each(function () {
                    var element = $(this).find("b");
                    var playerName = element.text();
                    var playerId = element.attr("playerId");
                    if (typeof playerId !== 'undefined') {
                        if (!alreadyAdded.includes(playerId)) {
                            var playerButton = '<button onclick="removeFromWatchlist(this, ' + playerId + ');" class="uk-button uk-dropdown-close playlistTvButton" style="margin-right: 5px">' + playerName + '</button>';
                            $("#watchlistPlayerModalContainer").append(playerButton);
                            alreadyAdded.push(playerId);
                        }
                    }
                });
                
                $("#watchlistPlayerModal").addClass("uk-open");
                $("#watchlistPlayerModal").removeClass("uk-hidden");
            }
            
            function closeWatchlistPlayerManager() {
                $("#watchlistPlayerModal").removeClass("uk-open");
                $("#watchlistPlayerModal").addClass("uk-hidden");
                
                window.location.reload();
            }
        </script>
    </head>
    <body>
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>

        <div id="breadcrumb">
            <ul class="uk-breadcrumb uk-margin-left">
                <li style="text-transform: uppercase;"><a href="/sicstv/user/myhome.htm"><spring:message code="menu.user.mysicstv"/></a></li>
                <li style="text-transform: uppercase;"><spring:message code="menu.user.watchlist"/></li>
            </ul>
        </div>

        <div style="display: flex; margin-top: 10px; padding-left: 1%">
            <span class="uk-form" style="margin-right: 10px">
                <spring:message code='menu.user.watchlist'/>
                <select id="watchlistSelect" onchange="changeWatchlist()">
                    <c:forEach var="item" items="${mWatchlist}">
                        <option value="${item.id}">${item.name}<c:if test="${item.editable != null}"> (<spring:message code='menu.condivisa'/>)</c:if></option>
                    </c:forEach>
                </select>
            </span>

            <div id="manageListContent">
                <button class="uk-button" onclick="openManageList()"><spring:message code='watchlist.gestisci'/></button>
            </div>

            <div class="center uk-margin-left-small">
                <span class="uk-margin-right-small"><spring:message code='watchlist.group'/></span>
                <label class="uk-switch" for="watchlistGrouped">
                    <input type="checkbox" id="watchlistGrouped" onchange="changeWatchlist();">
                    <div class="uk-switch-slider"></div>
                </label>
            </div>
                        
            <div class="center uk-margin-left-small">
                <button class="uk-button" onclick="openWatchlistPlayerManager()"><spring:message code='watchlist.manage.players'/></button>
            </div>
        </div>

        <div id="watchlistContent">
        </div>

        <div id="watchlistListModal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeManageList();">&times;</span>
                    <h3><spring:message code="watchlist.gestisci"/></h3>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                            <tbody>
                                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                    <td class="playlistTvTitle" style="width: 30vh">
                                        <spring:message code='team.stats.filter.name.label'/>
                                    </td>
                                    <td class="playlistTvTitle">
                                        <spring:message code='team.stats.colvis.title'/>
                                    </td>
                                </tr>
                                <c:forEach var="list" items="${mWatchlist}">
                                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                        <td id="teamStatsFilterName${list.id}">
                                            ${list.name}<c:if test="${list.editable != null}"> (<spring:message code='menu.condivisa'/>)</c:if>
                                            </td>
                                            <td id="teamStatsFilterButtons${list.id}">
                                            <button class="uk-icon-hover uk-button uk-button-mini" onclick="manageRenameList(${list.id}, false)" title="<spring:message code='playlist.tooltip.rename'/>" id="teamStatsRenameButton${list.id}" <c:if test="${list.editable != null && !list.editable}"> disabled title="<spring:message code="watchlist.share.viewonly"/>"</c:if>><i id="teamStatsRenameIcon${list.id}" class="uk-icon-pencil"></i></button>
                                                <c:if test="${list.editable == null}">
                                                <button class="uk-icon-hover uk-button uk-button-mini" onclick="deleteList(${list.id})" title="<spring:message code='menu.user.delete'/>"><i class="uk-icon-trash"></i></button>
                                                <button class="uk-icon-hover uk-button uk-button-mini" onclick="openShareWatchlist(${list.id})" title="<spring:message code='menu.share'/>"><i class="uk-icon-share"></i></button>
                                                </c:if>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                    <div class="uk-modal-footer uk-text-right uk-width-1-1">
                        <button style="min-width:80px;" onclick="closeManageList();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                    </div>
                </div>
            </div>
        </div>

        <div id="modalShareWatchlist" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;" id="dropdownShareWatchlist">
                <div class="playlistTv-container">
                    <span class="modalClose" onclick="closeShareWatchlist();">&times;</span>
                    <h2><spring:message code="playlist.share.people.share"/></h2>
                    <div>
                        <table class="playlistTv-table uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;" id="addUserShareTable">
                            <tbody>
                                <tr>
                                    <td></td>
                                    <td class="playlistTvTitle"><spring:message code="playlist.nome"/></td>
                                    <td class="playlistTvTitle"><spring:message code="playlist.email"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <h2><spring:message code="playlist.share.already.sharing"/></h2>
                    <div>
                        <table class="playlistTv-table uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;" id="addUserAlreadySharedTable">
                            <tbody>
                                <tr>
                                    <td></td>
                                    <td class="playlistTvTitle"><spring:message code="playlist.nome"/></td>
                                    <td class="playlistTvTitle"><spring:message code="playlist.email"/></td>
                                    <td class="playlistTvTitle"><spring:message code="playlist.edit"/></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <button id="cancelShareWatchlist" style="min-width:80px;" onclick="closeShareWatchlist();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                    <button id="confirmShareWatchlist" style="min-width:80px;" onclick="shareWatchlist();" class="uk-text-large uk-button uk-modal-close"><spring:message code="playlist.save"/></button>
                </div>
            </div>
        </div>
                
        <div id="watchlistPlayerModal" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeWatchlistPlayerManager();">&times;</span>
                </div>
                <div class="container" style="min-height: 25vh; max-height: 90vh;">
                    <ul class="uk-nav uk-nav-dropdown">
                        <li>
                            <div class="playlistTv">
                                <span style="font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="watchlist.fast.add.player"/></span>
                            </div>
                            <div id="autocompleteTeamPlayerWatchlist" class="uk-autocomplete uk-form uk-margin-top">
                                <div class="uk-flex">
                                    <input type="text" id="teamPlayerAutoWatchlist" class="uk-form-width-medium" placeholder="<spring:message code='video.giocatore'/>" style="text-transform: lowercase"/>
                                    <div id="searchSpinnerWatchlist" class="spinner-small uk-hidden center-vertically" style="margin-left: 5px"><div class="loader-small"></div></div>
                                </div>
                                <script type="text/autocomplete">
                                    <ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left">
                                    {{#isPlayer}}
                                    <li><i class="uk-icon-user"></i>&nbsp;<b><spring:message code='search.players'/></b><hr></li>
                                    {{~items}}
                                    {{#$item.player}}
                                    <li data-value="{{ $item.value }}" data-seasons="{{ $item.seasons }}" onclick="addToWatchlist({{ $item.id }});" >
                                    <a>{{{ $item.name }}} <span class="sCapitalize">{{{ $item.namef }}} </span> {{{ $item.namek }}} ({{{ $item.borny }}})</a>
                                    </li>
                                    {{ /$item.player }}
                                    {{/items}}
                                    <li>&nbsp;</li>
                                    {{/isPlayer }}
                                    </ul>
                                </script>
                            </div>
                            <hr>
                            <div class="playlistTv uk-margin-top">
                                <span style="font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="watchlist.remove.player"/></span>
                            </div>
                            <div id="watchlistPlayerModalContainer" class="uk-margin-top">
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <button style="min-width:80px;" onclick="closeWatchlistPlayerManager();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.conferma"/></button>
                </div>
            </div>
        </div>
    </body>
</html>
