<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

        <script typt="text/javascript" src="/sicstv/uikit/js/components/notify.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script type="text/javascript" src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" ></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css">
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        <script src="/sicstv/js/jquery.timer.js" type="text/javascript" ></script>
        <script type="text/javascript">
            var isMyHome = true;
            var timerProgressExport;
            $(document).ready(function () {
                timerProgressExport = $.timer(function () {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/checkEndProcessing.htm",
                        data: encodeURI("identifier=" + sessionStorage['exportIdentifier']),
                        cache: false,
                        success: function (msg) {
                            $("#tablePlaylist tbody:nth-child(1)").before(msg);
                            if (!$(".tableInProgress").length) {
                                timerProgressExport.stop();
                                sessionStorage['exportIdentifier'] = "";
                            }
                        }
                    });
                });
                timerProgressExport.set({time: 2000, autostart: true});

                if ('${mPlaylistSelected}' !== '') {
                    openPlaylist(${mPlaylistSelected});
                } else if ('${mGameSelected}' !== '') {
                    openGame(${mGameSelected});
                }
            });

            function openGame(id) {
                var strUrl = "/sicstv/user/game.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        $("#videoDiv").html(msg);
                        $("#breadcrumb ul li:nth-child(3)").remove();
                        $("#breadcrumb ul").append("<li>" + $("#gameName").val() + "</li>");
                    },
                    async: false
                });
                updateUnseenPlaylist();
            }

            function openPlaylist(id) {
                $("#newPlaylist" + id).html("");
                var strUrl = "/sicstv/user/playlist.htm?id=" + id + "&mobile=false";
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        $("#videoDiv").html(msg);
                        $("#breadcrumb ul li:nth-child(3)").remove();
                        $("#breadcrumb ul").append("<li>" + $("#playlistName").val() + "</li>");
                    },
                    async: false
                });
                updateUnseenPlaylist();
            }

            function jsDownloadPlaylist(id) {
                var strUrl = "/sicstv/user/downloadPlaylist.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "true") {
                            UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                            window.location.replace(msg.substr(5));
                            $.unblockUI();
                        }
                    }
                });
                sessionStorage.removeItem('exportIdentifier');
            }

            function jsDownloadGame(id) {
                var strUrl = "/sicstv/user/download.htm?id=" + id + "&type=0";
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "true") {
                            UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                            window.location.replace(msg.substr(5));
                        }
                    }
                });
            }

            function toggleTheatreMode() {
                // se è attiva la modalità cinema la tolgo
                if ($("#videoDiv").hasClass("uk-width-1-1")) {
                    $("#videoDiv").removeClass("uk-width-1-1");
                    $("#listFilesDiv").removeClass("uk-width-1-2");
                    $("#playlistInfo").removeClass("uk-width-1-2");
                    $("#videoDiv").addClass("uk-width-large-6-10");
                    $("#listFilesDiv").addClass("uk-width-large-4-10");
                    $("#playlistInfo").addClass("uk-width-large-6-10");
                } else {
                    // se non è attiva la attivo
                    $("#videoDiv").addClass("uk-width-1-1");
                    $("#listFilesDiv").addClass("uk-width-1-2");
                    $("#playlistInfo").addClass("uk-width-1-2");
                    $("#videoDiv").removeClass("uk-width-large-6-10");
                    $("#listFilesDiv").removeClass("uk-width-large-4-10");
                    $("#playlistInfo").removeClass("uk-width-large-6-10");
                }
            }
        </script>
    </head>
    <body>
        <%@ include file="header.jsp" %>
        <div id="breadcrumb" class="uk-text-truncate" style="text-transform: uppercase;">
            <ul class="uk-breadcrumb uk-margin-left">
                <li style="text-transform: uppercase;"><a href="/sicstv/user/myhome.htm"><spring:message code="menu.user.mysicstv"/></a><i id="showCurrentFilter"></i></li>
                <li style="text-transform: uppercase;"><a href="/sicstv/user/personalArchive.htm"><spring:message code="menu.user.mysicstv_archivio"/></a><i id="showCurrentFilter"></i></li>
                <li style="text-transform: uppercase;"><spring:message code="menu.user.mysicstv_preview"/><i id="showCurrentFilter"></i></li>
            </ul>
        </div>
        <div id="videoDiv" class="uk-width-large-3-4 uk-float-left uk-width-small-1-1">
            <video class='uk-width-1-1'>
            </video>
        </div>
        <div id="listFilesDiv" class="uk-width-large-1-4 uk-float-left uk-width-small-1-1">
            <!--div class="uk-margin-right uk-width-1-1 uk-nav">
                <span class="uk-container uk-margin-left">
                    <div class="uk-margin-left"><spring:message code="menu.user.mysicstv_archivio"/></div>
                </span>
            </div-->
            <div id="competitions" class="uk-width-1-1">
                <!--div id="menuDelete" class="uk-hidden absolute" style="vertical-align:middle;">
                    <button style="min-width:80px;" id="btnDeletePlGm" class="uk-button uk-button-danger uk-margin-all uk-float-right" onclick="askDelete();"><i class="uk-icon-medium uk-icon-trash uk-margin-small-right"></i><spring:message code="menu.user.delete"/></button>
                    <button style="min-width:80px;" id="btnSharePlGm" class="uk-button uk-margin-all uk-float-right" onclick="sharePlaylistAndGameSelected();"><i class="uk-icon-medium uk-icon-share uk-margin-small-right"></i><spring:message code="menu.share"/></button>
                    <button style="min-width:80px;" id="btnDownloadPlGm" class="uk-button uk-margin-all uk-float-right" onclick="downloadPlaylistAndGameSelected();"><i class="uk-icon-medium uk-icon-download uk-margin-small-right"></i><spring:message code="menu.user.download"/></button>
                    <div id="divDeletePlGm" class="uk-text-bold uk-margin-all-large uk-float-right"></div>
                </div-->
                <!--div id="searchbarpersonal2" class="uk-form uk-hidden-large" style="padding-top: 0px;">
                    <input type="text" id="myInput2" class=" uk-width-1-1" onkeyup="search2()" placeholder="<spring:message code="search.name"/>" title="<spring:message code="search.name"/>" style="padding: 12px 20px 12px 40px;background-position: 10px 10px;background-repeat: no-repeat;height:40px;">
                </div-->
                <c:forEach var="map" items="${mPersonalArchive}">
                    <c:if test="${map.value.isEmpty()==false}">
                        <div id="${map.key.replace(" ","")}">
                            <div class="uk-margin-right uk-width-1-1 uk-nav nav-date">
                                <span class="uk-container uk-margin-left">
                                    <div class="uk-margin-left">${map.key}</div>
                                </span>
                            </div>
                            <div class="uk-width-1-1">
                                <div class="uk-margin-small-left uk-margin-small-right">
                                    <c:forEach var="item" items="${map.value}">
                                        <c:choose>
                                            <c:when test="${item.getPlaylist()!=null}">
                                                <div id="${item.getPlaylist().getId()}playlistli" class="competitionBox uk-text-truncate uk-margin-small-bottom uk-width-1-1">
                                                    <a class="uk-width-1-1" href="/sicstv/user/mysicstv.htm?playlist=${item.getPlaylist().id}"  data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="${item.getPlaylist().name}</br>PLAYLIST</br>${item.getPlaylist().uploadLastName} ${item.getPlaylist().uploadFirstName}</br>${item.getPlaylist().getDateDayString()} ${item.getPlaylist().getDateMonthString()} ${item.getPlaylist().getDateYearString()}">
                                                        <div class="uk-overlay-default uk-position-relative">
                                                            <c:if test="${!item.getPlaylist().isZip()}">
                                                                <img class="uk-width-1-1 uk-margin-small-top" src="https://s3.eu-west-1.amazonaws.com/it.sics.svs.thumb/${item.getPlaylist().getThumbName()}" onerror="this.src='/sicstv/images/<spring:message code="mysicstv.nopreview"/>'" alt="${item.getPlaylist().name}">
                                                            </c:if>
                                                            <c:if test="${item.getPlaylist().isZip()}">
                                                                <img src="/sicstv/images/zip169.png" class="logoCompImgX uk-margin-top uk-margin-left uk-margin-right"/>
                                                            </c:if>
                                                            <div id="playPlaylist${item.getPlaylist().id}" class="uk-flex uk-flex-center uk-flex-middle uk-position-top uk-margin-small-top uk-width-1-1 uk-text-center uk-text-middle" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="Playlist" style="color:#f27e46;min-height:calc(100%*9/16);bottom:0px;">
                                                                <span class="squaredGearArchive uk-width-1-1 uk-text-center">
                                                                    <i class="uk-icon-large uk-icon-play uk-width-1-1" style="margin-top:20%;margin-left: 5%;"></i>
                                                                </span>
                                                            </div>
                                                            <c:choose>
                                                                <c:when test="${item.getPlaylist().isCanDelete()}">
                                                                    <!--div class="uk-position-top-left uk-margin-all-large uk-form" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.select"/>">
                                                                        <span class="squaredCheckboxArchive"><input id="checkbox${item.getPlaylist().getId()}" type="checkbox" class="competitionBoxX"/><label onclick="selectPlaylist(event, '${item.getPlaylist().getId()}',${item.getPlaylist().isZip()});" for="checkbox${item.getPlaylist().getId()}" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.select"/>"></label></span>
                                                                    </div-->
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <!--div class="uk-position-top-left uk-margin-all-large uk-form disabilitato" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.cantselect"/>">
                                                                        <span class="squaredCheckboxArchive disabilitato"><input id="checkbox${item.getPlaylist().getId()}" type="checkbox" class="competitionBoxDisabledX disabilitato" disabled/><label data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.cantselect"/>" class="disabilitato" for="checkbox${item.getPlaylist().getId()}"></label></span>
                                                                    </div-->
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </div>
                                                        <div class="uk-margin-left uk-margin-right uk-text-truncate">
                                                            <div class="uk-margin-small-top uk-margin-small-bottom uk-text-truncate">
                                                                <div style="text-transform: uppercase;" class="uk-text-bold uk-text-truncate">${item.getPlaylist().name}</div>
                                                                <span style="text-transform: uppercase;" class="playlistDetail uk-text-truncate"><b style="color:#f27e46;">${item.getPlaylist().getDateDayString()} ${item.getPlaylist().getDateMonthString()} </b>| PLAYLIST | ${item.getPlaylist().uploadLastName} ${item.getPlaylist().uploadFirstName}</span>
                                                            </div>
                                                        </div>
                                                    </a>
                                                </div>
                                            </c:when>
                                            <c:otherwise>
                                                <div id="${item.getGame().getId()}gameli" class="competitionBox uk-text-truncate uk-margin-small-bottom uk-width-1-1">
                                                    <a href="/sicstv/user/mysicstv.htm?game=${item.getGame().idFixture}"  data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="${item.getGame().homeTeam} - ${item.getGame().awayTeam}</br>${item.getGame().competitionName}<c:if test="${!item.getGame().ownerName.equals('')}"></br>${item.getGame().ownerName}</c:if></br>${item.getGame().getDateDayString()} ${item.getGame().getDateMonthString()} ${item.getGame().getDateYearString()}">
                                                            <div class="uk-overlay-default uk-position-cover uk-position-relative">
                                                                <img class="uk-width-1-1 uk-margin-small-top" src="https://s3.eu-west-1.amazonaws.com/it.sics.svs.thumb/${item.getGame().getThumbName()}" onerror="this.src='/sicstv/images/<spring:message code="mysicstv.nopreview"/>'" alt="${item.getGame().homeTeam} - ${item.getGame().awayTeam}">
                                                            <c:choose>
                                                                <c:when test="${item.getGame().isCanDelete()}">
                                                                    <!--div class="uk-position-top-left uk-margin-all-large uk-form" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.select"/>">
                                                                        <span class="squaredCheckboxArchive"><input id="checkbox${item.getGame().getId()}" type="checkbox" class="competitionBoxX"/><label onclick="selectGame(event, '${item.getGame().getId()}');" for="checkbox${item.getGame().getId()}" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.select"/>"></label></span>
                                                                    </div-->
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <!--div class="uk-position-top-left uk-margin-all-large uk-form disabilitato" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.cantselectfix"/>">
                                                                        <span class="squaredCheckboxArchive disabilitato"><input id="checkbox${item.getGame().getId()}" type="checkbox" class="competitionBoxDisabledX disabilitato" disabled/><label data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.cantselectfix"/>" class="disabilitato" for="checkbox${item.getGame().getId()}"></label></span>
                                                                    </div-->
                                                                </c:otherwise>
                                                            </c:choose>
                                                            <div id="playGame${item.getGame().idFixture}" class="uk-flex uk-flex-center uk-flex-middle uk-position-top uk-margin-small-top uk-width-1-1 uk-text-center uk-text-middle" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="video.partita"/>" style="color:#f27e46;min-height:calc(100%*9/16);bottom:0px;">
                                                                <span class="squaredGearArchive uk-width-1-1 uk-text-center">
                                                                    <i class="uk-icon-large uk-icon-play uk-width-1-1" style="margin-top:20%;margin-left: 5%;"></i>
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="uk-margin-left uk-margin-right uk-text-truncate">
                                                            <div style="text-transform: uppercase;" class="uk-text-bold uk-text-truncate">${item.getGame().homeTeam} - ${item.getGame().awayTeam}</div>
                                                            <span style="text-transform: uppercase;" class="playlistDetail uk-text-truncate"><b style="color:#f27e46;">${item.getGame().getDateDayString()} ${item.getGame().getDateMonthString()} </b>| ${item.getGame().competitionName} | <c:if test="${!item.getGame().ownerName.equals('')}">${item.getGame().ownerName}</c:if></span>
                                                            </div>
                                                        </a>
                                                    </div>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:forEach>
                                </div>
                            </div>
                        </div>
                    </c:if>
                </c:forEach>
            </div>
        </div>
        <div id="modalDeleteConfirm" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 90%;width:auto;height:auto;">
                <div>
                    <div id="modalDeleteConfirmText" class="uk-margin uk-modal-content uk-text-bold uk-text-large"></div>
                    <div class="uk-modal-footer uk-text-right">
                        <button style="min-width:80px;" onclick="closeDelete();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                        <button style="min-width:80px;" onclick="deletePlaylistAndGameSelected();" class="uk-text-large uk-text-bold uk-button uk-button-danger js-modal-confirm"><i class="uk-icon uk-icon-trash uk-margin-small-right"></i><spring:message code="menu.user.delete"/></button>
                    </div>
                </div>
            </div>
        </div>
        <div id="modalModifyPlaylist" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 90%;width:auto;height:auto;">
                <div>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <label class="uk-text-bold uk-text-large" for="namePlaylistModify"><spring:message code="video.descrizione" />:</label>
                    </div>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <input id="namePlaylistModify" class="uk-input uk-width-1-1 uk-form-large" type="text" aria-label="Input">
                    </div>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <label class="uk-text-bold uk-text-large" for="notePlaylistModify"><spring:message code="video.note" />:</label>
                    </div>
                    <div class="uk-margin uk-modal-content uk-width-1-1" disabled>
                        <textarea id="notePlaylistModify" class="uk-textarea uk-width-1-1 uk-form-large" name="text" style="resize: none;min-height: 80px;" disabled></textarea>
                    </div>
                    <div class="uk-modal-footer uk-text-right uk-width-1-1">
                        <button id="cancelModifyPlaylist"  style="min-width:80px;" onclick="closeModifyPlaylist();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                        <span id="saveModifyPlaylist"></span>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
