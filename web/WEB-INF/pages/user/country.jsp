<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/flat/flat.css?<%=System.currentTimeMillis()%>" rel="stylesheet">

        <script type="text/javascript">
            var pageStatus = [];
            var totMatches = [];
            var numMatches = [];
            var matchPerPage = 25;

            $(document).ready(function () {
                jsRefreshMatches(1);
                initSearchFilter();
                if ($(window).width() <= 768) {
                    $("#liCompetions").addClass("uk-active");
                    $("#tabMatches").removeClass("uk-active");
                } else {
                    $("#tabMatches").addClass("uk-active");
                    $("#matches").addClass("uk-active");
                    $("#liCompetions").removeClass("uk-active");
                }
            });

            // logout ajax
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);

            function jsRefreshMatches(page) {

                var idCou = $("#idCountry").val();
                refreshMatches("matchesList", "matches", page, "", "", idCou, "");
            }
            /*
             function jsDownload(id) {
             
             var strUrl = "/sicstv/user/download.htm?id=" + id;
             $.ajax({
             type: "GET",
             url: strUrl,
             cache: false,
             success: function (msg) {
             if (msg.substr(0, 4) === "true") {
             UIkit.notify(msg.substr(5), {status: 'danger'});
             } else {
             UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success'});
             window.location.replace(msg.substr(5));
             }
             }
             });
             }
             */

            function filterData() {
                readFilterMulti(false);
                filterMatches('matches', 1);
            }

        </script>
    </head>
    <body>

        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>

        <div>
            <div class="uk-grid">
                <input type="hidden" value="${mCountry.id}" id="idCountry"/>
                <div id="countries" 
                     <c:if test="${not empty mTeams}">
                         class="uk-width-6-10"
                     </c:if>
                     <c:if test="${empty mTeams}">
                         class="uk-width-1-1"
                     </c:if>
                     >
                    <div id="breadcrumb">
                        <ul class="uk-breadcrumb uk-margin-left">
                            <c:if test="${!mCountry.internationalCompetition}">
                                <li>
                                    <a href="/sicstv/user/home.htm?country=${!mCountry.internationalCompetition}"><spring:message code="menu.user.countries"/></a>
                                </li>
                            </c:if>
                            <c:if test="${mCountry.internationalCompetition}">
                                <li>
                                    <a href="/sicstv/user/home.htm?country=${!mCountry.internationalCompetition}"><spring:message code="menu.user.internationalCompetitions"/></a>
                                </li>
                            </c:if>
                            <li>${mCountry.name}</li>
                                <c:if test="${formCompetitionAll.isEmpty() == true}">
                                <li><spring:message code="menu.user.internationalCompetitions"/></li>
                                </c:if>
                                <c:if test="${formCompetitionAll.isEmpty() == false && !mCountry.internationalCompetition}">
                                <li><spring:message code="menu.user.nationalCompetitions"/></li>
                                </c:if>
                        </ul>
                    </div>

                    <c:if test="${formCompetitionAll.isEmpty() == false}">
                        <div class="uk-hidden-small">
                            <div class="uk-flex">
                                <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-10 uk-width-1-1 uk-margin-small-top" >
                                    <c:forEach var="item" items="${formCompetitionAll}">
                                        <c:choose>
                                            <c:when test="${item.visible}">
                                                <li class="uk-margin-left" id="${item.id}" onclick="location.href = '/sicstv/user/competition.htm?personal=false&formCompetitionId=${item.id}'" style="min-height: 175px;">
                                                    <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height:100px;" href="/sicstv/user/competition.htm?personal=false&formCompetitionId=${item.id}">
                                                        <div class="competitionBox">
                                                            <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                            <c:if test="${!mLastSeasonPair.contains(item.seasonName)}">
                                                                <p style="margin-bottom: 0; margin-top: 3px;">${item.seasonName}</p>
                                                            </c:if>
                                                        </div>
                                                        <p style="margin-bottom: 0; margin-top: 3px;">${item.name}</p>
                                                    </a>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li class="uk-margin-left" id="${item.id}">
                                                    <span class="uk-text-center" style="margin-top: 5px; min-height: 175px;">
                                                        <div class="competitionBoxDisabled">
                                                            <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                            <c:if test="${!mLastSeasonPair.contains(item.seasonName)}">
                                                                <p style="margin-bottom: 0; margin-top: 3px;">${item.seasonName}</p>
                                                            </c:if>
                                                        </div>
                                                        <p style="margin-bottom: 0; margin-top: 3px; text-transform: uppercase; color: #bfbfbf">${item.name}</p>
                                                    </span>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:forEach>
                                </ul>
                            </div>
                        </div>
                    </c:if>

                    <c:if test="${formCompetitionAll.isEmpty() == false && !mCountry.internationalCompetition}">
                        <div class="breadcrumb uk-hidden-small">
                            <ul class="uk-breadcrumb uk-margin-left">
                                <li>${mCountry.name}</li>
                                <li><spring:message code="menu.user.internationalCompetitions"/></li>
                            </ul>
                        </div>
                    </c:if>

                    <div class="uk-hidden-small">
                        <div class="uk-flex">
                            <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-10 uk-width-1-1 uk-margin-small-top" >
                                <c:forEach var="item" items="${formInternationalCompetitionAll}">
                                    <c:choose>
                                        <c:when test="${item.visible}">
                                            <li class="uk-margin-left" id="${item.id}" onclick="location.href = '/sicstv/user/competition.htm?personal=false&formCompetitionId=${item.id}&countryId=${mCountry.id}'" style="min-height: 175px;">
                                                <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height:100px;" href="/sicstv/user/competition.htm?personal=false&formCompetitionId=${item.id}&countryId=${mCountry.id}">
                                                    <div class="competitionBox">
                                                        <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                        <c:if test="${!mLastSeasonPair.contains(item.seasonName)}">
                                                            <p style="margin-bottom: 0; margin-top: 3px;">${item.seasonName}</p>
                                                        </c:if>
                                                    </div>
                                                    <p style="margin-bottom: 0; margin-top: 3px;">${item.name}</p>
                                                </a>
                                            </li>
                                        </c:when>
                                        <c:otherwise>
                                            <li class="uk-margin-left" id="${item.id}">
                                                <span class="uk-text-center" style="margin-top: 5px; min-height: 175px;">
                                                    <div class="competitionBoxDisabled">
                                                        <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                        <c:if test="${!mLastSeasonPair.contains(item.seasonName)}">
                                                            <p style="margin-bottom: 0; margin-top: 3px;">${item.seasonName}</p>
                                                        </c:if>
                                                    </div>
                                                    <p style="margin-bottom: 0; margin-top: 3px; text-transform: uppercase; color: #bfbfbf">${item.name}</p>
                                                </span>
                                            </li>
                                        </c:otherwise>
                                    </c:choose>
                                </c:forEach>
                            </ul>
                        </div>
                    </div>

                    <div class="uk-visible-small">
                        <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#mobileViewContent'}" id="mobileViewTab">
                            <li><a><spring:message code="menu.user.nationalCompetitions"/></a></li>
                            <li><a><spring:message code="menu.user.internationalCompetitions"/></a></li>
                            <li><a><spring:message code="user.nationalTeams"/></a></li>
                        </ul>

                        <ul id="mobileViewContent" class="uk-switcher">
                            <li id="campionati">
                                <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-7 uk-width-1-1 uk-margin-small-top" >
                                    <c:forEach var="item" items="${formCompetitionAll}">
                                        <c:choose>
                                            <c:when test="${item.visible}">
                                                <li class="competitionBox uk-margin-left uk-margin-top" id="${item.id}">
                                                    <a class="uk-text-center uk-margin-top" href="/sicstv/user/competition.htm?personal=false&formCompetitionId=${item.id}">
                                                        <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                        <p  data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                                    </a>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li class="competitionBoxDisabled uk-margin-left uk-margin-top" id="${item.id}">
                                                    <span class="uk-text-center uk-margin-top ">
                                                        <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                        <p data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                                    </span>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:forEach>
                                </ul>
                            </li>
                            <li id="campionatiInternazionali">
                                <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-7 uk-width-1-1 uk-margin-small-top" >
                                    <c:forEach var="item" items="${formInternationalCompetitionAll}">
                                        <c:choose>
                                            <c:when test="${item.visible}">
                                                <li class="competitionBox uk-margin-left uk-margin-top" id="${item.id}">
                                                    <a class="uk-text-center uk-margin-top" href="/sicstv/user/competition.htm?personal=false&formCompetitionId=${item.id}">
                                                        <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                        <p  data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                                    </a>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li class="competitionBoxDisabled uk-margin-left uk-margin-top" id="${item.id}">
                                                    <span class="uk-text-center uk-margin-top ">
                                                        <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                        <p  data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                                    </span>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:forEach>
                                </ul>
                            </li>
                            <li id="squadre">
                                <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-width-1-1 uk-margin-small-top" >
                                    <c:forEach var="item" items="${mTeams}">
                                        <c:choose>
                                            <c:when test="${item.visible}">
                                                <li class="competitionBox uk-margin-left uk-margin-top" id="${item.id}">
                                                    <a class="uk-text-center uk-margin-top" href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=-1&formTeamId=${item.id}">
                                                        <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${item.name}">
                                                        <p data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                                    </a>
                                                </li>
                                            </c:when>
                                            <c:otherwise>
                                                <li class="competitionBoxDisabled uk-margin-left uk-margin-top" id="${item.id}">
                                                    <a class="uk-text-center uk-margin-top" href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=-1&formTeamId=${item.id}">
                                                        <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${item.name}">
                                                        <p data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                                    </a>
                                                </li>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:forEach>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>

                <c:if test="${not empty mTeams}">
                    <div id="tabMenu" class="uk-width-4-10 uk-hidden-small">

                        <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#matchesCalendarContent'}" id='calendarTab'>
                            <li id="liNationalTeams">
                                <a><spring:message code="user.nationalTeams"/></a>
                            </li>
                        </ul>

                        <ul id="matchesCalendarContent" class="uk-switcher">
                            <li>
                                <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-6 uk-width-1-1 uk-margin-small-top" >
                                    <c:forEach var="item" items="${mTeams}">
                                        <li class="uk-margin-left" id="${item.id}" onclick="location.href = '/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=-1&formTeamId=${item.id}'" style="min-height: 175px;">
                                            <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height:100px;" href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=-1&formTeamId=${item.id}">
                                                <div class="competitionBox">
                                                    <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${item.name}">
                                                </div>
                                                <p style="margin-bottom: 0; margin-top: 3px;">${item.name}</p>
                                            </a>
                                        </li>
                                    </c:forEach>
                                </ul>
                            </li>
                        </ul>

                        <div id="divFilter" class="uk-offcanvas">
                            <div class="uk-offcanvas-bar uk-offcanvas-bar-flip">
                                <%@ include file="searchMulti.jsp" %>
                            </div>
                        </div>
                    </div>
                </c:if>
            </div>
        </div>
    </body>

</html>
