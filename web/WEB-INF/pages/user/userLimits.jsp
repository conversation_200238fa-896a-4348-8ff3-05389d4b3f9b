<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<c:set var="atLeastOneWarning" value="false"/>
<div id="userLimitsTooltipContent" class="uk-hidden">
    <div>
        <center>
            <span style="font-size: 20px; text-transform: uppercase; font-weight: bold">
                <spring:message code="menu.user.limiti"/>
            </span>
        </center>
        <table class="uk-table uk-table-striped uk-table-large" style="border-collapse: collapse;  height: 100%; width: 100%; box-sizing: content-box;">
            <tbody>
                <c:if test="${!mUser.isPlayerUser()}">
                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:30px;">
                        <c:set var="textClass" value="uk-text-success"/>
                        <c:set var="mainTextClass" value=""/>
                        <c:if test="${numDownload > (totDownload / 5 * 4)}">
                            <c:set var="textClass" value="uk-text-warning"/>
                            <c:set var="mainTextClass" value="uk-text-warning"/>
                            <c:set var="atLeastOneWarning" value="true"/>
                        </c:if>
                        <c:if test="${numDownload >= totDownload}">
                            <c:set var="textClass" value="uk-text-danger"/>
                            <c:set var="mainTextClass" value="uk-text-danger"/>
                            <c:set var="atLeastOneWarning" value="true"/>
                        </c:if>
                        <td class="uk-text-bold uk-width-4-10 ${mainTextClass}" style="font-size: 0.8em;text-transform: uppercase;"><spring:message code="menu.user.downloadDisp"/></td>
                        <td class="uk-text-center ${textClass}" style="font-size: 0.8em; width: 13%">${numDownload} / ${totDownload}</td>
                        <td>
                            <c:choose>
                                <c:when test="${numDownload >= totDownload}">
                                    <span class="${textClass}" style="font-size: 8px; font-weight: normal; font-style: italic; text-transform: uppercase"><spring:message code="menu.user.limit.reached"/></span>
                                </c:when>
                                <c:otherwise>
                                    <span style="font-size: 8px; font-weight: normal; font-style: italic; text-transform: uppercase">(<spring:message code="menu.user.infoDownload"/>)</span>
                                </c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:30px;">
                        <c:set var="textClass" value="uk-text-success"/>
                        <c:set var="mainTextClass" value=""/>
                        <c:set var="percLeft" value=""/>
                        <c:if test="${totExportXmlMade > (totExportXml / 5 * 4)}">
                            <c:set var="textClass" value="uk-text-warning"/>
                            <c:set var="mainTextClass" value="uk-text-warning"/>
                            <c:set var="percLeft" value="<br/>(${Math.round(100 - (totExportXmlMade / totExportXml * 100))}%"/>
                            <c:set var="atLeastOneWarning" value="true"/>
                        </c:if>
                        <c:if test="${totExportXmlMade >= totExportXml}">
                            <c:set var="textClass" value="uk-text-danger"/>
                            <c:set var="mainTextClass" value="uk-text-danger"/>
                            <c:set var="percLeft" value="<br/>(0%"/>
                            <c:set var="atLeastOneWarning" value="true"/>
                        </c:if>
                        <td class="uk-text-bold uk-width-4-10 ${mainTextClass}" style="font-size: 0.8em;text-transform: uppercase;"><spring:message code="menu.user.numero.esportazioni.xml"/>:</td>
                        <td class="uk-text-center ${textClass}" style="font-size: 0.8em; width: 13%">
                            ${totExportXmlMade} / ${totExportXml}
                            <c:if test="${!percLeft.equals('')}">
                                ${percLeft} <spring:message code="menu.user.restante"/>)
                            </c:if>
                        </td>
                        <td>
                            <c:choose>
                                <c:when test="${totExportXmlMade >= totExportXml}">
                                    <span class="${textClass}" style="font-size: 8px; font-weight: normal; font-style: italic; text-transform: uppercase"><spring:message code="menu.user.limit.reached"/></span>
                                </c:when>
                            </c:choose>
                        </td>
                    </tr>
                </c:if>
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:30px;">
                    <c:set var="textClass" value="uk-text-success"/>
                    <c:set var="mainTextClass" value=""/>
                    <c:set var="percLeft" value=""/>
                    <c:if test="${totPlaylistTvMade > (totPlaylistTv / 5 * 4)}">
                        <c:set var="textClass" value="uk-text-warning"/>
                        <c:set var="mainTextClass" value="uk-text-warning"/>
                        <c:set var="percLeft" value="<br/>(${Math.round(100 - (totPlaylistTvMade / totPlaylistTv * 100))}%"/>
                        <c:set var="atLeastOneWarning" value="true"/>
                    </c:if>
                    <c:if test="${totPlaylistTvMade >= totPlaylistTv}">
                        <c:set var="textClass" value="uk-text-danger"/>
                        <c:set var="mainTextClass" value="uk-text-danger"/>
                        <c:set var="percLeft" value="<br/>(0%"/>
                        <c:set var="atLeastOneWarning" value="true"/>
                    </c:if>
                    <td class="uk-text-bold uk-width-4-10 ${mainTextClass}" style="font-size: 0.8em;text-transform: uppercase;"><spring:message code="menu.user.limite.playlisttv"/>:</td>
                    <td class="uk-text-center ${textClass}" style="font-size: 0.8em; width: 13%">
                        ${totPlaylistTvMade} / ${totPlaylistTv}
                        <c:if test="${!percLeft.equals('')}">
                            ${percLeft} <spring:message code="menu.user.restante"/>)
                        </c:if>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${totPlaylistTvMade >= totPlaylistTv}">
                                <span class="${textClass}" style="font-size: 8px; font-weight: normal; font-style: italic; text-transform: uppercase"><spring:message code="menu.user.limit.reached"/></span>
                            </c:when>
                        </c:choose>
                    </td>
                </tr>
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="height:30px;">
                    <c:set var="textClass" value="uk-text-success"/>
                    <c:set var="mainTextClass" value=""/>
                    <c:set var="percLeft" value=""/>
                    <c:if test="${totExportUsed > (totExportMinutes / 5 * 4)}">
                        <c:set var="textClass" value="uk-text-warning"/>
                        <c:set var="mainTextClass" value="uk-text-warning"/>
                        <c:set var="percLeft" value="<br/>(${Math.round(100 - (totExportUsed / totExportMinutes * 100))}%"/>
                        <c:set var="atLeastOneWarning" value="true"/>
                    </c:if>
                    <c:if test="${totExportUsed >= totExportMinutes}">
                        <c:set var="textClass" value="uk-text-danger"/>
                        <c:set var="mainTextClass" value="uk-text-danger"/>
                        <c:set var="percLeft" value="<br/>(0%"/>
                        <c:set var="atLeastOneWarning" value="true"/>
                    </c:if>
                    <td class="uk-text-bold uk-width-4-10 ${mainTextClass}" style="font-size: 0.8em;text-transform: uppercase;"><spring:message code="menu.user.export.limit"/>:</td>
                    <td class="uk-text-center ${textClass}" style="font-size: 0.8em; width: 13%">
                        ${totExportUsed} / ${totExportMinutes} (min)
                        <c:if test="${!percLeft.equals('')}">
                            ${percLeft} <spring:message code="menu.user.restante"/>)
                        </c:if>
                    </td>
                    <td>
                        <c:choose>
                            <c:when test="${totExportUsed >= totExportMinutes}">
                                <span class="${textClass}" style="font-size: 8px; font-weight: normal; font-style: italic; text-transform: uppercase"><spring:message code="menu.user.limit.reached"/></span>
                            </c:when>
                            <c:otherwise>
                                <span style="font-size: 8px; font-weight: normal; font-style: italic; text-transform: uppercase">(<spring:message code="menu.user.export.limit.info"/>)</span>
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </tbody>
        </table>
        <div>
            <center>
                <span style="font-size: 20px; text-transform: uppercase; font-weight: bold">
                    <spring:message code="menu.user.glossary"/>
                </span>
                <br/>
                <a href="https://server.sics.it/download/glossary/SICS-Glossary%20for%20Customers%202025.pdf" target="_blank"><spring:message code="menu.user.glossary.description"/></a>
            </center>
        </div>
    </div>
</div>

<button class="uk-button" id="userLimitsButton" style="margin-top: 5px; margin-right: 5px;"><i class="uk-icon-info uk-icon-small <c:if test="${atLeastOneWarning.equals('true')}">uk-text-danger</c:if>"></i></button>