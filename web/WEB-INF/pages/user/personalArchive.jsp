<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">
        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/flat/flat.css?<%=System.currentTimeMillis()%>" rel="stylesheet">
        <script type="text/javascript">
            // logout ajax
            var allGames = [];
            var allPlaylists = [];
            var zipSelected = 0;
            var canDeleteSelected = 0;
            var isMyHome = true;
            var isPersonalArchive = true;
            var selectedPlaylist = new Map();
            var selectedGame = new Map();
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);


            $(document).ready(function () {
                selectedPlaylist = new Map();
                selectedGame = new Map();
                $("input:checkbox").removeAttr("checked");
            <c:forEach var="map" items="${mPersonalArchive}">
                <c:forEach var="item" items="${map.value}">
                    <c:if test="${item.getPlaylist()!=null}">
                allPlaylists.push('${item.getPlaylist().id}');
                    </c:if>
                    <c:if test="${item.getGame()!=null}">
                allGames.push('${item.getGame().idFixture}');
                    </c:if>
                </c:forEach>
            </c:forEach>

                $("#namePlaylistModify").on('change', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });

                $("#notePlaylistModify").on('change', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });

                $("#namePlaylistModify").on('paste', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });

                $("#notePlaylistModify").on('paste', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);

                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });

                $("#namePlaylistModify").on('keypress', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });

                $("#notePlaylistModify").on('keypress', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });
            });

            function search(mobile) {
                var input, filter, table, ul, li, p1, p2, i, j, txtValue;
                var searchIdTag = "myInput";
                if (mobile) {
                    searchIdTag = "myInput2";
                }
                input = document.getElementById(searchIdTag);
                filter = input.value.toUpperCase();
            <c:forEach var="map" items="${mPersonalArchive}">
                table = document.getElementById("${map.key.replace(" ","")}");
                if (table != null) {
                    ul = document.getElementById("baseSearch_${map.key.replace(" ","")}");
                    li = ul.getElementsByTagName("li");
                    var found = false;
                    for (i = 0; i < li.length; i++) {
                        if (li[i].getElementsByTagName("table").length > 0) {
                            p1 = li[i].getElementsByTagName("table")[0];
                            p1 = p1.getElementsByTagName("td")[1];
                            p1 = p1.getElementsByTagName("div")[0];
                            //p2 = a[i].getElementsByTagName("p")[1];
                            if (p1/* && p2*/) {
                                txtValue = p1.textContent || p1.innerText;
                                //txtValue = txtValue + p2.textContent || p2.innerText;
                                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                                    li[i].style.display = "";
                                    found = true;
                                } else {
                                    li[i].style.display = "none";
                                }
                            }
                        }
                    }
                    if (!found) {
                        table.style.display = "none";
                    } else {
                        table.style.display = "";
                    }
                }
            </c:forEach>
            }

            function selectPlaylist(event, playlistId, isZip, isCanDelete) {
                if (event != null && typeof event != 'undefined') {
                    event.preventDefault();
                }
                if (typeof selectedPlaylist.get(playlistId) == 'undefined') {
                    selectedPlaylist.set(playlistId, isCanDelete);
                    $("#" + playlistId + "playlistli").addClass("playlistSelected");
                    $("#checkbox" + playlistId).prop("checked", true);
                    if (isZip) {
                        zipSelected = zipSelected + 1;
                    }
                    if (isCanDelete) {
                        canDeleteSelected = canDeleteSelected + 1;
                    }
                } else {
                    $("#checkbox" + playlistId).prop("checked", false);
                    selectedPlaylist.delete(playlistId);
                    $("#" + playlistId + "playlistli").removeClass("playlistSelected");
                    if (isZip) {
                        zipSelected = zipSelected - 1;
                    }
                    if (isCanDelete) {
                        canDeleteSelected = canDeleteSelected - 1;
                    }
                }
                var numCheck = selectedGame.size + selectedPlaylist.size;
                if (numCheck > 0) {
                    $("#menuDelete").removeClass("uk-hidden");
                    if (numCheck == 1) {
                        //$("#btnDeletePlGm").removeClass("uk-hidden");
                        $("#btnDownloadPlGm").removeClass("uk-hidden");
                        if (zipSelected == 0) {
                            $("#btnSharePlGm").removeClass("uk-hidden");
                        }
                        if (selectedGame.size == 0) {
                            const [firstKey] = selectedPlaylist.keys();
                            if (typeof selectedPlaylist.get(firstKey) != null && selectedPlaylist.get(firstKey)) {
                                $("#btnModifyPlGm").removeClass("uk-hidden");
                                $("#btnModifyPlGm").removeClass("uk-disabled");
                                $("#btnModifyPlGm").removeClass("disabilitato");
                            } else {
                                $("#btnModifyPlGm").addClass("uk-hidden");
                                $("#btnModifyPlGm").addClass("uk-disabled");
                                $("#btnModifyPlGm").addClass("disabilitato");
                            }
                        } else {
                            $("#btnModifyPlGm").addClass("uk-hidden");
                            $("#btnModifyPlGm").addClass("uk-disabled");
                            $("#btnModifyPlGm").addClass("disabilitato");
                        }
                    } else {
                        //$("#divDeletePlGm").addClass("uk-hidden");
                        //$("#btnDeletePlGm").addClass("uk-hidden");
                        $("#btnDownloadPlGm").addClass("uk-hidden");
                        $("#btnSharePlGm").addClass("uk-hidden");
                        $("#btnModifyPlGm").addClass("uk-hidden");
                        $("#btnModifyPlGm").addClass("uk-disabled");
                        $("#btnModifyPlGm").addClass("disabilitato");
                    }
                    //$("#searchbarpersonal2").addClass("uk-hidden");
                } else {
                    disableSelectionAll();
                    $("#menuDelete").addClass("uk-hidden");
                    $("#btnDownloadPlGm").addClass("uk-hidden");
                    $("#btnSharePlGm").addClass("uk-hidden");
                    zipSelected = 0;
                    canDeleteSelected = 0;
                    //$("#searchbarpersonal2").removeClass("uk-hidden");
                }
                if (canDeleteSelected > 0) {
                    $("#btnDeletePlGm").removeClass("uk-disabled");
                    $("#btnDeletePlGm").removeClass("disabilitato");
                    $("#btnDeletePlGm").prop("disabled", false);
                } else {
                    $("#btnDeletePlGm").addClass("uk-disabled");
                    $("#btnDeletePlGm").addClass("disabilitato");
                    $("#btnDeletePlGm").prop("disabled", true);
                }
                $("#divDeletePlGm").text(numCheck + " " + "<spring:message code="selected.text"/>");
            }

            $(document).on("scroll", function (e) {
                if (window.pageYOffset > 80) {
                    $("#menuDelete").addClass("fix-del");
                    //$("#searchbarpersonal2").addClass("fix-del");
                    $("#menuDelete").removeClass("absolute");
                } else {
                    $("#menuDelete").removeClass("fix-del");
                    //$("#searchbarpersonal2").removeClass("fix-del");
                    $("#menuDelete").addClass("absolute");
                }
            });

            function selectGame(event, gameId, isCanDelete) {
                if (event != null && typeof event != 'undefined') {
                    event.preventDefault();
                }
                if (typeof selectedGame.get(gameId) == 'undefined') {
                    selectedGame.set(gameId, isCanDelete);
                    $("#" + gameId + "gameli").addClass("playlistSelected");
                    if (isCanDelete) {
                        canDeleteSelected = canDeleteSelected + 1;
                    }
                    $("#checkbox" + gameId).prop("checked", true);
                } else {
                    selectedGame.delete(gameId);
                    $("#" + gameId + "gameli").removeClass("playlistSelected");
                    if (isCanDelete) {
                        canDeleteSelected = canDeleteSelected - 1;
                    }
                    $("#checkbox" + gameId).prop("checked", false);
                }
                var numCheck = selectedGame.size + selectedPlaylist.size;
                if (numCheck > 0) {
                    $("#menuDelete").removeClass("uk-hidden");
                    if (numCheck == 1) {
                        //$("#btnDeletePlGm").removeClass("uk-hidden");
                        $("#btnDownloadPlGm").removeClass("uk-hidden");
                        if (zipSelected == 0) {
                            $("#btnSharePlGm").removeClass("uk-hidden");
                        }
                        if (selectedGame.size == 0) {
                            const [firstKey] = selectedPlaylist.keys();
                            if (typeof selectedPlaylist.get(firstKey) != null && selectedPlaylist.get(firstKey)) {
                                $("#btnModifyPlGm").removeClass("uk-hidden");
                                $("#btnModifyPlGm").removeClass("uk-disabled");
                                $("#btnModifyPlGm").removeClass("disabilitato");
                            } else {
                                $("#btnModifyPlGm").addClass("uk-hidden");
                                $("#btnModifyPlGm").addClass("uk-disabled");
                                $("#btnModifyPlGm").addClass("disabilitato");
                            }
                        } else {
                            $("#btnModifyPlGm").addClass("uk-hidden");
                            $("#btnModifyPlGm").addClass("uk-disabled");
                            $("#btnModifyPlGm").addClass("disabilitato");
                        }
                    } else {
                        //$("#divDeletePlGm").addClass("uk-hidden");
                        //$("#btnDeletePlGm").addClass("uk-hidden");
                        $("#btnDownloadPlGm").addClass("uk-hidden");
                        $("#btnSharePlGm").addClass("uk-hidden");
                        $("#btnModifyPlGm").addClass("uk-hidden");
                        $("#btnModifyPlGm").addClass("uk-disabled");
                        $("#btnModifyPlGm").addClass("disabilitato");
                    }
                    //$("#searchbarpersonal2").addClass("uk-hidden");
                } else {
                    disableSelectionAll();
                    $("#menuDelete").addClass("uk-hidden");
                    $("#btnDownloadPlGm").addClass("uk-hidden");
                    $("#btnSharePlGm").addClass("uk-hidden");
                    $("#btnModifyPlGm").addClass("uk-hidden");
                    $("#btnModifyPlGm").addClass("uk-disabled");
                    $("#btnModifyPlGm").addClass("disabilitato");
                    zipSelected = 0;
                    canDeleteSelected = 0;
                    //$("#searchbarpersonal2").removeClass("uk-hidden");
                }
                if (canDeleteSelected > 0) {
                    $("#btnDeletePlGm").removeClass("uk-disabled");
                    $("#btnDeletePlGm").removeClass("disabilitato");
                    $("#btnDeletePlGm").prop("disabled", false);
                } else {
                    $("#btnDeletePlGm").addClass("uk-disabled");
                    $("#btnDeletePlGm").addClass("disabilitato");
                    $("#btnDeletePlGm").prop("disabled", true);
                }
                $("#divDeletePlGm").text(numCheck + " " + "<spring:message code="selected.text"/>");
            }

            function reset() {
                selectedPlaylist = new Map();
                selectedGame = new Map();
                zipSelected = 0;
                canDeleteSelected = 0;
                $("input:checkbox").removeAttr("checked");
            }

            function jsDownloadPlaylist(id) {
                var strUrl = "/sicstv/user/downloadPlaylist.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "true") {
                            UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                            window.location.replace(msg.substr(5));
                            $.unblockUI();
                        }
                    }
                });
                sessionStorage.removeItem('exportIdentifier');
            }

            function shareLinkPlaylist(id) {
                const fallbackCopyTextToClipboardPlaylist = str => {
                    var textArea = document.createElement("textarea");
                    textArea.value = str;

                    // Avoid scrolling to bottom
                    textArea.style.top = "0";
                    textArea.style.left = "0";
                    textArea.style.position = "fixed";

                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();

                    try {
                        var successful = document.execCommand('copy');
                        var msg = successful ? 'successful' : 'unsuccessful';
                        UIkit.notify("<spring:message code="menu.user.linkCopiato"/>", {status: 'success', timeout: 1000});
                    } catch (err) {
                        UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger', timeout: 1000});
                        return Promise.reject('The Clipboard API is not available.');
                    }
                    document.body.removeChild(textArea);
                }

                const copyToClipboardPlaylist = str => {
                    if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
                        UIkit.notify("<spring:message code="menu.user.linkCopiato"/>", {status: 'success', timeout: 1000});
                        return navigator.clipboard.writeText(str);
                    } else {
                        fallbackCopyTextToClipboardPlaylist(str);
                    }
                    //UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger'});
                    //return Promise.reject('The Clipboard API is not available.');
                };

                var strUrl = "/sicstv/user/shareLinkPlaylist.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        console.log(msg);
                        if (msg.substr(0, 5) === "false") {
                            UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger'});
                        } else {
                            //COPIA msg negli appunti
                            copyToClipboardPlaylist(msg);
                        }
                    },
                    async: false
                });
            }

            function shareLinkGame(id) {
                const fallbackCopyTextToClipboardGame = str => {
                    var textArea = document.createElement("textarea");
                    textArea.value = str;

                    // Avoid scrolling to bottom
                    textArea.style.top = "0";
                    textArea.style.left = "0";
                    textArea.style.position = "fixed";

                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();

                    try {
                        var successful = document.execCommand('copy');
                        var msg = successful ? 'successful' : 'unsuccessful';
                        UIkit.notify("<spring:message code="menu.user.linkCopiato"/>", {status: 'success', timeout: 1000});
                    } catch (err) {
                        UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger', timeout: 1000});
                        return Promise.reject('The Clipboard API is not available.');
                    }
                    document.body.removeChild(textArea);
                }

                const copyToClipboardGame = str => {
                    if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
                        UIkit.notify("<spring:message code="menu.user.linkCopiato"/>", {status: 'success', timeout: 1000});
                        return navigator.clipboard.writeText(str);
                    } else {
                        fallbackCopyTextToClipboardGame(str);
                    }
                    //UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger'});
                    //return Promise.reject('The Clipboard API is not available.');
                };
                var strUrl = "/sicstv/user/shareLinkGame.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        console.log(msg);
                        if (msg.substr(0, 5) === "false") {
                            UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger', timeout: 1000});
                        } else {
                            //COPIA msg negli appunti
                            copyToClipboardGame(msg);
                        }
                    },
                    async: false
                });
            }

            function jsDownloadGame(id) {
                var strUrl = "/sicstv/user/download.htm?id=" + id + "&type=0";
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "true") {
                            UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                            window.location.replace(msg.substr(5));
                        }
                    }
                });
            }

            function goToPage(pageLink, type, id, zip, candelete) {
                if (selectedGame.size + selectedPlaylist.size > 0) {
                    if (type == 0) {
                        //PLAYLIST
                        selectPlaylist(null, id, zip, candelete);
                    } else {
                        //GAME
                        selectGame(null, id, candelete);
                    }
                } else {
                    window.location.replace(pageLink);
                }
            }

            function openModify() {
                $("#menuDelete").addClass("uk-hidden");
                const [firstKey] = selectedPlaylist.keys();
                var pl = selectedPlaylist.get(firstKey);
                if (pl != null && typeof pl != 'undefined' && pl) {
                    openModifyPlaylist(firstKey);
                } else {
                    closeModifyPlaylist();
                }
            }

            function openModifyPlaylist(id) {
                var playlistDiv = document.getElementById("playlistNameDiv_" + id);
                var namePlaylist = playlistDiv.textContent || playlistDiv.innerText;
                $("#saveModifyPlaylist").html("<button style='min-width:80px;' onclick='updatePlaylist(" + id + ");' class='uk-text-large uk-text-bold uk-button js-modal-confirm'><spring:message code='playlist.save'/></button>");
                $("#menuDelete").addClass("uk-hidden");
                $("#modalModifyPlaylist").addClass("uk-open");
                $("#modalModifyPlaylist").removeClass("uk-hidden");
                $("#namePlaylistModify").val(namePlaylist);
            }

            function updatePlaylist(id) {
                var nameValue = $("#namePlaylistModify").val();
                var noteValue = $("#notePlaylistModify").val();
                if (typeof nameValue !== 'undefined' && typeof noteValue !== 'undefined') {
                    noteValue = noteValue.trim();
                    nameValue = nameValue.trim();
                    if (nameValue !== '') {// && noteValue != '') {
                        $.ajax({
                            type: "POST",
                            url: "/sicstv/user/updatePlaylist.htm",
                            cache: false,
                            data: encodeURI("&id=" + id + "&name=" + nameValue + "&note=" + noteValue),
                            success: function (msg) {
                                $("#menuDelete").addClass("uk-hidden");
                                if (msg.substr(0, 5) === "false") {
                                    UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                                } else {
                                    closeModifyPlaylist();
                                }
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            }
                        });
                    } else {
                        UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                    }
                } else {
                    UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                }
            }

            function closeDelete() {
                if (selectedPlaylist.size + selectedGame.size > 0) {
                    $("#menuDelete").removeClass("uk-hidden");
                } else {
                    $("#menuDelete").addClass("uk-hidden");
                }
                $("#modalDeleteConfirm").removeClass("uk-open");
                $("#modalDeleteConfirm").addClass("uk-hidden");
                $("#modalDeleteConfirmText").html("");
            }

            function closeDelete2() {
                if (selectedPlaylist.size + selectedGame.size > 0) {
                    $("#menuDelete").removeClass("uk-hidden");
                } else {
                    $("#menuDelete").addClass("uk-hidden");
                }
                $("#modalDeleteConfirm2").removeClass("uk-open");
                $("#modalDeleteConfirm2").addClass("uk-hidden");
                $("#modalDeleteConfirmText2").html("");
            }

            function enableSelectGame(idGame, isCanDelete) {
                selectGame(null, idGame, isCanDelete);
                $("#checkbox" + idGame).prop("checked", true);
                for (var i = 0; i < allPlaylists.length; i++) {
                    $("#gearPlaylist" + allPlaylists[i]).addClass("uk-hidden");
                    $("#selectionPlaylist" + allPlaylists[i]).removeClass("uk-hidden");
                    $("#playPlaylist" + allPlaylists[i]).addClass("uk-hidden");
                }
                for (var i = 0; i < allGames.length; i++) {
                    $("#gearGame" + allGames[i]).addClass("uk-hidden");
                    $("#selectionGame" + allGames[i]).removeClass("uk-hidden");
                    $("#playGame" + allGames[i]).addClass("uk-hidden");
                }
            }

            function enableSelectPlaylist(idPlaylist, isZip, isCanDelete) {
                selectPlaylist(null, idPlaylist, isZip, isCanDelete);
                $("#checkbox" + idPlaylist).prop("checked", true);
                for (var i = 0; i < allPlaylists.length; i++) {
                    $("#gearPlaylist" + allPlaylists[i]).addClass("uk-hidden");
                    $("#selectionPlaylist" + allPlaylists[i]).removeClass("uk-hidden");
                    $("#playPlaylist" + allPlaylists[i]).addClass("uk-hidden");
                }
                for (var i = 0; i < allGames.length; i++) {
                    $("#gearGame" + allGames[i]).addClass("uk-hidden");
                    $("#selectionGame" + allGames[i]).removeClass("uk-hidden");
                    $("#playGame" + allGames[i]).addClass("uk-hidden");
                }
            }

            function deselectAll() {
                for (var i = 0; i < allPlaylists.length; i++) {
                    selectedPlaylist.delete(allPlaylists[i]);
                    $("#" + allPlaylists[i] + "playlistli").removeClass("playlistSelected");
                    $("#gearPlaylist" + allPlaylists[i]).addClass("uk-hidden");
                    $("#selectionPlaylist" + allPlaylists[i]).removeClass("uk-hidden");
                    $("#checkbox" + allPlaylists[i]).prop("checked", false);
                }
                for (var i = 0; i < allGames.length; i++) {
                    selectedGame.delete(allGames[i]);
                    $("#" + allGames[i] + "gameli").removeClass("playlistSelected");
                    $("#gearGame" + allGames[i]).addClass("uk-hidden");
                    $("#selectionGame" + allGames[i]).removeClass("uk-hidden");
                    $("#checkbox" + allGames[i]).prop("checked", false);
                }
                $("#menuDelete").addClass("uk-hidden");
                zipSelected = 0;
                canDeleteSelected = 0;
                selectedPlaylist = new Map();
                selectedGame = new Map();
                disableSelectionAll();
            }

            function disableSelectionAll() {
                for (var i = 0; i < allPlaylists.length; i++) {
                    $("#gearPlaylist" + allPlaylists[i]).removeClass("uk-hidden");
                    $("#selectionPlaylist" + allPlaylists[i]).addClass("uk-hidden");
                    $("#playPlaylist" + allPlaylists[i]).removeClass("uk-hidden");
                }
                for (var i = 0; i < allGames.length; i++) {
                    $("#gearGame" + allGames[i]).removeClass("uk-hidden");
                    $("#selectionGame" + allGames[i]).addClass("uk-hidden");
                    $("#playGame" + allGames[i]).removeClass("uk-hidden");
                }
            }

            function selectAll() {
                for (var i = 0; i < allPlaylists.length; i++) {
                    $("#gearPlaylist" + allPlaylists[i]).addClass("uk-hidden");
                    $("#selectionPlaylist" + allPlaylists[i]).removeClass("uk-hidden");
                }
                for (var i = 0; i < allGames.length; i++) {
                    $("#gearGame" + allGames[i]).addClass("uk-hidden");
                    $("#selectionGame" + allGames[i]).removeClass("uk-hidden");
                }
            <c:forEach var="map" items="${mPersonalArchive}">
                <c:forEach var="item" items="${map.value}">
                    <c:if test="${item.getPlaylist()!=null}">
                if (typeof selectedPlaylist.get('${item.getPlaylist().id}') == 'undefined') {
                    selectPlaylist(null, '${item.getPlaylist().id}',${item.getPlaylist().isZip()},${item.getPlaylist().isCanDelete()});
                    $("#checkbox${item.getPlaylist().id}").prop("checked", true);
                }
                    </c:if>
                    <c:if test="${item.getGame()!=null}">

                if (typeof selectedGame.get('${item.getGame().idFixture}') == 'undefined') {
                    selectGame(null, '${item.getGame().idFixture}',${item.getGame().isCanDelete()});
                    $("#checkbox${item.getGame().idFixture}").prop("checked", true);
                }
                    </c:if>
                </c:forEach>
            </c:forEach>
            }

            function askDelete() {
                $("#menuDelete").addClass("uk-hidden");
                var lenghtPlaylist = selectedPlaylist.size;
                var lenghtGame = selectedGame.size;
                $("#modalDeleteConfirmText").html('<spring:message code="menu.deleting"/>:<br>' + lenghtPlaylist + ' <spring:message code="menu.admin.esportazioni"/><br>' + lenghtGame + ' <spring:message code="menu.user.incontri"/><br><spring:message code="menu.areyousure"/>');
                $("#modalDeleteConfirm").removeClass("uk-hidden");
                $("#modalDeleteConfirm").addClass("uk-open");
                $("#modalDeleteConfirmDeleteBtn").removeClass("uk-hidden");
                $("#modalDeleteConfirmCancelBtn").removeClass("uk-hidden");
                $("#modalDeleteConfirmOkBtn").addClass("uk-hidden");
            }

            function askDeletePlaylist(id) {
                $("#menuDelete").addClass("uk-hidden");
                $("#modalDeleteConfirmText2").html('<spring:message code="menu.deleting"/>:<br>' + 1 + ' <spring:message code="menu.admin.esportazioni"/><br><spring:message code="menu.areyousure"/>');
                $("#modalDeleteConfirm2").removeClass("uk-hidden");
                $("#modalDeleteConfirm2").addClass("uk-open");
                $("#modalDeleteConfirmDeleteBtn2").removeClass("uk-hidden");
                $("#modalDeleteConfirmCancelBtn2").removeClass("uk-hidden");
                $("#modalDeleteConfirmOkBtn2").addClass("uk-hidden");
                $("#spanDel2").html("<button id='modalDeleteConfirmDeleteBtn2'  style='min-width:80px;' onclick='deletePlaylist(" + id + ")' class='uk-text-large uk-text-bold uk-button uk-button-danger js-modal-confirm'><i class='uk-icon uk-icon-trash uk-margin-small-right'></i><spring:message code='menu.user.delete'/></button>");
            }

            function askDeleteGame(id) {
                $("#menuDelete").addClass("uk-hidden");
                $("#modalDeleteConfirmText2").html('<spring:message code="menu.deleting"/>:<br>' + 0 + ' <spring:message code="menu.admin.esportazioni"/><br>' + 1 + ' <spring:message code="menu.user.incontri"/><br><spring:message code="menu.areyousure"/>');
                $("#modalDeleteConfirm2").removeClass("uk-hidden");
                $("#modalDeleteConfirm2").addClass("uk-open");
                $("#modalDeleteConfirmDeleteBtn2").removeClass("uk-hidden");
                $("#modalDeleteConfirmCancelBtn2").removeClass("uk-hidden");
                $("#modalDeleteConfirmOkBtn2").addClass("uk-hidden");
                $("#spanDel2").html("<button id='modalDeleteConfirmDeleteBtn2'  style='min-width:80px;' onclick='deleteGame(" + id + ")' class='uk-text-large uk-text-bold uk-button uk-button-danger js-modal-confirm'><i class='uk-icon uk-icon-trash uk-margin-small-right'></i><spring:message code='menu.user.delete'/></button>");
            }

            function deleteGame(id) {
                var strUrl = "/sicstv/user/deleteGamesAndPlaylists.htm?idPlaylists=&idGames=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        $("#menuDelete").addClass("uk-hidden");
                        if (msg.substr(0, 5) === "false") {
                            UIkit.notify("<spring:message code="menu.user.errorDeleting"/>", {status: 'danger', timeout: 1000});
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            $("#modalDeleteConfirmText2").html(msg);
                            $("#modalDeleteConfirm2").removeClass("uk-hidden");
                            $("#modalDeleteConfirm2").addClass("uk-open");
                            $("#modalDeleteConfirmDeleteBtn2").addClass("uk-hidden");
                            $("#modalDeleteConfirmCancelBtn2").addClass("uk-hidden");
                            $("#modalDeleteConfirmOkBtn2").removeClass("uk-hidden");
                        }
                    }
                });
            }

            function deletePlaylist(id) {
                var strUrl = "/sicstv/user/deleteGamesAndPlaylists.htm?idPlaylists=" + id + "&idGames=";
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        $("#menuDelete").addClass("uk-hidden");
                        if (msg.substr(0, 5) === "false") {
                            UIkit.notify("<spring:message code="menu.user.errorDeleting"/>", {status: 'danger', timeout: 1000});
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            $("#modalDeleteConfirmText2").html(msg);
                            $("#modalDeleteConfirm2").removeClass("uk-hidden");
                            $("#modalDeleteConfirm2").addClass("uk-open");
                            $("#modalDeleteConfirmDeleteBtn2").addClass("uk-hidden");
                            $("#modalDeleteConfirmCancelBtn2").addClass("uk-hidden");
                            $("#modalDeleteConfirmOkBtn2").removeClass("uk-hidden");
                        }
                    }
                });
            }

            function deletePlaylistAndGameSelected() {
                var lenghtPlaylist = selectedPlaylist.size;
                var lenghtGame = selectedGame.size;
                var totalLenght = lenghtPlaylist + lenghtGame;
                if (totalLenght > 0) {
                    var strUrl = "/sicstv/user/deleteGamesAndPlaylists.htm?idPlaylists=" + String(Array.from(selectedPlaylist.keys())).replaceAll(",", "-") + "&idGames=" + String(Array.from(selectedGame.keys())).replaceAll(",", "-");
                    $.ajax({
                        type: "GET",
                        url: strUrl,
                        cache: false,
                        success: function (msg) {
                            $("#menuDelete").addClass("uk-hidden");
                            if (msg.substr(0, 5) === "false") {
                                UIkit.notify("<spring:message code="menu.user.errorDeleting"/>", {status: 'danger', timeout: 1000});
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            } else {
                                $("#modalDeleteConfirmText").html(msg);
                                $("#modalDeleteConfirm").removeClass("uk-hidden");
                                $("#modalDeleteConfirm").addClass("uk-open");
                                $("#modalDeleteConfirmDeleteBtn").addClass("uk-hidden");
                                $("#modalDeleteConfirmCancelBtn").addClass("uk-hidden");
                                $("#modalDeleteConfirmOkBtn").removeClass("uk-hidden");
                            }
                        }
                    });
                }
                $("#menuDelete").removeClass("uk-hidden");
                closeDelete();
            }

            function reloadPage() {
                setTimeout(() => {
                    window.location.reload();
                }, 500);
            }

            function downloadPlaylistAndGameSelected() {
                if (selectedPlaylist.size == 1 && selectedGame.size == 0) {
                    const [firstKey] = selectedPlaylist.keys();
                    jsDownloadPlaylist(firstKey);
                } else if (selectedPlaylist.size == 0 && selectedGame.size == 1) {
                    const [firstKey] = selectedGame.keys();
                    jsDownload(firstKey, 0);
                }
            }

            function sharePlaylistAndGameSelected() {
                if (selectedPlaylist.size == 1 && selectedGame.size == 0) {
                    const [firstKey] = selectedPlaylist.keys();
                    shareLinkPlaylist(firstKey);
                } else if (selectedPlaylist.size == 0 && selectedGame.size == 1) {
                    const [firstKey] = selectedGame.keys();
                    shareLinkGame(firstKey);
                }
            }
        </script>
    </head>
    <body onload="reset();">
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>
        <!-- This is the modal -->
        <div>
            <div class="uk-grid">
                <div id="competitions" class="uk-width-1-1">
                    <div id="breadcrumb">
                        <ul class="uk-breadcrumb uk-margin-left">
                            <li style="text-transform: uppercase;"><a href="/sicstv/user/myhome.htm"><spring:message code="menu.user.mysicstv"/></a><i id="showCurrentFilter"></i></li>
                            <li style="text-transform: uppercase;"><spring:message code="menu.user.mysicstv_archivio"/><i id="showCurrentFilter"></i></li>
                        </ul>
                    </div>
                    <div id="searchbarpersonal2" class="uk-form uk-hidden-large" style="padding-top: 0px;">
                        <input type="text" id="myInput2" class=" uk-width-1-1" onkeyup="search(true)" placeholder="<spring:message code="search.name"/>" title="<spring:message code="search.name"/>" style="padding: 12px 20px 12px 40px;background-position: 10px 10px;background-repeat: no-repeat;height:40px;">
                    </div>
                    <div id="menuDelete" class="uk-hidden absolute" style="vertical-align:middle;">
                        <button style="min-width:50px;" id="btnDeletePlGm" class="uk-button uk-button-danger uk-margin-all uk-float-right" onclick="askDelete();"><i class="uk-icon-medium uk-icon-trash"></i><span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.user.delete"/></span></button>
                        <button style="min-width:50px;" id="btnDownloadPlGm" class="uk-button uk-margin-all uk-float-right" onclick="downloadPlaylistAndGameSelected();"><i class="uk-icon-medium uk-icon-download"></i><span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.user.download"/></span></button>
                        <button style="min-width:50px;" id="btnSharePlGm" class="uk-button uk-margin-all uk-float-right" onclick="sharePlaylistAndGameSelected();"><i class="uk-icon-medium uk-icon-share"></i><span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.share"/></span></button>
                        <button style="min-width:50px;" id="btnModifyPlGm" class="uk-button uk-margin-all uk-float-right" onclick="openModify();"><i class="uk-icon-medium uk-icon-pencil"></i><span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.modify"/></span></button>
                        <button style="min-width:50px;" id="btnSelectAllPlGm" class="uk-button uk-margin-all uk-float-right" onclick="selectAll();"><i class="uk-icon-medium uk-icon-check"></i><span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.user.selectall"/></span></button>
                        <button style="min-width:50px;" id="btnDeselectAllPlGm" class="uk-button uk-margin-all uk-float-right" onclick="deselectAll();"><i class="uk-icon-medium uk-icon-close"></i><span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.annulla"/></span></button>
                        <div id="divDeletePlGm" class="uk-text-bold uk-margin-all-large uk-float-right"></div>
                    </div>
                    <c:forEach var="map" items="${mPersonalArchive}">
                        <c:if test="${map.value.isEmpty()==false}">
                            <div id="${map.key.replace(" ","")}">
                                <div class="uk-margin-right uk-width-1-1 uk-nav nav-date">
                                    <span class="uk-container uk-margin-left">
                                        <div class="uk-margin-left">${map.key}</div>
                                    </span>
                                </div>
                                <div class="uk-flex uk-width-1-1">
                                    <ul id="baseSearch_${map.key.replace(" ","")}" class="uk-thumbnav uk-flex-left uk-margin-small-top uk-margin-left">
                                        <c:forEach var="item" items="${map.value}">
                                            <c:choose>
                                                <c:when test="${item.getPlaylist()!=null}">
                                                    <li id="${item.getPlaylist().getId()}playlistli" class="competitionBox uk-margin-left" style="max-width: 270px;min-width: 270px;">
                                                        <a href="#">
                                                            <div class="uk-overlay-default uk-position-relative">
                                                                <c:if test="${!item.getPlaylist().isZip()}">
                                                                    <img class="logoCompImgX uk-margin-top uk-margin-left uk-margin-right" src="https://s3.eu-west-1.amazonaws.com/it.sics.svs.thumb/${item.getPlaylist().getThumbName()}" onerror="this.src='/sicstv/images/<spring:message code="mysicstv.nopreview"/>'" alt="${item.getPlaylist().name}" onclick="goToPage('/sicstv/user/mysicstv.htm?playlist=${item.getPlaylist().id}', 0, '${item.getPlaylist().id}',${item.getPlaylist().isZip()},${item.getPlaylist().isCanDelete()})">
                                                                </c:if>
                                                                <c:if test="${item.getPlaylist().isZip()}">
                                                                    <img src="/sicstv/images/zip169.png" class="logoCompImgX uk-margin-top uk-margin-left uk-margin-right" onclick="goToPage('/sicstv/user/mysicstv.htm?playlist=${item.getPlaylist().id}', 0, '${item.getPlaylist().id}',${item.getPlaylist().isZip()},${item.getPlaylist().isCanDelete()})"/>
                                                                </c:if>
                                                                <!--div class="uk-position-top-right uk-margin-all-small uk-form" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="Playlist">
                                                                <c:if test="${item.getPlaylist().isZip()}">
                                                                    <span class="squaredGearArchive uk-text-center">
                                                                        <i class="uk-icon-large uk-icon-file-zip-o uk-margin-small-right" style="margin-top:18%;margin-left:15%;"></i>
                                                                    </span>
                                                                </c:if>
                                                                <c:if test="${!item.getPlaylist().isZip()}">
                                                                    <span class="squaredGearArchive uk-text-center">
                                                                        <i class="uk-icon-large uk-icon-file-video-o uk-margin-small-right" style="margin-top:18%;margin-left:15%;"></i>
                                                                    </span>
                                                                </c:if>
                                                            </div-->
                                                                <div onclick="goToPage('/sicstv/user/mysicstv.htm?playlist=${item.getPlaylist().id}', 0, '${item.getPlaylist().id}',${item.getPlaylist().isZip()},${item.getPlaylist().isCanDelete()})" id="playPlaylist${item.getPlaylist().id}" class="uk-flex uk-flex-center uk-flex-middle uk-position-top uk-margin-small-top uk-width-1-1 uk-text-center uk-text-middle" style="color:#f27e46;min-height:calc(100%*9/16);bottom:0px;">
                                                                    <span class="squaredGearArchive uk-width-1-1 uk-text-center" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="Playlist" >
                                                                        <i class="uk-icon-large uk-icon-play uk-width-1-1" style="margin-top:20%;margin-left: 5%;"></i>
                                                                    </span>
                                                                </div>
                                                                <div id="selectionPlaylist${item.getPlaylist().id}" class="uk-hidden uk-position-top-left uk-margin-all-large uk-form" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.select"/>">
                                                                    <span class="squaredCheckboxArchive"><input id="checkbox${item.getPlaylist().getId()}" type="checkbox" class="competitionBoxX"/><label onclick="selectPlaylist(event, '${item.getPlaylist().getId()}',${item.getPlaylist().isZip()},${item.getPlaylist().isCanDelete()});" for="checkbox${item.getPlaylist().getId()}" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.select"/>"></label></span>
                                                                </div>
                                                                <div id="gearPlaylist${item.getPlaylist().id}" class="uk-position-top-left uk-margin-all uk-form">
                                                                    <div data-uk-dropdown="{mode:'click'}">
                                                                        <span class="squaredMenuArchive"><i class="uk-icon-large uk-icon-ellipsis-h" style="margin-top:20%;margin-left:25%;color:white"></i></span>                                                                        
                                                                        <div class=" uk-dropdown uk-padding-remove">
                                                                            <ul class="uk-nav uk-nav-dropdown uk-navbar-dropdown-nav">
                                                                                <li style="margin-top:6px;margin-bottom:6px;margin-left: 9px;" onclick="enableSelectPlaylist('${item.getPlaylist().id}',${item.getPlaylist().isZip()},${item.getPlaylist().isCanDelete()})"><i class="uk-icon-check-square-o uk-margin-small-right"></i><spring:message code="menu.user.select"/></li>
                                                                                <li class="uk-nav-divider uk-margin-remove"></li>
                                                                                    <c:if test="${item.getPlaylist().isCanDelete()}">
                                                                                    <li style="margin-top:6px;margin-bottom:6px;margin-left: 9px;" onclick="openModifyPlaylist('${item.getPlaylist().id}');"><i class="uk-icon-pencil uk-margin-small-right"></i><spring:message code="menu.modify"/></li>
                                                                                    <li class="uk-nav-divider uk-margin-remove"></li>
                                                                                    </c:if>
                                                                                    <c:if test="${!item.getPlaylist().isZip()}">
                                                                                    <li style="margin-top:6px;margin-bottom:6px;margin-left: 9px;" onclick="shareLinkPlaylist('${item.getPlaylist().id}');"><i class="uk-icon-share uk-margin-small-right"></i><spring:message code="menu.share"/></li>
                                                                                    <li class="uk-nav-divider uk-margin-remove"></li>
                                                                                    </c:if>
                                                                                <li style="margin-top:6px;margin-bottom:6px;margin-left: 9px;" onclick="jsDownloadPlaylist('${item.getPlaylist().id}');"><i class="uk-icon-download uk-margin-small-right"></i><spring:message code="menu.user.download"/></li>
                                                                                    <c:if test="${item.getPlaylist().isCanDelete()}">
                                                                                    <li class="uk-nav-divider uk-margin-remove"></li>
                                                                                    <li style="margin-top:6px;margin-bottom:6px;margin-left: 9px;" onclick="askDeletePlaylist('${item.getPlaylist().id}');"><i class="uk-icon-trash uk-margin-small-right"></i><spring:message code="menu.user.delete"/></li>
                                                                                    </c:if>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <table class="uk-grid uk-margin-left uk-margin-right uk-text-truncate" style="max-width:270;" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="${item.getPlaylist().name}</br>PLAYLIST</br>${item.getPlaylist().uploadLastName} ${item.getPlaylist().uploadFirstName}</br>${item.getPlaylist().getDateDayString()} ${item.getPlaylist().getDateMonthString()} ${item.getPlaylist().getDateYearString()}">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>
                                                                            <div style="max-width: 60px;" class="dateBoxPlaylistX uk-margin-small-top uk-margin-small-bottom uk-text-center">
                                                                                <b>${item.getPlaylist().getDateDayString()}<br>${item.getPlaylist().getDateMonthString()}</b>
                                                                            </div>
                                                                        </td>
                                                                        <td style="max-width: 180px;">
                                                                            <div id="playlistNameDiv_${item.getPlaylist().id}" style="text-transform: uppercase;max-width: 180px;" class="uk-text-bold uk-margin-small-top uk-text-truncate">${item.getPlaylist().name}</div>
                                                                            <div style="text-transform: uppercase;max-width: 180px;" class="uk-text-truncate playlistDetail"><spring:message code="esportazione"/></div>
                                                                            <div style="text-transform: uppercase;max-width: 180px;" class="uk-text-truncate uk-margin-small-bottom playlistDetail">${item.getPlaylist().uploadLastName} ${item.getPlaylist().uploadFirstName}</div>
                                                                        </td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </a>
                                                    </li>
                                                </c:when>
                                                <c:otherwise>
                                                    <li id="${item.getGame().getId()}gameli" class="competitionBox uk-margin-left uk-text-truncate" style="max-width: 270px;min-width: 270px;">
                                                        <a href="#">
                                                            <div class="uk-overlay-default uk-position-cover uk-position-relative">
                                                                <img class="logoCompImgX uk-margin-top uk-margin-left uk-margin-right" src="https://s3.eu-west-1.amazonaws.com/it.sics.svs.thumb/${item.getGame().getThumbName()}" onerror="this.src='/sicstv/images/<spring:message code="mysicstv.nopreview"/>'" alt="${item.getGame().homeTeam} - ${item.getGame().awayTeam}" onclick="goToPage('/sicstv/user/mysicstv.htm?game=${item.getGame().idFixture}', 1, '${item.getGame().idFixture}', false,${item.getGame().isCanDelete()})">
                                                                <!--div class="uk-position-top-right uk-margin-all-small uk-form" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="video.partita"/>">
                                                                    <span class="squaredGearArchive uk-text-center"><i class="uk-icon-large uk-icon-tag uk-margin-small-right" style="margin-top:18%;margin-left:15%;"></i></span>
                                                                </div-->
                                                                <div onclick="goToPage('/sicstv/user/mysicstv.htm?game=${item.getGame().idFixture}', 1, '${item.getGame().idFixture}', false,${item.getGame().isCanDelete()})" id="playGame${item.getGame().idFixture}" class="uk-flex uk-flex-center uk-flex-middle uk-position-top uk-margin-small-top uk-width-1-1 uk-text-center uk-text-middle" style="color:#f27e46;min-height:calc(100%*9/16);bottom:0px;">
                                                                    <span class="squaredGearArchive uk-width-1-1 uk-text-center" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="video.partita"/>">
                                                                        <i class="uk-icon-large uk-icon-play uk-width-1-1" style="margin-top:20%;margin-left: 5%;"></i>
                                                                    </span>
                                                                </div>
                                                                <div id="selectionGame${item.getGame().idFixture}" class="uk-hidden uk-position-top-left uk-margin-all-large uk-form" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.select"/>">
                                                                    <span class="squaredCheckboxArchive"><input id="checkbox${item.getGame().getIdFixture()}" value="${item.getGame()}" type="checkbox" class="competitionBoxX"/><label onclick="selectGame(event, '${item.getGame().idFixture}', ${item.getGame().isCanDelete()});" for="checkbox${item.getGame().getIdFixture()}" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="<spring:message code="menu.user.select"/>"></label></span>
                                                                </div>
                                                                <div id="gearGame${item.getGame().idFixture}" class="uk-position-top-left uk-margin-all uk-form">
                                                                    <div data-uk-dropdown="{mode:'click'}">
                                                                        <span class="squaredMenuArchive"><i class="uk-icon-large uk-icon-ellipsis-h" style="margin-top:20%;margin-left:25%;"></i></span>
                                                                        <div class=" uk-dropdown uk-padding-remove">
                                                                            <ul class="uk-nav uk-nav-dropdown uk-navbar-dropdown-nav">
                                                                                <li style="margin-top:6px;margin-bottom:6px;margin-left: 9px;" onclick="enableSelectGame('${item.getGame().idFixture}',${item.getGame().isCanDelete()});"><i class="uk-icon-check-square-o uk-margin-small-right"></i><spring:message code="menu.user.select"/></li>
                                                                                <li class="uk-nav-divider uk-margin-remove"></li>
                                                                                <li style="margin-top:6px;margin-bottom:6px;margin-left: 9px;" onclick="shareLinkGame('${item.getGame().idFixture}');"><i class="uk-icon-share uk-margin-small-right"></i><spring:message code="menu.share"/></li>
                                                                                <li class="uk-nav-divider uk-margin-remove"></li>
                                                                                <li style="margin-top:6px;margin-bottom:6px;margin-left: 9px;" onclick="jsDownloadGame('${item.getGame().idFixture}');"><i class="uk-icon-download uk-margin-small-right"></i><spring:message code="menu.user.download"/></li>
                                                                                    <c:if test="${item.getGame().isCanDelete()}">
                                                                                    <li class="uk-nav-divider uk-margin-remove"></li>
                                                                                    <li style="margin-top:6px;margin-bottom:6px;margin-left: 9px;" onclick="askDeleteGame('${item.getGame().idFixture}');"><i class="uk-icon-trash uk-margin-small-right"></i><spring:message code="menu.user.delete"/></li>
                                                                                    </c:if>
                                                                            </ul>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <table class="uk-grid uk-margin-left uk-margin-right uk-text-truncate" style="max-width:270;" data-uk-tooltip="{pos:'bottom',animation:'true',cls:'width-height-auto'}" title="${item.getGame().homeTeam} - ${item.getGame().awayTeam}</br>${item.getGame().competitionName}<c:if test="${!item.getGame().ownerName.equals('')}"></br>${item.getGame().ownerName}</c:if></br>${item.getGame().getDateDayString()} ${item.getGame().getDateMonthString()} ${item.getGame().getDateYearString()}">
                                                                    <tr>
                                                                        <td>
                                                                            <div style="max-width: 60px;" class="dateBoxPlaylistX uk-margin-small-top uk-margin-small-bottom uk-text-center">
                                                                                <b>${item.getGame().getDateDayString()}<br>${item.getGame().getDateMonthString()}</b>
                                                                        </div>
                                                                    </td>
                                                                    <td style="max-width: 180px;">
                                                                        <div style="text-transform: uppercase;max-width: 180px;" class="uk-text-truncate uk-margin-small-top uk-text-bold">${item.getGame().homeTeam} - ${item.getGame().awayTeam}</div>
                                                                        <div style="text-transform: uppercase;max-width: 180px;" class="uk-text-truncate playlistDetail"><spring:message code="partita"/>: ${item.getGame().competitionName}</div>
                                                                        <div style="text-transform: uppercase;max-width: 180px;" class="uk-text-truncate uk-margin-small-bottom playlistDetail"><c:if test="${!item.getGame().ownerName.equals('')}">${item.getGame().ownerName}</c:if></div>
                                                                        </td>
                                                                    </tr>
                                                                </table>
                                                            </a>
                                                        </li>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:forEach>
                                    </ul>
                                </div>
                            </div>
                        </c:if>
                    </c:forEach>
                </div>
            </div>
            <div id="modalDeleteConfirm" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
                <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 90%;height:auto;">
                    <div>
                        <div id="modalDeleteConfirmText" class="uk-margin uk-modal-content uk-text-bold uk-text-large"></div>
                        <div class="uk-modal-footer uk-text-right">
                            <button id="modalDeleteConfirmCancelBtn"  style="min-width:80px;" onclick="closeDelete();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                            <button id="modalDeleteConfirmDeleteBtn"  style="min-width:80px;" onclick="deletePlaylistAndGameSelected();" class="uk-text-large uk-text-bold uk-button uk-button-danger js-modal-confirm"><i class="uk-icon uk-icon-trash uk-margin-small-right"></i><spring:message code="menu.user.delete"/></button>
                            <button id="modalDeleteConfirmOkBtn" style="min-width:80px;" onclick="reloadPage();" class="uk-text-large uk-text-bold uk-button js-modal-confirm">OK</button>
                        </div>
                    </div>
                </div>
            </div>
            <div id="modalDeleteConfirm2" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
                <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 90%;height:auto;">
                    <div>
                        <div id="modalDeleteConfirmText2" class="uk-margin uk-modal-content uk-text-bold uk-text-large"></div>
                        <div class="uk-modal-footer uk-text-right">
                            <button id="modalDeleteConfirmCancelBtn2"  style="min-width:80px;" onclick="closeDelete2();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                            <span id="spanDel2"><button id="modalDeleteConfirmDeleteBtn2"  style="min-width:80px;" onclick="" class="uk-text-large uk-text-bold uk-button uk-button-danger js-modal-confirm"><i class="uk-icon uk-icon-trash uk-margin-small-right"></i><spring:message code="menu.user.delete"/></button></span>
                            <button id="modalDeleteConfirmOkBtn2" style="min-width:80px;" onclick="reloadPage();" class="uk-text-large uk-text-bold uk-button js-modal-confirm">OK</button>
                        </div>
                    </div>
                </div>
            </div>
            <div id="modalModifyPlaylist" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
                <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 90%;height:auto;">
                    <div>
                        <div class="uk-margin uk-modal-content uk-width-1-1">
                            <label class="uk-text-bold uk-text-large" for="namePlaylistModify"><spring:message code="video.descrizione" />:</label>
                        </div>
                        <div class="uk-margin uk-modal-content uk-width-1-1">
                            <input id="namePlaylistModify" class="uk-input uk-width-1-1 uk-form-large" type="text" aria-label="Input">
                        </div>
                        <div class="uk-margin uk-modal-content uk-width-1-1">
                            <label class="uk-text-bold uk-text-large" for="notePlaylistModify"><spring:message code="video.note" />:</label>
                        </div>
                        <div class="uk-margin uk-modal-content uk-width-1-1" disabled>
                            <textarea id="notePlaylistModify" class="uk-textarea uk-width-1-1 uk-form-large" name="text" style="resize: none;min-height: 80px;" disabled></textarea>
                        </div>
                        <div class="uk-modal-footer uk-text-right uk-width-1-1">
                            <button id="cancelModifyPlaylist"  style="min-width:80px;" onclick="closeModifyPlaylist();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                            <span id="saveModifyPlaylist"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
