<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/slider.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/flat/flat.css?<%=System.currentTimeMillis()%>" rel="stylesheet">

        <!-- Keep here: needed for teamStats.jsp -->
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/3.2.1/css/fixedColumns.dataTables.min.css"/>
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdn.datatables.net/fixedcolumns/3.2.1/js/dataTables.fixedColumns.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.colVis.min.js"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.lazyload/1.9.1/jquery.lazyload.min.js"></script>
        <!-- Keep here: needed for teamStats.jsp for buttons (export) -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
        <script src="https://cdn.datatables.net/colreorder/1.7.0/js/dataTables.colReorder.min.js"></script>

        <!-- Keep here: needed for slider -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.js"></script>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/noUiSlider/15.7.1/nouislider.min.css" rel="stylesheet">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/wnumb/1.2.0/wNumb.min.js"></script>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <script src="https://cdn.jsdelivr.net/npm/choices.js/public/assets/scripts/choices.min.js"></script>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js/public/assets/styles/choices.min.css"/>

        <style>
            .noUi-target {
                transform: scale(0.75) translateX(-50%);
                transform-origin: 0 0;
                left: 50%;
            }

            #compareTable tbody > tr {
                min-height: 50px !important;
            }

            #tableContainer .dataTables_length {
                float: right !important;
            }

            .choices__input {
                min-width: 100% !important;
                width: 100% !important;
                box-sizing: border-box;
            }

            .choices__inner {
                width: 94% !important;
            }
        </style>

        <script type="text/javascript">
            var table, tableCompare, tableOptions, tableLastAjaxParams = "params=&columns=", tableData, tableColumns, tablePlayerLink;
            var pageToLoad, pageLen = 10, tableSort = [[1, 'asc']];
            var baseAjaxUrl = '/sicstv/user/smartSearchPlayerData.htm?';
            var statsType = new Map(), statsTypeFilter = new Map(), userAvailableCompetitions = [];
            var currentFilter;
            var skipAjax = true;
            var smartSearchTranslation = new Map(), oppositeColumns = new Map();
            var competitionSelect;

            $(document).ready(function () {
            <c:forEach var="stat" items="${mAllColumns}">
                statsType.set(${stat.id}, ${stat.getJson()});
            </c:forEach>
            <c:forEach var="stat" items="${mStatsTypeFilters}">
                statsTypeFilter.set(${stat.id}, '${stat.filter}');
            </c:forEach>
            <c:forEach var="competition" items="${mUser.availableCompetition}">
                userAvailableCompetitions.push("${competition.name}");
            </c:forEach>
            <c:forEach var="stat" items="${mAllOppositeColumns}">
                oppositeColumns.set("${stat.desc}", ${stat.getJson()});
            </c:forEach>

                smartSearchTranslation.set("playerPhoto", "<spring:message code='smart.search.playerPhoto'/>");
                smartSearchTranslation.set("playerKnownName", "<spring:message code='smart.search.playerKnownName'/>");
                smartSearchTranslation.set("playerLastTeamName", "<spring:message code='smart.search.playerLastTeamName'/>");
                smartSearchTranslation.set("playerCountryName", "<spring:message code='smart.search.playerCountryName'/>");
                smartSearchTranslation.set("playerPosition", "<spring:message code='smart.search.playerPosition'/>");
                smartSearchTranslation.set("playerAge", "<spring:message code='smart.search.playerAge'/>");
                smartSearchTranslation.set("playerFoot", "<spring:message code='smart.search.playerFoot'/>");
                smartSearchTranslation.set("playerHeight", "<spring:message code='smart.search.playerHeight'/>");
                smartSearchTranslation.set("totalMatches", "<spring:message code='smart.search.totalMatches'/>");
                smartSearchTranslation.set("totalMatchesTitolare", "<spring:message code='smart.search.totalMatchesTitolare'/>");
                smartSearchTranslation.set("totalMatchesSubentrato", "<spring:message code='smart.search.totalMatchesSubentrato'/>");
                smartSearchTranslation.set("totalMatchesSostituito", "<spring:message code='smart.search.totalMatchesSostituito'/>");
                smartSearchTranslation.set("totalMatchesForNational", "<spring:message code='smart.search.totalMatchesForNational'/>");
                smartSearchTranslation.set("playerValue", "<spring:message code="smart.search.player.value"/>");
                smartSearchTranslation.set("timeframe", "<spring:message code='smart.search.timeframe'/>");
                smartSearchTranslation.set("genere", "<spring:message code='smart.search.genere'/>");

                if (typeof sessionStorage["smartSearchTablePage"] !== 'undefined') {
                    pageToLoad = parseInt(sessionStorage["smartSearchTablePage"]);
                }
                if (typeof sessionStorage["smartSearchTablePageLen"] !== 'undefined') {
                    pageLen = parseInt(sessionStorage["smartSearchTablePageLen"]);
                }
                if (typeof localStorage["tablePageLen"] !== 'undefined') {
                    pageLen = parseInt(localStorage["tablePageLen"]);
                }
                if (typeof sessionStorage["smartSearchTableSort"] !== 'undefined') {
                    tableSort = sessionStorage["smartSearchTableSort"];

                    if (tableSort.includes(",")) {
                        var splitted = tableSort.split(",");
                        if (splitted.length === 2) {
                            var tmpSort = [];
                            tmpSort.push([parseInt(splitted[0]), splitted[1]]);
                            console.log("applying sort", splitted[1], "on column", splitted[0]);
                            tableSort = tmpSort;
                        }
                    }
                }

                initSearchFilter();
                tableOptions = {
                    language: {
                        "emptyTable": "<spring:message code='smart.search.no.data'/>"
                    },
                    processing: true,
                    serverSide: true,
                    deferLoading: true,
                    ajax: {
                        type: "POST",
                        url: baseAjaxUrl + tableLastAjaxParams,
                        dataType: 'json',
                        contentType: 'application/json; charset=utf-8',
                        dataSrc: function (datas) {
                            // console.log("ajax call made");
                            sessionStorage["smartSearchTablePage"] = table.page();
                            sessionStorage["smartSearchTablePageLen"] = table.page.len();
                            localStorage["tablePageLen"] = table.page.len();
                            sessionStorage["smartSearchTableSort"] = table.order()[0];

                            var columns = [];
                            if (datas.columns) {
                                // Se la risposta contiene informazioni sulle colonne
                                columns = datas.columns.map(function (column) {
                                    if (column.title.startsWith("statsType")) {
                                        var baseTitle = column.title;
                                        var typeId = parseInt(column.title.replace("statsType", "").replace("P90", "").replace("Percentage", ""));
                                        if (statsType.has(typeId)) {
                                            column.title = statsType.get(typeId).desc;
                                            if (baseTitle.includes("P90")) {
                                                column.title += " P90";
                                            } else if (baseTitle.includes("Percentage")) {
                                                column.title += " %";
                                            }
                                        }
                                    } else {
                                        if (typeof smartSearchTranslation.get(column.title) !== "undefined") {
                                            column.title = smartSearchTranslation.get(column.title);
                                        } else if (column.title === "checkbox") {
                                            column.title = "";
                                        }
                                    }

                                    return {name: column.name, title: column.title};
                                });
                            }

                            tableColumns = columns;
                            // tolgo colonna checkbox altrimenti il compare non viene fuori correttamente
                            tableColumns = tableColumns.filter(item => item.name !== "checkbox");

                            tableData = JSON.parse(JSON.stringify(datas.data));
                            tablePlayerLink = datas.playerLink;
                            reloadCompare();
                            var indexToAddFilter = [];
                            tableColumns.forEach(function (element, index) {
                                if (element.name.startsWith("statsType")) {
                                    indexToAddFilter.push(index + 1);
                                }
                            });
                            if (datas.playerLink) {
                                if (datas.playerLink.length === datas.data.length) {
                                    datas.data.forEach(function (element, index) {
                                        var playerCompetitions = datas.playerLink[index].playerCompetitions;
                                        var clickAllowed = false;
                                        if (typeof playerCompetitions !== "undefined" && playerCompetitions) {
                                            var splitted = playerCompetitions.split(",");
                                            if (splitted) {
                                                // uso every perchè altrimenti il return false non esce dal loop
                                                splitted.every(function (element, index) {
                                                    var competition = element;
                                                    if (typeof competition !== "undefined") {
                                                        var found = userAvailableCompetitions.indexOf(competition) >= 0;
                                                        if (found) {
                                                            clickAllowed = true;
                                                            return false;
                                                        }
                                                    }
                                                });
                                            }
                                        }

                                        if (clickAllowed) {
                                            var playerId, competitionId = "", teamId = "";

                                            // nella posizione 1 c'è il nome del giocatore
                                            var playerLink = datas.playerLink[index];
                                            var seasons = playerLink.playerSeasons;
                                            var seasonToLoad = seasons;
                                            var seasonToLoadForStatistics = seasons;
                                            if (seasons) {
                                                if (seasons.includes(",")) {
                                                    var splitted = seasons.split(",");
                                                    // 2023,23
                                                    if (splitted.length === 2) {
                                                        var firstSeason = parseInt(splitted[0]);
                                                        var secondSeason = parseInt(splitted[1]);

                                                        if (firstSeason < 2000) {
                                                            seasonToLoad = firstSeason;
                                                        } else {
                                                            seasonToLoadForStatistics = firstSeason;
                                                        }
                                                        if (secondSeason < 2000) {
                                                            seasonToLoad = secondSeason;
                                                        } else {
                                                            seasonToLoadForStatistics = secondSeason;
                                                        }
                                                    }
                                                }
                                            }
                                            var link = '<a style="font-weight: bold; color: #46ab44" class="orangeOnHover statRowteams" href="/sicstv/user/goTo.htm?seasonId=' + seasonToLoad + '&link=';
                                            var playerGotoLink = '/sicstv/user/player.htm?personal=false';
                                            if (playerLink.playerId) {
                                                playerGotoLink += "&playerId=" + playerLink.playerId;
                                                playerId = playerLink.playerId;
                                            }
                                            if (playerLink.competitionId) {
                                                playerGotoLink += "&competitionId=" + playerLink.competitionId;
                                                competitionId = playerLink.competitionId;
                                            }
                                            if (playerLink.teamId) {
                                                playerGotoLink += "&teamId=" + playerLink.teamId;
                                                teamId = playerLink.teamId;
                                            }

                                            element[1] = link + encodeURIComponent(playerGotoLink) + '">' + element[1] + '</a>'; // foto cliccabile
                                            element[2] = link + encodeURIComponent(playerGotoLink) + '">' + element[2] + '</a>'; // giocatore cliccabile
                                            if (playerLink.lastTeamId) {
                                                var teamGotoLink = '/sicstv/user/team.htm?personal=false';
                                                if (playerLink.competitionId) {
                                                    teamGotoLink += "&formCompetitionId=" + playerLink.competitionId;
                                                } else {
                                                    teamGotoLink += "&formCompetitionId=-1";
                                                }
                                                teamGotoLink += "&formTeamId=" + playerLink.lastTeamId;
                                                element[3] = link + encodeURIComponent(teamGotoLink) + '">' + element[3] + '</a>'; // team cliccabile
                                            }

                                            if (indexToAddFilter.length > 0) {
                                                indexToAddFilter.forEach(function (indexToFilter) {
                                                    var value = element[indexToFilter];
                                                    if (!isNaN(parseFloat(value))) {
                                                        var typeId = parseInt(tableColumns[indexToFilter - 1].name.replace("statsType", "").replace("P90", "").replace("Percentage", ""));
                                                        if (typeId && !isNaN(typeId)) {
                                                            var filter = statsTypeFilter.get(typeId);
                                                            if (typeof filter !== "undefined") {
                                                                link = '<a style="font-weight: bold; color: #46ab44" class="orangeOnHover statRowteams" href="/sicstv/user/goTo.htm?seasonId=' + seasonToLoadForStatistics + '&link=';
                                                                link += encodeURIComponent('/sicstv/user/video.htm?personal=false&id=&idComp=' + competitionId + '&idTeam=' + teamId + '&idPlayer=' + playerId + '&goals=false&event=' + encodeURIComponent(filter) + '&filter=&limit=50');
                                                                link += '">';
                                                                link += value + "</a>";

                                                                element[indexToFilter] = link;
                                                            }
                                                        }
                                                    }
                                                });
                                            }
                                        } else {
                                            element[1] = element[1].replace(">", " clickable=false>");
                                        }
                                    });
                                }
                            }
                            if (typeof table.init().columns === 'undefined' || table.init().columns.length !== columns.length || lastAjaxAdditionalColumns !== additionalColumns) {
                                lastAjaxAdditionalColumns = additionalColumns;
                                tableOptions.columns = columns;
                                table.clear().destroy(true);
                                $("#tableContainer").append('<table id="smartSearch" class="uk-table uk-table-hover row-border order-column" style="border-collapse: collapse; margin-left: 0px; max-height: 95%"/>');
                                var tmpColumnDefs = [{orderable: false, targets: [0, 1]}, {width: '30px', targets: [1, 2, 3, 4, 5]}];
                                var playerValueIndex;
                                Object.keys(columns).forEach(function (index) {
                                    if (columns[index].name === "playerValue") {
                                        playerValueIndex = parseInt(index);
                                    }
                                });
                                if (typeof playerValueIndex !== "undefined") {
                                    tmpColumnDefs.push({render: $.fn.dataTable.render.number('.', ',', 0, ''), targets: [playerValueIndex]});
                                }
                                table = $('#smartSearch').DataTable($.extend({}, tableOptions, {
                                    order: tableSort,
                                    columnDefs: tmpColumnDefs
                                }));
                                table.clear().rows.add(datas.data).draw();
                                if (typeof table.columns !== "undefined") {
                                    table.columns.adjust();
                                }
                                // se cambio tabella ritorno nulla, altrimenti il numero di colonne
                                // definite nella tabella e il numero di valori per ogni riga generano
                                // un errore nel caso in cui rimuovo delle colonne
                                return [];
                            } else {
                                return datas.data;
                            }
                        }
                    },
                    createdRow: function (row, data, dataIndex) {
                        // 'data' contiene l'oggetto JSON per la riga corrente
                        // metto tutti i dati in centro
                        $(row).find("td").attr('class', "center-text middle-text");
                        $(row).find("td").each(function (index, element) {
                            var columnName = tableOptions.columns[index].name;
                            $(element).attr("column-index", columnName);
                        });

                        var clickable = $(row).find("img:first").attr("clickable");
                        if (clickable === "false") {
                            $(row).attr("style", "background-color: #eaeaea!important");
                            //$("img[clickable='false']").parent().parent().attr("style", "background-color: #f5f5c0!important");
                        }
                    },
                    columns: [{name: '1'}],
                    paging: true,
                    dom: 'Blfrtip',
                    buttons: [
                        {
                            text: '<spring:message code='smart.search.manage.columns'/>',
                            attr: {
                                class: 'uk-button'
                            },
                            action: function (e, dt, node, config) {
                                manageColumns();
                            }
                        }, {
                            text: '<spring:message code='team.stats.manage.filter'/>',
                            attr: {
                                class: 'uk-button'
                            },
                            action: function (e, dt, node, config) {
                                manageTables();
                            }
                        }, {
                            text: '<i class="uk-icon-eye"></i><span class="uk-margin-small-left" id="addToWatchlistAmount"></span>',
                            attr: {
                                id: 'addToWatchlist',
                                title: '<spring:message code='watchlist.add.players'/>',
                                class: 'uk-button uk-button-confirm uk-hidden'
                            },
                            action: function (e, dt, node, config) {
                                openWatchlistModal();
                            }
                        }, {
                            text: '<i class="uk-icon-save"></i>',
                            attr: {
                                title: '<spring:message code='team.stats.save.filter'/>',
                                class: 'uk-button'
                            },
                            action: function (e, dt, node, config) {
                                createSaveFilter();
                            }
                        }, {
                            text: '<spring:message code='team.stats.esporta'/>',
                            attr: {
                                class: 'uk-button uk-hidden showMeIfNeeded'
                            },
                            extend: 'collection',
                            buttons: [
                                {
                                    extend: 'print',
                                    exportOptions: {
                                        columns: ':visible'
                                    },
                                    title: 'SICS.tv-SmartSearch'
                                }, {
                                    extend: 'excel',
                                    exportOptions: {
                                        columns: ':visible'
                                    },
                                    title: 'SICS.tv-SmartSearch'
                                }
                            ]
                        }, {
                            text: '<spring:message code='team.stats.esporta'/>',
                            attr: {
                                class: 'uk-button uk-hidden'
                            },
                            extend: 'collection',
                            buttons: [
                                {
                                    extend: 'print',
                                    exportOptions: {
                                        columns: ':visible'
                                    },
                                    title: 'SICS.tv-SmartSearch'
                                }, {
                                    extend: 'excel',
                                    exportOptions: {
                                        columns: ':visible'
                                    },
                                    title: 'SICS.tv-SmartSearch'
                                }
                            ]
                        }
                    ],
                    searching: false,
                    scrollY: "75%",
                    scrollX: true,
                    scrollCollapse: true,
                    autoWidth: true,
                    stateSave: true,
                    pageLength: pageLen,
                    lengthMenu: [10, 15, 20, 25, 50],
                    lengthChange: true,
                    drawCallback: function () {
                        selectedPlayerIds = [];
                        updateSelectedPlayerIds();

                        // rinvio il caricamento delle immagini
                        // per velocizzare la visualizzazione della tabella
                        $("img.lazy").lazyload({
                            placeholder: "/sicstv/images/user_gray.png"
                        }).on("error", function () {
                            // Questo codice verrà eseguito se l'immagine non potrà essere caricata
                            $(this).attr("src", "/sicstv/images/user_gray.png");
                        });
                    },
                    initComplete: function () {
                        var searchWrapper = $(".dt-buttons");

            <c:if test="${mFilters.isEmpty() == false}">
                        var select = '<span class="uk-hidden showMeIfNeeded uk-form" style="margin-right: 10px"><select id="smartSearchFilter" onchange="changeFilter(this);" class="uk-form-select selectItem" style="width: auto">';
                <c:set var="sicsTitleAdded" value="false"/>
                <c:set var="personalTitleAdded" value="false"/>
                <c:forEach var="filter" items="${mFilters.keySet()}">
                    <c:if test="${sicsTitleAdded == false}">
                        select += '<option disabled class="uk-text-bold">SICS</option>';
                        <c:set var="sicsTitleAdded" value="true"/>
                    </c:if>
                    <c:if test="${personalTitleAdded == false && filter.id > 0}">
                        select += '<option disabled class="uk-text-uppercase uk-text-bold"><spring:message code="menu.custom"/></option>';
                        <c:set var="personalTitleAdded" value="true"/>
                    </c:if>
                        select += '<option value="${filter.id}"' + (typeof currentFilter !== "undefined" && currentFilter === ${filter.id} ? " selected" : "") + '>${filter.name}</option>';
                </c:forEach>
                        select += '</select></span>';
                        var customButton = $(select);
                        searchWrapper.prepend(customButton);
            </c:if>
                        searchWrapper.append('<div class="uk-hidden showMeIfNeeded center uk-margin-left-small uk-float-left uk-margin-right"><span class="uk-margin-right-small"><spring:message code='menu.user.compare.mode'/></span><label class="uk-switch" for="smartSearchGrouped"><input type="checkbox" class="smartSearchGrouped" id="smartSearchGrouped" onchange="manageCompare();" ' + (isCompareMode ? "checked" : "") + '><div class="uk-switch-slider"></div></label></div>');

                        $('#smartSearch').on('draw.dt', function () {
                            if (typeof table !== "undefined") {
                                if (typeof pageToLoad !== "undefined" && pageToLoad >= 0) {
                                    if (table.page() !== pageToLoad) {
                                        console.log("loaded page", pageToLoad, "from cache");
                                        table.page(pageToLoad).draw('page');
                                    } else {
                                        if ($("#compareContainer").hasClass("uk-hidden")) {
                                            $("#tableContainer").removeClass("uk-hidden");
                                            $(".showMeIfNeeded").removeClass("uk-hidden");
                                        }
                                        // bisogna lanciare l'adjust altrimenti la grafica è rotta
                                        table.columns.adjust();
                                    }
                                    pageToLoad = -1;
                                } else {
                                    if ($("#compareContainer").hasClass("uk-hidden")) {
                                        $("#tableContainer").removeClass("uk-hidden");
                                        $(".showMeIfNeeded").removeClass("uk-hidden");
                                    }
                                    // bisogna lanciare l'adjust altrimenti la grafica è rotta
                                    table.columns.adjust();
                                }
                            }
                        });
                        $('#smartSearch').on('column-reorder.dt', function (e, settings, details) {
                            $("img.lazy").lazyload({
                                placeholder: "/sicstv/images/user_gray.png"
                            }).on("error", function () {
                                // Questo codice verrà eseguito se l'immagine non potrà essere caricata
                                $(this).attr("src", "/sicstv/images/user_gray.png");
                            });
                        });
                    }
                };

                table = $('#smartSearch').DataTable(tableOptions);

            <c:if test="${mFilters.isEmpty() == false}">
                currentFilter = ${mFirstFilter.id};
            </c:if>

                if (typeof sessionStorage["smartSearchFilterId"] !== 'undefined') {
                    currentFilter = parseInt(sessionStorage["smartSearchFilterId"]);
                }

                if (typeof currentFilter !== "undefined") {
                    var columnsToCheck = getFilterColumns(currentFilter);
                    columnsToCheck.forEach(function (element) {
                        manageColumnCheck(element, true);
                        additionalColumns.push(element);
                    });
                }

                if (typeof sessionStorage["smartSearchSliders"] !== 'undefined') {
                    var sliderToInitialize = Array.from(sessionStorage["smartSearchSliders"].split("&"));
                    if (sliderToInitialize) {
                        sliderToInitialize.forEach(function (element) {
                            var splitted = element.split(";");
                            if (splitted.length === 5) {
                                var fieldName = splitted[0];
                                var min = parseFloat(splitted[1]);
                                var max = parseFloat(splitted[2]);
                                var minToSet = parseFloat(splitted[3]);
                                var maxToSet = parseFloat(splitted[4]);

                                initializeSlider(fieldName, min, max, true, minToSet, maxToSet);

                                // codice copiato dal manageSlider();
                                var index = additionalColumns.indexOf(fieldName);
                                if (index === -1) {
                                    additionalColumns.push(fieldName);
                                }
                                $("." + fieldName).attr("checked", "checked");
                            }
                        });
                    }
                }

                competitionSelect = new Choices("[fieldname='competition']", {
                    removeItemButton: true,
                    placeholder: true,
                    placeholderValue: '<spring:message code="menu.area.search"/>...',
                    searchPlaceholderValue: '',
                    itemSelectText: '',
                    shouldSort: false,         // <--- DISATTIVA ordinamento automatico!
                    shouldSortItems: false,    // <--- (opzionale) disattiva anche l'ordinamento dei selezionati
                });

                if (typeof sessionStorage["smartSearchFiltergenere"] !== 'undefined') {
                    $("#filterContainer select[fieldname='genere']").val(sessionStorage["smartSearchFiltergenere"]);
                }
                // lo faccio sempre altrimenti quando resetto filtri (deleteCache()) non viene impostato "men" e carica tutte le competizioni (sia maschili che femminili)
                updateSelectValues("genere", false);
                if (typeof sessionStorage["smartSearchFiltercompetition"] !== 'undefined') {
                    competitionSelect.setChoiceByValue(sessionStorage["smartSearchFiltercompetition"].split("||"));
                    updateSelectValues("competition", false);
                }
                if (typeof sessionStorage["smartSearchFiltertimeframe"] !== 'undefined') {
                    $("#filterContainer select[fieldname='timeframe']").val(sessionStorage["smartSearchFiltertimeframe"]);
                }
                if (typeof sessionStorage["smartSearchFilterteam"] !== 'undefined') {
                    $("#filterContainer select[fieldname='team']").val(sessionStorage["smartSearchFilterteam"]);
                    updateSelectValues("team", false);
                }
                if (typeof sessionStorage["smartSearchFiltercountry"] !== 'undefined') {
                    $("#filterContainer select[fieldname='country']").val(sessionStorage["smartSearchFiltercountry"]);
                }
                if (typeof sessionStorage["smartSearchFilterrole"] !== 'undefined') {
                    $("#filterContainer select[fieldname='role']").val(sessionStorage["smartSearchFilterrole"]);
                }
                if (typeof sessionStorage["smartSearchFilterrole.detailed"] !== 'undefined') {
                    $("#filterContainer select[fieldname='role.detailed']").val(sessionStorage["smartSearchFilterrole.detailed"]);
                }
                if (typeof sessionStorage["smartSearchFilterfoot"] !== 'undefined') {
                    $("#filterContainer select[fieldname='foot']").val(sessionStorage["smartSearchFilterfoot"]);
                }
                if (typeof sessionStorage["smartSearchCompareMode"] !== 'undefined') {
                    isCompareMode = !(sessionStorage["smartSearchCompareMode"] === 'true');
                    manageCompare();
                }

                applyFilters(true);
                updateSelectedButtons();

                var customOptionsAccordion = {
                    collapse: false,
                    showfirst: true
                };
                $(".accordionDiv").each(function (index, element) {
                    UIkit.accordion("#" + $(element).attr("id"), customOptionsAccordion).on('toggle.uk.accordion', function () {
                        updateSelectedButtons();
                    });
                });
            });

            function getPlayerLink(index) {
                // nella posizione 1 c'è il nome del giocatore
                var playerLink = tablePlayerLink[index];
                var seasons = playerLink.playerSeasons;
                var seasonToLoad = seasons;
                if (seasons) {
                    if (seasons.includes(",")) {
                        var splitted = seasons.split(",");
                        // 2023,23
                        if (splitted.length === 2) {
                            var firstSeason = parseInt(splitted[0]);
                            var secondSeason = parseInt(splitted[1]);

                            if (firstSeason < 2000) {
                                seasonToLoad = firstSeason;
                            }
                            if (secondSeason < 2000) {
                                seasonToLoad = secondSeason;
                            }
                        }
                    }
                }
                var link = '<a style="font-weight: bold; color: #46ab44" class="orangeOnHover statRowteams" href="/sicstv/user/goTo.htm?seasonId=' + seasonToLoad + '&link=';
                var playerGotoLink = '/sicstv/user/player.htm?personal=false';
                if (playerLink.playerId) {
                    playerGotoLink += "&playerId=" + playerLink.playerId;
                }
                if (playerLink.competitionId) {
                    playerGotoLink += "&competitionId=" + playerLink.competitionId;
                }
                if (playerLink.teamId) {
                    playerGotoLink += "&teamId=" + playerLink.teamId;
                }
                return link + encodeURIComponent(playerGotoLink) + '">{{name}}</a>';
            }

            function getTeamLink(index) {
                // nella posizione 1 c'è il nome del giocatore
                var playerLink = tablePlayerLink[index];
                if (playerLink.lastTeamId) {
                    var seasons = playerLink.playerSeasons;
                    var seasonToLoad = seasons;
                    if (seasons) {
                        if (seasons.includes(",")) {
                            var splitted = seasons.split(",");
                            // 2023,23
                            if (splitted.length === 2) {
                                var firstSeason = parseInt(splitted[0]);
                                var secondSeason = parseInt(splitted[1]);

                                if (firstSeason < 2000) {
                                    seasonToLoad = firstSeason;
                                }
                                if (secondSeason < 2000) {
                                    seasonToLoad = secondSeason;
                                }
                            }
                        }
                    }
                    var link = '<a style="font-weight: bold; color: #46ab44" class="orangeOnHover statRowteams" href="/sicstv/user/goTo.htm?seasonId=' + seasonToLoad + '&link=';
                    var teamGotoLink = '/sicstv/user/team.htm?personal=false';
                    if (playerLink.competitionId) {
                        teamGotoLink += "&formCompetitionId=" + playerLink.competitionId;
                    } else {
                        teamGotoLink += "&formCompetitionId=-1";
                    }
                    teamGotoLink += "&formTeamId=" + playerLink.lastTeamId;
                    return link + encodeURIComponent(teamGotoLink) + '">{{name}}</a>';
                } else {
                    return null;
                }
            }

            var sliders = new Map();
            function manageSlider(fieldName, element) {
                var isChecked = $(element).attr("checked");
                if (typeof isChecked === "undefined") {
                    $("#smartSearchFiltersModal").find("." + fieldName).removeAttr("checked");
                } else {
                    var index = additionalColumns.indexOf(fieldName);
                    if (index === -1) {
                        additionalColumns.push(fieldName);
                    }
                    $("." + fieldName).attr("checked", "checked");
                }

                if (sliders.has(fieldName)) {
                    $("#slider" + fieldName).remove();
                    sliders.get(fieldName).destroy();
                    sliders.delete(fieldName);
                    applyFilters();
                    updateSelectedButtons();
                    return;
                }

                var timeframe = $("select.timeframe").val();
                var genere = $("select.genere").val();
                // prima di tutto devo prendere i valori min e max
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/getStatsTypeMinAndMax.htm?fieldName=" + fieldName + "&timeframe=" + timeframe + "&genere=" + genere,
                    cache: false,
                    success: function (values) {
                        if (values && values.includes("-_-")) {
                            var min, max;
                            min = parseFloat(values.split("-_-")[0]);
                            max = parseFloat(values.split("-_-")[1]);

                            initializeSlider(fieldName, min, max);
                        }
                    }
                });

                updateSelectedButtons();
            }

            function reloadSliders() {
                if (sliders && sliders.size > 0) {
                    sliders.keys().forEach(function (element) {
                        var timeframe = $("select.timeframe").val();
                        var genere = $("select.genere").val();
                        // prima di tutto devo prendere i valori min e max
                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/getStatsTypeMinAndMax.htm?fieldName=" + element + "&timeframe=" + timeframe + "&genere=" + genere,
                            cache: false,
                            success: function (values) {
                                if (values && values.includes("-_-")) {
                                    var min, max;
                                    min = parseFloat(values.split("-_-")[0]);
                                    max = parseFloat(values.split("-_-")[1]);

                                    console.log("updating slider", element, "range to", min, max);
                                    sliders.get(element).updateOptions({
                                        range: {
                                            'min': min,
                                            'max': max
                                        }
                                    });
                                    checkIfNeededToApplyFilters();
                                }
                            }
                        });
                    });
                }
            }

            // questa funzione serve per aspettare che tutte le chiamate ajax che ricaricano
            // i valori degli slider finiscano per poi ricaricare i dati correttamente
            var sliderCounter = 0;
            function checkIfNeededToApplyFilters() {
                sliderCounter++;
                if (sliderCounter === sliders.size) {
                    applyFilters();
                    sliderCounter = 0;
                }
            }

            var additionalColumns = [], additionalColumnsToRemove = [], lastAjaxAdditionalColumns;
            function manageColumn(fieldName, specificCheck) {
                var index = additionalColumns.indexOf(fieldName);
                if (index === -1) {
                    additionalColumns.push(fieldName);
                    var needToReAdd = additionalColumnsToRemove.indexOf(fieldName);
                    if (needToReAdd !== -1) {
                        additionalColumnsToRemove.splice(needToReAdd, 1);
                    }
                } else {
                    additionalColumnsToRemove.push(fieldName);
                    additionalColumns.splice(index, 1);
                }

                manageColumnCheck(fieldName, specificCheck);
                updateSelectedButtons();
                // applyFilters();
            }

            function manageColumnCheck(fieldName, initialCheck) {
                // aggiorno solo le checkbox del modale delle colonne, altrimenti segna la colonna anche come filtro
                var isChecked = $("#smartSearchColumnsModal").find("." + fieldName + "[checked]").length > 0;
                if (isChecked && (typeof initialCheck === "undefined" || !initialCheck)) {
                    $("#smartSearchColumnsModal").find("." + fieldName).removeAttr("checked");
                } else {
                    $("#smartSearchColumnsModal").find("." + fieldName).attr("checked", "checked");
                }
                $("#param-search").val(null).trigger("change");
                $("#param-search-container").removeClass("uk-open");

                updateSelectedButtons();
            }

            function initializeSlider(fieldName, min, max, skipReload, minToSet, maxToSet) {
                var label = fieldName;
                var step = 1, decimals = 0;
                if (fieldName.startsWith("statsType")) {
                    var typeId = parseInt(fieldName.replace("statsType", ""));
                    if (statsType.has(typeId)) {
                        label = statsType.get(typeId).desc;
                    }
                    if (fieldName.includes("P90")) {
                        label += " P90";
                        step = 0.1;
                        decimals = 1;
                    } else if (fieldName.includes("Percentage")) {
                        label += " %";
                        step = 0.1;
                        decimals = 1;
                    }
                } else {
                    if (typeof smartSearchTranslation.get(fieldName) !== "undefined") {
                        label = smartSearchTranslation.get(fieldName);
                    }
                }

                var newElement = "<div class='uk-margin-top-big' id='slider" + fieldName + "'>";
                newElement += "<div><span class='center " + fieldName + "' style='margin-left: 20%'>" + label + "<i class='uk-icon-remove ' style='color: red; margin-left: auto; cursor: pointer' onclick='triggerFilterClick(\"" + fieldName + "\")'></i></div>";
                newElement += "<div class='uk-margin-top-small " + fieldName + "' fieldName='" + fieldName + "' id='" + fieldName + "'/>";
                newElement += "</div>";
                $("#filterContainer").append(newElement);
                var range = document.getElementById(fieldName);

                if (typeof minToSet === 'undefined') {
                    minToSet = min;
                }
                if (typeof maxToSet === 'undefined') {
                    maxToSet = max;
                }

                var customTooltipFormat = true;
                var customRange = {
                    'min': min,
                    'max': max
                };
                if (fieldName === "playerValue") {
                    step = 1000000;
                    customTooltipFormat = [
                        {
                            from: function (value) {
                                return formatNumberAsString(parseInt(value), 2);
                            },
                            to: function (value) {
                                return formatNumberAsString(parseInt(value), 2);
                            }
                        },
                        {
                            from: function (value) {
                                return formatNumberAsString(parseInt(value), 2);
                            },
                            to: function (value) {
                                return formatNumberAsString(parseInt(value), 2);
                            }
                        }
                    ];

                    customRange = {
                        'min': [0, 25000],
                        '30%': [1000000, 1000000],
                        '60%': [30000000, 5000000],
                        '85%': [100000000, 10000000],
                        'max': [max]
                    };
                }
                var slider = noUiSlider.create(range, {
                    range: customRange,
                    step: step,
                    start: [minToSet, maxToSet],
                    // Display colored bars between handles
                    connect: true,
                    // Move handle on tap, bars are draggable
                    behaviour: 'tap-drag',
                    tooltips: customTooltipFormat,
                    format: wNumb({
                        decimals: decimals
                    })
                });
                slider.fieldName = fieldName;
                slider.minValue = min;
                slider.maxValue = max;
                slider.on("end", function (values, handle, unencoded, tap, positions, noUiSlider) {
                    // values: Current slider values (array);
                    // handle: Handle that caused the event (number);
                    // unencoded: Slider values without formatting (array);
                    // tap: Event was caused by the user tapping the slider (boolean);
                    // positions: Left offset of the handles (array);
                    // noUiSlider: slider public Api (noUiSlider);
                    applyFilters();
                });
                sliders.set(fieldName, slider);
                if (typeof skipReload === "undefined" || !skipReload) {
                    applyFilters();
                }
            }

            function applyFilters(loadFilter) {
                var ajaxParams = "params=";
                $("#filterContainer select").each(function () {
                    var filter = eval($(this).attr("filter"));
                    if (filter) {
                        if (ajaxParams !== "params=") {
                            ajaxParams += "-_-";
                        }
                        ajaxParams += filter;
                    }
                });
                var sliderFieldNames = [];
                if (sliders.size > 0) {
                    sliders.forEach(function (value, key) {
                        var fieldName = value.fieldName;
                        var values = value.get("range");

                        if (fieldName && values) {
                            sliderFieldNames.push(fieldName);
                            if (ajaxParams !== "params=") {
                                ajaxParams += "-_-";
                            }
                            ajaxParams += encodeURIComponent(fieldName + ";;;" + values);
                        }
                    });
                }
                ajaxParams += "&columns=";
                if (typeof loadFilter === 'undefined') {
                    if (table.init().columns.length > 1) {
                        var visibleColumns = [];
                        // 1. Metto tutte le colonne che ci sono attualmente nella tabella nell'ordine giusto
                        table.columns().visible().each(function (value, index) {
                            var columnIndex = table.column(index).nodes().to$().attr("column-index");
                            if (typeof columnIndex !== 'undefined') { // escludo la prima colonna
                                var existsOnAdditionalColumns = additionalColumns.indexOf(columnIndex);
                                if (existsOnAdditionalColumns === -1) {
                                    if (value) {
                                        visibleColumns.push(columnIndex);
                                    }
                                }
                            }
                        });
                        visibleColumns = visibleColumns.concat(additionalColumns);
                        if (additionalColumnsToRemove.length > 0) {
                            additionalColumnsToRemove.forEach(function (element) {
                                var indexToRemove = visibleColumns.indexOf(element);
                                if (indexToRemove !== -1) {
                                    visibleColumns.splice(indexToRemove, 1);
                                }
                            });
                        }

                        ajaxParams += visibleColumns;
//                        if (sliderFieldNames.length > 0) {
//                            sliderFieldNames.forEach(function (fieldName) {
//                                if (visibleColumns.indexOf(fieldName) === -1) {
//                                    if (additionalColumnsToRemove.indexOf(fieldName) === -1) {
//                                        if (visibleColumns.length > 0) {
//                                            ajaxParams += ",";
//                                        }
//                                        ajaxParams += fieldName;
//                                    }
//                                }
//                            });
//                        }
                    }
                } else {
                    columns = getFilterColumns(currentFilter);
                    ajaxParams += columns;
                }

                // additionalColumns = [];
                additionalColumnsToRemove = [];

                console.log(ajaxParams);
                tableLastAjaxParams = ajaxParams;
                tableOptions.ajax.url = baseAjaxUrl + tableLastAjaxParams;
                tableOptions.order = table.order();
                lastAjaxAdditionalColumns = [...additionalColumns];
                table.ajax.url(baseAjaxUrl + ajaxParams).load();

                updateCache();
            }

            function getFilter(type, fieldName, element) {
                if (type === 1) {
                    var realFieldName;
                    var value = $(element).val();

                    if (fieldName === "competition") {
                        realFieldName = "competitionName";
                        value = competitionSelect.getValue().map(item => item.value).join("||");
                    } else if (fieldName === "team") {
                        realFieldName = "playerLastTeamName";
                    } else if (fieldName === "country") {
                        realFieldName = "playerCountryName";
                    } else if (fieldName === "role") {
                        realFieldName = "playerPosition";
                    } else if (fieldName === "role.detailed") {
                        realFieldName = "playerPositionDetail";
                    } else if (fieldName === "foot") {
                        realFieldName = "playerFoot";
                    } else if (fieldName === "timeframe") {
                        realFieldName = "timeframe";
                    } else if (fieldName === "genere") {
                        realFieldName = "genere";
                    }

                    if (typeof realFieldName !== "undefined" && value) {
                        return encodeURIComponent(realFieldName + ";;" + value);
                    }
                }
            }

            function manageFilters() {
                $("#smartSearchFiltersModal").addClass("uk-open");
                $("#smartSearchFiltersModal").removeClass("uk-hidden");
            }

            function closeManageFilters() {
                $("#smartSearchFiltersModal").removeClass("uk-open");
                $("#smartSearchFiltersModal").addClass("uk-hidden");
            }

            function manageColumns() {
                $("#smartSearchColumnsModal").addClass("uk-open");
                $("#smartSearchColumnsModal").removeClass("uk-hidden");
            }

            function closeManageColumns() {
                $("#smartSearchColumnsModal").removeClass("uk-open");
                $("#smartSearchColumnsModal").addClass("uk-hidden");
            }

            function manageTables() {
                $("#smartSearchTablesModal").addClass("uk-open");
                $("#smartSearchTablesModal").removeClass("uk-hidden");
            }

            function closeManageTables() {
                $("#smartSearchTablesModal").removeClass("uk-open");
                $("#smartSearchTablesModal").addClass("uk-hidden");
                
                location.reload();
            }

            function createSaveFilter(createNew) {
                var canContinue = true;
//                if (typeof createNew === 'undefined') {
//                    if (typeof currentFilter !== 'undefined' && currentFilter > 0) {
//                        if (window.confirm("<spring:message code="team.stats.update.confirm"/>")) {
//                            canContinue = true;
//                        }
//                    } else {
//                        canContinue = true;
//                    }
//                } else {
//                    canContinue = true;
//                }

                if (canContinue) {
                    var visibleColumns = [];
                    // 1. Metto tutte le colonne che ci sono attualmente nella tabella nell'ordine giusto
//                    table.columns().visible().each(function (value, index) {
//                        var columnIndex = table.column(index).nodes().to$().attr("column-index");
//                        if (typeof columnIndex !== 'undefined') { // escludo la prima colonna
//                            if (value) {
//                                visibleColumns.push(columnIndex);
//                            }
//                        }
//                    });
                    visibleColumns = visibleColumns.concat(additionalColumns);
                    if (additionalColumnsToRemove.length > 0) {
                        additionalColumnsToRemove.forEach(function (element) {
                            var indexToRemove = visibleColumns.indexOf(element);
                            if (indexToRemove !== -1) {
                                visibleColumns.splice(indexToRemove, 1);
                            }
                        });
                    }

                    var sorting = [];
                    table.order().forEach(function (column) {
                        var index = table.column(column[0]).nodes().to$().attr("column-index");
                        if (typeof index !== 'undefined') {
                            sorting.push(index + "|" + column[1]);
                        }
                    });

                    var ajaxData;
                    var filterName;
                    if ($("#smartSearchFilterName").val().length > 0) {
                        ajaxData = encodeURI("&filterId=&tableType=smartSearch&name=" + $("#smartSearchFilterName").val() + "&columns=" + visibleColumns + "&sort=" + sorting + "&isSmartSearch=true");
                        $("#smartSearchFilterName").val("");
                    } else if (typeof createNew === 'undefined' && typeof currentFilter !== 'undefined' && currentFilter > 0) { // aggiorno filtro attuale
                        var counter = 1;
            <c:if test="${mFilters.isEmpty() == false}">
                        counter = ${mFilters.size()} + 1;
            </c:if>
                        ajaxData = encodeURI("&filterId=" + currentFilter + "&tableType=smartSearch&name=" + counter + "&columns=" + visibleColumns + "&sort=" + sorting + "&isSmartSearch=true");
                        filterName = $("#smartSearchFilter option:selected").text();
                    } else {
                        var counter = 1;
            <c:if test="${mFilters.isEmpty() == false}">
                        counter = ${mFilters.size()} + 1;
            </c:if>
                        ajaxData = encodeURI("&filterId=&tableType=smartSearch&name=" + $("#smartSearchFilter option:selected").text() + counter + "&columns=" + visibleColumns + "&sort=" + sorting + "&isSmartSearch=true");
                        filterName = $("#smartSearchFilter option:selected").text() + counter;
                    }

                    $.ajax({
                        type: "POST",
                        url: "/sicstv/user/creaFiltroTeamStats.htm",
                        cache: false,
                        data: ajaxData,
                        success: function (msg) {
                            if (typeof createNew === 'undefined' && typeof currentFilter !== 'undefined' && currentFilter > 0) {
                                UIkit.notify("<spring:message code="team.stats.update.filter.success"/>" + (typeof filterName !== "undefined" ? (" (" + filterName + ")") : ""), {status: 'success', timeout: 1000});
                            } else {
                                UIkit.notify("<spring:message code="team.stats.create.filter.success"/>" + (typeof filterName !== "undefined" ? (" (" + filterName + ")") : ""), {status: 'success', timeout: 1000});
                                currentFilter = parseInt(msg);
                                sessionStorage["smartSearchFilterId"] = currentFilter;
                            }

                            location.reload();
                        },
                        error: function () {
                            if (typeof currentFilter !== 'undefined' && currentFilter > 0) {
                                UIkit.notify("<spring:message code="team.stats.update.filter.error"/>", {status: 'danger', timeout: 1000});
                            } else {
                                UIkit.notify("<spring:message code="team.stats.create.filter.error"/>", {status: 'danger', timeout: 1000});
                            }
                        }
                    });
                }
            }

            function manageRenameFilter(id, confirm) {
                if ($("#smartSearchConfirmRename" + id).length > 0) { // salvo modifica
                    $("#smartSearchConfirmRename" + id).remove();

                    if (confirm) {
                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/rinominaFiltroTeamStats.htm",
                            cache: false,
                            data: encodeURI("&filterId=" + id + "&name=" + $("#smartSearchFilterNameInput" + id).val()),
                            success: function (msg) {
                                if (msg === 'true') {
                                    UIkit.notify("<spring:message code="team.stats.update.success"/>", {status: 'success', timeout: 1000});
                                    currentFilter = id;
                                    sessionStorage["smartSearchFilterId"] = currentFilter;
                                    $("#smartSearchFilterName" + id).html($("#smartSearchFilterNameInput" + id).val());
                                    // location.reload();
                                } else if (msg === 'noPermission') {
                                    UIkit.notify("<spring:message code="team.stats.update.noperm"/>", {status: 'danger', timeout: 1000});
                                } else {
                                    UIkit.notify("<spring:message code="team.stats.update.error"/>", {status: 'danger', timeout: 1000});
                                }
                            },
                            error: function () {
                                UIkit.notify("<spring:message code="team.stats.update.error"/>", {status: 'danger', timeout: 1000});
                            }
                        });
                    }
                    // $("#smartSearchFilterName" + id).html($("#smartSearchFilterNameInput" + id).attr("defaultValue"));
                    $("#smartSearchRenameButton" + id).attr("title", "<spring:message code='playlist.tooltip.rename'/>");
                    $("#smartSearchRenameIcon" + id).attr("class", "uk-icon-pencil");
                } else {
                    var newButton = $('<button id="smartSearchConfirmRename' + id + '" class="uk-icon-hover uk-button uk-button-mini" onclick="manageRenameFilter(' + id + ', true)" title="Rinomina"><i class="uk-icon-check"></i></button>');
                    $("#smartSearchFilterButtons" + id).prepend(newButton);

                    var newInput = $('<input id="smartSearchFilterNameInput' + id + '" class="uk-input" type="text" aria-label="Input" maxlength="32" value="' + ($("#smartSearchFilterName" + id).html().trim()) + '" defaultValue="' + ($("#smartSearchFilterName" + id).html().trim()) + '">');
                    $("#smartSearchFilterName" + id).html(newInput);

                    $("#smartSearchRenameButton" + id).attr("title", "<spring:message code='playlist.tooltip.rename.undo'/>");
                    $("#smartSearchRenameIcon" + id).attr("class", "uk-icon-close");
                }
            }

            function deleteFilter(id) {
                if (window.confirm("<spring:message code="team.stats.delete.question"/>")) {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/cancellaFiltroTeamStats.htm",
                        cache: false,
                        data: encodeURI("&filterId=" + id),
                        success: function (msg) {
                            if (msg === 'true') {
                                UIkit.notify("<spring:message code="team.stats.delete.success"/>", {status: 'success', timeout: 1000});
                                currentFilter = void 0; // undefined
                                sessionStorage.removeItem("smartSearchFilterId");
                                $("#smartSearchFilterName" + id).parent().remove();
                                // location.reload();
                            } else if (msg === 'noPermission') {
                                UIkit.notify("<spring:message code="team.stats.delete.noperm"/>", {status: 'danger', timeout: 1000});
                            } else {
                                UIkit.notify("<spring:message code="team.stats.delete.error"/>", {status: 'danger', timeout: 1000});
                            }
                        },
                        error: function () {
                            UIkit.notify("<spring:message code="team.stats.delete.error"/>", {status: 'danger', timeout: 1000});
                        }
                    });
                }
            }

            function getFilterColumns(id) {
                currentFilter = id;

                var orderColumns = [];
                var sortedColumns = [];
                var validColumns = [];
                var index = 0;
            <c:forEach var="filterItem" items="${mFilters.keySet()}" varStatus="status">
                if (${filterItem.id} === id) {
                <c:forEach var="item" items="${mFilters.get(filterItem)}">
                    validColumns.push('${item.columnName}'); // inserisco le colonne visibili
                    orderColumns.push(['${item.columnName}', ${item.order}]);
                    <c:if test="${item.sort != null}">
                    if (${item.sort}) { // controllo per sorting
                        sortedColumns.push(['${item.columnName}', "asc"]);
                    } else {
                        sortedColumns.push(['${item.columnName}', "desc"]);
                    }
                    </c:if>
                    index++;
                </c:forEach>
                }
            </c:forEach>
                if (validColumns.length > 0) {
                    return validColumns;
                } else {
                    return [];
                }
            }

            function changeFilter(element) {
                currentFilter = parseInt($("#" + element.id + " option:selected").val());
                applyFilters(true);

                // per i tasti attivati
                additionalColumns = [];
                // tolgo tutti i check altrimenti le colonne in più degli altri filtri rimangono selezionate
                $("#smartSearchColumnsModal").find("input:checked").removeAttr("checked");
                if (typeof currentFilter !== "undefined") {
                    var columnsToCheck = getFilterColumns(currentFilter);
                    columnsToCheck.forEach(function (element) {
                        manageColumnCheck(element, true);
                        additionalColumns.push(element);
                    });
                }
                updateSelectedButtons();
            }

            var isCompareMode;
            function manageCompare() {
                isCompareMode = !isCompareMode;
                sessionStorage["smartSearchCompareMode"] = isCompareMode;
                if (typeof isCompareMode === "undefined") {
                    isCompareMode = false;
                }

                if (isCompareMode) {
                    $(".smartSearchGrouped").attr("checked", "checked");
                    $("#compareContainer").removeClass("uk-hidden");
                    $("#tableContainer").addClass("uk-hidden");
                } else {
                    $(".showMeIfNeeded").removeClass("uk-hidden");
                    $(".smartSearchGrouped").removeAttr("checked");
                    $("#compareContainer").addClass("uk-hidden");
                    $("#tableContainer").removeClass("uk-hidden");
                }

                // carico tutte le immagini, c'è un bug per il quale passando da visible a non visible non carica le immagini
                $("img.lazy").lazyload({
                    placeholder: "/sicstv/images/user_gray.png"
                }).on("error", function () {
                    // Questo codice verrà eseguito se l'immagine non potrà essere caricata
                    $(this).attr("src", "/sicstv/images/user_gray.png");
                });
            }

            function manageEnableCompareColors() {
                enableColors = !enableColors;
                reloadCompare();
            }

            var enableColors = true;
            function reloadCompare() {
                var table = '<table id="compareTable" class="uk-table uk-table-hover uk-table-striped stripe row-border order-column" style="border-collapse: collapse; max-height: 95%">';

                var correctTableData = [...tableData];
                if (selectedPlayerIds.length > 0) {
                    // se ci sono X giocatori selezionati mostro solo quelli
                    var adjustedCorrectTableData = [];
                    correctTableData.forEach(function (element) {
                        var playerId = $(element[0]).find("input").attr("playerId");
                        if (typeof playerId !== "undefined" && playerId) {
                            playerId = parseInt(playerId);
                            if (selectedPlayerIds.includes(playerId)) {
                                adjustedCorrectTableData.push(element);
                            }
                        }
                    });

                    correctTableData = adjustedCorrectTableData;
                }

                // tolgo il primo valore di ogni array (ovvero la checkbox)
                correctTableData = correctTableData.map(item => {
                    const [, ...rest] = item; // Ignora il primo elemento e prendi il resto
                    return rest;
                });

                // 1. thead
                table += '<thead><tr><th></th>';
                correctTableData.forEach(function (element, columnIndex) {
                    var content = getPlayerLink(columnIndex).replace("{{name}}", element[0].replaceAll("'40px'", "'80px'"));
                    table += '<th class="center-text middle-text" style="min-width: 80px !important">' + content + '</th>';
                });
                table += '</tr></thead><tbody>';

                // 2. tbody
                var index = 0;
                tableColumns.forEach(function (column) {
                    // skippo l'index 0 perchè è quello usato per il thead
                    if (index > 0) {
                        var title = column.title;
                        if (typeof tableSort !== "undefined") {
                            if (tableSort[0] && tableSort[0][0]) {
                                if (tableSort[0][0] === (index + 1)) {
                                    if (tableSort[0][1] === "desc") {
                                        title = "<i class='uk-icon uk-icon-arrow-down uk-margin-right-small'/>" + title;
                                    } else {
                                        title = "<i class='uk-icon uk-icon-arrow-up uk-margin-right-small'/>" + title;
                                    }
                                }
                            }
                        }
                        table += '<tr><td>' + title + '</td>';
                        var isNumericColumn = false, colorScale, minValue, maxValue;
                        correctTableData.forEach(function (element, columnIndex) {
                            var elementValue = element[index].replace(',', '.');
                            var elementLabel = elementValue;
                            if (elementValue === '-') {
                                elementValue = 0;
                            }
                            if (typeof enableColors !== 'undefined' && enableColors) {
                                //elementValue = parseFloat(elementValue);

                                if (!isNumericColumn && !isNaN(parseFloat(element[index].replace(',', '.')))) {
                                    isNumericColumn = true;
                                    correctTableData.forEach(function (tmpElement) {
                                        var value = tmpElement[index].replace(',', '.');
                                        if (value === '-') {
                                            value = 0;
                                        } else {
                                            value = parseFloat(value);
                                        }

                                        if (typeof minValue === 'undefined' || value < minValue) {
                                            minValue = value;
                                        }
                                        if (typeof maxValue === 'undefined' || value > maxValue) {
                                            maxValue = value;
                                        }
                                    });

//                                console.log(column, minValue, maxValue);
                                    if (minValue === maxValue) {
                                        colorScale = chroma.scale(['#ffffff00', '#ffffff00']).domain([minValue, maxValue]);
                                    } else {
                                        // colorScale = chroma.scale(['#ff000055', '#ff000011', '#ffffff00', '#ffffff00', '00ff0055']).domain([minValue, maxValue]);
                                        if (typeof oppositeColumns.get(column.title) !== "undefined") {
                                            colorScale = chroma.scale(['00ff00aa', '#ffff00aa', '#ff0000aa']).domain([minValue, maxValue]);
                                        } else {
                                            colorScale = chroma.scale(['#ff0000aa', '#ffff00aa', '00ff00aa']).domain([minValue, maxValue]);
                                        }
                                    }
                                }
                                if (isNumericColumn) {
//                                table += '<td><i class="uk-icon-medium uk-icon-dot-circle-o" style="color: ' + colorScale(elementValue).css() + '"></i></td>';
                                    // table += '<td><div class="center imgPlayerLogo"><div style="width: 50px; background-clip: content-box; background-color: ' + colorScale(elementValue).css() + '; object-fit: contain; filter: drop-shadow(0 0 5px #adadad) drop-shadow(0 0 5px #adadad)">' + elementLabel + '</div></div></td>';
                                    var tmpColor = colorScale(elementValue).rgb();
                                    table += '<td><div class="center"><div style="width: 45px; border-radius: 25px; padding-top: 3px; padding-bottom: 3px; background: radial-gradient(at center, rgba(' + tmpColor + ',0.65), rgba(' + tmpColor + ',0.5)); object-fit: contain; filter: drop-shadow(0 0 5px rgba(' + tmpColor + ',0.2)) drop-shadow(0 0 5px rgba(' + tmpColor + ',0.2));"><span style="color: ' + getColoreInContrasto(tmpColor) + '">' + elementLabel + '</span></div></div></td>';
                                } else {
                                    if (index === 1) {
                                        // giocatore, metto link cliccabile
                                        table += '<td>' + getPlayerLink(columnIndex).replace("{{name}}", elementLabel) + '</td>';
                                    } else if (index === 2) {
                                        // team, metto link cliccabile
                                        var teamLink = getTeamLink(columnIndex);
                                        if (teamLink !== null) {
                                            table += '<td>' + teamLink.replace("{{name}}", elementLabel) + '</td>';
                                        } else {
                                            table += '<td>' + elementLabel + '</td>';
                                        }
                                    } else {
                                        table += '<td>' + elementLabel + '</td>';
                                    }
                                }
                            } else {
                                table += '<td>' + elementLabel + '</td>';
                            }
                        });
                        table += '</tr>';
                    }
                    index++;
                });

                if ($.fn.DataTable.isDataTable('#compareTable')) {
                    tableCompare.clear().destroy(true);
                }

                $("#compareContent").empty(); // non dovrebbe fare nulla
                $("#compareContent").append('<div class="center uk-margin-left-small uk-float-left"><span class="uk-margin-right-small"><spring:message code='menu.user.compare.mode'/></span><label class="uk-switch" for="smartSearchGrouped"><input type="checkbox" class="smartSearchGrouped" id="smartSearchGrouped" onchange="manageCompare();" ' + (isCompareMode ? "checked" : "") + '><div class="uk-switch-slider"></div></label></div>');
                $("#compareContent").append('<div class="center uk-margin-left-small uk-float-left"><span class="uk-margin-right-small"><spring:message code='smart.search.show.outliers'/></span><label class="uk-switch" for="smartSearchOutliers"><input type="checkbox" class="smartSearchOutliers" id="smartSearchOutliers" onchange="manageEnableCompareColors();" ' + (enableColors ? "checked" : "") + '><div class="uk-switch-slider"></div></label></div>');
                $("#compareContent").append(table);
                tableCompare = $('#compareTable').DataTable({
                    ordering: false,
                    dom: 'Bfrtip',
                    searching: false,
                    paging: false,
                    info: false,
                    buttons: [
                        {
                            text: '<spring:message code='team.stats.esporta'/>',
                            attr: {
                                class: 'uk-button'
                            },
                            extend: 'collection',
                            buttons: [
                                {
                                    extend: 'print',
                                    exportOptions: {
                                        columns: ':visible'
                                    },
                                    title: 'SICS.tv-SmartSearch'
                                }, {
                                    extend: 'excel',
                                    exportOptions: {
                                        columns: ':visible'
                                    },
                                    title: 'SICS.tv-SmartSearch'
                                }
                            ]
                        }
                    ],
                    drawCallback: function () {
                        // rinvio il caricamento delle immagini
                        // per velocizzare la visualizzazione della tabella
                        $("img.lazy").lazyload({
                            placeholder: "/sicstv/images/user_gray.png"
                        }).on("error", function () {
                            // Questo codice verrà eseguito se l'immagine non potrà essere caricata
                            $(this).attr("src", "/sicstv/images/user_gray.png");
                        });
                    },
                    createdRow: function (row, data, dataIndex) {
                        // 'data' contiene l'oggetto JSON per la riga corrente
                        // metto tutti i dati in centro
                        $(row).find("td:not(:first-child)").attr('class', "center-text middle-text");
                    }
                });
            }

            function updateSelectValues(fieldName, reloadTable) {
//                if (fieldName === "role") {
//                    var counter = $("#filterContainer select." + fieldName).find("option:selected").attr("counter");
//                    if (typeof counter === "undefined") {
//                        // tolgo il filtro
//                        $("#filterContainer select." + fieldName).find("option").removeClass("uk-hidden");
//                    } else {
//                        if (counter === "0") {
//                            // portiere
//                            $("#filterContainer select[fieldname='role.detailed']").find("option:not([counter='7'])").addClass("uk-hidden");
//                        } else if (counter === "2") {
//                            // difensore
//                            
//                        }
//                    }
//                }

                var validFilters = ["competition", "team", "genere", "timeframe", "role"];
                var allowedFilters = [];
                if (validFilters.includes(fieldName)) {
                    if (fieldName === "competition") {
                        allowedFilters = ["competition", "genere", "timeframe"];
                    } else if (fieldName === "team") {
                        allowedFilters = ["team", "competition", "genere", "timeframe"];
                    } else if (fieldName === "genere" || fieldName === "timeframe") {
                        allowedFilters = ["genere", "timeframe"];
                    } else if (fieldName === "role") {
                        allowedFilters = ["genere", "timeframe", "role"];
                    }

                    var ajaxParams = "params=";
                    $("#filterContainer select").each(function (index, element) {
                        var filter = eval($(element).attr("filter"));
                        var elementFieldName = $(element).attr("fieldname");

                        if (allowedFilters.includes(elementFieldName)) {
                            if (filter && !(fieldName === "competition" && elementFieldName === "team")) {
                                // se sto caricando partendo da competition, skippo il filtro per team
                                // altrimenti nella query risulta che sto caricando partendo da team
                                if (ajaxParams !== "params=") {
                                    ajaxParams += "-_-";
                                }
                                ajaxParams += filter;
                            }
                        }
                    });

                    if (ajaxParams) {
                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/smartSearchValues.htm?" + ajaxParams,
                            cache: false,
                            success: function (values) {
                                if (values) {
                                    values = eval(values);
                                    values.forEach(function (element) {
                                        if (element) {
                                            var previousValue = $("#filterContainer select[fieldname='" + element.fieldName + "']").val();
                                            // tolgo le opzioni attuali
                                            $("#filterContainer select[fieldname='" + element.fieldName + "'] option").remove();
                                            // rimetto la selezione del "tutti", ovvero la opzione vuota
                                            $("#filterContainer select[fieldname='" + element.fieldName + "']").append($('<option>', {
                                                value: "",
                                                text: ""
                                            }));
                                            var counter = 0;
                                            element.fieldValues.forEach(function (arrayElement) {
                                                $("#filterContainer select[fieldname='" + element.fieldName + "']").append($('<option>', {
                                                    counter: counter,
                                                    value: arrayElement,
                                                    text: arrayElement.toUpperCase()
                                                }));
                                                counter++;
                                            });

                                            if (previousValue) {
                                                if ($("#filterContainer select[fieldname='" + element.fieldName + "']" + " option[value='" + previousValue + "'").length > 0) {
                                                    $("#filterContainer select[fieldname='" + element.fieldName + "']").val(previousValue);
                                                } else {
                                                    $("#filterContainer select[fieldname='" + element.fieldName + "']").val("");
                                                }
                                            }
                                        }
                                    });
                                }

                                if (typeof reloadTable !== "undefined" && reloadTable) {
                                    reloadSliders();
                                    applyFilters();
                                }
                            }
                        });
                    }
                } else { // per gli altri filtri aggiorno sempre
                    applyFilters();
                }
            }

            function updateSelectedButtons() {
                // ogni volta riparto da 0 per evitare di dover gestire tutto
                $(".uk-modal-dialog button.uk-button-active").removeClass("uk-button-active");
                $(".uk-accordion-content").find("button").find("i:not(.skip-reload)").removeClass("uk-icon-caret-down").addClass("uk-icon-caret-right");

                // ora attivo quelli che hanno almeno un input con il checked
                $(".uk-modal-dialog input").each(function (index, element) {
                    if ($(element).prop("checked")) {
                        $(element).closest("div.width-1-4-x").find("button").addClass("uk-button-active");
                    }
                });

                // gestione icona "aperto" e "chiuso"
                $(".uk-accordion-content").find("button.uk-active").find("i.uk-icon-caret-right:not(.skip-reload)").removeClass("uk-icon-caret-right").addClass("uk-icon-caret-down");
            }

            function triggerFilterClick(fieldName) {
                var isChecked = $("#smartSearchFiltersButtonContent input." + fieldName).first().attr("checked");
                if (typeof isChecked === "undefined") {
                    $("." + fieldName).attr("checked", "checked");
                } else {
                    $("#smartSearchFiltersModal").find("." + fieldName).removeAttr("checked");
                }

                $("#smartSearchFiltersModal").find("." + fieldName).first().click();
                if (typeof isChecked === "undefined") {
                    $("#smartSearchFiltersModal").find("." + fieldName).first().attr("checked", "checked");
                } else {
                    $("#smartSearchFiltersModal").find("." + fieldName).first().removeAttr("checked");
                }
            }

            function updateCache() {
                var slidersCache = "";
                sliders.forEach(function (value, key) {
                    if (slidersCache !== "") {
                        slidersCache += "&";
                    }
                    slidersCache += key + ";" + value.options.range.min + ";" + value.options.range.max + ";" + value.get("range")[0] + ";" + value.get("range")[1];
                });
                sessionStorage["smartSearchSliders"] = slidersCache;
                sessionStorage["smartSearchFilterId"] = currentFilter;
                sessionStorage["smartSearchTablePage"] = table.page();
                sessionStorage["smartSearchTablePageLen"] = table.page.len();
                localStorage["tablePageLen"] = table.page.len();

                $("#filterContainer select").each(function (index, element) {
                    var fieldName = $(element).attr("fieldName");
                    var value = $(element).val();

                    if (fieldName === "competition") {
                        value = competitionSelect.getValue().map(item => item.value).join("||");
                    }

                    if (value) {
                        sessionStorage["smartSearchFilter" + fieldName] = value;
                    } else {
                        sessionStorage.removeItem("smartSearchFilter" + fieldName);
                    }
                });
            }

            function deleteCache() {
                sessionStorage.removeItem("smartSearchSliders");
                sessionStorage.removeItem("smartSearchFilterId");
                sessionStorage.removeItem("smartSearchTablePage");
                sessionStorage.removeItem("smartSearchTablePageLen");
                sessionStorage.removeItem("smartSearchTableSort");

                $("#filterContainer select").each(function (index, element) {
                    var fieldName = $(element).attr("fieldName");
                    sessionStorage.removeItem("smartSearchFilter" + fieldName);
                });

                location.reload();
            }

            function manageGroupStats(element) {
                event.preventDefault();
                event.stopPropagation();

                var specificCheck = false;
                if ($(element).attr("class").includes("uk-icon-square-o")) {
                    specificCheck = true;
                    $(element).attr("class", $(element).attr("class").replace("uk-icon-square-o", "uk-icon-check-square-o"));
                } else {
                    $(element).attr("class", $(element).attr("class").replace("uk-icon-check-square-o", "uk-icon-square-o"));
                }

                $(element).parent().parent().find("input").each(function (index, input) {
                    var typeId = $(input).attr("data-value");
                    manageColumn(typeId, specificCheck);
                });

                updateSelectedButtons();
            }

            var selectedPlayerIds = [];
            function managePlayerCheck() {
                var playerId = $(event.target).attr("playerid");
                if (typeof playerId !== "undefined") {
                    playerId = parseInt(playerId);
                    var alreadyExists = selectedPlayerIds.indexOf(playerId);
                    if (alreadyExists === -1) {
                        // se non esiste aggiungo
                        selectedPlayerIds.push(playerId);
                    } else {
                        selectedPlayerIds.splice(alreadyExists, 1);
                    }
                }

                updateSelectedPlayerIds();
            }

            function updateSelectedPlayerIds() {
                $(".playerCheckbox").removeAttr("checked");

                selectedPlayerIds.forEach(function (playerId) {
                    $("input[playerid='" + playerId + "']").attr("checked", "checked");
                });

                if (selectedPlayerIds.length > 0) {
                    $("#addToWatchlist").removeClass("uk-hidden");
                    $("#addToWatchlist").addClass("uk-button-confirm");
                    $("#addToWatchlistAmount").text(selectedPlayerIds.length);

                    // se ci sono X giocatori selezionati mostro solo quelli
                    // ho detto a Michele che in questo modo bisogna togliere ogni volta
                    // tutti i giocatori selezionati quando cambio pagina / tocco la tabella
                    reloadCompare();
                } else {
                    $("#addToWatchlist").addClass("uk-hidden");
                }
            }

            function openWatchlistModal() {
                $("#watchlistModal").addClass("uk-open");
                $("#watchlistModal").removeClass("uk-hidden");
            }

            function closeWatchlistModal() {
                $("#watchlistModal").removeClass("uk-open");
                $("#watchlistModal").addClass("uk-hidden");
            }

            // (!) LA STESSA FUNZIONE C'E' ANCHE NELLA PAGINA mywatchlist.jsp (!)
            function addToWatchlist(watchlistId) {
                var name = $('#nameWatchlist').val();
                if (name === "" && typeof watchlistId === 'undefined') {
                    UIkit.notify("<spring:message code='watchlist.add.failed.no.name'/>", {status: 'warning', timeout: 1000});
                    return;
                }

                var playerIds = "";
                selectedPlayerIds.forEach(function (playerId) {
                    if (playerIds.length > 0) {
                        playerIds += ",";
                    }
                    playerIds += playerId;
                });

                if (playerIds) {
                    $.ajax({
                        type: "POST",
                        url: "/sicstv/user/creaWatchlist.htm",
                        data: encodeURI("name=" + name + "&playerId=&playerIds=" + playerIds + "&watchlistId=" + (typeof watchlistId !== 'undefined' ? watchlistId : '')),
                        cache: false,
                        success: function (result) {
                            if (result === 'ok') {
                                if (typeof watchlistId === 'undefined') {
                                    UIkit.notify("<spring:message code='watchlist.add.success'/>", {status: 'success', timeout: 1000});
                                } else {
                                    UIkit.notify("<spring:message code='watchlist.update.success'/>", {status: 'success', timeout: 1000});
                                }

                                selectedPlayerIds = [];
                                updateSelectedPlayerIds();
                            } else {
                                if (typeof watchlistId === 'undefined') {
                                    UIkit.notify("<spring:message code='watchlist.add.failed'/>", {status: 'warning', timeout: 1000});
                                } else {
                                    UIkit.notify("<spring:message code='watchlist.update.failed'/>", {status: 'warning', timeout: 1000});
                                }
                            }
                        }
                    });
                } else {
                    console.warn("No player selected. can't continue");
                }
            }

            // (!) LA STESSA FUNZIONE C'E' ANCHE NELLA PAGINA mywatchlist.jsp (!)
            function removeFromWatchlist(watchlistId) {
                var name = $('#nameWatchlist').val();
                if (name === "" && typeof watchlistId === 'undefined') {
                    UIkit.notify("<spring:message code='watchlist.add.failed.no.name'/>", {status: 'warning', timeout: 1000});
                    return;
                }

                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/rimuoviPlayerDaWatchlist.htm",
                    data: encodeURI("playerId=&playerIds=" + playerIds + "&watchlistId=" + (typeof watchlistId !== 'undefined' ? watchlistId : '')),
                    cache: false,
                    success: function (result) {
                        if (result === 'ok') {
                            UIkit.notify("<spring:message code='watchlist.update.success'/>", {status: 'success', timeout: 1000});
                            window.location.reload();
                        } else {
                            UIkit.notify("<spring:message code='watchlist.update.failed'/>", {status: 'warning', timeout: 1000});
                        }
                    }
                });
            }

            function searchParams() {
                var input = $("#param-search").val().toLowerCase();
                if (input && input.length >= 2) {
                    $("#param-search-result-container").empty();

                    var valid = [];
                    $("#smartSearchColumnsButton").find(".smartSearch-param").each(function (index, element) {
                        var metric = $(element).text().trim();
                        if (metric.toLowerCase().includes(input)) {
                            var validText = metric + "|" + $(element).attr("stats-id");
                            if (!valid.includes(validText)) {
                                valid.push(validText);
                            }
                        }
                    });

                    if (valid.length > 0) {
                        valid.forEach(function (element) {
                            var splitted = element.split("|");

                            var newElement = "<li class='uk-hover'>";
                            newElement += "<a onclick='manageColumn(\"statsType" + splitted[1] + "\", true);'>" + splitted[0] + "</a>";
                            newElement += "</li>";
                            $("#param-search-result-container").append(newElement);
                        });

                        $("#param-search-container").addClass("uk-open");
                    }
                } else {
                    $("#param-search-container").removeClass("uk-open");
                }
            }

            function formatNumberAsString(num, digits) {
                const lookup = [
                    {value: 1, symbol: ""},
                    {value: 1e3, symbol: "k"},
                    {value: 1e6, symbol: "M"},
                    {value: 1e9, symbol: "G"},
                    {value: 1e12, symbol: "T"},
                    {value: 1e15, symbol: "P"},
                    {value: 1e18, symbol: "E"}
                ];
                const regexp = /\.0+$|(?<=\.[0-9]*[1-9])0+$/;
                const item = lookup.findLast(item => num >= item.value);
                return item ? (num / item.value).toFixed(digits).replace(regexp, "").concat(item.symbol) : "0";
            }
        </script>
    </head>
    <body id="smartSearchPlayerBody">
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>

        <div id="breadcrumb">
            <ul class="uk-breadcrumb uk-margin-left">
                <li style="text-transform:uppercase;" id="liBreadcrumb">Smart Search <i id="showCurrentFilter"></i></li>
            </ul>
        </div>

        <div class="uk-flex">
            <div class="uk-width-1-6 uk-padding-left uk-padding-top uk-padding-right" id="filterContainer" style="background-color: #b0b0b033; min-height: calc(100dvh - 51px); border-right: 1px solid gray">
                <div class="uk-flex">
                    <h3 class="uk-text-bold uk-margin-bottom-remove uk-text-uppercase"><spring:message code='menu.filters'/></h3>
                    <button class="uk-button uk-button-small" style="margin-left: auto" onclick="manageFilters();">+</button>
                </div>
                <c:if test="${mBaseValues != null}">
                    <c:forEach var="filter" items="${mBaseValues}">
                        <div class="uk-margin-top">
                            <span class="uk-form">
                                <span class="center"><spring:message code='smart.search.${filter.fieldName}'/>:</span>
                                <select class="uk-width-10-10 ${filter.fieldName} uk-text-uppercase"
                                        fieldName="${filter.fieldName}"
                                        filter="getFilter(1, '${filter.fieldName}', this)"
                                        onchange="updateSelectValues('${filter.fieldName}', true);"
                                        <c:if test="${filter.multipleChoice != null && filter.multipleChoice}">multiple="multiple"</c:if>>
                                    <c:if test="${!filter.fieldName.equals('timeframe') && !filter.fieldName.equals('genere')}">
                                        <c:if test="${!filter.fieldName.equals('competition')}">
                                        <option value=""></option>
                                        </c:if>
                                        <c:set var="counter" value="${0}"/>
                                        <c:forEach var="item" items="${filter.fieldValues}">
                                            <option counter="${counter}" value="${item}"
                                                    class="uk-text-uppercase">${item}</option>
                                            <c:set var="counter" value="${counter + 1}"/>
                                        </c:forEach>
                                    </c:if>
                                    <c:if test="${filter.fieldName.equals('timeframe') || filter.fieldName.equals('genere')}">
                                        <c:forEach var="item" items="${filter.fieldValues}">
                                            <option value="${filter.fieldValues.indexOf(item)}">${item.toUpperCase()}</option>
                                        </c:forEach>
                                    </c:if>
                                </select>
                            </span>
                        </div>
                    </c:forEach>
                </c:if>
            </div>
            <div class="uk-width-5-6 uk-margin-top uk-hidden" id="compareContainer">
                <div id="compareContent"></div>
            </div>
            <div class="uk-width-5-6 uk-margin-top uk-hidden" id="tableContainer">
                <table id="smartSearch" class="uk-table uk-table-hover row-border order-column" style="border-collapse: collapse; margin-left: 0px; max-height: 95%"></table>
            </div>
        </div>

        <div id="smartSearchFiltersModal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeManageFilters();">&times;</span>
                    <h3><spring:message code="smart.search.manage.filters"/></h3>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <div id="smartSearchFiltersColumnsButton" style="height: 50vh; overflow-y: auto">
                            <!-- viene inizializzato tramite Javascript funzione manageColumnsModal() -->
                            <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-float-left" id="smartSearchFiltersColumnsButtonul" data-uk-tab="{connect:'#smartSearchFiltersButtonContent'}" style="margin-bottom: 10px">
                                <c:set var="isFirst" value="true"/>
                                <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                                    <li><a style="border-top: 2px solid #dddddd;">${statGroup}</a></li>
                                        <c:set var="isFirst" value="false"/>
                                    </c:forEach>
                            </ul>

                            <ul id="smartSearchFiltersButtonContent" class="uk-switcher">
                                <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                                    <li>
                                        <div uk-accordion data-uk-accordion="{collapse: false, showfirst: true}" class="uk-margin-remove">
                                            <span class="uk-accordion-title uk-hidden"></span>
                                            <div class="uk-accordion-content uk-width-1-1 uk-padding-remove">
                                                <div class="uk-accordion uk-text-left">
                                                    <c:forEach var="group" items="${mGroupedColumns.get(statGroup).keySet()}">
                                                        <div class="width-1-4-x">
                                                            <button class="uk-button uk-accordion-title buttonTag" base-name="${group}" value="" title="${group}">
                                                                <i class="uk-float-left uk-button-mini uk-icon uk-icon-caret-down uk-active"></i>
                                                                ${group}
                                                            </button>
                                                            <div>
                                                                <div class="uk-accordion-content uk-text-left">
                                                                    <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                        <div class="uk-margin-small">
                                                                            <div class="uk-flex">
                                                                                <div style="width: 70%"></div>
                                                                                <div class="uk-margin-right-small" style="width: 10%">Tot.</div>
                                                                                <div class="uk-margin-right-small" style="width: 10%">P90</div>
                                                                                <div class="uk-margin-right-small" style="width: 10%">%</div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <c:forEach var="stat" items="${mGroupedColumns.get(statGroup).get(group)}">
                                                                        <c:if test="${stat.id == -1}">
                                                                            <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                                <div class="uk-margin-small">
                                                                                    <div class="uk-flex">
                                                                                        <div style="width: 70%">
                                                                                            ${stat.desc}
                                                                                        </div>
                                                                                        <div class="uk-margin-right-small" style="width: 10%">
                                                                                            <input type="checkbox" class="${stat.code}" onclick="manageSlider('${stat.code}', this);"/>
                                                                                        </div>
                                                                                        <div class="uk-margin-right-small" style="width: 10%"></div>
                                                                                        <div class="uk-margin-right-small" style="width: 10%"></div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </c:if>
                                                                        <c:if test="${stat.id != -1}">
                                                                            <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                                <div class="uk-margin-small">
                                                                                    <div class="uk-flex">
                                                                                        <div style="width: 70%">
                                                                                            ${stat.desc}
                                                                                        </div>
                                                                                        <div class="uk-margin-right-small" style="width: 10%">
                                                                                            <input type="checkbox" class="statsType${stat.id}" onclick="manageSlider('statsType${stat.id}', this);"/>
                                                                                        </div>
                                                                                        <div class="uk-margin-right-small" style="width: 10%">
                                                                                            <c:if test="${stat.id != 1}">
                                                                                                <input type="checkbox" class="statsType${stat.id}P90" onclick="manageSlider('statsType${stat.id}P90', this);"/>
                                                                                            </c:if>
                                                                                        </div>
                                                                                        <div class="uk-margin-right-small" style="width: 10%">
                                                                                            <c:if test="${stat.avgTypeId != null}">
                                                                                                <input type="checkbox" class="statsType${stat.id}Percentage" onclick="manageSlider('statsType${stat.id}Percentage', this);"/>
                                                                                            </c:if>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </c:if>
                                                                    </c:forEach>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </c:forEach>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                </c:forEach>
                            </ul>
                        </div>
                    </div>
                    <div class="uk-modal-footer uk-width-1-1" style="display: flex">
                        <div class="uk-text-right" style="margin-left: auto">
                            <button style="min-width:80px;" onclick="deleteCache();" class="uk-text-large uk-button uk-modal-close"><spring:message code="video.resetfiltri"/></button>
                            <button style="min-width:80px;" onclick="closeManageFilters();
                                    createSaveFilter();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.conferma"/></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="smartSearchColumnsModal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 55%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeManageColumns();">&times;</span>
                    <h3><spring:message code="smart.search.manage.columns"/></h3>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <div id="smartSearchColumnsButton" style="height: 50vh; overflow-y: auto">
                            <!-- viene inizializzato tramite Javascript funzione manageColumnsModal() -->
                            <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-float-left" id="smartSearchColumnsButtonul" data-uk-tab="{connect:'#smartSearchColumnsButtonContent'}" style="margin-bottom: 10px">
                                <c:set var="isFirst" value="true"/>
                                <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                                    <li><a style="border-top: 2px solid #dddddd;">${statGroup}</a></li>
                                        <c:set var="isFirst" value="false"/>
                                    </c:forEach>
                            </ul>
                            <div class="uk-float-right uk-autocomplete uk-form" id="param-search-container">
                                <label><spring:message code="video.ricerca"/>:</label>
                                <input type="text" id="param-search" oninput="searchParams();" class="uk-form-width-medium"/>
                                <div class="uk-dropdown" aria-expanded="true">
                                    <ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left" id="param-search-result-container">
                                    </ul>
                                </div>
                            </div>

                            <ul id="smartSearchColumnsButtonContent" class="uk-switcher">
                                <c:set var="counter" value="0"/>
                                <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                                    <li>
                                        <div id="accordition${counter}" uk-accordion data-uk-accordion="{collapse: false, showfirst: true}" class="uk-margin-remove accordionDiv">
                                            <span class="uk-accordion-title uk-hidden"></span>
                                            <div class="uk-accordion-content uk-width-1-1 uk-padding-remove">
                                                <div class="uk-accordion uk-text-left">
                                                    <c:forEach var="group" items="${mGroupedColumns.get(statGroup).keySet()}">
                                                        <div class="width-1-4-x">
                                                            <button class="uk-button uk-accordion-title buttonTag" base-name="${group}" value="" title="${group}">
                                                                <i class="uk-float-left uk-button-mini uk-icon uk-icon-caret-down uk-active"></i>
                                                                ${group}
                                                                <i class="uk-float-right uk-button-mini uk-icon-square-o skip-reload" onclick="manageGroupStats(this);"></i>
                                                            </button>
                                                            <div>
                                                                <div class="uk-accordion-content uk-text-left">
                                                                    <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                        <div class="uk-margin-small">
                                                                            <div class="uk-flex">
                                                                                <div style="width: 70%"></div>
                                                                                <div class="uk-margin-right-small" style="width: 10%">Tot.</div>
                                                                                <div class="uk-margin-right-small" style="width: 10%">P90</div>
                                                                                <div class="uk-margin-right-small" style="width: 10%">%</div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <c:forEach var="stat" items="${mGroupedColumns.get(statGroup).get(group)}">
                                                                        <c:if test="${!stat.code.equals('playerAge') && !stat.code.equals('playerValue')}">
                                                                            <c:if test="${stat.id == -1}">
                                                                                <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                                    <div class="uk-margin-small">
                                                                                        <div class="uk-flex">
                                                                                            <div style="width: 70%">
                                                                                                ${stat.desc}
                                                                                            </div>
                                                                                            <div class="uk-margin-right-small" style="width: 10%">
                                                                                                <input type="checkbox" class="${stat.code}" onclick="manageColumn('${stat.code}');" data-value="${stat.code}"/>
                                                                                            </div>
                                                                                            <div class="uk-margin-right-small" style="width: 10%"></div>
                                                                                            <div class="uk-margin-right-small" style="width: 10%"></div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </c:if>
                                                                            <c:if test="${stat.id != -1}">
                                                                                <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                                    <div class="uk-margin-small">
                                                                                        <div class="uk-flex">
                                                                                            <div class="smartSearch-param" style="width: 70%" stats-id="${stat.id}">
                                                                                                ${stat.desc}
                                                                                            </div>
                                                                                            <div class="uk-margin-right-small" style="width: 10%">
                                                                                                <input type="checkbox" class="statsType${stat.id}" onclick="manageColumn('statsType${stat.id}');" data-value="statsType${stat.id}"/>
                                                                                            </div>
                                                                                            <div class="uk-margin-right-small" style="width: 10%">
                                                                                                <c:if test="${stat.id != 1}">
                                                                                                    <input type="checkbox" class="statsType${stat.id}P90" onclick="manageColumn('statsType${stat.id}P90');" data-value="statsType${stat.id}P90"/>
                                                                                                </c:if>
                                                                                            </div>
                                                                                            <div class="uk-margin-right-small" style="width: 10%">
                                                                                                <c:if test="${stat.avgTypeId != null}">
                                                                                                    <input type="checkbox" class="statsType${stat.id}Percentage" onclick="manageColumn('statsType${stat.id}Percentage');" data-value="statsType${stat.id}Percentage"/>
                                                                                                </c:if>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </c:if>
                                                                        </c:if>
                                                                    </c:forEach>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </c:forEach>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                    <c:set var="counter" value="${counter + 1}"/>
                                </c:forEach>
                            </ul>
                        </div>
                    </div>
                    <div class="uk-modal-footer uk-width-1-1" style="display: flex">
                        <div class="uk-text-right" style="margin-left: auto">
                            <button style="min-width:80px;" onclick="closeManageColumns();
                                    createSaveFilter();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.conferma"/></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="smartSearchTablesModal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeManageTables();">&times;</span>
                    <h3 style="text-transform: uppercase; font-weight: bold"><spring:message code="team.stats.create.filter.title"/></h3>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <label class="uk-text-medium" for="namePlaylistModify"><spring:message code="video.nome" />:</label>
                        <input id="smartSearchFilterName" class="uk-input" type="text" aria-label="Input" maxlength="32">
                        <button class="uk-h4 uk-button" onclick="createSaveFilter(true)"><spring:message code="team.stats.create"/></button>
                    </div>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                            <tbody>
                                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                    <td class="playlistTvTitle" style="width: 30vh">
                                        <spring:message code='team.stats.filter.name.label'/>
                                    </td>
                                    <td class="playlistTvTitle">
                                        <spring:message code='team.stats.colvis.title'/>
                                    </td>
                                </tr>
                                <c:forEach var="filter" items="${mFilters.keySet()}">
                                    <c:if test="${filter.isSics == null}">
                                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                            <td id="smartSearchFilterName${filter.id}">
                                                ${filter.name}
                                            </td>
                                            <td id="smartSearchFilterButtons${filter.id}">
                                                <button class="uk-icon-hover uk-button uk-button-mini" onclick="manageRenameFilter(${filter.id}, false)" title="<spring:message code='playlist.tooltip.rename'/>" id="smartSearchRenameButton${filter.id}"><i id="smartSearchRenameIcon${filter.id}" class="uk-icon-pencil"></i></button>
                                                <button class="uk-icon-hover uk-button uk-button-mini" onclick="deleteFilter(${filter.id})" title="<spring:message code='menu.user.delete'/>"><i class="uk-icon-trash"></i></button>
                                            </td>
                                        </tr>
                                    </c:if>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                    <div class="uk-modal-footer uk-text-right uk-width-1-1">
                        <button style="min-width:80px;" onclick="closeManageTables();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.close"/></button>
                    </div>
                </div>
            </div>
        </div>

        <div id="watchlistModal" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeWatchlistModal();">&times;</span>
                </div>
                <div class="container" style="min-height: 25vh; max-height: 90vh;">
                    <ul class="uk-nav uk-nav-dropdown">
                        <li>
                            <span style="font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="watchlist.create"/></span>
                        </li>
                        <li style="margin-top: 10px">
                            <div style="width: 20%; float: left">
                                <spring:message code="watchlist.nome"/>
                            </div>
                            <div style="width: 80%">
                                <input type="text" id="nameWatchlist" maxlength="128"/>
                            </div>
                        </li>
                        <li style="margin-top: 10px">
                            <button onclick="addToWatchlist();" class="uk-button uk-dropdown-close"><spring:message code="watchlist.create"/></button>
                        </li>
                        <li id="watchlistButtonLi"<c:if test="${mWatchlist == null || mWatchlist.size() == 0}"> class="uk-hidden"</c:if>>
                                <div class="playlistTv">
                                    <hr>
                                    <span style="font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="watchlist.fastadd"/></span>
                            </div>
                            <div id="playlistTvButtonDiv">
                                <c:forEach var="item" items="${mWatchlist}">
                                    <button onclick="addToWatchlist(${item.id});" class="uk-button uk-dropdown-close playlistTvButton" style="margin-right: 5px" <c:if test="${item.editable != null && !item.editable}"> disabled title="<spring:message code="watchlist.share.viewonly"/>"</c:if>>${item.name}<c:if test="${item.editable != null}"> (<spring:message code='menu.condivisa'/>)</c:if></button>
                                </c:forEach>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <button style="min-width:80px;" onclick="closeWatchlistModal();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.close"/></button>
                    <span id="saveModifyPlaylist"></span>
                </div>
            </div>
        </div>
    </body>

</html>
