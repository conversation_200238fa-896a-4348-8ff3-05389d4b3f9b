<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script type="text/javascript" src="/sicstv/js/jquery.timer.js"></script>
        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

        <script typt="text/javascript" src="/sicstv/uikit/js/components/notify.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script type="text/javascript" src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" ></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css">
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        <script type="text/javascript">
            $(document).ready(function () {
                $("#namePlaylistModify").on('change', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });

                $("#notePlaylistModify").on('change', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });

                $("#namePlaylistModify").on('paste', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });

                $("#notePlaylistModify").on('paste', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);

                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });

                $("#namePlaylistModify").on('keypress', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });

                $("#notePlaylistModify").on('keypress', function (event) {
                    var regex = new RegExp("^[a-zA-Z0-9_-\\s]*$");
                    //var regex = new RegExp("^[a-zA-Z0-9!%\^&*\)(\\?+=._-\\s]*$");
                    var key = String.fromCharCode(!event.charCode ? event.which : event.charCode);
                    if (!regex.test(key)) {
                        event.preventDefault();
                        return false;
                    }
                });
            });

            function shareLinkPlaylist(id) {
                const fallbackCopyTextToClipboardPlaylist = str => {
                    var textArea = document.createElement("textarea");
                    textArea.value = str;

                    // Avoid scrolling to bottom
                    textArea.style.top = "0";
                    textArea.style.left = "0";
                    textArea.style.position = "fixed";

                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();

                    try {
                        var successful = document.execCommand('copy');
                        var msg = successful ? 'successful' : 'unsuccessful';
                        UIkit.notify("<spring:message code="menu.user.linkCopiato"/>", {status: 'success', timeout: 1000});
                    } catch (err) {
                        UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger', timeout: 1000});
                        return Promise.reject('The Clipboard API is not available.');
                    }
                    document.body.removeChild(textArea);
                }

                const copyToClipboardPlaylist = str => {
                    if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
                        UIkit.notify("<spring:message code="menu.user.linkCopiato"/>", {status: 'success', timeout: 1000});
                        return navigator.clipboard.writeText(str);
                    } else {
                        fallbackCopyTextToClipboardPlaylist(str);
                    }
                    //UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger'});
                    //return Promise.reject('The Clipboard API is not available.');
                };

                var strUrl = "/sicstv/user/shareLinkPlaylist.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        console.log(msg);
                        if (msg.substr(0, 5) === "false") {
                            UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger', timeout: 1000});
                        } else {
                            //COPIA msg negli appunti
                            copyToClipboardPlaylist(msg);
                        }
                    },
                    async: false
                });
            }
            function askDelete() {
                $("#menuDelete").addClass("uk-hidden");
                $("#modalDeleteConfirmText").html('<spring:message code="menu.deleting"/>:<br>' + 1 + ' <spring:message code="menu.admin.esportazioni"/><br><spring:message code="menu.areyousure"/>');
                $("#modalDeleteConfirm").removeClass("uk-hidden");
                $("#modalDeleteConfirm").addClass("uk-open");
            }

            function jsDownloadPlaylist(id) {
                var strUrl = "/sicstv/user/downloadPlaylist.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "true") {
                            UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                            window.location.replace(msg.substr(5));
                            $.unblockUI();
                        }
                    }
                });
                sessionStorage.removeItem('exportIdentifier');
            }
            function closeDelete() {
                $("#menuDelete").removeClass("uk-hidden");
                $("#modalDeleteConfirm").removeClass("uk-open");
                $("#modalDeleteConfirm").addClass("uk-hidden");
                $("#modalDeleteConfirmText").html("");
            }
            function deletePlaylistAndGameSelected() {
                var strUrl = "/sicstv/user/deleteGamesAndPlaylists.htm?idPlaylists=" + ${mPlaylist.getId()} + "&idGames=";
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "false") {
                            UIkit.notify("<spring:message code="menu.user.errorDeleting"/>", {status: 'danger', timeout: 1000});
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            UIkit.notify(1 + " <spring:message code="menu.user.playlistDeleted"/>", {status: 'success', timeout: 1000});
                            setTimeout(() => {
                                window.location.replace("/sicstv/user/mysicstv.htm");
                            }, 1000);
                        }
                    }
                });
                $("#menuDelete").removeClass("uk-hidden");
                closeDelete();
            }

            function openModifyPlaylist(id) {
                var namePlaylist = "${mPlaylist.getName()}";
                $("#saveModifyPlaylist").html("<button style='min-width:80px;' onclick='updatePlaylist(" + id + ");' class='uk-text-large uk-text-bold uk-button js-modal-confirm'><spring:message code='playlist.save'/></button>");
                $("#menuDelete").addClass("uk-hidden");
                $("#modalModifyPlaylist").addClass("uk-open");
                $("#modalModifyPlaylist").removeClass("uk-hidden");
                $("#namePlaylistModify").val(namePlaylist);
            }

            function updatePlaylist(id) {
                var nameValue = $("#namePlaylistModify").val();
                var noteValue = $("#notePlaylistModify").val();
                if (typeof nameValue !== 'undefined' && typeof noteValue !== 'undefined') {
                    noteValue = noteValue.trim();
                    nameValue = nameValue.trim();
                    if (nameValue !== '') {// && noteValue != '') {
                        $.ajax({
                            type: "POST",
                            url: "/sicstv/user/updatePlaylist.htm",
                            cache: false,
                            data: encodeURI("&id=" + id + "&name=" + nameValue + "&note=" + noteValue),
                            success: function (msg) {
                                $("#menuDelete").addClass("uk-hidden");
                                if (msg.substr(0, 5) === "false") {
                                    UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                                } else {
                                    closeModifyPlaylist();
                                }
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            }
                        });
                    } else {
                        UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                    }
                } else {
                    UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                }
            }
        </script>
    </head>
    <body style="background:#F5F5F5;">
        <c:if test="${mobile}">
            <%@ include file="header.jsp" %>
            <div id='breadcrumbMobile' class='uk-width-1-1'><span class="data"><b>${mPlaylist.name}</b></span></div>
                    </c:if>
        <input type="hidden" value="${mPlaylist.name}" id="playlistName"/>
        <input type="hidden" value="${mPlaylist.name}||${mPlaylist.uploadedBy}||${mPlaylist.getDateDayString()} ${mPlaylist.getDateMonthString()} ${mPlaylist.getDateYearString()}" id="playlistDetails"/>
        <div id="videoContainer" class="uk-width-1-1">
            <!--button id='btnCinema' class="uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small" onclick="toggleTheatreMode();" title="<spring:message code='tooltip.cinema'/>">
                <i class="uk-icon-desktop"></i>
            </button-->
            <c:choose>
                <c:when test="${mPlaylist.providerId==2}">
                    <video id="media_player1" class='uk-width-1-1' data-setup='{}' autoplay controls>
                        <source src="${mPlaylist.getVideoPathS3()}" type='video/mp4'/>
                    </video>
                </c:when>
            </c:choose>
        </div>
        <div id="menuDelete" class="uk-navbar orangecolor" style="vertical-align:middle;">
            <c:if test="${mPlaylist.isCanDelete()}">
                <button style="min-width:50px;" id="btnDeletePlGm" class="uk-button uk-button-danger uk-margin-all uk-float-right" onclick="askDelete();">
                    <i class="uk-icon-medium uk-icon-trash"></i>
                    <span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.user.delete"/></span>
                </button>
                <button style="min-width:50px;" id="btnModifyPlGm" class="uk-button uk-margin-all uk-float-right" onclick="openModifyPlaylist('${mPlaylist.getId()}');">
                    <i class="uk-icon-medium uk-icon-pencil"></i>
                    <span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.modify"/></span>
                </button>
            </c:if>
            <button style="min-width:50px;" id="btnDownloadPlGm" class="uk-button uk-margin-all uk-float-right" onclick="jsDownloadPlaylist('${mPlaylist.getId()}');">
                <i class="uk-icon-medium uk-icon-download"></i>
                <span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.user.download"/></span>
            </button>
            <c:if test="${!mPlaylist.isZip()}">
                <button style="min-width:50px;" id="btnSharePlGm" class="uk-button uk-margin-all uk-float-right" onclick="shareLinkPlaylist('${mPlaylist.getId()}');">
                    <i class="uk-icon-medium uk-icon-share"></i>
                    <span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.share"/></span>
                </button>
            </c:if>
        </div>
        <div id="playlistInfo" class="uk-margin-bottom uk-width-1-1 uk-text-truncate">
            <div class="uk-margin-left uk-margin-right uk-width-1-1" style="text-transform: uppercase;">
                <div id="infoTable">
                    <div class="uk-margin-top">
                        <div class="uk-text-bold">
                            <spring:message code="playlist.descrizione" />:
                        </div>
                        <div>
                            ${mPlaylist.name}
                        </div>
                    </div>
                    <div class="uk-margin-top">
                        <div class="uk-text-bold">
                            <spring:message code="playlist.caricatoDa" />
                        </div>
                        <div>
                            ${mPlaylist.uploadLastName}&nbsp;${mPlaylist.uploadFirstName}<!--${mPlaylist.uploadedBy}-->
                        </div>
                    </div>
                    <div class="uk-margin-top" style="height: 30px;vertical-align: middle;">
                        <div class="uk-text-bold">
                            <spring:message code="playlist.caricatoData" />:
                        </div>
                        <div>
                            ${mPlaylist.getDateDayString()} ${mPlaylist.getDateMonthString()} ${mPlaylist.getDateYearString()}
                        </div>
                    </div>
                    <div class="uk-margin-top" style="height: 30px;vertical-align: middle;">
                        <div class="uk-text-bold">
                            <spring:message code="menu.type"/>:
                        </div>
                        <div>
                            <c:if test="${mPlaylist.isZip()}">
                                PLAYLIST (ZIP)
                            </c:if>
                            <c:if test="${!mPlaylist.isZip()}">
                                PLAYLIST (VIDEO)
                            </c:if>
                        </div>
                    </div>
                    <div class="uk-margin-top" style="height: 30px;vertical-align: middle;">
                        <div class="uk-text-bold">
                            <spring:message code="video.note" />:
                        </div>
                        <div>
                            ${mPlaylist.getNote()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>