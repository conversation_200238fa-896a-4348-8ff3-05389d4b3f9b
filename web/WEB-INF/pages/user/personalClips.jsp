<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

        <script typt="text/javascript" src="/sicstv/uikit/js/components/notify.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script type="text/javascript" src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" ></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css">
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        <script src="/sicstv/js/jquery.timer.js" type="text/javascript" ></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>

        <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet" />
        <script src="/sicstv/js/video.js"></script>
        <script src="/sicstv/js/videojs-offset.js?<%=System.currentTimeMillis()%>"></script>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>
        <script type="text/javascript">
            var eventMap = new Map();
            var teams = new Map(), players = new Map();

            $(document).ready(function () {
            <c:forEach var="item" items="${mPersonalClips}" >
                eventMap.set('${item.id}', eval(${item.getJson(mLanguage)}));
            </c:forEach>

                eventMap.forEach(function (element) {
                    if (typeof teams.get(element._idTeam) === "undefined") {
                        teams.set(element._idTeam, element.mTeam);
                    }
                    if (element.playerId !== 0) {
                        if (typeof players.get(element.playerId) === "undefined") {
                            players.set(element.playerId, element.mPlayer);
                        }
                    }
                });

                reloadTeamOptions();
                reloadPlayerOptions();

                setTimeout(function () {
                    $(".squaredCheckboxEvent").find("input").removeAttr("checked");
                }, 50);
            });

            function reloadTeamOptions() {
                $("#team-select option").remove();
                $("#team-select").append($('<option>', {
                    value: "",
                    text: ""
                }));
                teams.forEach(function (value, key) {
                    $("#team-select").append($('<option>', {
                        value: key,
                        text: value
                    }));
                });
            }

            function reloadPlayerOptions() {
                $("#player-select option").remove();
                $("#player-select").append($('<option>', {
                    value: "",
                    text: ""
                }));
                players.forEach(function (value, key) {
                    $("#player-select").append($('<option>', {
                        value: key,
                        text: value
                    }));
                });
            }

            function filterRows() {
                // reset stato base
                $("#userTable tbody tr").removeClass("uk-hidden");

                var teamToSearch = null, playerToSearch = null, descriptionToSearch = null;
                if ($("#team-select").val()) {
                    teamToSearch = $("#team-select").val();
                }
                if ($("#player-select").val()) {
                    playerToSearch = $("#player-select").val();
                }
                if ($("#description-input").val()) {
                    descriptionToSearch = $("#description-input").val();
                }

                if (teamToSearch !== null && playerToSearch !== null) {
                    $("#userTable tbody tr:not([team='" + teamToSearch + "'][player='" + playerToSearch + "'])").addClass("uk-hidden");
                } else if (teamToSearch !== null) {
                    $("#userTable tbody tr:not([team='" + teamToSearch + "'])").addClass("uk-hidden");
                } else if (playerToSearch !== null) {
                    $("#userTable tbody tr:not([player='" + playerToSearch + "'])").addClass("uk-hidden");
                }

                if (descriptionToSearch !== null) {
                    $("#userTable tbody tr:not(.uk-hidden)").each(function (index, element) {
                        var description = $(element).attr("note").toLowerCase();

                        if (!description.includes(descriptionToSearch)) {
                            $(element).addClass("uk-hidden");
                        }
                    });
                }
            }

            var selectedEventIds = [];
            function manageCheckbox(id) {
                if (selectedEventIds.indexOf(id) === -1) {
                    selectedEventIds.push(id);
                    $("#event-" + id).attr("checked", "checked");
                } else {
                    selectedEventIds.splice(selectedEventIds.indexOf(id), 1);
                    $("#event-" + id).removeAttr("checked");
                }

                $("#selected-events-amount").html(" " + selectedEventIds.length);
                if (selectedEventIds.length > 0) {
                    $("#view-selected-events").removeClass("uk-hidden");
                    $("#remove-rec-button").removeClass("uk-hidden");
                } else {
                    $("#view-selected-events").addClass("uk-hidden");
                    $("#remove-rec-button").addClass("uk-hidden");
                }
            }

            function viewSelectedEvents() {
                if (selectedEventIds.length > 0) {
                    location.href = '/sicstv/user/video.htm?personal=false&id=&eventIds=' + selectedEventIds.join(",") + '&fixtureId=&idComp=&idTeam=&idPlayer=&goals=false&event=&filter=&limit=50&startClip=true';
                }
            }

            function selectAllEvents() {
                $(".squaredCheckboxEvent").find("input:not([checked='checked'])").each(function (index, element) {
                    eval($(element).attr("onclick"));
                });
            }

            function deselectAllEvents() {
                var eventIds = [...selectedEventIds];

                eventIds.forEach(function (element) {
                    manageCheckbox(element);
                });
            }

            function deleteRec() {
                if (selectedEventIds.length > 0) {
                    if (confirm("Are you sure you want to delete these clip(s)?")) {
                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/deletePersonalClip.htm",
                            data: encodeURI("eventId=&eventIds=" + selectedEventIds.join(",")),
                            success: function (result) {
                                if (result === "ok") {
                                    UIkit.notify("<spring:message code="video.record.delete.success"/>", {status: 'success', timeout: 1000});
                                    location.reload();
                                } else {
                                    UIkit.notify("<spring:message code="video.record.save.error"/>", {status: 'danger', timeout: 1000});
                                }
                            }
                        });
                    }
                }
            }
        </script>
    </head>
    <body>
        <%@ include file="header.jsp" %>
        <div id="breadcrumb" class="uk-text-truncate" style="text-transform: uppercase;">
            <ul class="uk-breadcrumb uk-margin-left">
                <li style="text-transform: uppercase;"><a href="/sicstv/user/myhome.htm"><spring:message code="menu.user.mysicstv"/></a></li>
                <li style="text-transform: uppercase;"><spring:message code="lib.personal.clips"/></li>
            </ul>
        </div>

        <c:choose>
            <c:when test="${empty mPersonalClips}">
                <h2 class="uk-margin-left-big"><spring:message code="team.stats.no.data"/></h2>
            </c:when>
            <c:otherwise>
                <div class="uk-padding-top uk-padding-left uk-flex">
                    <div class="uk-margin-right uk-width-1-6">
                        <span class="uk-form">
                            <spring:message code="video.record.input.description"/>:
                            <input type="text" class="uk-width-7-10" id="description-input" onkeyup="filterRows();">
                        </span>
                    </div>
                    <div class="uk-margin-right uk-width-1-6">
                        <span class="uk-form">
                            <spring:message code="lib.squadra"/>:
                            <select id="team-select" class="uk-width-7-10" onchange="filterRows();">
                            </select>
                        </span>
                    </div>
                    <div class="uk-width-1-6">
                        <span class="uk-form">
                            <spring:message code="video.giocatore"/>:
                            <select id="player-select" class="uk-width-7-10" onchange="filterRows();">
                            </select>
                        </span>
                    </div>
                </div>

                <div class="uk-margin-top d-flex">
                    <div class="uk-padding-left">
                        <button class="uk-button" onclick="selectAllEvents();"><spring:message code="menu.selezionatutto"/></button>
                        <button class="uk-button" onclick="deselectAllEvents();"><spring:message code="menu.deselezionatutto"/></button>
                        <button id="view-selected-events" class="uk-button uk-button-confirm uk-hidden" onclick="viewSelectedEvents();"><spring:message code="menu.play.all"/> - <span id="selected-events-amount"> 0</span></button>
                        <button id="remove-rec-button" class="uk-button uk-hidden" onclick="deleteRec();"><i class="uk-icon-trash"></i></button>
                    </div>
                    <table id="userTable" class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small" style="width: 50%">
                        <thead>
                            <tr>
                                <th></th>
                                <th class="uk-width-4-10">Description</th>
                                <th class="uk-width-3-10">Team</th>
                                <th class="uk-width-3-10">Player</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="row" items="${mPersonalClips}">
                                <tr note="${row.mNote}" team="${row.getIdTeam()}" player="${row.playerId}">
                                    <td>
                                        <span class="squaredCheckboxEvent">
                                            <input type="checkbox" id="event-${row.id}" onclick="manageCheckbox(${row.id});">
                                            <label for="event-${row.id}"></label>
                                        </span>
                                    </td>
                                    <td style="vertical-align: middle">
                                        <a class="uk-text-decoration-underline" href="/sicstv/user/video.htm?personal=false&id=&eventIds=${row.getId()}&fixtureId=&idComp=&idTeam=&idPlayer=&goals=false&event=&filter=&limit=50&startClip=true">${row.mNote}</a>
                                    </td>
                                    <td style="vertical-align: middle">${row.mTeam}</td>
                                    <td style="vertical-align: middle">${row.mPlayer}</td>
                                    <td>
                                        <button class="uk-icon-hover uk-button uk-button-mini" onclick="location.href = '/sicstv/user/video.htm?personal=false&id=&eventIds=${row.getId()}&fixtureId=&idComp=&idTeam=&idPlayer=&goals=false&event=&filter=&limit=50&startClip=true'"><i class="uk-icon-play"></i></button>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </c:otherwise>
        </c:choose>
    </body>
</html>
