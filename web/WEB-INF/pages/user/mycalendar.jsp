<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">
        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/index.global.min.js" type="text/javascript"></script>
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/flat/flat.css?<%=System.currentTimeMillis()%>" rel="stylesheet">
        <script type="text/javascript">
            const calendarPage = "CALENDAR";
            function failure() {

            }

            function success() {

            }

            function dataTransform() {

            }

            function addEvent(calendar, id, title, description, dateStart, dateEnd, type, startTime, location, players, image, colorV, colorTxt) {
                calendar.addEvent({
                    start: dateStart,
                    end: dateEnd,
                    editable: true,
                    startEditable: true,
                    durationEditable: true,
                    color: colorV,
                    textColor: colorTxt,
                    overlap: true,
                    resourceEditable: true,
                    id: id,
                    title: title,
                    description: description,
                    image: image,
                    type: type,
                    location: location,
                    players: players,
                    start_time: startTime,
                    //url: "",
                    //format: "",
                    //backgroundColor: "yellow",
                    //borderColor: "black",
                    //className: "uk-icon uk-icon-calendar",
                    //display: "",
                    //constraint: "",
                    //allow: true,
                    //defaultAllDay: true,
                    //success: success,
                    //failure: failure,
                    //eventDataTransform: dataTransform
                });
            }

            $(document).ready(function () {

                var calendarEl = document.getElementById('calendar');
                var calendar = new FullCalendar.Calendar(calendarEl, {
                    selectable: true,
                    editable: true,
                    initialView: 'dayGridMonth', //multiMonthYear
                    multiMonthMaxColumns: 1, // force a single column
                    editable: true,
                    firstDay: 1,
                    fixedWeekCount: 6,
                    dayMaxEventRows: true,
                    select: function (start, end) {
                        console.log(start);
                        window.alert(start['startStr'] + " - " + start['endStr']);
                    }, eventContent: function (arg) {
                        var event = arg.event;
                        var customHtml = "<div class='uk-text-break uk-text-bold' style='padding-left:5px;padding-right:5px;padding-top:10px;padding-bottom:10px;'>";
                        if (event.extendedProps.image) {
                            customHtml += "<img src='" + event.extendedProps.image + "' width='18' height='12' style='overflow: hidden;'/> ";
                        }
                        if (event.extendedProps.start_time) {
                            customHtml += "(" + event.extendedProps.start_time + ") ";
                        }
                        if (event.extendedProps.type) {
                            customHtml += "";
                        }
                        if (event.extendedProps.location) {
                            customHtml += "";
                        }
                        if (event.extendedProps.description) {
                            customHtml += "";
                        }
                        customHtml += event.title + "</div>";
                        return {html: customHtml};
                    },
                    headerToolbar:
                            {
                                start: 'prevYear,prev,next,nextYear today', // will normally be on the left. if RTL, will be on the right
                                center: 'title',
                                end: '' // will normally be on the right. if RTL, will be on the left
                            },
                    showNonCurrentDates: false,
                });
                calendar.setOption('locale', 'it');
                calendar.setOption('locale', 'en');
                calendar.on('dateClick', function (info) {
                    console.log('clicked on ' + info.dateStr);
                });

                var start = 14;
                for (var i = 1; i < 10; i++) {
                    if (i == 4) {
                        addEvent(calendar, i, "GIOVANISSIMI", i + " descrizioneLavoro", "2023-02-14", "2023-02-14", "nomeMezzo", i + ":30", "Bassano del Grappa", "Fabio Baggio,Gabriele Bosco", "/sicstv/images/tact.png", "yellow", "black");
                    } else if (i % 2 === 0) {
                        addEvent(calendar, i, "ALLIEVI", i + " descrizioneLavoro", "2023-02-14", "2023-02-14", "nomeMezzo", i + ":30", "Bassano del Grappa", "Fabio Baggio,Gabriele Bosco", "/sicstv/images/tact.png", "green", "white");
                    } else if (i % 3 === 0) {
                        addEvent(calendar, i, "GIOVANISSIMI", i + " descrizioneLavoro", "2023-02-14", "2023-02-14", "nomeMezzo", i + ":30", "Bassano del Grappa", "Fabio Baggio,Gabriele Bosco", "/sicstv/images/tact.png", "blue", "white");
                    } else {
                        addEvent(calendar, i, "1ma SQUADRA GIOVANISSIMA DI NOTTE E GIORNO", i + " descrizioneLavoro", "2023-02-14", "2023-02-14", "nomeMezzo", i + ":30", "Bassano del Grappa", "Fabio Baggio,Gabriele Bosco", "/sicstv/images/tact.png", "red", "white");
                    }
                }
                calendar.render();
            });
        </script>
    </head>
    <body style="height:calc(100vh-10px);">
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>
        <div id="breadcrumb">
            <ul class="uk-breadcrumb uk-margin-left">
                <li style="text-transform: uppercase;"><a href="/sicstv/user/myhome.htm"><spring:message code="menu.user.mysicstv"/></a><i id="showCurrentFilter"></i></li>
                <li style="text-transform: uppercase;"><spring:message code="menu.user.mysicstv_calendar"/><i id="showCurrentFilter"></i></li>
            </ul>
        </div>
        <div id="menuCalendar" class="uk-navbar uk-width-1-1">
            <ul class="uk-navbar-nav">
                <li style="text-transform: uppercase;"><button class="uk-button"><span class="uk-icon uk-icon-wrench uk-icon-medium uk-margin-small-right uk-margin-small-left"></span><spring:message code="mycalendar.mezzi"/></button></li>
                <li style="text-transform: uppercase;"><button class="uk-button"><span class="uk-icon uk-icon-flash uk-icon-medium uk-margin-small-right uk-margin-small-left"></span><spring:message code="mycalendar.lavori"/></button></li>
                <li style="text-transform: uppercase;"><button class="uk-button"><span class="uk-icon uk-icon-users uk-icon-medium uk-margin-small-right uk-margin-small-left"></span><spring:message code="mycalendar.squadre"/></button></li>
                <li style="text-transform: uppercase;"><button class="uk-button"><span class="uk-icon uk-icon-user uk-icon-medium uk-margin-small-right uk-margin-small-left"></span><spring:message code="mycalendar.contatti"/></button></li>
                <li style="text-transform: uppercase;"><button class="uk-button"><span class="uk-icon uk-icon-calendar uk-icon-medium uk-margin-small-right uk-margin-small-left"></span><spring:message code="mycalendar.stagioni"/></button></li>
                <li style="text-transform: uppercase;"><button class="uk-button"><span class="uk-icon uk-icon-medkit uk-icon-medium uk-margin-small-right uk-margin-small-left"></span><spring:message code="mycalendar.infermeria"/></button></li>
                <li style="text-transform: uppercase;"><button class="uk-button"><span class="uk-icon uk-icon-file-pdf-o uk-icon-medium uk-margin-small-right uk-margin-small-left"></span><spring:message code="mycalendar.report"/></button></li>
            </ul>
        </div>
        <div class="uk-margin-all-small">
            <div id='calendar' class="marteiii uk-width-1-1"></div>
            <!--div id='calendar' class="marteiii uk-width-4-5 uk-float-left"></div>
            <div class="marteiii uk-float-left uk-width-1-5 uk-visible-large">
                <ul class="uk-nav uk-nav-navbar uk-margin-all-small uk-text-center">
                    <li class="uk-button uk-width-1-1 uk-margin-bottom">
                        AGGIUNGI ALLENAMENTO
                    </li>
                    <li class="uk-button uk-width-1-1 uk-margin-top uk-margin-bottom">
                        MODIFICA ALLENAMENTO
                    </li>
                    <li class="uk-button uk-width-1-1 uk-margin-top uk-margin-bottom">
                        ELIMINA ALLENAMENTO
                    </li>
                    <li class="uk-button uk-width-1-1 uk-margin-top uk-margin-bottom">
                        IMPOSTAZIONI
                    </li>
                </ul>
            </div-->
        </div>
    </body>
</html>
