${dwnLimit}<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<c:choose> 
    <c:when test="${dwnLimit == false && !error}">
        ${path}
    </c:when>

    <c:when test="${dwnLimit == true}">
        <p class="uk-text-center"><i class="uk-icon-minus-circle"></i><br/><spring:message code="alert.download.limit"/></p>
        </c:when>

    <c:otherwise>
        <p class="uk-text-center"><i class="uk-icon-minus-circle"></i><br/><spring:message code="alert.download.error"/></p>
        </c:otherwise>
    </c:choose>
