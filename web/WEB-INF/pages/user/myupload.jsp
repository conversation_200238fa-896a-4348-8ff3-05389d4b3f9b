<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

        <script typt="text/javascript" src="/sicstv/uikit/js/components/notify.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script type="text/javascript" src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" ></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css">
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        <script src="/sicstv/js/jquery.timer.js" type="text/javascript" ></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>

        <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet" />
        <script src="/sicstv/js/video.js"></script>
        <script src="/sicstv/js/videojs-offset.js?<%=System.currentTimeMillis()%>"></script>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>
        <script type="text/javascript">
            var dropzone, attachmentDropzone;
            var videoPlayer;
            var randomUUID = "${mRandomUUID}";

            var totMatches = [];
            var numMatches = [];
            var matchPerPage = 25;
            var useLastSeason = true; // per bypass stagione

            document.addEventListener("keydown", function (event) {
                // Verifica se il tasto premuto è la barra spaziatrice (codice 32)
                if (event.keyCode === 32) {
                    // Verifica se l'elemento attivo è un campo di input
                    const activeElement = document.activeElement;
                    if ((activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA") && (activeElement.type === "text" || activeElement.type === "password" || activeElement.type === "search" || activeElement.type === "textarea")) {
                        // Se siamo in un campo di input, lascia che la barra spaziatrice venga gestita normalmente
                        return;
                    }

                    event.preventDefault();
                    if (videoPlayer !== null && typeof videoPlayer !== 'undefined' && videoPlayer.src()) {
                        resumeStopVideo();
                    }
                } else if (event.keyCode === 37) { // freccia sinistra
                    // Verifica se l'elemento attivo è un campo di input
                    const activeElement = document.activeElement;
                    if ((activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA") && (activeElement.type === "text" || activeElement.type === "password" || activeElement.type === "search" || activeElement.type === "textarea")) {
                        // Se siamo in un campo di input, lascia che la barra spaziatrice venga gestita normalmente
                        return;
                    }

                    event.preventDefault();
                    if (videoPlayer !== null && typeof videoPlayer !== 'undefined' && videoPlayer.src()) {
                        if (videoPlayer.currentTime() <= 2) {
                            videoPlayer.currentTime(0);
                        } else {
                            videoPlayer.currentTime(videoPlayer.currentTime() - 2);
                        }
                    }
                } else if (event.keyCode === 39) { // freccia destra
                    // Verifica se l'elemento attivo è un campo di input
                    const activeElement = document.activeElement;
                    if ((activeElement.tagName === "INPUT" || activeElement.tagName === "TEXTAREA") && (activeElement.type === "text" || activeElement.type === "password" || activeElement.type === "search" || activeElement.type === "textarea")) {
                        // Se siamo in un campo di input, lascia che la barra spaziatrice venga gestita normalmente
                        return;
                    }

                    event.preventDefault();
                    if (videoPlayer !== null && typeof videoPlayer !== 'undefined' && videoPlayer.src()) {
                        if ((videoPlayer.currentTime() + 2) <= videoPlayer.duration()) {
                            videoPlayer.currentTime(videoPlayer.currentTime() + 2);
                        } else {
                            videoPlayer.currentTime(videoPlayer.duration());
                        }
                    }
                }
            });

            function resumeStopVideo() {
                if (!videoPlayer.paused()) {
                    videoPlayer.pause();
                } else {
                    videoPlayer.play();
                }
            }

            $(document).ready(function () {
                // Configura Dropzone
                Dropzone.autoDiscover = false;

                dropzone = new Dropzone("#videoUpload", {
                    url: "/", // Indirizzo URL per il caricamento dei file
                    method: "put", // Metodo HTTP per il caricamento
                    paramName: "file", // Nome del parametro che contiene il file
                    autoProcessQueue: false, // Non avviare l'upload automaticamente
                    maxFilesize: 5120, // Dimensione massima del file in MB
                    addRemoveLinks: true,
                    uploadMultiple: false,
                    parallelUploads: 1,
                    sending(file, xhr) {
                        let _send = xhr.send;
                        xhr.send = () => {
                            _send.call(xhr, file);
                        };
                    },
                    acceptedFiles: 'video/*', // Accetta tutti i tipi di file video
                    maxFiles: 1,
                    headers: ''
                });
                attachmentDropzone = new Dropzone("#videoAttachmentsUpload", {
                    url: "/", // Indirizzo URL per il caricamento dei file
                    method: "put", // Metodo HTTP per il caricamento
                    paramName: "file", // Nome del parametro che contiene il file
                    autoProcessQueue: false, // Non avviare l'upload automaticamente
                    maxFilesize: 5120, // Dimensione massima del file in MB
                    addRemoveLinks: true,
                    uploadMultiple: false,
                    parallelUploads: 1
                });

                dropzone.on("addedfile", function (file) {
                    if (dropzone.options.modality === 1) {
                        videoPlayer.src({type: file.type, src: URL.createObjectURL(file)});
                    }
                    $("#upload-button").attr("style", "color: limegreen");
                });

                dropzone.on("queuecomplete", function (file, res) {
                    if (dropzone.getUploadingFiles().length === 0 && attachmentDropzone.getUploadingFiles().length === 0) {
                        var allDone = true;
                        dropzone.files.forEach(function (file) {
                            if (file.status !== Dropzone.SUCCESS) {
                                allDone = false;
                            }
                        });
                        if (allDone) {
                            completeUpload();
                        }
                    }
                });
                attachmentDropzone.on("queuecomplete", function () {
                    if (dropzone.getUploadingFiles().length === 0 && attachmentDropzone.getUploadingFiles().length === 0) {
                        var allDone = true;
                        dropzone.files.forEach(function (file) {
                            if (file.status !== Dropzone.SUCCESS) {
                                allDone = false;
                            }
                        });
                        if (allDone) {
                            completeUpload();
                        }
                    }
                });

                dropzone.on("success", function () {
                    uploadFile(true);
                });
                attachmentDropzone.on("success", function () {
                    uploadFileAttachment();
                });

                videoPlayer = videojs("preview");
                videoPlayer.fluid(true); // resize al contenuto
                videoPlayer.playbackRates([0.5, 1, 2, 3]);
                videoPlayer.autoplay(true);
                videoPlayer.controlBar.removeChild(videoPlayer.controlBar.getChild('PictureInPictureToggle'));  // toglie tasto per minimizzare il video
                videoPlayer.controlBar.removeChild(videoPlayer.controlBar.getChild('remainingTimeDisplay'));    // toglie tempo rimanente clip

                var btna = addPlayerNewButton({
                    player: videoPlayer,
                    content: "<img width='15px' src='/sicstv/images/back-forward-player.svg'/>",
                    insertBeforeClassName: "vjs-play-control",
                    id: "fastForward"
                });
                btna.onclick = function () {
                    if (videoPlayer !== null && typeof videoPlayer !== 'undefined' && videoPlayer.src()) {
                        if (videoPlayer.currentTime() <= 2) {
                            videoPlayer.currentTime(0);
                        } else {
                            videoPlayer.currentTime(videoPlayer.currentTime() - 2);
                        }
                    }
                };
                var btnb = addPlayerNewButton({
                    player: videoPlayer,
                    content: "<img width='15px' src='/sicstv/images/fast-forward-player.svg'/>",
                    insertBeforeClassName: "vjs-volume-panel",
                    id: "backForward"
                });
                btnb.onclick = function () {
                    if (videoPlayer !== null && typeof videoPlayer !== 'undefined' && videoPlayer.src()) {
                        if ((videoPlayer.currentTime() + 2) <= videoPlayer.duration()) {
                            videoPlayer.currentTime(videoPlayer.currentTime() + 2);
                        } else {
                            videoPlayer.currentTime(videoPlayer.duration());
                        }
                    }
                };

                // gestisco il fatto che se clicci sul tasto play non vanno più gli altri tasti
                $("#videoPlayerContainer").on("click", function () {
                    setTimeout(function () {
                        $("#preview_html5_api").focus();
                    }, 100);
                });
            });

            function completeUpload() {
                $("#browserWarning").addClass("uk-hidden");
                $("#browserSuccess").removeClass("uk-hidden");

                var data = "";
                data += "countryId=" + $("#countrySelect").val();
                if (!isValidData(data)) {
                    UIkit.notify("Country not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&countryName=" + $("#countrySelect option[value='" + $("#countrySelect").val() + "']").text();
                if (!isValidData(data)) {
                    UIkit.notify("Country Name not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&competitionId=" + $("#competitionSelect").val();
                if (!isValidData(data)) {
                    UIkit.notify("Competition not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                if ($("#competitionSelect").hasClass("uk-hidden")) {
                    data += "&competitionName=" + $("#competitionInput").val();
                } else {
                    data += "&competitionName=" + $("#competitionSelect option[value='" + $("#competitionSelect").val() + "']").text();
                }
                if (!isValidData(data)) {
                    UIkit.notify("Competition Name not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                if ($("#homeTeamSelect").hasClass("uk-hidden")) {
                    data += "&homeTeam=" + $("#homeTeamInput").val();
                } else {
                    data += "&homeTeam=" + $("#homeTeamSelect").val();
                }
                if (!isValidData(data)) {
                    UIkit.notify("Home Team not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                if ($("#awayTeamSelect").hasClass("uk-hidden")) {
                    data += "&awayTeam=" + $("#awayTeamInput").val();
                } else {
                    data += "&awayTeam=" + $("#awayTeamSelect").val();
                }
                if (!isValidData(data)) {
                    UIkit.notify("Away Team not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&gameDate=" + $("#gameDate").val();
                if (!isValidData(data)) {
                    UIkit.notify("Game Date not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&fixtureId=" + $("#fixtureSelect").val();
                if (!isValidData(data)) {
                    UIkit.notify("Game not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&homeScore=" + ($("#homeScore").val() ? parseInt($("#homeScore").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("Home Score not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&awayScore=" + ($("#awayScore").val() ? parseInt($("#awayScore").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("Away Score not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&description=" + encodeURI($("#videoDescription").val());
                if (!isValidData(data)) {
                    UIkit.notify("Notes not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&firstTimeStart=" + ($("#firstTimeStart").val() ? parseInt($("#firstTimeStart").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("First Time Start not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&firstTimeEnd=" + ($("#firstTimeEnd").val() ? parseInt($("#firstTimeEnd").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("First Time End not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&secondTimeStart=" + ($("#secondTimeStart").val() ? parseInt($("#secondTimeStart").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("Second Time Start not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&secondTimeEnd=" + ($("#secondTimeEnd").val() ? parseInt($("#secondTimeEnd").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("Second Time End not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&thirdTimeStart=" + ($("#thirdTimeStart").val() ? parseInt($("#thirdTimeStart").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("Third Time Start not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&thirdTimeEnd=" + ($("#thirdTimeEnd").val() ? parseInt($("#thirdTimeEnd").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("Third Time End not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&fourthTimeStart=" + ($("#fourthTimeStart").val() ? parseInt($("#fourthTimeStart").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("Fourth Time Start not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&fourthTimeEnd=" + ($("#fourthTimeEnd").val() ? parseInt($("#fourthTimeEnd").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("Fourth Time End not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&fifthTimeStart=" + ($("#fifthTimeStart").val() ? parseInt($("#fifthTimeStart").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("Fifth Time Start not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&fifthTimeEnd=" + ($("#fifthTimeEnd").val() ? parseInt($("#fifthTimeEnd").val()) : "");
                if (!isValidData(data)) {
                    UIkit.notify("Fifth Time End not valid", {status: 'danger', timeout: 1000});
                    return;
                }

                var videoFileNames = "";
                dropzone.files.forEach(function (file) {
                    if (videoFileNames) {
                        videoFileNames += "#";
                    }
                    videoFileNames += file.name;
                });
                data += "&videoFileNames=" + videoFileNames;
                if (!isValidData(data)) {
                    UIkit.notify("Video File not valid", {status: 'danger', timeout: 1000});
                    return;
                }

                var attachmentFileNames = "";
                attachmentDropzone.files.forEach(function (file) {
                    if (attachmentFileNames) {
                        attachmentFileNames += "#";
                    }
                    attachmentFileNames += file.name;
                });
                data += "&attachmentFileNames=" + attachmentFileNames;
                if (!isValidData(data)) {
                    UIkit.notify("Attachments not valid", {status: 'danger', timeout: 1000});
                    return;
                }
                data += "&randomUUID=" + randomUUID;

                if (!isValidData(data) || encodeURI(data) === null) {
                    UIkit.notify("One or more parameters are not valid", {status: 'danger', timeout: 1000});
                    return;
                }

                console.log(data);
                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/myuploadCompleteUpload.htm",
                    data: encodeURI(data),
                    cache: false,
                    success: function (message) {
                        if (message) {
                            if (message === "ok") {
                                UIkit.notify("<spring:message code="menu.upload.success"/>", {status: 'success', timeout: 1000});
                            } else {
                                UIkit.notify("<spring:message code="menu.upload.error"/>", {status: 'danger', timeout: 1000});
                            }
                        } else {
                            UIkit.notify("<spring:message code="menu.upload.error"/>", {status: 'danger', timeout: 1000});
                        }
                    }
                });
            }

            function isValidData(data) {
                return data !== null;
            }

            // ricorsiva (non sapevo come altro fare)
            function uploadFile(skipErrorMessage) {
                if (dropzone.getQueuedFiles().length > 0) {
                    if (!$("#fixtureSelect").val() && ((!$("#competitionInput").val() && !$("#competitionSelect").val()) || (!$("#homeTeamInput").val() && !$("#homeTeamSelect").val()) || (!$("#awayTeamInput").val() && !$("#awayTeamSelect").val()))) {
                        UIkit.notify("You must choose a game or fill informations about competition and teams", {status: 'danger', timeout: 1000});
                        return;
                    }
                    if (!$("#homeScore").val() || !$("#awayScore").val()) {
                        UIkit.notify("Home and Away score are required", {status: 'danger', timeout: 1000});
                        return;
                    }

                    $("#upload-button").attr("disabled", "true");
                    var fileName = dropzone.getQueuedFiles()[0].name;
                    var contentType = dropzone.getQueuedFiles()[0].type;
                    dropzone.options.headers = {"Content-Type": contentType};
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/getAWSSignedURL.htm",
                        data: encodeURI("fileName=" + fileName + "&contentType=" + contentType + "&randomUUID=" + randomUUID),
                        cache: false,
                        success: function (link) {
                            if (link) {
                                dropzone.options.url = link;

                                $("#browserWarning").removeClass("uk-hidden");
                                dropzone.processQueue(); // Avvia l'upload
                                // ricorsione nell'evento "success" (ovvero quando finisce di caricare un file)
                            }
                        }
                    });
                } else if (typeof skipErrorMessage === "undefined") {
                    UIkit.notify("<spring:message code="menu.upload.no.file"/>", {status: 'danger', timeout: 1000});
                }
                uploadFileAttachment();
            }

            function uploadFileAttachment() {
                if (attachmentDropzone.getQueuedFiles().length > 0) {
                    var fileName = attachmentDropzone.getQueuedFiles()[0].name;
                    var contentType = attachmentDropzone.getQueuedFiles()[0].type;
                    attachmentDropzone.options.headers = {"Content-Type": contentType};
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/getAWSSignedURL.htm",
                        data: encodeURI("fileName=" + fileName + "&contentType=" + contentType + "&randomUUID=" + randomUUID),
                        cache: false,
                        success: function (link) {
                            if (link) {
                                attachmentDropzone.options.url = link;

                                $("#browserWarning").removeClass("uk-hidden");
                                attachmentDropzone.processQueue(); // Avvia l'upload
                                // ricorsione nell'evento "success" (ovvero quando finisce di caricare un file)
                            }
                        }
                    });
                }
            }

            function setTime() {
                if (videoPlayer !== null && typeof videoPlayer !== 'undefined' && videoPlayer.src()) {
                    var relativeInput = $(event.target).next("input");
                    var currentTime = videoPlayer.currentTime();
                    currentTime = parseInt(currentTime * 100); // aggiungo 2 cifre decimali
                    currentTime = currentTime / 100;
                    relativeInput.val(currentTime);
                }
            }

            function setModality(type) {
                $("#mainContainer").addClass("uk-hidden");

                if (type === 1) {
                    dropzone.options.modality = 1;
                    $("#completeVideoContainer").removeClass("uk-hidden");
                } else {
                    // imposto upload multiplo
                    dropzone.options.modality = 2;
                    dropzone.options.maxFiles = null;
                    var fileInput = dropzone.hiddenFileInput;
                    if (fileInput) {
                        fileInput.setAttribute('multiple', 'multiple');
                    }
                    $("#partialVideoContainer").removeClass("uk-hidden");
                }
                $("#commonContainer").removeClass("uk-hidden");
            }

            function filterCompetitions() {
                var selectValue = $('#countrySelect').val();
                var countryId = -1;
                var internationalCountry = -1;
                if (selectValue.includes("INT-")) {
                    internationalCountry = parseInt(selectValue.replace("INT-", ""));
                } else {
                    countryId = parseInt(selectValue);
                }
                $('#competitionSelect option').remove();
                $('#fixtureSelect option').remove();

                $('#competitionSelect').append($('<option>'));
            <c:forEach var="competition" items="${mCompetitions}">
                if (countryId === ${competition.countryId} || internationalCountry === ${competition.internationalCompetitionId}) {
                    $('#competitionSelect').append($('<option>', {
                        value: ${competition.id == 0 ? competition.internationalCompetitionId : competition.id},
                        text: "${competition.name}"
                    }));
                }
            </c:forEach>
            }

            var fixtures;
            function updateFixtures() {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/myuploadFixtures.htm",
                    data: encodeURI("competitionId=" + $("#competitionSelect").val()),
                    cache: false,
                    success: function (json) {
                        if (json) {
                            // console.log(json);
                            fixtures = JSON.parse(json);

                            $('#fixtureSelect option').remove();
                            $('#fixtureSelect').append($('<option>'));
                            fixtures.data.forEach(function (element) {
                                // console.log(element);
                                $('#fixtureSelect').append($('<option>', {
                                    value: element.fixtureId,
                                    text: element.homeTeam + " - " + element.awayTeam + " | " + element.gameDate
                                }));
                            });
                        }
                    }
                });
            }

            function checkFixtureData() {
                var fixtureId = $('#fixtureSelect').val();
                $("#homeScore").val("");
                $("#homeScore").removeAttr("disabled");
                $("#awayScore").val("");
                $("#awayScore").removeAttr("disabled");

                if (fixtureId) {
                    $("#homeTeamSelect").attr("disabled", "disabled");
                    $("#awayTeamSelect").attr("disabled", "disabled");
                    $("#gameDate").attr("disabled", "disabled");

                    fixtures.data.forEach(function (element) {
                        if (element.fixtureId === parseInt(fixtureId)) {
                            if (element.homeScore !== null) {
                                $("#homeScore").val(element.homeScore);
                                $("#homeScore").attr("disabled", "disabled");
                            }
                            if (element.awayScore !== null) {
                                $("#awayScore").val(element.awayScore);
                                $("#awayScore").attr("disabled", "disabled");
                            }
                        }
                    });
                } else {
                    $("#homeTeamSelect").removeAttr("disabled");
                    $("#awayTeamSelect").removeAttr("disabled");
                    $("#gameDate").removeAttr("disabled");
                }
            }

            function manageInputField(selectId) {
                $("#" + selectId).toggleClass("uk-hidden");
                $("#" + selectId.replace("Select", "Input")).toggleClass("uk-hidden");
            }

            function switchUploadModality() {
                var checked = $("#upload-modality").is(":checked");
                if (checked) {
                    $("#new-fixture-container").removeClass("uk-hidden");
                    if ($("#competitionSelect").val()) {
                        UIkit.notify("Please check if the game doesn't exists and/or the video is not already uploaded", {status: 'warning', timeout: 2500});
                        jsRefreshMatches(1);
                        // jsRefreshMatchesCalendar($("#competitionSelect").val());
                        $("#calendar-modal").addClass("uk-open");
                        $("#calendar-modal").removeClass("uk-hidden");
                    }
                } else {
                    $("#new-fixture-container").addClass("uk-hidden");
                }
            }

            function jsRefreshMatches(page) {
                var idC = $("#competitionSelect").val();
                refreshMatches('matchesList', 'matches', page, idC);
            }

            function switchMatchday(step) {
                var valDay = parseInt($("#mMatchDay").val());
                if (step === "prev") {
                    valDay = valDay - 1;
                } else if (step === "next") {
                    valDay = valDay + 1;
                } else {
                    valDay = step;
                }

                if (valDay >= 1 && valDay <= parseInt($("#mMaxDay").val())) {
//                    jsLoadingAlpha("#matchDayCalendar");
                    $("#mDay").val(valDay);
                    jsRefreshMatchesCalendar($("#competitionSelect").val());
//                    jsLoadingOff("#matchDayCalendar");
                }
            }

            function closeCalendarModal() {
                $("#calendar-modal").removeClass("uk-open");
                $("#calendar-modal").addClass("uk-hidden");
            }

            function adjustCalendarTable() {
                $("#matchTable").find("tr").find("td.matchesColumnResult2:last-child").remove()
            }
        </script>
    </head>
    <body>
        <%@ include file="header.jsp" %>
        <div id="breadcrumb" class="uk-text-truncate" style="text-transform: uppercase;">
            <ul class="uk-breadcrumb uk-margin-left">
                <li style="text-transform: uppercase;"><a href="/sicstv/user/myhome.htm"><spring:message code="menu.user.mysicstv"/></a></li>
                <li style="text-transform: uppercase;"><a href="/sicstv/user/myupload.htm">UPLOAD</a></li>
            </ul>
        </div>

        <div id="mainContainer" class="center" style="height: 90vh; flex-direction: column">
            <div>
                <h2><spring:message code="menu.upload.modality"/></h2>
            </div>
            <div style="flex-direction: row">
                <button class="uk-button uk-button-large uk-text-bold" onclick="setModality(1);"><spring:message code="menu.upload.whole"/></button>
                <button class="uk-button uk-button-large uk-text-bold" onclick="setModality(2);"><spring:message code="menu.upload.partial"/></button>
            </div>
        </div>

        <div id="commonContainer" class="uk-hidden">
            <div class="center">
                <div id="videoUpload" class="dropzone uk-width-5-10">
                    <div class="dz-message">
                        <h2><spring:message code="menu.upload.selectfile"/></h2>
                    </div>
                </div>
                <div id="videoAttachmentsUpload" class="dropzone uk-width-5-10">
                    <div class="dz-message">
                        <h2><spring:message code="menu.upload.selectattachment"/></h2>
                    </div>
                </div>
            </div>

            <div class="uk-flex">
                <div class="uk-width-5-10">
                    <textarea id="videoDescription" class="uk-textarea uk-width-1-1 uk-form-large" style="resize: none;min-height: 175px;" placeholder="<spring:message code="menu.upload.video.description"/>"></textarea>
                </div>
                <div class="uk-width-5-10 uk-form">
                    <center>
                        <h2><spring:message code="menu.upload.selezione.partita"/></h2>
                    </center>
                    <div class="uk-flex">
                        <div class="uk-flex uk-width-6-10 center">
                            <div class="uk-flex-column uk-width-5-10 center">
                                <p class="uk-margin-remove"><spring:message code="search.country"/></p>
                                <select id="countrySelect" onchange="filterCompetitions();" data-placeholder="<spring:message code="menu.upload.select.country"/>" class="form-control select uk-width-9-10" data-width="1%">
                                    <option></option>
                                    <c:forEach var="country" items="${mCountries}">
                                        <option value="${country.id}">${country.name}</option>
                                    </c:forEach>
                                </select>
                                <p class="uk-margin-remove"><spring:message code="player.report.competizione"/><i class="uk-margin-small-left cursor-pointer uk-icon-pencil" onclick="manageInputField('competitionSelect');"></i></p>
                                <select id="competitionSelect" onchange="updateFixtures();" data-placeholder="<spring:message code="menu.upload.select.competition"/>" class="form-control select uk-width-9-10" data-width="1%">
                                    <option></option>
                                    <c:forEach var="competition" items="${mCompetitions}">
                                        <option value="${competition.id}">${competition.name}</option>
                                    </c:forEach>
                                </select>
                                <input type="text" id="competitionInput" class="uk-width-9-10 uk-hidden">
                                <p class="uk-margin-remove"><spring:message code="menu.admin.partita"/></p>
                                <select id="fixtureSelect" data-placeholder="<spring:message code="menu.upload.select.fixture"/>" class="form-control select uk-width-10-10" data-width="1%" onchange="checkFixtureData();">
                                    <option></option>
                                </select>
                                <div class="uk-flex uk-width-10-10">
                                    <div class="uk-flex-column uk-width-5-10 center">
                                        <p class="uk-margin-remove"><spring:message code="menu.upload.home.score"/></p>
                                        <input type="number" class="uk-width-5-10" min="0" id="homeScore"/>
                                    </div>
                                    <div class="uk-flex-column uk-width-5-10 center">
                                        <p class="uk-margin-remove"><spring:message code="menu.upload.away.score"/></p>
                                        <input type="number" class="uk-width-5-10" min="0" id="awayScore"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="uk-flex-column uk-width-4-10 center-vertically">
                            <div class="center uk-margin-left-small uk-margin-bottom">
                                <span class="uk-margin-right-small">New Game</span>
                                <label class="uk-switch" for="upload-modality">
                                    <input type="checkbox" class="uk-hidden" id="upload-modality" onchange="switchUploadModality();">
                                    <div class="uk-switch-slider"></div>
                                </label>
                            </div>
                            <div class="uk-flex-column uk-width-5-10 center uk-hidden" id="new-fixture-container">
                                <p class="uk-margin-remove"><spring:message code="home.team"/><i class="uk-margin-small-left cursor-pointer uk-icon-pencil" onclick="manageInputField('homeTeamSelect');"></i></p>
                                <select id="homeTeamSelect" data-placeholder="<spring:message code="home.team"/>" class="form-control select uk-width-9-10" data-width="1%">
                                    <option></option>
                                    <c:forEach var="team" items="${mTeams}">
                                        <option value="${team.name}">${team.name}</option>
                                    </c:forEach>
                                </select>
                                <input type="text" id="homeTeamInput" class="uk-width-9-10 uk-hidden">
                                <p class="uk-margin-remove"><spring:message code="away.team"/><i class="uk-margin-small-left cursor-pointer uk-icon-pencil" onclick="manageInputField('awayTeamSelect');"></i></p>
                                <select id="awayTeamSelect" data-placeholder="<spring:message code="away.team"/>" class="form-control select uk-width-9-10" data-width="1%">
                                    <option></option>
                                    <c:forEach var="team" items="${mTeams}">
                                        <option value="${team.name}">${team.name}</option>
                                    </c:forEach>
                                </select>
                                <input type="text" id="awayTeamInput" class="uk-width-9-10 uk-hidden">
                                <p class="uk-margin-remove"><spring:message code="lib.game.date"/></p>
                                <input type="text" id="gameDate" class="uk-width-9-10" data-uk-datepicker="{format:'DD/MM/YYYY'}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="uk-margin-top uk-hidden" id="browserWarning">
                <center>
                    <h2 class="uk-text-bold" style="color: red"><spring:message code="menu.upload.warning"/></h2>
                </center>
            </div>
            <div class="uk-margin-top uk-hidden" id="browserSuccess">
                <center>
                    <h2 class="uk-text-bold" style="color: green"><spring:message code="menu.upload.success"/></h2>
                </center>
            </div>

            <div class="uk-margin-top-big center uk-width-5-10">
                <button class="uk-button uk-button-large uk-text-bold" id="upload-button" onclick="uploadFile();"><spring:message code="menu.upload.begin"/></button>
            </div>
        </div>

        <div id="partialVideoContainer" class="uk-hidden">

        </div>

        <div id="completeVideoContainer" class="uk-hidden">
            <div class="uk-flex" style="padding: 2px">
                <div class="uk-width-5-10" style="margin-right: 2px">
                    <div class="uk-form">
                        <center>
                            <h2 class="uk-margin-remove"><spring:message code="menu.upload.video.configuration"/></h2>
                            <p class="uk-margin-top-remove"><spring:message code="menu.upload.video.configuration.description"/></p>
                        </center>
                        <div class="uk-width-5-10 uk-flex center-vertically uk-padding-top uk-padding-left">
                            <p class="uk-width-2-10 uk-margin-remove"><spring:message code="menu.upload.primo.tempo.inizio"/></p>
                            <img width="25px" src='/sicstv/images/confirm.svg' onclick="setTime();" style="cursor: pointer"/>
                            <input type="number" class="uk-width-3-10" id="firstTimeStart" disabled/>
                            <p class="uk-width-2-10 uk-margin-remove" style="margin-left: 5px !important"><spring:message code="menu.upload.primo.tempo.fine"/></p>
                            <img width="25px" src='/sicstv/images/confirm.svg' onclick="setTime();" style="cursor: pointer"/>
                            <input type="number" class="uk-width-3-10" id="firstTimeEnd" disabled/>
                        </div>
                        <div class="uk-width-5-10 uk-flex center-vertically uk-padding-top uk-padding-left">
                            <p class="uk-width-2-10 uk-margin-remove"><spring:message code="menu.upload.secondo.tempo.inizio"/></p>
                            <img width="25px" src='/sicstv/images/confirm.svg' onclick="setTime();" style="cursor: pointer"/>
                            <input type="number" class="uk-width-3-10" id="secondTimeStart" disabled/>
                            <p class="uk-width-2-10 uk-margin-remove" style="margin-left: 5px !important"><spring:message code="menu.upload.secondo.tempo.fine"/></p>
                            <img width="25px" src='/sicstv/images/confirm.svg' onclick="setTime();" style="cursor: pointer"/>
                            <input type="number" class="uk-width-3-10" id="secondTimeEnd" disabled/>
                        </div>
                        <div class="uk-width-5-10 uk-flex center-vertically uk-padding-top uk-padding-left">
                            <p class="uk-width-2-10 uk-margin-remove"><spring:message code="menu.upload.terzo.tempo.inizio"/></p>
                            <img width="25px" src='/sicstv/images/confirm.svg' onclick="setTime();" style="cursor: pointer"/>
                            <input type="number" class="uk-width-3-10" id="thirdTimeStart" disabled/>
                            <p class="uk-width-2-10 uk-margin-remove" style="margin-left: 5px !important"><spring:message code="menu.upload.terzo.tempo.fine"/></p>
                            <img width="25px" src='/sicstv/images/confirm.svg' onclick="setTime();" style="cursor: pointer"/>
                            <input type="number" class="uk-width-3-10" id="thirdTimeEnd" disabled/>
                        </div>
                        <div class="uk-width-5-10 uk-flex center-vertically uk-padding-top uk-padding-left">
                            <p class="uk-width-2-10 uk-margin-remove"><spring:message code="menu.upload.quarto.tempo.inizio"/></p>
                            <img width="25px" src='/sicstv/images/confirm.svg' onclick="setTime();" style="cursor: pointer"/>
                            <input type="number" class="uk-width-3-10" id="fourthTimeStart" disabled/>
                            <p class="uk-width-2-10 uk-margin-remove" style="margin-left: 5px !important"><spring:message code="menu.upload.quarto.tempo.fine"/></p>
                            <img width="25px" src='/sicstv/images/confirm.svg' onclick="setTime();" style="cursor: pointer"/>
                            <input type="number" class="uk-width-3-10" id="fourthTimeEnd" disabled/>
                        </div>
                        <div class="uk-width-5-10 uk-flex center-vertically uk-padding-top uk-padding-left">
                            <p class="uk-width-2-10 uk-margin-remove"><spring:message code="menu.upload.quinto.tempo.inizio"/></p>
                            <img width="25px" src='/sicstv/images/confirm.svg' onclick="setTime();" style="cursor: pointer"/>
                            <input type="number" class="uk-width-3-10" id="fifthTimeStart" disabled/>
                            <p class="uk-width-2-10 uk-margin-remove" style="margin-left: 5px !important"><spring:message code="menu.upload.quinto.tempo.fine"/></p>
                            <img width="25px" src='/sicstv/images/confirm.svg' onclick="setTime();" style="cursor: pointer"/>
                            <input type="number" class="uk-width-3-10" id="fifthTimeEnd" disabled/>
                        </div>
                    </div>
                </div>

                <div class="uk-width-5-10" id="videoPlayerContainer" style="margin-top: -5em">
                    <center>
                        <h2 class="uk-margin-remove"><spring:message code="menu.upload.video.player"/></h2>
                        <p class="uk-margin-top-remove"><spring:message code="menu.upload.video.player.description"/></p>
                    </center>
                    <video id="preview" class="uk-width-1-1 video-js vjs-default-skin" controls preload muted autoplay></video>
                </div>
            </div>
        </div>

        <div id="calendar-modal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeCalendarModal();">&times;</span>
                    <div id="matches">

                    </div>
                </div>
            </div>
        </div>
    </body>
</html>
