<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/css/flat/flat.css" rel="stylesheet">
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />

        <!-- Keep here: needed for teamStats.jsp -->
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/3.2.1/css/fixedColumns.dataTables.min.css"/>
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdn.datatables.net/fixedcolumns/3.2.1/js/dataTables.fixedColumns.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.colVis.min.js"></script>
        <!-- Keep here: needed for teamStats.jsp for buttons (export) -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
        <script src="https://cdn.datatables.net/colreorder/1.7.0/js/dataTables.colReorder.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>

        <!-- D3.js Library for canvas/svg -->
        <script src="https://d3js.org/d3.v5.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.1/d3-tip.min.js"></script>
        <script src="/sicstv/js/sics-canvas-shadow-teams.js?<%=System.currentTimeMillis()%>"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <script type="text/javascript">
            var currentPlayerPersonalId = "", currentPlayerPersonalAction;
            $(document).ready(function () {
                loadPlayerPersonal();
            });

            function loadPlayerPersonal() {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/getPlayerPersonalList.htm",
                    data: encodeURI("all=" + $("#showAllCheckbox").is(":checked")),
                    cache: false,
                    success: function (message) {
                        $("#container").empty();
                        $("#container").append(message);
                    }
                });
            }

            function editPlayerPersonal(id) {
                var playerId = "";
                if (typeof id !== "undefined") {
                    playerId = id;
                }
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/getPlayerPersonal.htm",
                    dataType: 'json',
                    contentType: 'application/json; charset=UTF-8',
                    cache: false,
                    data: "playerId=" + playerId,
                    success: function (player) {
                        console.log(player);
                        if (!$.isEmptyObject(player)) {
                            currentPlayerPersonalAction = 1;
                            currentPlayerPersonalId = player.id;
                            $("#personalFirstName").val(player.firstName);
                            $("#personalLastName").val(player.lastName);
                            $("#personalKnownName").val(player.knownName);
                            $("#personalBornDate").val(player.bornDate);
                            $("#personalHeight").val(player.height);
                            $("#personalFootSelect").val("" + player.footId + "");
                            $("#personalPositionSelect").val("" + player.positionId + "");
                            $("#personalOwnerTeam").val(player.ownerTeam);
                            if (player.shared) {
                                $("#personalShared").attr("checked", "checked");
                            } else {
                                $("#personalShared").removeAttr("checked");
                            }
                        } else {
                            currentPlayerPersonalAction = 0;
                            $("#personalFirstName").val("");
                            $("#personalLastName").val("");
                            $("#personalKnownName").val("");
                            $("#personalBornDate").val("");
                            $("#personalHeight").val("");
                            $("#personalFootSelect").val("0");
                            $("#personalPositionSelect").val("0");
                            $("#personalOwnerTeam").val("");
                            $("#personalShared").removeAttr("checked");
                        }

                        $("#editPlayerPersonal").addClass("uk-open");
                        $("#editPlayerPersonal").removeClass("uk-hidden");
                    }
                });
            }

            function closeEditPlayerPersonal() {
                $("#editPlayerPersonal").removeClass("uk-open");
                $("#editPlayerPersonal").addClass("uk-hidden");
            }

            function savePlayerPersonal() {
                if ($("#personalFirstName").val().length === 0 || $("#personalLastName").val().length === 0) {
                    UIkit.notify("<spring:message code="private.db.player.first.last.required"/>", {status: 'danger', timeout: 1000});
                    return;
                }
                
                var firstName = $("#personalFirstName").val();
                var lastName = $("#personalLastName").val();
                var knownName = $("#personalKnownName").val();
                var bornDate = $("#personalBornDate").val();
                var height = $("#personalHeight").val();
                var footId = $("#personalFootSelect").val() === "0" ? null : $("#personalFootSelect").val();
                var positionId = $("#personalPositionSelect").val() === "0" ? null : $("#personalPositionSelect").val();
                var ownerTeam = $("#personalOwnerTeam").val();
                var shared = $("#personalShared").is(":checked");
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/managePlayerPersonal.htm",
                    cache: false,
                    data: encodeURI("personalId=" + currentPlayerPersonalId + "&firstName=" + firstName + "&lastName=" + lastName + "&knownName=" + knownName + "&bornDate=" + bornDate + "&height=" + height + "&footId=" + (footId === null ? "" : footId) + "&positionId=" + (positionId === null ? "" : positionId) + "&ownerTeam=" + ownerTeam + "&shared=" + shared + "&action=" + currentPlayerPersonalAction),
                    success: function (result) {
                        if (result === "ko") {
                            UIkit.notify("<spring:message code="generic.error"/>", {status: 'danger', timeout: 1000});
                        } else {
                            if (currentPlayerPersonalAction === 1) {
                                UIkit.notify("<spring:message code="private.db.player.updated"/>", {status: 'success', timeout: 1000});
                            } else {
                                UIkit.notify("<spring:message code="private.db.player.created"/>", {status: 'success', timeout: 1000});
                            }
                            location.reload();
                        }
                    }
                });
            }
            
            function deletePlayerPersonal(id) {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/managePlayerPersonal.htm",
                    cache: false,
                    data: encodeURI("personalId=" + id + "&action=2"),
                    success: function (result) {
                        if (result === "ko") {
                            UIkit.notify("<spring:message code="generic.error"/>", {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="private.db.player.deleted"/>", {status: 'success', timeout: 1000});
                            location.reload();
                        }
                    }
                });
            }
        </script>
    </head>
    <body>
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>

        <div id="breadcrumb">
            <ul class="uk-breadcrumb uk-margin-left ">
                <li><spring:message code="private.db.title"/></li>
            </ul>
        </div>
        
        <div class="uk-flex uk-height-1-1">
            <div class="uk-width-2-10 uk-margin-left" style="border-right: 1px solid black">
                <div class="uk-margin-small-top">
                    <center>
                        <h3 class="uk-text-uppercase"><spring:message code="private.db.players"/></h3>
                    </center>
                </div>
            </div>
            <div id="container" class="uk-width-8-10">

            </div>
        </div>

        <div id="editPlayerPersonal" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height: 0; min-width: 0; max-width: 25%; width: auto; height: auto;">
                <div>
                    <span class="modalClose" onclick="closeEditPlayerPersonal();">&times;</span>
                </div>
                <div class="container uk-margin-top-big" style="min-height: 35vh; max-height: 90vh;">
                    <div class="row">
                        <h2><spring:message code="private.db.edit.player"/></h2>
                    </div>
                    <div class="row">
                        <div class="column uk-width-3-10">
                            <span><spring:message code="private.db.first.name"/> <span class="uk-text-danger">*</span></span>
                        </div>
                        <div class="column uk-width-7-10">
                            <input id="personalFirstName" type="text"/>
                        </div>
                    </div>
                    <div class="row uk-margin-small-top">
                        <div class="column uk-width-3-10">
                            <span><spring:message code="private.db.last.name"/> <span class="uk-text-danger">*</span></span>
                        </div>
                        <div class="column uk-width-7-10">
                            <input id="personalLastName" type="text"/>
                        </div>
                    </div>
                    <div class="row uk-margin-small-top">
                        <div class="column uk-width-3-10">
                            <spring:message code="private.db.known.name"/>
                        </div>
                        <div class="column uk-width-7-10">
                            <input id="personalKnownName" type="text"/>
                        </div>
                    </div>
                    <div class="row uk-margin-small-top">
                        <div class="column uk-width-3-10">
                            <spring:message code="private.db.born.date"/>
                        </div>
                        <div class="column uk-width-7-10">
                            <input type="text" id="personalBornDate" data-uk-datepicker="{format:'YYYY-MM-DD'}">
                        </div>
                    </div>
                    <div class="row uk-margin-small-top">
                        <div class="column uk-width-3-10">
                            <spring:message code="private.db.height"/>
                        </div>
                        <div class="column uk-width-7-10">
                            <input id="personalHeight" min="0" type="number"/>
                        </div>
                    </div>
                    <div class="row uk-margin-small-top">
                        <div class="column uk-width-3-10">
                            <spring:message code="private.db.foot"/>
                        </div>
                        <div class="uk-form column uk-width-7-10">
                            <select id="personalFootSelect" class="uk-form-select uk-width-9-10">
                                <option value="0"></option>
                                <option value="1"><spring:message code="private.db.foot.right"/></option>
                                <option value="2"><spring:message code="private.db.foot.left"/></option>
                                <option value="3"><spring:message code="private.db.foot.both"/></option>
                            </select>
                        </div>
                    </div>
                    <div class="row uk-margin-small-top">
                        <div class="column uk-width-3-10">
                            <spring:message code="private.db.position"/>
                        </div>
                        <div class="uk-form column uk-width-7-10">
                            <select id="personalPositionSelect" class="uk-form-select uk-width-9-10">
                                <option value="0"></option>
                                <option value="1"><spring:message code="private.db.position.goalkeeper"/></option>
                                <option value="2"><spring:message code="private.db.position.defender"/></option>
                                <option value="3"><spring:message code="private.db.position.midfielder"/></option>
                                <option value="4"><spring:message code="private.db.position.attacker"/></option>
                            </select>
                        </div>
                    </div>
                    <div class="row uk-margin-small-top">
                        <div class="column uk-width-3-10">
                            <spring:message code="private.db.owner.team"/>
                        </div>
                        <div class="column uk-width-7-10">
                            <input id="personalOwnerTeam" type="text"/>
                        </div>
                    </div>
                    <div class="row uk-margin-small-top">
                        <div class="column uk-width-3-10">
                            <spring:message code="private.db.shared"/>
                        </div>
                        <div class="column uk-width-7-10">
                            <input type="checkbox" id="personalShared"/>
                        </div>
                    </div>
                </div>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <button style="min-width:80px;" onclick="savePlayerPersonal();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.conferma"/></button>
                </div>
            </div>
        </div>
    </body>
</html>