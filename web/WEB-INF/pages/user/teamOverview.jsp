<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div id="teamOverviewContainer">
    <script>
        var chartData = [];
        <c:forEach var="statName" items="${mCompetitionAverage.keySet()}">
            <c:set var="row" value="${mTeamStats.get(statName)}"/>
            <c:set var="maxStatValue" value="${mTables.get(statName).get(0).eventAmountAverage}"/>
            <c:set var="minStatValue" value="${mTables.get(statName).get(mTables.get(statName).size() - 1).eventAmountAverage}"/>

            <c:if test="${maxStatValue != null}">
                <c:set var="rowPercentage" value="${row.getEventPercentage(maxStatValue, minStatValue)}"/>
                <c:if test="${rowPercentage > 0}">
                    <c:set var="averagePercentage" value="${row.getPercentageByValues(mCompetitionAverage.get(statName), maxStatValue, minStatValue)}"/>
                    <c:set var="color" value="${row.statsTypeIsOpposite ? '#e85f5f' : '#008000'}"/>
                    <c:set var="halfAverage" value="${averagePercentage / 2}"/>
                    <c:set var="secondHalfAverage" value="${(100 - averagePercentage) / 2}"/>
                    <c:choose>
                        <c:when test="${rowPercentage <= halfAverage}">
                            <c:set var="color" value="${row.statsTypeIsOpposite ? '#008000' : '#e85f5f'}"/>
                        </c:when>
                        <c:when test="${rowPercentage <= (averagePercentage - 1)}">
                            <c:set var="color" value="${row.statsTypeIsOpposite ? '#5ab550' : '#e1e85f'}"/>
                        </c:when>
                        <c:when test="${rowPercentage <= (averagePercentage + secondHalfAverage)}">
                            <c:set var="color" value="${row.statsTypeIsOpposite ? '#e1e85f' : '#5ab550'}"/>
                        </c:when>
                    </c:choose>

        chartData.push({category: "${statName}", value: ${rowPercentage}, color: "${color}", tooltip: "<strong>${statName}</strong><br/><spring:message code="report.team.valore"/>: ${row.getEventAmountAverage1Decimal()}<br/><spring:message code="report.player.posizione"/>: ${row.index}�"});
                </c:if>
            </c:if>
        </c:forEach>

        $(document).ready(function () {
            updateSelectedButtons();

            if (localStorage.getItem("sicstv/600/team/overview/view") === "table") {
                $("#overview-click").click();
            }

            $("#competitionListLabel").html($("#competitionListLabel").html() + " (<spring:message code="team.stats.partite"/> ${mGamesAnalyzed}/" + $("#totalMatchesAmount").text() + ")");
            if (chartData.length > 0) {
                createOverviewChart(chartData);
            }
        });

        function openTeamOverviewColumnsModal() {
            $("#teamOverviewColumnsModal").addClass("uk-open");
            $("#teamOverviewColumnsModal").removeClass("uk-hidden");

            // inizializzo a mano lo switcher altrimenti non va per qualche motivo
            if ($("#teamOverviewColumnsButtonul > li.uk-active").length === 0) {
                var customOptions = {
                    connect: '#teamOverviewColumnsButtonContent'
                };
                UIkit.switcher('#teamOverviewColumnsButtonul', customOptions);

                var customOptionsAccordion = {
                    collapse: false,
                    showfirst: true
                };
                $(".accordionDiv").each(function (index, element) {
                    UIkit.accordion("#" + $(element).attr("id"), customOptionsAccordion).on('toggle.uk.accordion', function () {
                        updateSelectedButtons();
                    });
                });
            }
        }

        function closeTeamOverviewColumnsModal() {
            $("#teamOverviewColumnsModal").removeClass("uk-open");
            $("#teamOverviewColumnsModal").addClass("uk-hidden");
        }

        function createSaveTeamOverviewFilter() {
            var filterId;
        <c:if test="${mTeamOverviewFilter != null}">
            filterId = ${mTeamOverviewFilter.id};
        </c:if>

            var selectedColumns = [];
            $("#teamOverviewColumnsButtonContent").find("input:checked").each(function () {
                var columnIndex = $(this).attr("data-value");
                if (typeof columnIndex !== 'undefined') {
                    selectedColumns.push(columnIndex);
                }
            });
            $("#teamOverviewColumnsButtonContent").find("input:not(:checked)").each(function () {
                var columnIndex = $(this).attr("data-value");
                if (typeof columnIndex !== 'undefined') {
                    if (selectedColumns.includes(columnIndex)) {
                        selectedColumns.splice(selectedColumns.indexOf(columnIndex), 1);
                    }
                }
            });
            if (!selectedColumns.includes("1")) {
                selectedColumns.push("1");
            }

            var ajaxData;
            if (typeof filterId !== 'undefined') {
                ajaxData = encodeURI("filterId=" + filterId + "&tableType=TeamOverview&name=teamOverview&columns=" + selectedColumns + "&sort=");
            } else {
                ajaxData = encodeURI("filterId=&tableType=teamOverview&name=teamOverview&columns=" + selectedColumns + "&sort=");
            }

            $.ajax({
                type: "POST",
                url: "/sicstv/user/creaFiltroTeamStats.htm",
                cache: false,
                data: ajaxData,
                success: function (msg) {
                    if (typeof filterId !== 'undefined') {
                        UIkit.notify("<spring:message code="team.stats.update.filter.success"/>", {status: 'success', timeout: 1000});
                    } else {
                        UIkit.notify("<spring:message code="team.stats.create.filter.success"/>", {status: 'success', timeout: 1000});
                    }

                    //loadTeamOverview("filterSicsOverview");
                    // ricarico la pagina altrimenti non si aggiornano i filtri per il report
                    window.location.reload();
                },
                error: function () {
                    if (typeof currentFilter !== 'undefined') {
                        UIkit.notify("<spring:message code="team.stats.update.filter.error"/>", {status: 'danger', timeout: 1000});
                    } else {
                        UIkit.notify("<spring:message code="team.stats.create.filter.error"/>", {status: 'danger', timeout: 1000});
                    }
                }
            });
        }

        function deleteTeamOverviewFilter() {
            if (window.confirm("<spring:message code="team.stats.reset.question"/>")) {
                var filterId;
        <c:if test="${mTeamOverviewFilter != null}">
                filterId = ${mTeamOverviewFilter.id};
        </c:if>

                if (typeof filterId !== 'undefined') {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/cancellaFiltroTeamStats.htm",
                        cache: false,
                        data: encodeURI("&filterId=" + filterId),
                        success: function (msg) {
                            if (msg === 'true') {
                                UIkit.notify("<spring:message code="team.stats.reset.success"/>", {status: 'success', timeout: 1000});

                                //loadTeamOverview("filterSicsOverview");
                                // ricarico la pagina altrimenti non si aggiornano i filtri per il report
                                sessionStorage.removeItem("statsTypeLastFilterId");
                                window.location.reload();
                            } else {
                                UIkit.notify("<spring:message code="team.stats.reset.error"/>", {status: 'danger', timeout: 1000});
                            }
                        },
                        error: function () {
                            UIkit.notify("<spring:message code="team.stats.reset.error"/>", {status: 'danger', timeout: 1000});
                        }
                    });
                }
            }
        }

        function selectAllStats() {
            $("#teamOverviewColumnsButtonContent > li.uk-active").find("input:not(:checked)").each(function (index, element) {
                manageColumnCheck($(element).attr("data-value"));
            });
        }

        function deselectAllStats() {
            $("#teamOverviewColumnsButtonContent > li.uk-active").find("input:checked").each(function (index, element) {
                manageColumnCheck($(element).attr("data-value"));
            });
        }

        function updateSelectedButtons() {
            // ogni volta riparto da 0 per evitare di dover gestire tutto
            $("#teamOverviewColumnsModal").find("button.uk-button-active").removeClass("uk-button-active");
            $(".uk-accordion-content").find("button").find("i:not(.skip-reload)").removeClass("uk-icon-caret-down").addClass("uk-icon-caret-right");

            // ora attivo quelli che hanno almeno un input con il checked
            $(".uk-modal-dialog input").each(function (index, element) {
                if ($(element).prop("checked")) {
                    $(element).closest("div.width-1-4-x").find("button").addClass("uk-button-active");
                }
            });

            // gestione icona "aperto" e "chiuso"
            $(".uk-accordion-content").find("button.uk-active").find("i.uk-icon-caret-right:not(.skip-reload)").removeClass("uk-icon-caret-right").addClass("uk-icon-caret-down");
        }

        function manageColumnCheck(typeId, specificCheck) {
            // aggiorno solo le checkbox del modale delle colonne, altrimenti segna la colonna anche come filtro
            var fieldName = "statsType" + typeId;
            var isChecked = $("#teamOverviewColumnsModal").find("." + fieldName + "[checked]").length > 0;
            if (isChecked && (typeof specificCheck === "undefined" || !specificCheck)) {
                $("#teamOverviewColumnsModal").find("." + fieldName).removeAttr("checked");
            } else {
                $("#teamOverviewColumnsModal").find("." + fieldName).attr("checked", "checked");
            }
            $("#param-search").val(null).trigger("change");
            $("#param-search-container").removeClass("uk-open");

            updateSelectedButtons();
        }

        function manageGroupStats(element) {
            event.preventDefault();
            event.stopPropagation();

            var specificCheck = false;
            if ($(element).attr("class").includes("uk-icon-square-o")) {
                specificCheck = true;
                $(element).attr("class", $(element).attr("class").replace("uk-icon-square-o", "uk-icon-check-square-o"));
            } else {
                $(element).attr("class", $(element).attr("class").replace("uk-icon-check-square-o", "uk-icon-square-o"));
            }

            $(element).parent().parent().find("input").each(function (index, input) {
                var typeId = $(input).attr("data-value");
                manageColumnCheck(typeId, specificCheck);
            });
        }

        function changeOverview() {
            $(".swap-me").toggleClass("uk-hidden");
            $("#tableDiv").toggleClass("uk-width-1-2");

            if ($("#tableDiv").hasClass("uk-width-1-2")) {
                localStorage.setItem("sicstv/600/team/overview/view", "chart");
            } else {
                localStorage.setItem("sicstv/600/team/overview/view", "table");
            }
        }

        function searchParams() {
            var input = $("#param-search").val().toLowerCase();
            if (input && input.length >= 2) {
                $("#param-search-result-container").empty();

                var valid = [];
                $("#teamOverviewColumnsButton").find(".teamOverview-param").each(function (index, element) {
                    var metric = $(element).text().trim();
                    if (metric.toLowerCase().includes(input)) {
                        var validText = metric + "|" + $(element).attr("stats-id");
                        if (!valid.includes(validText)) {
                            valid.push(validText);
                        }
                    }
                });

                if (valid.length > 0) {
                    valid.forEach(function (element) {
                        var splitted = element.split("|");

                        var newElement = "<li class='uk-hover'>";
                        newElement += "<a onclick='manageColumnCheck(" + splitted[1] + ", true);'>" + splitted[0] + "</a>";
                        newElement += "</li>";
                        $("#param-search-result-container").append(newElement);
                    });

                    $("#param-search-container").addClass("uk-open");
                }
            } else {
                $("#param-search-container").removeClass("uk-open");
            }
        }
    </script>

    <style>
        #chartdiv {
            width: 90%;
            height: 50vh;
        }
    </style>

    <c:if test="${mCompetitions != null && !mCompetitions.isEmpty()}">
        <c:set var="isMultiCompetition" value="false"/>
        <c:set var="competitionList" value=""/>
        <c:set var="competitionIdList" value=""/>
        <c:forEach var="competition" items="${mCompetitions}">
            <c:if test="${!competitionList.isEmpty()}">
                <c:set var="isMultiCompetition" value="true"/>
                <c:set var="competitionList" value="${competitionList} / "/>
            </c:if>
            <c:set var="competitionList" value="${competitionList}${competition.name}"/>
            <c:set var="competitionIdList" value="${competitionIdList}${competition.id}"/>
        </c:forEach>
    </c:if>

    <button class="uk-button" onclick="openTeamOverviewColumnsModal()"><spring:message code='team.stats.colvis.title'/></button>
    <c:if test="${mDivideStatistics}">
        <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse; margin-bottom: 5px; margin-top: 0px">
            <tbody>
                <!-- Colonne -->
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                    <td class="tableHeader"><spring:message code="report.player.statistica"/></td>
                    <td class="tableHeader"><spring:message code="report.player.posizione"/></td>
                    <td class="tableHeader"><spring:message code="report.team.valore"/></td>
                    <td class="tableHeader" style="width: 45%; font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="team.stats.variabili.offensive"/></td>
                    <td class="tableHeader"><spring:message code="report.team.media.campionato"/></td>
                    <td class="tableHeader"><spring:message code="report.team.valore.massimo"/></td>
                </tr>
                <c:forEach var="statName" items="${mCompetitionAverage.keySet()}">
                    <c:set var="row" value="${mTeamStats.get(statName)}"/>
                    <c:if test="${!row.statsTypeIsOpposite}">
                        <c:set var="maxStatValue" value="${mTables.get(statName).get(0).eventAmountAverage}"/>
                        <c:set var="minStatValue" value="${mTables.get(statName).get(mTables.get(statName).size() - 1).eventAmountAverage}"/>

                        <c:if test="${maxStatValue != null}">
                            <c:set var="rowPercentage" value="${row.getEventPercentage(maxStatValue, minStatValue)}"/>
                            <tr>
                                <c:if test="${row.filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${isMultiCompetition == 'true' ? -1 : competitionIdList}&idTeam=${mTeam.id}&idPlayer=&goals=false&event=${row.getFilterEncoded()}&filter=&limit=50"/>
                                    <td><a href="/sicstv/user/goTo.htm?seasonId=${mSeasonId}&link=${row.encodeString(link)}" style="text-decoration: underline">${statName} (${row.getEventAmountFormatted()})</a></td>
                                </c:if>
                                <c:if test="${row.filter == null}">
                                    <td>${statName} (${row.getEventAmountFormatted()})</td>
                                </c:if>
                                <td class="tableRow">${row.index}�</td>
                                <td class="tableRow">${row.getEventAmountAverage1Decimal()}</td>
                                <td>
                                    <div class="container-progress-bar">
                                        <c:set var="averagePercentage" value="${row.getPercentageByValues(mCompetitionAverage.get(statName), maxStatValue, minStatValue)}"/>

                                        <div style="margin-left: ${averagePercentage}%; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                        <c:set var="color" value="${row.statsTypeIsOpposite ? '#e85f5f' : 'green'}"/>
                                        <c:set var="halfAverage" value="${averagePercentage / 2}"/>
                                        <c:set var="secondHalfAverage" value="${(100 - averagePercentage) / 2}"/>
                                        <c:choose>
                                            <c:when test="${rowPercentage <= halfAverage}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? 'green' : '#e85f5f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage - 1)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#5ab550' : '#e1e85f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage + secondHalfAverage)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#e1e85f' : '#5ab550'}"/>
                                            </c:when>
                                        </c:choose>
                                        <div class="progress" statsTypeIsOpposite="${row.statsTypeIsOpposite}" halfAverage="${halfAverage}" secondHalfAverage="${secondHalfAverage}" rowPercentage="${rowPercentage}" style="margin-left: -${averagePercentage}%; width: ${rowPercentage}%; background-color: ${color} !important">
                                        </div>
                                    </div>
                                </td>
                                <td class="tableRow">${mStatsFunctions.valueWith1Decimal(mCompetitionAverage.get(statName))}</td>
                                <td class="tableRow">${mTables.get(statName).get(0).getEventAmountAverage1Decimal()}</td>
                            </tr>
                        </c:if>
                    </c:if>
                </c:forEach>
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                    <td class="tableHeader"><spring:message code="report.player.statistica"/></td>
                    <td class="tableHeader"><spring:message code="report.player.posizione"/></td>
                    <td class="tableHeader"><spring:message code="report.team.valore"/></td>
                    <td class="tableHeader" style="width: 45%; font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="team.stats.variabili.difensive"/></td>
                    <td class="tableHeader"><spring:message code="report.team.media.campionato"/></td>
                    <td class="tableHeader"><spring:message code="report.team.valore.massimo"/></td>
                </tr>
                <c:forEach var="statName" items="${mCompetitionAverage.keySet()}">
                    <c:set var="row" value="${mTeamStats.get(statName)}"/>
                    <c:if test="${row.statsTypeIsOpposite}">
                        <c:set var="maxStatValue" value="${mTables.get(statName).get(0).eventAmountAverage}"/>
                        <c:set var="minStatValue" value="${mTables.get(statName).get(mTables.get(statName).size() - 1).eventAmountAverage}"/>

                        <c:if test="${maxStatValue != null}">
                            <c:set var="rowPercentage" value="${row.getEventPercentage(maxStatValue, minStatValue)}"/>
                            <tr>
                                <c:if test="${row.filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${isMultiCompetition == 'true' ? -1 : competitionIdList}&idTeam=${mTeam.id}&idPlayer=&goals=false&event=${row.getFilterEncoded()}&filter=&limit=50"/>
                                    <td><a href="/sicstv/user/goTo.htm?seasonId=${mSeasonId}&link=${row.encodeString(link)}" style="text-decoration: underline">${statName} (${row.getEventAmountFormatted()})</a></td>
                                </c:if>
                                <c:if test="${row.filter == null}">
                                    <td>${statName} (${row.getEventAmountFormatted()})</td>
                                </c:if>
                                <td class="tableRow">${row.index}�</td>
                                <td class="tableRow">${row.getEventAmountAverage1Decimal()}</td>
                                <td>
                                    <div class="container-progress-bar">
                                        <c:set var="averagePercentage" value="${row.getPercentageByValues(mCompetitionAverage.get(statName), maxStatValue, minStatValue)}"/>

                                        <div style="margin-left: ${averagePercentage}%; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                        <c:set var="color" value="${row.statsTypeIsOpposite ? '#e85f5f' : 'green'}"/>
                                        <c:set var="halfAverage" value="${averagePercentage / 2}"/>
                                        <c:set var="secondHalfAverage" value="${(100 - averagePercentage) / 2}"/>
                                        <c:choose>
                                            <c:when test="${rowPercentage <= halfAverage}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? 'green' : '#e85f5f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage - 1)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#5ab550' : '#e1e85f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage + secondHalfAverage)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#e1e85f' : '#5ab550'}"/>
                                            </c:when>
                                        </c:choose>
                                        <div class="progress" statsTypeIsOpposite="${row.statsTypeIsOpposite}" halfAverage="${halfAverage}" secondHalfAverage="${secondHalfAverage}" rowPercentage="${rowPercentage}" style="margin-left: -${averagePercentage}%; width: ${rowPercentage}%; background-color: ${color} !important">
                                        </div>
                                    </div>
                                </td>
                                <td class="tableRow">${mStatsFunctions.valueWith1Decimal(mCompetitionAverage.get(statName))}</td>
                                <td class="tableRow">${mTables.get(statName).get(0).getEventAmountAverage1Decimal()}</td>
                            </tr>
                        </c:if>
                    </c:if>
                </c:forEach>
            </tbody>
        </table>
    </c:if>
    <c:if test="${!mDivideStatistics}">
        <div class="center">
            <span class="uk-margin-right-small"><spring:message code="overview.chart"/></span>
            <label class="uk-switch" for="overviewView">
                <input type="checkbox" id="overviewView" onchange="changeOverview();">
                <div class="uk-switch-slider" id="overview-click"></div>
            </label>
            <span class="uk-margin-left-small"><spring:message code="overview.table"/></span>
        </div>
        <div class="uk-flex">
            <div id="chartdiv" class="swap-me">

            </div>
            <table id="tableDiv" class="uk-table uk-table-hover uk-table-striped uk-width-1-2" style="border-collapse: collapse; margin-bottom: 5px; margin-top: 0px">
                <tbody>
                    <!-- Colonne -->
                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                        <td class="tableHeader"><spring:message code="report.player.statistica"/></td>
                        <td class="tableHeader"><spring:message code="report.player.posizione"/></td>
                        <td class="tableHeader"><spring:message code="report.team.valore"/></td>
                        <td class="tableHeader uk-hidden swap-me" style="width: 45%"></td>
                        <td class="tableHeader"><spring:message code="report.team.media.campionato"/></td>
                        <td class="tableHeader"><spring:message code="report.team.valore.massimo"/></td>
                    </tr>
                    <c:forEach var="statName" items="${mCompetitionAverage.keySet()}">
                        <c:set var="row" value="${mTeamStats.get(statName)}"/>
                        <c:set var="maxStatValue" value="${mTables.get(statName).get(0).eventAmountAverage}"/>
                        <c:set var="minStatValue" value="${mTables.get(statName).get(mTables.get(statName).size() - 1).eventAmountAverage}"/>

                        <c:if test="${maxStatValue != null}">
                            <c:set var="rowPercentage" value="${row.getEventPercentage(maxStatValue, minStatValue)}"/>
                            <tr>
                                <c:if test="${row.filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${isMultiCompetition == 'true' ? -1 : competitionIdList}&idTeam=${mTeam.id}&idPlayer=&goals=false&event=${row.getFilterEncoded()}&filter=&limit=50"/>
                                    <td><a href="/sicstv/user/goTo.htm?seasonId=${mSeasonId}&link=${row.encodeString(link)}" style="text-decoration: underline">${statName} (${row.getEventAmountFormatted()})</a></td>
                                </c:if>
                                <c:if test="${row.filter == null}">
                                    <td>${statName} (${row.getEventAmountFormatted()})</td>
                                </c:if>
                                <td class="tableRow">${row.index}�</td>
                                <td class="tableRow">${row.getEventAmountAverage1Decimal()}</td>
                                <td class="uk-hidden swap-me">
                                    <div class="container-progress-bar">
                                        <c:set var="averagePercentage" value="${row.getPercentageByValues(mCompetitionAverage.get(statName), maxStatValue, minStatValue)}"/>

                                        <div style="margin-left: ${averagePercentage}%; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                        <c:set var="color" value="${row.statsTypeIsOpposite ? '#e85f5f' : 'green'}"/>
                                        <c:set var="halfAverage" value="${averagePercentage / 2}"/>
                                        <c:set var="secondHalfAverage" value="${(100 - averagePercentage) / 2}"/>
                                        <c:choose>
                                            <c:when test="${rowPercentage <= halfAverage}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? 'green' : '#e85f5f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage - 1)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#5ab550' : '#e1e85f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage + secondHalfAverage)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#e1e85f' : '#5ab550'}"/>
                                            </c:when>
                                        </c:choose>
                                        <div class="progress" statsTypeIsOpposite="${row.statsTypeIsOpposite}" halfAverage="${halfAverage}" secondHalfAverage="${secondHalfAverage}" rowPercentage="${rowPercentage}" style="margin-left: -${averagePercentage}%; width: ${rowPercentage}%; background-color: ${color} !important">
                                        </div>
                                    </div>
                                </td>
                                <td class="tableRow">${mStatsFunctions.valueWith1Decimal(mCompetitionAverage.get(statName))}</td>
                                <td class="tableRow">${mTables.get(statName).get(0).getEventAmountAverage1Decimal()}</td>
                            </tr>
                        </c:if>
                    </c:forEach>
                </tbody>
            </table>
        </div>
    </c:if>
    <p id="competitionListLabel" style="width: 100%; text-align: center; margin: 0; font-size: 11px"><spring:message code="player.profile.competition.list"/>: ${competitionList}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if></p>

        <div id="teamOverviewColumnsModal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 55%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeTeamOverviewColumnsModal();">&times;</span>
                    <h3><spring:message code="team.stats.colvis.title"/></h3>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <div id="teamOverviewColumnsButton" style="height: 50vh; overflow-y: auto">                            
                        <!-- viene inizializzato tramite Javascript funzione manageColumnsModal() -->
                        <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-float-left" id="teamOverviewColumnsButtonul" data-uk-tab="{connect:'#teamOverviewColumnsButtonContent'}" style="margin-bottom: 10px">
                            <c:set var="isFirst" value="true"/>
                            <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                                <li><a style="border-top: 2px solid #dddddd;">${statGroup}</a></li>
                                    <c:set var="isFirst" value="false"/>
                                </c:forEach>
                        </ul>
                        <div class="uk-float-right uk-autocomplete uk-form" id="param-search-container">
                            <label><spring:message code="video.ricerca"/>:</label>
                            <input type="text" id="param-search" oninput="searchParams();" class="uk-form-width-medium"/>
                            <div class="uk-dropdown" aria-expanded="true">
                                <ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left" id="param-search-result-container">
                                </ul>
                            </div>
                        </div>

                        <ul id="teamOverviewColumnsButtonContent" class="uk-switcher">
                            <c:set var="counter" value="0"/>
                            <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                                <li>
                                    <div id="accordition${counter}" uk-accordion data-uk-accordion="{collapse: false, showfirst: true}" class="uk-margin-remove accordionDiv">
                                        <span class="uk-accordion-title uk-hidden"></span>
                                        <div class="uk-accordion-content uk-width-1-1 uk-padding-remove">
                                            <div class="uk-accordion uk-text-left">
                                                <c:forEach var="group" items="${mGroupedColumns.get(statGroup).keySet()}">
                                                    <div class="width-1-4-x">
                                                        <button class="uk-button uk-accordion-title buttonTag" base-name="${group}" value="" title="${group}">
                                                            <i class="uk-float-left uk-button-mini uk-icon uk-icon-caret-down uk-active"></i>
                                                            ${group}
                                                            <i class="uk-float-right uk-button-mini uk-icon-square-o skip-reload" onclick="manageGroupStats(this);"></i>
                                                        </button>
                                                        <div>
                                                            <div class="uk-accordion-content uk-text-left">
                                                                <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                    <div class="uk-margin-small">
                                                                        <div class="uk-flex">
                                                                            <div style="width: 70%"></div>
                                                                            <div class="uk-margin-right-small" style="width: 10%">Tot.</div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <c:forEach var="stat" items="${mGroupedColumns.get(statGroup).get(group)}">
                                                                    <c:set var="checked" value=""/>
                                                                    <c:if test="${mCompetitionAverage.keySet().contains(stat.desc)}">
                                                                        <c:set var="checked" value="checked"/>
                                                                    </c:if>
                                                                    <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                        <div class="uk-margin-small">
                                                                            <div class="uk-flex">
                                                                                <div class="teamOverview-param" style="width: 70%" stats-id="${stat.id}">
                                                                                    ${stat.desc}
                                                                                </div>
                                                                                <div class="uk-margin-right-small" style="width: 10%">
                                                                                    <input type="checkbox" class="statsType${stat.id}" onclick="manageColumnCheck(${stat.id});" data-value="${stat.id}" ${checked}/>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </c:forEach>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:forEach>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <c:set var="counter" value="${counter + 1}"/>
                            </c:forEach>
                        </ul>
                    </div>
                </div>
                <div class="uk-modal-footer uk-width-1-1" style="display: flex">
                    <div class="uk-text-left">
                        <button style="min-width:80px;" onclick="selectAllStats();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.selezionatutto"/></button>
                        <button style="min-width:80px;" onclick="deselectAllStats();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.deselezionatutto"/></button>
                    </div>
                    <div class="uk-text-right" style="margin-left: auto">
                        <c:if test="${mTeamOverviewFilter != null}">
                            <button style="min-width:80px;" onclick="deleteTeamOverviewFilter();" class="uk-text-large uk-button uk-modal-close" title="<spring:message code="team.stats.reset.table.title"/>"><spring:message code="team.stats.reset.table"/></button>
                        </c:if>
                        <button style="min-width:80px;" onclick="createSaveTeamOverviewFilter();" class="uk-text-large uk-button uk-modal-close"><spring:message code="team.stats.save.table"/></button>
                        <button style="min-width:80px;" onclick="closeTeamOverviewColumnsModal();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>