<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<button class="uk-button" id="favoritesOpenButton" title="<spring:message code='favorites.tooltip'/>" style="margin-top: 5px" onclick="openFavoritesModal();"><i class="uk-icon-heart uk-icon-small"></i></button>

<div id="favoritesModal" class="uk-hidden favorites-content">
    <div style="grid-area: header;">
        <center>
            <span style="font-size: 20px; text-transform: uppercase; font-weight: bold">
                <spring:message code='favorites'/>
            </span>
            <div style="float: right">
                <span class="modalClose" onclick="closeFavoritesModal();" style="top: -3px; right: 10px">&times;</span>
            </div>
        </center>
    </div>
    
    <div style="grid-area: player; min-width: 25em">
        <center><div style="text-transform: uppercase; margin-bottom: 10px;"><i class="uk-icon-user uk-icon-medium"></i> <spring:message code='search.players'/></div></center>
        <c:forEach var="item" items="${mPlayerFavorites}">
            <c:set var="player" value="${mPlayerFavoritesData.get(item.playerId)}"/>
            <div style="margin-bottom: 5px; padding-right: 10px" class="center-vertically">
                <a class="favorites-a" href="/sicstv/user/player.htm?personal=false&playerId=${player.id}">
                    <img height="50px" width="50px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${player.photo}.png" onerror="this.src='/sicstv/images/user_gray.png'" alt="${player.last_name} ${player.first_name}">
                    ${player.last_name} ${player.first_name} (${player.getBornDateYearLast2Digit()})
                </a>
                <div style="margin-left: auto" class="clickRow" onclick="deletePlayerFromFavorites(${player.id})"><i class="uk-icon-trash"></i></div>
            </div>
        </c:forEach>
        <c:if test="${mPlayerFavorites.isEmpty()}">
            <center><div><spring:message code='favorites.no.player'/></div></center>
        </c:if>
    </div>

    <div style="grid-area: team; min-width: 25em">
        <center><div style="text-transform: uppercase; margin-bottom: 10px;"><i class="uk-icon-shield uk-icon-medium"></i> <spring:message code='search.teams'/></div></center>
        <c:forEach var="item" items="${mTeamFavorites}">
            <c:set var="team" value="${mTeamFavoritesData.get(item.teamId)}"/>
            <div style="margin-bottom: 5px; padding-right: 10px" class="center-vertically">
                <a class="favorites-a" href="/sicstv/user/team.htm?personal=false&formCompetitionId=-1&formTeamId=${team.id}">
                    <img height="50px" width="50px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${team.logo}.png" alt="${team.name}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'">
                    ${team.name}
                </a>
                <div style="margin-left: auto" class="clickRow" onclick="deleteTeamFromFavorites(${team.id})"><i class="uk-icon-trash"></i></div>
            </div>
        </c:forEach>
        <c:if test="${mTeamFavorites.isEmpty()}">
            <center><div><spring:message code='favorites.no.team'/></div></center>
        </c:if>
    </div>
</div>