<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/amcharts.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/css/flat/flat.css" rel="stylesheet">
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />

        <!-- D3.js Library for canvas/svg -->
        <script src="https://d3js.org/d3.v5.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.1/d3-tip.min.js"></script>
        <script src="/sicstv/js/sics-canvas-player.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/js/temperatureMap.js?<%=System.currentTimeMillis()%>"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>

        <!-- AmCharts -->
        <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
        <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
        <script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
        <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

        <script type="text/javascript">
            var pageStatus = [];
            var totMatches = [];
            var numMatches = [];
            var matchPerPage = 25;
            var dropzone;
            var baseUrl = "/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}";

            var lastDivCompetitions;
            $(document).ready(function () {
                // Configura Dropzone
                Dropzone.autoDiscover = false;

                var idsToCheck = $("#gameIds").val();
                if (idsToCheck !== '') {
                    selectedMatches = idsToCheck.split(",");
                }

                // jsRefreshMatches(1);
                initSearchFilter();
                readFilterMulti(false);
                filterMatches('allMatches', 1);
                lastDivCompetitions = $('#divCompetitions').html();

                if ($(window).width() <= 768) {
                    $("#liPlayerInfo").addClass("uk-active");
                    $("#liPlayerInfoContent").addClass("uk-active");
                    $("#teamTab").removeClass("uk-active");
                    $("#teams").removeClass("uk-active");
                } else {
                    $("#calendarTab3").addClass("uk-active");
                    $("#allMatches").addClass("uk-active");
                    $("#teamTab").removeClass("uk-active");
                    $("#teams").removeClass("uk-active");
                    $("#liPlayerInfo").removeClass("uk-active");
                    $("#liPlayerInfoContent").removeClass("uk-active");
                }
                changePanel('');

                // evidenzio la competizione selezionata se disponibile
                var selectedCompetition = new URL(document.URL).searchParams.get("competitionId");
                var selectedGroupId = new URL(document.URL).searchParams.get("groupId");
                if (typeof selectedGroupId !== 'undefined' && selectedGroupId !== null && typeof selectedCompetition !== 'undefined' && selectedCompetition !== null) {
                    $("#competition-" + selectedCompetition + "-" + selectedGroupId).addClass("uk-button-active");
                } else if (typeof selectedCompetition !== 'undefined' && selectedCompetition !== null) {
                    $("#competition-" + selectedCompetition).addClass("uk-button-active");
                } else {
                    $("#competition--1").addClass("uk-button-active");
                }
                // evidenzio il team selezionato se disponibile
                var selectedTeam = new URL(document.URL).searchParams.get("teamId");
                if (typeof selectedTeam !== 'undefined' && selectedTeam !== null) {
                    $("#team-" + selectedTeam).addClass("uk-button-active");
                } else {
                    $("#team--1").addClass("uk-button-active");
                }

            <c:if test="${mElementToClick != null}">
                $("#${mElementToClick}").click();
            </c:if>
            <c:if test="${mElementToClick == null && !personalSource}">
                $("#filterSicsOverviewButton").click();
            </c:if>
            <c:if test="${mElementToClick == null && personalSource}">
                $("#filterSicsSearchButton").click();
            </c:if>

                setTimeout(function () {
                    $("#reportModalConfronto").attr("checked", "true");
                }, 100);

            <c:if test="${mCurrentSeason == mLastSeasonId}">
                <c:if test="${mTeam != null and mLastTeam != null and !mTeam.name.equalsIgnoreCase(mLastTeam.name)}">
                $("#playerTeamsLabel").html($("#playerTeamsLabel").html() + " (<a href='/sicstv/user/team.htm?personal=${personalSource}&formTeamId=${mLastTeam.id}<c:choose><c:when test="${mComp != null}">&formCompetitionId=${mComp.id}</c:when><c:otherwise>&formCompetitionId=-1</c:otherwise></c:choose>'>${mLastTeam.name}</a>)");
                </c:if>
                <c:if test="${mTeam != null and mLastTeam != null and !mTeam.nationalTeam and mTeam.mainTeamId != mLastTeam.mainTeamId}">
                $("#playerTeamsLabel").html($("#playerTeamsLabel").html() + "<br>(<strong><spring:message code="player.last.game.with"/></strong>)");
                </c:if>
            </c:if>

                // carico comunque default nel caso in cui non ho almeno una tabella nei profili
                reportChangeRole('reportModal${mPlayer.ruolo}');

                // inizializzo a mano lo switcher altrimenti non va per qualche motivo
                if ($("#reportPlayerStatsTypesButtonul > li.uk-active").length === 0) {
                    var customOptions = {
                        connect: '#reportPlayerStatsTypes'
                    };
                    UIkit.switcher('#reportPlayerStatsTypesButtonul', customOptions);
                }

                // gestione serie D
            <c:set var="isSerieD" value="${false}"/>
            <c:forEach var="item" items="${formCompetitionPlayer}">
                <c:if test="${item.id == 12}">
                    <c:set var="isSerieD" value="${true}"/>
                </c:if>
            </c:forEach>
            <c:if test="${isSerieD && mGirone == null}">
                $("#competitions").find("p").each(function (index, element) {
                    var text = $(element).text();
                    if (text.startsWith("SERIE D")) {
                        var relativeDiv = $(element).prev("div");
                        var divId = $(relativeDiv).attr("id");
                        if (divId.startsWith("competition-12")) {
                            var groupId = divId.replace("competition-12-", "");
                            if (!isNaN(parseInt(groupId))) {
                                $(relativeDiv).click();
                            }
                        }
                    }
                });
            </c:if>

                // carico sempre profilo altrimenti mancano dati in pagina
                // solo se ci sono dati però
            <c:if test="${mHasData}">
                loadPlayerOverview("filterSicsOverview");
            </c:if>

                //drawField('canvasContainer', pitchHeatMap);
                drawField('canvasContainer', pitch);
                var heatmapPoints = [];
            <c:if test="${mPositions.size() > 0}">
                var maxPercentage = 0;
                <c:forEach var="positionEntry" items="${mPositions}">
                var tmpPercentage = Math.round(${positionEntry.value} / ${mPositionTotal} * 100);
                if (tmpPercentage > maxPercentage) {
                    maxPercentage = tmpPercentage;
                }
                </c:forEach>

                <c:forEach var="positionEntry" items="${mPositions}">
                drawPoint(${positionEntry.key.x}, ${positionEntry.key.y}, ${positionEntry.value}, ${mPositionTotal});

                var percentage = Math.round(${positionEntry.value} / ${mPositionTotal} * 100);
                var value = percentage / maxPercentage * 45;

                var tmpPoint = getCorrectPoint(${positionEntry.key.x}, ${positionEntry.key.y});
                var newPoint = {x: Math.round(tmpPoint.cx), y: Math.round(tmpPoint.cy), value: value};
                heatmapPoints.push(newPoint);
                </c:forEach>

                // creo un rettangolo intorno ai punti per evitare che le "chiazze" di colore
                // siano troppo larghe
                var rectangle = getPointsRectangle(heatmapPoints);
                for (var i = 0; i < rectangle.larghezza; i++) {
                    if (i % 3 === 0) {
                        heatmapPoints.push({x: rectangle.x + i, y: rectangle.y, value: 0});
                        heatmapPoints.push({x: rectangle.x + i, y: rectangle.y + rectangle.altezza, value: 0});
                    }
                }
                for (var i = 0; i < rectangle.altezza; i++) {
                    if (i % 3 === 0) {
                        heatmapPoints.push({x: rectangle.x, y: rectangle.y + i, value: 0});
                        heatmapPoints.push({x: rectangle.x + rectangle.larghezza, y: rectangle.y + i, value: 0});
                    }
                }

//                    var can3 = document.getElementById("heatMapCanvas");
//                    ctx3 = can3.getContext("2d"),
//                    drw3 = new TemperatureMap(ctx3);
//                    drw3.setPoints(heatmapPoints, $("#" + pitchHeatMap.id).attr("width"), $("#" + pitchHeatMap.id).attr("height"));
//                    drw3.drawFull(false, function () { });
            </c:if>
            <c:if test="${mPositions.size() == 0}">
                drawPlayerRole("${mPlayer.ruolo}");
            </c:if>

                //drawHeatMap(heatmapPoints);

                // no data message
                var teamName = $("#teamName").val();
                var compName = $("#compName").val();
                if (notEmpty(teamName) || notEmpty(compName)) {
                    var filters = [];

                    if (compName) {
                        filters.push(compName);
                    }
                    if (teamName) {
                        filters.push(teamName);
                    }

                    if (filters.length > 0) {
                        var currentText = $("#no-data-content").text();
                        currentText = currentText.replace("and/or", "in " + filters.join(", ") + " and/or");
                        currentText = currentText.replace("e/o", "in " + filters.join(", ") + " e/o");

                        $("#no-data-content").text(currentText);
                    }
                }

            <c:if test="${mPlayer == null}">
                var playerIdParam = new URL(document.URL).searchParams.get("playerId");
                baseUrl += playerIdParam;
            </c:if>
            });

            var arrayMatch = [];

            // logout ajax
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);

            function jsRefreshMatches(page) {
                var idC = $("#idComp").val();
                var idT = $("#idTeam").val();

                refreshMatches('matches', 'matches', page, idC, idT);
            }

            function switchMatchday(step) {

                var valDay = parseInt($("#mDay").val());
                if (step === "prev") {
                    valDay = valDay - 1;
                } else if (step === "next") {
                    valDay = valDay + 1;
                }

                if (valDay >= 1 && valDay <= parseInt($("#mMaxDay").val())) {
                    //jsLoadingAlpha("#matchDayCalendar");
                    $("#mDay").val(valDay);
                    jsRefreshMatchesCalendar(${mComp.id});
                }
            }

            function addMatch(id, check) {
                if ($("#" + check).is(':checked')) {
                    arrayMatch.push(id);
                } else {
                    arrayMatch.splice($.inArray(id, arrayMatch), 1);
                }
            }

            function submitMulti() {
                var ids = "";
                $.each(arrayMatch, function (index, value) {
                    if (!ids.trim()) {
                        ids = value;
                    } else {
                        ids = ids + '-' + value;
                    }
                });
                location.href = '/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=' + ids + "&idComp=-1&idTeam=&idPlayer=&goals=false&event=&filter=&limit=50";
            }

            function filterData() {
                readFilterMulti(false);
                filterMatches('allMatches', 1, '');
            }

            function showPlayerEvents(type) {
                if (type.includes("SICS <spring:message code='video.ricercaAvanzata'/>")) {
                    type = type.replace("SICS <spring:message code='video.ricercaAvanzata'/>", "SICS");
                }
                var idP = $("#idPlayer").val();
                var idC = $("#idComp").val();
                var filtro = $("#gameFilter").val();
                if (typeof filtro === 'undefined') {
                    filtro = "";
                }
                var idTeam = ${param.teamId==null} ? "" : "${param.teamId}";
                var strUrl = "/sicstv/user/video.htm?personal=${personalSource}&id=&idComp=" + idC + "&idTeam=" + idTeam + "&idPlayer=" + idP + "&goals=false&filter=" + escape(filtro) + "&event=" + escape(type) + "&limit=50&gameIds=" + selectedMatches;
                location.href = strUrl;
            }

            function showPlayerBestEvents() {
                var idP = $("#idPlayer").val();
                var idC = $("#idComp").val();
                var idTeam = ${param.teamId == null} ? "" : "${param.teamId}";
                var groupId = new URL(document.URL).searchParams.get("groupId");
                if (typeof groupId === 'undefined' || groupId === null || !groupId) {
                    groupId = "";
                }

                var strUrl = "/sicstv/user/video.htm?personal=${personalSource}&id=&idComp=" + idC + "&idTeam=" + idTeam + "&idPlayer=" + idP + "&goals=false&filter=&event=&limit=50&groupId=" + groupId + "&bestActions=true&removeDuplicates=true&startClip=true";
                location.href = strUrl;
            }

            function showPlayerHighlight() {
                var idP = $("#idPlayer").val();
                var idC = $("#idComp").val();
                var idTeam = ${param.teamId == null} ? "" : "${param.teamId}";
                var groupId = new URL(document.URL).searchParams.get("groupId");
                if (typeof groupId === 'undefined' || groupId === null || !groupId) {
                    groupId = "";
                }

                var strUrl = "/sicstv/user/video.htm?personal=${personalSource}&id=&idComp=" + idC + "&idTeam=" + idTeam + "&idPlayer=" + idP + "&goals=false&filter=&event=&limit=50&groupId=" + groupId + "&playerHighlight=true&removeDuplicates=true&startClip=true";
                location.href = strUrl;
            }

            function changePanel(element) {
                var panelName = $(".filterSelect option:selected").val();
                var onlyOneConfig = ${filterToShow.size() == 1};

                if (element.length > 0) {
                    panelName = $("#" + element).val();
                    $(".configChoiceMobile").removeClass("uk-button-success");
                    $("#" + element).addClass("uk-button-success");
                }
                var divContent1 = "<div class='uk-float-left panelDiv panelSicsDiv'>", divContent2 = "<div class='uk-float-right panelDiv panelSicsDiv'>";
            <c:forEach items="${filterToShow}" var="list" varStatus="status" >
                if ('${list.key}' === panelName || onlyOneConfig) {
                    if ('${list.key}' === "SICS") {
                        $(".filterSicsDiv").removeClass("uk-hidden");
                        $(".filterGroupset").addClass("uk-hidden");
                    } else {
                        $("#filterSicsSearchDiv > button:not(:first-child)").each(function (index, element) {
                            $("#" + element.id).prop("disabled", "disabled");
                        });
                        $(".filterSicsDiv").addClass("uk-hidden");
                        $(".filterGroupset").removeClass("uk-hidden");
                <c:forEach items="${list.value}" var="object">
                    <c:set var="counter" value="1"></c:set>

                    <c:forEach items="${object}" var="btn">

                        var content = "<button class='uk-button teamFilterButton'";
                        if ('${btn.value[1]}' === 'false') {
                            content += "disabled ";
                        }
                        content += "onclick=\"showPlayerEvents('${list.key}-_-${btn.key}');\" title='${btn.value[0]}'><i class='uk-icon-video-camera' ></i>${btn.value[0]}  (${btn.value[2]})</button>";
                        <c:choose>
                            <c:when test="${counter % 2 == 0}">
                        divContent1 += content;
                                <c:set var="counter" value="1"></c:set>
                            </c:when>
                            <c:when test="${counter % 2 != 0}">
                        divContent2 += content;
                                <c:set var="counter" value="2"></c:set>
                            </c:when>
                        </c:choose>
                    </c:forEach>
                </c:forEach>
                        divContent1 += "</div>";
                        divContent2 += "</div>";

                        $(".filterGroupset").html(divContent1 + divContent2);
                    }
                }
            </c:forEach>

            }

            function jsFilterGames(removeFilters) {
                if (removeFilters) {
                    var currentUrl = window.location.href;
                    if (currentUrl.includes("gameIds")) {
                        currentUrl = currentUrl.substring(0, currentUrl.indexOf("&gameIds"));
                    }
                    window.location.href = currentUrl;
                } else if (selectedMatches.length > 0) {
                    var currentUrl = window.location.href;
                    if (currentUrl.includes("gameIds")) {
                        currentUrl = currentUrl.substring(0, currentUrl.indexOf("&gameIds"));
                    }
                    window.location.href = currentUrl + "&gameIds=" + selectedMatches;
                }
            }

            var advancedFilter = new Map();
            function showSicsFilters(type) {
                if (type === 'search') {
                    $("#filterSicsOverview").addClass("uk-hidden");
                    $("#filterSicsOverviewButton").removeClass("uk-button-active");
                    $("#filterSicsSearchAdvanced").addClass("uk-hidden");
                    $("#filterSicsSearchAdvancedButton").removeClass("uk-button-active");
                    $("#filterSicsSearch").removeClass("uk-hidden");
                    $("#filterSicsSearchButton").addClass("uk-button-active");
                    $("#filterSelectedActions").addClass("uk-hidden");
                } else if (type === 'advanced') {
                    $("#filterSicsOverview").addClass("uk-hidden");
                    $("#filterSicsOverviewButton").removeClass("uk-button-active");
                    $("#filterSicsSearch").addClass("uk-hidden");
                    $("#filterSicsSearchButton").removeClass("uk-button-active");
                    $("#filterSicsSearchAdvanced").removeClass("uk-hidden");
                    $("#filterSicsSearchAdvancedButton").addClass("uk-button-active");
                    $("#filterSelectedActions").removeClass("uk-hidden");

                    if (advancedFilter.size === 0) {
                        // resetto le checkbox per filtro advanced -> se entro filtrato nella pagina video e torno indietro
                        // rimangono le checkbox sugli input dei filtri selezionati
                        setTimeout(function () {
                            $("#filterSicsSearchAdvanced").find("input").prop("checked", false);
                        }, 250);
                    }
                } else if (type === 'overview') {
                    $("#filterSicsSearchAdvanced").addClass("uk-hidden");
                    $("#filterSicsSearchAdvancedButton").removeClass("uk-button-active");
                    $("#filterSicsSearch").addClass("uk-hidden");
                    $("#filterSicsSearchButton").removeClass("uk-button-active");
                    $("#filterSelectedActions").addClass("uk-hidden");
                    $("#filterSicsOverview").removeClass("uk-hidden");
                    $("#filterSicsOverviewButton").addClass("uk-button-active");

//                    if ($("#playerOverviewContainer").length === 0) {
//                        loadPlayerOverview("filterSicsOverview");
//                    }
                }

                // ri-sistemo la parte di destra
                $("#divMatches").removeClass("uk-hidden");
                $("#divTeamInfo").addClass("uk-width-6-10");
                $("#divTeamInfo").removeClass("uk-width-10-10");
                $(".dt-button.uk-button-active").removeClass("uk-button-active");
            }

            function addRemoveAdvancedFilter(tagCode) {
                if (event.target.tagName !== 'I') { // In questo modo funziona solo se clicco il button
                    var generalTagFound = false;
                    var tagToFound = tagCode;
                    if (tagCode.includes("-")) {
                        tagToFound = tagCode.substring(0, tagCode.indexOf("-"));
                    }

                    [...advancedFilter.keys()].forEach(key => {
                        if (key === tagToFound) {
                            generalTagFound = true;
                        }
                    });

                    if (tagCode.includes("-")) { // AMM-0 AMM-1...
                        if (generalTagFound) { // ho già lo stesso tag oppure altri tag
                            var mapValue = advancedFilter.get(tagToFound);
                            if (mapValue.includes(tagCode)) { // devo togliere tag
                                mapValue = mapValue.replace("@" + tagCode, '');
                                mapValue = mapValue.replace(tagCode + "@", '');
                                mapValue = mapValue.replace(tagCode, '');

                                $("#SICS2015_" + tagToFound + "_" + tagCode).removeAttr("checked");
                            } else { // devo aggiungere il tag
                                mapValue += "@" + tagCode;
                                $("#SICS2015_" + tagToFound + "_" + tagCode).attr("checked", "checked");
                            }

                            if (mapValue === "") {
                                advancedFilter.delete(tagToFound);
                            } else {
                                advancedFilter.set(tagToFound, mapValue);
                            }
                        } else { // la categoria non esiste: devo crearla
                            advancedFilter.set(tagToFound, tagCode);
                            $("#SICS2015_" + tagToFound + "_" + tagCode).attr("checked", "checked");
                        }
                    } else { // AMM ATT..
                        if (generalTagFound) {
                            advancedFilter.delete(tagToFound);
                            document.querySelectorAll("." + tagToFound).forEach(function (element) {
                                var id = element.id;
                                if (!element.disabled) {
                                    $('#' + id).removeAttr("checked");
                                }
                            });
                        } else {
                            var atLeastOneAdded = false;
                            document.querySelectorAll("." + tagToFound).forEach(function (element) {
                                var id = element.id;
                                if (!element.disabled) {
                                    if (!$('#' + id).attr("checked")) {
                                        atLeastOneAdded = true;
                                        var functionToExecute = $('#' + id).attr("onclick");
                                        eval(functionToExecute);
                                    }
                                }
                            });

                            // se ho tutti i tag disabilitati e clicco sul tasto
                            // non succede nulla, invece deve selezionare tutti i tag
                            if (!atLeastOneAdded) {
                                advancedFilter.set(tagToFound, "");
                            }
                        }
                    }
                    checkSelectedButtonAdvanced();
                }
            }

            function checkSelectedButtonAdvanced() {
                $("#filterSicsSearchAdvanced").find(".uk-button-active").removeClass("uk-button-active");
                [...advancedFilter.keys()].forEach(key => {
                    $("#SICS2015-_-" + key).addClass("uk-button-active");
                });

                $("#filterSelectedActionsAmount").html(" " + advancedFilter.size);
                if (advancedFilter.size > 0) {
                    $("#filterSelectedActions").addClass("uk-button-confirm");
                } else {
                    $("#filterSelectedActions").removeClass("uk-button-confirm");
                }
            }

            function applyAdvancedFilter() {
                if (advancedFilter.size > 0) {
                    var filter = "";

                    [...advancedFilter.keys()].forEach(key => {
                        // devo guardare se devo applicare il filtro su TUTTA la categoria
                        var allChecked = true;
                        document.querySelectorAll("." + key).forEach(function (element) {
                            var id = element.id;
                            if (!$('#' + id).attr("checked") && $('#' + id).attr("disabled") !== 'disabled') {
                                allChecked = false;
                            }
                        });

                        if (allChecked) {
                            filter += (filter === "" ? "" : "|") + "SICS-_-" + key;
                        } else {
                            filter += (filter === "" ? "" : "|") + "SICS-_-" + key + "#" + advancedFilter.get(key);
                        }
                    });

                    showPlayerEvents(filter);
                }
            }

            function openReportModal() {
                $("#reportModal").addClass("uk-open");
                $("#reportModal").removeClass("uk-hidden");

                var teamSelected = -1, competitionSelected = -1, groupSelected;
                var paramValue = new URL(document.URL).searchParams.get("teamId");
                if (paramValue !== null) {
                    teamSelected = parseInt(paramValue);
                }
                paramValue = new URL(document.URL).searchParams.get("competitionId");
                if (paramValue !== null) {
                    competitionSelected = parseInt(paramValue);
                }
                paramValue = new URL(document.URL).searchParams.get("groupId");
                if (paramValue !== null) {
                    groupSelected = parseInt(paramValue);
                }

                $("#reportModalSeason").html('<spring:message code="lib.stagione"/>: <strong>' + $("#seasonSelect option:selected").html() + '</strong>');
                $("#reportModalSeasonAdvanced").html('<spring:message code="lib.stagione"/>');
                $("#reportModalSeasonAdvancedValue").html('<strong>' + $("#seasonSelect option:selected").html() + '</strong>');

                if (teamSelected === -1) {
            <c:set var="teamList" value=""/>
            <c:set var="teamIdList" value=""/>
            <c:forEach var="team" items="${mPlayer.teamPlayer}">
                <c:if test="${!teamList.isEmpty()}">
                    <c:set var="teamList" value="${teamList} / "/>
                    <c:set var="teamIdList" value="${teamIdList},"/>
                </c:if>
                <c:set var="teamList" value="${teamList}${team.name}"/>
                <c:set var="teamIdList" value="${teamIdList}${team.id}"/>
            </c:forEach>
                    $("#reportModalTeams").html("<spring:message code="player.report.teams.title"/>: <strong>${teamList}</strong>");
                    $("#reportModalTeams").attr("data-value", "${teamIdList}");
                } else {
            <c:set var="teamList" value=""/>
            <c:forEach var="team" items="${mPlayer.teamPlayer}">
                    if (${team.id} === teamSelected) {
                <c:set var="teamList" value="${team.name}"/>
                    }
            </c:forEach>
                    $("#reportModalTeams").html("<spring:message code="player.report.teams.title"/>: <strong>${teamList}</strong>");
                    $("#reportModalTeams").attr("data-value", "${teamIdList}");
                }

                if (typeof groupSelected === 'undefined') {
                    if (competitionSelected === -1) {
            <c:set var="competitionList" value=""/>
            <c:set var="competitionIdList" value=""/>
            <c:forEach var="competition" items="${formCompetitionPlayer}">
                <c:if test="${!competitionList.isEmpty()}">
                    <c:set var="competitionList" value="${competitionList} / "/>
                    <c:set var="competitionIdList" value="${competitionIdList},"/>
                </c:if>
                <c:set var="competitionList" value="${competitionList}${competition.name}"/>
                <c:set var="competitionIdList" value="${competitionIdList}${competition.id}"/>
            </c:forEach>
                        $("#reportModalCompetitions").html("<spring:message code="player.report.competitions.title"/>: <strong>${competitionList}</strong>");
                        $("#reportModalCompetitions").attr("data-value", "${competitionIdList}");
                    } else {
                        var selectedCompetitionName = "";
            <c:forEach var="competition" items="${formCompetitionPlayer}">
                        if (${competition.id} === competitionSelected) {
                            selectedCompetitionName = "${competition.name}";
                        }
            </c:forEach>
                        $("#reportModalCompetitions").html("<spring:message code="player.report.competitions.title"/>: <strong>" + selectedCompetitionName + "</strong>");
                        $("#reportModalCompetitions").attr("data-value", competitionSelected);
                    }
                } else {
                    var selectedCompetitionName = "";
                    var dataValue = "";
            <c:forEach var="competition" items="${mGironi}">
                    if (${competition.groupId} === groupSelected) {
                        dataValue = "${competition.id}-${competition.groupId}";
                                        selectedCompetitionName = "${competition.name} - ${competition.groupName}";
                                                    }
            </c:forEach>

                                                    if (!selectedCompetitionName && competitionSelected !== -1) {
            <c:forEach var="competition" items="${formCompetitionPlayer}">
                                                        if (${competition.id} === competitionSelected) {
                                                            selectedCompetitionName = "${competition.name}";
                                                            dataValue = "${competition.id}";
                                                        }
            </c:forEach>
                                                    }
                                                    $("#reportModalCompetitions").html("<spring:message code="player.report.competitions.title"/>: <strong>" + selectedCompetitionName + "</strong>");
                                                    $("#reportModalCompetitions").attr("data-value", dataValue);
                                                }

                                                // TEAMS
                                                $("#reportModalTeamsAdvanced").empty();
                                                var paragraph = $("<p>");
                                                paragraph.attr("style", "font-size: 14px; text-align:center");
                                                paragraph.html("<spring:message code="player.report.teams.title"/>");
                                                $("#reportModalTeamsAdvanced").append(paragraph);

            <c:forEach var="team" items="${mPlayer.teamPlayer}">
                                                var button = $("<button>");
                                                button.addClass("uk-button");
                                                button.attr("onclick", "jsManageActive(this)");
                                                button.attr("style", "margin-left: 2px");
                                                button.attr("data-value", "${team.id}");
                                                if (teamSelected === -1 || ${team.id} === teamSelected) {
                                                    button.addClass("uk-button-active");
                                                }
                                                button.html("${team.name}");

                                                $("#reportModalTeamsAdvanced").append(button);
            </c:forEach>

                                                // COMPETITIONS
                                                $("#reportModalCompetitionsAdvanced").empty();
                                                var paragraph = $("<p>");
                                                paragraph.attr("style", "font-size: 14px; text-align:center");
            <c:if test="${mGironi == null || mGironi.isEmpty()}">
                                                paragraph.html("<spring:message code="player.report.competitions.title"/>");
            </c:if>
            <c:if test="${mGironi != null && !mGironi.isEmpty()}">//paragraph.html("<span style='text-decoration: underline; cursor: pointer' onclick='showReportModalCompetitions()'><spring:message code="player.report.competitions.title"/></span> / <span style='text-decoration: underline; cursor: pointer' onclick='showReportModalGironi()'><spring:message code="menu.user.gironi"/></span>");
                                                paragraph.html("<span class='uk-form'><spring:message code="menu.user.livello.dettaglio"/>: <select onchange='changeReportModalDetailLevel(this);'><option value='100' selected><spring:message code="menu.user.competizione"/></option><option value='200'><spring:message code="menu.user.gironi"/></option></select></span>");
            </c:if>
                                                $("#reportModalCompetitionsAdvanced").append(paragraph);

            <c:forEach var="competition" items="${formCompetitionPlayer}">
                                                var button = $("<button>");
                                                button.addClass("uk-button");
                                                button.attr("onclick", "jsManageActive(this)");
                                                button.attr("style", "margin-left: 2px");
                                                button.attr("data-value", "${competition.id}");
                                                if (competitionSelected === -1 || ${competition.id} === competitionSelected) {
                                                    button.addClass("uk-button-active");
                                                }
                                                button.html("${competition.name}");

                                                $("#reportModalCompetitionsAdvanced").append(button);
            </c:forEach>
                                                $("#reportModalGironiAdvanced").empty();
                                                var paragraph = $("<p>");
                                                paragraph.attr("style", "font-size: 14px; text-align:center");
            <c:if test="${mGironi == null || mGironi.isEmpty()}">
                                                paragraph.html("<spring:message code="player.report.competitions.title"/>");
            </c:if>
            <c:if test="${mGironi != null && !mGironi.isEmpty()}">
                                                //paragraph.html("<span style='text-decoration: underline; cursor: pointer' onclick='showReportModalCompetitions()'><spring:message code="player.report.competitions.title"/></span> / <span style='text-decoration: underline; cursor: pointer onclick='showReportModalGironi()''><spring:message code="menu.user.gironi"/></span>");
                                                paragraph.html("<span class='uk-form'><spring:message code="menu.user.livello.dettaglio"/>: <select onchange='changeReportModalDetailLevel(this);'><option value='100'><spring:message code="menu.user.competizione"/></option><option value='200' selected><spring:message code="menu.user.gironi"/></option></select></span>");
            </c:if>
                                                $("#reportModalGironiAdvanced").append(paragraph);
            <c:forEach var="competition" items="${mGironi}">
                                                var button = $("<button>");
                                                button.addClass("uk-button");
                                                button.attr("onclick", "removeAllChecked(this); jsManageActive(this)");
                                                button.attr("style", "margin-left: 2px");
                                                button.attr("data-value", "${competition.id}-${competition.groupId}");
                                                        if (competitionSelected === -1 || ${competition.id} === competitionSelected) {
                                                            button.addClass("uk-button-active");
                                                        }
                                                        button.html("${competition.name} - ${competition.groupName}");

                                                                $("#reportModalGironiAdvanced").append(button);
            </c:forEach>
                                                            }

                                                            function changeReportModalDetailLevel(element) {
                                                                var currentValue = $(element).val();
                                                                if (currentValue === '100') {
                                                                    showReportModalCompetitions();
                                                                    $(element).val(200);
                                                                } else {
                                                                    showReportModalGironi();
                                                                    $(element).val(100);
                                                                }
                                                            }

                                                            function removeAllChecked(element) {
                                                                var checked = $(element).hasClass("uk-button-active");
                                                                $("#reportModalCompetitionsAdvanced > button").removeClass("uk-button-active");
                                                                $("#reportModalGironiAdvanced > button").removeClass("uk-button-active");

                                                                if (checked) {
                                                                    $(element).addClass("uk-button-active");
                                                                }
                                                            }

                                                            function showReportModalCompetitions() {
                                                                $("#reportModalCompetitionsAdvanced").removeClass("uk-hidden");
                                                                $("#reportModalGironiAdvanced").addClass("uk-hidden");
                                                            }

                                                            function showReportModalGironi() {
                                                                $("#reportModalGironiAdvanced").removeClass("uk-hidden");
                                                                $("#reportModalCompetitionsAdvanced").addClass("uk-hidden");
                                                            }

                                                            function closeReportModal() {
                                                                $("#reportModal").removeClass("uk-open");
                                                                $("#reportModal").addClass("uk-hidden");
                                                            }

                                                            function openWatchlistModal() {
                                                                $("#watchlistModal").addClass("uk-open");
                                                                $("#watchlistModal").removeClass("uk-hidden");
                                                            }

                                                            function closeWatchlistModal() {
                                                                $("#watchlistModal").removeClass("uk-open");
                                                                $("#watchlistModal").addClass("uk-hidden");
                                                            }

                                                            function generateReport() {
                                                                var teamSelected = -1, competitionSelected = -1, groupId = "";
                                                                var paramValue = new URL(document.URL).searchParams.get("teamId");
                                                                if (paramValue !== null) {
                                                                    teamSelected = paramValue;
                                                                }
                                                                paramValue = new URL(document.URL).searchParams.get("competitionId");
                                                                if (paramValue !== null) {
                                                                    competitionSelected = paramValue;
                                                                }
                                                                var teams, competitions;
                                                                teams = $("#reportModalTeams").attr("data-value");
                                                                competitions = $("#reportModalCompetitions").attr("data-value");
                                                                if (competitions.includes("-")) {
                                                                    groupId = competitions.substring(competitions.indexOf("-") + 1, competitions.length);
                                                                    competitions = competitions.substring(0, competitions.indexOf("-"));
                                                                    competitionSelected = competitions;
                                                                }

                                                                var minMatches = 3, minMinutes = 30;

                                                                var positionId = $("#reportPlayerRole > center > button.uk-button-active").attr("data-value");
                                                                var averageCompetition = true;
                                                                if (typeof $("#reportModalConfronto").attr("checked") !== 'undefined') {
                                                                    averageCompetition = false;
                                                                }

                                                                var statsTypeIds = "";
                                                                $("#reportPlayerStatsTypes > li > button.uk-button-active").each(function () {
                                                                    if (statsTypeIds !== "")
                                                                        statsTypeIds += ",";
                                                                    statsTypeIds += $(this).attr("data-value");
                                                                });

                                                                var dateFrom = "", dateTo = "";

                                                                var wait = 0;
                                                                if (!statsTypeIds) {
                                                                    UIkit.notify("<spring:message code='report.choose.warning'/>", {status: 'warning', timeout: 1000});
                                                                    return;
                                                                }
//                if (statsTypeIds.match(/,/g).length > 15) {
//                    // MESSO DI PROPOSITO 2500 MS
//                    UIkit.notify("<spring:message code='menu.user.player.report.limit.message'/>", {status: 'warning', timeout: 2500});
//                    wait = 2500;
//                }

                                                                setTimeout(function () {
                                                                    if ($("#reportModalSimple").hasClass("uk-button-active")) {
                                                                        // SEMPLICE

                                                                        if (!teams.includes(",")) {
                                                                            teamSelected = teams;
                                                                        }
                                                                        if (!competitions.includes(",")) {
                                                                            competitionSelected = competitions;
                                                                        }

                                                                        if (!competitionSelected && !competitions) {
                                                                            UIkit.notify("<spring:message code="player.report.competition.required"/>", {status: 'warning', timeout: 1000});
                                                                            return;
                                                                        }
                                                                        if (!teamSelected && !teams) {
                                                                            UIkit.notify("<spring:message code="player.report.team.required"/>", {status: 'warning', timeout: 1000});
                                                                            return;
                                                                        }

                                                                        window.location.href = "/sicstv/user/reportPlayer.htm?playerId=" + ${mPlayer.id} + "&teamSelected=" + teamSelected + "&teams=" + teams + "&competitionSelected=" + competitionSelected + "&competitions=" + competitions + "&minutesPerMatch=" + minMinutes + "&minMatches=" + minMatches + "&statsTypeIds=" + statsTypeIds + "&positionId=" + positionId + "&averageCompetition=" + averageCompetition + "&groupId=" + groupId;
                                                                    } else {
                                                                        // AVANZATO
                                                                        // metto entrambi a -1 così gestisco tutto con i tasti attivi o meno
                                                                        teamSelected = -1;
                                                                        competitionSelected = -1;
                                                                        teams = "";
                                                                        competitions = "";
                                                                        groupId = "";

                                                                        if ($("#reportModalTeamsAdvanced > button.uk-button-active").length === 0) {
                                                                            $("#reportModalTeamsAdvanced > button").each(function () {
                                                                                if (teams !== "")
                                                                                    teams += ",";
                                                                                teams += $(this).attr("data-value");
                                                                            });
                                                                        } else {
                                                                            $("#reportModalTeamsAdvanced > button.uk-button-active").each(function () {
                                                                                if (teams !== "")
                                                                                    teams += ",";
                                                                                teams += $(this).attr("data-value");
                                                                            });
                                                                        }

                                                                        if ($("#reportModalGironiAdvanced").hasClass("uk-hidden")) {
                                                                            if ($("#reportModalCompetitionsAdvanced > button.uk-button-active").length === 0) {
                                                                                $("#reportModalCompetitionsAdvanced > button").each(function () {
                                                                                    if (competitions !== "")
                                                                                        competitions += ",";
                                                                                    competitions += $(this).attr("data-value");
                                                                                });
                                                                            } else {
                                                                                $("#reportModalCompetitionsAdvanced > button.uk-button-active").each(function () {
                                                                                    if (competitions !== "")
                                                                                        competitions += ",";
                                                                                    competitions += $(this).attr("data-value");
                                                                                });
                                                                            }
                                                                        } else {
                                                                            $("#reportModalGironiAdvanced > button.uk-button-active").each(function (el) {
                                                                                competitions = $(this).attr("data-value");
                                                                            });
                                                                            if (competitions.includes("-")) {
                                                                                groupId = competitions.substring(competitions.indexOf("-") + 1, competitions.length);
                                                                                competitions = competitions.substring(0, competitions.indexOf("-"));
                                                                                competitionSelected = competitions;
                                                                            }

                                                                            if (!groupId) {
                                                                                UIkit.notify("<spring:message code='report.groupid.warning'/>", {status: 'warning', timeout: 1000});
                                                                                return;
                                                                            }
                                                                        }

                                                                        if (!teams.includes(",")) {
                                                                            teamSelected = teams;
                                                                        }
                                                                        if (!competitions.includes(",")) {
                                                                            competitionSelected = competitions;
                                                                        }
                                                                        if (!competitions) {
                                                                            // ci sono casi che non è popolato...
                                                                            competitions = $("#reportModalCompetitions").attr("data-value");
                                                                        }

                                                                        minMatches = $("#reportModalMinMatches").val();
                                                                        minMinutes = $("#reportModalMinMinutes").val();

                                                                        dateFrom = $("#reportModalFrom").val();
                                                                        dateTo = $("#reportModalTo").val();

                                                                        if (!competitionSelected && !competitions) {
                                                                            UIkit.notify("<spring:message code="player.report.competition.required"/>", {status: 'warning', timeout: 1000});
                                                                            return;
                                                                        }
                                                                        if (!teamSelected && !teams) {
                                                                            UIkit.notify("<spring:message code="player.report.team.required"/>", {status: 'warning', timeout: 1000});
                                                                            return;
                                                                        }
                                                                        window.location.href = "/sicstv/user/reportPlayer.htm?playerId=" + ${mPlayer.id} + "&teamSelected=" + teamSelected + "&teams=" + teams + "&competitionSelected=" + competitionSelected + "&competitions=" + competitions + "&minutesPerMatch=" + minMinutes + "&minMatches=" + minMatches + "&statsTypeIds=" + statsTypeIds + "&positionId=" + positionId + "&averageCompetition=" + averageCompetition + "&groupId=" + groupId + "&from=" + dateFrom + "&to=" + dateTo;
                                                                    }
                                                                }, wait);
                                                            }

                                                            // TODO: CAPIRE SE E' CORRETTA, FORSE NO...
                                                            function reportChangeRole(idSelected) {
                                                                $("#reportPlayerRole > center > button").removeClass("uk-button-active");
                                                                $("#" + idSelected).addClass("uk-button-active");

                                                                $("#reportPlayerStatsTypes > li > button").removeClass("uk-button-active");
                                                                var found = false;
            <c:forEach var="filter" items="${mReportFiltersPersonal.keySet()}">
                <c:set var="extraClasses" value="idSelected"/>
                                                                if (idSelected === 'reportModalA') {
                                                                    if ("${filter.tableType}" === "44") {
                                                                        found = true;
                <c:forEach var="filterDetail" items="${mReportFiltersPersonal.get(filter)}">
                                                                        $("#reportModalStatButton${filterDetail.statsTypeId}").addClass("uk-button-active");
                </c:forEach>
                                                                    }
                                                                } else if (idSelected === 'reportModalC') {
                                                                    if ("${filter.tableType}" === "43") {
                                                                        found = true;
                <c:forEach var="filterDetail" items="${mReportFiltersPersonal.get(filter)}">
                                                                        $("#reportModalStatButton${filterDetail.statsTypeId}").addClass("uk-button-active");
                </c:forEach>
                                                                    }
                                                                } else if (idSelected === 'reportModalD') {
                                                                    if ("${filter.tableType}" === "42") {
                                                                        found = true;
                <c:forEach var="filterDetail" items="${mReportFiltersPersonal.get(filter)}">
                                                                        $("#reportModalStatButton${filterDetail.statsTypeId}").addClass("uk-button-active");
                </c:forEach>
                                                                    }
                                                                } else if (idSelected === 'reportModalP') {
                                                                    if ("${filter.tableType}" === "41") {
                                                                        found = true;
                <c:forEach var="filterDetail" items="${mReportFiltersPersonal.get(filter)}">
                                                                        $("#reportModalStatButton${filterDetail.statsTypeId}").addClass("uk-button-active");
                </c:forEach>
                                                                    }
                                                                }
            </c:forEach>
                                                                if (!found) {
            <c:forEach var="stat" items="${mAllColumns}">
                <c:set var="extraClasses" value=""/>
                <c:choose>
                    <c:when test="${mPlayer.ruolo.equals('A')}">
                        <c:if test="${stat.strikerValid != null && stat.strikerValid}">
                            <c:set var="extraClasses" value="uk-button-active"/>
                        </c:if>
                    </c:when>
                    <c:when test="${mPlayer.ruolo.equals('C')}">
                        <c:if test="${stat.midfielderValid != null && stat.midfielderValid}">
                            <c:set var="extraClasses" value="uk-button-active"/>
                        </c:if>
                    </c:when>
                    <c:when test="${mPlayer.ruolo.equals('D')}">
                        <c:if test="${stat.defenderValid != null && stat.defenderValid}">
                            <c:set var="extraClasses" value="uk-button-active"/>
                        </c:if>
                    </c:when>
                    <c:when test="${mPlayer.ruolo.equals('P')}">
                        <c:if test="${stat.goalkeeperValid != null && stat.goalkeeperValid}">
                            <c:set var="extraClasses" value="uk-button-active"/>
                        </c:if>
                    </c:when>
                </c:choose>
                <c:if test="${!extraClasses.equals('')}">
                                                                    $("#reportModalStatButton${stat.id}").addClass("uk-button-active");
                </c:if>
            </c:forEach>
                                                                }
                                                            }

                                                            function showReportModalSimple() {
                                                                $("#reportModalSimpleDiv").removeClass("uk-hidden");
                                                                $("#reportModalSimple").addClass("uk-button-active");

                                                                $("#reportModalAdvancedDiv").addClass("uk-hidden");
                                                                $("#reportModalAdvanced").removeClass("uk-button-active");

                                                                $(".hideOnSimple").addClass("uk-hidden");
                                                            }

                                                            function showReportModalAdvanced() {
                                                                $("#reportModalAdvancedDiv").removeClass("uk-hidden");
                                                                $("#reportModalAdvanced").addClass("uk-button-active");

                                                                $("#reportModalSimpleDiv").addClass("uk-hidden");
                                                                $("#reportModalSimple").removeClass("uk-button-active");

                                                                $(".hideOnSimple").removeClass("uk-hidden");
                                                            }

                                                            // (!) LA STESSA FUNZIONE C'E' ANCHE NELLA PAGINA mywatchlist.jsp (!)
                                                            function addToWatchlist(watchlistId) {
                                                                var name = $('#nameWatchlist').val();
                                                                if (name === "" && typeof watchlistId === 'undefined') {
                                                                    UIkit.notify("<spring:message code='watchlist.add.failed.no.name'/>", {status: 'warning', timeout: 1000});
                                                                    return;
                                                                }

                                                                $.ajax({
                                                                    type: "POST",
                                                                    url: "/sicstv/user/creaWatchlist.htm",
                                                                    data: encodeURI("name=" + name + "&playerId=${mPlayer.id}&watchlistId=" + (typeof watchlistId !== 'undefined' ? watchlistId : '')),
                                                                    cache: false,
                                                                    success: function (result) {
                                                                        if (result === 'ok') {
                                                                            if (typeof watchlistId === 'undefined') {
                                                                                UIkit.notify("<spring:message code='watchlist.add.success'/>", {status: 'success', timeout: 1000});
                                                                            } else {
                                                                                UIkit.notify("<spring:message code='watchlist.update.success'/>", {status: 'success', timeout: 1000});
                                                                            }

                                                                            window.location.reload();
                                                                        } else {
                                                                            if (typeof watchlistId === 'undefined') {
                                                                                UIkit.notify("<spring:message code='watchlist.add.failed'/>", {status: 'warning', timeout: 1000});
                                                                            } else {
                                                                                UIkit.notify("<spring:message code='watchlist.update.failed'/>", {status: 'warning', timeout: 1000});
                                                                            }
                                                                        }
                                                                    }
                                                                });
                                                            }

                                                            // (!) LA STESSA FUNZIONE C'E' ANCHE NELLA PAGINA mywatchlist.jsp (!)
                                                            function removeFromWatchlist(watchlistId) {
                                                                var name = $('#nameWatchlist').val();
                                                                if (name === "" && typeof watchlistId === 'undefined') {
                                                                    UIkit.notify("<spring:message code='watchlist.add.failed.no.name'/>", {status: 'warning', timeout: 1000});
                                                                    return;
                                                                }

                                                                $.ajax({
                                                                    type: "POST",
                                                                    url: "/sicstv/user/rimuoviPlayerDaWatchlist.htm",
                                                                    data: encodeURI("playerId=${mPlayer.id}&watchlistId=" + (typeof watchlistId !== 'undefined' ? watchlistId : '')),
                                                                    cache: false,
                                                                    success: function (result) {
                                                                        if (result === 'ok') {
                                                                            UIkit.notify("<spring:message code='watchlist.update.success'/>", {status: 'success', timeout: 1000});
                                                                            window.location.reload();
                                                                        } else {
                                                                            UIkit.notify("<spring:message code='watchlist.update.failed'/>", {status: 'warning', timeout: 1000});
                                                                        }
                                                                    }
                                                                });
                                                            }

                                                            function addPlayerToFavorites(id) {
                                                                $.ajax({
                                                                    type: "POST",
                                                                    url: "/sicstv/user/aggiungiAPreferiti.htm",
                                                                    data: encodeURI("playerId=" + id),
                                                                    cache: false,
                                                                    success: function (result) {
                                                                        if (result === 'ok') {
                                                                            UIkit.notify("<spring:message code='favorites.add.success'/>", {status: 'success', timeout: 1000});

                                                                            $("#manageFavoritesButton").attr("onclick", "removePlayerFromFavorites(" + id + ")");
                                                                            $("#manageFavoritesButton").addClass("uk-button-active");
                                                                            jsReloadFavorites();
                                                                        } else {
                                                                            UIkit.notify("<spring:message code='favorites.add.failed'/>", {status: 'warning', timeout: 1000});
                                                                        }
                                                                    }
                                                                });
                                                            }

                                                            function removePlayerFromFavorites(id) {
                                                                $.ajax({
                                                                    type: "POST",
                                                                    url: "/sicstv/user/rimuoviDaiPreferiti.htm",
                                                                    data: encodeURI("playerId=" + id),
                                                                    cache: false,
                                                                    success: function (result) {
                                                                        if (result === 'ok') {
                                                                            UIkit.notify("<spring:message code='favorites.delete.success'/>", {status: 'success', timeout: 1000});

                                                                            $("#manageFavoritesButton").attr("onclick", "addPlayerToFavorites(" + id + ")");
                                                                            $("#manageFavoritesButton").removeClass("uk-button-active");
                                                                            jsReloadFavorites();
                                                                        } else {
                                                                            UIkit.notify("<spring:message code='favorites.delete.failed'/>", {status: 'warning', timeout: 1000});
                                                                        }
                                                                    }
                                                                });
                                                            }

                                                            function selectAllStatsReport() {
                                                                $("#reportPlayerStatsTypes > li.uk-active > button").addClass("uk-button-active");
                                                            }

                                                            function deselectAllStatsReport() {
                                                                $("#reportPlayerStatsTypes > li.uk-active > button").removeClass("uk-button-active");
                                                            }

                                                            function manageUploadLogo(playerId) {
                                                                if ($(event.target).attr("disabled")) {
                                                                    UIkit.notify("<spring:message code="upload.logo.permission.denied"/>", {status: 'danger', timeout: 1000});
                                                                    return;
                                                                }

                                                                if (typeof dropzone === "undefined") {
                                                                    dropzone = initializeLogoDropzone("uploadLogoDropzone");
                                                                }

                                                                dropzone.options.url = dropzone.options.baseUrl + "playerId=" + playerId;
                                                                $("#upload-logo-modal").addClass("uk-open");
                                                                $("#upload-logo-modal").removeClass("uk-hidden");
                                                            }

                                                            function closeUploadLogo() {
                                                                $("#upload-logo-modal").removeClass("uk-open");
                                                                $("#upload-logo-modal").addClass("uk-hidden");
                                                            }

                                                            function removeFilters() {
                                                                location.href = baseUrl;
                                                            }

                                                            function reportSearchParams() {
                                                                var input = $("#report-param-search").val().toLowerCase();
                                                                if (input && input.length >= 2) {
                                                                    $("#report-param-search-result-container").empty();

                                                                    var valid = [];
                                                                    $("#reportPlayerStatsTypes").find(".statsType-param").each(function (index, element) {
                                                                        var metric = $(element).text().trim();
                                                                        if (metric.toLowerCase().includes(input)) {
                                                                            var validText = metric + "|" + $(element).attr("data-value");
                                                                            if (!valid.includes(validText)) {
                                                                                valid.push(validText);
                                                                            }
                                                                        }
                                                                    });

                                                                    if (valid.length > 0) {
                                                                        valid.forEach(function (element) {
                                                                            var splitted = element.split("|");

                                                                            var newElement = "<li class='uk-hover'>";
                                                                            newElement += "<a onclick='activeButton(\"reportModalStatButton" + splitted[1] + "\");'>" + splitted[0] + "</a>";
                                                                            newElement += "</li>";
                                                                            $("#report-param-search-result-container").append(newElement);
                                                                        });

                                                                        $("#report-param-search-container").addClass("uk-open");
                                                                    }
                                                                } else {
                                                                    $("#report-param-search-container").removeClass("uk-open");
                                                                }
                                                            }

                                                            function activeButton(id) {
                                                                $("#" + id).addClass("uk-button-active");
                                                                $("#report-param-search").val(null).trigger("change");
                                                                $("#report-param-search-container").removeClass("uk-open");
                                                            }
        </script>

        <style>
            .container {
                display: flex;
                width: 100%;
            }

            .box2 {
                width: 50%;
                height: 100%;
                float: left;
            }

            .box3-header {
                width: 33%;
                height: 50%;
                float: left;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .box4-header {
                width: 25%;
                height: 50%;
                float: left;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .box3 {
                width: 33%;
                height: 50%;
                float: left;
            }

            .box100 {
                width: 100%;
                height: auto;
                float: left;
            }

            .box97 {
                width: 97%;
                height: auto;
                float: left;
            }

            .box70 {
                width: 70%;
                height: auto;
                float: left;
            }

            .box30 {
                width: 30%;
                height: auto;
                float: left;
            }

            .large-div {
                vertical-align: middle
            }

            .tableHeader {
                text-align: center;
                height: 10px;
            }

            .tableRow {
                text-align: center;
            }

            @media print {
                body * {
                    visibility:hidden;
                }
            }
        </style>
    </head>

    <body>

        <%@ include file="header.jsp" %>

        <div class="uk-grid">

            <div id="divTeamInfo" class="uk-width-6-10 uk-hidden-small">
                <input type="hidden" value="${mTeam.id}" id="idTeam"/>
                <input type="hidden" value="${mTeam.name}" id="teamName"/>
                <input type="hidden" value="${mComp.id}" id="idComp"/>
                <input type="hidden" value="${mComp.name}" id="compName"/>
                <input type="hidden" value="${mPlayer.id}" id="idPlayer"/>
                <input type="hidden" value="${gameIds}" id="gameIds"/>

                <div id="breadcrumb">
                    <ul class="uk-breadcrumb uk-margin-left ">
                        <c:if test="${mComp != null}">
                            <c:if test="${personalSource}">
                                <li>
                                    <a href="/sicstv/user/mycompetition.htm"><spring:message code="user.competitions"/></a>
                                </li>
                            </c:if>
                            <c:if test="${!personalSource}">
                                <!--li>
                                    <a href="/sicstv/user/home.htm"><spring:message code="user.competitions"/></a>
                                </li-->
                            </c:if>
                            <li><a href="/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${mComp.id}">${mComp.getName()}</a></li>
                            </c:if>
                            <c:if test="${mTeam != null}">
                            <li><a href="/sicstv/user/team.htm?personal=${personalSource}&formTeamId=${mTeam.id}<c:choose><c:when test="${mComp != null}">&formCompetitionId=${mComp.id}"</c:when><c:otherwise>&formCompetitionId=-1"</c:otherwise></c:choose>>${mTeam.name}</a></li>
                            </c:if>
                        <li><a href="/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}">${mPlayer.last_name} ${mPlayer.first_name} <i id="showCurrentFilter"></i></a></li>
                                <c:if test="${mGirone != null}">
                            <li style="text-transform: uppercase">${mGirone.groupName}</li>
                            </c:if>
                    </ul>
                </div>
                <div class="uk-text-center uk-margin-all">
                    <div class="container">
                        <div class="large-div uk-position-relative" style="flex-basis: 22%">
                            <center>
                                <c:if test="${personalSource || mUser.groupsetId == 2}">
                                    <span onclick="manageUploadLogo(${mPlayer.id})" class="uk-float-left uk-margin-remove uk-position-absolute" style="top: 3%; right: 10%;" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='upload.player.photo'/>"><i class="uk-icon-upload" ${mPlayer.groupset_id == -1 && mUser.groupsetId != 2 ? "disabled" : ""}></i></span>
                                    </c:if>
                                <img height="150px" width="150px" class="imgPlayerLogo" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayer.photo}.png" onerror="this.src='/sicstv/images/user_gray.png'" alt="${mPlayer.last_name} ${mPlayer.first_name}">

                                <c:set var="teamList" value=""/>
                                <c:set var="teamIdList" value=""/>
                                <c:forEach var="team" items="${mPlayer.teamPlayer}">
                                    <c:if test="${!teamList.isEmpty()}">
                                        <c:set var="teamList" value="${teamList} / "/>
                                        <c:set var="teamIdList" value="${teamIdList},"/>
                                    </c:if>
                                    <c:choose>
                                        <c:when test="${mComp != null}">
                                            <c:set var="teamList" value="${teamList}<a href='/sicstv/user/team.htm?personal=${personalSource}&formTeamId=${team.id}&formCompetitionId=${mComp.id}'>${team.name}</a>"/>
                                        </c:when>
                                        <c:otherwise>
                                            <c:set var="teamList" value="${teamList}<a href='/sicstv/user/team.htm?personal=${personalSource}&formTeamId=${team.id}&formCompetitionId=-1'>${team.name}</a>"/>
                                        </c:otherwise>
                                    </c:choose>
                                    <c:set var="teamIdList" value="${teamIdList}${team.id}"/>
                                </c:forEach>
                                <p style="font-size: 16px;">${mPlayer.known_name} <c:if test="${mTeam != null}"><span style="font-size: 12px">(#${mPlayer.numero})</span></c:if><br><span style="font-size: 10px" id="playerTeamsLabel"><c:choose><c:when test="${mTeam != null}"><a href="/sicstv/user/team.htm?personal=${personalSource}&formTeamId=${mTeam.id}<c:choose><c:when test="${mComp != null}">&formCompetitionId=${mComp.id}</c:when><c:otherwise>&formCompetitionId=-1</c:otherwise></c:choose>">${mTeam.name}</a></c:when><c:otherwise>${teamList}</c:otherwise></c:choose></span></p>
                                </center>
                            </div>
                            <div style="flex-basis: 62%">
                                <table class="uk-table">
                                    <thead>
                                                            <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.name.surname"/></th>
                                <th class="uk-text-center" style="border-bottom-width: 0; width: 20%;"><spring:message code="player.report.age"/></th>
                                <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.citizenship"/></th>
                                <th class="uk-text-center" style="border-bottom-width: 0; width: 30%;"><spring:message code="player.agency.market.contract"/></th>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="border-bottom-width: 0;">
                                            <span style="font-size: 15px; font-weight: normal">${mPlayer.first_name}</span><br>
                                            <span style="font-size: 15px; font-weight: normal">${mPlayer.last_name}</span>
                                        </td>
                                        <td style="border-bottom-width: 0;">
                                            <span style="font-size: 25px; font-weight: normal">${mPlayer.getAge()}</span><br>
                                            <span style="font-size: 13px; font-weight: normal">${mPlayer.getBornDatefull()}</span>
                                        </td>
                                        <td style="border-bottom-width: 0;">
                                            <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mPlayer.country_logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'"><br>
                                            <c:choose>
                                                <c:when test="${mLanguage == 'it'}">
                                                    <c:if test="${mPlayer.country_it.isEmpty()}">
                                                        <span style="font-size: 13px; font-weight: normal">N/A</span>
                                                    </c:if>
                                                    <c:if test="${!mPlayer.country_it.isEmpty()}">
                                                        <span style="font-size: 13px; font-weight: normal">${mPlayer.country_it}</span>
                                                    </c:if>
                                                </c:when>
                                                <c:otherwise>
                                                    <c:if test="${mPlayer.country_en.isEmpty()}">
                                                        <span style="font-size: 13px; font-weight: normal">N/A</span>
                                                    </c:if>
                                                    <c:if test="${!mPlayer.country_en.isEmpty()}">
                                                        <span style="font-size: 13px; font-weight: normal">${mPlayer.country_en}</span>
                                                    </c:if>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td style="border-bottom-width: 0;">
                                            <c:choose>
                                                <c:when test="${mPlayer.agentId != null}">
                                                    <a href="/sicstv/user/playerAgent.htm?agentId=${mPlayer.agentId}" class="uk-text-decoration-underline" style="font-size: 13px; font-weight: normal">${mPlayer.agentName}</a><br>
                                                </c:when>
                                                <c:otherwise>
                                                    <span style="font-size: 13px; font-weight: normal">N/A</span><br>
                                                </c:otherwise>
                                            </c:choose>
                                            <span style="font-size: 13px; font-weight: normal" title="${mPlayer.marketValue != null ? mPlayer.marketValue : ''}">${mPlayer.marketValue != null ? mPlayer.getMarketValueFormatted() : 'N/A'}</span><br>
                                            <span style="font-size: 13px; font-weight: normal">${mPlayer.contractExpires != null ? mPlayer.getContractExpiresString() : 'N/A'}</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <table class="uk-table">
                                <thead>
                                <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.height"/></th>
                                <th class="uk-text-center" style="border-bottom-width: 0; width: 20%;"><spring:message code="player.report.foot"/></th>
                                <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.role"/></th>
                                <th class="uk-text-center" style="border-bottom-width: 0; width: 30%;"><spring:message code="player.dati"/></th>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td style="border-bottom-width: 0;">
                                            <c:if test="${mPlayer.height.isEmpty()}">
                                                <span style="font-size: 13px; font-weight: normal">N/A</span><br>
                                            </c:if>
                                            <c:if test="${!mPlayer.height.isEmpty()}">
                                                <span style="font-size: 25px; font-weight: normal">${mPlayer.height.replace(" cm", "")}</span><br>
                                                <span style="font-size: 13px; font-weight: normal">cm</span></p>
                                            </c:if>
                                        </td>
                                        <td style="border-bottom-width: 0;">
                                            <c:if test="${mPlayer.foot_it.isEmpty()}">
                                                <img height="30px" width="30px" src="/sicstv/images/foot-none.png"><br>
                                                <span style="font-size: 13px; font-weight: normal">N/A</span><br>
                                            </c:if>
                                            <c:if test="${!mPlayer.foot_it.isEmpty()}">
                                                <c:if test="${mPlayer.foot_it == 'DESTRO'}">
                                                    <img height="30px" width="30px" src="/sicstv/images/foot-right.png"><br>
                                                </c:if>
                                                <c:if test="${mPlayer.foot_it == 'SINISTRO'}">
                                                    <img height="30px" width="30px" src="/sicstv/images/foot-left.png"><br>
                                                </c:if>
                                                <c:if test="${mPlayer.foot_it == 'ENTRAMBI'}">
                                                    <img height="30px" width="30px" src="/sicstv/images/foot.png"><br>
                                                </c:if>
                                                <c:choose>
                                                    <c:when test="${mLanguage == 'it'}">
                                                        <span style="font-size: 13px; font-weight: normal">${mPlayer.foot_it}</span><br>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span style="font-size: 13px; font-weight: normal">${mPlayer.foot_en}</span><br>
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:if>
                                        </td>
                                        <td style="border-bottom-width: 0;">
                                            <c:choose>
                                                <c:when test="${mUser.tvLanguage.equals('it')}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_it}</span><br>
                                                    <c:if test="${!mPlayer.descrizione_ruolo_it.equalsIgnoreCase(mPlayer.position_detail_it)}">
                                                        <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_it}</span><br>
                                                    </c:if>
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_it}</span>
                                                </c:when>
                                                <c:when test="${mUser.tvLanguage.equals('fr')}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_fr}</span><br>
                                                    <c:if test="${!mPlayer.descrizione_ruolo_fr.equalsIgnoreCase(mPlayer.position_detail_fr)}">
                                                        <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_fr}</span><br>
                                                    </c:if>
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_fr}</span>
                                                </c:when>
                                                <c:when test="${mUser.tvLanguage.equals('es')}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_es}</span><br>
                                                    <c:if test="${!mPlayer.descrizione_ruolo_es.equalsIgnoreCase(mPlayer.position_detail_es)}">
                                                        <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_es}</span><br>
                                                    </c:if>
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_es}</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_en}</span><br>
                                                    <c:if test="${!mPlayer.descrizione_ruolo_en.equalsIgnoreCase(mPlayer.position_detail_en)}">
                                                        <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_en}</span><br>
                                                    </c:if>
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_en}</span>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td style="border-bottom-width: 0; padding: 0;">
                                            <span style="font-size: 13px; text-transform: uppercase; font-weight: normal" id="playerMinutesPlayedLabel" default-text="<spring:message code="player.minutes.played"/>: "><spring:message code="player.minutes.played"/>: </span>
                                            <br/>
                                            <span style="font-size: 13px; text-transform: uppercase; font-weight: normal" id="playerMatchesPlayedLabel" default-text="<spring:message code="player.matches.played"/>: ${mInLineupAmount}/"><spring:message code="player.matches.played"/>: ${mInLineupAmount}/</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="large-div" style="flex-basis: 15%; text-align: right !important; margin-left: auto" id="canvasContainer">
                            <!--<img height="150px" width="150px" src="/sicstv/images/campo_2023_linee_${mPlayer.ruolo}.png">-->
                            <!--<canvas id="heatMapCanvas" width="141" height="215" style="position: absolute; height: 215px; width: 141px;"></canvas>-->
                        </div>
                    </div>
                    <c:if test="${mHasData}">
                        <div id="playerStats">
                            <div>
                                <div class="uk-text-left" style="margin: 2px" id="filterSicsSearchDiv">
                                    <button class="uk-button uk-button-active" id="filterSicsSearchButton" onclick="showSicsFilters('search');
                                            saveTab('filterSicsSearchButton', ${mPlayer.id});" title=""><spring:message code="video.ricerca"/></button>
                                    <button class="uk-button" id="filterSicsSearchAdvancedButton" onclick="showSicsFilters('advanced');
                                            saveTab('filterSicsSearchAdvancedButton', ${mPlayer.id});" title=""><spring:message code="video.ricercaAvanzata"/></button>
                                    <button id="filterSelectedActions" class="uk-button uk-hidden" onclick="applyAdvancedFilter()" title="<spring:message code='filtri.team.azioni.selezionate'/>"><i class="uk-icon-filter"></i><span id="filterSelectedActionsAmount"> 0</span></button>
                                    <button class="uk-button" id="generateReportButton" onclick="openReportModal();" title="<spring:message code="menu.report.player"/>"><i class="uk-icon-file-text-o"></i><span style="margin-left: 5px"><spring:message code="menu.report"/></span></button>
                                    <button class="uk-button" id="filterSicsOverviewButton" onclick="showSicsFilters('overview');
                                            saveTab('filterSicsOverviewButton', ${mPlayer.id});" title="<spring:message code="menu.panoramica"/>"><span style="margin-left: 5px"><spring:message code="menu.panoramica"/></span></button>
                                        <c:choose>
                                            <c:when test="${filterToShow.size() > 1}">
                                            <div class="uk-text-right" style="float: right">
                                                <span style="margin-right:5px;"><spring:message code="menu.configurazione"/></span>
                                                <div class="uk-button uk-form-select uk-margin-small-bottom" data-uk-form-select>
                                                    <span></span>
                                                    <i class="uk-icon-caret-down"></i>
                                                    <select onchange="changePanel('');" class='filterSelect'>

                                                        <c:forEach items="${filterToShow}" var="map" varStatus="status" >
                                                            <option value="${map.key}">
                                                                <c:choose>
                                                                    <c:when test="${map.key == 'SICS2015'}">
                                                                        SICS
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        ${map.key}
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </option>
                                                        </c:forEach>
                                                    </select>
                                                </div>
                                            </div>
                                        </c:when>
                                        <c:otherwise>
                                            <c:forEach items="${filterToShow}" var="map" varStatus="status" >
                                                <c:choose>
                                                    <c:when test="${map.key == 'SICS2015'}">

                                                    </c:when>
                                                    <c:when test="${map.key == 'SICS'}">

                                                    </c:when>
                                                    <c:otherwise>
                                                        <span style="margin-right:5px;"><spring:message code="menu.configurazione"/></span> ${map.key}
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:forEach>
                                        </c:otherwise>
                                    </c:choose>
                                    <c:if test="${!mUser.isPlayerUser()}">
                                        <div class="uk-text-right" style="float: right">
                                            <c:choose>
                                                <c:when test="${mPlayerInWatchlist}">
                                                    <button onclick="openWatchlistModal()" class="uk-button uk-button-small uk-button-active" title="<spring:message code="watchlist.add.players"/>"><i class="uk-icon-eye"></i></button>
                                                    </c:when>
                                                    <c:otherwise>
                                                    <button onclick="openWatchlistModal()" class="uk-button uk-button-small" title="<spring:message code="watchlist.add"/>"><i class="uk-icon-eye"></i></button>
                                                    </c:otherwise>
                                                </c:choose>
                                                <c:choose>
                                                    <c:when test="${mPlayerInFavorites}">
                                                    <button id="manageFavoritesButton" onclick="removePlayerFromFavorites(${mPlayer.id})" class="uk-button uk-button-small uk-button-active" title="<spring:message code="favorites.remove"/>"><i class="uk-icon-heart"></i></button>
                                                    </c:when>
                                                    <c:otherwise>
                                                    <button id="manageFavoritesButton" onclick="addPlayerToFavorites(${mPlayer.id})" class="uk-button uk-button-small" title="<spring:message code="favorites.add"/>"><i class="uk-icon-heart"></i></button>
                                                    </c:otherwise>
                                                </c:choose>
                                        </div>
                                    </c:if>
                                </div>
                            </div>

                            <div id="filterDiv">
                                <div class="filterSicsDiv" id="filterSicsSearch">
                                    <c:if test="${!personalSource}">
                                        <div class="uk-margin-small-top center uk-width-1-1">
                                            <button class='uk-button teamFilterButton panelSicsDiv' onclick="showPlayerEvents('SICS-_-TV');" ${mPersonalEvents == 0 ? 'disabled' : ''}><i class="uk-icon-video-camera"></i><spring:message code="lib.personal.clips"/> (${mPersonalEvents})</button>
                                        </div>
                                        <div class="uk-float-left panelSicsDiv">
                                            <button class='uk-button teamFilterButton' onclick="showPlayerBestEvents();" title=""><i class="uk-icon-video-camera"></i><spring:message code="lib.best.actions"/></button>
                                        </div>
                                        <div class="uk-float-right panelSicsDiv">
                                            <button class='uk-button teamFilterButton' onclick="showPlayerHighlight();" title=""><i class="uk-icon-video-camera"></i><spring:message code="lib.player.highlight"/></button>
                                        </div>
                                    </c:if>
                                    <div class="uk-float-left panelSicsDiv">
                                        <c:set var="count" value="0"/>
                                        <c:forEach items="${filterToShow['SICS'][0]}" var="btn">
                                            <c:set var="count" value="${count + 1}"/>
                                            <c:if test="${count % 2 == 1}">
                                                <button class='uk-button teamFilterButton' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="showPlayerEvents('SICS-_-${btn.key}');" title="<spring:message code='${btn.value[0]}'/>"><i class="uk-icon-video-camera" ></i><spring:message code="${btn.value[0]}"/>  (${btn.value[2]})</button>
                                            </c:if>   
                                        </c:forEach>
                                    </div>
                                    <div class="uk-float-right panelSicsDiv">
                                        <c:set var="count" value="0"/>
                                        <c:forEach items="${filterToShow['SICS'][0]}" var="btn">
                                            <c:set var="count" value="${count + 1}"/>
                                            <c:if test="${count % 2 == 0}">
                                                <button class='uk-button teamFilterButton' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="showPlayerEvents('SICS-_-${btn.key}');" title="<spring:message code='${btn.value[0]}'/>"><i class="uk-icon-video-camera" ></i><spring:message code="${btn.value[0]}"/>  (${btn.value[2]})</button>
                                            </c:if>    
                                        </c:forEach>
                                    </div>
                                </div>
                                <div class="filterGroupset">

                                </div>
                                <c:if test="${sicsAdvancedFilter != null}">
                                    <div uk-accordion data-uk-accordion="{collapse: false,showfirst: false}" class="uk-text-center uk-hidden" id="filterSicsSearchAdvanced">
                                        <c:set var="counter" value="0"/>
                                        <c:forEach items="${sicsAdvancedFilter}" var="btn">
                                            <c:set var="counter" value="${counter + 1}"/>
                                            <c:choose>
                                                <c:when test="${not fn:containsIgnoreCase(btn.key, '-')}">
                                                    <!-- Ho un evento padre -->
                                                    <c:set var="chiave" value="${btn.key}"/>
                                                    <c:set var="padreDisabled" value="${btn.value[1] == 'false'}"/>
                                                    <c:set var="chiaveCounter" value="0"/>

                                                    <c:if test="${counter > 1}">
                                                        <!-- Chiudo i div dei sotto eventi -->
                                                    </div>
                                                </div>
                                            </div>
                                            </span>
                                        </c:if>
                                        <span class="width-1-2-x">
                                            <button id="SICS2015-_-${chiave}" class='uk-button' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="addRemoveAdvancedFilter('${chiave}');" title="${btn.value[0]}">
                                                    <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down"></i>
                                                ${btn.value[0]} (${btn.value[2]})
                                            </button>
                                        </c:when>
                                        <c:otherwise>
                                            <!-- Ho un sotto evento -->
                                            <c:set var="figlioDisabled" value="${btn.value[2] == '0'}"/>
                                            <c:choose>
                                                <c:when test="${chiaveCounter == 0}">
                                                    <!-- Devo prima creare il div iniziale -->
                                                    <div data-wrapper="true" style="overflow: hidden; height: 0px; position: relative;" aria-expanded="false">
                                                        <div id="tag${chiaveCounter + 1}" class="uk-accordion-content uk-text-left">
                                                            <div class="tagEvento uk-form">
                                                                <div class="uk-margin-small buttonTag">
                                                                    <input type="checkbox" class="${chiave}" id="SICS2015_${chiave}_${btn.key}" class="uk-margin-right" onclick="addRemoveAdvancedFilter('${btn.key}');" value="${btn.value[0]}" <c:if test="${figlioDisabled}">disabled</c:if>>
                                                                    <label for="SICS2015_${chiave}_${btn.key}" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</label>
                                                                    <!--<button class='uk-button' style="width: 100%" <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="addRemoveAdvancedFilter('${btn.key}');" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</button>-->
                                                                    </div>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <!-- Basta aggiungere il tasto -->
                                                                <div class="uk-margin-small buttonTag">
                                                                    <!--<button class='uk-button' style="width: 100%" <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="addRemoveAdvancedFilter('${btn.key}');" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</button>-->
                                                                    <input type="checkbox" class="${chiave}" id="SICS2015_${chiave}_${btn.key}" class="uk-margin-right" onclick="addRemoveAdvancedFilter('${btn.key}');" value="${btn.value[0]}" <c:if test="${figlioDisabled}">disabled</c:if>>
                                                                    <label for="SICS2015_${chiave}_${btn.key}" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</label>
                                                                </div>
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <c:set var="chiaveCounter" value="${chiaveCounter + 1}"/>
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:forEach>
                                        </div>
                                    </div>
                                </div>
                            </span>
                        </div>
                    </c:if>
                    <div id="filterSicsOverview" class="uk-hidden" style="min-height: 10vh">
                        <center><div class="loader"></div></center>
                    </div>
                </div>
            </div>
        </c:if>
        <c:if test="${!mHasData}">
            <div>
                <span class="titlePlayerStats"><span id="no-data-content"><spring:message code="player.no.data"/></span>.<a class="uk-margin-left-small uk-text-decoration-underline" onclick="removeFilters();"><spring:message code="menu.view.all"/></a></span>
            </div>
        </c:if>
    </div>
</div>

<div id="divMatches" class="uk-width-4-10">

    <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#matchesCalendarContent',pos:'bottom-left'}">
        <li id="liPlayerInfo"><a class="uk-visible-small">${mPlayer.getKnown_name()}</a></li>
        <li id="teamTab"><a><spring:message code="menu.user.squadre"/></a></li>
        <li id="competitionTab"><a><spring:message code="user.competitions"/></a></li>
        <li id="calendarTab3"><a><spring:message code="menu.user.incontri"/> <span id="numAllMatches" class="uk-badge uk-hidden uk-hidden-small" onclick="jsRefreshMatches('1');">0</span></a></li> 
    </ul>

    <span class="uk-float-right uk-margin-small-top uk-margin-small-right uk-hidden-small">
        <button id="addToMulti" class="uk-button uk-button-small" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.multipartita'/>" onclick="jsGoToMultipartita('${personalSource}');"><i class="uk-icon-external-link  "></i></button>
        <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}" >
            <button id="selectedMatches" class="uk-button uk-button-small ${gameIdsAmount > 0 ? 'uk-button-active' : ''}" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.selezionati'/>"><i class="uk-icon-bars  "></i> ${gameIdsAmount != null ? gameIdsAmount : 0} </button> 
            <!--<a href="#" id="selectedMatches" class="uk-button uk-button-small" ><i class="uk-icon-bars  "></i> 0 </a>-->
            <span id="spanSelAll" class="uk-dropdown">
                <ul class="uk-nav uk-nav-navbar">
                    <li class="selAllSubnav"><a onclick="jsFilterGames(true);"><spring:message code="menu.selezionatutto"/></a></li>
                    <li class="uk-nav-divider"></li>
                    <li class="selAllSubnav"><a onclick="jsSelectAll(false, 'matchesCalendarContent');"><spring:message code="menu.deselezionatutto"/></a></li>
                </ul>							
            </span>
        </span>
        <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}" >
            <button id="filterGames" class="uk-button uk-button-small ${gameIdsAmount > 0 ? 'uk-button-active' : ''}" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='filtri.team.partite.selezionate'/>"><i class="uk-icon-filter"></i></button> 
            <span id="spanFilterGames" class="uk-dropdown">
                <ul class="uk-nav uk-nav-navbar">
                    <li class="selAllSubnav"><a onclick="jsFilterGames(false);"><spring:message code="filtri.team.partite.applica"/></a></li>
                    <li class="uk-nav-divider"></li>
                    <li class="selAllSubnav"><a onclick="jsFilterGames(true);"><spring:message code="filtri.team.partite.rimuovi"/></a></li>
                </ul>							
            </span>
        </span>
        <!--<c:if test="${gameIdsAmount > 0}">
            <button id="filterGames" class="uk-button uk-button-small uk-button-active" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='filtri.team.partite.rimuovi'/>" onclick="jsFilterGames(true);"><i class="uk-icon-filter"></i></button>
        </c:if>
        <c:if test="${gameIdsAmount == 0}">
            <button id="filterGames" class="uk-button uk-button-small" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='filtri.team.partite.applica'/>" onclick="jsFilterGames(false);"><i class="uk-icon-filter"></i></button>
        </c:if>-->
        <button id="btnSearch" class="uk-button uk-button-small" data-uk-offcanvas="{target:'#divFilter'}" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.ricerca'/>"><i class="uk-icon-search  "></i> </button>
    </span>

    <ul id="matchesCalendarContent" class="uk-switcher">
        <li id="liPlayerInfoContent" class="uk-visible-small">
            <div class="uk-text-center">
                <div class="container">
                    <div style="flex-basis: 61%; display: flex; justify-content: center; align-items: center">
                        <center>
                            <img height="150px" width="150px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayer.photo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknown_player.png'" alt="${mPlayer.last_name} ${mPlayer.first_name}">

                            <c:set var="teamList" value=""/>
                            <c:set var="teamIdList" value=""/>
                            <c:forEach var="team" items="${mPlayer.teamPlayer}">
                                <c:if test="${!teamList.isEmpty()}">
                                    <c:set var="teamList" value="${teamList} / "/>
                                    <c:set var="teamIdList" value="${teamIdList},"/>
                                </c:if>
                                <c:set var="teamList" value="${teamList}${team.name}"/>
                                <c:set var="teamIdList" value="${teamIdList}${team.id}"/>
                            </c:forEach>
                            <p style="font-size: 16px;">${mPlayer.known_name}<br><span style="font-size: 10px"><c:choose><c:when test="${mTeam != null}">${mTeam.name}</c:when><c:otherwise>${teamList}</c:otherwise></c:choose></span></p>
                                </center>
                            </div>
                            <div class="large-div" style="flex-basis: 39%">
                                    <img height="150px" width="150px" src="/sicstv/images/campo_2023_linee_${mPlayer.ruolo}.png">
                    </div>
                </div>
                <div id="playerStats">
                    <div id='statsTeam' class="uk-text-center">
                        <div class="container">
                            <div style="flex-basis: 100%">
                                <div class="box3-header">
                                    <center>
                                        <p style="font-size: 14px;"><spring:message code="player.report.name.surname"/><br>
                                            <span style="font-size: 15px">${mPlayer.first_name}</span><br>
                                            <span style="font-size: 15px">${mPlayer.last_name}</span></p>
                                    </center>
                                </div>
                                <div class="box3-header">
                                    <center>
                                        <p style="font-size: 14px;"><spring:message code="player.report.age"/><br>
                                            <span style="font-size: 25px">${mPlayer.getAge()}</span><br>
                                            <span style="font-size: 10px">${mPlayer.getBornDatefull()}</span></p>
                                    </center>
                                </div>
                                <div class="box3-header">
                                    <center>
                                        <p style="font-size: 14px; margin: 0"><spring:message code="player.report.citizenship"/></p>
                                        <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mPlayer.country_logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'"><br>
                                        <c:choose>
                                            <c:when test="${mLanguage == 'it'}">
                                                <c:if test="${mPlayer.country_it.isEmpty()}">
                                                    <span style="font-size: 10px">N/A</span>
                                                </c:if>
                                                <c:if test="${!mPlayer.country_it.isEmpty()}">
                                                    <span style="font-size: 10px">${mPlayer.country_it}</span>
                                                </c:if>
                                            </c:when>
                                            <c:otherwise>
                                                <c:if test="${mPlayer.country_en.isEmpty()}">
                                                    <span style="font-size: 10px">N/A</span>
                                                </c:if>
                                                <c:if test="${!mPlayer.country_en.isEmpty()}">
                                                    <span style="font-size: 10px">${mPlayer.country_en}</span>
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>
                                    </center>
                                </div>
                                <div class="box3-header">
                                    <center>
                                        <p style="font-size: 14px;"><spring:message code="player.report.height"/><br>
                                            <c:if test="${mPlayer.height.isEmpty()}">
                                                <span style="font-size: 25px">N/A</span><br>
                                            </c:if>
                                            <c:if test="${!mPlayer.height.isEmpty()}">
                                                <span style="font-size: 25px">${mPlayer.height.replace(" cm", "")}</span><br>
                                                <span style="font-size: 10px">cm</span></p>
                                            </c:if>
                                    </center>
                                </div>
                                <div class="box3-header">
                                    <center>
                                        <p style="font-size: 14px;"><spring:message code="player.report.foot"/><br>
                                            <c:if test="${mPlayer.foot_it.isEmpty()}">
                                                <img height="30px" width="30px" src="/sicstv/images/foot-none.png"><br>
                                                <span style="font-size: 10px">N/A</span><br>
                                            </c:if>
                                            <c:if test="${!mPlayer.foot_it.isEmpty()}">
                                                <c:if test="${mPlayer.foot_it == 'DESTRO'}">
                                                    <img height="30px" width="30px" src="/sicstv/images/foot-right.png"><br>
                                                </c:if>
                                                <c:if test="${mPlayer.foot_it == 'SINISTRO'}">
                                                    <img height="30px" width="30px" src="/sicstv/images/foot-left.png"><br>
                                                </c:if>
                                                <c:if test="${mPlayer.foot_it == 'ENTRAMBI'}">
                                                    <img height="30px" width="30px" src="/sicstv/images/foot.png"><br>
                                                </c:if>
                                                <c:choose>
                                                    <c:when test="${mLanguage == 'it'}">
                                                        <span style="font-size: 10px">${mPlayer.foot_it}</span><br>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <span style="font-size: 10px">${mPlayer.foot_en}</span><br>
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:if>
                                    </center>
                                </div>
                                <div class="box3-header">
                                    <center>
                                        <p style="font-size: 14px;"><spring:message code="player.report.role"/><br>
                                            <c:choose>
                                                <c:when test="${mUser.tvLanguage.equals('it')}">
                                                    <span style="font-size: 16px">${mPlayer.descrizione_ruolo_it}</span><br>
                                                    <c:if test="${!mPlayer.descrizione_ruolo_it.equalsIgnoreCase(mPlayer.position_detail_it)}">
                                                        <span style="font-size: 10px">${mPlayer.position_detail_it}</span><br>
                                                    </c:if>
                                                    <span style="font-size: 8px">${mPlayer.position_secondary_it}</span>
                                                </c:when>
                                                <c:when test="${mUser.tvLanguage.equals('fr')}">
                                                    <span style="font-size: 16px">${mPlayer.descrizione_ruolo_fr}</span><br>
                                                    <c:if test="${!mPlayer.descrizione_ruolo_fr.equalsIgnoreCase(mPlayer.position_detail_fr)}">
                                                        <span style="font-size: 10px">${mPlayer.position_detail_fr}</span><br>
                                                    </c:if>
                                                    <span style="font-size: 8px">${mPlayer.position_secondary_fr}</span>
                                                </c:when>
                                                <c:when test="${mUser.tvLanguage.equals('es')}">
                                                    <span style="font-size: 16px">${mPlayer.descrizione_ruolo_es}</span><br>
                                                    <c:if test="${!mPlayer.descrizione_ruolo_es.equalsIgnoreCase(mPlayer.position_detail_es)}">
                                                        <span style="font-size: 10px">${mPlayer.position_detail_es}</span><br>
                                                    </c:if>
                                                    <span style="font-size: 8px">${mPlayer.position_secondary_es}</span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span style="font-size: 16px">${mPlayer.descrizione_ruolo_en}</span><br>
                                                    <c:if test="${!mPlayer.descrizione_ruolo_en.equalsIgnoreCase(mPlayer.position_detail_en)}">
                                                        <span style="font-size: 10px">${mPlayer.position_detail_en}</span><br>
                                                    </c:if>
                                                    <span style="font-size: 8px">${mPlayer.position_secondary_en}</span>
                                                </c:otherwise>
                                            </c:choose>
                                    </center>
                                </div>
                            </div>
                        </div>
                    </div>


                    <c:if test="${filterToShow.size() > 1}"> 
                        <div style="border: 1px solid; padding: 5px;margin-top:10px;">
                            <c:forEach items="${filterToShow}" var="map" varStatus="status">
                                <button id="configChoice${map.key.replace(' ','_')}" onclick="changePanel('configChoice${map.key.replace(' ','_')}')" class="configChoiceMobile uk-button <c:if test="${map.key == 'SICS'}"> uk-button-success </c:if>" value="${map.key}">${map.key}</button>
                            </c:forEach>
                        </div>
                    </c:if>
                    <div class="uk-margin-top">
                        <div class="filterSicsDiv">
                            <div class="uk-float-left panelSicsDiv">
                                <c:set var="count" value="0"/>
                                <c:forEach items="${filterToShow['SICS'][0]}" var="btn">
                                    <c:set var="count" value="${count + 1}"/>
                                    <c:if test="${count % 2 == 1}">
                                        <button class='uk-button teamFilterButton' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="showPlayerEvents('SICS-_-${btn.key}');" title="<spring:message code='${btn.value[0]}'/>"><i class="uk-icon-video-camera" ></i><spring:message code="${btn.value[0]}"/>  (${btn.value[2]})</button>
                                    </c:if>   
                                </c:forEach>
                            </div>
                            <div class="uk-float-right panelSicsDiv">
                                <c:set var="count" value="0"/>
                                <c:forEach items="${filterToShow['SICS'][0]}" var="btn">
                                    <c:set var="count" value="${count + 1}"/>
                                    <c:if test="${count % 2 == 0}">
                                        <button class='uk-button teamFilterButton' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="showPlayerEvents('SICS-_-${btn.key}');" title="<spring:message code='${btn.value[0]}'/>"><i class="uk-icon-video-camera" ></i><spring:message code="${btn.value[0]}"/>  (${btn.value[2]})</button>
                                    </c:if>    
                                </c:forEach>
                            </div>
                        </div>
                        <div class="filterGroupset">

                        </div>
                    </div>
                </div>
            </div>
        </li>
        <li id="teams">
            <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-6 uk-width-1-1 uk-margin-small-top" >
                <c:forEach var="item" items="${mPlayer.teamPlayer}">
                    <li class="uk-margin-left" onclick="location.href = '/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}&teamId=${item.id}'" style="min-height: 155px;">
                        <a class="uk-text-center zoomOnHover" style="margin-top: 5px" href="/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}&teamId=${item.id}">
                            <div class="competitionBox" id="team-${item.id}">
                                <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${item.name}" width="100px" height="100px">
                            </div>
                            <p style="margin-bottom: 0; margin-top: 3px;">${item.name}</p>
                        </a>
                    </li>
                </c:forEach>
                <c:if test="${mPlayer.teamPlayer.size() > 1}">
                    <li class="uk-margin-left" onclick="location.href = '/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}&teamId=-1'" style="min-height: 155px;">
                        <a style="display: grid; place-items: center" class="uk-text-center zoomOnHover" style="margin-top: 5px" href="/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}&teamId=-1">
                            <div class="competitionBox" style="display: grid; grid-template-areas: 'first second' 'third fourth'; grid-template-rows: 42.5px 42.5px; grid-template-columns: 42.5px 42.5px; place-items: center; margin-top: 5px" id="team--1">
                                <c:set var="counter" value="${0}"/>
                                <c:forEach var="item" items="${mPlayer.teamPlayer}">
                                    <c:if test="${counter < 4}">
                                        <c:set var="counter" value="${counter + 1}"/>
                                        <img style="width: 42.5px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${item.name}">
                                    </c:if>
                                </c:forEach>
                            </div>
                            <p style="margin-bottom: 0; margin-top: 3px;"><spring:message code="menu.tutte"/></p>
                        </a>
                    </li>
                </c:if>
            </ul>
        </li>

        <li id="competitions">
            <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-6 uk-width-1-1 uk-margin-small-top" >
                <c:forEach var="item" items="${formCompetitionPlayer}">
                    <c:if test="${item.id == 12}">
                        <c:set var="isSerieD" value="${true}"/>
                    </c:if>
                    <c:if test="${item.id != 12}">
                        <li class="uk-margin-left" onclick="location.href = '/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}&competitionId=${item.id}<c:if test="${mTeam != null}">&teamId=${mTeam.id}</c:if>'" style="min-height: 155px;">
                            <a class="uk-text-center zoomOnHover" style="margin-top: 5px" href="/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}&competitionId=${item.id}<c:if test="${mTeam != null}">&teamId=${mTeam.id}</c:if>">
                                <div class="competitionBox" id="competition-${item.id}">
                                    <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                </div>
                                <p style="margin-bottom: 0; margin-top: 3px;">${item.name}</p>
                            </a>
                        </li>
                    </c:if>
                </c:forEach>
                <c:if test="${formCompetitionPlayer.size() > 1 && !isSerieD}">
                    <li class="uk-margin-left" onclick="location.href = '/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}&competitionId=-1&teamId=${mTeam.id == null ? -1 : mTeam.id}'" style="min-height: 155px;">
                        <a style="display: grid; place-items: center" class="uk-text-center zoomOnHover" style="margin-top: 5px" href="/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}&competitionId=-1&teamId=${mTeam.id == null ? -1 : mTeam.id}">
                            <div class="competitionBox" style="display: grid; grid-template-areas: 'first second' 'third fourth'; grid-template-rows: 42.5px 42.5px; grid-template-columns: 42.5px 42.5px; place-items: center; margin-top: 5px" id="competition--1">
                                <c:set var="counter" value="${0}"/>
                                <c:forEach var="item" items="${formCompetitionPlayer}">
                                    <c:if test="${counter < 4}">
                                        <c:set var="counter" value="${counter + 1}"/>
                                        <img style="width: 42.5px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                    </c:if>
                                </c:forEach>
                            </div>
                            <p style="margin-bottom: 0; margin-top: 3px;"><spring:message code="menu.tutte"/></p>
                        </a>
                    </li>
                </c:if>
                <c:forEach var="item" items="${mGironi}">
                    <li class="uk-margin-left" onclick="location.href = '/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}&competitionId=${item.id}<c:choose><c:when test="${mTeam != null}">&teamId=${mTeam.id}</c:when><c:otherwise></c:otherwise></c:choose>&groupId=${item.groupId}'" style="min-height: 155px;">
                        <a class="uk-text-center zoomOnHover" style="margin-top: 5px" href="/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}&competitionId=${item.id}<c:choose><c:when test="${mTeam != null}">&teamId=${mTeam.id}</c:when><c:otherwise></c:otherwise></c:choose>&groupId=${item.groupId}">
                            <div class="competitionBox" id="competition-${item.id}-${item.groupId}">
                                <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                            </div>
                            <p style="margin-bottom: 0; margin-top: 3px;">${item.name} - ${item.groupName}</p>
                        </a>
                    </li>
                </c:forEach>
            </ul>
        </li>
        <li id="allMatches" ></li> 
    </ul>

    <div id="divFilter" class="uk-offcanvas">
        <div class="uk-offcanvas-bar uk-offcanvas-bar-flip">
            <%@ include file="searchMulti.jsp" %>
        </div>
    </div>

</div>

</div>

<div id="reportModal" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
    <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 55%;width:auto;height:auto;" id="dropdownModifyPlaylist">
        <div>
            <span class="modalClose" onclick="closeReportModal();">&times;</span>
        </div>
        <div class="container" style="min-height: 25vh; max-height: 90vh;">
            <div class="large-div" style="flex-basis: 15%; border-right: 1px solid gray !important">
                <button onclick="showReportModalSimple()" style="width: 80%; border-bottom-left-radius: 0" class="uk-button uk-modal-close uk-button-active" id="reportModalSimple"><spring:message code="player.report.simple.title"/></button>
                <c:if test="${!isSerieD}">
                    <button onclick="showReportModalAdvanced()" style="width: 80%; border-top-left-radius: 0" class="uk-button uk-modal-close" id="reportModalAdvanced"><spring:message code="player.report.advanced.title"/></button>
                </c:if>
            </div>
            <div style="flex-basis: 85%" id="reportModalSimpleDiv">
                <div class="box2" id="reportPlayerRole">
                    <center>
                        <span style="font-size: 20px"><spring:message code="player.report.role.title"/></span><br>
                        <button id="reportModalA" data-value="4" onclick="reportChangeRole('reportModalA')" class="uk-button <c:if test="${mPlayer.ruolo.equals('A')}">uk-button-active</c:if>" style="min-width: 150px; margin-top: 25px"><spring:message code="ruoli.attaccante"/></button><br>
                        <button id="reportModalC" data-value="3" onclick="reportChangeRole('reportModalC')" class="uk-button <c:if test="${mPlayer.ruolo.equals('C')}">uk-button-active</c:if>" style="min-width: 150px; margin-top: 10px"><spring:message code="ruoli.centrocampista"/></button><br>
                        <button id="reportModalD" data-value="2" onclick="reportChangeRole('reportModalD')" class="uk-button <c:if test="${mPlayer.ruolo.equals('D')}">uk-button-active</c:if>" style="min-width: 150px; margin-top: 10px"><spring:message code="ruoli.difensore"/></button><br>
                        <button id="reportModalP" data-value="1" onclick="reportChangeRole('reportModalP')" class="uk-button <c:if test="${mPlayer.ruolo.equals('P')}">uk-button-active</c:if>" style="min-width: 150px; margin-top: 10px"><spring:message code="ruoli.portiere"/></button>
                        </center>
                    </div>
                    <div class="box2">
                        <p style="font-size: 20px;"><spring:message code="player.report.filters.title"/></p>
                    <p style="font-size: 14px; margin-top: 25px" id="reportModalSeason"></p>
                    <p style="font-size: 14px;" id="reportModalTeams"></p>
                    <p style="font-size: 14px;" id="reportModalCompetitions"></p>
                    <p style="font-size: 14px;"><spring:message code="player.report.min.matches"/>: <strong>3</strong></p>
                    <p style="font-size: 14px;"><spring:message code="player.report.min.minutes"/>: <strong>30</strong></p>
                    <p style="font-size: 14px;"><spring:message code="player.report.tipologia.confronto"/>: <strong><spring:message code="player.report.ruolo"/></strong></p>
                </div>
            </div>
            <div style="flex-basis: 85%" id="reportModalAdvancedDiv" class="uk-hidden">
                <!-- viene inizializzato tramite Javascript funzione manageColumnsModal() -->
                <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-float-left" id="reportPlayerStatsTypesButtonul" data-uk-tab="{connect:'#reportPlayerStatsTypes'}" style="margin-bottom: 10px">
                    <c:set var="isFirst" value="true"/>
                    <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                        <li><a style="border-top: 2px solid #dddddd;">${statGroup}</a></li>
                            <c:set var="isFirst" value="false"/>
                        </c:forEach>
                </ul>
                <div class="uk-float-right uk-autocomplete uk-form uk-margin-right-big" id="report-param-search-container">
                    <label><spring:message code="video.ricerca"/>:</label>
                    <input type="text" id="report-param-search" oninput="reportSearchParams();" class="uk-form-width-small"/>
                    <div class="uk-dropdown" aria-expanded="true">
                        <ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left" id="report-param-search-result-container">
                        </ul>
                    </div>
                </div>

                <ul id="reportPlayerStatsTypes" class="box97 uk-switcher" style="height: 33vh; overflow-y: auto; border-bottom: 1px solid gray">
                    <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                        <li>
                            <c:forEach var="group" items="${mGroupedColumns.get(statGroup).keySet()}">
                                <c:forEach var="stat" items="${mGroupedColumns.get(statGroup).get(group)}">
                                    <c:set var="extraClasses" value=""/>
                                    <c:choose>
                                        <c:when test="${mPlayer.ruolo.equals('A')}">
                                            <c:if test="${stat.strikerValid}">
                                                <c:set var="extraClasses" value="uk-button-active"/>
                                            </c:if>
                                        </c:when>
                                        <c:when test="${mPlayer.ruolo.equals('C')}">
                                            <c:if test="${stat.midfielderValid}">
                                                <c:set var="extraClasses" value="uk-button-active"/>
                                            </c:if>
                                        </c:when>
                                        <c:when test="${mPlayer.ruolo.equals('D')}">
                                            <c:if test="${stat.defenderValid}">
                                                <c:set var="extraClasses" value="uk-button-active"/>
                                            </c:if>
                                        </c:when>
                                        <c:when test="${mPlayer.ruolo.equals('P')}">
                                            <c:if test="${stat.goalkeeperValid}">
                                                <c:set var="extraClasses" value="uk-button-active"/>
                                            </c:if>
                                        </c:when>
                                    </c:choose>
                                    <button class="uk-button ${extraClasses} statsType-param" id="reportModalStatButton${stat.id}" onclick="jsManageActive(this)" style="width: 32%; line-height: 15px; margin-top: 2px" data-value="${stat.id}">${stat.desc}</button>
                                </c:forEach>
                            </c:forEach>
                        </li>
                    </c:forEach>
                </ul>
                <div class="box100" style="margin-top: 10px">
                    <p style="font-size: 20px; text-align: center"><spring:message code="player.report.filters.title"/></p>
                    <div class="uk-flex justify-content-center align-items-center uk-margin-bottom">
                        <label class="uk-margin-right-small"><spring:message code="lib.dataDa"/></label>
                        <input id="reportModalFrom" autocomplete="off" class="ui-state-default ui-corner-all inputSpacer3 uk-float-left" type="text" data-uk-datepicker="{format:'DD/MM/YYYY'}" size="20" style="text-align:center"/>
                        <label class="uk-margin-left uk-margin-right-small"><spring:message code="lib.dataA"/></label>
                        <input id="reportModalTo" autocomplete="off" class="ui-state-default ui-corner-all inputSpacer3 uk-float-left" type="text" data-uk-datepicker="{format:'DD/MM/YYYY'}" size="20" style="text-align:center"/>
                    </div>
                    <div class="box3" style="border-right: 1px solid gray">
                        <p style="font-size: 14px; text-align:center" id="reportModalSeasonAdvanced"></p>
                        <p style="font-size: 14px; text-align:center" id="reportModalSeasonAdvancedValue"></p>
                    </div>
                    <div class="box3" id="reportModalTeamsAdvanced" style="border-right: 1px solid gray">

                    </div>
                    <div class="box3" id="reportModalCompetitionsAdvanced">

                    </div>
                    <div class="box3 uk-hidden" id="reportModalGironiAdvanced">

                    </div>
                </div>
                <div class="box100" style="margin-top: 20px">
                    <div class="box3" id="reportModalTeamsAdvanced" style="border-right: 1px solid gray">
                        <center>
                            <p style="font-size: 14px"><spring:message code="player.report.min.matches"/></p>
                            <input type="number" id="reportModalMinMatches" min="1" max="999" value="3" class="uk-form-width-mini filter-select input-text-calendar-search" style="width: 50px">
                        </center>
                    </div>
                    <div class="box3" style="border-right: 1px solid gray">
                        <center>
                            <p style="font-size: 14px"><spring:message code="player.report.min.minutes"/></p>
                            <input type="number" id="reportModalMinMinutes" min="1" max="999" value="30" class="uk-form-width-mini filter-select input-text-calendar-search" style="width: 50px">
                        </center>
                    </div>
                    <div class="box3">
                        <center>
                            <p style="font-size: 14px"><spring:message code="player.report.tipologia.confronto"/></p>
                            <center>
                                <div style="display: flex; align-items: center">
                                    <div class="uk-width-expand">
                                        <span><spring:message code="player.report.competizione"/></span>
                                    </div>
                                    <div>
                                        <label class="uk-switch" for="reportModalConfronto">
                                            <input type="checkbox" id="reportModalConfronto">
                                            <div class="uk-switch-slider"></div>
                                        </label>
                                    </div>
                                    <div class="uk-width-expand">
                                        <span><spring:message code="player.report.ruolo"/></span>
                                    </div>
                                </div>
                            </center>
                        </center>
                    </div>
                </div>
            </div>
        </div>
        <div class="uk-modal-footer uk-width-1-1" style="display: flex">
            <div class="uk-text-left">
                <button style="min-width:80px;" onclick="selectAllStatsReport();" class="uk-text-large uk-button uk-modal-close hideOnSimple uk-hidden"><spring:message code="menu.selezionatutto"/></button>
                <button style="min-width:80px;" onclick="deselectAllStatsReport();" class="uk-text-large uk-button uk-modal-close hideOnSimple uk-hidden"><spring:message code="menu.deselezionatutto"/></button>
            </div>
            <div class="uk-text-right" style="margin-left: auto">
                <button style="min-width:80px;" onclick="generateReport();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.genera"/></button>
                <button style="min-width:80px;" onclick="closeReportModal();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                <span id="saveModifyPlaylist"></span>
            </div>
        </div>
    </div>
</div>

<div id="watchlistModal" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
    <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
        <div>
            <span class="modalClose" onclick="closeWatchlistModal();">&times;</span>
        </div>
        <div class="container" style="min-height: 25vh; max-height: 90vh;">
            <ul class="uk-nav uk-nav-dropdown">
                <li>
                    <span style="font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="watchlist.create"/></span>
                </li>
                <li style="margin-top: 10px">
                    <div style="width: 20%; float: left">
                        <spring:message code="watchlist.nome"/>
                    </div>
                    <div style="width: 80%">
                        <input type="text" id="nameWatchlist" maxlength="128"/>
                    </div>
                </li>
                <li style="margin-top: 10px">
                    <button onclick="addToWatchlist();" class="uk-button uk-dropdown-close"><spring:message code="watchlist.create"/></button>
                </li>
                <li id="watchlistButtonLi"<c:if test="${mWatchlist == null || mWatchlist.size() == 0}"> class="uk-hidden"</c:if>>
                        <div class="playlistTv">
                            <hr>
                            <span style="font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="watchlist.fastadd"/></span>
                    </div>
                    <div id="playlistTvButtonDiv">
                        <c:forEach var="item" items="${mWatchlist}">
                            <button onclick="addToWatchlist(${item.id});" class="uk-button uk-dropdown-close playlistTvButton" style="margin-right: 5px" <c:if test="${item.editable != null && !item.editable}"> disabled title="<spring:message code="watchlist.share.viewonly"/>"</c:if>>${item.name}<c:if test="${item.editable != null}"> (<spring:message code='menu.condivisa'/>)</c:if></button>
                        </c:forEach>
                    </div>
                </li>
                <li <c:if test="${mWatchlistWithPlayerIn == null || mWatchlistWithPlayerIn.size() == 0}"> class="uk-hidden"</c:if>>
                        <div class="playlistTv">
                            <hr>
                            <span style="font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="watchlist.fastremove"/></span>
                    </div>
                    <div id="playlistTvButtonDiv">
                        <c:forEach var="item" items="${mWatchlistWithPlayerIn}">
                            <button onclick="removeFromWatchlist(${item.id});" class="uk-button uk-dropdown-close playlistTvButton" style="margin-right: 5px" <c:if test="${item.editable != null && !item.editable}"> disabled title="<spring:message code="watchlist.share.viewonly"/>"</c:if>>${item.name}<c:if test="${item.editable != null}"> (<spring:message code='menu.condivisa'/>)</c:if></button>
                        </c:forEach>
                    </div>
                </li>
            </ul>
        </div>
        <div class="uk-modal-footer uk-text-right uk-width-1-1">
            <button style="min-width:80px;" onclick="closeWatchlistModal();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
            <span id="saveModifyPlaylist"></span>
        </div>
    </div>
</div>

<div id="upload-logo-modal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
    <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
        <div>
            <span class="modalClose" onclick="closeUploadLogo();">&times;</span>
            <h3><spring:message code="upload.player.photo"/></h3>
            <div class="uk-margin uk-modal-content uk-width-1-1">
                <form action="/" class="dropzone" id="uploadLogoDropzone">
                    <div class="dz-message">
                        <h2><spring:message code="menu.upload.selectlogo"/></h2>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

</body>
</html>