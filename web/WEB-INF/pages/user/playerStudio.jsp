<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/css/flat/flat.css" rel="stylesheet">
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />

        <!-- D3.js Library for canvas/svg -->
        <script src="https://d3js.org/d3.v5.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.0/d3-tip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>
        <script src="/sicstv/js/sics-canvas-vertical.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
        <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>

        <!-- Keep here: needed for teamStats.jsp -->
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/3.2.1/css/fixedColumns.dataTables.min.css"/>
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdn.datatables.net/fixedcolumns/3.2.1/js/dataTables.fixedColumns.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.colVis.min.js"></script>
        <!-- Keep here: needed for teamStats.jsp for buttons (export) -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
        <script src="https://cdn.datatables.net/colreorder/1.7.0/js/dataTables.colReorder.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <style>
            @page {
                size: A4;
            }

            @media screen {
                .mainContainer {
                    position: absolute;
                    left: 25%;
                }
            }

            @media print {
                .hideOnPrint {
                    display: none !important;
                    height: 0 !important;
                }

                html, body {
                    width: 210mm;
                    height: 297mm;
                }
            }

            .mainContainer {
                width: 794px;
            }

            .page {
                height: 1108px;
                padding-top: 15px;
                padding-right: 15px;
                padding-left: 15px;
            }

            p {
                font-size: 11px;
                margin: 0;
            }
            table {
                border-spacing: 0px;
            }
            table thead {
                position: static;
                background: #fff;
            }
            td {
                font-size: 8px;
                padding: 2px;
            }
            td:nth-child(1):not(.black) {
                font-weight: bold;
            }
            td.black {
                background-color: black !important;
                color: white !important;
                text-align: center;
                padding: 0;
            }
            th {
                padding: 0;
            }
            .theadStyled {
                font-size: 8px;
                font-weight: bold;
                border-top: 1px dashed gray !important;
                border-right: 1px dashed gray !important;
                border-left: 1px dashed gray !important;
                margin: 0;
                padding-top: 2px;
                padding-right: 5px;
                padding-left: 5px;
            }
            .theadStyledLeft {
                border-right: 0px !important;
            }
            .theadStyledRight {
                border-left: 0px !important;
            }
            .theadOrange {
                height: 5px;
                background-color: #EB5A10 !important;
            }
            .uk-table-striped tbody tr:nth-of-type(even) {
                background: #dadada !important;
            }
            .uk-table-striped tbody tr:nth-of-type(odd) {
                background: white !important;
            }
            .black-container {
                width: 20px;
            }
            .uk-width-7-10-calculated {
                width: calc(100% - 20px);
            }

            .red {
                background-color: #EB5A10 !important;
            }

            .black {
                background-color: #000000 !important;
            }

            .redQuad {
                width: 50px;
                height: 100%;
            }

            .headerQuad {
                width: 100%;
                height: 90px;
            }

            .container {
                display: flex;
                width: 100%;
            }

            .box2 {
                width: 50%;
                height: 50%;
                float: left;
            }

            .box3 {
                width: 33%;
                height: 50%;
                float: left;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .large-div {
                vertical-align: middle
            }

            .orangeBox {
                width: 50px;
                height: 42px;
                background-color: #EB5A10 !important;
            }

            .blackBox {
                width: calc(100% - 50px);
                height: 42px;
                background-color: black !important;
            }

            .verticalText {
                transform: rotate(-90deg);
                font-weight: bold;
            }

            .blackLine {
                width: 205px;
                height: 3px;
                background-color: black !important;
            }
            .rowBlackLine {
                width: 342px;
                height: 3px;
                background-color: black !important;
            }

            .orangeLine, .rowOrangeLine {
                width: 35px;
                height: 3px;
                background-color: #EB5A10 !important;
            }

            .teamLogo {
                width: 39px;    /* 42 (blackbox) - 3(blackLine) */
            }

            .playerInfo {
                font-size: 20px;
                font-weight: bold;
            }
            .boxTitle {
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 2px;
            }

            .half {
                width: 50%;
            }
            .row {
                margin-top: 10px;
            }

            .row .uk-flex-column:nth-child(2), .row .half:nth-child(2) {
                margin-left: 10px;
            }

            .whiteColor {
                color: white !important;
                font-size: 14px;
                font-weight: bold;
            }
            .whiteSubColor {
                color: white !important;
                font-size: 10px;
                font-weight: bold;
            }

            .blackBox p {
                margin-left: 5%;
            }
        </style>

        <script type="text/javascript">
            $('link[rel=stylesheet][href*="fonts.googleapis.com"]').remove();

            var eventMapSecondZone = new Map();
            var eventMapThirdZone = new Map();
            var eventMapFourthZone = new Map();
            var eventMapFifthZone = new Map();
            $(document).ready(function () {
                initSearchFilter();

            <c:forEach var="item" items="${mSecondZone.events}" >
                eventMapSecondZone.set('${item.id}', eval(${item.getJson(mLanguage)}));
            </c:forEach>
            <c:forEach var="item" items="${mThirdZone.events}" >
                eventMapThirdZone.set('${item.id}', eval(${item.getJson(mLanguage)}));
            </c:forEach>
            <c:forEach var="item" items="${mFourthZone.events}" >
                eventMapFourthZone.set('${item.id}', eval(${item.getJson(mLanguage)}));
            </c:forEach>
            <c:forEach var="item" items="${mFifthZone.events}" >
                eventMapFifthZone.set('${item.id}', eval(${item.getJson(mLanguage)}));
            </c:forEach>

                drawVerticalField('passaggiFattiContainer', 1);
                drawVerticalField('passaggiRicevutiContainer', 2);
                drawVerticalField('duelliDribblingContainer', 3);
                drawVerticalField('tocchiAreaContainer', 4);

                eventMapSecondZone.forEach((value, key) => {
                    drawVerticalEvent(1, value, true, 1);
                });
                eventMapThirdZone.forEach((value, key) => {
                    drawVerticalEvent(2, value, true, 2);
                });
                eventMapFourthZone.forEach((value, key) => {
                    drawVerticalEvent(3, value, true, 3);
                });
                eventMapFifthZone.forEach((value, key) => {
                    drawVerticalEvent(4, value, true, 4);
                });

                am4core.ready(function () {
                    // Create chart instance
                    var chart = am4core.create("chartdiv", am4charts.XYChart);
                    chart.hiddenState.properties.opacity = 0; // this creates initial fade-in
                    // Add data
                    chart.data = eval(${mSixthZone});

                    // Create axes
                    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
                    categoryAxis.dataFields.category = "year";
                    categoryAxis.renderer.minGridDistance = 5;
                    categoryAxis.renderer.grid.template.location = 0.5;
                    categoryAxis.renderer.labels.template.fontSize = 12;
                    categoryAxis.startLocation = 0.1;
                    categoryAxis.endLocation = 0.8;

                    // Create value axis
                    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
                    valueAxis.baseValue = 0;
                    valueAxis.renderer.labels.template.fontSize = 12;
                    valueAxis.extraMax = 0.1;

                    // Create series
                    var series = chart.series.push(new am4charts.LineSeries());
                    series.dataFields.valueY = "value";
                    series.dataFields.categoryX = "year";
                    series.strokeWidth = 3;
                    series.tensionX = 0.77;
                    series.stroke = am4core.color("#737373");
                    series.fill = series.stroke;
                    series.fillOpacity = 0.15;

                    var secondSeries = chart.series.push(new am4charts.LineSeries());
                    secondSeries.dataFields.valueY = "value2";
                    secondSeries.dataFields.categoryX = "year";
                    secondSeries.strokeWidth = 2;
                    secondSeries.tensionX = 0.77;
                    secondSeries.stroke = am4core.color("#f26521");

                    // bullet is added because we add tooltip to a bullet for it to change color
                    var bullet = series.bullets.push(new am4charts.Bullet());
                    bullet.adapter.add("fill", function (fill, target) {
                        if (target.dataItem.valueY < 0) {
                            return am4core.color("#FF0000");
                        }
                        return fill;
                    });

                    var circleBullet = series.bullets.push(new am4charts.CircleBullet());
                    circleBullet.circle.radius = 1;

                    var circleBulletSecond = secondSeries.bullets.push(new am4charts.CircleBullet());
                    circleBulletSecond.circle.radius = 5;
                    circleBulletSecond.circle.fill = am4core.color("#f26521");
                    var labelBulletSecond = secondSeries.bullets.push(new am4charts.LabelBullet());
                    labelBulletSecond.label.text = "{valueY}";
                    labelBulletSecond.label.fontSize = 12;
                    labelBulletSecond.label.dy = -25;

                    var range = valueAxis.createSeriesRange(series);
                    range.value = 0;
                    range.endValue = -1000;
                    range.contents.stroke = am4core.color("#FF0000");
                    range.contents.fill = range.contents.stroke;

                    // Add scrollbar

                    chart.cursor = new am4charts.XYCursor();

                }); // end am4core.ready()
            });
        </script>
    </head>
    <body>
        <div class="hideOnPrint" style="width: 100%"><%@ include file="header.jsp" %></div>

        <div class="mainContainer">
            <div class="page">
                <div class="uk-flex">
                    <div class="orangeBox"></div>
                    <div class="blackBox">
                        <div class="uk-flex uk-height-1-1">
                            <div class="uk-flex-column uk-width-7-10">
                                <p style="height: 50%" class="whiteColor center-vertically">
                                    ${mFixture.homeTeam} - ${mFixture.awayTeam}
                                </p>
                                <p style="height: 50%" class="whiteSubColor center-vertically">
                                    ${mFixture.competitionName} | <spring:message code="lib.giornata"/> ${mFixture.matchday} | ${mFixture.getDateFormatted()}
                                </p>
                            </div>
                            <div class="center-vertically">
                                <img height="40px" width="130px" src="/sicstv/images/logosics.png">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="uk-flex" style="align-items: flex-end;">
                    <div class="orangeBox">
                        <p class="verticalText">Player.Studio</p>
                    </div>
                    <div class="uk-flex-column">
                        <div class="uk-flex">
                            <img class="teamLogo" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mPlayer.teamPlayer.get(0).logo}.png">
                            <div class="uk-flex-column">
                                <p class="playerInfo">
                                    ${mPlayer.numero} - ${mPlayer.known_name} (${mPlayer.ruolo}) - ???'
                                </p>
                                <p>
                                    ${mPlayer.teamName}
                                </p>
                            </div>
                        </div>
                        <div class="uk-flex">
                            <div class="blackLine"></div>
                            <div class="orangeLine"></div>
                        </div>
                    </div>
                </div>
                <div class="uk-flex row">
                    <div class="half">
                        <table class="uk-table-striped uk-width-1-1">
                            <thead>
                            <th class="uk-width-7-10"></th>
                            <th class="uk-width-1-10">
                                <div class="uk-flex-column">
                                    <div class="theadOrange"></div>
                                    <p class="theadStyled theadStyledLeft">Totale</p>
                                </div>
                            </th>
                            <th class="uk-width-1-10">
                                <div class="uk-flex-column">
                                    <div class="theadOrange"></div>
                                    <p class="theadStyled">1T</p>
                                </div>
                            </th>
                            <th class="uk-width-1-10">
                                <div class="uk-flex-column">
                                    <div class="theadOrange"></div>
                                    <p class="theadStyled theadStyledRight">2T</p>
                                </div>
                            </th>
                            </thead>
                            <tbody>
                                <c:forEach var="row" items="${mMainTable}">
                                    <tr>
                                        <td>${row.key}</td>
                                        <td class="uk-text-center">${row.total}</td>
                                        <td class="uk-text-center">${row.firstTime}</td>
                                        <td class="uk-text-center">${row.secondTime}</td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                    <div class="half">
                        <div id="chartdiv" class="uk-width-1-1 uk-height-1-1">
                            
                        </div>
                    </div>
                </div>
                <div class="uk-flex row">
                    <div class="uk-flex-column">
                        <div>
                            <p class="boxTitle">Passaggi Fatti</p>
                        </div>
                        <div class="uk-flex">
                            <div class="rowBlackLine"></div>
                            <div class="rowOrangeLine"></div>
                        </div>
                        <div class="uk-margin-top-small uk-flex">
                            <table class="uk-table-striped uk-width-6-10 uk-height-1-1">
                                <thead>
                                <th class="black-container"></th>
                                <th class="uk-width-7-10-calculated"></th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled theadStyledLeft">Totale</p>
                                    </div>
                                </th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled">1T</p>
                                    </div>
                                </th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled theadStyledRight">2T</p>
                                    </div>
                                </th>
                                </thead>
                                <tbody>
                                    <c:forEach var="row" items="${mSecondZone.tableRows}">
                                        <tr>
                                            <td class="black">${row.number}</td>
                                            <td>${row.key}</td>
                                            <td class="uk-text-center">${row.total}</td>
                                            <td class="uk-text-center">${row.firstTime}</td>
                                            <td class="uk-text-center">${row.secondTime}</td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                            <div id="passaggiFattiContainer" class="uk-width-4-10 uk-margin-left-small">

                            </div>
                        </div>
                    </div>
                    <div class="uk-flex-column">
                        <div>
                            <p class="boxTitle">Passaggi Ricevuti</p>
                        </div>
                        <div class="uk-flex">
                            <div class="rowBlackLine"></div>
                            <div class="rowOrangeLine"></div>
                        </div>
                        <div class="uk-margin-top-small uk-flex">
                            <table class="uk-table-striped uk-width-6-10 uk-height-1-1">
                                <thead>
                                <th class="black-container"></th>
                                <th class="uk-width-7-10-calculated"></th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled theadStyledLeft">Totale</p>
                                    </div>
                                </th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled">1T</p>
                                    </div>
                                </th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled theadStyledRight">2T</p>
                                    </div>
                                </th>
                                </thead>
                                <tbody>
                                    <c:forEach var="row" items="${mThirdZone.tableRows}">
                                        <tr>
                                            <td class="black">${row.number}</td>
                                            <td>${row.key}</td>
                                            <td class="uk-text-center">${row.total}</td>
                                            <td class="uk-text-center">${row.firstTime}</td>
                                            <td class="uk-text-center">${row.secondTime}</td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                            <div id="passaggiRicevutiContainer" class="uk-width-4-10 uk-margin-left-small">

                            </div>
                        </div>
                    </div>
                </div>
                <div class="uk-flex row">
                    <div class="uk-flex-column">
                        <div>
                            <p class="boxTitle">Duelli e Dribbling</p>
                        </div>
                        <div class="uk-flex">
                            <div class="rowBlackLine"></div>
                            <div class="rowOrangeLine"></div>
                        </div>
                        <div class="uk-margin-top-small uk-flex">
                            <table class="uk-table-striped uk-width-6-10 uk-height-1-1">
                                <thead>
                                <th class="black-container"></th>
                                <th class="uk-width-7-10-calculated"></th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled">Duelli</p>
                                    </div>
                                </th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled theadStyledRight">Dribbling</p>
                                    </div>
                                </th>
                                </thead>
                                <tbody>
                                    <c:forEach var="row" items="${mFourthZone.tableRows}">
                                        <tr>
                                            <td class="black">${row.number}</td>
                                            <td>${row.key}</td>
                                            <td class="uk-text-center">${row.total}</td>
                                            <td class="uk-text-center">${row.firstTime}</td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                            <div id="duelliDribblingContainer" class="uk-width-4-10 uk-margin-left-small">

                            </div>
                        </div>
                    </div>
                    <div class="uk-flex-column">
                        <div>
                            <p class="boxTitle">Tocchi Palla</p>
                        </div>
                        <div class="uk-flex">
                            <div class="rowBlackLine"></div>
                            <div class="rowOrangeLine"></div>
                        </div>
                        <div class="uk-margin-top-small uk-flex">
                            <table class="uk-table-striped uk-width-6-10 uk-height-1-1 ignore-bold">
                                <thead>
                                <th class="uk-width-7-10"></th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled theadStyledLeft">Totale</p>
                                    </div>
                                </th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled">1T</p>
                                    </div>
                                </th>
                                <th class="uk-width-1-10">
                                    <div class="uk-flex-column">
                                        <div class="theadOrange"></div>
                                        <p class="theadStyled theadStyledRight">2T</p>
                                    </div>
                                </th>
                                </thead>
                                <tbody>
                                    <c:forEach var="row" items="${mFifthZone.tableRows}">
                                        <tr>
                                            <td>${row.key}</td>
                                            <td class="uk-text-center">${row.total}</td>
                                            <td class="uk-text-center">${row.firstTime}</td>
                                            <td class="uk-text-center">${row.secondTime}</td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                            <div id="tocchiAreaContainer" class="uk-width-4-10 uk-margin-left-small">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>