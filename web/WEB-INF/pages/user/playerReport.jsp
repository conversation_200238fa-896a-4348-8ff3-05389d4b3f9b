<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
<script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

<script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
<script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
<script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
<script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
<script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>

<script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/tooltip.js"></script>

<link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
<link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
<link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >

<script src="https://cdn.amcharts.com/lib/4/core.js"></script>
<script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
<script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>

<!-- D3.js Library for canvas/svg -->
<script src="https://d3js.org/d3.v5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.1/d3-tip.min.js"></script>
<script src="/sicstv/js/sics-canvas-player.js?<%=System.currentTimeMillis()%>"></script>
<script src="/sicstv/js/temperatureMap.js?<%=System.currentTimeMillis()%>"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

<div class="hideOnPrint" style="width: 100%"><%@ include file="header.jsp" %></div>
<div id="playerReport">

    <script type="text/javascript">
        $('link[rel=stylesheet][href*="fonts.googleapis.com"]').remove();
        $(document).ready(function () {
            initSearchFilter();

            //drawField('canvasContainer', pitchHeatMap);
            drawField('canvasContainer', pitch);
            var heatmapPoints = [];
        <c:if test="${mPositions.size() > 0}">
            var maxPercentage = 0;
            <c:forEach var="positionEntry" items="${mPositions}">
            var tmpPercentage = Math.round(${positionEntry.value} / ${mPositionTotal} * 100);
            if (tmpPercentage > maxPercentage) {
                maxPercentage = tmpPercentage;
            }
            </c:forEach>

            <c:forEach var="positionEntry" items="${mPositions}">
            drawPoint(${positionEntry.key.x}, ${positionEntry.key.y}, ${positionEntry.value}, ${mPositionTotal});

            var percentage = Math.round(${positionEntry.value} / ${mPositionTotal} * 100);
            var value = percentage / maxPercentage * 45;

            var tmpPoint = getCorrectPoint(${positionEntry.key.x}, ${positionEntry.key.y});
            var newPoint = {x: Math.round(tmpPoint.cx), y: Math.round(tmpPoint.cy), value: value};
            heatmapPoints.push(newPoint);
            </c:forEach>

            // creo un rettangolo intorno ai punti per evitare che le "chiazze" di colore
            // siano troppo larghe
            var rectangle = getPointsRectangle(heatmapPoints);
            for (var i = 0; i < rectangle.larghezza; i++) {
                if (i % 3 === 0) {
                    heatmapPoints.push({x: rectangle.x + i, y: rectangle.y, value: 0});
                    heatmapPoints.push({x: rectangle.x + i, y: rectangle.y + rectangle.altezza, value: 0});
                }
            }
            for (var i = 0; i < rectangle.altezza; i++) {
                if (i % 3 === 0) {
                    heatmapPoints.push({x: rectangle.x, y: rectangle.y + i, value: 0});
                    heatmapPoints.push({x: rectangle.x + rectangle.larghezza, y: rectangle.y + i, value: 0});
                }
            }
        </c:if>
        <c:if test="${mPositions.size() == 0}">
            drawPlayerRole("${mPlayer.ruolo}");
        </c:if>
        });
    </script>

    <style>
        @page {
            size: A4;
        }

        @media screen {
            #playerReport {
                position: absolute;
                left: 25%;
            }
        }

        @media print {
            .hideOnPrint {
                display: none !important;
                height: 0 !important;
            }

            html, body {
                width: 210mm;
                height: 297mm;
            }
        }

        #playerReport {
            max-width: 794px;
        }

        .tableHeader {
            text-align: center;
            height: 10px;
        }

        .tableRow {
            text-align: center;
        }

        .page {
            height: 1108px;
            padding-top: 15px;
            padding-right: 15px;
            padding-left: 15px;
        }

        .red {
            background-color: #EB5A10 !important;
        }

        .black {
            background-color: #000000 !important;
        }

        .redQuad {
            width: 50px;
            height: 100%;
        }

        .headerQuad {
            width: 100%;
            height: 90px;
        }

        .container {
            display: flex;
            width: 100%;
        }

        .box2 {
            width: 50%;
            height: 50%;
            float: left;
        }

        .box3 {
            width: 33%;
            height: 50%;
            float: left;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .box4 {
            width: 25%;
            height: 50%;
            float: left;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .large-div {
            vertical-align: middle
        }

        .container-progress-bar {
            width: 100%;
            background-color: #e5e5e5 !important;
            border-radius: 0.4rem;
            float: right;
            display: flex;
        }

        .progress {
            /*            background-color: green;*/
            height: 1.5rem;
            padding: 0;
            border-radius: 0.4rem;
            display: flex;
            justify-content: flex-start;
        }

        .progress span {
            font-weight: 700;
            text-align: center;
        }

    </style>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.3/jspdf.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <c:choose>
        <c:when test="${mTables == null || mTables.keySet().size() == 0}">
            <div class="page">
                <img height="40px" width="150px" src="/sicstv/images/logosics.png" style="margin-bottom: 15px; float: left">
                <div style="height: 35px; width: 1px; background-color: #adadad !important; float: left; margin-left: 5px; margin-right: 5px"></div>
                <p style="float: left; font-size: 25px; margin-top: 8px;">Player.Studio</p>
                <div class="container">
                    <div class="large-div uk-position-relative" style="flex-basis: 22%">
                        <center>
                            <c:if test="${personalSource || mUser.groupsetId == 2}">
                                <span onclick="manageUploadLogo(${mPlayer.id})" class="uk-float-left uk-margin-remove uk-position-absolute" style="top: 3%; right: 10%;" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='upload.player.photo'/>"><i class="uk-icon-upload" ${mPlayer.groupset_id == -1 && mUser.groupsetId != 2 ? "disabled" : ""}></i></span>
                                </c:if>
                            <img height="150px" width="150px" class="imgPlayerLogo" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayer.photo}.png" onerror="this.src='/sicstv/images/user_gray.png'" alt="${mPlayer.last_name} ${mPlayer.first_name}">

                            <c:set var="teamList" value=""/>
                            <c:set var="teamIdList" value=""/>
                            <c:forEach var="team" items="${mPlayer.teamPlayer}">
                                <c:if test="${!teamList.isEmpty()}">
                                    <c:set var="teamList" value="${teamList} / "/>
                                    <c:set var="teamIdList" value="${teamIdList},"/>
                                </c:if>
                                <c:choose>
                                    <c:when test="${mComp != null}">
                                        <c:set var="teamList" value="${teamList}<a href='/sicstv/user/team.htm?personal=${personalSource}&formTeamId=${team.id}&formCompetitionId=${mComp.id}'>${team.name}</a>"/>
                                    </c:when>
                                    <c:otherwise>
                                        <c:set var="teamList" value="${teamList}<a href='/sicstv/user/team.htm?personal=${personalSource}&formTeamId=${team.id}&formCompetitionId=-1'>${team.name}</a>"/>
                                    </c:otherwise>
                                </c:choose>
                                <c:set var="teamIdList" value="${teamIdList}${team.id}"/>
                            </c:forEach>
                            <p style="font-size: 16px;">${mPlayer.known_name} <c:if test="${mTeam != null}"><span style="font-size: 12px">(#${mPlayer.numero})</span></c:if><br><span style="font-size: 10px" id="playerTeamsLabel"><c:choose><c:when test="${mTeam != null}"><a href="/sicstv/user/team.htm?personal=${personalSource}&formTeamId=${mTeam.id}<c:choose><c:when test="${mComp != null}">&formCompetitionId=${mComp.id}</c:when><c:otherwise>&formCompetitionId=-1</c:otherwise></c:choose>">${mTeam.name}</a></c:when><c:otherwise>${teamList}</c:otherwise></c:choose></span></p>
                            </center>
                        </div>
                        <div style="flex-basis: 56%">
                            <table class="uk-table">
                                <thead>
                                                        <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.name.surname"/></th>
                            <th class="uk-text-center" style="border-bottom-width: 0; width: 20%;"><spring:message code="player.report.age"/></th>
                            <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.citizenship"/></th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <span style="font-size: 15px; font-weight: normal">${mPlayer.first_name}</span><br>
                                        <span style="font-size: 15px; font-weight: normal">${mPlayer.last_name}</span>
                                    </td>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <span style="font-size: 25px; font-weight: normal">${mPlayer.getAge()}</span><br>
                                        <span style="font-size: 13px; font-weight: normal">${mPlayer.getBornDatefull()}</span>
                                    </td>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mPlayer.country_logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'"><br>
                                        <c:choose>
                                            <c:when test="${mLanguage == 'it'}">
                                                <c:if test="${mPlayer.country_it.isEmpty()}">
                                                    <span style="font-size: 13px; font-weight: normal">N/A</span>
                                                </c:if>
                                                <c:if test="${!mPlayer.country_it.isEmpty()}">
                                                    <span style="font-size: 13px; font-weight: normal">${mPlayer.country_it}</span>
                                                </c:if>
                                            </c:when>
                                            <c:otherwise>
                                                <c:if test="${mPlayer.country_en.isEmpty()}">
                                                    <span style="font-size: 13px; font-weight: normal">N/A</span>
                                                </c:if>
                                                <c:if test="${!mPlayer.country_en.isEmpty()}">
                                                    <span style="font-size: 13px; font-weight: normal">${mPlayer.country_en}</span>
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table class="uk-table">
                            <thead>
                            <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.height"/></th>
                            <th class="uk-text-center" style="border-bottom-width: 0; width: 20%;"><spring:message code="player.report.foot"/></th>
                            <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.role"/></th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <c:if test="${mPlayer.height.isEmpty()}">
                                            <span style="font-size: 13px; font-weight: normal">N/A</span><br>
                                        </c:if>
                                        <c:if test="${!mPlayer.height.isEmpty()}">
                                            <span style="font-size: 25px; font-weight: normal">${mPlayer.height.replace(" cm", "")}</span><br>
                                            <span style="font-size: 13px; font-weight: normal">cm</span></p>
                                        </c:if>
                                    </td>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <c:if test="${mPlayer.foot_it.isEmpty()}">
                                            <img height="30px" width="30px" src="/sicstv/images/foot-none.png"><br>
                                            <span style="font-size: 13px; font-weight: normal">N/A</span><br>
                                        </c:if>
                                        <c:if test="${!mPlayer.foot_it.isEmpty()}">
                                            <c:if test="${mPlayer.foot_it == 'DESTRO'}">
                                                <img height="30px" width="30px" src="/sicstv/images/foot-right.png"><br>
                                            </c:if>
                                            <c:if test="${mPlayer.foot_it == 'SINISTRO'}">
                                                <img height="30px" width="30px" src="/sicstv/images/foot-left.png"><br>
                                            </c:if>
                                            <c:if test="${mPlayer.foot_it == 'ENTRAMBI'}">
                                                <img height="30px" width="30px" src="/sicstv/images/foot.png"><br>
                                            </c:if>
                                            <c:choose>
                                                <c:when test="${mLanguage == 'it'}">
                                                    <span style="font-size: 13px; font-weight: normal">${mPlayer.foot_it}</span><br>
                                                </c:when>
                                                <c:otherwise>
                                                    <span style="font-size: 13px; font-weight: normal">${mPlayer.foot_en}</span><br>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:if>
                                    </td>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <c:choose>
                                            <c:when test="${mUser.tvLanguage.equals('it')}">
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_it}</span><br>
                                                <c:if test="${!mPlayer.descrizione_ruolo_it.equalsIgnoreCase(mPlayer.position_detail_it)}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_it}</span><br>
                                                </c:if>
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_it}</span>
                                            </c:when>
                                            <c:when test="${mUser.tvLanguage.equals('fr')}">
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_fr}</span><br>
                                                <c:if test="${!mPlayer.descrizione_ruolo_fr.equalsIgnoreCase(mPlayer.position_detail_fr)}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_fr}</span><br>
                                                </c:if>
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_fr}</span>
                                            </c:when>
                                            <c:when test="${mUser.tvLanguage.equals('es')}">
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_es}</span><br>
                                                <c:if test="${!mPlayer.descrizione_ruolo_es.equalsIgnoreCase(mPlayer.position_detail_es)}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_es}</span><br>
                                                </c:if>
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_es}</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_en}</span><br>
                                                <c:if test="${!mPlayer.descrizione_ruolo_en.equalsIgnoreCase(mPlayer.position_detail_en)}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_en}</span><br>
                                                </c:if>
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_en}</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="large-div" style="flex-basis: 15%" id="canvasContainer">
                        <!--                        <center>
                                                    <img height="150px" width="150px" src="/sicstv/images/campo_2023_linee_${mPlayer.ruolo}.png">
                                                </center>-->
                    </div>
                </div>
                <center><p style="font-size: 16px; font-weight: bold; color: red; margin-top: 25px"><spring:message code="player.report.no.data"/></p></center>
            </div>
        </c:when>
        <c:otherwise>
            <c:set var="teamList" value=""/>
            <c:forEach var="team" items="${mTeams}">
                <c:if test="${!teamList.isEmpty()}">
                    <c:set var="teamList" value="${teamList} / "/>
                </c:if>
                <c:set var="teamList" value="${teamList}${team.name}"/>
            </c:forEach>
            <c:set var="competitionList" value=""/>
            <c:forEach var="competition" items="${mCompetitions}">
                <c:if test="${!competitionList.isEmpty()}">
                    <c:set var="competitionList" value="${competitionList} / "/>
                </c:if>
                <c:set var="competitionList" value="${competitionList}${competition.name}"/>
            </c:forEach>

            <div class="page">
                <img height="40px" width="150px" src="/sicstv/images/logosics.png" style="margin-bottom: 15px; float: left">
                <div style="height: 35px; width: 1px; background-color: #adadad !important; float: left; margin-left: 5px; margin-right: 5px"></div>
                <p style="float: left; font-size: 25px; margin-top: 8px;">Player.Studio</p>
                <button onclick="document.title = '${mPlayer.known_name}-${mPlayer.ruolo}-${mDay}';
                        window.print();
                        return false;" class="uk-button hideOnPrint" style="float: right"><spring:message code="report.save.pdf"/></button>
                <div class="container">
                    <div class="large-div" style="flex-basis: 22%">
                        <center>
                            <img height="150px" width="150px" class="imgPlayerLogo" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayer.photo}.png" onerror="this.src='/sicstv/images/user_gray.png'" alt="${mPlayer.last_name} ${mPlayer.first_name}">
                            <p style="font-size: 16px;">${mPlayer.known_name} <c:if test="${mTeams.size() == 1}"><span style="font-size: 12px">(#${mPlayer.numero})</span></c:if><br><span style="font-size: 10px">${teamList}</span></p>
                            </center>
                        </div>
                        <div style="flex-basis: 56%">
                            <table class="uk-table">
                                <thead>
                                <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.name.surname"/></th>
                            <th class="uk-text-center" style="border-bottom-width: 0; width: 20%;"><spring:message code="player.report.age"/></th>
                            <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.citizenship"/></th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <span style="font-size: 15px; font-weight: normal">${mPlayer.first_name}</span><br>
                                        <span style="font-size: 15px; font-weight: normal">${mPlayer.last_name}</span>
                                    </td>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <span style="font-size: 25px; font-weight: normal">${mPlayer.getAge()}</span><br>
                                        <span style="font-size: 13px; font-weight: normal">${mPlayer.getBornDatefull()}</span>
                                    </td>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mPlayer.country_logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png'"><br>
                                        <c:choose>
                                            <c:when test="${mLanguage == 'it'}">
                                                <c:if test="${mPlayer.country_it.isEmpty()}">
                                                    <span style="font-size: 13px; font-weight: normal">N/A</span>
                                                </c:if>
                                                <c:if test="${!mPlayer.country_it.isEmpty()}">
                                                    <span style="font-size: 13px; font-weight: normal">${mPlayer.country_it}</span>
                                                </c:if>
                                            </c:when>
                                            <c:otherwise>
                                                <c:if test="${mPlayer.country_en.isEmpty()}">
                                                    <span style="font-size: 13px; font-weight: normal">N/A</span>
                                                </c:if>
                                                <c:if test="${!mPlayer.country_en.isEmpty()}">
                                                    <span style="font-size: 13px; font-weight: normal">${mPlayer.country_en}</span>
                                                </c:if>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <table class="uk-table">
                            <thead>
                            <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.height"/></th>
                            <th class="uk-text-center" style="border-bottom-width: 0; width: 20%;"><spring:message code="player.report.foot"/></th>
                            <th class="uk-text-center" style="border-bottom-width: 0; width: 25%;"><spring:message code="player.report.role"/></th>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <c:if test="${mPlayer.height.isEmpty()}">
                                            <span style="font-size: 13px; font-weight: normal">N/A</span><br>
                                        </c:if>
                                        <c:if test="${!mPlayer.height.isEmpty()}">
                                            <span style="font-size: 25px; font-weight: normal">${mPlayer.height.replace(" cm", "")}</span><br>
                                            <span style="font-size: 13px; font-weight: normal">cm</span></p>
                                        </c:if>
                                    </td>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <c:if test="${mPlayer.foot_it.isEmpty()}">
                                            <img height="30px" width="30px" src="/sicstv/images/foot-none.png"><br>
                                            <span style="font-size: 13px; font-weight: normal">N/A</span><br>
                                        </c:if>
                                        <c:if test="${!mPlayer.foot_it.isEmpty()}">
                                            <c:if test="${mPlayer.foot_it == 'DESTRO'}">
                                                <img height="30px" width="30px" src="/sicstv/images/foot-right.png"><br>
                                            </c:if>
                                            <c:if test="${mPlayer.foot_it == 'SINISTRO'}">
                                                <img height="30px" width="30px" src="/sicstv/images/foot-left.png"><br>
                                            </c:if>
                                            <c:if test="${mPlayer.foot_it == 'ENTRAMBI'}">
                                                <img height="30px" width="30px" src="/sicstv/images/foot.png"><br>
                                            </c:if>
                                            <c:choose>
                                                <c:when test="${mLanguage == 'it'}">
                                                    <span style="font-size: 13px; font-weight: normal">${mPlayer.foot_it}</span><br>
                                                </c:when>
                                                <c:otherwise>
                                                    <span style="font-size: 13px; font-weight: normal">${mPlayer.foot_en}</span><br>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:if>
                                    </td>
                                    <td style="border-bottom-width: 0; text-align: center;">
                                        <c:choose>
                                            <c:when test="${mUser.tvLanguage.equals('it')}">
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_it}</span><br>
                                                <c:if test="${!mPlayer.descrizione_ruolo_it.equalsIgnoreCase(mPlayer.position_detail_it)}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_it}</span><br>
                                                </c:if>
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_it}</span>
                                            </c:when>
                                            <c:when test="${mUser.tvLanguage.equals('fr')}">
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_fr}</span><br>
                                                <c:if test="${!mPlayer.descrizione_ruolo_fr.equalsIgnoreCase(mPlayer.position_detail_fr)}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_fr}</span><br>
                                                </c:if>
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_fr}</span>
                                            </c:when>
                                            <c:when test="${mUser.tvLanguage.equals('es')}">
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_es}</span><br>
                                                <c:if test="${!mPlayer.descrizione_ruolo_es.equalsIgnoreCase(mPlayer.position_detail_es)}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_es}</span><br>
                                                </c:if>
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_es}</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.descrizione_ruolo_en}</span><br>
                                                <c:if test="${!mPlayer.descrizione_ruolo_en.equalsIgnoreCase(mPlayer.position_detail_en)}">
                                                    <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_detail_en}</span><br>
                                                </c:if>
                                                <span style="font-size: 13px; font-weight: normal" class="uk-text-uppercase">${mPlayer.position_secondary_en}</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="large-div" style="flex-basis: 15%" id="canvasContainer">
                        <!--                        <center>
                                                    <img height="150px" width="150px" src="/sicstv/images/campo_2023_linee_${mPlayer.ruolo}.png">
                                                </center>-->
                    </div>
                </div>
                <div style="margin-top: 20px">
                    <p style="text-align: center; font-size: 25px; width: 100%"><spring:message code="player.report.career.resume"/></p>
                    <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                        <tbody>
                            <c:set var="counter" value="${0}"/>
                            <c:set var="pages" value="${1}"/>
                            <c:set var="rowCounter" value="${0}"/>
                            <c:set var="maxRows" value="${30}"/>
                            <c:forEach var="table" items="${mCareer.keySet()}">
                                <c:set var="rowCounter" value="${rowCounter + 2}"/>
                                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                                    <td class="tableHeader" style="font-size: 20px">${table}</td>
                                    <td class="tableHeader" style="text-align: left"><c:if test="${counter == 0}"><spring:message code="player.report.team"/></c:if></td>
                                    <td class="tableHeader"><c:if test="${counter == 0}"><spring:message code="player.report.playtime"/></c:if></td>
                                    <td class="tableHeader"><c:if test="${counter == 0}"><spring:message code="player.report.attendance"/></c:if></td>
                                    </tr>
                                <c:forEach var="row" items="${mCareer.get(table)}">
                                    <c:set var="rowCounter" value="${rowCounter + 1}"/>
                                    <c:if test="${rowCounter >= maxRows}">
                                        <c:set var="maxRows" value="${40}"/>
                                        <c:set var="pages" value="${pages + 1}"/>
                                        <c:set var="rowCounter" value="${0}"/>
                                    </tbody></table></div><c:if test="${pages <= 2}"></div></c:if>
                            <div class="page"><table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;"><tbody>
                                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                                            <td class="tableHeader" style="font-size: 20px">${table}</td>
                                        <td class="tableHeader" style="text-align: left"><spring:message code="player.report.team"/></td>
                                        <td class="tableHeader"><spring:message code="player.report.playtime"/></td>
                                        <td class="tableHeader"><spring:message code="player.report.attendance"/></td>
                                    </tr>
                                </c:if>
                                <tr style="vertical-align: middle; background: white !important">
                                    <td style="vertical-align: middle"><img height="20px" width="20px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${row.competitionLogo}.png"><span style="margin-left: 5px">${row.competitionName}</span></td>
                                    <td style="vertical-align: middle"><img height="20px" width="20px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.teamLogo}.png"><span style="margin-left: 5px">${row.teamName}</span></td>
                                    <td style="vertical-align: middle; text-align: center">${row.getEventAmountFormatted()}</td>
                                    <td style="vertical-align: middle; text-align: center">${row.inLineupsAmount}/${row.fixtureAmount}</td>
                                </tr>
                            </c:forEach>
                            <c:set var="counter" value="${counter + 1}"/>
                        </c:forEach>
                    </tbody>
                </table>
                <c:if test="${pages <= 1}">
                </div>
            </c:if>
        </div>
    </c:otherwise>
</c:choose>

<!-- Sezione Statistiche Giocatore -->
<c:if test="${mPlayerStats != null && mPlayerStats.keySet().size() > 0 && mCompetitionAverage != null}">
    <div class="page">
        <div class="headerQuad" style="margin-top: 2px">
            <div class="red redQuad"><div style="transform: rotate(-90deg);margin-right: -55px;margin-left: -50px;"><span style="font-weight: bold">Player.Studio</span></div></div>
            <div class="black" style="width: 390px; height: 5px; margin-left: 50px; margin-top: -5px;"></div>
            <div class="red" style="width: 45px; height: 5px; margin-left: 440px; margin-top: -5px;"></div>
            <img height="40px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayer.photo}.png" onerror="this.src='/sicstv/images/user_gray.png';" alt="${mPlayer.last_name} ${mPlayer.first_name}" style="margin-top: -45px; margin-left: 50px">
            <p style="margin-top: -40px; margin-left: 90px; font-size: 16px; font-weight: bold;">${mPlayer.known_name}</p>
            <div class="black" style="width: 719px; height: 45px; margin-left: 50px; margin-top: -83px;"></div>
            <img height="40px" width="80px" src="/sicstv/images/logosics.png" style="margin-top: -35px; margin-left: 600px">
            <c:set var="isMultiTeam" value="false"/>
            <c:set var="teamList" value=""/>
            <c:set var="teamIdList" value=""/>
            <c:forEach var="team" items="${mTeams}">
                <c:if test="${!teamList.isEmpty()}">
                    <c:set var="isMultiTeam" value="true"/>
                    <c:set var="teamList" value="${teamList} / "/>
                </c:if>
                <c:set var="teamList" value="${teamList}${team.name}"/>
                <c:set var="teamIdList" value="${teamIdList}${team.id}"/>
            </c:forEach>
            <p style="margin-top: 26px; margin-left: 90px; font-size: 12px;">${teamList}</p>
            <c:if test="${mCompetitions != null && !mCompetitions.isEmpty()}">
                <c:set var="isMultiCompetition" value="false"/>
                <c:set var="competitionList" value=""/>
                <c:set var="competitionIdList" value=""/>
                <c:forEach var="competition" items="${mCompetitions}">
                    <c:if test="${!competitionList.isEmpty()}">
                        <c:set var="isMultiCompetition" value="true"/>
                        <c:set var="competitionList" value="${competitionList} / "/>
                    </c:if>
                    <c:set var="competitionList" value="${competitionList}${competition.name}"/>
                    <c:set var="competitionIdList" value="${competitionIdList}${competition.id}"/>
                </c:forEach>
            </c:if>
            <p style="margin-top: -95px; margin-left: 90px; font-size: 12px; font-weight: bold; color: white !important; max-width: 500px"><spring:message code="lib.stagione"/>: ${mSeason.name}<c:if test="${mIsAverageCompetition != null && !mIsAverageCompetition}"> <spring:message code="player.report.comparazione.ruolo"/>: ${mPosition.description}</c:if><br><spring:message code="player.report.competitions.title"/>: ${competitionList}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if></p>
            </div>

            <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                <tbody>
                    <!-- Colonne -->
                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                            <td class="tableHeader"><spring:message code="report.player.statistica"/></td>
                    <c:if test="${mIsAverageCompetition == null || mIsAverageCompetition}">
                        <td class="tableHeader"><spring:message code="report.player.posizione"/></td>
                    </c:if>
                    <c:if test="${mIsAverageCompetition != null && !mIsAverageCompetition}">
                        <td class="tableHeader"><spring:message code="report.player.posizione.ruolo"/></td>
                    </c:if>
                    <td class="tableHeader"><spring:message code="report.player.valore"/></td>
                    <td class="tableHeader" style="width: 45%"></td>
                    <c:if test="${mIsAverageCompetition == null || mIsAverageCompetition}">
                        <td class="tableHeader"><spring:message code="report.player.media.campionato"/></td>
                        <td class="tableHeader"><spring:message code="report.player.valore.massimo"/></td>
                    </c:if>
                    <c:if test="${mIsAverageCompetition != null && !mIsAverageCompetition}">
                        <td class="tableHeader"><spring:message code="report.player.media.ruolo"/></td>
                        <td class="tableHeader"><spring:message code="report.player.valore.massimo.ruolo"/></td>
                    </c:if>
                </tr>
                <c:set var="rowCounter" value="${0}"/>
                <c:set var="maxRows" value="${35}"/>
                <c:forEach var="statName" items="${mCompetitionAverage.keySet()}">
                    <c:set var="rowCounter" value="${rowCounter + 1}"/>
                    <c:if test="${rowCounter >= maxRows}">
                        <c:set var="rowCounter" value="${0}"/>
                        <c:set var="maxRows" value="${37}"/>
                    </tbody></table></div>
            <div class="page"><table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;"><tbody>
                        <!-- Colonne -->
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                            <td class="tableHeader"><spring:message code="report.player.statistica"/></td>
                            <c:if test="${mIsAverageCompetition == null || mIsAverageCompetition}">
                                <td class="tableHeader"><spring:message code="report.player.posizione"/></td>
                            </c:if>
                            <c:if test="${mIsAverageCompetition != null && !mIsAverageCompetition}">
                                <td class="tableHeader"><spring:message code="report.player.posizione.ruolo"/></td>
                            </c:if>
                            <td class="tableHeader"><spring:message code="report.player.valore"/></td>
                            <td class="tableHeader" style="width: 45%"></td>
                            <c:if test="${mIsAverageCompetition == null || mIsAverageCompetition}">
                                <td class="tableHeader"><spring:message code="report.player.media.campionato"/></td>
                                <td class="tableHeader"><spring:message code="report.player.valore.massimo"/></td>
                            </c:if>
                            <c:if test="${mIsAverageCompetition != null && !mIsAverageCompetition}">
                                <td class="tableHeader"><spring:message code="report.player.media.ruolo"/></td>
                                <td class="tableHeader"><spring:message code="report.player.valore.massimo.ruolo"/></td>
                            </c:if>
                        </tr>
                    </c:if>

                    <c:set var="row" value="${mPlayerStats.get(statName)}"/>
                    <c:set var="maxStatValue" value="${mCompetitionMax.get(statName)}"/>
                    <c:set var="minStatValue" value="${mCompetitionMin.get(statName)}"/>

                    <c:if test="${maxStatValue != null}">
                        <c:set var="rowPercentage" value="${row.getEventPercentage90Minutes(maxStatValue, minStatValue)}"/>
                        <tr>
                            <c:if test="${row.filter != null && mCompetition != -1 && mTeam != -1}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${isMultiCompetition == 'true' ? -1 : competitionIdList}&idTeam=${isMultiTeam == 'true' ? -1 : teamIdList}&idPlayer=${mPlayer.id}&goals=false&event=${row.getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td><a href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.encodeString(link)}" style="text-decoration: underline">${statName} (${row.getEventAmountFormatted()})</a></td>
                            </c:if>
                            <c:if test="${row.filter == null || mCompetition == -1 || mTeam == -1}">
                                <td>${statName} (${row.getEventAmountFormatted()})</td>
                            </c:if>
                            <td class="tableRow">${row.getIndexForTable()}</td>
                            <td class="tableRow">${row.getEventAmountAverage90Minutes1Decimal()}</td>
                            <td>
                                <div class="container-progress-bar">
                                    <c:set var="averagePercentage" value="${row.getPercentageByValues(mCompetitionAverage.get(statName), maxStatValue, minStatValue)}"/>

                                    <div style="margin-left: ${averagePercentage}%; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                    <c:set var="color" value="${row.statsTypeIsOpposite ? '#e85f5f' : 'green'}"/>
                                    <c:set var="halfAverage" value="${averagePercentage / 2}"/>
                                    <c:set var="secondHalfAverage" value="${(100 - averagePercentage) / 2}"/>
                                    <c:choose>
                                        <c:when test="${rowPercentage <= halfAverage}">
                                            <c:set var="color" value="${row.statsTypeIsOpposite ? 'green' : '#e85f5f'}"/>
                                        </c:when>
                                        <c:when test="${rowPercentage <= (averagePercentage - 1)}">
                                            <c:set var="color" value="${row.statsTypeIsOpposite ? '#5ab550' : '#e1e85f'}"/>
                                        </c:when>
                                        <c:when test="${rowPercentage <= (averagePercentage + secondHalfAverage)}">
                                            <c:set var="color" value="${row.statsTypeIsOpposite ? '#e1e85f' : '#5ab550'}"/>
                                        </c:when>
                                    </c:choose>
                                    <div class="progress" statsTypeIsOpposite="${row.statsTypeIsOpposite}" halfAverage="${halfAverage}" secondHalfAverage="${secondHalfAverage}" rowPercentage="${rowPercentage}" style="margin-left: -${averagePercentage}%; width: ${rowPercentage}%; background-color: ${color} !important">
                                    </div>
                                </div>
                            </td>
                            <td class="tableRow">${mStatsFunctions.valueWith1Decimal(mCompetitionAverage.get(statName))}</td>
                            <td class="tableRow">${mStatsFunctions.valueWith1Decimal(mCompetitionMax.get(statName))}</td>
                        </tr>
                    </c:if>
                </c:forEach>
            </tbody>
        </table>
    </div>
</c:if>

<!-- Sezione Matches -->
<c:if test="${mTables != null && mTables.keySet().size() > 0}">
    <div class="page">
        <div class="headerQuad" style="margin-top: 2px">
            <div class="red redQuad"><div style="transform: rotate(-90deg);margin-right: -55px;margin-left: -50px;"><span style="font-weight: bold">Player.Studio</span></div></div>
            <div class="black" style="width: 390px; height: 5px; margin-left: 50px; margin-top: -5px;"></div>
            <div class="red" style="width: 45px; height: 5px; margin-left: 440px; margin-top: -5px;"></div>
            <img height="40px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayer.photo}.png" onerror="this.src='/sicstv/images/user_gray.png';" alt="${mPlayer.last_name} ${mPlayer.first_name}" style="margin-top: -45px; margin-left: 50px">
            <p style="margin-top: -40px; margin-left: 90px; font-size: 16px; font-weight: bold;">${mPlayer.known_name}</p>
            <div class="black" style="width: 719px; height: 45px; margin-left: 50px; margin-top: -83px;"></div>
            <img height="40px" width="80px" src="/sicstv/images/logosics.png" style="margin-top: -35px; margin-left: 600px">
            <c:set var="teamList" value=""/>
            <c:forEach var="team" items="${mTeams}">
                <c:if test="${!teamList.isEmpty()}">
                    <c:set var="teamList" value="${teamList} / "/>
                </c:if>
                <c:set var="teamList" value="${teamList}${team.name}"/>
            </c:forEach>
            <p style="margin-top: 26px; margin-left: 90px; font-size: 12px;">${teamList}</p>
            <c:set var="competitionList" value=""/>
            <c:forEach var="competition" items="${mCompetitions}">
                <c:if test="${!competitionList.isEmpty()}">
                    <c:set var="competitionList" value="${competitionList} / "/>
                </c:if>
                <c:set var="competitionList" value="${competitionList}${competition.name}"/>
            </c:forEach>
            <p style="margin-top: -95px; margin-left: 90px; font-size: 12px; font-weight: bold; color: white !important; max-width: 500px"><spring:message code="lib.stagione"/>: ${mSeason.name}<c:if test="${mIsAverageCompetition != null && !mIsAverageCompetition}"> <spring:message code="player.report.comparazione.ruolo"/>: ${mPosition.description}</c:if><br><spring:message code="player.report.competitions.title"/>: ${competitionList}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if></p>
            </div>
            <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                <tbody>
                    <!-- Nome Competizione -->
                <c:set var="competition" value="${mGames.entrySet().iterator().next().getValue().competitionName}"/>
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                    <c:set var="counter" value="${0}"/>
                    <c:forEach var="column" items="${mGamesColumns}">
                        <c:choose>
                            <c:when test="${counter == 0}">
                                <td class="tableHeader" style="font-size: 15px; font-weight: bold">${competition}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if></td>
                            </c:when>
                            <c:otherwise>
                                <td></td>
                            </c:otherwise>
                        </c:choose>
                        <c:set var="counter" value="${counter + 1}"/>
                    </c:forEach>
                </tr>
                <!-- Colonne -->
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                    <c:forEach var="column" items="${mGamesColumns}">
                        <td class="tableHeader" style="font-size: 8px;">${column}</td>
                    </c:forEach>
                </tr>
                <!-- Righe -->
                <c:set var="rowCounter" value="${3}"/>
                <c:set var="maxRows" value="${32}"/>
                <c:if test="${mGamesColumns.size() > 15}">
                    <c:set var="maxRows" value="${23}"/>
                </c:if>

                <c:forEach var="row" items="${mGames.keySet()}">
                    <c:if test="${rowCounter >= maxRows}">
                    </tbody></table></div>
            <div class="page"><table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;"><tbody>
                        <c:set var="competition" value="${mGames.get(row).competitionName}"/>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                            <c:set var="counter" value="${0}"/>
                            <c:forEach var="column" items="${mGamesColumns}">
                                <c:choose>
                                    <c:when test="${counter == 0}">
                                        <td class="tableHeader" style="font-size: 15px; font-weight: bold">${competition}</td>
                                    </c:when>
                                    <c:otherwise>
                                        <td></td>
                                    </c:otherwise>
                                </c:choose>
                                <c:set var="counter" value="${counter + 1}"/>
                            </c:forEach>
                        </tr>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                            <c:forEach var="column" items="${mGamesColumns}">
                                <td class="tableHeader" style="font-size: 8px;">${column}</td>
                            </c:forEach>
                        </tr>
                        <c:set var="rowCounter" value="${3}"/>
                    </c:if>

                    <!-- Se cambia competizione scrivo di nuovo l'header -->
                    <c:if test="${!mGames.get(row).competitionName.equalsIgnoreCase(competition)}">
                        <c:set var="competition" value="${mGames.get(row).competitionName}"/>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                            <c:set var="counter" value="${0}"/>
                            <c:forEach var="column" items="${mGamesColumns}">
                                <c:choose>
                                    <c:when test="${counter == 0}">
                                        <td class="tableHeader" style="font-size: 15px; font-weight: bold">${competition}</td>
                                    </c:when>
                                    <c:otherwise>
                                        <td></td>
                                    </c:otherwise>
                                </c:choose>
                                <c:set var="counter" value="${counter + 1}"/>
                            </c:forEach>
                        </tr>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                            <c:forEach var="column" items="${mGamesColumns}">
                                <td class="tableHeader" style="font-size: 8px;">${column}</td>
                            </c:forEach>
                        </tr>
                        <c:set var="rowCounter" value="${rowCounter + 3}"/>
                    </c:if>
                    <tr style="background: white !important">
                        <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&fixtureId=${mGames.get(row).idFixture}&idComp=${mGames.get(row).competitionId}&idTeam=&idPlayer=&goals=false&event=&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                        <c:set var="gameTeams" value="${mGames.get(row).homeTeam} - ${mGames.get(row).awayTeam}"/>
                        <td style="width: 250px"><a href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${mGamesRows.entrySet().iterator().next().getValue().get(0).encodeString(link)}" style="text-decoration: none">${gameTeams} <p style="font-size: 8px; margin: 0">(${mGames.get(row).homeTeamScore}:${mGames.get(row).awayTeamScore}) ${mGames.get(row).getDateString()}</p></a></td>
                        <c:forEach var="column" items="${mGamesRows.get(row)}">
                            <c:choose>
                                <c:when test="${column.eventAmount == -1}">
                                    <td style="text-align: center; vertical-align: middle; font-size: 8px;">-</td>
                                </c:when>
                                <c:otherwise>
                                    <c:choose>
                                        <c:when test="${column.filter != null && column.playerId == mPlayer.id}">
                                            <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&fixtureId=${mGames.get(row).idFixture}&idComp=${mGames.get(row).competitionId}&idTeam=&idPlayer=${mPlayer.id}&goals=false&event=${column.getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                            <td style="text-align: center; vertical-align: middle; font-size: 8px"><a href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${column.encodeString(link)}" style="text-decoration: underline">${column.getEventAmountFormatted()}</a></td>
                                            </c:when>
                                            <c:otherwise>
                                            <td style="text-align: center; vertical-align: middle; font-size: 8px;">${column.getEventAmountFormatted()}</td>
                                        </c:otherwise>
                                    </c:choose>
                                </c:otherwise>
                            </c:choose>
                        </c:forEach>
                        <c:set var="rowCounter" value="${rowCounter + (gameTeams.length() > 30? 1.5 : 1)}"/>
                    </tr>
                </c:forEach>
            </tbody>
        </table>
    </div>
</c:if>

<c:set var="counter" value="${0}"/>
<c:forEach var="table" items="${mTables.keySet()}">
    <c:if test="${counter % 8 == 0}">
        <!-- Cambio pagina -->
        <c:if test="${counter > 0}">
        </div>
    </c:if>
    <div class="page">
        <div class="headerQuad">
            <div class="red redQuad"><div style="transform: rotate(-90deg);margin-right: -55px;margin-left: -50px;"><span style="font-weight: bold">Player.Studio</span></div></div>
            <div class="black" style="width: 390px; height: 5px; margin-left: 50px; margin-top: -5px;"></div>
            <div class="red" style="width: 45px; height: 5px; margin-left: 440px; margin-top: -5px;"></div>
            <img height="40px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayer.photo}.png" onerror="this.src='/sicstv/images/user_gray.png';" alt="${mPlayer.last_name} ${mPlayer.first_name}" style="margin-top: -45px; margin-left: 50px">
            <p style="margin-top: -40px; margin-left: 90px; font-size: 16px; font-weight: bold;">${mPlayer.known_name}</p>
            <div class="black" style="width: 719px; height: 45px; margin-left: 50px; margin-top: -83px;"></div>
            <img height="40px" width="80px" src="/sicstv/images/logosics.png" style="margin-top: -35px; margin-left: 600px">
            <c:set var="teamList" value=""/>
            <c:forEach var="team" items="${mTeams}">
                <c:if test="${!teamList.isEmpty()}">
                    <c:set var="teamList" value="${teamList} / "/>
                </c:if>
                <c:set var="teamList" value="${teamList}${team.name}"/>
            </c:forEach>
            <p style="margin-top: 26px; margin-left: 90px; font-size: 12px;">${teamList}</p>
            <c:set var="competitionList" value=""/>
            <c:forEach var="competition" items="${mCompetitions}">
                <c:if test="${!competitionList.isEmpty()}">
                    <c:set var="competitionList" value="${competitionList} / "/>
                </c:if>
                <c:set var="competitionList" value="${competitionList}${competition.name}"/>
            </c:forEach>
            <p style="margin-top: -95px; margin-left: 90px; font-size: 12px; font-weight: bold; color: white !important; max-width: 500px"><spring:message code="lib.stagione"/>: ${mSeason.name}<c:if test="${mIsAverageCompetition != null && !mIsAverageCompetition}"> <spring:message code="player.report.comparazione.ruolo"/>: ${mPosition.description}</c:if><br><spring:message code="player.report.competitions.title"/>: ${competitionList}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if></p>
            </div>
    </c:if>

    <c:if test="${counter % 2 == 0}">
        <div style="display: flex; margin-top: 5px;">
        </c:if>
        <div style="flex: 1;<c:if test="${counter % 2 != 0}"> margin-left: 5px; margin-top: 0px;</c:if>">
            <p style="margin-bottom: 1px; font-size: 15px"><strong>${table}</strong></p>
            <div class="black" style="width: 80%; height: 5px;"></div>
            <div class="red" style="width: 20%; height: 5px; margin-left: 80%; margin-top: -5px;"></div>
            <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                <tbody>
                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                        <td class="tableHeader"></td>
                        <td class="tableHeader"><spring:message code="player.report.player"/></td>
                        <td class="tableHeader"><spring:message code="player.report.total"/></td>
                        <c:if test="${mTables.get(table).get(0).statsTypeId != 1}">
                            <td class="tableHeader"><spring:message code="player.report.average.90"/></td>
                        </c:if>
                    </tr>
                    <c:forEach var="row" items="${mTables.get(table)}">
                        <c:if test="${row.playerId == mPlayer.id || !mUser.isPlayerUser()}">
                            <c:set var="trStyle" value=""/>
                            <c:if test="${row.playerId == mPlayer.id && !mUser.isPlayerUser()}">
                                <c:set var="trStyle" value="${trStyle}background-color: orange !important;"/>
                            </c:if>
                            <c:if test="${row.index == 3 && !mUser.isPlayerUser()}">
                                <c:set var="trStyle" value="${trStyle}border-bottom: 2px solid black !important;"/>
                            </c:if>
                            <tr style="${trStyle}">
                                <td style="padding: 0; vertical-align: middle">
                        <center>
                            <c:choose>
                                <c:when test="${row.index == 1}">
                                    ${row.getIndexForTable()}
                                    <!--<img height="15px" width="15px" src="/sicstv/images/1st.png">-->
                                </c:when>
                                <c:when test="${row.index == 2}">
                                    ${row.getIndexForTable()}
                                    <!--<img height="15px" width="15px" src="/sicstv/images/2nd.png">-->
                                </c:when>
                                <c:when test="${row.index == 3}">
                                    ${row.getIndexForTable()}
                                    <!--<img height="15px" width="15px" src="/sicstv/images/3rd.png">-->
                                </c:when>
                                <c:otherwise>
                                    ${row.getIndexForTable()}
                                </c:otherwise>
                            </c:choose>
                        </center>
                        </td>
                        <td style="padding: 0; vertical-align: middle">
                            <img height="18px" width="18px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.teamLogo}.png" title="${row.teamName}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'">
                            ${row.playerKnownName}
                        </td>
                        <c:choose>
                            <c:when test="${row.filter != null && row.playerId == mPlayer.id && row.eventAmount > 0 && mCompetition != -1 && mTeam != -1}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp="/>
                                <c:if test='${mCompetition != -1}'>
                                    <c:set var="link" value="${link}${mCompetition}"/>
                                </c:if>
                                <c:set var="link" value="${link}&idTeam="/>
                                <c:if test='${mTeam != -1}'>
                                    <c:set var="link" value="${link}${mTeam}"/>
                                </c:if>
                                <c:set var="link" value="${link}&idPlayer=${mPlayer.id}&goals=false&event=${row.getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td class="tableRow"><a href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.encodeString(link)}" style="text-decoration: underline; font-weight: bold">${row.getEventAmountFormatted()}</a></td>
                                </c:when>
                                <c:otherwise>
                                <td class="tableRow">${row.getEventAmountForTable()}</td>
                            </c:otherwise>
                        </c:choose>
                        <c:if test="${row.statsTypeId != 1}">
                            <td class="tableRow">${row.getEventAmountAverage90Minutes2Decimals()}</td>
                        </c:if>
                        </tr>
                    </c:if>
                </c:forEach>
                </tbody>
            </table>
        </div>

        <c:if test="${counter % 2 != 0}">
        </div>
    </c:if>
    <c:if test="${(counter + 1) == mTables.keySet().size()}">
        <c:if test="${counter % 2 == 0}">
            <div style="flex: 1;<c:if test="${counter % 2 != 0}"> margin-left: 5px; margin-top: 0px;</c:if>"></div></div>
        </c:if>
    </c:if>

<c:set var="counter" value="${counter + 1}"/>
</c:forEach>

</div>