<link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
<link rel="icon" href="/sicstv/images/favicon.ico">
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<script type="text/javascript" src="/sicstv/js/jquery.timer.js"></script>

<!-- Stile predefinito di Tippy.js (opzionale) -->
<link rel="stylesheet" href="/sicstv/css/tippy.css">
<!--<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/themes/light.css"/>-->
<!--<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/themes/light-border.css"/>-->
<!--<link rel="stylesheet" href="https://unpkg.com/tippy.js@6/themes/translucent.css"/>-->
<link rel="stylesheet" href="/sicstv/css/tippy/light.min.css"/>
<link rel="stylesheet" href="/sicstv/css/tippy/light-border-min.css"/>
<link rel="stylesheet" href="/sicstv/css/tippy/translucent.min.css"/>
<!-- Script di Tippy.js -->
<!--<script src="https://unpkg.com/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>-->
<!--<script src="https://unpkg.com/tippy.js@6.3.1/dist/tippy.umd.min.js"></script>-->
<script src="/sicstv/js/tippy/popper.min.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
<script src="/sicstv/js/tippy/tippy.umd.min.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
<script src="/sicstv/js/blockUI.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

<style>
    @media screen {
        #printSection {
            display: none;
        }
    }

    @media print {
        #printSection, #printSection * {
            visibility:visible;
        }
        #printSection {
            position:absolute;
            left:0;
            top:0;
        }
    }
</style>

<script type="text/javascript">
    var multiLogin = "${mUser.getMulti_login()}";

    var timerProgressStatus;
    var exportIdentifier = sessionStorage['exportIdentifier'];

    var strings = new Array();
    strings['menu.user.vittorie'] = "<spring:message code='menu.user.vittorie' javaScriptEscape='true' />";
    strings['menu.user.pareggi'] = "<spring:message code='menu.user.pareggi' javaScriptEscape='true' />";
    strings['menu.user.sconfitte'] = "<spring:message code='menu.user.sconfitte' javaScriptEscape='true' />";
    strings['menu.user.incasa'] = "<spring:message code='menu.user.incasa' javaScriptEscape='true' />";
    strings['menu.user.intrasferta'] = "<spring:message code='menu.user.intrasferta' javaScriptEscape='true' />";
    strings['menu.user.daData'] = "<spring:message code='menu.user.daData' javaScriptEscape='true' />";
    strings['menu.user.aData'] = "<spring:message code='menu.user.aData' javaScriptEscape='true' />";

    function updateUnseenPlaylist() {
        var notifyNewPlaylist = 1;
        $.ajax({
            type: "GET",
            url: "/sicstv/user/getUnseen.htm",
            //data: "identifier=" + ids,
            cache: false,
            success: function (msg) {
                notifyNewPlaylist = parseInt(msg);
            },
            async: false
        });

        if (typeof sessionStorage['exportIdentifier'] !== 'undefined') {
            var splitSessionStorage = sessionStorage['exportIdentifier'].split("-");
            // se il primo � vuoto allora lo tolgo
            if (!splitSessionStorage[0]) {
                splitSessionStorage.splice(0, 1);
            }
            notifyNewPlaylist += splitSessionStorage.length;
        }


        if (notifyNewPlaylist > 0) {
            $("#notifyNewPlaylist").removeClass("uk-hidden");
            $("#notifyNewPlaylist").html(notifyNewPlaylist);
            $("#notifyNewPlaylistSmall").removeClass("uk-hidden");
            $("#notifyNewPlaylistSmall").html(notifyNewPlaylist);
        } else {
            $("#notifyNewPlaylist").addClass("uk-hidden");
            $("#notifyNewPlaylistSmall").addClass("uk-hidden");
        }
    }

    function showLoadingMessage() {
        $("#loadingDivNotifier").removeClass("uk-hidden");
        jsLoadingAlpha("#loadingNotifier");
    }

    function hideLoadingMessage() {
        $("#loadingDivNotifier").addClass("uk-hidden");
    }

    function checkSession() {
        $.ajax({
            type: "GET",
            url: "/sicstv/user/isValidSession.htm",
            data: encodeURI("identifier=1"),
            cache: false,
            success: function (msg) {
                var continueLoop = true;
                if (msg === "askClearSession") {
                    // Get the modal
                    continueLoop = false;
                    $("#myModal").addClass("uk-open");
                    $("#myModal").removeClass("uk-hidden");
                } else if (msg === "expired") {
                    ajaxSessionTimeoutExpired();
                } else if (msg !== "true") {
                    ajaxSessionTimeoutDisposed();
                }

                if (continueLoop) {
                    setTimeout(function () {
                        checkSession();
                    }, 5000);
                }
            }
        });
    }

    var exportTippyElement, flagLanguageTippy;
    $(document).ready(function () {
        if (multiLogin === "false") {
            // controllo subito
            checkSession();
        }

//        if (typeof isMyHome !== 'undefined' || typeof calendarPage !== 'undefined') {
//            $("#searchbarteamplayer").addClass("uk-hidden");
//        }
//        if (typeof isPersonalArchive !== 'undefined') {
//            $("#searchbarpersonal").removeClass("uk-hidden");
//        }
        if (typeof calendarPage !== 'undefined') {
            $("#seasonDiv").addClass("uk-hidden");
        }
        if (typeof timerProgressExport === 'undefined' && typeof exportIdentifier !== 'undefined') {
            $("#exportAction").removeClass("uk-hidden");
            if (typeof timerProgressStatus !== 'undefined') {
                timerProgressStatus.stop();
            }

            var ids = sessionStorage['exportIdentifier'];
            //alert("timer");
            timerProgressStatus = $.timer(function () {
                if (typeof ids === "undefined") {
                    timerProgressStatus.stop();
                }

                if (!${mExportUseLambda}) {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/isExportRunning.htm",
                        data: encodeURI("identifier=" + ids),
                        cache: false,
                        success: function (msg) {
                            if (msg === "true") {
                                $("#exportRunningNotify").removeClass("uk-hidden");
                            } else {
                                $("#exportRunningNotify").addClass("uk-hidden");
                                updateUnseenPlaylist();
                                timerProgressStatus.stop();
                                //                            UIkit.notify("<spring:message code='playlist.download.end'/>", {status: 'success', timeout: 1000});

                                if (msg !== "false" && typeof sessionStorage['exportIdentifier'] !== "undefined") {
                                    var strUrl = "/sicstv/user/downloadPlaylist.htm?id=" + msg;
                                    $.ajax({
                                        type: "GET",
                                        url: strUrl,
                                        contentType: "application/force-download",
                                        cache: false,
                                        success: function (msg) {
                                            if (msg.substr(0, 4) === "true") {
                                                UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                                            } else {
                                                UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                                                window.location.replace(msg.substr(5));
                                                $.unblockUI();
                                            }
                                        }
                                    });
                                    sessionStorage.removeItem('exportIdentifier');
                                }
                            }
                        }
                    });
                } else {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/isExportRunningLambda.htm",
                        cache: false,
                        success: function (exportInfo) {
                            if (exportInfo.trim().length > 0) {
                                var scriptToExecute = exportInfo.substring(exportInfo.indexOf('// START') + 8, exportInfo.indexOf('// END')).trim();
                                if (scriptToExecute && scriptToExecute.length > 0) {
                                    eval(scriptToExecute);
                                }

                                $("#exportRunningNotify").removeClass("uk-hidden");
                                if (typeof exportTippyElement === 'undefined') {
                                    exportTippyElement = tippy("#exportRunningNotify", {
                                        theme: 'light-border',
                                        content: exportInfo,
                                        maxWidth: 500,
                                        allowHTML: true,
                                        placement: 'bottom'
                                    });
                                } else {
                                    exportTippyElement.forEach(function (element) {
                                        element.setContent(exportInfo);
                                    });
                                }
                            } else {
                                $("#exportRunningNotify").addClass("uk-hidden");
                                updateUnseenPlaylist();
                                timerProgressStatus.stop();
                            }
                        }
                    });
                }
            });
            timerProgressStatus.set({time: 2000, autostart: true});

        }

        $.ajax({
            type: "GET",
            url: "/sicstv/user/getSource.htm",
            cache: false,
            success: function (msg) {
                $("#sourceSelect").html(msg);
                $("#sourceSelectMobile").html(msg);
                return false;
            }
        });

    <c:if test="${!mUser.isPlayerUser()}">
        jsReloadFavorites();
    </c:if>
        jsReloadShortcuts();
        jsReloadUserLimits();

        flagLanguageTippy = tippy("#currentFlagLanguage", {
            theme: 'light-border',
            content: $("#currentFlagLanguageContent").html(),
            interactive: true,
            maxWidth: 600,
            allowHTML: true,
            placement: 'bottom',
            trigger: 'click mouseenter focus focusin manual'
        });

        updateUnseenPlaylist();

        // bind for modals to move them
        // Detect mousedown and dynamically bind draggable behavior
        document.addEventListener('mouseenter', (e) => {
            if ($(e.target).hasClass("uk-modal-dialog")) {
                if (!$(e.target).hasClass("draggable-bound")) {
                    $(e.target).addClass("draggable-bound");
                    bindDraggableModal(e.target);
                }
            }
        }, true);
    });

    function bindDraggableModal(modal) {
        let offsetX = 0, offsetY = 0, isDragging = false;

        // Ensure modal has initial `left` and `top` styles set if not already present
        if (!modal.style.left)
            modal.style.left = "0px";
        if (!modal.style.top)
            modal.style.top = "0px";

        // Add mousedown event to start dragging
        modal.addEventListener('mousedown', (e) => {
            if (typeof e.target !== "undefined" && $(e.target).hasClass("uk-modal-dialog")) {
                // e.preventDefault(); // Prevent text selection
                offsetX = e.clientX - parseInt(modal.style.left, 10);
                offsetY = e.clientY - parseInt(modal.style.top, 10);
                isDragging = true;

                modal.style.zIndex = '1000'; // Bring modal to the front

                // Mousemove event to drag the modal
                const onMouseMove = (e) => {
                    if (isDragging) {
                        modal.style.left = (e.clientX - offsetX) + "px";
                        modal.style.top = (e.clientY - offsetY) + "px";
                    }
                };

                // Mouseup event to stop dragging
                const onMouseUp = () => {
                    isDragging = false;
                    modal.style.zIndex = ''; // Reset z-index
                    document.removeEventListener('mousemove', onMouseMove);
                    document.removeEventListener('mouseup', onMouseUp);
                };

                // Attach the move and up listeners
                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
            }
        });
    }

    window.addEventListener('beforeunload', function (event) {
        // questo evento viene richiamato ogni volta prima che si esce dalla pagina
        // quindi sia per navigazione avanti e indietro, che per reload della pagina
        if (typeof videoJsPlayer !== 'undefined' && videoJsPlayer !== null) {
            videoJsPlayer.pause();
        }
        if (typeof playerHTML !== 'undefined' && playerHTML !== null) {
            playerHTML.pause;
        }
        jsShowBlockUI();
    });

    setTimeout(function () {
        checkAndLoadSeason();
    }, 50);

    function checkAndLoadSeason() {
        if (!new URL(document.URL).pathname.includes("/home.htm")
                && !new URL(document.URL).pathname.includes("/country.htm")
                && !new URL(document.URL).pathname.includes("/video.htm")
                && !new URL(document.URL).pathname.includes("/myPlaylistTv.htm")
                && !new URL(document.URL).pathname.includes("/reportPlayer.htm")
                && !new URL(document.URL).pathname.includes("/reportTeam.htm")
                && !new URL(document.URL).pathname.includes("/smartSearchPlayer.htm")
                && !new URL(document.URL).pathname.includes("/403.htm")
                && !new URL(document.URL).pathname.includes("/404.htm")
                && !new URL(document.URL).pathname.includes("/404Playlist.htm")) {
            var countryId, intCompId, formCompetitionId, teamId, playerId, groupId;
            // gestione sia per country che per internal competition
            var paramValue = new URL(document.URL).searchParams.get("countryId");
            if (paramValue !== null) {
                countryId = paramValue;
            }
            paramValue = new URL(document.URL).searchParams.get("intCompId");
            if (paramValue !== null) {
                intCompId = paramValue;
            }
            paramValue = new URL(document.URL).searchParams.get("formCompetitionId");
            if (paramValue !== null) {
                formCompetitionId = paramValue;
            }
            paramValue = new URL(document.URL).searchParams.get("formTeamId");
            if (paramValue !== null) {
                teamId = paramValue;
            } else {
                paramValue = new URL(document.URL).searchParams.get("teamId");
                if (paramValue !== null) {
                    teamId = paramValue;
                }
            }
            paramValue = new URL(document.URL).searchParams.get("playerId");
            if (paramValue !== null) {
                playerId = paramValue;
            }
            paramValue = new URL(document.URL).searchParams.get("groupId");
            if (paramValue !== null) {
                groupId = paramValue;
            }

            var data = "";
            if (typeof countryId !== 'undefined') {
                data += (data === "" ? "" : "&") + "countryId=" + countryId;
            }
            if (typeof intCompId !== 'undefined') {
                data += (data === "" ? "" : "&") + "intCompId=" + intCompId;
            }
            if (typeof formCompetitionId !== 'undefined') {
                data += (data === "" ? "" : "&") + "competitionId=" + formCompetitionId;
            }
            if (typeof teamId !== 'undefined') {
                data += (data === "" ? "" : "&") + "teamId=" + teamId;
            }
            if (typeof playerId !== 'undefined') {
                data += (data === "" ? "" : "&") + "playerId=" + playerId;
            }
            if (typeof groupId !== 'undefined') {
                data += (data === "" ? "" : "&") + "groupId=" + groupId;
            }
            $.ajax({
                type: "GET",
                url: "/sicstv/user/getSeason.htm",
                cache: false,
                data: encodeURI(data),
                success: function (msg) {
                    $("#seasonSelect").html(msg);
                    $("#seasonSelectMobile").html(msg);
                    return false;
                }
            });
        } else {
            $("#seasonDiv").remove();
        }
    }

    function changeSeason(mobile) {
        var seasonId;
        if (mobile) {
            seasonId = $("#seasonSelectMobile option:selected").val();
        } else {
            seasonId = $("#seasonSelect option:selected").val();
        }

        var countryId, intCompId, formCompetitionId, teamId;
        // gestione sia per country che per internal competition
        var paramValue = new URL(document.URL).searchParams.get("countryId");
        if (paramValue !== null) {
            countryId = paramValue;
        }
        paramValue = new URL(document.URL).searchParams.get("intCompId");
        if (paramValue !== null) {
            intCompId = paramValue;
        }
        paramValue = new URL(document.URL).searchParams.get("formCompetitionId");
        if (paramValue !== null) {
            formCompetitionId = paramValue;
        }
        paramValue = new URL(document.URL).searchParams.get("formTeamId");
        if (paramValue !== null) {
            teamId = paramValue;
        }

        var data = "newSeason=" + seasonId;
        if (typeof countryId !== 'undefined') {
            data += "&countryId=" + countryId;
        }
        if (typeof intCompId !== 'undefined') {
            data += "&intCompId=" + intCompId;
        }
        if (typeof formCompetitionId !== 'undefined') {
            data += "&competitionId=" + formCompetitionId;
        }
        if (typeof teamId !== 'undefined') {
            data += "&teamId=" + teamId;
        }
        $.ajax({
            type: "GET",
            url: "/sicstv/user/changeSeason.htm",
            cache: false,
            data: encodeURI(data),
            success: function (msg) {
                if (typeof timerProgressExport !== 'undefined') {
                    window.location.replace("/sicstv/user/mysicstv.htm");
                } else {
                    location.reload();
                }
                return false;
            }
        });
    }
    ;

    function changeSource(mobile) {
        var sourceId;
        if (mobile) {
            sourceId = $("#sourceSelectMobile option:selected").val();
        } else {
            sourceId = $("#sourceSelect option:selected").val();
        }
        if (typeof isPageVideo !== 'undefined' || typeof isMyHome !== 'undefined') {
            $.ajax({
                type: "GET",
                url: "/sicstv/user/changeSource.htm",
                cache: false,
                data: encodeURI("newSource=" + sourceId),
                success: function (msg) {
                    window.location.replace("/sicstv/user/home.htm");
                    return false;
                }
            });
        } else {
            $.ajax({
                type: "GET",
                url: "/sicstv/user/changeSource.htm",
                cache: false,
                data: encodeURI("newSource=" + sourceId),
                success: function (msg) {
                    location.reload();
                    return false;
                }
            });
        }
    }

    function openFavoritesModal() {
        closeShortcutsModal();

        $("#favoritesModal").removeClass("uk-hidden");
        $("#favoritesOpenButton").attr("onclick", "closeFavoritesModal();");
    }

    function closeFavoritesModal() {
        $("#favoritesModal").addClass("uk-hidden");
        $("#favoritesOpenButton").attr("onclick", "openFavoritesModal();");
    }

    function openShortcutsModal() {
        closeFavoritesModal();

        $("#shortcutsModal").removeClass("uk-hidden");
//        $("#shortcutsModalManage").removeClass("uk-hidden");
        $("#shortcutsOpenButton").attr("onclick", "closeShortcutsModal();");
    }

    function deletePlayerFromFavorites(id) {
        $.ajax({
            type: "GET",
            url: "/sicstv/user/rimuoviDaiPreferiti.htm",
            cache: false,
            data: encodeURI("playerId=" + id),
            success: function (msg) {
                if (msg === 'ok') {
                    UIkit.notify("<spring:message code='favorites.delete.success'/>", {status: 'success', timeout: 1000});

                    jsReloadFavorites();
                } else {
                    UIkit.notify("<spring:message code='favorites.delete.failed'/>", {status: 'warning', timeout: 1000});
                }
                return false;
            }
        });
    }

    function deleteTeamFromFavorites(id) {
        $.ajax({
            type: "GET",
            url: "/sicstv/user/rimuoviDaiPreferiti.htm",
            cache: false,
            data: encodeURI("teamId=" + id),
            success: function (msg) {
                if (msg === 'ok') {
                    UIkit.notify("<spring:message code='favorites.delete.success'/>", {status: 'success', timeout: 1000});

                    jsReloadFavorites();
                } else {
                    UIkit.notify("<spring:message code='favorites.delete.failed'/>", {status: 'warning', timeout: 1000});
                }
                return false;
            }
        });
    }

    function closeShortcutsModal() {
        $("#shortcutsModal").addClass("uk-hidden");
//        $("#shortcutsModalManage").addClass("uk-hidden");
        $("#shortcutsOpenButton").attr("onclick", "openShortcutsModal();");
    }

    function openShortcutsModalManage() {
        $("#shortcutsModalManage").removeClass("uk-hidden");
        $("#shortcutAddButton").attr("onclick", "closeShortcutsModalManage();");
    }

    function closeShortcutsModalManage() {
        $("#shortcutsModalManage").addClass("uk-hidden");
        $("#shortcutAddButton").attr("onclick", "openShortcutsModalManage();");
    }

    function openShortcutsNewGroup() {
        $("#shortcutsModalGroupSelect").addClass("uk-hidden");
        $("#shortcutsModalGroupNew").removeClass("uk-hidden");
    }

    function closeShortcutsNewGroup() {
        $("#shortcutsModalGroupNew").addClass("uk-hidden");
        $("#shortcutsModalGroupSelect").removeClass("uk-hidden");
    }

    function saveShortcuts() {
        var name = $("#shortcutsModalName").val().trim();
        if (!name) {
            UIkit.notify("<spring:message code='shortcuts.name.required'/>", {status: 'danger', timeout: 1000});
            return;
        }

        var group;
        if ($("#shortcutsModalGroupSelect").hasClass("uk-hidden")) {
            group = $("#shortcutsModalGroupInput").val().trim();
        } else {
            group = $("#shortcutsModalGroup").val().trim();
        }
        if (!group) {
            UIkit.notify("<spring:message code='shortcuts.group.required'/>", {status: 'danger', timeout: 1000});
            return;
        }

        $.ajax({
            type: "GET",
            url: "/sicstv/user/aggiungiScorciatoia.htm",
            cache: false,
            data: encodeURI("name=" + name + "&group=" + group + "&link=" + encodeURIComponent(window.location.href.replace(window.location.origin, ""))),
            success: function (msg) {
                if (msg === 'ok') {
                    UIkit.notify("<spring:message code='shortcuts.add.success'/>", {status: 'success', timeout: 1000});

                    jsReloadShortcuts();
                } else {
                    UIkit.notify("<spring:message code='shortcuts.add.failed'/>", {status: 'warning', timeout: 1000});
                }
                return false;
            }
        });
    }

    function removeShortcuts() {
        $.ajax({
            type: "GET",
            url: "/sicstv/user/rimuoviScorciatoia.htm",
            cache: false,
            data: encodeURI("link=" + encodeURIComponent(window.location.href.replace(window.location.origin, ""))),
            success: function (msg) {
                if (msg === 'ok') {
                    UIkit.notify("<spring:message code='shortcuts.delete.success'/>", {status: 'success', timeout: 1000});

                    jsReloadShortcuts();
                } else {
                    UIkit.notify("<spring:message code='shortcuts.delete.failed'/>", {status: 'warning', timeout: 1000});
                }
                return false;
            }
        });
    }

    function deleteShortcut(id) {
        $.ajax({
            type: "GET",
            url: "/sicstv/user/rimuoviScorciatoia.htm",
            cache: false,
            data: encodeURI("id=" + id),
            success: function (msg) {
                if (msg === 'ok') {
                    UIkit.notify("<spring:message code='shortcuts.delete.success'/>", {status: 'success', timeout: 1000});

                    jsReloadShortcuts();
                } else {
                    UIkit.notify("<spring:message code='shortcuts.delete.failed'/>", {status: 'warning', timeout: 1000});
                }
                return false;
            }
        });
    }

    function removeClickTab() {
        sessionStorage.removeItem("sicstv/600/do/click");
    }
</script>

<!-- The Modal -->
<div id="myModal" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
    <!-- Modal content -->
    <div class="uk-modal-dialog uk-text-center" style="min-height:0;min-width: 0;height:auto;">
        <div class="uk-width-1-1 uk-modal-content">
            <strong class="uk-text-large"><spring:message code='sessione.messaggio'/></strong>
        </div>
        <br>
        <div class="uk-width-1-1 uk-modal-content">
            <span class="uk-text-middle"><spring:message code='sessione.messaggio.secondario'/></span>
        </div>
        <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-width-1-1 uk-text-center uk-margin-top uk-modal-content" style="display: flex; justify-content: center">
            <li class="width-1-4" style="height: auto"><button id='closeModalLogin' class="uk-button uk-button-large uk-button-primary" onclick="clickLogin()"><spring:message code='sessione.entra'/></button></li>
            <li class="width-1-4" style="height: auto"><button id='closeModalLogout' class="uk-button uk-button-large" onclick="clickLogout()"><spring:message code='sessione.esci'/></button></li>
        </ul>
    </div>
</div>

<nav id="sicsHeader" class="uk-margin-small-top uk-margin-small-bottom uk-navbar-container uk-margin" uk-navbar>

    <div class="uk-container">

        <div class="uk-visible-large uk-navbar-nav" style="width: 100%">
            <a class="uk-navbar-brand" href="home.htm" onclick="removeClickTab();"><img style='width:150px;' src="/sicstv/<spring:message code='theme.logo'/>" title="SICS.tv" alt="SICS.tv"/></a>

            <ul class="uk-navbar-nav center-vertically">
                <li>
                    <ul class="uk-subnav uk-subnav-pill uk-subnav-line uk-visible-large uk-margin-remove">
                        <li><a href="/sicstv/user/home.htm" class="headerSubnav" onclick="removeClickTab();"><spring:message code="menu.homepage"/></a></li>
                        <li>
                            <a href="/sicstv/user/myhome.htm" class="headerSubnav"><spring:message code="menu.user.mysicstv"/></a>
                            <i id="notifyNewPlaylist" class="uk-badge uk-badge-notification-contrast"> </i>
                        </li>
                        <!--li><a href="/sicstv/user/videoMulti.htm" class="headerSubnav"><spring:message code="menu.user.multipartita"/></a></li-->
                        <li><div data-uk-dropdown>

                                <a href="#" class="headerSubnav"><i class="uk-icon-user user" style="width: 16px; margin-right: 6px;"></i>${mUser.firstName}</a>

                                <div class="uk-dropdown" style="padding-right: 0; padding-left: 0">
                                    <ul class="uk-nav uk-nav-navbar">
                                        <li><a href="/sicstv/user/shadowTeamsList.htm" class="headerSubnav"><img height="15px" width="15px" src="/sicstv/images/shadow_team.svg" class="uk-margin-right"/><spring:message code="shadow.teams.lower"/></a></li>
                                        <li class="uk-nav-divider"></li>
                                        <li><a href="/sicstv/user/privateDatabase.htm" class="headerSubnav"><img height="15px" width="15px" src="/sicstv/images/database.svg" class="uk-margin-right"/><spring:message code="private.db"/></a></li>
                                        <li class="uk-nav-divider"></li>
                                        <li><a href="/sicstv/user/data.htm" class="headerSubnav"><i class="uk-icon-th-list uk-margin-right"></i><spring:message code="menu.user.data"/></a></li>
                                        <li class="uk-nav-divider"></li>
                                        <li><a href="/sicstv/auth/logout.htm" class="headerSubnav"><i class="uk-icon-sign-out uk-margin-right"></i><spring:message code="menu.logout"/></a></li>
                                    </ul>
                                </div>

                            </div>
                        </li>

                        <c:if test="${mUser.isUserAdmin() || mUser.groupsetId == 412 || mUser.groupsetId == 1}">
                            <li>
                                <a href="/sicstv/admin/home.htm?timeFilter=2" class="headerSubnav">Manager</a>
                            </li>
                        </c:if>

                        <li class="uk-form" id="seasonDiv">
                            <label class="uk-form-label headerSubnavNoHover"><spring:message code="lib.stagione"/>:</label>
                            <select id="seasonSelect" onchange="changeSeason(false);" class="uk-visible-large uk-form-select selectItem" style="margin-top: -8px;"></select>
                        </li>
                        <c:if test="${mUser.getIsShare()}">
                            <li class="uk-form uk-hidden" id="sourceDiv" style="padding-left: 19px;">
                                <label class="uk-form-label headerSubnav"><spring:message code="lib.source"/>:</label>
                                <select id="sourceSelect" onchange="changeSource(false);" class="uk-visible-large uk-form-select " style="margin-top: -8px;"></select>
                            </li>
                        </c:if>
                        <li id="searchbarteamplayer" style="padding-left: 19px;">
                            <input type="hidden" id="teamSelAutocomplete" value="">
                            <input type="hidden" id="playerSelAutocomplete" value="">
                            <input type="hidden" id="seasonTitle" value="<spring:message code='search.seasons'/>">
                            <div id="autocompleteTeamPlayer" class="uk-autocomplete uk-form">
                                <div class="uk-flex">
                                    <label id="lblTeam" class="uk-margin-right headerSubnav"><spring:message code="video.ricerca"/>:</label>
                                    <input type="text" id="teamPlayerAuto" oninput="showSearchHistory();" list="search-history" class="uk-form-width-medium" placeholder="<spring:message code='menu.ricercateamplayer'/>" style="margin-top: -8px;" <c:if test="${mUser.isPlayerUser()}">disabled</c:if>/>
                                        <div id="searchSpinner" class="spinner-small uk-hidden" style="margin-left: 5px; margin-top: -3px"><div class="loader-small"></div></div>
                                    </div>
                                    <datalist id="search-history" class="search-history">
                                    </datalist>
                                    <script type="text/autocomplete">
                                        <ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left">

                                        {{#isTeam}}
                                        <li><i class="uk-icon-shield"></i>&nbsp;<b><spring:message code='search.teams'/></b><hr></li>
                                    {{~items}}
                                    {{#$item.team}}
                                    <li data-value="{{ $item.value }}" data-seasons="{{ $item.seasons }}" onclick="hideElement('autoCompleteSeasons'); setUserSeasonId('{{ $item.lastSeason }}'); openTeamPage('{{ $item.id }}','${personalSource}');" >
                                    <a>{{{ $item.name }}} ({{{ $item.countryName }}})</a>
                                    </li>
                                    {{/$item.team }}
                                    {{/items }}
                                    <li>&nbsp;</li>
                                    {{/isTeam }}

                                    {{#isPlayer}}
                                    <li><i class="uk-icon-user"></i>&nbsp;<b><spring:message code='search.players'/></b><hr></li>
                                    {{~items}}
                                    {{#$item.player}}
                                    <li data-value="{{ $item.value }}" data-seasons="{{ $item.seasons }}" onclick="hideElement('autoCompleteSeasons'); setUserSeasonId('{{ $item.lastSeason }}'); openPlayerPage('{{ $item.id }}','${personalSource}');" >
                                    <a>{{{ $item.namel }}} <span class="sCapitalize">{{{ $item.namef }}} </span> {{{ $item.namek }}} ({{{ $item.borny }}})</a>
                                    </li>
                                    {{ /$item.player }}
                                    {{/items}}
                                    <li>&nbsp;</li>
                                    {{/isPlayer }}

                                    {{#isCompetition}}
                                    <li><i class="uk-icon-shield"></i>&nbsp;<b><spring:message code='search.competitions'/></b><hr></li>
                                    {{~items}}
                                    {{#$item.competition}}
                                    <li data-value="{{ $item.value }}" data-seasons="{{ $item.seasons }}" onclick="hideElement('autoCompleteSeasons'); setUserSeasonId('{{ $item.lastSeason }}'); openCompetitionPage('{{ $item.id }}','${personalSource}');" >
                                    <a>{{{ $item.name }}}</a>
                                    </li>
                                    {{/$item.competition }}
                                    {{/items }}
                                    <li>&nbsp;</li>
                                    {{/isCompetition }}

                                    {{#isCountry}}
                                    <li><i class="uk-icon-shield"></i>&nbsp;<b><spring:message code='search.country'/></b><hr></li>
                                    {{~items}}
                                    {{#$item.country}}
                                    <li data-value="{{ $item.value }}" data-seasons="{{ $item.seasons }}" onclick="hideElement('autoCompleteSeasons'); setUserSeasonId('{{ $item.lastSeason }}'); openCountryPage('{{ $item.id }}','${personalSource}')" >
                                    <a>{{{ $item.name }}}</a>
                                    </li>
                                    {{/$item.country }}
                                    {{/items }}
                                    {{/isCountry }}

                                    </ul>
                                </script>
                            </div>
                            <div id="autoCompleteSeasons" class="uk-dropdown" style="display: none;">
                            </div>
                        </li>
                        <c:if test="${!mUser.isPlayerUser()}">
                            <li class="uk-form" id="smartSearchDiv">
                                <a href="/sicstv/user/smartSearchPlayer.htm" class="headerSubnav">
                                    <img style='width: 20px; padding-bottom: 4px' src="/sicstv/images/smart-search-player.png">
                                    <spring:message code="smart.search"/>
                                </a>
                            </li>
                            <li class="uk-form" id="shadowTeamDiv">
                                <a href="/sicstv/user/shadowTeamsList.htm" class="headerSubnav">
                                    <img style='width: 20px; padding-bottom: 4px' src="/sicstv/images/shadow_team.svg">
                                    <spring:message code="shadow.teams.lower"/>
                                </a>
                            </li>
                        </c:if>
                        <li id="searchbarpersonal" class="uk-form uk-hidden" style="padding-left: 19px;">
                            <label class="headerSubnavNoHover" for="myInput"><spring:message code="menu.area.search"/>:</label><input type="text" id="myInput" class="uk-form-width-medium" onkeyup="search(false)" placeholder="<spring:message code="search.name"/>" title="<spring:message code="search.name"/>" style="margin-top: -8px;">
                        </li>
                        <li id="exportRunningNotify" class="uk-form uk-hidden">
                            <label class="headerSubnavNoHover uk-text-success" for="myInput"><spring:message code="player.user.export.title"/></label>
                            <i class="uk-icon-spinner uk-icon-small uk-icon-spin uk-margin-right uk-float-right"></i>
                        </li>
                    </ul>
                </li>
            </ul>
            <c:if test="${mUser.expireIn7Days()}">
                <div class="alert">
                    <spring:message code="menu.scadenza"/>${mUser.getDatascadenzaString()}
                </div>
            </c:if>
            <div id="loadingDivNotifier" class="spinner uk-hidden" >
                <div class="loader"></div> 
            </div>
            <c:if test="${!mUser.isPlayerUser()}">
                <div style="float: right" id="favoritesContent"></div>
            </c:if>
            <div style="float: right" id="shortcutsContent"></div>
            <div style="float: right" id="userLimitsContent"></div>
            <div style="float: right" class="uk-margin-small-right" id="languageContent">
                <img height="40px" width="40px" src="/sicstv/images/country_flags/${mUser.tvLanguage}.svg" class="cursor-pointer" id="currentFlagLanguage">
                <div class="uk-hidden" id="currentFlagLanguageContent">
                    <div>
                        <div class="uk-flex language-container">
                            <c:if test="${!mUser.tvLanguage.equals('it')}">
                                <img height="50px" width="50px" src="/sicstv/images/country_flags/it.svg" class="cursor-pointer" data-uk-tooltip title="<spring:message code="language.it.desc"/>" onclick="changeLanguage('it', '<spring:message code="menu.user.language.update.error"/>')">
                            </c:if>
                            <c:if test="${!mUser.tvLanguage.equals('en')}">
                                <img height="50px" width="50px" src="/sicstv/images/country_flags/en.svg" class="cursor-pointer uk-margin-left" data-uk-tooltip title="<spring:message code="language.en.desc"/>" onclick="changeLanguage('en', '<spring:message code="menu.user.language.update.error"/>')">
                            </c:if>
                            <c:if test="${!mUser.tvLanguage.equals('fr')}">
                                <img height="50px" width="50px" src="/sicstv/images/country_flags/fr.svg" class="cursor-pointer uk-margin-left" data-uk-tooltip title="<spring:message code="language.fr.desc"/>" onclick="changeLanguage('fr', '<spring:message code="menu.user.language.update.error"/>')">
                            </c:if>
                            <c:if test="${!mUser.tvLanguage.equals('es')}">
                                <img height="50px" width="50px" src="/sicstv/images/country_flags/es.svg" class="cursor-pointer uk-margin-left" data-uk-tooltip title="<spring:message code="language.es.desc"/>" onclick="changeLanguage('es', '<spring:message code="menu.user.language.update.error"/>')">
                            </c:if>
                            <c:if test="${!mUser.tvLanguage.equals('ru')}">
                                <img height="50px" width="50px" src="/sicstv/images/country_flags/ru.svg" class="cursor-pointer uk-margin-left" data-uk-tooltip title="<spring:message code="language.ru.desc"/>" onclick="changeLanguage('ru', '<spring:message code="menu.user.language.update.error"/>')">
                            </c:if>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="uk-hidden-large">
            <a href="#sics-offcanvas" class="uk-navbar-toggle" data-uk-offcanvas></a>
            <div id="sics-offcanvas" class="uk-offcanvas">
                <div class="uk-offcanvas-bar">
                    <ul class="uk-nav uk-nav-offcanvas" data-uk-nav>
                        <li class="uk-nav-header">${mUser.firstName} ${mUser.lastName}</li>
                        <li class="uk-nav-divider"></li>
                        <li><a href="/sicstv/user/home.htm"><i class="uk-icon-home uk-margin-right"></i><spring:message code="menu.homepage"/></a></li>
                        <li><a href="/sicstv/user/myhome.htm"><i class="uk-icon-video-camera uk-margin-right"></i>
                                <spring:message code="menu.user.mysicstv"/></a>
                            <i id="notifyNewPlaylistSmall" class="uk-badge uk-badge-notification-contrast"> </i>
                        </li>
                        <li><a href="/sicstv/user/data.htm"><i class="uk-icon-th-list uk-margin-right"></i> <spring:message code="menu.user.data"/></a></li>
                        <li class="uk-form" id="seasonMobile">
                            <a href="#" class="uk-display-inline-block"><i class="uk-icon-th-list uk-margin-right uk-hidden-large" ></i><spring:message code="lib.stagione"/>:</a>
                            <select id="seasonSelectMobile" onchange="changeSeason(true);" class="uk-hidden-large" style="background: #333; color:#ccc;">

                            </select>
                        </li>
                        <c:if test="${mUser.getIsShare()}">
                            <li class="uk-form" id="sourceMobile">
                                <a href="#" class="uk-display-inline-block"><i class="uk-icon-th-list uk-margin-right uk-hidden-large" ></i><spring:message code="lib.source"/>:</a>
                                <select id="sourceSelectMobile" onchange="changeSource(true);" class="uk-hidden-large" style="background: #333; color:#ccc;">

                                </select>
                            </li>
                        </c:if>
                        <li class="uk-nav-divider"></li>
                        <li><a href="/sicstv/auth/logout.htm"><i class="uk-icon-sign-out uk-margin-right"></i> <spring:message code="menu.logout"/></a></li>
                    </ul>
                </div>
            </div>

            <div id="exportRunningNotify" class="uk-navbar-brand uk-hidden-large" style="margin-left: 20px;">
                <a class="uk-navbar-brand" href="home.htm"><img style='width:150px;' src="/sicstv/<spring:message code='theme.logo'/>" title="SICS.tv" alt="SICS.tv"></a>
            </div>
        </div>
    </div>
</nav>
<div id="subline" class="uk-width-1-1"></div>

