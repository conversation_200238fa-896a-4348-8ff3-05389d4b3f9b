<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<meta charset="utf-8">

<div id="teamStatsContainer${mType}" style="padding-top: 15px; margin-left: 5px; margin-right: 5px">
    
    <style>
        th, td { white-space: nowrap; }
        div.dataTables_wrapper {
            width: 100%;
            margin: 0 auto;
        }
        .${mType}noVis {
            min-width: ${mFirstColumnWidth}px;
        }
        .dt-button-collection {
            max-height: 500px !important; /* Imposta l'altezza massima del dropdown */
            overflow-y: auto !important; /* Abilita la barra di scorrimento verticale */
        }
        th {
            font-size: small;
        }
        #compareTable${mType} th:first-child {
            display: none;
        }
        #compareTable${mType} tr td:first-child {
            display: none;
        }
    </style>
    
    <script type="text/javascript">
        var currentFilter${mType};
        <c:if test="${mFilters.isEmpty() == false}">
            currentFilter${mType} = ${mFirstFilter.id};
        </c:if>
        var watchlistId${mType} = ${mWatchlistId};
        var statsTable${mType};
        var statsTable${mType}fullscreen = false;
        var compareTable${mType}, playerLinkMap${mType} = new Map(), playerPhotoMap${mType} = new Map(), teamLinkMap${mType} = new Map(), teamPhotoMap${mType} = new Map();
        var oppositeColumns${mType} = new Map();
        var isTabellino${mType};
        $(document).ready(function () {
            <c:set var="firstFilter" value="${mFilters.get(mFirstFilter)}"/>
            <c:set var="visibleColumns" value="0"/>
            <c:forEach var="item" items="${firstFilter}">
                <c:set var="visibleColumns" value="${visibleColumns}, "/>
                <c:set var="visibleColumns" value="${visibleColumns}${item.statsTypeId}"/>
            </c:forEach>
                
            <c:forEach var="stat" items="${mAllOppositeColumns}">
                oppositeColumns${mType}.set("${stat.desc}", ${stat.getJson()});
            </c:forEach>
            
            var cachePageLength;
            if (typeof localStorage["tablePageLen"] !== 'undefined') {
                cachePageLength = parseInt(localStorage["tablePageLen"]);
            }
            
            statsTable${mType} = $('#teamStatsTable${mType}').DataTable({
                language: {
                    "emptyTable": "<spring:message code='team.stats.no.data'/>"
                },
                paging: true,
                ajax: {
                    url: '/sicstv/user/teamStatsData.htm',
                    dataType: 'json',
                    dataSrc: function(datas) {
                        playerLinkMap${mType} = new Map();
                        if (datas.playerLink) {
                            datas.playerLink.forEach(function (element) {
                                playerLinkMap${mType}.set(element.playerId, element);
                            });
                        }
                        playerPhotoMap${mType} = new Map();
                        if (datas.playerPhoto) {
                            datas.playerPhoto.forEach(function (element) {
                                playerPhotoMap${mType}.set(element.playerId, element.photo);
                            });
                        }
                        teamLinkMap${mType} = new Map();
                        if (datas.teamLink) {
                            datas.teamLink.forEach(function (element) {
                                teamLinkMap${mType}.set(element.teamId, element);
                            });
                        }
                        teamPhotoMap${mType} = new Map();
                        if (datas.teamPhoto) {
                            datas.teamPhoto.forEach(function (element) {
                                teamPhotoMap${mType}.set(element.teamId, element.photo);
                            });
                        }
                        if (typeof datas.hasValues !== "undefined") {
                            if (!datas.hasValues) {
                                // se non ci sono dati, specialmente per le watchlist, gestisco compare
                                // e ritorno array vuoto cos� da mostrare il messaggio che dice che non ci sono
                                // dati in questa stagione 
                                if (typeof sessionStorage["statsType${mType}CompareMode"] !== 'undefined') {
                                    isCompareMode${mType} = true;
                                    manageCompare${mType}();
                                }
                                return [];
                            }
                        }
                        isTabellino${mType} = datas.isTabellino;
                        if (isTabellino${mType}) {
                            $("#teamStatsContainer${mType}").find(".remove-tabellino").addClass("uk-hidden");
                        } else {
                            $("#teamStatsContainer${mType}").find(".remove-tabellino").removeClass("uk-hidden");
                        }
                        return datas.data;
                    }
                },
                createdRow: function (row, data, dataIndex) {
                    // 'data' contiene l'oggetto JSON per la riga corrente
                    var columns = "${mTable.columnNameList}".split(", ");
                    var columnIds = "${mColumnIds}".split(", ");
                    
                    $(row).find("td:not(:first-child)").attr('class', "center-text middle-text");
                    $(row).find("td:not(:first-child)").each(function(index, element) {
                        $(element).attr("column-index", columnIds[index].replace("[", "").replace("]", ""));
                    });
                    //$('td', row).attr('class', "center-text middle-text orangeOnHover");
                },
                columnDefs: [
                    {
                        targets: 0,
                        render: function(data, type, row, meta) {
                            if (type === 'sort') {
                                if (typeof data !== "undefined" && data) {
                                    var dataSortValue = $(data).attr('data-order');
                                    if (dataSortValue !== undefined) {
                                        // Converti in numero per un sort numerico
                                        return parseFloat(dataSortValue);
                                    }
                                }
                                // Se non c'è data-sort o non si riesce a leggere l'elemento,
                                // torna al dato originale o a un valore di fallback.
                                // Potresti voler usare il 'data' originale che DataTables ti fornisce,
                                // o il testo dell'HTML se non hai specificato 'data' per la colonna.
                                return $(data).text() || data; // Fallback al testo visibile o dato originale
                            }
                            // Per tutti gli altri tipi di rendering (display, filter, type),
                            // restituisci il dato originale o l'HTML della cella.
                            return data;
                        }
                    }
                ],
                <c:if test="${mTable != null}">
                    dom: 'Blfrtip',
                    buttons: [
                        {
                            text: '<spring:message code='team.stats.colvis.title'/>',
                            attr: {
                                class: 'uk-button remove-tabellino',
                            },
                            action: function ( e, dt, node, config ) {
                                manageColumnsModal${mType}();
                            }
                        }, {
                            text: '<spring:message code='team.stats.manage.filter'/>',
                            attr: {
                                class: 'uk-button remove-tabellino',
                            },
                            action: function ( e, dt, node, config ) {
                                manageFilters${mType}();
                            }
                        }, {
                            text: '<i class="uk-icon-save"></i>',
                            attr: {
                                title: '<spring:message code='team.stats.save.filter'/>',
                                class: 'uk-button remove-tabellino',
                            },
                            action: function ( e, dt, node, config ) {
                                createSaveFilter${mType}();
                            }
                        }, {
                            text: '<spring:message code='team.stats.esporta'/>',
                            attr: {
                                class: 'uk-button',
                            },
                            extend: 'collection',
                            buttons: [
                                {
                                    extend: 'csv',
                                    exportOptions: {
                                        columns: ':visible',
                                        customizeData: function (data) {
                                            if ("${mType}" === "players") {
                                                data['header'].splice(1, 0, "<spring:message code="player.report.citizenship"/>");
                                                data['header'].splice(1, 0, "<spring:message code="player.piede"/>");
                                                data['header'].splice(1, 0, "<spring:message code="player.ruolodettagliato"/>");
                                                data['header'].splice(1, 0, "<spring:message code="player.ruolo"/>");
                                                data['header'].splice(1, 0, "<spring:message code="lib.squadra"/>");
                                                $.each(data['body'], function(key, row) {
                                                    let firstCol = row[0];
                                                    row.splice(0, 1, (firstCol.split("-_-")[0] || "").trim());
                                                    row.splice(1, 0, (firstCol.split("-_-")[1].split(" - ")[0] || "").trim());
                                                    row.splice(2, 0, (firstCol.split("-_-")[1].split(" - ")[1] || "").trim());
                                                    row.splice(3, 0, (firstCol.split("-_-")[1].split(" - ")[2] || "").trim());
                                                    if (firstCol.split("-_-")[2].includes(" - ")) {
                                                        row.splice(4, 0, (firstCol.split("-_-")[2].split(" - ")[0] || "").trim());
                                                        row.splice(5, 0, (firstCol.split("-_-")[2].split(" - ")[1] || "").trim());
                                                    } else {
                                                        row.splice(4, 0, "");
                                                        row.splice(5, 0, (firstCol.split("-_-")[2].split(" - ")[0] || "").trim());
                                                    }
                                                });
                                            }
                                        },
                                        format: {
                                            body: function (data, row, column, node) {
                                                if ("${mType}" === "players") {
                                                    if (column === 0) {
                                                        data = data.replaceAll("<br/>", "-_-");
                                                    }
                                                }
                                                return $(data).text();
                                            }
                                        }
                                    },
                                    title: 'SICS.tv-${mType}'
                                }, {
                                    extend: 'excel',
                                    exportOptions: {
                                        columns: ':visible',
                                        customizeData: function (data) {
                                            if ("${mType}" === "players") {
                                                data['header'].splice(1, 0, "<spring:message code="player.report.citizenship"/>");
                                                data['header'].splice(1, 0, "<spring:message code="player.piede"/>");
                                                data['header'].splice(1, 0, "<spring:message code="player.ruolodettagliato"/>");
                                                data['header'].splice(1, 0, "<spring:message code="player.ruolo"/>");
                                                data['header'].splice(1, 0, "<spring:message code="lib.squadra"/>");
                                                $.each(data['body'], function(key, row) {
                                                    let firstCol = row[0];
                                                    row.splice(0, 1, (firstCol.split("-_-")[0] || "").trim());
                                                    row.splice(1, 0, (firstCol.split("-_-")[1].split(" - ")[0] || "").trim());
                                                    row.splice(2, 0, (firstCol.split("-_-")[1].split(" - ")[1] || "").trim());
                                                    row.splice(3, 0, (firstCol.split("-_-")[1].split(" - ")[2] || "").trim());
                                                    if (firstCol.split("-_-")[2].includes(" - ")) {
                                                        row.splice(4, 0, (firstCol.split("-_-")[2].split(" - ")[0] || "").trim());
                                                        row.splice(5, 0, (firstCol.split("-_-")[2].split(" - ")[1] || "").trim());
                                                    } else {
                                                        row.splice(4, 0, "");
                                                        row.splice(5, 0, (firstCol.split("-_-")[2].split(" - ")[0] || "").trim());
                                                    }
                                                });
                                            }
                                        },
                                        format: {
                                            body: function (data, row, column, node) {
                                                if ("${mType}" === "players") {
                                                    if (column === 0) {
                                                        data = data.replaceAll("<br/>", "-_-");
                                                    }
                                                }
                                                return $(data).text();
                                            }
                                        }
                                    },
                                    title: 'SICS.tv-${mType}'
                                }
                            ]
                        }, {
                            text: '<span><spring:message code='menu.filters'/></span>',
                            attr: {
                                class: 'uk-button',
                                id: 'manageTableFilters${mType}Button'
                            },
                            action: function ( e, dt, node, config ) {
                                manageTableFilters${mType}();
                            }
                        }
                        <c:if test="${mType.equals('competitionPlayers') or mType.equals('players') or mType.equals('watchlistPlayer') or mType.equals('teams')}">
                        , {
                            text: '<span>P90</span>',
                            attr: {
                                title: '<spring:message code='team.stats.show.p90'/>',
                                class: 'uk-button',
                                id: 'manageValues${mType}P90Button'
                            },
                            action: function ( e, dt, node, config ) {
                                if ($("#manageValues${mType}P90Button").hasClass("uk-button-active")) {
                                    // riclicco quindi devo mostrare il totale
                                    $("#manageValues${mType}P90Button").removeClass("uk-button-active");
                                } else {
                                    $("#manageValues${mType}P90Button").addClass("uk-button-active");
                                }
                                $("#manageValues${mType}PercentageButton").removeClass("uk-button-active");
                                manageValues${mType}();
                            }
                        }
                        </c:if>
                        , {
                            text: '<span>%</span>',
                            attr: {
                                title: '<spring:message code='team.stats.show.percentages'/>',
                                class: 'uk-button',
                                id: 'manageValues${mType}PercentageButton'
                            },
                            action: function ( e, dt, node, config ) {
                                if ($("#manageValues${mType}PercentageButton").hasClass("uk-button-active")) {
                                    // riclicco quindi devo mostrare il totale
                                    $("#manageValues${mType}PercentageButton").removeClass("uk-button-active");
                                } else {
                                    $("#manageValues${mType}PercentageButton").addClass("uk-button-active");
                                }
                                $("#manageValues${mType}P90Button").removeClass("uk-button-active");
                                manageValues${mType}();
                            }
                        }, {
                            text: '<i class="uk-icon-expand" style="transform: rotate(45deg);"></i>',
                            attr: {
                                id: 'fullscreenButton${mType}',
                                title: '<spring:message code='team.stats.colvis.fullscreen'/>',
                                class: 'uk-button',
                            },
                            action: function ( e, dt, node, config ) {
                                manageFullscreen${mType}();
                            }
                        }
                    ],
                    scrollY: "800px",
                    scrollX: true,
                    scrollCollapse: true,
                    fixedColumns: {
                        leftColumns: 1
                    },
                    <c:if test="${mSortTable == true}">
                    order: [[0, "asc"]],
                    </c:if>
                    <c:if test="${mSortTable == false}">
                    order: [],
                    </c:if>
                    autoWidth: true,
                    colReorder: {
                        fixedColumnsLeft: 1
                    },
                    responsive: true,
                    <c:set var="pageLength" value="10"/>
                    <c:if test="${mType.equals('teams')}">
                        <c:set var="pageLength" value="20"/>
                    </c:if>
                    pageLength: (typeof cachePageLength !== "undefined" ? cachePageLength : ${pageLength}),
                    lengthMenu: [10, 15, 20, 25, 50],
                    initComplete: function(settings, json) {
                        var searchWrapper = $("#teamStatsTable${mType}_wrapper .dt-buttons");
                        
                        if (!isTabellino${mType}) {
                            <c:set var="sicsTitleAdded" value="false"/>
                            <c:set var="personalTitleAdded" value="false"/>
                            <c:if test="${mFilters.isEmpty() == false}">
                            var select = '<span class="uk-form" style="margin-right: 10px"><select id="teamStats${mType}Filter" onchange="changeFilter${mType}(this);" class="uk-form-select selectItem" style="width: auto">';
                            <c:forEach var="filter" items="${mFilters.keySet()}">
                                <c:if test="${sicsTitleAdded == false}">
                                    select += '<option disabled class="uk-text-bold">SICS</option>';
                                    <c:set var="sicsTitleAdded" value="true"/>
                                </c:if>
                                <c:if test="${personalTitleAdded == false && filter.id > 0}">
                                    select += '<option disabled class="uk-text-uppercase uk-text-bold"><spring:message code="menu.custom"/></option>';
                                    <c:set var="personalTitleAdded" value="true"/>
                                </c:if>
                                select += '<option value="${filter.id}">${filter.name}</option>';
                            </c:forEach>
                            select += '</select></span>';
                            var customButton = $(select);
                            searchWrapper.prepend(customButton);
                            </c:if>

                            <c:if test="${mFilters.isEmpty() == false}">
                                showFilter${mType}(${mFirstFilter.id});
                            </c:if>
                        }
                        searchWrapper.append('<div class="center uk-margin-left-small uk-float-left uk-margin-right"><span class="uk-margin-right-small"><spring:message code='menu.user.compare.mode'/></span><label class="uk-switch" for="teamStatsGrouped${mType}"><input type="checkbox" class="teamStatsGrouped${mType}" id="teamStatsGrouped${mType}" onchange="manageCompare${mType}();" ' + (isCompareMode${mType} ? "checked" : "") + '><div class="uk-switch-slider"></div></label></div>');
                        
                        // compare mode
                        if (typeof sessionStorage["statsType${mType}CompareMode"] !== 'undefined') {
                            isCompareMode${mType} = !(sessionStorage["statsType${mType}CompareMode"] === 'true');
                            manageCompare${mType}();
                        }
//                        if ("${mType}" === "players" || "${mType}" === "competitionPlayers") {
//                            var input = '<span class="uk-form" style="margin-right: 10px"><spring:message code='team.stats.min.minutes.played'/>: <input style="number" min="1" value="45" id="${mType}MinutesFilter" onchange="applyMinutesFilter${mType}()" class="uk-form-width-mini filter-select input-text-calendar-search"></span>'
//                            var customButton = $(input);
//                            searchWrapper.prepend(customButton);
//                        }
                    }
                </c:if>
            });
            
            statsTable${mType}.on('length.dt', function (e, settings, len) {
                localStorage["tablePageLen"] = len;
            });
            
//            $("teamStatsTable${mType}").removeClass("uk-hidden");
            
            // il codice sotto serve per disegnare la tabella quando viene visualizzato il div
            // sembra che se caricato tramite ajax il DataTable non viene renderizzato correttamente
            // questo succede nella pagina team.jsp quando entri nella pagina che era salvato che stavi guardando
            // le statistiche
            const callback${mType} = (entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        setTimeout(function() {
                            statsTable${mType}.draw();
                        }, 150);
                    }
                });
            };
            const observer${mType} = new IntersectionObserver(callback${mType});
            const myDiv${mType} = document.getElementById('teamStatsContainer${mType}');
            observer${mType}.observe(myDiv${mType});
            
            setTimeout(function() {
                $("#teamStatsContainer${mType}").css("height", $("#teamStatsTable${mType}_wrapper").outerHeight());
            }, 500);
            
            // caricamento filtri sessione
            if ("${mType}" === "players" || "${mType}" === "competitionPlayers" || "${mType}" === "watchlistPlayer") {
                if (typeof sessionStorage["statsType${mType}Minutes"] !== "undefined") {
                    $("#${mType}MinutesFilter").val(parseInt(sessionStorage["statsType${mType}Minutes"]));
                    $("#${mType}AgeFilter").val(parseInt(sessionStorage["statsType${mType}Age"]));
                    $("#tableFilterModal${mType}AgeType").val(sessionStorage["statsType${mType}AgeType"]);
                    $("#tableFilterModal${mType}Role").val(sessionStorage["statsType${mType}Role"]);
                    $("#tableFilterModal${mType}Team").val(sessionStorage["statsType${mType}Team"]);
                    $("#tableFilterModal${mType}Foot").val(sessionStorage["statsType${mType}Foot"]);
                    $("#tableFilterModal${mType}Country").val(sessionStorage["statsType${mType}Country"]);

                    applyTableFilter${mType}();
                }
            
                // bisogna farlo sempre altrimenti la prima volta che apri una tabella non filtra per i minuti giocati
                applyMinutesAndAgeFilter${mType}();
            }
            
            updateSelectedButtons${mType}();
        });
        
        function manageFullscreen${mType}() {
            statsTable${mType}fullscreen = $("#fullscreenButton${mType}").hasClass("uk-button-active");
            if (statsTable${mType}fullscreen) {
                statsTable${mType}fullscreen = false;
                $("#tabMenu").removeClass("uk-hidden");
                $("#divMatches").removeClass("uk-hidden");
                $("#fullscreenButton${mType}").removeClass("uk-button-active");
                $("#competitions").addClass("uk-width-6-10");
                $("#competitions").removeClass("uk-width-10-10");
                $("#divTeamInfo").addClass("uk-width-6-10");
                $("#divTeamInfo").removeClass("uk-width-10-10");
            } else {
                statsTable${mType}fullscreen = true;
                $("#tabMenu").addClass("uk-hidden");
                $("#divMatches").addClass("uk-hidden");
                $("#fullscreenButton${mType}").addClass("uk-button-active");
                $("#competitions").removeClass("uk-width-6-10");
                $("#competitions").addClass("uk-width-10-10");
                $("#divTeamInfo").removeClass("uk-width-6-10");
                $("#divTeamInfo").addClass("uk-width-10-10");
            }
        }
        
        function changeFilter${mType}(element) {
            currentFilter${mType} = $("#" + element.id + " option:selected").val();
            reloadTeamStatsTables${mType}();
            
//            jsShowBlockUI();
//            setTimeout(function() {
//                showFilter${mType}($("#" + element.id + " option:selected").val());
//                $.unblockUI();
//            }, 250);
        }
        
//        function updateFilterPreferred${mType}() {
//            $.ajax({
//                type: "GET",
//                url: "/sicstv/user/aggiornaFiltroTeamStatsPrincipale.htm",
//                cache: false,
//                data: encodeURI("filterId=" + currentFilter${mType} + "&tableType=${mType}"),
//                success: function (msg) {
//                    UIkit.notify("<spring:message code="team.stats.update.success"/>", {status: 'success', timeout: 1000});
//                    $("#setPreferredFilter${mType}").html('<img style="margin-left: -5px" width="25px" src="/sicstv/images/won.png"></img> Predefinito');
//                    $("#setPreferredFilter${mType}").attr("disabled", "true");
//                    $("#setPreferredFilter${mType}").attr("title", "");
//                },
//                error: function () {
//                    UIkit.notify("<spring:message code="team.stats.update.error"/>", {status: 'danger', timeout: 1000});
//                }
//            });
//        }
        
        function showAllColumns${mType}() {
            event.preventDefault();
            jsShowBlockUI();
            setTimeout(function() {
                statsTable${mType}.columns().visible(true);
                $.unblockUI();
                $("#colVisButton${mType}").click();
            }, 500);
        }
        
        function hideAllColumns${mType}() {
            event.preventDefault();
            jsShowBlockUI();
            setTimeout(function() {
                statsTable${mType}.columns().visible(false);
                statsTable${mType}.column(0).visible(true);
                $.unblockUI();
                $("#colVisButton${mType}").click();
            }, 500);
        }
        
        function manageFilters${mType}() {
            $("#teamStatsFiltersModal${mType}").addClass("uk-open");
            $("#teamStatsFiltersModal${mType}").removeClass("uk-hidden");
        }
        
        function closeManageFilters${mType}() {
            $("#teamStatsFiltersModal${mType}").removeClass("uk-open");
            $("#teamStatsFiltersModal${mType}").addClass("uk-hidden");
            
            reloadTeamStatsTables${mType}();
        }
        
        function manageColumnsModal${mType}() {
            $("#teamStatsColumnsModal${mType}").addClass("uk-open");
            $("#teamStatsColumnsModal${mType}").removeClass("uk-hidden");

            // inizializzo a mano lo switcher altrimenti non va per qualche motivo
            if ($("#teamStatsColumnsButton${mType}ul > li.uk-active").length === 0) {
                var customOptions${mType} = {
                    connect: '#teamStatsColumnsButton${mType}Content'
                };
                UIkit.switcher('#teamStatsColumnsButton${mType}ul', customOptions${mType});
                
                var customOptionsAccordion${mType} = {
                    collapse: false,
                    showfirst: true
                };
                $(".accordionDiv${mType}").each(function (index, element) {
                        UIkit.accordion("#" + $(element).attr("id"), customOptionsAccordion${mType}).on('toggle.uk.accordion', function (event, active, toggle, content) {
                            if ("${mType}" !== "matches" && "${mType}" !== "players") {
                                updateSelectedButtons${mType}();
                            }
                        });
                    });
                if ("${mType}" !== "matches" && "${mType}" !== "players") {
                    $(".accordionDiv${mType}").each(function (index, element) {
                        UIkit.accordion("#" + $(element).attr("id"), customOptionsAccordion${mType}).on('toggle.uk.accordion', function (event, active, toggle, content) {
                            if ("${mType}" !== "matches" && "${mType}" !== "players") {
                                updateSelectedButtons${mType}();
                            }
                        });
                    });
                }
                if (new URL(document.URL).pathname.includes("/team.htm")) {
                    customOptionsAccordion${mType} = {
                        collapse: false,
                        showfirst: false
                    };
                    $(".accordionDivChild${mType}").each(function (index, element) {
                        UIkit.accordion("#" + $(element).attr("id"), customOptionsAccordion${mType}).on('toggle.uk.accordion', function (event, active, toggle, content) {
                            updateSelectedButtons${mType}();
                        });
                    });
                }
            }
        }
        
        function updateSelectedButtons${mType}() {
            // ogni volta riparto da 0 per evitare di dover gestire tutto
            $("#teamStatsColumnsModal${mType}").find("button.uk-button-active").removeClass("uk-button-active");
            $(".uk-accordion-content").find("button").find("i:not(.skip-reload)").removeClass("uk-icon-caret-down").addClass("uk-icon-caret-right");

            // ora attivo quelli che hanno almeno un input con il checked
            $(".uk-modal-dialog input").each(function (index, element) {
                if ($(element).prop("checked")) {
                    $(element).closest("div.width-1-4-x").find("button").addClass("uk-button-active");
                }
            });
            
            // gestione icona "aperto" e "chiuso"
            $(".uk-accordion-content").find("button.uk-active").find("i.uk-icon-caret-right:not(.skip-reload)").removeClass("uk-icon-caret-right").addClass("uk-icon-caret-down");
        }
        
        function closeManageColumnsModal${mType}() {
            $("#teamStatsColumnsModal${mType}").removeClass("uk-open");
            $("#teamStatsColumnsModal${mType}").addClass("uk-hidden");
        }
        
        function createSaveFilter${mType}(createNew) {
            var canContinue = false;
            if (typeof createNew === 'undefined') {
                if (typeof currentFilter${mType} !== 'undefined' && currentFilter${mType} > 0) {
                    if (window.confirm("<spring:message code="team.stats.update.confirm"/>")) {
                        canContinue = true;
                    }
                } else {
                    canContinue = true;
                }
            } else {
                canContinue = true;
            }
            
            if (canContinue) {
                var visibleColumns = [];
                // 1. Metto tutte le colonne che ci sono attualmente nella tabella nell'ordine giusto
                statsTable${mType}.columns().visible().each(function (value, index) {
                    var columnIndex = statsTable${mType}.column(index).nodes().to$().attr("column-index");
                    if (typeof columnIndex !== 'undefined') { // escludo la prima colonna
                        if (value) {
                            visibleColumns.push(columnIndex);
                        }
                    }
                });
                // 2. Aggiungo le colonne che sono state selezionate in pi�
                $("#teamStatsColumnsButton${mType}Content").find("input:checked").each(function() {
                    var columnIndex = $(this).attr("data-value");
                    if (typeof columnIndex !== 'undefined') {
                        if (!visibleColumns.includes(columnIndex)) {
                            visibleColumns.push(columnIndex);
                        }
                    }
                });
                // 3. Tolgo le colonne che ho deselezionato
                $("#teamStatsColumnsButton${mType}Content").find("input:not(:checked)").each(function() {
                    var columnIndex = $(this).attr("data-value");
                    if (typeof columnIndex !== 'undefined') {
                        if (visibleColumns.includes(columnIndex)) {
                            visibleColumns.splice(visibleColumns.indexOf(columnIndex), 1);
                        }
                    }
                });

                var sorting = [];
                statsTable${mType}.order().forEach(function (column) {
                    var index = statsTable${mType}.column(column[0]).nodes().to$().attr("column-index");
                    if (typeof index !== 'undefined') {
                        sorting.push(index + "|" + column[1]);
                    }
                });

                var ajaxData;
                if ($("#teamStatsFilterName${mType}").val().length > 0) {
                    ajaxData = encodeURI("&filterId=&tableType=${mType}&name=" + $("#teamStatsFilterName${mType}").val() + "&columns=" + visibleColumns + "&sort=" + sorting);
                    $("#teamStatsFilterName${mType}").val("");
                } else if (typeof createNew === 'undefined' && typeof currentFilter${mType} !== 'undefined' && currentFilter${mType} > 0) { // aggiorno filtro attuale
                    var counter = 1;
                    <c:if test="${mFilters.isEmpty() == false}">
                        counter = ${mFilters.size()} + 1;
                    </c:if>
                    ajaxData = encodeURI("&filterId=" + currentFilter${mType} + "&tableType=${mType}&name=${mType}" + counter + "&columns=" + visibleColumns + "&sort=" + sorting);
                } else {
                    var counter = 1;
                    <c:if test="${mFilters.isEmpty() == false}">
                        counter = ${mFilters.size()} + 1;
                    </c:if>
                    ajaxData = encodeURI("&filterId=&tableType=${mType}&name=${mType}" + counter + "&columns=" + visibleColumns + "&sort=" + sorting);
                }

                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/creaFiltroTeamStats.htm",
                    cache: false,
                    data: ajaxData,
                    success: function (msg) {
                        if (typeof createNew === 'undefined' && typeof currentFilter${mType} !== 'undefined' && currentFilter${mType} > 0) {
                            UIkit.notify("<spring:message code="team.stats.update.filter.success"/>", {status: 'success', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="team.stats.create.filter.success"/>", {status: 'success', timeout: 1000});
                            currentFilter${mType} = parseInt(msg);
                        }

                        reloadTeamStatsTables${mType}();
                    },
                    error: function () {
                        if (typeof currentFilter${mType} !== 'undefined' && currentFilter${mType} > 0) {
                            UIkit.notify("<spring:message code="team.stats.update.filter.error"/>", {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="team.stats.create.filter.error"/>", {status: 'danger', timeout: 1000});
                        }
                    }
                });
            }
        }
        
        function reloadTeamStatsTables${mType}() {
            if ($("#teamStatsLi").length > 0) {
                if ('${mType}' === 'teams') {
                    jsLoadTeamStats("teamStatsLi", false, false, currentFilter${mType}, undefined, true);
                } else {
                    jsLoadTeamStatsCompetitionPlayers("playerStatsLi", currentFilter${mType}, true);
                }
            } else {
                if ('${mType}' === 'teams') {
                    jsLoadTeamStats("filterSicsTeamStats", false, false, currentFilter${mType}, undefined, true);
                } else if ('${mType}' === 'watchlistPlayer') {
                    var isGrouped = new URL(document.URL).searchParams.get("isGrouped");
                    jsLoadTeamStatsFromWatchlist("watchlistContent", currentFilter${mType}, watchlistId${mType}, true, isGrouped);
                } else {
                    jsLoadTeamStatsPlayers("filterSicsTeamStatsPlayers", currentFilter${mType}, true);
                }
            }
        }
        
        function showFilter${mType}(id) {
            // trick: mettendo a non visibile prima di applicare un filtro velocizza circa del doppio le prestazioni
            $("#teamStatsContainer${mType}").addClass("uk-hidden");
            
            $("#teamStats${mType}Filter").val(id);
            currentFilter${mType} = id;

            var orderColumns = [];
            var sortedColumns = [];
            var validColumns = [0];
            var filterId = "";
            var index = 0;
            <c:forEach var="filterItem" items="${mFilters.keySet()}" varStatus="status">
                if (${filterItem.id} == id) {
                    <c:forEach var="item" items="${mFilters.get(filterItem)}">
                        validColumns.push(${item.statsTypeId}); // inserisco le colonne visibili
                        orderColumns.push([${item.statsTypeId}, ${item.order}]);
                        <c:if test="${item.sort != null}">
                            if (${item.sort}) { // controllo per sorting
                                sortedColumns.push([${item.statsTypeId}, "asc"]);
                            } else {
                                sortedColumns.push([${item.statsTypeId}, "desc"]);
                            }
                        </c:if>
                        index++;
                    </c:forEach>
                }
            </c:forEach>
//            var visibleColumns = [];
//            var notVisibleColumns = [];
//            for (var i = 0; i < statsTable${mType}.columns().count(); i++) {
//                var columnIndex = statsTable${mType}.column(i).nodes().to$().attr("column-index");
//                if (typeof columnIndex === 'undefined') {columnIndex = -1}; // per prima colonna
//
//                if (validColumns.includes(parseInt(columnIndex)) || i === 0) { // se � una colonna visibile
//                    visibleColumns.push(i);
//                } else {
//                    notVisibleColumns.push(i);
//                }
//            }
//            statsTable${mType}.columns(visibleColumns).visible(true);
//            statsTable${mType}.columns(notVisibleColumns).visible(false);

            if (sortedColumns && sortedColumns.length > 0) {
                index = 0;
                var fixedSortedColumns = [];
                // passo ogni colonna per cercare il campo column-index
                // dentro a sortedColumns c'� il valore di column-index, invece deve esserci
                // il numero della colonna, quindi con un contatore inserisco poi il giusto index
                statsTable${mType}.columns().every(function (column) {
                    var columnIndex = statsTable${mType}.column(column).nodes().to$().attr("column-index");
                    sortedColumns.forEach(function (array) { // la funzione .each() non funziona
                        if (columnIndex == array[0]) {
                            fixedSortedColumns.push([index, array[1]]);
                        }
                    });
                    index++;
                });

                statsTable${mType}.order(fixedSortedColumns);
                statsTable${mType}.draw(); // necessario altrimenti non imposta i filtri
            } else {
                statsTable${mType}.order([0, "asc"]);
                statsTable${mType}.draw(); // necessario altrimenti non imposta i filtri
            }

            var tableVisibleColumns = statsTable${mType}.columns(':visible');
            orderColumns.forEach(function (array) { // la funzione .each() non funziona
                tableVisibleColumns.every(function () {
                    var column = this;
                    var columnIndex = column[0][0];
                    var columnAttribute = statsTable${mType}.column(columnIndex).nodes().to$().attr('column-index');

                    if (typeof columnAttribute !== 'undefined') { // escludo prima colonna
                        if (columnAttribute == array[0]) {
                            statsTable${mType}.colReorder.move(columnIndex, array[1]);

                            // devo ricalcolare le colonne visibili dopo una modifica
                            tableVisibleColumns = statsTable${mType}.columns(':visible');
                        }
                    }
                });
            });

            statsTable${mType}.draw();
            
            // trick: mettendo a non visibile prima di applicare un filtro velocizza circa del doppio le prestazioni
            $("#teamStatsContainer${mType}").removeClass("uk-hidden");
        }
        
        function manageRenameFilter${mType}(id, confirm) {
            if ($("#teamStatsConfirmRename${mType}" + id).length > 0) { // salvo modifica
                $("#teamStatsConfirmRename${mType}" + id).remove();
                
                if (confirm) {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/rinominaFiltroTeamStats.htm",
                        cache: false,
                        data: encodeURI("&filterId=" + id + "&name=" + $("#teamStatsFilterNameInput${mType}" + id).val()),
                        success: function (msg) {
                            if (msg === 'true') {
                                UIkit.notify("<spring:message code="team.stats.update.success"/>", {status: 'success', timeout: 1000});
                                currentFilter${mType} = id;
                                $("#teamStatsFilterName${mType}" + id).html($("#teamStatsFilterNameInput${mType}" + id).val());
                                // reloadTeamStatsTables${mType}();
                            } else if (msg === 'noPermission') {
                                UIkit.notify("<spring:message code="team.stats.update.noperm"/>", {status: 'danger', timeout: 1000});
                            } else {
                                UIkit.notify("<spring:message code="team.stats.update.error"/>", {status: 'danger', timeout: 1000});
                            }
                        },
                        error: function () {
                            UIkit.notify("<spring:message code="team.stats.update.error"/>", {status: 'danger', timeout: 1000});
                        }
                    });
                }
                // $("#teamStatsFilterName${mType}" + id).html($("#teamStatsFilterNameInput${mType}" + id).attr("defaultValue"));
                $("#teamStatsRenameButton${mType}" + id).attr("title", "<spring:message code='playlist.tooltip.rename'/>");
                $("#teamStatsRenameIcon${mType}" + id).attr("class", "uk-icon-pencil");
            } else {
                var newButton = $('<button id="teamStatsConfirmRename${mType}' + id + '" class="uk-icon-hover uk-button uk-button-mini" onclick="manageRenameFilter${mType}(' + id + ', true)" title="Rinomina"><i class="uk-icon-check"></i></button>');
                $("#teamStatsFilterButtons${mType}" + id).prepend(newButton);
                
                var newInput = $('<input id="teamStatsFilterNameInput${mType}' + id + '" class="uk-input" type="text" aria-label="Input" maxlength="32" value="' + ($("#teamStatsFilterName${mType}" + id).html().trim()) + '" defaultValue="' + ($("#teamStatsFilterName${mType}" + id).html().trim()) + '">');
                $("#teamStatsFilterName${mType}" + id).html(newInput);
                
                $("#teamStatsRenameButton${mType}" + id).attr("title", "<spring:message code='playlist.tooltip.rename.undo'/>");
                $("#teamStatsRenameIcon${mType}" + id).attr("class", "uk-icon-close");
            }
        }
        
        function deleteFilter${mType}(id) {
            if (window.confirm("<spring:message code="team.stats.delete.question"/>")) {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/cancellaFiltroTeamStats.htm",
                    cache: false,
                    data: encodeURI("&filterId=" + id),
                    success: function (msg) {
                        if (msg === 'true') {
                            UIkit.notify("<spring:message code="team.stats.delete.success"/>", {status: 'success', timeout: 1000});
                            if ($("#teamStats${mType}Filter").length === 1) {
                                if ($("#teamStats${mType}Filter option:not([disabled])").length > 0) {
                                    currentFilter${mType} = parseInt($($("#teamStats${mType}Filter option:not([disabled])")[0]).attr("value"));
                                }
                            }
                            sessionStorage.removeItem("statsTypeLastFilterId");
                            $("#teamStatsFilterName${mType}" + id).parent().remove();
                            // reloadTeamStatsTables${mType}();
                        } else if (msg === 'noPermission') {
                            UIkit.notify("<spring:message code="team.stats.delete.noperm"/>", {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="team.stats.delete.error"/>", {status: 'danger', timeout: 1000});
                        }
                    },
                    error: function () {
                        UIkit.notify("<spring:message code="team.stats.delete.error"/>", {status: 'danger', timeout: 1000});
                    }
                });
            }
        }
        
        function manageValues${mType}() {
            var needToShowP90 = $("#manageValues${mType}P90Button").hasClass("uk-button-active");
            var needToShowPercentage = $("#manageValues${mType}PercentageButton").hasClass("uk-button-active");
            var needToShowTotals = !needToShowP90 && !needToShowPercentage;
            
            if (needToShowTotals) {
                statsTable${mType}.rows().every(function (rowIdx, tableLoop, rowLoop) {
                    $(this.node()).find(".statRow${mType}").each(function() {
                        $(this).html($(this).attr('totalValue'));
                        var currentTitle = $(this).attr('defaultTitle');
                        if (typeof currentTitle !== "undefined" && currentTitle) {
                            $(this).attr('title', currentTitle);
                        } else {
                            $(this).removeAttr('title');
                        }
                    });
                });
            } else if (needToShowP90) {
                statsTable${mType}.rows().every(function (rowIdx, tableLoop, rowLoop) {
                    $(this.node()).find(".statRow${mType}").each(function() {
                        $(this).html($(this).attr('p90Value'));
                        var currentTitle = $(this).attr('defaultTitle');
                        if (typeof currentTitle !== "undefined" && currentTitle) {
                            $(this).attr('title', currentTitle);
                        } else {
                            $(this).removeAttr('title');
                        }
                    });
                });
            } else if (needToShowPercentage) {
                statsTable${mType}.rows().every(function (rowIdx, tableLoop, rowLoop) {
                    $(this.node()).find(".statRow${mType}").each(function() {
                        var currentTitle = $(this).attr('defaultTitle');
                        if (typeof currentTitle !== "undefined" && currentTitle) {
                            currentTitle += " - ";
                            currentTitle += $(this).attr('titlePercentage');
                        } else {
                            currentTitle = $(this).attr('titlePercentage');
                        }
                        $(this).attr('title', currentTitle);

                        $(this).html($(this).attr('percentageValue'));
                    });
                });
            }
            
            var currentPage = statsTable${mType}.page();
            statsTable${mType}.rows().invalidate('dom').draw(); // serve per far si che il sort funzioni con i valori aggiornati
            statsTable${mType}.page(currentPage).draw(false);   // rimetto la pagina che stavo visualizzando
        }
        
        function applyMinutesAndAgeFilter${mType}() {
            $.fn.dataTable.ext.search.pop();
            $.fn.dataTable.ext.search.push(
                function(settings, data, dataIndex) {
                    var row = statsTable${mType}.row(dataIndex);

                    var data = row.data();
                    var element = $(data[0])[0];
                    if (typeof element !== 'undefined') {
                        var minutePlayedValue = $(element).find("input").val();
                        if (typeof minutePlayedValue !== 'undefined') {
                            var ageType = $("#tableFilterModal${mType}AgeType").val();
                            var ageValue = parseInt($(element).find("age").attr("value"));

                            if (typeof ageValue !== 'undefined') {
                                var validAge = false;
                                if (ageType === "1") { // <=
                                    if (ageValue <= parseInt($("#${mType}AgeFilter").val())) {
                                        validAge = true;
                                    }
                                } else if (ageType === "0") { // =
                                    if (ageValue === parseInt($("#${mType}AgeFilter").val())) {
                                        validAge = true;
                                    }
                                } else if (ageType === "2") { // >=
                                    if (ageValue >= parseInt($("#${mType}AgeFilter").val())) {
                                        validAge = true;
                                    }
                                }

                                if (minutePlayedValue < parseInt($("#${mType}MinutesFilter").val()) || !validAge) {
                                    return false;
                                } else {
                                    return true;
                                }
                            } else {
                                return true;
                            }
                        } else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                }
            );
            statsTable${mType}.draw();
            
            // se ho un filtro allora metto il tasto attivato cos� da far capire che ci sono dei filtri applicati
            manageFilterButton${mType}();
            sessionStorage["statsType${mType}Minutes"] = $("#${mType}MinutesFilter").val();
            sessionStorage["statsType${mType}Age"] = $("#${mType}AgeFilter").val();
            sessionStorage["statsType${mType}AgeType"] = $("#tableFilterModal${mType}AgeType").val();
        }
        
        function manageTableFilters${mType}() {
            tmpFormDateFrom${mType} = $("#formDateFrom${mType}").val();
            tmpFormDateTo${mType} = $("#formDateTo${mType}").val();
            
            $("#teamStatsTableFilterModal${mType}").addClass("uk-open");
            $("#teamStatsTableFilterModal${mType}").removeClass("uk-hidden");
        }
        
        function closeManageTableFilters${mType}() {
            $("#teamStatsTableFilterModal${mType}").removeClass("uk-open");
            $("#teamStatsTableFilterModal${mType}").addClass("uk-hidden");
        }
        
        function applyTableFilter${mType}() {
            var searchText = $("#tableFilterModal${mType}Role").val();
            searchText += " " + $("#tableFilterModal${mType}Team").val();
            searchText += " " + $("#tableFilterModal${mType}Foot").val();
            searchText += " " + $("#tableFilterModal${mType}Country").val();
            searchText = searchText.trim();
            
            sessionStorage["statsType${mType}Minutes"] = $("#${mType}MinutesFilter").val();
            sessionStorage["statsType${mType}Age"] = $("#${mType}AgeFilter").val();
            sessionStorage["statsType${mType}AgeType"] = $("#tableFilterModal${mType}AgeType").val();
            sessionStorage["statsType${mType}Role"] = $("#tableFilterModal${mType}Role").val();
            sessionStorage["statsType${mType}Team"] = $("#tableFilterModal${mType}Team").val();
            sessionStorage["statsType${mType}Foot"] = $("#tableFilterModal${mType}Foot").val();
            sessionStorage["statsType${mType}Country"] = $("#tableFilterModal${mType}Country").val();
            
            searchText = searchText.replaceAll("undefined", "").trim();
            
            statsTable${mType}.search(searchText).draw();
            
            // se ho un filtro allora metto il tasto attivato cos� da far capire che ci sono dei filtri applicati
            manageFilterButton${mType}();
            // fixo il fatto che cambiando i filtri non vengono applicati i valori giusti
            manageValues${mType}();
        }
        
        function resetTableTableFilters${mType}() {
            $("#${mType}MinutesFilter").val(45);
            $("#${mType}AgeFilter").val(40);
            $("#tableFilterModal${mType}AgeType").val(1);
            $("#tableFilterModal${mType}Role").val("");
            $("#tableFilterModal${mType}Team").val("");
            $("#tableFilterModal${mType}Foot").val("");
            $("#tableFilterModal${mType}Country").val("");
            
            applyMinutesAndAgeFilter${mType}();
            applyTableFilter${mType}();
        }
        
        function manageFilterButton${mType}() {
            if (statsTable${mType}.search()
                    || parseInt($("#${mType}MinutesFilter").val()) !== 45
                    || parseInt($("#${mType}AgeFilter").val()) !== 40
                    || parseInt($("#tableFilterModal${mType}AgeType").val()) !== 1) {
                $("#manageTableFilters${mType}Button").addClass("uk-button-active");
            } else {
                $("#manageTableFilters${mType}Button").removeClass("uk-button-active");
            }
        }
        
        function manageTableDateFilters${mType}() {
            $("#teamStatsTableDateFilterModal${mType}").addClass("uk-open");
            $("#teamStatsTableDateFilterModal${mType}").removeClass("uk-hidden");
        }
        
        function closeManageTableDateFilters${mType}() {
            $("#teamStatsTableDateFilterModal${mType}").removeClass("uk-open");
            $("#teamStatsTableDateFilterModal${mType}").addClass("uk-hidden");
        }
        
        function resetTableTableDateFilters${mType}() {
            resetTableTableFilters${mType}();
            
            var newUrl = new URL(document.URL);
            newUrl.searchParams.delete("statsFrom");
            newUrl.searchParams.delete("statsTo");
            window.location.replace(newUrl);
        }
        
        var tmpFormDateFrom${mType}, tmpFormDateTo${mType};
        function applyTableDateFilters${mType}() {
            if ($("#formDateFrom${mType}").val() !== tmpFormDateFrom${mType} || $("#formDateTo${mType}").val() !== tmpFormDateTo${mType}) {
                var date = $("#formDateFrom${mType}").val();
                var splitted = date.split("/");
                var from = new Date(splitted[1] + "/" + splitted[0] + "/" + splitted[2]).getTime();
                date = $("#formDateTo${mType}").val();
                splitted = date.split("/");
                var to = new Date(splitted[1] + "/" + splitted[0] + "/" + splitted[2]).getTime();

                var fromDate = new URL(document.URL).searchParams.get("statsFrom");
                var toDate = new URL(document.URL).searchParams.get("statsTo");
                if (typeof fromDate === 'undefined' || fromDate === null) {
                    if (document.URL.includes("?")) {
                        window.location.replace(document.URL + "&statsFrom=" + from + "&statsTo=" + to);
                    } else {
                        window.location.replace(document.URL + "?statsFrom=" + from + "&statsTo=" + to);
                    }
                } else {
                    var newUrl = document.URL;
                    newUrl = newUrl.replace("statsFrom=" + fromDate, "statsFrom=" + from);
                    newUrl = newUrl.replace("statsTo=" + toDate, "statsTo=" + to);
                    window.location.replace(newUrl);
                }
            } else {
                closeManageTableFilters${mType}();
            }
        }
        
        function selectAllStats${mType}() {
            // $("#teamStatsColumnsButton${mType}Content > li.uk-active").find("input:not(:checked)").attr("checked", "checked");
            $("#teamStatsColumnsButton${mType}Content > li.uk-active").find("input:not(:checked)").each(function (index, element) {
                manageColumnCheck${mType}($(element).attr("data-value"));
            });
        }
        
        function deselectAllStats${mType}() {
            $("#teamStatsColumnsButton${mType}Content > li.uk-active").find("input:checked").each(function (index, element) {
                manageColumnCheck${mType}($(element).attr("data-value"));
            });
        }
        
        function manageColumnCheck${mType}(typeId, specificCheck) {
            // aggiorno solo le checkbox del modale delle colonne, altrimenti segna la colonna anche come filtro
            var fieldName = "statsType" + typeId;
            var isChecked = $("#teamStatsColumnsModal${mType}").find("." + fieldName + "[checked]").length > 0;
            if (isChecked && (typeof specificCheck === "undefined" || !specificCheck)) {
                $("#teamStatsColumnsModal${mType}").find("." + fieldName).removeAttr("checked");
            } else {
                $("#teamStatsColumnsModal${mType}").find("." + fieldName).attr("checked", "checked");
            }
            $("#param-search-${mType}").val(null).trigger("change");
            $("#param-search-container-${mType}").removeClass("uk-open");
            
            updateSelectedButtons${mType}();
        }
        
        function manageGroupStats${mType}(element) {
            event.preventDefault();
            event.stopPropagation();
            
            var specificCheck = false;
            if ($(element).attr("class").includes("uk-icon-square-o")) {
                specificCheck = true;
                $(element).attr("class", $(element).attr("class").replace("uk-icon-square-o", "uk-icon-check-square-o"));
            } else {
                $(element).attr("class", $(element).attr("class").replace("uk-icon-check-square-o", "uk-icon-square-o"));
            }
            
            $(element).parent().parent().find("input").each(function (index, input) {
                var typeId = $(input).attr("data-value");
                manageColumnCheck${mType}(typeId, specificCheck);
            });
        }
        
        function manageEnableCompareColors${mType}() {
            enableColors${mType} = !enableColors${mType};
            reloadCompare${mType}();
        }
        
        var isCompareMode${mType}, needToFullscreen${mType} = false;
        function manageCompare${mType}() {
            isCompareMode${mType} = !isCompareMode${mType};
            sessionStorage["statsType${mType}CompareMode"] = isCompareMode${mType};
            if (typeof isCompareMode${mType} === "undefined") {
                isCompareMode${mType} = false;
            }

            if (isCompareMode${mType}) {
                $(".teamStatsGrouped${mType}").attr("checked", "checked");
                $("#compareContainer${mType}").removeClass("uk-hidden");
                $("#tableContainer${mType}").addClass("uk-hidden");
                
                if (!statsTable${mType}fullscreen) {
                    manageFullscreen${mType}();
                    needToFullscreen${mType} = true;
                }
            } else {
                $(".teamStatsGrouped${mType}").removeAttr("checked");
                $("#compareContainer${mType}").addClass("uk-hidden");
                $("#tableContainer${mType}").removeClass("uk-hidden");
                
                if (needToFullscreen${mType}) {
                    manageFullscreen${mType}();
                    needToFullscreen${mType} = false;
                }
            }
            
            reloadCompare${mType}();

            // carico tutte le immagini, c'� un bug per il quale passando da visible a non visible non carica le immagini
//            $("img.lazy").lazyload({
//                placeholder: "/sicstv/images/user_gray.png"
//            }).on("error", function () {
//                // Questo codice verr� eseguito se l'immagine non potr� essere caricata
//                $(this).attr("src", "/sicstv/images/user_gray.png");
//            });
        }
        
        var enableColors${mType} = true;
        function reloadCompare${mType}() {
            var table = '<table id="compareTable${mType}" class="uk-table uk-table-hover uk-table-striped stripe row-border order-column" style="border-collapse: collapse; max-height: 95%; width: 100%; display: block; overflow: auto">';

            var thead = statsTable${mType}.table().header();
            var tableColumns = [], tableData = [], playerIds = [], teamIds = [];
            if (typeof thead !== "undefined" && thead) {
                $(thead).find("th").each(function (index, column) {
                    tableColumns.push(column.innerText.trim());
                });
            }
            statsTable${mType}.rows({ page:'current' }).every(function (rowIdx, tableLoop, rowLoop) {
                var rowClass = $(this.node()).attr("class");
                if (typeof rowClass !== "undefined") {
                    tableData.push($(this.data()));
                    
                    var currentRow = $(this.data());
                    var firstColumn = currentRow[0];
                    var playerId = $(firstColumn).find("b").attr("playerid");
                    if (typeof playerId !== "undefined") {
                        playerIds.push(parseInt(playerId));
                    }
                    
                    var teamId = $(firstColumn).find("b").attr("teamid");
                    if (typeof teamId !== "undefined") {
                        teamIds.push(parseInt(teamId));
                    }
                }
            });

            table += '<thead><tr><th></th>';
            var index = 0, columnsLength;
            columnsLength = statsTable${mType}.rows({ page:'current' })[0].length + 1;
            for (var i = 0; i < columnsLength; i++) {
                if (playerIds.length > 0) {
                    if (index > 0) {
                        var link = getPlayerLink${mType}(playerIds[index - 1]);
                        var element = '<img height="80px" width="80px" class="imgPlayerLogo lazy" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/' + playerPhotoMap${mType}.get(playerIds[index - 1]) + '.png" onerror="this.src=&#39;/sicstv/images/user_gray.png&#39;">'
                        
                        table += '<th class="center-text middle-text" style="min-width: 80px !important">' + link.replace("{{name}}", element) + '</th>';
                    } else {
                        table += '<th class="center-text middle-text" style="min-width: 80px !important"></th>';
                    }
                } else if (teamIds.length > 0) {
                    if (index > 0) {
                        var link = getTeamLink${mType}(teamIds[index - 1]);
                        var element = '<img height="80px" width="80px" class="logoCompImg lazy" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/' + teamPhotoMap${mType}.get(teamIds[index - 1]) + '.png" onerror="this.src=&#39;https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png&#39;">'
                        
                        table += '<th class="center-text middle-text" style="min-width: 80px !important">' + link.replace("{{name}}", element) + '</th>';
                    } else {
                        table += '<th class="center-text middle-text" style="min-width: 80px !important"></th>';
                    }
                } else {
                    table += '<th class="center-text middle-text" style="min-width: 80px !important"></th>';
                }
                index++;
            }
            table += '</tr></thead>';
            
            // tbody
            table += '<tbody>';
            index = 0;
            tableColumns.forEach(function (column) {
                var title = column;
                
                var tableSort = statsTable${mType}.order();
                if (typeof tableSort !== "undefined") {
                    if (tableSort[0] && tableSort[0][0]) {
                        if (tableSort[0][0] === index) {
                            if (tableSort[0][1] === "desc") {
                                title = "<i class='uk-icon uk-icon-arrow-down uk-margin-right-small'/>" + title;
                            } else {
                                title = "<i class='uk-icon uk-icon-arrow-up uk-margin-right-small'/>" + title;
                            }
                        }
                    }
                }
                
                table += '<tr><td></td><td>' + title + '</td>';
                var isNumericColumn = false, colorScale, minValue, maxValue;
                var innerIndex = 0;
                tableData.forEach(function (element) {
                    var elementValue;
                    if (element[index] === "-") {
                        elementValue = "0";
                    } else {
                        elementValue = $(element[index]).text().trim().replace(',', '.');
                        var playerId = $(element[index]).find("b").attr("playerid");
                        if (typeof playerId !== "undefined") {
                            playerId = parseInt(playerId);
                        }
                    }
                    var elementLabel = elementValue;
                    
                    if (elementLabel.includes("<spring:message code='calendar.giornata'/>")) {
                        // se partita
                        elementLabel = elementLabel.replace(elementLabel.split("�")[1], "");
                    } else if (elementLabel.includes(" - ")) {
                        // se giocatore
                        elementLabel = elementLabel.split(" - ")[0];
                        var parts = elementLabel.split("�");
                        for (var i = 1; i < parts.length; i++) {
                            elementLabel = elementLabel.replace(parts[i], "");
                        }
                        
                        var link = getPlayerLink${mType}(playerIds[innerIndex]);
                        elementLabel = link.replace("{{name}}", elementLabel);
                    } else if (index === 0) {
                        // dovrebbe esserci il nome del team
                        var link = getTeamLink${mType}(teamIds[innerIndex]);
                        elementLabel = link.replace("{{name}}", elementLabel);
                    }
                    
                    if (typeof enableColors${mType} !== 'undefined' && enableColors${mType}) {
                        //elementValue = parseFloat(elementValue);

                        if (!isNumericColumn && !isNaN(parseFloat(elementValue))) {
                            isNumericColumn = true;
                            tableData.forEach(function (tmpElement) {
                                var value = $(tmpElement[index]).text().trim().replace(',', '.');
                                if (value === '-') {
                                    value = 0;
                                } else {
                                    value = parseFloat(value);
                                }

                                if (!isNaN(parseFloat(value))) {
                                    if (typeof minValue === 'undefined' || value < minValue) {
                                        minValue = value;
                                    }
                                    if (typeof maxValue === 'undefined' || value > maxValue) {
                                        maxValue = value;
                                    }
                                }
                            });

                            if (minValue === maxValue) {
                                colorScale = chroma.scale(['#ffffff00', '#ffffff00']).domain([minValue, maxValue]);
                            } else {
                                if (typeof oppositeColumns${mType}.get(column) !== "undefined") {
                                    colorScale = chroma.scale(['00ff00aa', '#ffff00aa', '#ff0000aa']).domain([minValue, maxValue]);
                                } else {
                                    colorScale = chroma.scale(['#ff0000aa', '#ffff00aa', '00ff00aa']).domain([minValue, maxValue]);
                                }
                            }
                        }
                        if (isNumericColumn) {
                            var tmpColor = colorScale(elementValue).rgb();
                            table += '<td><div class="center"><div style="width: 45px; border-radius: 25px; padding-top: 3px; padding-bottom: 3px; background: radial-gradient(at center, rgba(' + tmpColor + ',0.65), rgba(' + tmpColor + ',0.5)); object-fit: contain; filter: drop-shadow(0 0 5px rgba(' + tmpColor + ',0.2)) drop-shadow(0 0 5px rgba(' + tmpColor + ',0.2));"><span style="color: ' + getColoreInContrasto(tmpColor) + '">' + elementLabel + '</span></div></div></td>';
                        } else {
                            table += '<td>' + elementLabel + '</td>';
                        }
                    } else {
                        table += '<td>' + elementLabel + '</td>';
                    }
                    innerIndex++;
                });
                table += '</tr>';
                index++;
            });
            table += '</tbody>';

            if ($.fn.DataTable.isDataTable('#compareTable${mType}')) {
                tableCompare${mType}.clear().destroy(true);
            }

            $("#compareContent${mType}").empty(); // non dovrebbe fare nulla
            $("#compareContent${mType}").append('<div class="center uk-margin-left-small uk-float-left"><span class="uk-margin-right-small"><spring:message code='menu.user.compare.mode'/></span><input type="checkbox" class="teamStatsGrouped${mType}" id="teamStatsGrouped${mType}" onclick="manageCompare${mType}();" ' + (isCompareMode${mType} ? "checked" : "") + '></div>');
            $("#compareContent${mType}").append('<div class="center uk-margin-left-small uk-float-left"><span class="uk-margin-right-small"><spring:message code='smart.search.show.outliers'/></span><input type="checkbox" class="teamStatsOutliers" id="teamStatsOutliers${mType}" onchange="manageEnableCompareColors${mType}();" ' + (enableColors${mType} ? "checked" : "") + '></div>');
            $("#compareContent${mType}").append(table);
            tableCompare${mType} = $('#compareTable${mType}').DataTable({
                ordering: false,
                dom: 'Bfrtip',
                searching: false,
                paging: false,
                info: false,
                buttons: [
                    {
                        text: '<spring:message code='team.stats.esporta'/>',
                        attr: {
                            class: 'uk-button'
                        },
                        extend: 'collection',
                        buttons: [
                            {
                                extend: 'print',
                                exportOptions: {
                                    columns: ':visible'
                                },
                                title: 'SICS.tv-Stats'
                            }, {
                                extend: 'excel',
                                exportOptions: {
                                    columns: ':visible'
                                },
                                title: 'SICS.tv-Stats'
                            }
                        ]
                    }
                ],
                createdRow: function (row, data, dataIndex) {
                    // 'data' contiene l'oggetto JSON per la riga corrente
                    // metto tutti i dati in centro
                    $(row).find("td:not(:first-child)").attr('class', "center-text middle-text");
                }
            });
        }
        
        function getPlayerLink${mType}(playerId) {
            if (typeof playerId !== "undefined" && typeof playerLinkMap${mType} !== "undefined") {
                var playerLink = playerLinkMap${mType}.get(playerId);
                if (typeof playerLink !== "undefined") {
                    var link = '<a style="font-weight: bold; color: #46ab44" class="orangeOnHover statRowteams" href="/sicstv/user/goTo.htm?seasonId=${mUser.savedSeason}&link=';
                    var playerGotoLink = '/sicstv/user/player.htm?personal=false';
                    if (playerLink.playerId) {
                        playerGotoLink += "&playerId=" + playerLink.playerId;
                    }
                    if (playerLink.competitionId) {
                        playerGotoLink += "&competitionId=" + playerLink.competitionId;
                    }
                    if (playerLink.teamId) {
                        playerGotoLink += "&teamId=" + playerLink.teamId;
                    }
                } else {
                    return "{{name}}";
                }
                return link + encodeURIComponent(playerGotoLink) + '">{{name}}</a>';
            } else {
                return "{{name}}";
            }
        }
        
        function getTeamLink${mType}(teamId) {
            if (typeof teamId !== "undefined") {
                var teamLink = teamLinkMap${mType}.get(teamId);
                var link = '<a style="font-weight: bold; color: #46ab44" class="orangeOnHover statRowteams" href="/sicstv/user/goTo.htm?seasonId=${mUser.savedSeason}&link=';
                var teamGotoLink = '/sicstv/user/team.htm?personal=false';
                if (teamLink.competitionId) {
                    teamGotoLink += "&formCompetitionId=" + teamLink.competitionId;
                } else {
                    teamGotoLink += "&formCompetitionId=-1";
                }
                teamGotoLink += "&formTeamId=" + teamLink.teamId;
                return link + encodeURIComponent(teamGotoLink) + '">{{name}}</a>';
            } else {
                return "{{name}}";
            }
        }
        
        function searchParams${mType}() {
            var input = $("#param-search-${mType}").val().toLowerCase();
            if (input && input.length >= 2) {
                $("#param-search-result-container-${mType}").empty();
                
                var valid = [];
                $("#teamStatsColumnsButton${mType}Content").find(".${mType}-param").each(function (index, element) {
                    var metric = $(element).text().trim();
                    if (metric.toLowerCase().includes(input)) {
                        var validText = metric + "|" + $(element).attr("stats-id");
                        if (!valid.includes(validText)) {
                            valid.push(validText);
                        }
                    }
                });
                
                if (valid.length > 0) {
                    valid.forEach(function (element) {
                        var splitted = element.split("|");
                        
                        var newElement = "<li class='uk-hover'>";
                        newElement += "<a onclick='manageColumnCheck${mType}(" + splitted[1] + ", true);'>" + splitted[0] + "</a>";
                        newElement += "</li>";
                        $("#param-search-result-container-${mType}").append(newElement);
                    });
                    
                    $("#param-search-container-${mType}").addClass("uk-open");
                }
            } else {
                $("#param-search-container-${mType}").removeClass("uk-open");
            }
        }
    </script>

    <div class="uk-width-1-1 uk-hidden" id="compareContainer${mType}">
        <div id="compareContent${mType}"></div>
    </div>
    <div class="uk-width-1-1" id="tableContainer${mType}">
        <table id="teamStatsTable${mType}" class="uk-table uk-table-hover uk-table-striped stripe row-border order-column" style="border-collapse: collapse; margin-left: 3px;">
            <thead>
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                    <th class="center-text middle-text ${mType}noVis">${mFirstColumnLabel}</th>
                    <c:forEach var="column" items="${mTable.columnNameListFormatted}">
                        <th class="center-text middle-text">
                            ${column}
                        </th>
                    </c:forEach>
                </tr>
            </thead>
        </table>
    </div>
    
    <div id="teamStatsFiltersModal${mType}" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
        <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
            <div>
                <span class="modalClose" onclick="closeManageFilters${mType}();">&times;</span>
                <h3 style="text-transform: uppercase; font-weight: bold"><spring:message code="team.stats.create.filter.title"/></h3>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <label class="uk-text-medium" for="namePlaylistModify"><spring:message code="video.nome" />:</label>
                    <input id="teamStatsFilterName${mType}" class="uk-input" type="text" aria-label="Input" maxlength="32">
                    <button class="uk-h4 uk-button" onclick="createSaveFilter${mType}(true)"><spring:message code="team.stats.create"/></button>
                </div>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                        <tbody>
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td class="playlistTvTitle" style="width: 30vh">
                                   <spring:message code='team.stats.filter.name.label'/>
                                </td>
                                <td class="playlistTvTitle">
                                   <spring:message code='team.stats.colvis.title'/>
                                </td>
                            </tr>
                            <c:forEach var="filter" items="${mFilters.keySet()}">
                                <c:if test="${filter.isSics == null}">
                                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                        <td id="teamStatsFilterName${mType}${filter.id}">
                                            ${filter.name}
                                        </td>
                                        <td id="teamStatsFilterButtons${mType}${filter.id}">
                                            <button class="uk-icon-hover uk-button uk-button-mini" onclick="manageRenameFilter${mType}(${filter.id}, false)" title="<spring:message code='playlist.tooltip.rename'/>" id="teamStatsRenameButton${mType}${filter.id}"><i id="teamStatsRenameIcon${mType}${filter.id}" class="uk-icon-pencil"></i></button>
                                            <button class="uk-icon-hover uk-button uk-button-mini" onclick="deleteFilter${mType}(${filter.id})" title="<spring:message code='menu.user.delete'/>"><i class="uk-icon-trash"></i></button>
                                        </td>
                                    </tr>
                                </c:if>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <button style="min-width:80px;" onclick="closeManageFilters${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.close"/></button>
                </div>
            </div>
        </div>
    </div>

    <div id="teamStatsColumnsModal${mType}" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
        <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 55%;width:auto;height:auto;">
            <div>
                <span class="modalClose" onclick="closeManageColumnsModal${mType}();">&times;</span>
                <h3><spring:message code="team.stats.colvis.title"/></h3>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <div id="teamStatsColumnsButton${mType}" style="height: 50vh; overflow-y: auto">
                        <!-- viene inizializzato tramite Javascript funzione manageColumnsModal${mType}() -->
                        <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-float-left" id="teamStatsColumnsButton${mType}ul" data-uk-tab="{connect:'#teamStatsColumnsButton${mType}Content'}" style="margin-bottom: 10px">
                        <c:set var="isFirst" value="true"/>
                        <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                            <li><a style="border-top: 2px solid #dddddd;">${statGroup}</a></li>
                            <c:set var="isFirst" value="false"/>
                        </c:forEach>
                        </ul>
                        <div class="uk-float-right uk-autocomplete uk-form" id="param-search-container-${mType}">
                            <label><spring:message code="video.ricerca"/>:</label>
                            <input type="text" id="param-search-${mType}" oninput="searchParams${mType}();" class="uk-form-width-medium"/>
                            <div class="uk-dropdown" aria-expanded="true">
                                <ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left" id="param-search-result-container-${mType}">
                                </ul>
                            </div>
                        </div>
                        
                        <ul id="teamStatsColumnsButton${mType}Content" class="uk-switcher">
                            <c:set var="counter" value="0"/>
                            <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                                <li>
                                    <div id="accordition${counter}${mType}" uk-accordion data-uk-accordion="{collapse: false, showfirst: true}" class="uk-accordion uk-margin-remove accordionDiv${mType}">
                                        <span class="uk-accordion-title uk-hidden"></span>
                                        <div class="uk-accordion-content uk-width-1-1 uk-padding-remove">
                                            <div id="accorditionChild${counter}${mType}" class="uk-accordion uk-text-left accordionDivChild${mType}">
                                                <c:forEach var="group" items="${mGroupedColumns.get(statGroup).keySet()}">
                                                    <div class="width-1-4-x">
                                                        <button class="uk-button uk-accordion-title buttonTag" base-name="${group}" value="" title="${group}">
                                                            <i class="uk-float-left uk-button-mini uk-icon uk-icon-caret-down uk-active"></i>
                                                            ${group}
                                                            <i class="uk-float-right uk-button-mini uk-icon-square-o skip-reload" onclick="manageGroupStats${mType}(this);"></i>
                                                        </button>
                                                        <div>
                                                            <div class="uk-accordion-content uk-text-left">
                                                                <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                    <div class="uk-margin-small">
                                                                        <div class="uk-flex">
                                                                            <div style="width: 70%"></div>
                                                                            <div class="uk-margin-right-small" style="width: 10%">Tot.</div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <c:forEach var="stat" items="${mGroupedColumns.get(statGroup).get(group)}">
                                                                    <c:set var="checked" value=""/>
                                                                    <c:if test="${mTable.columnNameList.contains(stat.desc)}">
                                                                        <c:set var="checked" value="checked"/>
                                                                    </c:if>
                                                                    <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                        <div class="uk-margin-small">
                                                                            <div class="uk-flex">
                                                                                <div class="${mType}-param" style="width: 70%" stats-id="${stat.id}">
                                                                                    ${stat.desc}
                                                                                </div>
                                                                                <div class="uk-margin-right-small" style="width: 10%">
                                                                                    <input type="checkbox" class="statsType${stat.id}" onclick="manageColumnCheck${mType}(${stat.id});" data-value="${stat.id}" ${checked}/>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </c:forEach>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:forEach>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <c:set var="counter" value="${counter + 1}"/>
                            </c:forEach>
                        </ul>
                    </div>
                </div>
                <div class="uk-modal-footer uk-width-1-1" style="display: flex">
                    <div class="uk-text-left">
                        <button style="min-width:80px;" onclick="selectAllStats${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.selezionatutto"/></button>
                        <button style="min-width:80px;" onclick="deselectAllStats${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.deselezionatutto"/></button>
                    </div>
                    <div class="uk-text-right" style="margin-left: auto">
                        <button style="min-width:80px;" onclick="closeManageColumnsModal${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.close"/></button>
                        <button style="min-width:80px;" onclick="createSaveFilter${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="team.stats.save.table"/></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div id="teamStatsTableFilterModal${mType}" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
        <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
            <div>
                <span class="modalClose" onclick="closeManageTableFilters${mType}();">&times;</span>
                <h3><spring:message code="team.stats.table.filter.title"/></h3>
                <table class="uk-margin uk-modal-content uk-width-1-1">
                    <tbody>
                        <c:if test="${mType.equals('competitionPlayers') or mType.equals('players') or mType.equals('watchlistPlayer')}">
                            <tr>
                                <td style="width: 20%"><spring:message code='team.stats.min.minutes.played'/></td>
                                <td>
                                    <span class="uk-form" style="margin-right: 10px">
                                        <c:set var="minutesFilter" value="${45}"/>
                                        <c:if test="${mType == 'watchlistPlayer'}">
                                            <c:set var="minutesFilter" value="${0}"/>
                                        </c:if>
                                        <input style="number" min="1" value="${minutesFilter}" id="${mType}MinutesFilter" onchange="applyMinutesAndAgeFilter${mType}();" class="uk-form-width-mini filter-select input-text-calendar-search">
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td style="width: 20%"><spring:message code='player.report.age'/></td>
                                <td>
                                    <span class="uk-form" style="margin-right: 10px">
                                        <select id="tableFilterModal${mType}AgeType" class="uk-form-select selectItem" style="width: auto" onchange="applyMinutesAndAgeFilter${mType}();">
                                            <option value="1" selected><spring:message code='team.stats.minore.uguale'/></option>
                                            <option value="0"><spring:message code='team.stats.uguale'/></option>
                                            <option value="2"><spring:message code='team.stats.maggiore.uguale'/></option>
                                        </select>
                                        <input style="number" min="1" value="40" id="${mType}AgeFilter" onchange="applyMinutesAndAgeFilter${mType}();" class="uk-form-width-mini filter-select input-text-calendar-search">
                                    </span>
                                </td>
                            </tr>
                            <c:if test="${!mRoleFilters.isEmpty()}">
                                <tr>
                                    <td><spring:message code='team.ruolo'/></td>
                                    <td>
                                        <span class="uk-form" style="margin-right: 10px">
                                            <select id="tableFilterModal${mType}Role" class="uk-form-select selectItem" style="width: auto" onchange="applyTableFilter${mType}();">
                                                <option value="" selected><spring:message code='menu.all'/></option>
                                                <c:forEach var="filter" items="${mRoleFilters}">
                                                    <option value="${filter}">${filter}</option>
                                                </c:forEach>
                                            </select>
                                        </span>
                                    </td>
                                </tr>
                            </c:if>
                            <c:if test="${!mTeamFilters.isEmpty()}">
                                <tr>
                                    <td><spring:message code='player.report.team'/></td>
                                    <td>
                                        <span class="uk-form" style="margin-right: 10px">
                                            <select id="tableFilterModal${mType}Team" class="uk-form-select selectItem" style="width: auto" onchange="applyTableFilter${mType}();">
                                                <option value="" selected><spring:message code='menu.all'/></option>
                                                <c:forEach var="filter" items="${mTeamFilters}">
                                                    <option value="${filter}">${filter}</option>
                                                </c:forEach>
                                            </select>
                                        </span>
                                    </td>
                                </tr>
                            </c:if>
                            <c:if test="${!mFootFilters.isEmpty()}">
                                <tr>
                                    <td><spring:message code='player.report.foot'/></td>
                                    <td>
                                        <span class="uk-form" style="margin-right: 10px">
                                            <select id="tableFilterModal${mType}Foot" class="uk-form-select selectItem" style="width: auto" onchange="applyTableFilter${mType}();">
                                                <option value="" selected><spring:message code='menu.all'/></option>
                                                <c:forEach var="filter" items="${mFootFilters}">
                                                    <option value="${filter}">${filter}</option>
                                                </c:forEach>
                                            </select>
                                        </span>
                                    </td>
                                </tr>
                            </c:if>
                            <c:if test="${!mCountryFilters.isEmpty()}">
                                <tr>
                                    <td><spring:message code='player.report.citizenship'/></td>
                                    <td>
                                        <span class="uk-form" style="margin-right: 10px">
                                            <select id="tableFilterModal${mType}Country" class="uk-form-select selectItem" style="width: auto" onchange="applyTableFilter${mType}();">
                                                <option value="" selected><spring:message code='menu.all'/></option>
                                                <c:forEach var="filter" items="${mCountryFilters}">
                                                    <option value="${filter}">${filter}</option>
                                                </c:forEach>
                                            </select>
                                        </span>
                                    </td>
                                </tr>
                            </c:if>
                        </c:if>
                        <tr>
                            <td><spring:message code='lib.dataDa'/></td>
                            <td>
                                <span class="uk-form" style="margin-right: 10px">                                    
                                    <input id="formDateFrom${mType}" class="ui-state-default ui-corner-all inputSpacer3" type="text" value="<c:choose><c:when test="${mFrom != null}">${mFrom}</c:when><c:otherwise>01/01/${mSeasonId}</c:otherwise></c:choose>" data-uk-datepicker="{format:'DD/MM/YYYY'}" size="20" style="text-align:center"/>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><spring:message code='lib.dataA'/></td>
                            <td>
                                <span class="uk-form" style="margin-right: 10px">
                                    <input id="formDateTo${mType}" class="ui-state-default ui-corner-all inputSpacer3" type="text" value="<c:choose><c:when test="${mTo != null}">${mTo}</c:when><c:otherwise>${mToday}</c:otherwise></c:choose>" data-uk-datepicker="{format:'DD/MM/YYYY'}" size="20" style="text-align:center;"/>
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <button style="min-width:80px;" onclick="applyTableDateFilters${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.conferma"/></button>
                    <button style="min-width:80px;" onclick="resetTableTableDateFilters${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="video.resetfiltri"/></button>
                    <button style="min-width:80px;" onclick="closeManageTableFilters${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.close"/></button>
                </div>
            </div>
        </div>
    </div>
                
<!--    <div id="teamStatsTableDateFilterModal${mType}" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
        <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
            <div>
                <span class="modalClose" onclick="closeManageTableDateFilters${mType}();">&times;</span>
                <h3><spring:message code="team.stats.table.filter.title"/></h3>
                <table class="uk-margin uk-modal-content uk-width-1-1">
                    <tbody>
                        <tr>
                            <td><spring:message code='lib.dataDa'/></td>
                            <td>
                                <span class="uk-form" style="margin-right: 10px">                                    
                                    <input id="formDateFrom" class="ui-state-default ui-corner-all inputSpacer3" type="text" value="<c:choose><c:when test="${mFrom != null}">${mFrom}</c:when><c:otherwise>01/01/${mSeasonId}</c:otherwise></c:choose>" data-uk-datepicker="{format:'DD/MM/YYYY'}" size="20" style="text-align:center"/>
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <td><spring:message code='lib.dataA'/></td>
                            <td>
                                <span class="uk-form" style="margin-right: 10px">
                                    <input id="formDateTo" class="ui-state-default ui-corner-all inputSpacer3" type="text" value="<c:choose><c:when test="${mTo != null}">${mTo}</c:when><c:otherwise>${mToday}</c:otherwise></c:choose>" data-uk-datepicker="{format:'DD/MM/YYYY'}" size="20" style="text-align:center;"/>
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <button style="min-width:80px;" onclick="applyTableDateFilters${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.conferma"/></button>
                    <button style="min-width:80px;" onclick="resetTableTableDateFilters${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="video.resetfiltri"/></button>
                    <button style="min-width:80px;" onclick="closeManageTableDateFilters${mType}();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.close"/></button>
                </div>
            </div>
        </div>
    </div>-->
</div>