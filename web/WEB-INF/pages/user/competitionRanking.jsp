<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div id="competitionRankingContainer">
    <style>
        #competitionRankingContainer {
            padding: 10px;
        }

        .tableHeader {
            text-align: center;
            height: 10px;
            font-weight: bold;
        }
    </style>

    <c:forEach var="table" items="${mRanking.keySet()}">
        <p style="text-align: center; font-size: 25px; width: 100%; margin: 0">
            <img height="75px" width="75px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${mCompetitionMap.get(table).logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${mCompetitionMap.get(table).name}">
            <b class="uk-text-uppercase">${mCompetitionMap.get(table).name}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if></b> <span class="uk-text-uppercase"><spring:message code="menu.user.classifica"/></span>
        </p>
        <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
            <tbody>
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                    <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                    <td class="tableHeader" style="text-align: left"><spring:message code="player.report.team"/></td>
                    <td class="tableHeader"><spring:message code="team.report.totale.punti"/></td>
                    <td class="tableHeader"><spring:message code="team.report.totale.partite"/></td>
                    <td class="tableHeader"><spring:message code="team.report.punti.penalita"/></td>
                    <td class="tableHeader"><spring:message code="team.report.totale.vinte"/></td>
                    <td class="tableHeader"><spring:message code="team.report.totale.pari"/></td>
                    <td class="tableHeader"><spring:message code="team.report.totale.perse"/></td>
                    <td class="tableHeader"><spring:message code="team.report.totale.goal"/></td>
                    <td class="tableHeader"><spring:message code="team.report.totale.goal.subiti"/></td>
                    <td class="tableHeader"><spring:message code="team.report.totale.goal.differenza"/></td>
                </tr>
                <c:forEach var="row" items="${mRanking.get(table)}">
                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" <c:if test="${row.teamId == mTeam.id}">style="background-color: orange !important"</c:if>>
                        <td style="vertical-align: middle">
                            <c:choose>
                                <c:when test="${row.index == 1}">
                                    <img height="20px" width="20px" src="/sicstv/images/1st.png">
                                </c:when>
                                <c:when test="${row.index == 2}">
                                    <img height="20px" width="20px" src="/sicstv/images/2nd.png">
                                </c:when>
                                <c:when test="${row.index == 3}">
                                    <img height="20px" width="20px" src="/sicstv/images/3rd.png">
                                </c:when>
                                <c:otherwise>
                                    ${row.index}
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td style="vertical-align: middle"><img height="20px" width="20px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.teamLogo}.png" alt="${row.teamName}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'">
                            <a href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${mCompetitionMap.get(table).id}&formTeamId=${row.teamId}<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>">${row.teamName}</a>
                        </td>
                        <td style="vertical-align: middle; text-align: center">${row.totalPoints}</td>
                        <td style="vertical-align: middle; text-align: center">${row.totalMatchPlayed}</td>
                        <td style="vertical-align: middle; text-align: center">${row.penalityPoints}</td>
                        <td style="vertical-align: middle; text-align: center">${row.totalWin}</td>
                        <td style="vertical-align: middle; text-align: center">${row.totalEqual}</td>
                        <td style="vertical-align: middle; text-align: center">${row.totalLost}</td>
                        <td style="vertical-align: middle; text-align: center">
                            <a href="/sicstv/user/video.htm?personal=${personalSource}&id=&idComp=${mCompetitionMap.get(table).id}&idTeam=${row.teamId}&idPlayer=&goals=false&event=SICS-_-RTF&filter=&limit=50&gameIds=<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>">${row.totalGoal}</a>
                        </td>
                        <td style="vertical-align: middle; text-align: center">
                            <a href="/sicstv/user/video.htm?personal=${personalSource}&id=&idComp=${mCompetitionMap.get(table).id}&idTeam=${row.teamId}&idPlayer=&goals=false&event=SICS-_-RTS&filter=&limit=50&gameIds=<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>">${row.totalGoalSuffered}</a>
                        </td>
                        <td style="vertical-align: middle; text-align: center">${row.totalGoalDifference}</td>
                    </tr>
                </c:forEach>
            </tbody>
        </table>
        <p style="width: 100%; text-align: center; margin: 0; font-size: 11px"><spring:message code="competition.ranking.text.warning"/></p>
    </c:forEach>
</div>