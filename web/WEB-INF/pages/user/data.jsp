<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />

        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

        <script typt="text/javascript" src="/sicstv/uikit/js/components/notify.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script type="text/javascript" src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" ></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css">
        <link rel="stylesheet" href="/sicstv/uikit/css/components/tooltip.almost.flat.min.css" />
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />

        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.css?<%=System.currentTimeMillis()%>" />

        <script type="text/javascript">

            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }

            function jsResetField() {
                $("#newpsw").val("");
                $("#pswcheck").val("");
                $("#email").val('${mUser.email}');
            }

            function jsCheckData() {

                if (!validateEmail($("#email").val())) {
                    UIkit.notify('<p class="uk-text-center"><spring:message code="menu.email.errata"/></p>', {status: 'danger', timeout: 1000});
                    jsResetField();
                    return false;
                }

                if ($("#newpsw").val() === "" && $("#newpsw").is(":visible")) {
                    UIkit.notify('<p class="uk-text-center"><spring:message code="menu.password.errata"/></p>', {status: 'danger', timeout: 1000});
                    jsResetField();
                    return false;
                }
                if ($("#pswcheck").val() === "" && $("#pswcheck").is(":visible")) {
                    UIkit.notify('<p class="uk-text-center"><spring:message code="menu.password.errata"/></p>', {status: 'danger', timeout: 1000});
                    jsResetField();
                    return false;
                }
                if ($("#newpsw").val() !== $("#pswcheck").val()) {
                    UIkit.notify('<p class="uk-text-center"><spring:message code="menu.password.noncoincidenti"/></p>', {status: 'danger', timeout: 1000});
                    jsResetField();
                    return false;
                }

                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/checkPwd.htm",
                    cache: false,
                    data: encodeURI("&newPassword=" + $("#newpsw").val() + "&newEmail=" + $("#email").val()),
                    success: function (msg) {
                        UIkit.notify(msg, {status: 'success'});
                        $("#oldpsw").val($("#newpsw").val());
                        return false;
                    }
                });

            }

            function enablePswModify() {
                $("#newpswRow").removeClass("uk-hidden");
                $("#pswcheckRow").removeClass("uk-hidden");
                $("#newpsw").val("");
                $("#pswcheck").val("");
            }

            function validateEmail(email) {
                var re = /^([\w-]+(?:\.[\w-]+)*)@((?:[\w-]+\.)*\w[\w-]{0,66})\.([a-z]{2,6}(?:\.[a-z]{2})?)$/i;
                return re.test(email);
            }

            function jsUpdateSettings() {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/updateSettings.htm",
                    data: encodeURI("additionalStart=" + $("#eventAdditionalStart").val() + "&additionalEnd=" + $("#eventAdditionalEnd").val()),
                    cache: false,
                    success: function () {
                        UIkit.notify("<spring:message code='settings.update.success'/>", {status: 'success', timeout: 1000});
//                        setTimeout(function() {
//                            window.location.reload();
//                        }, 1000);
                    },
                    error: function () {
                        UIkit.notify("<spring:message code='settings.update.failed'/>", {status: 'danger', timeout: 1000});
                    }
                });
            }

            var exportTable;
            // Array to track the ids of the details displayed rows
            var detailRows = [];
            $(document).ready(function () {
                $(".inputNumber").on("keyup", function () {
                    var min = parseInt($(this).attr("min"));
                    var max = parseInt($(this).attr("max"));

                    var value = parseInt($(this).val());
                    if (value) {
                        if (value > max) {
                            $(this).val(max);
                        } else if (value < min) {
                            $(this).val(min);
                        }
                    }
                });

                exportTable = $('#exportTable').DataTable({
                    "dom": 'frtip',
                    "columnDefs": [
                        {"type": "date-euro", "targets": 5}
                    ],
                    "lengthChange": true,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "ext.errMode": "none",
                    "order": [[5, "desc"]],
                    "fixedHeader": true,
                    "lengthMenu": [10],
                    "pagingType": 'simple'
                });

                exportTable.on('draw.dt', function () {
                    initExportTable();
                });
                initExportTable();
            });

            function initExportTable() {
                $('#exportTable tbody tr td.dt-control').click(function () {
                    var tr = $(this).parents('tr');
                    var row = exportTable.row(tr);
                    var idx = $.inArray(tr.attr('id'), detailRows);

                    if (row.child.isShown()) {
                        tr.removeClass('details');
                        row.child.hide();

                        // Remove from the 'open' array
                        detailRows.splice(idx, 1);
                    } else {
                        tr.addClass('details');
                        row.child(format(row.data())).show();

                        // Add to the 'open' array
                        if (idx === -1) {
                            detailRows.push(tr.attr('id'));
                        }
                    }
                });
            }

            function format(d) {
                var rows = d[d.length - 1];
                var result = "<table><thead><tr><th style='min-width: 350px'><spring:message code="menu.user.export.tipologia"/></th><th><spring:message code="menu.user.export.stato"/></th><th><spring:message code="menu.user.export.inizio.clip"/></th><th><spring:message code="menu.user.export.durata.clip"/></th><th><spring:message code="menu.user.export.tempo.esecuzione"/></th></tr></thead><tbody>";
                var splitted = rows.split("||");
                splitted.forEach(function (item, index) {
                    item = item.trim();
                    if (item.includes(";")) {
                        var type = item.split(";")[0];
                        var status = item.split(";")[1];
                        var start = item.split(";")[2];
                        var duration = item.split(";")[3];
                        var execution = item.split(";")[4];

                        result += "<tr>";
                        result += "<td>" + type + "</td>";
                        result += "<td>" + status + "</td>";
                        result += "<td>" + start + "</td>";
                        result += "<td>" + duration + "</td>";
                        result += "<td>" + execution + "</td>";
                        result += "<tr>";
                    }
                });
                result += "</tbody></table>";
                return result;
            }
            
            function updateTranslations() {
                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/updateTranslations.htm",
                    cache: false,
                    success: function (msg) {
                        if (msg === "noPermission") {
                            UIkit.notify("<spring:message code="update.translations.no.perm"/>", {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="update.translations.ok"/>", {status: 'success', timeout: 1000});
                            window.location.reload();
                        }
                    },
                    error: function () {
                        UIkit.notify("<spring:message code="update.translations.no.perm"/>", {status: 'danger', timeout: 1000});
                    }
                });
            }
        </script>
    </head>

    <body>

        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>

        <div id="breadcrumb">
            <ul class="uk-breadcrumb uk-margin-left">
                <li style="text-transform: uppercase;" id="liBreadcrumb"><spring:message code="menu.user.data"/> <i id="showCurrentFilter"></i></li>
            </ul>
        </div>

        <div id="centralGrid" style="display: flex">
            <div id="userData" class="uk-width-1-2 uk-margin-left uk-margin-top">
                <div id="formUserData" class="uk-form uk-form-horizontal">
                    <legend><h2 style="text-transform: uppercase; font-weight: bold"><spring:message code="menu.user.data"/></h2></legend>
                    <div class="uk-form-row">
                        <label class="uk-form-label" style="text-transform: capitalize"><spring:message code="menu.email"/>:</label>
                        <input id="email" type="text" value="${curUser.email}" disabled>
                    </div>
                    <div class="uk-form-row" >
                        <label class="uk-form-label"><spring:message code="menu.password.old"/>:</label>
                        <input id="oldpsw" type="password" value="${curUser.password}" disabled="true">
                        <button class="uk-button uk-button-small uk-margin-small-left" onclick="enablePswModify();"><spring:message code="menu.modifica"/></button>
                    </div>
                    <div id="newpswRow" class="uk-form-row uk-hidden">
                        <label class="uk-form-label"><spring:message code="menu.password.new"/>:</label>
                        <input id="newpsw" type="password" value="${curUser.password}" >
                    </div>
                    <div id="pswcheckRow" class="uk-form-row uk-hidden">
                        <label class="uk-form-label"><spring:message code="menu.password.new.check"/>:</label>
                        <input id="pswcheck" type="password" value="${curUser.password}">
                    </div>

                    <button class="uk-button uk-margin-top" onclick="jsCheckData();"><spring:message code="menu.conferma"/></button>
                </div>

                <div class="uk-form uk-form-horizontal uk-margin-top">
                    <legend><h2 style="text-transform: uppercase; font-weight: bold"><spring:message code="menu.user.settings"/></h2></legend>
                    <div class="uk-form-row">
                        <label class="uk-form-label" style="text-transform: capitalize"><spring:message code="settings.additional.start"/>:</label>
                        <input id="eventAdditionalStart" class="inputNumber" type="number" min="0" max="30" value="${mSettings.getEventAdditionalStartSec()}">
                        <span data-uk-tooltip title="<spring:message code="settings.additional.start.tooltip"/>"><i class="uk-icon-info-circle"></i></span>
                    </div>
                    <div class="uk-form-row">
                        <label class="uk-form-label" style="text-transform: capitalize"><spring:message code="settings.additional.end"/>:</label>
                        <input id="eventAdditionalEnd" class="inputNumber" type="number" min="0" max="30" value="${mSettings.getEventAdditionalEndSec()}">
                        <span data-uk-tooltip title="<spring:message code="settings.additional.end.tooltip"/>"><i class="uk-icon-info-circle"></i></span>
                    </div>

                    <button class="uk-button uk-margin-top" onclick="jsUpdateSettings();"><spring:message code="menu.aggiorna"/></button>
                </div>

<!--                <div class="uk-form uk-form-horizontal uk-margin-top">
                    <legend><h2 style="text-transform: uppercase; font-weight: bold"><spring:message code="menu.user.language"/></h2></legend>
                    <div class="uk-flex language-container">
                        <img height="50px" width="50px" src="/sicstv/images/country_flags/it.svg" class="cursor-pointer <c:if test="${mUser.tvLanguage != null && mUser.tvLanguage.equals('it')}">current-language</c:if>" data-uk-tooltip title="<spring:message code="language.it.desc"/>" onclick="changeLanguage('it', '<spring:message code="menu.user.language.update.error"/>')">
                        <img height="50px" width="50px" src="/sicstv/images/country_flags/en.svg" class="cursor-pointer uk-margin-left <c:if test="${mUser.tvLanguage != null && mUser.tvLanguage.equals('en')}">current-language</c:if>" data-uk-tooltip title="<spring:message code="language.en.desc"/>" onclick="changeLanguage('en', '<spring:message code="menu.user.language.update.error"/>')">
                        <img height="50px" width="50px" src="/sicstv/images/country_flags/fr.svg" class="cursor-pointer uk-margin-left <c:if test="${mUser.tvLanguage != null && mUser.tvLanguage.equals('fr')}">current-language</c:if>" data-uk-tooltip title="<spring:message code="language.fr.desc"/>" onclick="changeLanguage('fr', '<spring:message code="menu.user.language.update.error"/>')">
                        <img height="50px" width="50px" src="/sicstv/images/country_flags/es.svg" class="cursor-pointer uk-margin-left <c:if test="${mUser.tvLanguage != null && mUser.tvLanguage.equals('es')}">current-language</c:if>" data-uk-tooltip title="<spring:message code="language.es.desc"/>" onclick="changeLanguage('es', '<spring:message code="menu.user.language.update.error"/>')">
                        </div>
                    </div>-->

                <c:if test="${mUser.isUserAdmin() || mUser.groupsetId == 2}">
                    <div class="uk-form uk-form-horizontal uk-margin-top">
                        <legend><h2 style="text-transform: uppercase; font-weight: bold">TRANSLATIONS</h2></legend>
                        <button onclick="updateTranslations();" class="uk-button">Update Labels</button>
                    </div>
                </c:if>

                <div class="uk-width-1-1 uk-margin-large-top" id="dwnDisp">
                    <c:if test="${(downloadLimit-numDownload) >=4}">
                        <span class="uk-text-success uk-text-large">
                            <spring:message code="menu.user.downloadDisp"/> ${numDownload} / ${downloadLimit}&nbsp;&nbsp;
                            <span data-uk-tooltip title="<spring:message code="menu.user.infoDownload"/>"><i class="uk-icon-info-circle"></i></span>
                        </span>
                    </c:if>
                    <c:if test="${(downloadLimit-numDownload)  < 4  && (downloadLimit-numDownload) > 0}">
                        <span class="uk-text-warning uk-text-large">
                            <spring:message code="menu.user.downloadDisp"/> ${numDownload} / ${downloadLimit}&nbsp;&nbsp;
                            <span data-uk-tooltip title="<spring:message code="menu.user.infoDownload"/>"><i class="uk-icon-info-circle"></i></span>
                        </span>
                    </c:if>
                    <c:if test="${(downloadLimit-numDownload) <= 0}">
                        <span class="uk-text-danger uk-text-large">
                            <spring:message code="menu.user.downloadDisp"/> ${numDownload} / ${downloadLimit}&nbsp;&nbsp;
                            <span data-uk-tooltip title="<spring:message code="menu.user.infoDownload"/>"><i class="uk-icon-info-circle"></i></span>
                        </span>
                    </c:if>
                </div>

            </div>
            <div id="userExport" class="uk-width-1-2 uk-margin-left uk-margin-top" style="padding: 10px">
                <c:if test="${!mExports.isEmpty()}">
                    <center><h2 style="font-weight: bold; text-transform: uppercase"><spring:message code="menu.user.export.storico"/></h2></center>
                    <table class="uk-table uk-table-comp uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1" id="exportTable">
                        <thead>
                            <tr>
                                <th style="width: 20px;"></th>
                                <th><spring:message code="menu.user.export.nome"/></th>
                                <th><spring:message code="menu.user.export.tipologia"/></th>
                                <th><spring:message code="menu.user.export.stato"/></th>
                                <th><spring:message code="menu.user.export.numero.clip"/></th>
                                <th><spring:message code="menu.user.export.data.richiesta"/></th>
                                <th class="uk-hidden">Detail</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="row" items="${mExports.keySet()}">
                                <tr>
                                    <td class="dt-control"></td>
                                    <td>${row.name}</td>
                                    <td>${row.type == 1 ? 'Video' : 'ZIP'}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${row.statusCode == 200}">
                                                <spring:message code="menu.user.export.completata"/>
                                            </c:when>
                                            <c:otherwise>
                                                <spring:message code="menu.user.export.in.errore"/>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>${row.clipAmount}</td>
                                    <td>${row.getStartTimeString()}</td>
                                    <td class="uk-hidden">
                                        <c:forEach var="detail" items="${mExports.get(row)}">
                                            <c:choose>
                                                <c:when test="${detail.type == 2}">
                                                    <c:set var="type" value="Zip"/>
                                                </c:when>
                                                <c:when test="${detail.type == 3}">
                                                    <c:set var="type" value="Video"/>
                                                </c:when>
                                                <c:otherwise>
                                                    <c:set var="type" value="Clip"/>
                                                </c:otherwise>
                                            </c:choose>
                                            ${type};${detail.statusCode == 200 ? mCompletataLabel : mErroreLabel};${detail.getClipStartString()};${detail.getClipDurationString()};${detail.getExecutionTimeString()}||
                                        </c:forEach>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </c:if>
            </div>
        </div>
    </body>
</html>
