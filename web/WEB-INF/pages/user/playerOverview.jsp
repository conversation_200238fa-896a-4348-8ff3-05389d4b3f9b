<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div id="playerOverviewContainer" style="margin-top: 5px">
    <script>
        var chartData = [];
        <c:forEach var="statName" items="${mCompetitionAverage.keySet()}">
            <c:set var="row" value="${mPlayerStats.get(statName)}"/>
            <c:set var="maxStatValue" value="${mTables.get(statName).get(0).eventAmountAverage90Minutes}"/>
            <c:set var="minStatValue" value="${mTables.get(statName).get(mTables.get(statName).size() - 1).eventAmountAverage90Minutes}"/>

            <c:if test="${maxStatValue != null}">
                <c:set var="rowPercentage" value="${row.getEventPercentage90Minutes(maxStatValue, minStatValue)}"/>
                <c:if test="${rowPercentage > 0}">
                    <c:set var="averagePercentage" value="${row.getPercentageByValues(mCompetitionAverage.get(statName), maxStatValue, minStatValue)}"/>
                    <c:set var="color" value="${row.statsTypeIsOpposite ? '#e85f5f' : '#008000'}"/>
                    <c:set var="halfAverage" value="${averagePercentage / 2}"/>
                    <c:set var="secondHalfAverage" value="${(100 - averagePercentage) / 2}"/>
                    <c:choose>
                        <c:when test="${rowPercentage <= halfAverage}">
                            <c:set var="color" value="${row.statsTypeIsOpposite ? '#008000' : '#e85f5f'}"/>
                        </c:when>
                        <c:when test="${rowPercentage <= (averagePercentage - 1)}">
                            <c:set var="color" value="${row.statsTypeIsOpposite ? '#5ab550' : '#e1e85f'}"/>
                        </c:when>
                        <c:when test="${rowPercentage <= (averagePercentage + secondHalfAverage)}">
                            <c:set var="color" value="${row.statsTypeIsOpposite ? '#e1e85f' : '#5ab550'}"/>
                        </c:when>
                    </c:choose>

        chartData.push({category: "${statName}", value: ${rowPercentage}, color: "${color}", tooltip: "<strong>${statName}</strong><br/><spring:message code="report.player.valore"/>: ${row.getEventAmountAverage90Minutes1Decimal()}<br/><spring:message code="report.player.posizione"/>: ${row.index}�"});
                </c:if>
            </c:if>
        </c:forEach>

        $(document).ready(function () {
            $("#playerMinutesPlayedLabel").html($("#playerMinutesPlayedLabel").attr("default-text") + $("#playerMinutesPlayed").val());
            $("#playerMatchesPlayedLabel").html($("#playerMatchesPlayedLabel").attr("default-text") + $("#playerMatchesPlayed").val());
            if (!$("#playerMinutesPlayed").val()) {
                $("#playerMinutesPlayedLabel").html($("#playerMinutesPlayedLabel").attr("default-text") + "N/A");
            }
            if (!$("#playerMatchesPlayed").val()) {
                $("#playerMatchesPlayedLabel").html("<spring:message code="player.matches.played"/>: N/A");
            }

            loadFilterModalTable("4${mPlayer.idRuolo}");
            updatePlayerReport();
            updateSelectedButtons();

            if (localStorage.getItem("sicstv/600/player/overview/view") === "table") {
                $("#overview-click").click();
            }
            if (chartData.length > 0) {
                createOverviewChart(chartData);
            }
        });

        function updatePlayerReport() {
        <c:if test="${mStatsTypeIds != null}">
            <c:if test="${mPlayerOverviewFilter != null}">
            $("#reportPlayerRole > center > button").removeClass("uk-button-active");
                <c:choose>
                    <c:when test="${mPlayerOverviewFilter.tableType.equals('41')}">
            $("#reportModalP").addClass("uk-button-active");
                    </c:when>
                    <c:when test="${mPlayerOverviewFilter.tableType.equals('42')}">
            $("#reportModalD").addClass("uk-button-active");
                    </c:when>
                    <c:when test="${mPlayerOverviewFilter.tableType.equals('43')}">
            $("#reportModalC").addClass("uk-button-active");
                    </c:when>
                    <c:when test="${mPlayerOverviewFilter.tableType.equals('44')}">
            $("#reportModalA").addClass("uk-button-active");
                    </c:when>
                </c:choose>
            </c:if>
            $("#reportPlayerStatsTypes > li > button").removeClass("uk-button-active");
        </c:if>
        <c:forEach var="statId" items="${mStatsTypeIds}">
            $("#reportModalStatButton${statId}").addClass("uk-button-active");
        </c:forEach>
        }

        function openPlayerOverviewColumnsModal() {
            $("#playerOverviewColumnsModal").addClass("uk-open");
            $("#playerOverviewColumnsModal").removeClass("uk-hidden");

            // inizializzo a mano lo switcher altrimenti non va per qualche motivo
            if ($("#playerOverviewColumnsButtonul > li.uk-active").length === 0) {
                var customOptions = {
                    connect: '#playerOverviewColumnsButtonContent'
                };
                UIkit.switcher('#playerOverviewColumnsButtonul', customOptions);

                var customOptionsAccordion = {
                    collapse: false,
                    showfirst: true
                };
                $(".accordionDiv").each(function (index, element) {
                    UIkit.accordion("#" + $(element).attr("id"), customOptionsAccordion).on('toggle.uk.accordion', function () {
                        updateSelectedButtons();
                    });
                });
            }
        }

        function closePlayerOverviewColumnsModal() {
            $("#playerOverviewColumnsModal").removeClass("uk-open");
            $("#playerOverviewColumnsModal").addClass("uk-hidden");
        }

        function createSavePlayerOverviewFilter(createNew) {
            var idPosition = ${mPlayer.idRuolo};
            var filterId;
            if (typeof createNew === 'undefined') {
        <c:if test="${mPlayerOverviewFilter != null}">
                filterId = ${mPlayerOverviewFilter.id};
        </c:if>
            }

            if (idPosition === 1 || idPosition === 2 || idPosition === 3 || idPosition === 4) {
                var selectedColumns = [];
                $("#playerOverviewColumnsButtonContent").find("input:checked").each(function () {
                    var columnIndex = $(this).attr("data-value");
                    if (typeof columnIndex !== 'undefined') {
                        selectedColumns.push(columnIndex);
                    }
                });
                $("#playerOverviewColumnsButtonContent").find("input:not(:checked)").each(function () {
                    var columnIndex = $(this).attr("data-value");
                    if (typeof columnIndex !== 'undefined') {
                        if (selectedColumns.includes(columnIndex)) {
                            selectedColumns.splice(selectedColumns.indexOf(columnIndex), 1);
                        }
                    }
                });
                if (!selectedColumns.includes("1")) {
                    selectedColumns.push("1");
                }

                var ajaxData;
                var name;
                if ($("#playerOverviewFilterName").val().length > 0) {
                    name = $("#playerOverviewFilterName").val();
                } else {
        <c:choose>
            <c:when test="${mUser.tvLanguage.equals('it')}">
                    name = "${mPlayer.descrizione_ruolo_it}";
            </c:when>
            <c:when test="${mUser.tvLanguage.equals('fr')}">
                    name = "${mPlayer.descrizione_ruolo_fr}";
            </c:when>
            <c:when test="${mUser.tvLanguage.equals('es')}">
                    name = "${mPlayer.descrizione_ruolo_es}";
            </c:when>
            <c:otherwise>
                    name = "${mPlayer.descrizione_ruolo_en}";
            </c:otherwise>
        </c:choose>
                    name += "${mRoleFilters != null ? (mRoleFilters.size() + 1) : 1}";
                }
                if (typeof filterId !== 'undefined') {
                    ajaxData = encodeURI("filterId=" + filterId + "&tableType=playerOverview" + idPosition + "&name=" + name + "&columns=" + selectedColumns + "&sort=");
                } else {
                    ajaxData = encodeURI("filterId=&tableType=playerOverview" + idPosition + "&name=" + name + "&columns=" + selectedColumns + "&sort=");
                }

                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/creaFiltroTeamStats.htm",
                    cache: false,
                    data: ajaxData,
                    success: function (msg) {
                        if (typeof filterId !== 'undefined') {
                            UIkit.notify("<spring:message code="team.stats.update.filter.success"/>", {status: 'success', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="team.stats.create.filter.success"/>", {status: 'success', timeout: 1000});
                        }

                        window.location.reload();
//                        loadPlayerOverview("filterSicsOverview");
                    },
                    error: function () {
                        if (typeof filterId !== 'undefined') {
                            UIkit.notify("<spring:message code="team.stats.update.filter.error"/>", {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="team.stats.create.filter.error"/>", {status: 'danger', timeout: 1000});
                        }
                    }
                });
            } else {
                UIkit.notify("<spring:message code="team.stats.update.filter.error"/>.<br>Invalid Role: " + idPosition, {status: 'danger', timeout: 1000});
            }
        }

        function deletePlayerOverviewFilter() {
            if (window.confirm("<spring:message code="team.stats.reset.question"/>")) {
                var filterId;
        <c:if test="${mPlayerOverviewFilter != null}">
                filterId = ${mPlayerOverviewFilter.id};
        </c:if>

                if (typeof filterId !== 'undefined') {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/cancellaFiltroTeamStats.htm",
                        cache: false,
                        data: encodeURI("&filterId=" + filterId),
                        success: function (msg) {
                            if (msg === 'true') {
                                UIkit.notify("<spring:message code="team.stats.reset.success"/>", {status: 'success', timeout: 1000});

                                // loadPlayerOverview("filterSicsOverview");
                                // ricarico la pagina altrimenti non si aggiornano i filtri per il report
                                window.location.reload();
                                sessionStorage.removeItem("statsTypeLastFilterId");
                            } else {
                                UIkit.notify("<spring:message code="team.stats.reset.error"/>", {status: 'danger', timeout: 1000});
                            }
                        },
                        error: function () {
                            UIkit.notify("<spring:message code="team.stats.reset.error"/>", {status: 'danger', timeout: 1000});
                        }
                    });
                }
            }
        }

        function selectAllStats() {
            $("#playerOverviewColumnsButtonContent > li.uk-active").find("input:not(:checked)").each(function (index, element) {
                manageColumnCheck($(element).attr("data-value"));
            });
        }

        function deselectAllStats() {
            $("#playerOverviewColumnsButtonContent > li.uk-active").find("input:checked").each(function (index, element) {
                manageColumnCheck($(element).attr("data-value"));
            });
        }

        function manageFilters() {
            $("#playerOverviewFiltersModal").addClass("uk-open");
            $("#playerOverviewFiltersModal").removeClass("uk-hidden");
        }

        function closeManageFilters() {
            $("#playerOverviewFiltersModal").removeClass("uk-open");
            $("#playerOverviewFiltersModal").addClass("uk-hidden");
            
            location.reload();
        }

        function manageRenameFilter(id, confirm) {
            if ($("#playerOverviewConfirmRename" + id).length > 0) { // salvo modifica
                $("#playerOverviewConfirmRename" + id).remove();

                if (confirm) {
                    var oldName = $("#playerOverviewFilterNameInput" + id).attr("defaultValue");
                    var newName = $("#playerOverviewFilterNameInput" + id).val();
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/rinominaFiltroTeamStats.htm",
                        cache: false,
                        data: encodeURI("&filterId=" + id + "&name=" + newName),
                        success: function (msg) {
                            if (msg === 'true') {
                                UIkit.notify("<spring:message code="team.stats.update.success"/>", {status: 'success', timeout: 1000});
                                $("#playerOverviewFilterName" + id).html(newName);
                                $("#playerOverviewFilter").html($("#playerOverviewFilter").html().replace(oldName, newName));
                            } else if (msg === 'noPermission') {
                                UIkit.notify("<spring:message code="team.stats.update.noperm"/>", {status: 'danger', timeout: 1000});
                            } else {
                                UIkit.notify("<spring:message code="team.stats.update.error"/>", {status: 'danger', timeout: 1000});
                            }
                        },
                        error: function () {
                            UIkit.notify("<spring:message code="team.stats.update.error"/>", {status: 'danger', timeout: 1000});
                        }
                    });
                } else {
                    $("#playerOverviewFilterName" + id).html($("#playerOverviewFilterNameInput" + id).attr("defaultValue"));
                }
                $("#playerOverviewRenameButton" + id).attr("title", "<spring:message code='playlist.tooltip.rename'/>");
                $("#playerOverviewRenameIcon" + id).attr("class", "uk-icon-pencil");
            } else {
                var newButton = $('<button id="playerOverviewConfirmRename' + id + '" class="uk-icon-hover uk-button uk-button-mini" onclick="manageRenameFilter(' + id + ', true)" title="Rinomina"><i class="uk-icon-check"></i></button>');
                $("#playerOverviewFilterButtons" + id).prepend(newButton);

                var newInput = $('<input id="playerOverviewFilterNameInput' + id + '" class="uk-input" type="text" aria-label="Input" maxlength="32" value="' + ($("#playerOverviewFilterName" + id).html().trim()) + '" defaultValue="' + ($("#playerOverviewFilterName" + id).html().trim()) + '">');
                $("#playerOverviewFilterName" + id).html(newInput);

                $("#playerOverviewRenameButton" + id).attr("title", "<spring:message code='playlist.tooltip.rename.undo'/>");
                $("#playerOverviewRenameIcon" + id).attr("class", "uk-icon-close");
            }
        }

        function deleteFilter(id) {
            if (window.confirm("<spring:message code="team.stats.delete.question"/>")) {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/cancellaFiltroTeamStats.htm",
                    cache: false,
                    data: encodeURI("&filterId=" + id),
                    success: function (msg) {
                        if (msg === 'true') {
                            UIkit.notify("<spring:message code="team.stats.delete.success"/>", {status: 'success', timeout: 1000});
                            sessionStorage.removeItem("statsTypeLastFilterId");
                            // window.location.reload();
                            $("#playerOverviewFilterName" + id).parent().remove();
                        } else if (msg === 'noPermission') {
                            UIkit.notify("<spring:message code="team.stats.delete.noperm"/>", {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="team.stats.delete.error"/>", {status: 'danger', timeout: 1000});
                        }
                    },
                    error: function () {
                        UIkit.notify("<spring:message code="team.stats.delete.error"/>", {status: 'danger', timeout: 1000});
                    }
                });
            }
        }

        function changeFilter(element) {
            var filterId = $("#" + element.id + " option:selected").val();
            var roleId = $("#" + element.id + " option:selected").attr("roleId");
            loadPlayerOverview("filterSicsOverview", filterId, roleId);
        }

        function loadFilterModalTable(tableType) {
            $("#playerOverviewFiltersModalTable > tbody > tr:not(:first-child)").remove();

        <c:forEach var="filter" items="${mFilters}">
            if ("${filter.tableType}" === tableType) {
                var content = '<tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">';
                content += '    <td id="playerOverviewFilterName${filter.id}">';
                content += '        ${filter.name}';
                content += '    </td>';
                content += '    <td id="playerOverviewFilterButtons${filter.id}">';
                content += '        <button class="uk-icon-hover uk-button uk-button-mini" onclick="manageRenameFilter(${filter.id}, false)" title="<spring:message code='playlist.tooltip.rename'/>" id="playerOverviewRenameButton${filter.id}"><i id="playerOverviewRenameIcon${filter.id}" class="uk-icon-pencil"></i></button>';
                content += '        <button class="uk-icon-hover uk-button uk-button-mini" onclick="deleteFilter(${filter.id})" title="<spring:message code='menu.user.delete'/>"><i class="uk-icon-trash"></i></button>';
                content += '    </td>';
                content += '</tr>';

                $("#playerOverviewFiltersModalTable > tbody").append(content);
            }
        </c:forEach>
        }

        function changeFilterModalTable(element) {
            var tableType = $("#" + element.id + " option:selected").val();
            loadFilterModalTable(tableType);
        }

        function updateSelectedButtons() {
            // ogni volta riparto da 0 per evitare di dover gestire tutto
            $("#playerOverviewColumnsButton").find("button.uk-button-active").removeClass("uk-button-active");
            $(".uk-accordion-content").find("button").find("i:not(.skip-reload)").removeClass("uk-icon-caret-down").addClass("uk-icon-caret-right");

            // ora attivo quelli che hanno almeno un input con il checked
            $(".uk-modal-dialog input").each(function (index, element) {
                if ($(element).prop("checked")) {
                    $(element).closest("div.width-1-4-x").find("button").addClass("uk-button-active");
                }
            });

            // gestione icona "aperto" e "chiuso"
            $(".uk-accordion-content").find("button.uk-active").find("i.uk-icon-caret-right:not(.skip-reload)").removeClass("uk-icon-caret-right").addClass("uk-icon-caret-down");
        }

        function manageColumnCheck(typeId, specificCheck) {
            // aggiorno solo le checkbox del modale delle colonne, altrimenti segna la colonna anche come filtro
            var fieldName = "statsType" + typeId;
            var isChecked = $("#playerOverviewColumnsModal").find("." + fieldName + "[checked]").length > 0;
            if (isChecked && (typeof specificCheck === "undefined" || !specificCheck)) {
                $("#playerOverviewColumnsModal").find("." + fieldName).removeAttr("checked");
            } else {
                $("#playerOverviewColumnsModal").find("." + fieldName).attr("checked", "checked");
            }
            $("#param-search").val(null).trigger("change");
            $("#param-search-container").removeClass("uk-open");

            updateSelectedButtons();
        }

        function manageGroupStats(element) {
            event.preventDefault();
            event.stopPropagation();

            var specificCheck = false;
            if ($(element).attr("class").includes("uk-icon-square-o")) {
                specificCheck = true;
                $(element).attr("class", $(element).attr("class").replace("uk-icon-square-o", "uk-icon-check-square-o"));
            } else {
                $(element).attr("class", $(element).attr("class").replace("uk-icon-check-square-o", "uk-icon-square-o"));
            }

            $(element).parent().parent().find("input").each(function (index, input) {
                var typeId = $(input).attr("data-value");
                manageColumnCheck(typeId, specificCheck);
            });
        }

        function changeOverview() {
            $(".swap-me").toggleClass("uk-hidden");
            $("#tableDiv").toggleClass("uk-width-1-2");

            if ($("#tableDiv").hasClass("uk-width-1-2")) {
                localStorage.setItem("sicstv/600/player/overview/view", "chart");
            } else {
                localStorage.setItem("sicstv/600/player/overview/view", "table");
            }
        }

        function searchParams() {
            var input = $("#param-search").val().toLowerCase();
            if (input && input.length >= 2) {
                $("#param-search-result-container").empty();

                var valid = [];
                $("#playerOverviewColumnsButton").find(".playerOverview-param").each(function (index, element) {
                    var metric = $(element).text().trim();
                    if (metric.toLowerCase().includes(input)) {
                        var validText = metric + "|" + $(element).attr("stats-id");
                        if (!valid.includes(validText)) {
                            valid.push(validText);
                        }
                    }
                });

                if (valid.length > 0) {
                    valid.forEach(function (element) {
                        var splitted = element.split("|");

                        var newElement = "<li class='uk-hover'>";
                        newElement += "<a onclick='manageColumnCheck(" + splitted[1] + ", true);'>" + splitted[0] + "</a>";
                        newElement += "</li>";
                        $("#param-search-result-container").append(newElement);
                    });

                    $("#param-search-container").addClass("uk-open");
                }
            } else {
                $("#param-search-container").removeClass("uk-open");
            }
        }
    </script>

    <style>
        #chartdiv {
            width: 90%;
            height: 50vh;
        }
    </style>

    <c:if test="${mCompetitions != null && !mCompetitions.isEmpty()}">
        <c:set var="isMultiCompetition" value="false"/>
        <c:set var="competitionList" value=""/>
        <c:set var="competitionIdList" value=""/>
        <c:forEach var="competition" items="${mCompetitions}">
            <c:if test="${!competitionList.isEmpty()}">
                <c:set var="isMultiCompetition" value="true"/>
                <c:set var="competitionList" value="${competitionList} / "/>
            </c:if>
            <c:set var="competitionList" value="${competitionList}${competition.name}"/>
            <c:set var="competitionIdList" value="${competitionIdList}${competition.id}"/>
        </c:forEach>
    </c:if>

    <input type="hidden" value="${mPlayerMinutesPlayed.getKey()}" id="playerMinutesPlayed"/>
    <input type="hidden" value="${mPlayerMinutesPlayed.getValue()}" id="playerMatchesPlayed"/>
    <div style="padding-right: 2px; padding-left: 2px">
        <button class="uk-button" onclick="openPlayerOverviewColumnsModal()"><spring:message code='team.stats.colvis.title'/></button>
        <button class="uk-button" onclick="manageFilters()"><spring:message code='team.stats.manage.filter'/></button>

        <c:if test="${mFilters != null}">
            <span class="uk-form" style="float: right;">
                <spring:message code='team.stats.view.as'/>
                <select id="playerOverviewFilter" onchange="changeFilter(this);" class="uk-form-select selectItem" style="width: auto">
                    <c:if test="${mPlayerOverviewFilter == null}">
                        <option value="" selected></option>
                    </c:if>
                    <c:set var="role" value=""/>
                    <c:forEach var="filter" items="${mFilters}">
                        <c:if test="${role.equals('') || !role.equals(filter.tableType)}">
                            <c:set var="role" value="${filter.tableType}"/>
                            <c:choose>
                                <c:when test="${role.equals('41')}">
                                    <option value="-1" roleId="1" style="text-transform: uppercase; font-weight: bold; font-size: 13px" <c:if test="${mPlayerOverviewFilter == null && mOverviewRoleId == 1}">selected</c:if>><spring:message code='ruoli.portiere'/></option>
                                </c:when>
                                <c:when test="${role.equals('42')}">
                                    <option value="-1" roleId="2" style="text-transform: uppercase; font-weight: bold; font-size: 13px" <c:if test="${mPlayerOverviewFilter == null && mOverviewRoleId == 2}">selected</c:if>><spring:message code='ruoli.difensore'/></option>
                                </c:when>
                                <c:when test="${role.equals('43')}">
                                    <option value="-1" roleId="3" style="text-transform: uppercase; font-weight: bold; font-size: 13px" <c:if test="${mPlayerOverviewFilter == null && mOverviewRoleId == 3}">selected</c:if>><spring:message code='ruoli.centrocampista'/></option>
                                </c:when>
                                <c:when test="${role.equals('44')}">
                                    <option value="-1" roleId="4" style="text-transform: uppercase; font-weight: bold; font-size: 13px" <c:if test="${mPlayerOverviewFilter == null && mOverviewRoleId == 4}">selected</c:if>><spring:message code='ruoli.attaccante'/></option>
                                </c:when>
                            </c:choose>
                        </c:if>
                        <option value="${filter.id}" <c:if test="${mPlayerOverviewFilter.id == filter.id}">selected</c:if>>${filter.name}</option>
                    </c:forEach>
                </select>
            </span>
        </c:if>
    </div>
    <c:if test="${mDivideStatistics}">
        <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse; margin-bottom: 5px; margin-top: 0px">
            <tbody>
                <!-- Colonne -->
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                    <td class="tableHeader"><spring:message code="report.player.statistica"/></td>
                    <td class="tableHeader"><spring:message code="report.player.posizione.ruolo"/></td>
                    <td class="tableHeader"><spring:message code="report.player.valore"/></td>
                    <td class="tableHeader" style="width: 45%; font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="team.stats.variabili.offensive"/></td>
                    <td class="tableHeader"><spring:message code="report.player.media.ruolo"/></td>
                    <td class="tableHeader"><spring:message code="report.player.valore.massimo.ruolo"/></td>
                </tr>
                <c:forEach var="statName" items="${mCompetitionAverage.keySet()}">
                    <c:set var="row" value="${mPlayerStats.get(statName)}"/>
                    <c:if test="${!row.statsTypeIsOpposite}">
                        <c:set var="maxStatValue" value="${mTables.get(statName).get(0).eventAmountAverage90Minutes}"/>
                        <c:set var="minStatValue" value="${mTables.get(statName).get(mTables.get(statName).size() - 1).eventAmountAverage90Minutes}"/>

                        <c:if test="${maxStatValue != null}">
                            <c:set var="rowPercentage" value="${row.getEventPercentage90Minutes(maxStatValue, minStatValue)}"/>
                            <tr>
                                <c:if test="${row.filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${isMultiCompetition == 'true' ? -1 : competitionIdList}&idTeam=${mTeamId}&idPlayer=${mPlayer.id}&goals=false&event=${row.getFilterEncoded()}&filter=&limit=50"/>
                                    <td><a href="/sicstv/user/goTo.htm?seasonId=${mSeasonId}&link=${row.encodeString(link)}" style="text-decoration: underline">${statName} (${row.getEventAmountFormatted()})</a></td>
                                </c:if>
                                <c:if test="${row.filter == null}">
                                    <td>${statName} (${row.getEventAmountFormatted()})</td>
                                </c:if>
                                <td class="tableRow">${row.index}</td>
                                <td class="tableRow">${row.getEventAmountAverage90Minutes1Decimal()}</td>
                                <td>
                                    <div class="container-progress-bar">
                                        <c:set var="averagePercentage" value="${row.getPercentageByValues(mCompetitionAverage.get(statName), maxStatValue, minStatValue)}"/>

                                        <div style="margin-left: ${averagePercentage}%; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                        <c:set var="color" value="${row.statsTypeIsOpposite ? '#e85f5f' : 'green'}"/>
                                        <c:set var="halfAverage" value="${averagePercentage / 2}"/>
                                        <c:set var="secondHalfAverage" value="${(100 - averagePercentage) / 2}"/>
                                        <c:choose>
                                            <c:when test="${rowPercentage <= halfAverage}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? 'green' : '#e85f5f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage - 1)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#5ab550' : '#e1e85f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage + secondHalfAverage)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#e1e85f' : '#5ab550'}"/>
                                            </c:when>
                                        </c:choose>
                                        <div class="progress" statsTypeIsOpposite="${row.statsTypeIsOpposite}" halfAverage="${halfAverage}" secondHalfAverage="${secondHalfAverage}" rowPercentage="${rowPercentage}" style="margin-left: -${averagePercentage}%; width: ${rowPercentage}%; background-color: ${color} !important">
                                        </div>
                                    </div>
                                </td>
                                <td class="tableRow">${mStatsFunctions.valueWith1Decimal(mCompetitionAverage.get(statName))}</td>
                                <td class="tableRow">${mTables.get(statName).get(0).getEventAmountAverage90Minutes1Decimal()}</td>
                            </tr>
                        </c:if>
                    </c:if>
                </c:forEach>
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                    <td class="tableHeader"><spring:message code="report.player.statistica"/></td>
                    <td class="tableHeader"><spring:message code="report.player.posizione.ruolo"/></td>
                    <td class="tableHeader"><spring:message code="report.player.valore"/></td>
                    <td class="tableHeader" style="width: 45%; font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="team.stats.variabili.difensive"/></td>
                    <td class="tableHeader"><spring:message code="report.player.media.ruolo"/></td>
                    <td class="tableHeader"><spring:message code="report.player.valore.massimo.ruolo"/></td>
                </tr>
                <c:forEach var="statName" items="${mCompetitionAverage.keySet()}">
                    <c:set var="row" value="${mPlayerStats.get(statName)}"/>
                    <c:if test="${row.statsTypeIsOpposite}">
                        <c:set var="maxStatValue" value="${mTables.get(statName).get(0).eventAmountAverage90Minutes}"/>
                        <c:set var="minStatValue" value="${mTables.get(statName).get(mTables.get(statName).size() - 1).eventAmountAverage90Minutes}"/>

                        <c:if test="${maxStatValue != null}">
                            <c:set var="rowPercentage" value="${row.getEventPercentage90Minutes(maxStatValue, minStatValue)}"/>
                            <tr>
                                <c:if test="${row.filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${isMultiCompetition == 'true' ? -1 : competitionIdList}&idTeam=${mTeamId}&idPlayer=${mPlayer.id}&goals=false&event=${row.getFilterEncoded()}&filter=&limit=50"/>
                                    <td><a href="/sicstv/user/goTo.htm?seasonId=${mSeasonId}&link=${row.encodeString(link)}" style="text-decoration: underline">${statName} (${row.getEventAmountFormatted()})</a></td>
                                </c:if>
                                <c:if test="${row.filter == null}">
                                    <td>${statName} (${row.getEventAmountFormatted()})</td>
                                </c:if>
                                <td class="tableRow">${row.index}</td>
                                <td class="tableRow">${row.getEventAmountAverage90Minutes1Decimal()}</td>
                                <td>
                                    <div class="container-progress-bar">
                                        <c:set var="averagePercentage" value="${row.getPercentageByValues(mCompetitionAverage.get(statName), maxStatValue, minStatValue)}"/>

                                        <div style="margin-left: ${averagePercentage}%; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                        <c:set var="color" value="${row.statsTypeIsOpposite ? '#e85f5f' : 'green'}"/>
                                        <c:set var="halfAverage" value="${averagePercentage / 2}"/>
                                        <c:set var="secondHalfAverage" value="${(100 - averagePercentage) / 2}"/>
                                        <c:choose>
                                            <c:when test="${rowPercentage <= halfAverage}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? 'green' : '#e85f5f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage - 1)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#5ab550' : '#e1e85f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage + secondHalfAverage)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#e1e85f' : '#5ab550'}"/>
                                            </c:when>
                                        </c:choose>
                                        <div class="progress" statsTypeIsOpposite="${row.statsTypeIsOpposite}" halfAverage="${halfAverage}" secondHalfAverage="${secondHalfAverage}" rowPercentage="${rowPercentage}" style="margin-left: -${averagePercentage}%; width: ${rowPercentage}%; background-color: ${color} !important">
                                        </div>
                                    </div>
                                </td>
                                <td class="tableRow">${mStatsFunctions.valueWith1Decimal(mCompetitionAverage.get(statName))}</td>
                                <td class="tableRow">${mTables.get(statName).get(0).getEventAmountAverage90Minutes1Decimal()}</td>
                            </tr>
                        </c:if>
                    </c:if>
                </c:forEach>
            </tbody>
        </table>
    </c:if>
    <c:if test="${!mDivideStatistics}">
        <div class="center">
            <span class="uk-margin-right-small"><spring:message code="overview.chart"/></span>
            <label class="uk-switch" for="overviewView">
                <input type="checkbox" id="overviewView" onchange="changeOverview();">
                <div class="uk-switch-slider" id="overview-click"></div>
            </label>
            <span class="uk-margin-left-small"><spring:message code="overview.table"/></span>
        </div>
        <div class="uk-flex">
            <div id="chartdiv" class="swap-me">

            </div>
            <table id="tableDiv" class="uk-table uk-table-hover uk-table-striped uk-width-1-2" style="border-collapse: collapse; margin-bottom: 5px; margin-top: 0px">
                <tbody>
                    <!-- Colonne -->
                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                        <td class="tableHeader"><spring:message code="report.player.statistica"/></td>
                        <td class="tableHeader"><spring:message code="report.player.posizione.ruolo"/></td>
                        <td class="tableHeader"><spring:message code="report.player.valore"/></td>
                        <td class="tableHeader uk-hidden swap-me" style="width: 45%"></td>
                        <td class="tableHeader"><spring:message code="report.player.media.ruolo"/></td>
                        <td class="tableHeader"><spring:message code="report.player.valore.massimo.ruolo"/></td>
                    </tr>
                    <c:forEach var="statName" items="${mCompetitionAverage.keySet()}">
                        <c:set var="row" value="${mPlayerStats.get(statName)}"/>
                        <c:set var="maxStatValue" value="${mTables.get(statName).get(0).eventAmountAverage90Minutes}"/>
                        <c:set var="minStatValue" value="${mTables.get(statName).get(mTables.get(statName).size() - 1).eventAmountAverage90Minutes}"/>

                        <c:if test="${maxStatValue != null}">
                            <c:set var="rowPercentage" value="${row.getEventPercentage90Minutes(maxStatValue, minStatValue)}"/>
                            <tr>
                                <c:if test="${row.filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${isMultiCompetition == 'true' ? -1 : competitionIdList}&idTeam=${mTeamId}&idPlayer=${mPlayer.id}&goals=false&event=${row.getFilterEncoded()}&filter=&limit=50"/>
                                    <td><a href="/sicstv/user/goTo.htm?seasonId=${mSeasonId}&link=${row.encodeString(link)}" style="text-decoration: underline">${statName} (${row.getEventAmountFormatted()})</a></td>
                                </c:if>
                                <c:if test="${row.filter == null}">
                                    <td>${statName} (${row.getEventAmountFormatted()})</td>
                                </c:if>
                                <td class="tableRow">${row.getIndexForTable()}</td>
                                <td class="tableRow">${row.getEventAmountAverage90Minutes1Decimal()}</td>
                                <td class="uk-hidden swap-me">
                                    <div class="container-progress-bar">
                                        <c:set var="averagePercentage" value="${row.getPercentageByValues(mCompetitionAverage.get(statName), maxStatValue, minStatValue)}"/>

                                        <div style="margin-left: ${averagePercentage}%; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                        <c:set var="color" value="${row.statsTypeIsOpposite ? '#e85f5f' : 'green'}"/>
                                        <c:set var="halfAverage" value="${averagePercentage / 2}"/>
                                        <c:set var="secondHalfAverage" value="${(100 - averagePercentage) / 2}"/>
                                        <c:choose>
                                            <c:when test="${rowPercentage <= halfAverage}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? 'green' : '#e85f5f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage - 1)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#5ab550' : '#e1e85f'}"/>
                                            </c:when>
                                            <c:when test="${rowPercentage <= (averagePercentage + secondHalfAverage)}">
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#e1e85f' : '#5ab550'}"/>
                                            </c:when>
                                        </c:choose>
                                        <div class="progress" statsTypeIsOpposite="${row.statsTypeIsOpposite}" halfAverage="${halfAverage}" secondHalfAverage="${secondHalfAverage}" rowPercentage="${rowPercentage}" style="margin-left: -${averagePercentage}%; width: ${rowPercentage}%; background-color: ${color} !important">
                                        </div>
                                    </div>
                                </td>
                                <td class="tableRow">${mStatsFunctions.valueWith1Decimal(mCompetitionAverage.get(statName))}</td>
                                <td class="tableRow">${mTables.get(statName).get(0).getEventAmountAverage90Minutes1Decimal()}</td>
                            </tr>
                        </c:if>
                    </c:forEach>
                </tbody>
            </table>
        </div>
    </c:if>
    <p style="width: 100%; text-align: center; margin: 0; font-size: 11px"><spring:message code="player.profile.competition.list"/>: ${competitionList}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if></p>
    <c:if test="${mShowWarningMessage != null}">
        <p class="uk-text-warning uk-text-bold" style="width: 100%; text-align: center; margin: 0; font-size: 11px"><spring:message code="player.overview.data.warning.playtime"/></p>
    </c:if>

    <div id="playerOverviewColumnsModal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
        <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 55%;width:auto;height:auto;">
            <div>
                <span class="modalClose" onclick="closePlayerOverviewColumnsModal();">&times;</span>
                <h3><spring:message code="team.stats.colvis.title"/></h3>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <div id="playerOverviewColumnsButton" style="height: 50vh; overflow-y: auto">
                        <!-- viene inizializzato tramite Javascript funzione manageColumnsModal() -->
                        <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-float-left" id="playerOverviewColumnsButtonul" data-uk-tab="{connect:'#playerOverviewColumnsButtonContent'}" style="margin-bottom: 10px">
                            <c:set var="isFirst" value="true"/>
                            <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                                <li><a style="border-top: 2px solid #dddddd;">${statGroup}</a></li>
                                    <c:set var="isFirst" value="false"/>
                                </c:forEach>
                        </ul>
                        <div class="uk-float-right uk-autocomplete uk-form" id="param-search-container">
                            <label><spring:message code="video.ricerca"/>:</label>
                            <input type="text" id="param-search" oninput="searchParams();" class="uk-form-width-medium"/>
                            <div class="uk-dropdown" aria-expanded="true">
                                <ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left" id="param-search-result-container">
                                </ul>
                            </div>
                        </div>

                        <ul id="playerOverviewColumnsButtonContent" class="uk-switcher">
                            <c:set var="counter" value="0"/>
                            <c:forEach var="statGroup" items="${mGroupedColumns.keySet()}">
                                <li>
                                    <div id="accordition${counter}" uk-accordion data-uk-accordion="{collapse: false, showfirst: true}" class="uk-margin-remove accordionDiv">
                                        <span class="uk-accordion-title uk-hidden"></span>
                                        <div class="uk-accordion-content uk-width-1-1 uk-padding-remove">
                                            <div class="uk-accordion uk-text-left">
                                                <c:forEach var="group" items="${mGroupedColumns.get(statGroup).keySet()}">
                                                    <div class="width-1-4-x">
                                                        <button class="uk-button uk-accordion-title buttonTag" base-name="${group}" value="" title="${group}">
                                                            <i class="uk-float-left uk-button-mini uk-icon uk-icon-caret-down uk-active"></i>
                                                            ${group}
                                                            <i class="uk-float-right uk-button-mini uk-icon-square-o skip-reload" onclick="manageGroupStats(this);"></i>
                                                        </button>
                                                        <div>
                                                            <div class="uk-accordion-content uk-text-left">
                                                                <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                    <div class="uk-margin-small">
                                                                        <div class="uk-flex">
                                                                            <div style="width: 70%"></div>
                                                                            <div class="uk-margin-right-small" style="width: 10%">Tot.</div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <c:forEach var="stat" items="${mGroupedColumns.get(statGroup).get(group)}">
                                                                    <c:set var="checked" value=""/>
                                                                    <c:if test="${mCompetitionAverage.keySet().contains(stat.desc)}">
                                                                        <c:set var="checked" value="checked"/>
                                                                    </c:if>
                                                                    <div class="tagEvento uk-form uk-margin-small-bottom">
                                                                        <div class="uk-margin-small">
                                                                            <div class="uk-flex">
                                                                                <div class="playerOverview-param" style="width: 70%" stats-id="${stat.id}">
                                                                                    ${stat.desc}
                                                                                </div>
                                                                                <div class="uk-margin-right-small" style="width: 10%">
                                                                                    <input type="checkbox" class="statsType${stat.id}" onclick="manageColumnCheck(${stat.id});" data-value="${stat.id}" ${checked}/>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </c:forEach>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </c:forEach>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <c:set var="counter" value="${counter + 1}"/>
                            </c:forEach>
                        </ul>
                    </div>
                </div>
                <div class="uk-modal-footer uk-width-1-1" style="display: flex">
                    <div class="uk-text-left">
                        <button style="min-width:80px;" onclick="selectAllStats();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.selezionatutto"/></button>
                        <button style="min-width:80px;" onclick="deselectAllStats();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.deselezionatutto"/></button>
                    </div>
                    <div class="uk-text-right" style="margin-left: auto">
                        <c:if test="${mPlayerOverviewFilter != null}">
                        <!--<button style="min-width:80px;" onclick="deletePlayerOverviewFilter();" class="uk-text-large uk-button uk-modal-close" title="<spring:message code="team.stats.reset.table.title"/>"><spring:message code="team.stats.reset.table"/></button>-->
                        </c:if>
                        <button style="min-width:80px;" onclick="createSavePlayerOverviewFilter();" class="uk-text-large uk-button uk-modal-close"><spring:message code="team.stats.save.table"/></button>
                        <button style="min-width:80px;" onclick="closePlayerOverviewColumnsModal();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.close"/></button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="playerOverviewFiltersModal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
        <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
            <div>
                <span class="modalClose" onclick="closeManageFilters();">&times;</span>
                <h3 style="text-transform: uppercase; font-weight: bold"><spring:message code="team.stats.create.filter.title"/></h3>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <label class="uk-text-medium" for="namePlaylistModify"><spring:message code="video.nome" />:</label>
                    <input id="playerOverviewFilterName" class="uk-input" type="text" aria-label="Input" maxlength="32">
                    <button class="uk-h4 uk-button" onclick="createSavePlayerOverviewFilter(true)"><spring:message code="team.stats.create"/></button>
                </div>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <span class="uk-form">
                        <spring:message code='player.ruolo'/>
                        <select onchange="changeFilterModalTable(this);" id="playerOverviewFiltersModalRole">
                            <option value="41" <c:if test="${mPlayer.idRuolo == 1}">selected</c:if>><spring:message code='ruoli.portiere'/></option>
                            <option value="42" <c:if test="${mPlayer.idRuolo == 2}">selected</c:if>><spring:message code='ruoli.difensore'/></option>
                            <option value="43" <c:if test="${mPlayer.idRuolo == 3}">selected</c:if>><spring:message code='ruoli.centrocampista'/></option>
                            <option value="44" <c:if test="${mPlayer.idRuolo == 4}">selected</c:if>><spring:message code='ruoli.attaccante'/></option>
                            </select>
                        </span>
                        <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;" id="playerOverviewFiltersModalTable">
                            <tbody>
                                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                    <td class="playlistTvTitle" style="width: 30vh">
                                    <spring:message code='team.stats.filter.name.label'/>
                                </td>
                                <td class="playlistTvTitle">
                                    <spring:message code='team.stats.colvis.title'/>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <button style="min-width:80px;" onclick="closeManageFilters();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.close"/></button>
                </div>
            </div>
        </div>
    </div>
</div>