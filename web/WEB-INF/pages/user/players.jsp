<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
		<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
		<title><spring:message code="theme.title"/></title>
		<link href="/sicstv/styles/darkness/jquery-ui-1.10.3.custom.css" rel="stylesheet" type="text/css" media="screen"  />
		<link href="/sicstv/styles/main.css" rel="stylesheet" type="text/css"/>
		<link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
		<link href="/sicstv/styles/analysisCss.css" rel="stylesheet" type="text/css"/>
		<link href="/sicstv/styles/jquery.multiselect.css" rel="stylesheet" type="text/css"/>
		<!--link href="//fonts.googleapis.com/css?family=Unica+One" rel="stylesheet" type="text/css"/-->
		<!--	<script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jquery/1.8.2/jquery.min.js"></script> -->
		<!--script type="text/javascript" src="/sicstv/js/jquery-ui-1.11.4.js"></script--> 
		<script type="text/javascript" src="/sicstv/js/jquery-ui-1.11.4.min.js"></script> 
		<!--	<script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jqueryui/1.8.2/jquery-ui.js"></script> -->
		<script type="text/javascript" src="/sicstv/js/jquery.multiselect.js"></script> 
		<script type="text/javascript" src="/sicstv/js/analysis/analysis.js"></script>
		<script type="text/javascript" src="/sicstv/js/analysis/tabs.js"></script>
		<script src="/sicstv/js/analysis/highcharts.js"></script>
		<script src="/sicstv/js/analysis/modules/exporting.js"></script>
		<script>
			function jsChangeSeason() {
				var valoreStagione = $("#formSeasonId").val();
				var valoreCompetizione = $("#formCompetitionId").val();//posso prendere quello visualizzato perchè sarà uguale a quello nel cookie
				//jQuery.cookie("cookieSics", valoreStagione + "#" + valoreCompetizione);
				jsListGames();
				jsListCompetition();
			}

			function jsCampionato() {
				var valoreStagione = $("#formSeasonId").val();
				var valoreCompetizione = $("#formCompetitionId").val();//posso prendere quello visualizzato perchè sarà uguale a quello nel cookie
				//jQuery.cookie("cookieSics", valoreStagione + "#" + valoreCompetizione);
				jsListGames();
			}


			function jsTeam() {
				jsListGames();

			}

			function jsListGames() {
				var matId = $("#formMatchdayId option:selected").val();
				if (matId == undefined)
					matId = "";
				var teamSelected = $("#formTeamId option:selected").val();
				$.ajax({
					type: "GET",
					url: "/sicstv/user/searchAnalysis.htm",
					cache: false,
					data: encodeURI("formSeasonId=" + $("#formSeasonId option:selected").val() + "&formCompetitionId=" + $("#formCompetitionId option:selected").val() + "&formTeamId=" + $("#formTeamId option:selected").val() + "&formDateFrom=" + $("#formDateFrom").val() + "&formDateTo=" + $("#formDateTo").val() + "&formReferee=" + $("#formReferee").val() + "&formMatchdayId=" + matId),
					//data: "formSeasonId=" + $("#formSeasonId option:selected").val() + "&formCompetitionId=" + $("#formCompetitionId option:selected").val() + "&formDateFrom=" + $("#formDateFrom").val() + "&formDateTo=" + $("#formDateTo").val() + "&formReferee=" + $("#formReferee").val() + "&formMatchdayId=" + matId,
					contentType: "json",
					success: function (data) {
						var response = $.parseJSON(data);
						$("#selectPartita").empty();
						var teams = [];
						$("#formTeamId").empty();
						$("#formTeamId").append("<option value='' selected='selected'>&nbsp;</option>");
						var listMatch = response[0];
						$('#listPartite').val(JSON.stringify(listMatch));
						$.each(listMatch, function (arrKey, arrValue) {
							if ($.inArray(arrValue.homeTeam, teams) === -1) {
								teams.push(arrValue.homeTeam);
								$("#formTeamId").append("<option value='" + arrValue.homeTeamId + "'> " + arrValue.homeTeam + "</option>");
							}
							if ($.inArray(arrValue.awayTeam, teams) === -1) {
								teams.push(arrValue.awayTeam);
								$("#formTeamId").append("<option value='" + arrValue.awayTeamId + "'> " + arrValue.awayTeam + "</option>");
							}
							$("#selectPartita").append("<option value='" + arrValue.idFixture + "'> " + arrValue.homeTeam + "-" + arrValue.awayTeam + "</option>");
						});
						$("#selectPartita").multiselect("refresh");

						var mapPlayers = JSON.stringify(response[1]);
						$('#mapPlayers').val(mapPlayers);

						$('#formTeamId option[value="' + teamSelected + '"]').attr("selected", "selected");

						return false;
					}
				});
			}

			function jsListCompetition() {
				$.ajax({
					type: "GET",
					url: "/sicstv/user/compFun.htm",
					cache: false,
					data: encodeURI("formCompetitionId=" + $("#formCompetitionId option:selected").val()),
					success: function (msg) {
						$("#formTeamId").html(msg);
						return false;
					}
				});
			}
		</script>

	</head>
	<body>
		<input type="hidden" id="contextPath" name="contextPath" value="sicstv"/>
		<input type="hidden" id="mapFasce" name="mapFasce" value="${mMapFasce}"/>
		<input type="hidden" id="mapIntervalli" name="mapIntervalli" value="${mMapIntervalli}"/>
		<input type="hidden" id="mapPlayers" name="mapPlayers" value="${mMapPlayers}"/>
		<input type="hidden" id="listPartite" name="listPartite" value="${mListPartite}"/>
		<div id="matchFilter">
			<p>
				<spring:message code="lib.stagione"/>&nbsp;<select id="formSeasonId" class="ui-state-default ui-corner-all inputSpacer" onchange="jsChangeSeason();">
					<option value=''>&nbsp;</option>
					<c:forEach var="item" items="${formSeasonAll}">
						<option value='${item.id}'  selected='selected'>${item.name}</option>
					</c:forEach>

				</select>
				<spring:message code="lib.campionato"/>&nbsp;<select id="formCompetitionId" class="ui-state-default ui-corner-all inputSpacer4" onchange="jsCampionato();">
					<option value='' selected='selected'>&nbsp;</option>
					<c:forEach var="item" items="${formCompetitionAll}">
						<option value='${item.id}'>${item.name}</option>
					</c:forEach>
				</select>
				<!--filtro per squadra SOLO SE collegato con svs, nella versione aia è sostituito dal nominativo dell'arbitro/assistente-->
				<c:if test="${sicstv=='sicstv'}"><spring:message code="lib.squadra"/>&nbsp;<select id="formTeamId" class="inputSpacer5 ui-state-default ui-corner-all" onchange="jsTeam();"></c:if>
						<option value='' selected='selected'>&nbsp;</option>
					<c:forEach var="item" items="${formTeamAll}">
						<option value='${item.id}'>${item.name}</option>
					</c:forEach>
				</select>


				<spring:message code="lib.giornata"/>&nbsp;<select id="formMatchdayId" class="ui-state-default ui-corner-all inputSpacer2" onchange="jsListGames();">
					<option value='' selected='selected'>&nbsp;</option>

					<!-- Itera fino la massimo numero di partite con il video   -->
					<c:forEach var="item" begin="1" end="${formMatchdays}">
						<option value='${item}'>${item}</option>
					</c:forEach>
				</select>
				<c:if test="${sicstv=='aia'}"><spring:message code="lib.arbitroAssistente"/>&nbsp;<input class="ui-state-default ui-corner-all inputSpacer3" id="formReferee" type="text" onchange="jsListGames();"/></c:if>
				&nbsp;<spring:message code="lib.dataDa"/>&nbsp;<input id="formDateFrom" type="text" value="${formDateFrom}" class="ui-state-default ui-corner-all inputSpacer3" onchange="jsListGames();"/><input id="datepickerFrom" type="hidden"/>
				&nbsp;<spring:message code="lib.dataA"/>&nbsp;<input id="formDateTo" type="text" value="${formDateTo}" class="ui-state-default ui-corner-all inputSpacer3" onchange="jsListGames();"/><input id="datepickerTo" type="hidden"/>
			</p>
		</div>

		<div id="playersChoice">
			<!--	<div class="sezioneAnalisi">
					<p id="sceltaPartita"> Scelta partita:
						<select id="selectPartita" onchange="addPartita();" multiple>
						</select>
					</p>
					
				</div> -->
			<div id="partiteSelezionate"></div>
			<div class="sezioneAnalisi">
				<div id="filtriGiocatoriSelezionati"></div>
				<span id="sceltaPartita" class="lblScelta"><span> Scelta partita:
						<select id="selectPartita" onchange="addPartita();" multiple>
						</select>
					</span>
					<span>
						Scelta squadra:<select id="selectSquadra" class="ui-state-default ui-corner-all inputSpacer" onchange="fillPlayers();">
						</select>
					</span>
				</span>


				<span class="sceltaAnalisi">
					<span class="checkboxPlayer lblScelta"> Scelta giocatore:</span>
					<div id="sceltaGiocatori"></div>
				</span>
				<span class="sceltaAnalisi" id="aggregati">
					<span class="checkboxPlayer titleLabel lblScelta"> Aggregati:</span>
					<span class="checkboxPlayer"><input type="checkbox" id='aggSqu' name="checkAS" value="TEAM" onclick="togglePlayer('aggSqu');"/> SQUADRA</span>
					<span class="checkboxPlayer"><input type="checkbox" id='aggPor' name="checkAP" value="POR" onclick="togglePlayer('aggPor');"/> PORTIERE</span>
					<span class="checkboxPlayer"><input type="checkbox" id='aggDif' name="checkAD" value="DIF" onclick="togglePlayer('aggDif');"/> DIFESA</span>
					<span class="checkboxPlayer"><input type="checkbox" id='aggCen' name="checkAC" value="CEN" onclick="togglePlayer('aggCen');"/> CENTROCAMPO</span>
					<span class="checkboxPlayer"><input type="checkbox" id='aggAtt' name="checkAA" value="ATT" onclick="togglePlayer('aggAtt');"/> ATTACCO</span>
				</span>

			</div>
			<span class="sceltaAnalisi" id="filtroIntervallo">
				<span class="checkboxPlayer titleLabel"> Intervallo:</span>
				<select id="tipoIntervalloFiltro" class="ui-state-default ui-corner-all inputSpacer" onchange="fillIntervalli();">
					<option value='match'>FULL MATCH</option>
					<option value='half'>HALF</option>
					<option value='15'>15</option>
					<option value='5'>5</option>
				</select>
				<input type="checkbox" value="average" id="checkMedia" name="checkMedia" > Average</input>
			</span>
		</div>

		<div id="filterChoice">
			Parameter: 
			<select id="parametriFiltro" class="ui-state-default ui-corner-all inputSpacer" onchange="fillFasce();">
				<option value=''>&nbsp;</option>
				<c:forEach var="item" items="${mMapParametri}">
					<option value='${item.key}'>${item.value}</option>
				</c:forEach>
			</select>
			Limit:
			<select id="fasceFiltro" class="ui-state-default ui-corner-all inputSpacer" onchange="setSpinner();">				
				<option value=''>&nbsp;</option>
			</select>
			Min:
			<input id="startFascia" class="spinnerFasce" type="number" min="0" value="0"/>
			Max:
			<input id="endFascia" class="spinnerFasce" type="number" min="0" value="0"/>
			Size:
			<select id="grandezzeFiltro" class="ui-state-default ui-corner-all inputSpacer" >
				<option value="dist">Distance</option>
				<option value="time">Time</option>
				<option value="occ">Occurence</option>
				<!--<option value="distEq">Equivalent Distance</option>
				<option value="distPer">Distance %</option>
				<option value="durPer">Time %</option>
				<option value="occPer">Occurence %</option>-->

			</select>


			<!--<select id="tipoIntervalloFiltro" onchange="fillIntervalli();">
				<option value=''>&nbsp;</option>
			<c:forEach var="item" items="${mListTipiIntervallo}">
				<option value='${item}'>${item}</option>
			</c:forEach>
		</select>
		<select id="intervalloFiltro">
			<option value=''>&nbsp;</option>
		</select>
		<select id="operazioniFiltro">
			<option value="tot">Totale</option>
			<option value="media">Media</option>
		</select> -->
			<button id="createButton" name="Create" value="Create" style='width: 100px; height: 20px' onclick="createButton();">Create</button>
		</div>


		<div id="dialog" > <!-- title="Tab data"-->
			<form>
				<fieldset class="ui-helper-reset">
					<label for="tab_title">Title</label>
					<input type="text" name="tab_title" id="tab_title" value="Tab Title" class="ui-widget-content ui-corner-all">
						<!-- <label for="tab_content">Content</label>
						<textarea name="tab_content" id="tab_content" class="ui-widget-content ui-corner-all">Tab content</textarea> -->
				</fieldset>
			</form>
		</div>


		<div id="tabsContainer">
			<button id="add_tab">Add Tab</button>
			<div id="tabs">
				${mTabsContent}
				<!--	<ul>
						<li><a href="#tabs-1">Prima</a> <span class="ui-icon ui-icon-close" role="presentation">Remove Tab</span></li>
						<li><a href="#tabs-2">Seconda</a> <span class="ui-icon ui-icon-close" role="presentation">Remove Tab</span></li>
						<li><a href="#tabs-3">Terza</a> <span class="ui-icon ui-icon-close" role="presentation">Remove Tab</span></li>
					</ul>
					<div class="tabsContent" id="tabs-1">
					</div>
					<div class="tabsContent" id="tabs-2">
					</div>
					<div class="tabsContent" id="tabs-3">
					</div> -->
			</div>
		</div>
		<div id="graphicDiv"><button id="expandGraphic" onclick="zoomGraphic();">Expand</button>	
			<div id="containerGrafico">

			</div>
			<div id="hiddenGraphic"></div>
		</div>
	</body>
</html>