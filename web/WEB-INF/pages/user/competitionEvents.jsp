<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

        <script type="text/javascript">
            function showCompetitionEvents(type, from, to) {
                var competitionId = new URL(document.URL).searchParams.get("formCompetitionId");
                if (typeof competitionId === 'undefined' || !competitionId || competitionId === null) {
                    competitionId = "";
                }

                if (competitionId) {
                    var groupId = new URL(document.URL).searchParams.get("groupId");
                    if (typeof groupId === 'undefined' || !groupId || groupId === null) {
                        groupId = "";
                    }
                    var teamId = $("#competitionTeam").val();
                    if (typeof teamId === 'undefined' || !teamId || teamId === null) {
                        teamId = "";
                    }
                    var playerId = $("#competitionTeamPlayer").val();
                    if (typeof playerId === 'undefined' || !playerId || playerId === null) {
                        playerId = "";
                    }

                    var strUrl = "/sicstv/user/video.htm?personal=${personalSource}&id=&idComp=" + competitionId + "&idTeam=" + teamId + "&idPlayer=" + playerId + "&goals=false&event=" + escape(type) + "&filter=&limit=50&gameIds=&groupId=" + groupId + "&from=" + from + "&to=" + to + "&startClip=true";
                    location.href = strUrl;
                } else {
                    UIkit.notify("Unable to find competitionId", {status: 'danger', timeout: 1000});
                }
            }

            function updateCompetitionEvents(teamChanged) {
                // check if valid requests -> max 1 month
                var dateString = $("#dateFrom").val();
                var parts = dateString.split('/');
                var from = new Date(parts[2], parts[1] - 1, parts[0]);

                dateString = $("#dateTo").val();
                parts = dateString.split('/');
                var to = new Date(parts[2], parts[1] - 1, parts[0]);


                var diffTime = to - from;
                var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                if (diffDays <= 31 && diffDays > 0) {
                    jsShowBlockUI();
                    loadEventsTab(true, teamChanged);
                } else {
                    UIkit.notify("<spring:message code='competition.events.days.warning'/>", {status: 'danger', timeout: 1000});
                }
            }
        </script>
    </head>
    <body>
        <div class="uk-width-10-10 uk-margin-small-left uk-padding-top" id="eventsTabLiContent">
            <div>

                <span class="uk-form">
                    <label class="uk-form-label" for="dateFrom"><spring:message code='lib.dataDa'/></label>
                    <input onchange="updateCompetitionEvents();" type="text" id="dateFrom" data-uk-datepicker="{format:'DD/MM/YYYY'}" value="${mFrom}">
                </span>

                <span class="uk-form uk-margin-small-left">
                    <label class="uk-form-label" for="dateTo"><spring:message code='lib.dataA'/></label>
                    <input onchange="updateCompetitionEvents();" type="text" id="dateTo" data-uk-datepicker="{format:'DD/MM/YYYY'}" value="${mTo}">
                </span>

                <c:if test="${mTeams != null}">
                    <span class="uk-form uk-margin-small-left">
                        <spring:message code='lib.squadra'/>: 
                        <select id="competitionTeam" onchange="updateCompetitionEvents(true);">
                            <option value=""></option>
                            <c:forEach var="item" items="${mTeams}">
                                <option value="${item.id}" <c:if test="${mSelectedTeam != null && mSelectedTeam == item.id}">selected</c:if>>${item.name}</option>
                            </c:forEach>
                        </select>
                    </span>
                </c:if>

                <c:if test="${mPlayers != null}">
                    <span class="uk-form uk-margin-small-left">
                        <spring:message code='video.giocatore'/>: 
                        <select id="competitionTeamPlayer" onchange="updateCompetitionEvents();">
                            <option value=""></option>
                            <c:forEach var="map" items="${mPlayers}">
                                <c:choose>
                                    <c:when test="${map.key == 'P'}">
                                        <option value="" style="font-weight: bold; text-transform: uppercase" disabled><spring:message code="ruoli.portieri"/></option>
                                    </c:when>
                                    <c:when test="${map.key == 'D'}">
                                        <option value="" style="font-weight: bold; text-transform: uppercase" disabled><spring:message code="ruoli.difensori"/></option>
                                    </c:when>
                                    <c:when test="${map.key == 'C' }">
                                        <option value="" style="font-weight: bold; text-transform: uppercase" disabled><spring:message code="ruoli.centrocampisti"/></option>
                                    </c:when>
                                    <c:when test="${map.key == 'A'}">
                                        <option value="" style="font-weight: bold; text-transform: uppercase" disabled><spring:message code="ruoli.attaccanti"/></option>
                                    </c:when>
                                </c:choose>
                                <c:forEach var="item" items="${map.value}">
                                    <option value="${item.id}" <c:if test="${mSelectedPlayer != null && mSelectedPlayer == item.id}">selected</c:if>>${item.first_name} ${item.last_name} (${item.numero})</option>
                                </c:forEach>
                            </c:forEach>
                        </select>
                    </span>
                </c:if>
            </div>

            <div class="filterSicsDiv uk-padding-top" id="filterSicsSearch">
                <div class="uk-float-left panelSicsDiv">
                    <c:forEach items="${mFilterToShow['SICS'][0]}" var="btn">
                        <button class='uk-button teamFilterButton' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="showCompetitionEvents('SICS-_-${btn.key}', '${mFromDB}', '${mToDB}');" title="<spring:message code='${btn.value[0]}'/>"><i class="uk-icon-video-camera" ></i><spring:message code="${btn.value[0]}"/> (${btn.value[2]})</button>
                    </c:forEach>
                </div>
                <div class="uk-float-right panelSicsDiv uk-padding-right">
                    <c:forEach items="${mFilterToShow['SICS'][1]}" var="btn">
                        <button class='uk-button teamFilterButton' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="showCompetitionEvents('SICS-_-${btn.key}', '${mFromDB}', '${mToDB}');" title="<spring:message code='${btn.value[0]}'/>"><i class="uk-icon-video-camera" ></i><spring:message code="${btn.value[0]}"/> (${btn.value[2]})</button>
                    </c:forEach>
                </div>
            </div>
        </div>
    </body>
</html>
