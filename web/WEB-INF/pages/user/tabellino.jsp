<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div>
    <button onclick="printElement(document.getElementById('containerTabellino')); window.print();" class="uk-button uk-float-right uk-margin-small-bottom">PRINT</button>
</div>
<div id="containerTabellino">
    <input type="hidden" id="mTabellinoGameId" value="${mGameInfo.id}"/>

    <div id="risultatoTabellino" class="uk-float-left" style="width: 100%">
        <span id="spanHomeScore" class="spanScore">
            <table style="width: 100%">
                <tr>
                    <td class="uk-width-2-10"> <img id="logoTeamHome"  src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeamH.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${mTeamH.name}"/></td>
                    <td class="uk-width-2-10"><label id="nameHome">${mGameInfo.homeTeam}</label></td>
                    <td class="uk-width-1-10"><label id="scoreHome" class="tabellino-score"s>${mGameInfo.homeTeamScore}</label></td>
                    <td class="uk-width-1-10"><label id="scoreAway" class="tabellino-score">${mGameInfo.awayTeamScore}</label></td>
                    <td class="uk-width-2-10"><label id="nameAway">${mGameInfo.awayTeam}</label></td>
                    <td class="uk-width-2-10"><img id="logoTeamAway" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeamA.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${mTeamA.name}"/></td>
                </tr>
                <tr>
                    <td></td>
                    <td class="tdScore" colspan="2">
                        <span class="uk-width-3-10">
                            <c:forEach var="item" items="${mapReti}">
                                <c:set var="chiave" value="${item.key}"/>
                                <c:if test="${chiave == mGameInfo.homeTeam}">
                                    <c:forEach var="azione" items="${item.value}">
                                        <label class="playerScore">${azione.getPeriod_start_minute()}' ${azione.mHalf}t ${azione.getPlayerTabellino()} </label>
                                    </c:forEach>
                                </c:if>
                            </c:forEach>
                        </span>
                    </td>
                    <td></td>
                    <td class="tdScore" colspan="2">
                        <span class="uk-width-3-10">
                            <c:forEach var="item" items="${mapReti}">
                                <c:set var="chiave" value="${item.key}"/>
                                <c:if test="${chiave == mGameInfo.awayTeam}">
                                    <c:forEach var="azione" items="${item.value}">
                                        <label class="playerScore">${azione.getPeriod_start_minute()}' ${azione.mHalf}t ${azione.getPlayerTabellino()}  </label>
                                    </c:forEach>
                                </c:if>
                            </c:forEach>
                        </span>
                    </td>
                </tr>
            </table>
        </span>
    </div>

    <div>
        <c:if test="${mGameInfo.isDrawCampoTabellino()}">
            <img id="imgModuli" src="/sicstv/images/tabellino/${imgName}" alt="moduli" class="uk-align-center"/>
        </c:if>
    </div>

    <div id="divFormazioniTabellino" class="uk-width-1-1 uk-display-inline-block">
        <div class="uk-grid">
            <div id="divFormazioniHome" class="uk-width-4-10 formazioniTabellino uk-container-center">
                <label>${mGameInfo.homeTeam} (${mGameInfo.homeModule})</label>
                <table class="uk-table uk-margin-right">
                    <tbody>
                        <c:set var="iterator" value="0"/>
                        <c:set var="style" value=""/>
                        <c:forEach var="player" items="${mFormazioneH}">
                            <tr>
                                <c:if test="${iterator == 10}">
                                    <c:set var="style" value="style='border-bottom: 1px solid #FF6600;'"/>
                                </c:if>
                                <td ${style} class="uk-width-1-10 tdTabellino"><label>${player.matchNumber} </label></td>
                                <td ${style} class="uk-width-1-10 tdTabellino"><label>${player.ruolo} </label></td>
                                <td ${style} class="tdTabellino"><label>${player.getKnown_name()} (${player.getBornDateYearLast2Digit()})</label></td>
                                <td ${style} class="tdTabellino">
                                    <c:if test="${player.sub > -1}">
                                        ${player.sub}' <img src="/sicstv/images/sub.png" alt="subentro" />
                                    </c:if>

                                    <c:forEach items="${player.ammonito}" var="amm">
                                        ${amm}' <img src="/sicstv/images/giallo.png" alt="ammonizione" />
                                    </c:forEach>

                                    <c:if test="${player.espulso > -1}">
                                        ${player.espulso}' <img src="/sicstv/images/rosso.png" alt="espulsione" />
                                    </c:if>

                                    <c:forEach begin="1" end="${player.reti}">
                                        <img src="/sicstv/images/pallone.png" alt="goal" />
                                    </c:forEach>

                                    <c:forEach begin="1" end="${player.autorete}">
                                        <img src="/sicstv/images/autorete.png" alt="goal" />
                                    </c:forEach>

                                    <c:if test="${player.sos > -1}">
                                        ${player.sos}' <img src="/sicstv/images/sos.png" alt="sostituzione" />
                                    </c:if>

                                </td>
                            </tr>
                            <c:set var="style" value=""/>
                            <c:set var="iterator" value="${iterator+1}"/>
                        </c:forEach>
                    </tbody>
                </table>
            </div>

            <div id="divFormazioniAway" class="uk-width-4-10 formazioniTabellino uk-container-center">
                <label>${mGameInfo.awayTeam} (${mGameInfo.awayModule})</label>
                <table class="uk-table">
                    <tbody>
                        <c:set var="iterator" value="0"/>
                        <c:set var="style" value=""/>
                        <c:forEach var="player" items="${mFormazioneA}">
                            <tr>
                                <c:if test="${iterator == 10}">
                                    <c:set var="style" value="style='border-bottom: 1px solid #FF6600;'"/>
                                </c:if>
                                <td ${style} class="uk-width-1-10 tdTabellino"><label>${player.numero} </label></td>
                                <td ${style} class="uk-width-1-10 tdTabellino"><label>${player.ruolo} </label></td>
                                <td ${style} class="tdTabellino"><label>${player.getKnown_name()} (${player.getBornDateYearLast2Digit()})</label></td>
                                <td ${style} class="tdTabellino">

                                    <c:if test="${player.sub > -1}">
                                        ${player.sub}' <img src="/sicstv/images/sub.png" alt="subentro" />
                                    </c:if>

                                    <c:forEach items="${player.ammonito}" var="amm">
                                        ${amm}' <img src="/sicstv/images/giallo.png" alt="ammonizione" />
                                    </c:forEach>

                                    <c:if test="${player.espulso > -1}">
                                        ${player.espulso}' <img src="/sicstv/images/rosso.png" alt="espulsione" />
                                    </c:if>

                                    <c:forEach begin="1" end="${player.reti}">
                                        <img src="/sicstv/images/pallone.png" alt="goal" />
                                    </c:forEach>

                                    <c:forEach begin="1" end="${player.autorete}">
                                        <img src="/sicstv/images/autorete.png" alt="goal" />
                                    </c:forEach>

                                    <c:if test="${player.sos > -1}">
                                        ${player.sos}' <img src="/sicstv/images/sos.png" alt="sostituzione" />
                                    </c:if>

                                </td>
                            </tr>
                            <c:set var="style" value=""/>
                            <c:set var="iterator" value="${iterator+1}"/>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <c:if test="${not empty mGameInfo.refereeName}">
        <div id="arbitriTabellino">
            <span class="uk-text-center">
                <label id="arbitro" ><spring:message code="tabellino.arbitro"/>${mGameInfo.refereeName} | </label>
                <label id="1as" ><spring:message code="tabellino.1assistente"/>${mGameInfo.assistant1Name} | </label>
                <label id="2as" ><spring:message code="tabellino.2assistente"/>${mGameInfo.assistant2Name}</label>
            </span>
            <span class="uk-text-center">
                <label id="1asp" ><spring:message code="tabellino.1assistenteporta"/>${mGameInfo.assistantGoal1Name} | </label>
                <label id="2asp" ><spring:message code="tabellino.2assistenteporta"/>${mGameInfo.assistantGoal2Name} | </label>
                <label id="4uomo"><spring:message code="tabellino.quartouomo"/>${mGameInfo.assistant4Name}</label>
            </span>
        </div>
    </c:if>
    <span> </span>
</div>