<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div id="playlistTvListContainer">
    <style>
        /* Stili CSS per il layout */
        .playlistTv-container {
          display: flex;
          flex-direction: column;
        }

        .playlistTv-half {
          flex-basis: 50%;
        }

        .playlistTv-button {
          padding: 10px;
        }

        .playlistTv-table {
          width: 100%;
        }

        .playlistTv-th,
        .playlistTv-td {
          padding: 5px;
          border: 1px solid black;
        }
        
        .playlistTv-confirm {
          margin-top: 10px;
          display: flex;
          justify-content: flex-end;
        }
    </style>
    
    <script type="text/javascript">
        $(document).ready(function () {
            if (typeof showPlaylistDetails === 'function') {
                <c:if test="${mFirstPlayListId != null}">
                if (${mFirstPlayListId} !== null) {
                    showPlaylistDetails(${mFirstPlayListId});
                }
                </c:if>
            } else {
                // se sto caricando la tab "Playlist" allora evidenzio la riga della playlist che sto visualizzando
                var playlistTvId = $("#mPlaylistTvId");
                if (typeof playlistTvId !== 'undefined') {
                    $('#playlistTvRowId' + $("#mPlaylistTvId").val() + ' td:not(:first-child)').addClass("currentClipPlaylist");
                }
            }
            
            const buttons = document.querySelectorAll('.tippy-button');
            buttons.forEach((button) => {
                const tooltipContent = button.getAttribute('data-tooltip-content');

                tippy(button, {
                    theme: 'light',
                    content: tooltipContent,
                    allowHTML: true,
                    placement: 'bottom'
                });
            });
            
            // aggiorna la sezione "aggiungi a playlist esistente"
            updateVideoPage();
        });
        
        function getDateFormatted(id, stringDate) {            
            $("#playlistTvCreationDate" + id).html(getDateFormattedString(stringDate));
        }
        
        function getDateFormattedString(stringDate) {
            var date = new Date(stringDate);
            return date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear() + " " + date.getHours() + ":" + (date.getMinutes() > 9 ? date.getMinutes() : "0" + date.getMinutes());
        }
        
        function openModifyPlaylistTv(id) {
            if (typeof stopVideo === 'function') {
                stopVideo();
            }
            var namePlaylist = $("#playlistTvName" + id).text();
            var descriptionPlaylist = $("#playlistTvDescription" + id).text();
            
            $("#saveModifyPlaylist").html("<button style='min-width:80px;' onclick='updatePlaylistTv(" + id + ");' class='uk-text-large uk-text-bold uk-button js-modal-confirm'><spring:message code='playlist.save'/></button>");
            $("#menuDelete").addClass("uk-hidden");
            $("#modalModifyPlaylist").addClass("uk-open");
            $("#modalModifyPlaylist").removeClass("uk-hidden");
            $("#namePlaylistModify").val(namePlaylist);
            $("#descriptionPlaylistModify").val(descriptionPlaylist);
        }
        
        var userIdToShareWith = [];
        var playlistSharedWithUser = new Map();
        var currentPlaylistId;
        function openSharePlaylistTv(id) {
            if (typeof stopVideo === 'function') {
                stopVideo();
            }
            
            userIdToShareWith = [];
            currentPlaylistId = id;
            $("#menuDelete").addClass("uk-hidden");
            $("#modalSharePlaylist").addClass("uk-open");
            $("#modalSharePlaylist").removeClass("uk-hidden");
            
            var table = document.getElementById("addUserShareTable");
            while (table.rows.length > 1) {
                table.deleteRow(1);
            }
            table = document.getElementById("addUserAlreadySharedTable");
            while (table.rows.length > 1) {
                table.deleteRow(1);
            }
            
            <c:forEach var="user" items="${mUserToShareList}">
                userIdToShareWith.push("${user.id}");
            </c:forEach>
            // qua tolgo quelli a cui ho gi� condiviso
            <c:forEach var="item" items="${mPlaylistSharedWithUser}">
                if (parseInt(${item.key}) == id) { // ho trovato la playlist
                    <c:forEach var="playlist" items="${item.value}">
                        if (userIdToShareWith.includes("${playlist.user.id}")) {
                            userIdToShareWith.splice(userIdToShareWith.indexOf("${playlist.user.id}"), 1);
                        }
                    </c:forEach>
                }
            </c:forEach>
            // ora aggiungo nella prima tabella le persone a cui POSSO condividere
            <c:forEach var="user" items="${mUserToShareList}">
                if (userIdToShareWith.includes("${user.id}")) {
                    addUserToShareTable('${user.id}', '${user.lastName} ${user.firstName}', '${user.email}', false);
                }
            </c:forEach>
            // ora aggiungo nella seconda tabella le persone a cui HO GIA condiviso
            <c:forEach var="item" items="${mPlaylistSharedWithUser}">
                if (parseInt(${item.key}) == id) { // ho trovato la playlist
                    <c:forEach var="playlist" items="${item.value}">
                        addUserToAlreadySharedTable('${playlist.user.id}', '${playlist.user.lastName} ${playlist.user.firstName}', '${playlist.user.email}', ${playlist.editable});
                    </c:forEach>
                }
            </c:forEach>
            // ora aggiungo i tasti per la condivisione pubblica
            var isPublicShared = false;
            var click = 0, maxClick = 0;
            <c:forEach var="item" items="${mPlaylistAllPublicShare}">
                if (parseInt(${item.playlistId}) == id) { // ho trovato la playlist
                    isPublicShared = true;
                    click = parseInt(${item.click});
                    maxClick = parseInt(${item.maxClick});
                }
            </c:forEach>
            $("#publicShareDiv").html("");
            var container = document.getElementById("publicShareDiv");
            if (isPublicShared) {
                var generate = document.createElement("button");
                generate.setAttribute("onclick", "sharePlaylist(); removeSharePlaylistTv(" + id + ")");
                generate.style = "min-width: 80px;";
                generate.className = "uk-h4 uk-button uk-button-danger";
                generate.innerText = "<spring:message code="playlist.public.share.cancel"/>";
                container.appendChild(generate);
                
                var copyLink = document.createElement("button");
                copyLink.setAttribute("onclick", "getLinkSharePlaylistTv(" + id + ")");
                copyLink.style = "min-width: 80px; margin-left: 3px";
                copyLink.className = "uk-h4 uk-button";
                copyLink.innerText = "<spring:message code="playlist.public.share.copyLink"/>";
                container.appendChild(copyLink);
                
                var counter = document.createElement("span");
                counter.style = "margin-left: 25px;";
                var label = "<spring:message code="playlist.public.share.click.label"/>";
                label = label.replace("X", click);
                label = label.replace("Y", maxClick);
                counter.innerText = label;
                container.appendChild(counter);
                
                var reset = document.createElement("button");
                reset.setAttribute("onclick", "resetSharePlaylistTv(" + id + ")");
                reset.style = "min-width: 80px; margin-left: 3px";
                reset.className = "uk-h4 uk-button uk-float-right";
                reset.innerText = "<spring:message code="playlist.public.share.reset"/>";
                container.appendChild(reset);
            } else {
                var generate = document.createElement("button");
                generate.setAttribute("onclick", "sharePlaylist(); sharePlaylistTv(" + id + ");");
                generate.className = "uk-h4 uk-button uk-button-primary";
                generate.innerText = "<spring:message code="playlist.public.share.public"/>";
                container.appendChild(generate);
            }
        }
        
        function sharePlaylistTv(id) {
            $.ajax({
                type: "GET",
                url: "/sicstv/user/publicSharePlaylistTv.htm",
                cache: false,
                data: encodeURI("&id=" + id),
                success: function (msg) {
                    if (msg === "true") {
                        UIkit.notify("<spring:message code="playlist.share.success"/>", {status: 'success', timeout: 1000});
                        setTimeout(function() {
                            jsShowBlockUI();
                            reloadPage(true);
                            getLinkSharePlaylistTv(currentPlaylistId);
                            $.unblockUI();
                        }, 1000);
                    } else if (msg === "alreadyShared") {
                        UIkit.notify("<spring:message code="playlist.public.already.shared"/>", {status: 'danger', timeout: 1000});
                    } else {
                        UIkit.notify("<spring:message code="playlist.share.error"/>", {status: 'danger', timeout: 1000});
                    }
                },
                error: function () {
                    UIkit.notify("<spring:message code="playlist.share.error"/>", {status: 'danger', timeout: 1000});
                }
            });
        }
        
        function removeSharePlaylistTv(id) {
            $.ajax({
                type: "GET",
                url: "/sicstv/user/removePublicSharePlaylistTv.htm",
                cache: false,
                data: encodeURI("&id=" + id),
                success: function (msg) {
                    if (msg === "true") {
                        UIkit.notify("<spring:message code="playlist.public.share.cancel.success"/>", {status: 'success', timeout: 1000});
                        setTimeout(function() {
                            jsShowBlockUI();
                            reloadPage(true);
                            $.unblockUI();
                        }, 1000);
                    } else {
                        UIkit.notify("<spring:message code="playlist.public.share.cancel.error"/>", {status: 'danger', timeout: 1000});
                    }
                },
                error: function () {
                    UIkit.notify("<spring:message code="playlist.public.share.cancel.error"/>", {status: 'danger', timeout: 1000});
                }
            });
        }
        
        function resetSharePlaylistTv(id) {
            $.ajax({
                type: "GET",
                url: "/sicstv/user/resetPublicSharePlaylistTv.htm",
                cache: false,
                data: encodeURI("&id=" + id),
                success: function (msg) {
                    if (msg === "true") {
                        UIkit.notify("<spring:message code="playlist.public.share.reset.success"/>", {status: 'success', timeout: 1000});
                        setTimeout(function() {
                            jsShowBlockUI();
                            reloadPage(true);
                            $.unblockUI();
                        }, 1000);
                    } else {
                        UIkit.notify("<spring:message code="playlist.public.share.reset.error"/>", {status: 'danger', timeout: 1000});
                    }
                },
                error: function () {
                    UIkit.notify("<spring:message code="playlist.public.share.reset.error"/>", {status: 'danger', timeout: 1000});
                }
            });
        }
        
        function getLinkSharePlaylistTv(id) {
            $.ajax({
                type: "GET",
                url: "/sicstv/user/getLinkSharePlaylistTv.htm",
                cache: false,
                data: encodeURI("&id=" + id),
                success: function (msg) {
                    if (msg === "false") {
                        UIkit.notify("<spring:message code="playlist.public.share.error"/>", {status: 'success', timeout: 1000});
                    } else {
                        copyToClipboardPlaylist(window.location.origin + msg);
                    }
                },
                error: function () {
                    UIkit.notify("<spring:message code="playlist.public.share.error"/>", {status: 'danger', timeout: 1000});
                }
            });
        }
        
        const fallbackCopyTextToClipboardPlaylist = str => {
            var textArea = document.createElement("textarea");
            textArea.value = str;

            // Avoid scrolling to bottom
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                var successful = document.execCommand('copy');
                var msg = successful ? 'successful' : 'unsuccessful';
                UIkit.notify("<spring:message code="menu.user.linkCopiato"/>", {status: 'success', timeout: 1000});
            } catch (err) {
                UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger', timeout: 1000});
                return Promise.reject('The Clipboard API is not available.');
            }
            document.body.removeChild(textArea);
        }
        const copyToClipboardPlaylist = str => {
            if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
                UIkit.notify("<spring:message code="menu.user.linkCopiato"/>", {status: 'success', timeout: 1000});
                return navigator.clipboard.writeText(str);
            } else {
                fallbackCopyTextToClipboardPlaylist(str);
            }
        };
        
        function addUserToShareTable(id, name, email, editable) {
            var table = document.getElementById("addUserShareTable");
            var row = table.insertRow(table.rows.length);
            row.className = "uk-table-middle uk-margin-large-bottom uk-margin-large-top " + id;
            var cell1 = row.insertCell(0);
            var cell2 = row.insertCell(1);
            var cell3 = row.insertCell(2);
            cell1.className = "uk-text-center matchesColumnResult1 clickRow uk-table-middle uk-margin-large-bottom uk-margin-large-top";
            cell1.innerHTML = '<button onclick="deleteTableRow(\'addUserShareTable\', ' + id + '); addUserToAlreadySharedTable(' + id + ', \'' + name + '\', \'' + email + '\', ' + editable + ')" class="uk-icon-hover uk-button uk-button-mini" title="<spring:message code="playlist.share.remove"/>"><i class="uk-icon-hover uk-icon-plus"></i></button>';
            cell2.innerHTML = name;
            cell3.innerHTML = email;
        }
        
        function addUserToAlreadySharedTable(id, name, email, editable) {
            var table = document.getElementById("addUserAlreadySharedTable");
            var row = table.insertRow(table.rows.length);
            row.className = "uk-table-middle uk-margin-large-bottom uk-margin-large-top " + id;
            row.setAttribute('userid', id);
            var cell1 = row.insertCell(0);
            var cell2 = row.insertCell(1);
            var cell3 = row.insertCell(2);
            var cell4 = row.insertCell(3);
            cell1.className = "uk-text-center matchesColumnResult1 clickRow uk-table-middle uk-margin-large-bottom uk-margin-large-top";
            cell1.innerHTML = '<button onclick="deleteTableRow(\'addUserAlreadySharedTable\', ' + id + '); addUserToShareTable(' + id + ', \'' + name + '\', \'' + email + '\', ' + editable + ')" class="uk-icon-hover uk-button uk-button-mini" title="<spring:message code="playlist.share.remove"/>"><i class="uk-icon-hover uk-icon-minus"></i></button>';
            cell2.innerHTML = name;
            cell3.innerHTML = email;
            cell4.innerHTML = '<span class="uk-form"><select class="canEdit' + id + '"><option value="view" ' + (editable ? 'selected="selected"' : '') + '><i class="uk-icon-eye uk-margin-right"></i><spring:message code="tooltip.visualizza"/></option><option value="edit" ' + (editable ? 'selected="selected"' : '') + '><i class="uk-icon-pencil uk-margin-right"></i><spring:message code="menu.modify"/></option></select></span>';
        }
        
        function deleteTableRow(tableId, userId) {
            var table = document.getElementById(tableId);
            $('.' + userId).remove();
        }
        
        function sharePlaylist() {
            var userIdList = [];
            $('#addUserAlreadySharedTable tr').each(function() {
                var userId = $(this).attr("userid")
                if (typeof userId !== 'undefined') { // l'header non ha l'attributo
                    userIdList.push(userId + ";" + ($('.canEdit' + userId).val() === 'edit'));
                }
            });
            
            $.ajax({
                type: "GET",
                url: "/sicstv/user/sharePlaylistTv.htm",
                cache: false,
                data: encodeURI("&id=" + currentPlaylistId + "&userIds=" + userIdList),
                success: function (msg) {
                    if (msg === "true") {
                        UIkit.notify("<spring:message code="playlist.share.success"/>", {status: 'success', timeout: 1000});
                        setTimeout(function() {
                            jsShowBlockUI();
                            reloadPage(true);
                            $.unblockUI();
                        }, 1000);
                    } else {
                        UIkit.notify("<spring:message code="playlist.share.error"/>", {status: 'danger', timeout: 1000});
                    }
                },
                error: function () {
                    UIkit.notify("<spring:message code="playlist.share.error"/>", {status: 'danger', timeout: 1000});
                }
            });
        }

        function updatePlaylistTv(id) {
            var nameValue = $("#namePlaylistModify").val();
            var descriptionValue = $("#descriptionPlaylistModify").val();
            if (typeof nameValue !== 'undefined' && typeof descriptionValue !== 'undefined') {
                descriptionValue = descriptionValue.trim();
                nameValue = nameValue.trim();
                if (nameValue !== '') {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/updatePlaylistTv.htm",
                        cache: false,
                        data: encodeURI("&id=" + id + "&name=" + nameValue + "&description=" + descriptionValue),
                        success: function (msg) {
                            if (msg === "true") {
                                if (typeof jsRefreshMatches === "function") {
                                    jsRefreshMatches('1'); // aggiorno pagina
                                    UIkit.notify("<spring:message code="playlist.update.success"/>", {status: 'success', timeout: 1000});
                                } else {
                                    UIkit.notify("<spring:message code="playlist.update.success"/>", {status: 'success', timeout: 1000});
                                    jsShowBlockUI();
                                    reloadPage(false);
                                    $.unblockUI();
                                }
                            } else {
                                UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                            }
                        },
                        error: function () {
                            UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                        }
                    });
                } else {
                    UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                }
            } else {
                UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
            }
        }

        function deletePlaylistTv(id) {
            if (window.confirm("<spring:message code="playlist.permanent.delete.question"/>")) {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/eliminaPlaylistTv.htm",
                    data: encodeURI("id=" + id),
                    cache: false,
                    success: function (result) {
                        if (result === 'true') {
                            if (typeof jsRefreshMatches === "function") {
                                UIkit.notify("<spring:message code='playlist.delete.success'/>", {status: 'success', timeout: 1000});
                                jsRefreshMatches('1'); // aggiorno pagina
                            } else {
                                UIkit.notify("<spring:message code='playlist.delete.success'/>", {status: 'success', timeout: 1000});
                                jsShowBlockUI();
                                reloadPage(false);
                                $.unblockUI();
                            }
                        } else {
                            UIkit.notify("<spring:message code='playlist.delete.failed'/>", {status: 'danger', timeout: 1000});
                        }
                    },
                    error: function () {
                        UIkit.notify("<spring:message code='playlist.delete.failed'/>", {status: 'danger', timeout: 1000});
                    }
                });
            }
        }

        function downloadPlaylistTv(id, multi) {
            if (typeof multi === 'undefined') {
                multi = false;
            }
            UIkit.notify("<spring:message code='menu.user.export.convalidazione'/>...", {status: 'success', timeout: 0});
            $.ajax({
                type: "GET",
                url: "/sicstv/user/esportaAzioniPlaylistTv.htm",
                data: encodeURI("playlistId=" + id + "&multi=" + multi + "&askedMaxQuality=false"),
                cache: false,
                success: function (id) {
                    UIkit.notify.closeAll();
                    if (id !== "" && id !== "tooMany" && id !== "limitReached") {
//                        UIkit.notify("<spring:message code='playlist.download.start'/>", {status: 'success', timeout: 1000});
                        if (typeof timerProgressStatus !== 'undefined') {
                            timerProgressStatus.stop();
                        }
                        if (typeof sessionStorage['exportIdentifier'] === 'undefined') {
                            sessionStorage['exportIdentifier'] = "";
                        }

                        sessionStorage['exportIdentifier'] = sessionStorage['exportIdentifier'] + "-" + id;
                        timerProgressStatus = $.timer(function () {
                            if (!${mExportUseLambda}) {
                                $.ajax({
                                    type: "GET",
                                    url: "/sicstv/user/isExportRunning.htm",
                                    data: encodeURI("identifier=" + sessionStorage['exportIdentifier']),
                                    cache: false,
                                    success: function (msg) {
                                        if (msg === "true") {
                                            $("#exportRunningNotify").removeClass("uk-hidden");
                                        } else {
                                            $("#exportRunningNotify").addClass("uk-hidden");
                                            updateUnseenPlaylist();
                                            timerProgressStatus.stop();
    //                                        UIkit.notify("<spring:message code='playlist.download.end'/>", {status: 'success', timeout: 1000});

                                            if (msg !== "false" && typeof sessionStorage['exportIdentifier'] !== "undefined") {
                                                var strUrl = "/sicstv/user/downloadPlaylist.htm?id=" + msg;
                                                $.ajax({
                                                    type: "GET",
                                                    url: strUrl,
                                                    contentType: "application/force-download",
                                                    cache: false,
                                                    success: function (msg) {
                                                        if (msg.substr(0, 4) === "true") {
                                                            UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                                                        } else {
                                                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                                                            window.location.replace(msg.substr(5));
                                                            $.unblockUI();
                                                        }
                                                    }
                                                });
                                                sessionStorage.removeItem('exportIdentifier');
                                            }
                                        }
                                    }
                                });
                            } else {
                                $.ajax({
                                    type: "GET",
                                    url: "/sicstv/user/isExportRunningLambda.htm",
                                    cache: false,
                                    success: function (exportInfo) {
                                        UIkit.notify.closeAll();
                                        if (exportInfo.trim().length > 0) {
                                            var scriptToExecute = exportInfo.substring(exportInfo.indexOf('// START') + 8, exportInfo.indexOf('// END')).trim();
                                            if (scriptToExecute && scriptToExecute.length > 0) {
                                                eval(scriptToExecute);
                                            }
                                            
                                            $("#exportRunningNotify").removeClass("uk-hidden");
                                            if (typeof exportTippyElement === 'undefined') {
                                                exportTippyElement = tippy("#exportRunningNotify", {
                                                    theme: 'light-border',
                                                    content: exportInfo,
                                                    maxWidth: 500,
                                                    allowHTML: true,
                                                    placement: 'bottom'
                                                });
                                            } else {
                                                exportTippyElement.forEach(function(element) {
                                                    element.setContent(exportInfo);
                                                });
                                            }
                                        } else {
                                            $("#exportRunningNotify").addClass("uk-hidden");
                                            updateUnseenPlaylist();
                                            timerProgressStatus.stop();
                                        }
                                    }
                                });
                            }
                        });
                        timerProgressStatus.set({time: 2000, autostart: true});
                    } else if (id === "tooMany") {
                        UIkit.notify("<spring:message code='playlist.download.limit.reached'/>.<br><spring:message code='playlist.download.limit.reached.solution'/>", {status: 'danger', timeout: 2500});
                    } else if (id === "limitReached") {
                        UIkit.notify("<spring:message code='playlist.export.limit.reached'/>", {status: 'danger', timeout: 2500});
                    }
                },
                async: true
            });
        }
        
        function clipAmountOnClick(id) {
            if (!new URL(document.URL).pathname.includes("/video.htm") && !new URL(document.URL).pathname.includes("/videoPlaylistTv.htm")) { // per la pagina video non faccio nulla
                if (typeof showPlaylistDetails === 'function') {
                    showPlaylistDetails(id);
                } else {
                    location.href = "/sicstv/user/videoPlaylistTv.htm?playlistTvId=" + id;
                }
            }
        }
        
        function reloadPage(openShare) {
            // http://*************:8084/sicstv/user/playlistTv.htm?isMySicsTvPage=true
            
            // NB: IL .load() RICARICA LE VARIABILI DI SPRING
            // USANDO INVECE LA CHIAMATA AJAX E IL .html() PER AGGIORNARE, LE VARIABILI NON VENGONO AGGIORNATE
            $('#playlistTv > *').load(new URL(document.URL).origin + "/sicstv/user/playlistTv.htm?isMySicsTvPage=${mIsMySicsTvPage}&currentPlaylistId=" + currentPlaylistId, function(response, status, xhr) {
                if (status === 'error') {
                  console.log(xhr.status + ' ' + xhr.statusText);
                }
                
                if (typeof openShare !== 'undefined' && openShare) {
                    clipAmountOnClick(currentPlaylistId);
                    openSharePlaylistTv(currentPlaylistId);
                }
                
                if (typeof showPlaylistDetails === 'function') {
                    showPlaylistDetails(currentPlaylistId);
                }
                
                // ricarico la tabella giusta se mi trovo nella pagina myplaylisttv.jsp
                var tableIdToEnable = "azioniPlaylist" + currentPlaylistId;
                document.querySelectorAll(".playlistTvDetails").forEach(function (element) {
                    var tmpId = element.id;
                    if (tmpId !== tableIdToEnable) {
                        $('#' + tmpId).addClass("uk-hidden");
                    } else {
                        $('#' + tmpId).removeClass("uk-hidden");
                    }
                });
            });
        }
        
        function updateVideoPage() {
            if ($("#playlistTvButtonLi").length > 0) {
                var content = '<table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse">\n\
                <tbody>\n\
                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">\n\
                        <td></td>\n\
                        <td class="playlistTvTitle">\n\
                            <spring:message code='playlist.name.label'/>\n\
                        </td>\n\
                        <td class="playlistTvTitle">\n\
                            <spring:message code='playlist.date.label'/>\n\
                        </td>\n\
                        <td class="playlistTvTitle">\n\
                            <spring:message code='playlist.owner.label'/>\n\
                        </td>\n\
                    </tr>';

                <c:forEach var="item" items="${mPlaylistList}">
                content += '<tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" onclick="addToPlaylist(${item.id});">';
                content += '<td class="uk-text-center matchesColumnResult1 clickRow" class="uk-width-1-10 clickRow uk-text-center">';
                content += '<div id="dateBox">';
                content += '<b>${item.clipAmount}</b><br/>';
                content += '<span class="playlistTvTinyText">Clip(s)</span>';
                content += '</div>';
                content += '</td>';
                content += '<td class="matchesColumnResult1 clickRow" class="uk-width-1-10 clickRow">';
                content += '<div>';
                content += '<a>${item.name}</a>';
                content += '</div>';
                content += '</td>';
                content += '<td class="matchesColumnResult1 clickRow" class="uk-width-1-10 clickRow">';
                content += '<div>';
                content += getDateFormattedString("${item.date}");
                content += '</div>';
                content += '</td>';
                content += '<td class="matchesColumnResult1 clickRow" class="uk-width-1-10 clickRow">';
                content += '<div>';
                content += '<a>${item.user.firstName} ${item.user.lastName}</a>';
                content += '</div>';
                content += '</td>';
                content += '</tr>';
                </c:forEach>

                content += '</tbody></table>';
                $("#playlistTvButtonLi > *:not(:first-child)").remove();
                $("#playlistTvButtonLi").append(content);
            }
        }
    </script>
    
    <table id="playlistTvTable" class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
        <tbody>
            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                <td></td>
                <td class="playlistTvTitle">
                   <spring:message code='playlist.name.label'/>
                </td>
                <td class="playlistTvTitle">
                   <spring:message code='playlist.description.label'/>
                </td>
                <td class="playlistTvTitle">
                   <spring:message code='playlist.date.label'/>
                </td>
                <td class="playlistTvTitle">
                   <spring:message code='playlist.owner.label'/>
                </td>
                <td></td>
            </tr>
            <c:forEach var="item" items="${mPlaylistList}">
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" id="playlistTvRowId${item.id}">
                    <td class="uk-text-center matchesColumnResult1 clickRow" onclick="clipAmountOnClick(${item.id})" class="uk-width-1-10 clickRow uk-text-center">
                        <div id="dateBox">
                            <b>${item.clipAmount}</b><br>
                            <span class="playlistTvTinyText">Clip(s)</span>
                        </div>
                    </td>
                    <td class="clickRow" onclick="<c:choose><c:when test="${mIsMySicsTvPage == true}">clipAmountOnClick(${item.id})</c:when><c:otherwise>location.href = '/sicstv/user/videoPlaylistTv.htm?playlistTvId=${item.id}'</c:otherwise></c:choose>">
                        <c:set var="icon" value="null"/>
                        <c:set var="iconPublic" value="null"/>
                        <c:set var="iconNew" value="null"/>
                        <c:choose>
                            <c:when test="${item.sharing != null && item.sharing == true}">
                                <c:set var="icon" value="uk-icon-link"/>
                            </c:when>
                            <c:when test="${item.sharing == null || item.sharing == false}">
                                <c:forEach var="share" items="${mPlaylistAllSharedWithUser}">
                                    <c:if test="${share.playlistId == item.id}">
                                        <!-- Ho trovato la playlist da controllare -->
                                        <c:if test="${share.played == null || !share.played}">
                                            <c:set var="iconNew" value="new"/>
                                        </c:if>
                                        <c:choose>
                                            <c:when test="${share.editable != null && share.editable == true}">
                                                <c:set var="icon" value="uk-icon-pencil"/>
                                                <c:set var="permission" value="edit"/>
                                            </c:when>
                                            <c:otherwise>
                                                <c:set var="icon" value="uk-icon-eye"/>
                                                <c:set var="permission" value="view"/>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:if>
                                </c:forEach>
                            </c:when>
                        </c:choose>
                        <c:forEach var="share" items="${mPlaylistAllPublicShare}">
                            <c:if test="${share.playlistId == item.id}">
                                <c:set var="iconPublic" value="uk-icon-globe"/>
                            </c:if>
                        </c:forEach>
                        <c:if test="${iconPublic != null}"><i class="${iconPublic}" style="margin-right: 5px" title="<spring:message code="playlist.share.public.title"/>"></i></c:if><c:if test="${icon != null}"><i class="${icon}" style="margin-right: 5px" title="<spring:message code="playlist.share.title"/>"></i></c:if><a id="playlistTvName${item.id}" onclick="<c:choose><c:when test="${mIsMySicsTvPage == true}">clipAmountOnClick(${item.id})</c:when><c:otherwise>location.href = '/sicstv/user/videoPlaylistTv.htm?playlistTvId=${item.id}'</c:otherwise></c:choose>">${item.name}</a>
                    </td>
                    <td class="clickRow" onclick="<c:choose><c:when test="${mIsMySicsTvPage == true}">clipAmountOnClick(${item.id})</c:when><c:otherwise>location.href = '/sicstv/user/videoPlaylistTv.htm?playlistTvId=${item.id}'</c:otherwise></c:choose>">
                        <a id="playlistTvDescription${item.id}" onclick="<c:choose><c:when test="${mIsMySicsTvPage == true}">clipAmountOnClick(${item.id})</c:when><c:otherwise>location.href = '/sicstv/user/videoPlaylistTv.htm?playlistTvId=${item.id}'</c:otherwise></c:choose>">${item.description}</a>
                    </td>
                    <td class="clickRow" onclick="<c:choose><c:when test="${mIsMySicsTvPage == true}">clipAmountOnClick(${item.id})</c:when><c:otherwise>location.href = '/sicstv/user/videoPlaylistTv.htm?playlistTvId=${item.id}'</c:otherwise></c:choose>">
                        <a onclick="<c:choose><c:when test="${mIsMySicsTvPage == true}">clipAmountOnClick(${item.id})</c:when><c:otherwise>location.href = '/sicstv/user/videoPlaylistTv.htm?playlistTvId=${item.id}'</c:otherwise></c:choose>" id="playlistTvCreationDate${item.id}">
                            <script type="text/javascript">
                            getDateFormatted(${item.id}, "${item.date}");
                            </script>
                        </a>
                    </td>
                    <td class="clickRow" onclick="<c:choose><c:when test="${mIsMySicsTvPage == true}">clipAmountOnClick(${item.id})</c:when><c:otherwise>location.href = '/sicstv/user/videoPlaylistTv.htm?playlistTvId=${item.id}'</c:otherwise></c:choose>">
                        <a id="playlistTvAuthor${item.id}" onclick="<c:choose><c:when test="${mIsMySicsTvPage == true}">clipAmountOnClick(${item.id})</c:when><c:otherwise>location.href = '/sicstv/user/videoPlaylistTv.htm?playlistTvId=${item.id}'</c:otherwise></c:choose>">${item.user.firstName} ${item.user.lastName}</a>
                    </td>
                    <td class="matchesColumnResult3">
                        <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}">
                            <button <c:if test="${mIsMySicsTvPage == true}">onclick="clipAmountOnClick(${item.id})"</c:if> class="uk-icon-hover uk-button uk-button-mini" title="<spring:message code="menu.user.options"/>"><i class="uk-icon-hover uk-icon-caret-down"></i></button>
                            <div class="uk-dropdown uk-dropdown-small uk-text-left">
                                <ul class="uk-nav uk-nav-dropdown">
                                    <c:if test="${item.shared == null || item.shared == false || (permission != null && permission == 'edit')}">
                                        <li><a onclick="openModifyPlaylistTv('${item.id}')"><i class="uk-icon-pencil uk-margin-right"></i><spring:message code="playlist.tooltip.rename"/></a></li>
                                        <li><a onclick="deletePlaylistTv(${item.id})"><i class="uk-icon-trash uk-margin-right"></i><spring:message code="menu.user.delete"/></a></li>
                                    </c:if>
                                    <li><a onclick="downloadPlaylistTv(${item.id}, false)"><i class="uk-icon-download uk-margin-right"></i><spring:message code="playlist.tv.download"/></a></li>
                                    <li><a onclick="downloadPlaylistTv(${item.id}, true)"><i class="uk-icon-download uk-margin-right"></i><spring:message code="playlist.tv.download.multi"/></a></li>
                                    <li><a onclick="location.href = '/sicstv/user/videoPlaylistTv.htm?playlistTvId=${item.id}'"><i class="uk-icon-external-link uk-margin-right"></i><spring:message code="playlist.tooltip.open"/></a></li>
                                    <c:if test="${mIsMySicsTvPage == false}">
                                        <li><a onclick="location.href = '/sicstv/user/myPlaylistTv.htm?playlistTvId=${item.id}'"><i class="uk-icon-external-link uk-margin-right"></i><spring:message code="playlist.tooltip.open.mysics"/></a></li>
                                    </c:if>
                                    <c:if test="${item.shared == null || item.shared == false}">
                                        <li><a onclick="openSharePlaylistTv('${item.id}')"><i class="uk-icon-share uk-margin-right" <c:if test="${mUserToShareList.size() == 0}">disabled</c:if>></i><spring:message code="menu.share"/></a></li>
                                    </c:if>
                                </ul>
                            </div>
                        </div>
                        <c:if test="${mIsMySicsTvPage == true}">
                            <button class="uk-icon-hover uk-button uk-button-mini" title="<spring:message code="video.play"/>" onclick="location.href = '/sicstv/user/videoPlaylistTv.htm?playlistTvId=${item.id}'"><i class="uk-icon-play"></i></button>
                        </c:if>
                        <c:if test="${item.lastUpdateBy != null}">
                            <c:set var="updateDescription" value="${mTooltipFirstRow}<br>"/>
                            <c:forEach var="user" items="${mLoadedUsers.values()}">
                                <c:if test="${item.lastUpdateBy == user.id}">
                                    <c:set var="updateDescription" value="${updateDescription}${mTooltipSecondRow} ${user.lastName} ${user.firstName}<br>"/>
                                </c:if>
                            </c:forEach>
                            <c:set var="updateDescription" value="${updateDescription}${mTooltipThirdRow} ${item.lastUpdateDate.toString().substring(0, item.lastUpdateDate.toString().indexOf('.'))}<br>"/>
                            <c:set var="updateDescription" value="${updateDescription}${mTooltipFourthRow} ${item.lastUpdateDescription}"/>
                            <button class="uk-button uk-button-mini tippy-button" data-tooltip-content="${updateDescription}" style="min-width: 20px"><i class="uk-icon-info"></i></button>
                        </c:if>
                        <c:if test="${iconNew != 'null'}">
                            <div class="playlist-new-div"><div class="playlist-new-text">NEW</div></div>
                        </c:if>
                    </td>
                </tr>
            </c:forEach>
        </tbody>
    </table>
    <div id="modalModifyPlaylist" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
        <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;" id="dropdownModifyPlaylist">
            <div>
                <span class="modalClose" onclick="closeModifyPlaylist();">&times;</span>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <label class="uk-text-bold uk-text-large" for="namePlaylistModify"><spring:message code="video.nome" />:</label>
                </div>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <input id="namePlaylistModify" class="uk-input uk-width-1-1 uk-form-large" type="text" aria-label="Input" maxlength="32">
                </div>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <label class="uk-text-bold uk-text-large" for="descriptionPlaylistModify"><spring:message code="video.descrizione" />:</label>
                </div>
                <div class="uk-margin uk-modal-content uk-width-1-1">
                    <input id="descriptionPlaylistModify" class="uk-input uk-width-1-1 uk-form-large" type="text" aria-label="Input" maxlength="255">
                </div>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <button id="cancelModifyPlaylist"  style="min-width:80px;" onclick="closeModifyPlaylist();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                    <span id="saveModifyPlaylist"></span>
                </div>
            </div>
        </div>
    </div>
    <div id="modalSharePlaylist" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
        <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;" id="dropdownSharePlaylist">
            <div class="playlistTv-container">
                <span class="modalClose" onclick="closeSharePlaylist();">&times;</span>
                <h2><spring:message code="playlist.share.people.share"/></h2>
                <div>
                    <table class="playlistTv-table uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;" id="addUserShareTable">
                        <tbody>
                            <tr>
                                <td></td>
                                <td class="playlistTvTitle"><spring:message code="playlist.nome"/></td>
                                <td class="playlistTvTitle"><spring:message code="playlist.email"/></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <h2><spring:message code="playlist.share.already.sharing"/></h2>
                <div>
                    <table class="playlistTv-table uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;" id="addUserAlreadySharedTable">
                        <tbody>
                            <tr>
                                <td></td>
                                <td class="playlistTvTitle"><spring:message code="playlist.nome"/></td>
                                <td class="playlistTvTitle"><spring:message code="playlist.email"/></td>
                                <td class="playlistTvTitle"><spring:message code="playlist.edit"/></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <hr>
                <div id="publicShareDiv">
                    
                </div>
            </div>
            <div class="uk-modal-footer uk-text-right uk-width-1-1">
                <button id="cancelSharePlaylist" style="min-width:80px;" onclick="closeSharePlaylist();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                <button id="confirmSharePlaylist" style="min-width:80px;" onclick="sharePlaylist();" class="uk-text-large uk-button uk-modal-close"><spring:message code="playlist.save"/></button>
            </div>
        </div>
    </div>
</div>