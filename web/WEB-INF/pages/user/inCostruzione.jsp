
<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=0.4">
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />

		<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
		<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

		<title><spring:message code="theme.title"/></title>

		<script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
		<link rel="stylesheet" href="/sicstv/uikit/css/default.css" />
		<link rel="stylesheet" href="/sicstv/css/sics.css" />
		<script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>
		<script type="text/javascript" src="/sicstv/js/utils.js" ></script>

	</head>

	<body>
		<%@ include file="header.jsp" %>
		<div class="uk-text-center uk-margin-large-top">
			<h1><spring:message code="alert.pagina.inesistente"/></h1><br>
			<h2><a href="/sicstv/user/home.htm"><spring:message code="menu.homepage"/></a></h2>
		</div>
	</body>

</html>
