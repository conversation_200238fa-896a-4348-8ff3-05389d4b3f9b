<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />

        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>

        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript" ></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript" ></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/toggle.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <script src="/sicstv/js/jquery.timer.js" type="text/javascript" ></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css"  >
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        
        <script type="text/javascript" defer>
            // PRE CARICAMENTO PAGINA
            function moveProgressBar(newValue) {
                var elem = document.getElementById("video-pre-bar");
                var width = parseInt(elem.style.width.replace("%", ""));
                var id = setInterval(frame, 15);
                function frame() {
                    if (width === 100) {
                        clearInterval(id);
                        $("#preLoadDiv").addClass("animazioneFadeOut");
                        
                        // Opzionale: gestisci l'evento di completamento dell'animazione
                        var preLoadDiv = document.getElementById("preLoadDiv");
                        preLoadDiv.addEventListener("transitionend", function() {
                            $("#preLoadDiv").addClass("uk-hidden");
                            $("#centralGrid").removeClass("uk-hidden");
                            
                            // controllo se devo cliccare su una clip specifica (post modifica clip)
                            var loadEvent = sessionStorage['loadEventIndex'];
                            if (typeof loadEvent !== "undefined" && loadEvent === "true") {
                                // sessionStorage.removeItem('loadEventIndex');
                                if (typeof lastEventIndex !== "undefined") {
                                    startAction("", lastEventIndex, isRunningOnChecked);
                                }
                            }
                        });
                    }
                    if (width >= 50) {
                        elem.style.color = "#ffffff";
                    } else {
                        elem.style.color = "#000000";
                    }
                    if (width >= newValue) {
                        clearInterval(id);
                    } else {
                        width++;
                        elem.style.width = width + '%';
                        $("#video-pre-bar-value").html(width + '%');
                    }
                }
            }
            
            function waitProgressBar() {
                setTimeout(function() {
                    var elem = document.getElementById("video-pre-bar");
                    if (elem === null) {
                        waitProgressBar();
                    } else {
                        // può capitare che non arrivi al 20% perchè ad un certo punto inizia il codice javascript
                        // e sembra bloccare tutte le modifiche della pagina
                        moveProgressBar(20);
                    }
                }, 100);
            }
            
            waitProgressBar();
            // PRE CARICAMENTO PAGINA
        </script>
        
        <script type="text/javascript">
            var isPageVideo = true;
            var personalSource = '${personalSource}';
            var pageStatus = [];
            var totMatches = [];
            var numMatches = [];
            var matchPerPage = 25;
            var ids = "";

            var openTag = false;
            var removeFilter = false;
            var eventSelected = [];
            var teamSelected = [];
            var playerSelected = [];
            var gamesSelected = [];
            var ultimaOperazione = 0;
            var numFiltri = 0;
            var singleMatch = false;
            var highlights = ${not empty highlights};
            var ipo = ${not empty ipo};
            var curVideoPath = "", curVideoHDPath = "", curVideoTACTPath = "";
            var curVideo = "";

            var pageHomeCompetition = "home.htm";
            var playerHTML;
            var hdActive = false;
            var tactActive = false;

            var countRowVisible = 0;

            var filtredEvents = false;
            var playerFilter = false;
            var currentFilterEvent = "";

            var actionTable;
            var actionTableMobile;
            
            var clipDurationMap = new Map();

            $("#numEventsSmall").addClass("uk-hidden");
            $(document).on("click", "a[aria-controls='azioni']", function () {
                // DAFARE azzerare lista selezionate al click su link della tabella
                $("#checkboxSelectAllEvents").removeAttr("checked");
                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                emptyAllActions();
            });

            $(document).on("click", "th[aria-controls='azioni']", function () {
                // DAFARE azzerare lista selezionate al click sul riordinamento della tabella
                $("#checkboxSelectAllEvents").removeAttr("checked");
                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                emptyAllActions();
            });

            $(document).on("click", "select.form-control[aria-controls='azioni']", function () {
                // DAFARE azzerare lista selezionate al cambio numero azioni per pagina
                $("#checkboxSelectAllEvents").removeAttr("checked");
                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                emptyAllActions();
            });

            $(document).on("click", "input.form-control[aria-controls='azioni']", function () {
                // DAFARE azzerare lista selezionate al cambio numero azioni per pagina
                $("#checkboxSelectAllEvents").removeAttr("checked");
                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                emptyAllActions();
            });

            var isMobile = false; //initiate as false
            // device detection
            if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent)
                    || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substr(0, 4))) {
                isMobile = true;
            }

            // Define some vars required later
            var videoWidth, videoHeight, videoRatio;
            var isPlaylistTv = false;
            var tacticalEventIds = [];
            
            // temporary
            var lastEventIndex;
            
            $(document).ready(function () {
                <c:forEach var="eventId" items="${mTacticalEventIdList}">
                    tacticalEventIds.push('${eventId}');
                </c:forEach>
                <c:forEach var="list" items="${mEvent}">
                    <c:forEach var="item" items="${list.value}" >
                        clipDurationMap.set('${item.id}', "${item.getDurataAzioneMinSec(false)}");
                    </c:forEach>
                </c:forEach>
                if ($("#mPlaylistTvId").val() !== '') {
                    isPlaylistTv = true;
                }
                if ('${personalSource}' == 'true') {
                    pageHomeCompetition = "mycompetition.htm";
                }
                // ultimo index mostrato
                lastEventIndex = sessionStorage['eventIndex'];
                // messaggio errore 
                $.fn.dataTable.ext.errMode = 'none';
                $('#azioni').on('error.dt', function (e, settings, techNote, message) {
                    console.log('DataTables error: ', message);
                });
                $('#azioni_mobile').on('error.dt', function (e, settings, techNote, message) {
                    console.log('DataTables mobile error: ', message);
                });
                jsRefreshMatches('1');
                if (${not empty matchPlayerId} || highlights || ipo || (${not empty usr} && ${fn:length(usr)>0}) || (${not empty psw} && ${fn:length(psw)>0}) || isPlaylistTv) {
                    $("#comboTeamSelection").remove();
                    $("#liMatches").remove();
                    $("#partite").remove();
                    $("#liCalendario").remove();
                    $("#calendario").remove();
                    $("#btnSearch").remove();
                    $("#quickChoiceSpan").addClass("uk-hidden");
                    $("#sourceSics").remove();
                    $("#sourcePersonal").remove();
                    $("#liRicerca").remove();
                    $("#liFiltri").remove();
                    $("#liFiltroGiocatori").remove();
                    $("#liFiltriContent").remove();
                    $("#liRicercaContent").remove();
                    $("#liFiltroGiocatoriContent").remove();
                    $("#liEventiSmall").attr("aria-expanded", "true");
                    $("#liEventiSmall").addClass("uk-active");
                    $("#liEventiSmallContent").addClass("uk-active");
                    $("#liEventiSmallContent").attr("aria-expanded", "true");
                    $("#liEventiSmallContent").attr("aria-hidden", "false");
                }
                actionTable = $('#azioni').DataTable({
                    "columnDefs": [
                        {type: 'date-euro', targets: 1, orderable: false}
                    ],
                    "stateSave": true,
                    "paging": true,
                    "lengthChange": true,
                    "pageLength": 15,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "ext.errMode": "none",
                    "lengthMenu" : [10, 15, 25, 50, 100]
                });
//                actionTable.column(actionTable.columns(':visible').nodes().length).visible(false); // hide last column
//                actionTable.column(actionTable.columns(':visible').nodes().length - 1).visible(false); // hide column
                actionTable.on('preDraw.dt', function() {
                    //console.log("going to hide id " + indexActionToPlay);
                    //$("#infoSearch" + selectedActions[indexActionToPlay] + " + i").addClass("uk-hidden");
                    var actionsArray = [];
                    if (isRunningOnChecked) {
                        actionsArray = checkedActions;
                    } else {
                        actionsArray = selectedActions;
                    }
                    //$('#rowEventId' + actionsArray[indexActionToPlay] +' td').removeClass("currentClip");
                    $('.currentClip').removeClass("currentClip");
                });
                actionTable.on('draw.dt', function() {
                    //console.log("going to show id " + indexActionToPlay);
                    //$("#infoSearch" + selectedActions[indexActionToPlay] + " + i").removeClass("uk-hidden");
                    var actionsArray = [];
                    if (isRunningOnChecked) {
                        actionsArray = checkedActions;
                    } else {
                        actionsArray = selectedActions;
                    }
                    if (indexActionToPlay >= 0) {
                        //checkIfNeededToChangePage();
                        $('#rowEventId' + actionsArray[indexActionToPlay] + ' td').addClass("currentClip");
                    }
                    
                    actionTable.rows({filter: 'applied'}).every( function ( rowIdx, tableLoop, rowLoop ) {
                        if (this.nodes().to$().is(':visible')) {
                            var data = this.data();
                            var id = data[actionTable.columns().nodes().length - 1];
                            //console.log("checking id " + id + " in array? " + (checkedActions.includes(id, checkedActions)));
                            if (checkedActions.includes(id) == true) {
                                $("#checkbox" + id).attr("checked", "checked");
                            } else { // per reset
                                $("#checkbox" + id).removeAttr("checked");
                                $("#checkboxSelectAllEvents").removeAttr("checked");
                                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                            }
                        }
                    });
                });

                actionTableMobile = $('#azioni_mobile').DataTable({
                    "columnDefs": [
                        {type: 'date-euro', targets: 1, orderable: false}
                    ],
                    "stateSave": true,
                    "paging": true,
                    "lengthChange": true,
                    "pageLength": 10,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "ext.errMode": "none"
                });
//                actionTableMobile.column(actionTableMobile.columns(':visible').nodes().length - 1).visible(false); // hide last column
//                actionTableMobile.column(actionTableMobile.columns(':visible').nodes().length - 2).visible(false); // hide column
                actionTableMobile.on('preDraw.dt', function() {
                    //$("#infoSearch" + selectedActions[indexActionToPlay] + " + i").addClass("uk-hidden");
                    var actionsArray = [];
                    if (isRunningOnChecked) {
                        actionsArray = checkedActions;
                    } else {
                        actionsArray = selectedActions;
                    }
                    $('.currentClip').removeClass("currentClip");
                });
                actionTableMobile.on('draw.dt', function() {
                    //$("#infoSearch" + selectedActions[indexActionToPlay] + " + i").removeClass("uk-hidden");
                    var actionsArray = [];
                    if (isRunningOnChecked) {
                        actionsArray = checkedActions;
                    } else {
                        actionsArray = selectedActions;
                    }
                    if (indexActionToPlay >= 0) {
                        checkIfNeededToChangePage();
                        $('#rowEventId' + actionsArray[indexActionToPlay] + ' td').addClass("currentClip");
                    }
                    
                    actionTableMobile.rows({filter: 'applied'}).every( function ( rowIdx, tableLoop, rowLoop ) {
                        if (this.nodes().to$().is(':visible')) {
                            var data = this.data();
                            var id = data[actionTableMobile.columns().nodes().length - 1];
                            //console.log("checking id " + id + " in array? " + (checkedActions.includes(id, checkedActions)));
                            if (checkedActions.includes(id) == true) {
                                $("#checkbox" + id).attr("checked", "checked");
                            } else { // per reset
                                $("#checkbox" + id).removeAttr("checked");
                                $("#checkboxSelectAllEvents").removeAttr("checked");
                                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                            }
                        }
                    });
                });

                singleMatch = ${fn:length(mGame)==1};
                filtredEvents = ${fn:length(mEventFilter)>0};
                playerFilter = ${mPlayer.id > 0};
                $("#quickChoiceSpan").remove();
                if (filtredEvents) {
                    currentFilterEvent = '${mEventFilter}';

                    $("#liRicerca").remove();
                    $("#liRicercaContent").remove();
                    if (singleMatch) {
                        $("#liSelezionate").remove();
                        $("#liSelezionateContent").remove();
                        $("#selectedMatches").remove();
                    }
                    $("#timeFirstButton").remove();
                    $("#timeSecondButton").remove();
                    $("#timeFirstQuarter").remove();
                    $("#timeSecondQuarter").remove();
                    $("#timeThirdQuarter").remove();
                    $("#timeFourthQuarter").remove();
                    $("#btnResetFiltri").remove();
                    $("#liCalendario").remove();
                    $("#calendario").remove();

                    $("#liFiltri").addClass("uk-active");
                    $("#liFiltriContent").addClass("uk-active");
                } else if (singleMatch) {
                    $("#liSelezionate").remove();
                    $("#liSelezionateContent").remove();
                    $("#selectedMatches").remove();
                } else {
                    $("#liCalendario").remove();
                    $("#liInfoMatch").remove();
                    $("#calendario").remove();
                    $("#matchData").remove();
                    var idPl = $("#mPlayerId").val();
                    if (typeof idPl !== 'undefined') {
                        jsSetButtonSelected('playerButton' + idPl, 3);
                    }
                }
                if (${empty filterToShow} || ${empty filterToShow['SICS'][0]}) {
                    $("#liRicerca").remove();
                    $("#liRicercaContent").remove();
                    if (${empty mButtonToShow}) {
                        $("#liFiltri").remove();
                        $("#liFiltriContent").remove();
                        $("#liFiltroGiocatori").remove();
                        $("#liFiltroGiocatoriContent").remove();
                        $("#btnFiltriRapidi").remove();
                        $("#liEventiSmall").remove();
                        $("#liEventiSmallContent").remove();
                        $("#liActions").remove();
                        $("#eventi").remove();
                        $("#doExportAction").remove();
                        $("#videoPlayerTools").remove();
                        $("#liMatches").addClass("uk-active");
                        $("#partite").addClass("uk-active");
                    } else {
                        $("#liFiltri").addClass("uk-active");
                        $("#liFiltriContent").addClass("uk-active");
                        $("#sourceSics").remove();
                        $("#sourcePersonal").remove();
                    }
                }
                initPageVideo();
                jsComponentsView();
                initSearchFilter();
                $("#bottomNav").removeClass("uk-hidden");
                if (isMobile || iOS()) {
                    hdActive = false;
                }
                //console.log(personalSource);
                if (personalSource === 'true') {
                    $("#sourceSics").remove();
                    $("#sourcePersonal").remove();
                }
                
                updateSelectedActionsArray(true);
                if ($('#mEventPresent').val() === 'true' || $('#mSer').val() !== '' || isPlaylistTv) { // se arrivo filtrato mostro le clip
                    startAction('', 0, isRunningOnChecked);
                }
                
                playerHTML.addEventListener("seeking", (event) => {
                    if (typeof fineAzione !== 'undefined' && !isClipStopped) {
                        var currentTime = playerHTML.currentTime - 1;
                        var endTime = fineAzione;
                        if (currentTime > endTime) {
                            closeClipViewer();
                        }
                    }
                });
                
                // screenshot function stuff		
		// Add a listener to wait for the 'loadedmetadata' state so the video's dimensions can be read
		playerHTML.addEventListener('loadedmetadata', function() {
                    // Calculate the ratio of the video's width to height
                    videoRatio = playerHTML.videoWidth / playerHTML.videoHeight;
                    // Define the required width as 100 pixels smaller than the actual video's width
                    videoWidth = playerHTML.videoWidth - 100;
                    // Calculate the height based on the video's width and the ratio
                    videoHeight = parseInt(videoWidth / videoRatio, 10);
                    // Set the canvas width and height to the values just calculated
//                    var canvas = document.getElementById("screenshotCanvas");
//                    canvas.width = videoWidth;
//                    canvas.height = videoHeight;
		}, false);
                
                if (isPlaylistTv) {
                    playerHTML.addEventListener("click", (event) => {
                        if (event.detail === 1) { // 1 click
                            resumeStopVideo();
                        } else if (event.detail === 2) {
                            if (document.fullscreenElement !== null) { // sono in fullscreen
                                document.webkitExitFullscreen();
                            } else {
                                playerHTML.requestFullscreen();
                            }
                            if (playerHTML.paused) {
                                playerHTML.play();
                            }
                            checkPlayButton();
                        }
                    });
                    
                    $("#timeFirstButton").addClass("uk-hidden");
                    $("#timeSecondButton").addClass("uk-hidden");
                    $("#btnResetFiltri").addClass("uk-hidden");
                    $("#closeClipViewer").addClass("uk-hidden");
                }
                
                if (isPlaylistTv || filtredEvents) {
                    $("#liSelezionate").click(); // mostro il toolbar delle partite selezionate
                }
                
                $('#azioni tbody').sortable({
                    // Update the DataTable order when the user finishes dragging and dropping
                    start: function(event, ui) {
                        stopVideo();
                    },
                    stop: function(event, ui) {
                        resumeVideo();
                    },
                    update: function(event, ui) {
                        jsShowBlockUI();
                        
                        // Get the new order of the rows
                        const newOrder = $(this).sortable("toArray", { attribute: "data-id" });

                        $.ajax({
                            type: "POST",
                            url: "/sicstv/user/updateClipTvOrder.htm",
                            cache: false,
                            data: encodeURI("&playlistId=" + $("#mPlaylistTvId").val() + "&eventIdsWithOrder=" + newOrder),
                            success: function (msg) {
                                if (msg === "true") {
                                    UIkit.notify("<spring:message code="playlist.update.success"/>", {status: 'success', timeout: 1000});
                                } else {
                                    UIkit.notify("<spring:message code="playlist.update.error"/>", {status: 'danger', timeout: 1000});
                                    $('#azioni tbody').sortable('cancel');
                                }
                                updateTableNavigation();
                                $.unblockUI();
                            },
                            error: function () {
                                UIkit.notify("<spring:message code="playlist.update.error"/>", {status: 'danger', timeout: 1000});
                                $('#azioni tbody').sortable('cancel');
                                $.unblockUI();
                            }
                        });
                    }
                });
                
                $('#azioni_mobile tbody').sortable({
                    // Update the DataTable order when the user finishes dragging and dropping
                    start: function(event, ui) {
                        stopVideo();
                    },
                    update: function(event, ui) {
                        jsShowBlockUI();
                        
                        // Get the new order of the rows
                        const newOrder = $(this).sortable("toArray", { attribute: "data-id" });

                        $.ajax({
                            type: "POST",
                            url: "/sicstv/user/updateClipTvOrder.htm",
                            cache: false,
                            data: encodeURI("&playlistId=" + $("#mPlaylistTvId").val() + "&eventIdsWithOrder=" + newOrder),
                            success: function (msg) {
                                if (msg === "true") {
                                    UIkit.notify("<spring:message code="playlist.update.success"/>", {status: 'success', timeout: 1000});
                                } else {
                                    UIkit.notify("<spring:message code="playlist.update.error"/>", {status: 'danger', timeout: 1000});
                                    $('#azioni tbody').sortable('cancel');
                                }
                                updateTableNavigation();
                                $.unblockUI();
                            },
                            error: function () {
                                UIkit.notify("<spring:message code="playlist.update.error"/>", {status: 'danger', timeout: 1000});
                                $('#azioni tbody').sortable('cancel');
                                $.unblockUI();
                            }
                        });
                    }
                });
                
                // se riordino la Datatable devo anche riordinare l'array delle azioni
                actionTable.on('order.dt', function() {
                    updateTableNavigation();
                    // Get the new order of the rows
//                    const newOrder = $('#azioni tbody').sortable("toArray", { attribute: "data-id" });
//
//                    $.ajax({
//                        type: "POST",
//                        url: "/sicstv/user/updateClipTvOrder.htm",
//                        cache: false,
//                        data: encodeURI("&playlistId=" + $("#mPlaylistTvId").val() + "&eventIdsWithOrder=" + newOrder),
//                        success: function (msg) {
//                            if (msg !== "true") {
//                                $('#azioni tbody').sortable('cancel');
//                            }
//                        },
//                        error: function () {
//                            $('#azioni tbody').sortable('cancel');
//                        }
//                    });
                });
                actionTableMobile.on('order.dt', function() {
                    updateTableNavigation();
                    // Get the new order of the rows
//                    const newOrder = $('#azioni_mobile tbody').sortable("toArray", { attribute: "data-id" });
//
//                    $.ajax({
//                        type: "POST",
//                        url: "/sicstv/user/updateClipTvOrder.htm",
//                        cache: false,
//                        data: encodeURI("&playlistId=" + $("#mPlaylistTvId").val() + "&eventIdsWithOrder=" + newOrder),
//                        success: function (msg) {
//                            if (msg !== "true") {
//                                $('#azioni_mobile tbody').sortable('cancel');
//                            }
//                        },
//                        error: function () {
//                            $('#azioni_mobile tbody').sortable('cancel');
//                        }
//                    });
                });
                
                moveProgressBar(100);
            });

            // responsive
            $(window).resize(function () {
                jsComponentsView();
            });

            function resumeStopVideo() {
                if (!playerHTML.paused) {
                    playerHTML.pause();
                } else {
                    // verifico se devo tornare alla prima clip
                    if (indexActionToPlay === (selectedActions.length - 1)) {
                        var params = $("#startData" + selectedActions[indexActionToPlay]).val().split("||");
                        if (playerHTML.currentTime > parseInt(params[1])) { // se il tempo attuale è maggiore della fine dell'ultima clip
                            startAction('', 0);
                        }
                    }
                    playerHTML.play();
                }
                checkPlayButton();
            }

            /* metodo che gestisce la visualizzazione delle azioni in base alla risoluzione*/
            var large = true;
            function jsComponentsView() {
                // hide medium

                if (UIkit.$win.width() < 769 && large) {
                    // se prima ero in visualizzazione large e ora la dimensione è cambiata in small
                    $('#video').removeClass("uk-width-5-10").addClass("uk-width-1-1");
                    //$('#liEventiSmallContent').html($('#eventi').html());
                    //$('#eventi').html("");
                    large = false;
                } else if (UIkit.$win.width() >= 769 && !large) {
                    // se prima ero in visualizzazione small e ora passo in large
                    $('#video').removeClass("uk-width-1-1").addClass("uk-width-5-10");
                    //$('#eventi').html($('#liEventiSmallContent').html());
                    //$('#liEventiSmallContent').html("");
                    if ($('#liEventiSmall').hasClass("uk-active")) {
                        $("#liFiltri").trigger("click");
                    }
                    large = true;
                }
                applyFilterEvents(true);
            }

            function updateBreadcrumb(videoname) {
                var breadcrumb = "";
                if (${mGameVideo.size() > 0}) {
                    var testGame = "";
                    var competitionName = "";
                    var competitionId = "";
                    var homeTeamId = "";
                    var homeTeamName = "";
                    var awayTeamId = "";
                    var awayTeamName = "";
                    var dateString = "";
            <c:forEach var="game" items="${mGameVideo}">
                    testGame = "<c:out value="${game.key}"/>";
                    testGame = testGame.replace("&#039;", "'");
                    if (testGame === videoname) {
                        competitionId = "<c:out value="${game.value.competitionId}"/>";
                        competitionName = "<c:out value="${game.value.competitionName}"/>";
                        homeTeamId = "<c:out value="${game.value.homeTeamId}"/>";
                        homeTeamName = "<c:out value="${game.value.homeTeam}"/>";
                        awayTeamId = "<c:out value="${game.value.awayTeamId}"/>";
                        awayTeamName = "<c:out value="${game.value.awayTeam}"/>";
                        dateString = "<c:out value="${game.value.getDateString()}"/>";
                    }
            </c:forEach>
                    if (dateString !== '' && competitionId !== '' && competitionName !== '' && homeTeamId !== '' && homeTeamName !== '' && awayTeamId !== '' && awayTeamName !== '') {
                        if (filtredEvents) {

                            if (playerFilter) {
                                <!--<li><a href='/sicstv/user/" + pageHomeCompetition + "'><spring:message code='user.competitions'/></a></li>-->
                                breadcrumb = "<ul class='uk-breadcrumb uk-margin-left'><li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'>${mPlayer.last_name} ${mPlayer.first_name}</a></li></ul>";
                            } else {
                            <!--<li><a href='/sicstv/user/" + pageHomeCompetition + "'><spring:message code='user.competitions'/></a></li>-->
                                breadcrumb = "<ul class='uk-breadcrumb uk-margin-left'>";
                                breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "'>" + competitionName + "</a></li>";
                                breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&formTeamId=" + homeTeamId + "'>" + homeTeamName + "</a> - <a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&formTeamId=" + awayTeamId + "'>" + awayTeamName + "</a> - " + dateString + "</li></ul>";
                            }
                        } else {
                        <!--<li><a href='/sicstv/user/" + pageHomeCompetition + "'><spring:message code='user.competitions'/></a></li>-->
                            breadcrumb = "<ul class='uk-breadcrumb uk-margin-left'>";
                            breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "'>" + competitionName + "</a></li>";
                            if (${not empty mPlayer.id}) {
                                breadcrumb += "<li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'><span>${mPlayer.last_name} ${mPlayer.first_name}</span></a></li>"
                            }
                            breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&formTeamId=" + homeTeamId + "'>" + homeTeamName + "</a> - <a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&formTeamId=" + awayTeamId + "'>" + awayTeamName + "</a> - " + dateString + "</li></ul>";
                        }
                    }
                } else {
                    if (filtredEvents) {
                        if (playerFilter) {
                            breadcrumb = "<ul class='uk-breadcrumb uk-margin-left'><li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'>${mPlayer.known_name} </a> - <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/></li></ul>";
                        } else {
                            breadcrumb = "<ul class='uk-breadcrumb uk-margin-left'>";
                            if (${not empty mCompetition.name}) {
                                breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${mCompetition.id}'>${mCompetition.name}</a></li>";
                            }
                            breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=-1&formTeamId=${mTeam.id}'>${mTeam.name}</a>  - <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/> </li></ul>";
                        }
                    }
                }
                if (breadcrumb !== "") {
                    breadcrumb = breadcrumb.replace("</ul>", "<li>Playlist: ${mPlaylistTv.name}</li></ul>");
                    breadcrumb = breadcrumb.replace("</ul>", "<li><spring:message code='video.durata'/>: ${mPlaylistDuration}</li></ul>");
                    $("#breadcrumb").html(breadcrumb);
                }
            }

            function initPageVideo() {
                singleMatch = ${fn:length(mGame)==1};
                var primo = true;
                if (${mGame.size() > 0}) {
            <c:forEach items="${mGame}" var="game" varStatus="status" >
                    gamesSelected.push('${game.value.idFixture}');
                    if (singleMatch) {
                        $('body').prepend("<input type='hidden' id='mCompetitionId' value='${game.value.competitionId}' /><input type = 'hidden' id = 'mDay' value = '${game.value.matchday}' />");
                    }
                    if (primo) {
                        primo = false;
                        var breadcrumb = "";

                        if (filtredEvents) {
                            if (playerFilter) {
                                <!--<li><a href='/sicstv/user/" + pageHomeCompetition + "'><spring:message code='user.competitions'/></a></li>-->
                                breadcrumb = "<div id='breadcrumb' class='uk-width-1-1'><ul class='uk-breadcrumb uk-margin-left'>" +
                                        "<li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'>${mPlayer.last_name} ${mPlayer.first_name}</a>" + /*- <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/>*/"</li></ul></div>";
                            } else {
                            <!--<li><a href='/sicstv/user/" + pageHomeCompetition + "'><spring:message code='user.competitions'/></a></li>-->
                                breadcrumb = "<div id='breadcrumb' class='uk-width-1-1'><ul class='uk-breadcrumb uk-margin-left'>";
                                //if (${not empty game.value.competitionName}) {
                                breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}'>${game.value.competitionName}</a></li>";
                                //}
                                //breadcrumb += "<li><a href='/sicstv/user/team.htm?formCompetitionId=${mCompetition.id}&formTeamId=${mTeam.id}'>${mTeam.name}</a>" + /*  - <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/> */"</li>";
                                breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}&formTeamId=${game.value.homeTeamId}'>${game.value.homeTeam}</a> - <a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}&formTeamId=${game.value.awayTeamId}'>${game.value.awayTeam}</a> - ${game.value.getDateString()}</li></ul></div>";
                            }

                        } else {
                        <!--<li><a href='/sicstv/user/" + pageHomeCompetition + "'><spring:message code='user.competitions'/></a></li>-->
                            breadcrumb = "<div id='breadcrumb' class='uk-width-1-1'><ul class='uk-breadcrumb uk-margin-left'>";
                            breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}'>${game.value.competitionName}</a></li>";
                            if (${not empty mPlayer.id}) {
                                breadcrumb += "<li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'><span>${mPlayer.last_name} ${mPlayer.first_name}</span></a></li>"
                            }
                            //else if (${not empty mTeam.id} && !highlights && singleMatch) {
                            //    breadcrumb += "<li><a href='/sicstv/user/team.htm?formCompetitionId=${game.value.competitionId}&formTeamId=${mTeam.id}'><span>${mTeam.name}</a></span></li>";
                            //}
                            breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}&formTeamId=${game.value.homeTeamId}'>${game.value.homeTeam}</a> - <a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}&formTeamId=${game.value.awayTeamId}'>${game.value.awayTeam}</a> - ${game.value.getDateString()}</li></ul></div>";
                        }
                        $('#video').prepend(breadcrumb);

                        curVideo = "${game.value.videoName}";
                        curVideoPath = "${game.value.getVideoPathS3()}";
                        if (${game.value.hd && !personalSource}) {
                            curVideoHDPath = "${game.value.getVideoPathHDS3()}";
                            $("#btnHD").removeClass("uk-hidden");
                        }
                        if (${game.value.tacticalVideo && !personalSource}) {
                            curVideoTACTPath = "${game.value.getVideoPathTACTS3()}";
                            $("#btnTACT").removeClass("uk-hidden");
                        }
                        loadVideo(curVideoPath, false, false, true);
                        $("<div id='playerType' hidden>${game.value.providerId}</div>").insertAfter("#mGame");
                    }
            </c:forEach>
                } else {
                    if (filtredEvents) {
                        if (playerFilter) {
                            breadcrumb = "<div id='breadcrumb' class='uk-width-1-1'>" +
                                    "<ul class='uk-breadcrumb uk-margin-left'><li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'>${mPlayer.known_name} </a> - <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/></li></ul></div>";
                        } else {
                            breadcrumb = "<div id='breadcrumb' class='uk-width-1-1'><ul class='uk-breadcrumb uk-margin-left'>";
                            if (${not empty mCompetition.name}) {
                                breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${mCompetition.id}'>${mCompetition.name}</a></li>";
                            }
                            breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=-1&formTeamId=${mTeam.id}'>${mTeam.name}</a>  - <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/> </li></ul></div>";
                        }
                    }
                    $('#video').prepend(breadcrumb);
                }

                $("#numMatchSelected").html(gamesSelected.length);
            }

            function loadVideo(videoname, playHD, playTACT, force) {
                if (typeof playerHTML === 'undefined') {
                    var hdEnable = !$("#btnHD").hasClass("uk-hidden");
                    if (hdEnable) {
                        playHD = true;
                        if (!isMobile && !iOS()) {
                            if ($("#btnHD").hasClass("HDon")) {
                                // se è in hd allora passo allo standard
                                hdActive = false;
                                $("#btnHD img").attr("src", "/sicstv/images/hd-dark.png");
                                $("#btnHD").removeClass("HDon");
                            } else {
                                // se è sd passo all'HD
                                hdActive = true;
                                $("#btnHD img").attr("src", "/sicstv/images/hd-selected.png");
                                $("#btnHD").addClass("HDon");
                            }
                            tactActive = false;
                            $("#btnTACT img").attr("src", "/sicstv/images/tact.png");
                            $("#btnTACT").removeClass("HDon");
                        }
                    }
                    var videoHtmlToAdd = '<source src="' + videoname + '" type="video/mp4">';
                    $('#media_playerHTML').html(videoHtmlToAdd);
                    playerHTML = $('#media_playerHTML').get(0);
                    if (isPlaylistTv) {
                        playerHTML.className += ' hideSeekBar';
                    }
                }
                if (videoname !== curVideoPath || force) {
                    var hd = !$("#btnHD").hasClass("uk-hidden");
                    var tact = !$("#btnTACT").hasClass("uk-hidden");
                    $("#btnHD").addClass("uk-hidden");
                    $("#btnTACT").addClass("uk-hidden");
                    $("#btnCinema").addClass("uk-hidden");
                    curVideoPath = videoname;
                    var splitVideName = curVideo.split(".");
                    updateBreadcrumb(splitVideName[0]);
                    var tactV = false;
                    var hdV = false;
                    var minVideoQuality = -1;
            <c:forEach items="${mGame}" var="game" varStatus="status" >
                    tactV = ${game.value.tacticalVideo && !personalSource};
                    hdV = ${game.value.hd && !personalSource};
                    minVideoQuality = ${game.value.minVideoQuality};
                    if ("${game.value.videoName}" === curVideo) {
                        if (hdV) {
                            curVideoHDPath = "${game.value.getVideoPathHDS3()}";
                        }
                        if (tactV) {
                            curVideoTACTPath = "${game.value.getVideoPathTACTS3()}";
                        }
                    }
            </c:forEach>
                    console.log("TACT " + curVideoTACTPath);
                    console.log("SD " + curVideoPath);
                    console.log("HD " + curVideoHDPath);
                    //console.log("tact=" + tactV + ", hd=" + hdV + ", minQuality=" + minVideoQuality);
                    if (playTACT && curVideoTACTPath.length > 0) {
                        $("#media_playerHTML source").attr("src", curVideoTACTPath);
                        playerHTML.load(curVideoTACTPath);
                    } else if (playHD && curVideoHDPath.length > 0) {
                        $("#media_playerHTML source").attr("src", curVideoHDPath);
                        playerHTML.load(curVideoHDPath);
                    } else {
                        $("#media_playerHTML source").attr("src", curVideoPath);
                        playerHTML.load(curVideoPath);
                    }

                    if (hd && minVideoQuality == 0) {
                        $("#btnHD").removeClass("uk-hidden");
                    }
                    if (tact) {
                        $("#btnTACT").removeClass("uk-hidden");
                    }
                    //$("#btnHD").addClass("uk-hidden");
                    $("#btnCinema").removeClass("uk-hidden");
                }
            }

            function initVideoHTML(videoname) {
                var videoHtmlToAdd = '<source src="' + videoname + '" type="video/mp4">';
                $('#media_playerHTML').html(videoHtmlToAdd);
                playerHTML = $('#media_playerHTML').get(0);
                
                if (isPlaylistTv) {
                    playerHTML.className += ' hideSeekBar';
                }
                curVideoPath = videoname;
            }

            // logout ajax
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);

            function selezionaDeseleziona() {
                //Se sto selezionando tutto
                if (ultimaOperazione == 1) {
                    $("#input_azione input").iCheck('check');
                    ultimaOperazione = 2;
                } else {
                    //Se sto deselezionando tutto
                    if (ultimaOperazione == 2) {
                        $("#input_azione input").iCheck('uncheck');
                        ultimaOperazione = 1;
                    }
                }
            }

            function applyFilterEvents(clearQuickSearch) {

                //emptySelectedActions();
                if (clearQuickSearch) {
                    jsRemoveFilter('', 5);
                }
                var squadre = [];
                var azioni = [];
                var sorgenti = [];
                var tempo = [];
                var giocatori = [];
                var esito = [];
                var partite = [];

                $.each(teamSelected, function (index, value) {
                    squadre.push(value);
                });
                //Seleziono le azioni
                $.each(eventSelected, function (index, value) {
                    azioni.push(value);
                });
                //Seleziono i sorgenti
                if ($("#sourceSics").hasClass("uk-button-success")) {
                    sorgenti.push($("#sourceSics").val());
                }
                if ($("#sourcePersonal").hasClass("uk-button-success")) {
                    sorgenti.push($("#sourcePersonal").val());
                }
                //Seleziono il tempo
                if ($("#timeFirstButton").hasClass('uk-button-success') || $("#timeFirstQuarter").hasClass('uk-button-success')) {
                    tempo.push('1');
                }
                if ($("#timeSecondButton").hasClass('uk-button-success') || $("#timeSecondQuarter").hasClass('uk-button-success')) {
                    tempo.push('2');
                }
                if ($("#timeThirdQuarter").hasClass('uk-button-success')) {
                    tempo.push('3');
                }
                if ($("#timeFourthQuarter").hasClass('uk-button-success')) {
                    tempo.push('4');
                }
                //Seleziono i giocatori
                $.each(playerSelected, function (index, value) {
                    giocatori.push(value);
                });

                //Seleziono le partite
                $.each(gamesSelected, function (index, value) {
                    partite.push(value);
                });

                // Selezione esito
                if ($("#btn2ptPos").hasClass('uk-button-success')) {
                    esito.push("2PT+");
                }
                if ($("#btn2ptNeg").hasClass('uk-button-success')) {
                    esito.push("2PT-");
                }
                if ($("#btn3ptPos").hasClass('uk-button-success')) {
                    esito.push("3PT+");
                }
                if ($("#btn3ptNeg").hasClass('uk-button-success')) {
                    esito.push("3PT-");
                }
                if ($("#btnFF").hasClass('uk-button-success')) {
                    esito.push("FF");
                }
                if ($("#btnFS").hasClass('uk-button-success')) {
                    esito.push("FS");
                }
                if ($("#btnPP").hasClass('uk-button-success')) {
                    esito.push("PP");
                }
                if ($("#btnREC").hasClass('uk-button-success')) {
                    esito.push("REC");
                }

                countRowVisible = 0;

                actionTable.rows().every(function (rowIdx, tableLoop, rowLoop) {
                    var nodeInfoSearch = $(this.node()).find(".infoSearch");
                    var infoAction = nodeInfoSearch.val().split("#");
                    /* SICS2017_PIF||PIF-1,PIF-2#9012,9013#1#1#108#2345#2pt+,pp -> 
                     * [0]=PIF||PIF-1,PIF-2
                     * [1]=9012,9013 -> giocatori
                     * [2]=1 -> half
                     * [3]=1 -> sics/pers
                     * [4]=108 -> squadra
                     * [5]= 2345 -> fixtureId
                     * [6]= 2PT+ -> esito*/
                    var condEsito = true;

                    if (esito.length > 0) {
                        condEsito = false;
                        var resultSplitted = infoAction[6].split(",");
                        $.each(esito, function (index, value) {
                            $.each(resultSplitted, function (indexEsito, valueEsito) {
                                if (valueEsito === value) {
                                    condEsito = true;
                                    return false;
                                }
                            });
                        });
                    }
                    if (!condEsito) {
                        //  $(this).closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                        return true;
                    }
                    var condHalf = true;
                    if (tempo.length > 0) {
                        condHalf = false;
                        $.each(tempo, function (index, value) {
                            if (value === infoAction[2]) {
                                condHalf = true;
                                return false;
                            }
                        });
                    }
                    if (!condHalf) {
                        //   nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                        return true;
                    }
                    var condSource = true;
                    if (sorgenti.length > 0) {
                        condSource = false;
                        $.each(sorgenti, function (index, value) {
                            if (value === infoAction[3]) {
                                condSource = true;
                                return false;
                            }
                        });
                    }
                    if (!condSource) {
                        //   nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                        return true;
                    }
                    var condType = true;
                    if (azioni.length > 0) {
                        condType = false;
                        $.each(azioni, function (index, value) {
                            // split del tipo che è strutturato: SICS2015_ATT_ATT-4 oppure SICS2105_ATT
                            var toSearch = value.split("_");
                            var size = toSearch.length;
                            var trovato = false;
                            if (size > 1) {
                                // individuo il tipo dell'azione che cerchiamo
                                var actionType = infoAction[0].split("||");
                                /* una volta splittato ho: es. SICS2017_PIF||PIF-1,PIF-2
                                 * actionType[0] = SICS2015_PIF
                                 * actionType[1] = PIF-1,PIF-2
                                 * */
                                var configTypePair = actionType[0].split("_");
                                if (configTypePair[0] === toSearch[0]) {

                                    if (size === 2) {
                                        // se la size è 2 allora è un fondamentale e quindi lo confronto con il tipo dell'azione
                                        if (actionType.length === 1) {
                                            trovato = configTypePair[1] === toSearch[1];
                                        }
                                    } else if (size === 3) {
                                        // se la size è 3 allora è un tag quindi vado a confrontare con i tag dell'azione
                                        if (actionType.length > 1) {
                                            // determino i tag dell'azione
                                            var tagsAction = actionType[1].split(",");
                                            $.each(tagsAction, function (indexTag, valueTag) {
                                                if (valueTag === toSearch[2]) {
                                                    trovato = true;
                                                    return false;
                                                }
                                            });
                                        }
                                    }
                                }
                                if (trovato) {
                                    // significa che è un azione da far vedere
                                    condType = true;
                                    return false;
                                }
                            }
                        });
                    }

                    if (!condType) {
                        //   nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                        return true;
                    }

                    var condPartita = true;
                    if (partite.length > 0) {
                        condPartita = false;
                        $.each(partite, function (index, value) {
                            var fixtureId = infoAction[5].split("||")[0];
                            if (value === fixtureId) {
                                condPartita = true;
                                return false;
                            }
                        });
                    }
                    if (!condPartita || (!singleMatch && partite.length == 0)) {
                        //    nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                        return true;
                    }


                    var condTeamPlayer = true;
                    // verifica sulle squadre e giocatori
                    if (squadre.length === 0) {
                        // se non è specificata una squadra
                        if (giocatori.length === 0) {
                            // se non sono specificati giocatori
                            condTeamPlayer = true;
                        } else {

                            // se sono specificati dei giocatori
                            $.each(giocatori, function (index, value) {
                                var selectedPlayer = infoAction[1].split(",");
                                var trovato = false;
                                $.each(selectedPlayer, function (indexP, valueP) {
                                    if (value === valueP) {
                                        trovato = true;
                                        return false;
                                    }
                                });
                                condTeamPlayer = trovato;
                                if (condTeamPlayer) {
                                    return false;
                                }
                            });
                        }
                    } else {

                        // squadre non è vuoto
                        $.each(squadre, function (index, value) {
                            var selectedTeam = infoAction[4].split(",");
                            var trovato = false;
                            $.each(selectedTeam, function (indexT, valueT) {
                                if (value === valueT) {
                                    trovato = true;
                                    return false;
                                }
                            });
                            // se l'ho trovato allora la condizione è soddisfatta
                            condTeamPlayer = trovato;
                            if (!condTeamPlayer) {
                                // se il team dell'azione è diverso da quello selezionato verifico se sono stati specificati giocatori
                                if (giocatori.length > 0) {
                                    // se sono specificati dei giocatori
                                    $.each(giocatori, function (index, value) {
                                        var selectedPlayer = infoAction[1].split(",");
                                        var trovato = false;
                                        $.each(selectedPlayer, function (indexP, valueP) {
                                            if (value === valueP) {
                                                trovato = true;
                                                return false;
                                            }
                                        });
                                        condTeamPlayer = trovato;
                                    });
                                }
                            } else {
                                return false;
                            }
                        });
                    }

                    if (condTeamPlayer) {
                        // alle righe che corrispondono al filtro di ricerca aggiungo la classe eventToShow
                        countRowVisible++;
                        //   nodeInfoSearch.closest("tr").removeClass("uk-hidden");
                        nodeInfoSearch.closest("tr").addClass("eventToShow");
                    } else {
                        // nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                    }


                });

                actionTableMobile.rows().every(function (rowIdx, tableLoop, rowLoop) {
                    var nodeInfoSearch = $(this.node()).find(".infoSearch");
                    var infoAction = nodeInfoSearch.val().split("#");
                    /* SICS2017_PIF||PIF-1,PIF-2#9012,9013#1#1#108#2345#2pt+,pp -> 
                     * [0]=PIF||PIF-1,PIF-2
                     * [1]=9012,9013 -> giocatori
                     * [2]=1 -> half
                     * [3]=1 -> sics/pers
                     * [4]=108 -> squadra
                     * [5]= 2345 -> fixtureId
                     * [6]= 2PT+ -> esito*/
                    var condEsito = true;

                    if (esito.length > 0) {
                        condEsito = false;
                        var resultSplitted = infoAction[6].split(",");
                        $.each(esito, function (index, value) {
                            $.each(resultSplitted, function (indexEsito, valueEsito) {
                                if (valueEsito === value) {
                                    condEsito = true;
                                    return false;
                                }
                            });
                        });
                    }
                    if (!condEsito) {
                        //  $(this).closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                        return true;
                    }
                    var condHalf = true;
                    if (tempo.length > 0) {
                        condHalf = false;
                        $.each(tempo, function (index, value) {
                            if (value === infoAction[2]) {
                                condHalf = true;
                                return false;
                            }
                        });
                    }
                    if (!condHalf) {
                        //   nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                        return true;
                    }
                    var condSource = true;
                    if (sorgenti.length > 0) {
                        condSource = false;
                        $.each(sorgenti, function (index, value) {
                            if (value === infoAction[3]) {
                                condSource = true;
                                return false;
                            }
                        });
                    }
                    if (!condSource) {
                        //   nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                        return true;
                    }
                    var condType = true;
                    if (azioni.length > 0) {
                        condType = false;
                        $.each(azioni, function (index, value) {
                            // split del tipo che è strutturato: SICS2015_ATT_ATT-4 oppure SICS2105_ATT
                            var toSearch = value.split("_");
                            var size = toSearch.length;
                            var trovato = false;
                            if (size > 1) {
                                // individuo il tipo dell'azione che cerchiamo
                                var actionType = infoAction[0].split("||");
                                /* una volta splittato ho: es. SICS2017_PIF||PIF-1,PIF-2
                                 * actionType[0] = SICS2015_PIF
                                 * actionType[1] = PIF-1,PIF-2
                                 * */
                                var configTypePair = actionType[0].split("_");
                                if (configTypePair[0] === toSearch[0]) {

                                    if (size === 2) {
                                        // se la size è 2 allora è un fondamentale e quindi lo confronto con il tipo dell'azione
                                        if (actionType.length === 1) {
                                            trovato = configTypePair[1] === toSearch[1];
                                        }
                                    } else if (size === 3) {
                                        // se la size è 3 allora è un tag quindi vado a confrontare con i tag dell'azione
                                        if (actionType.length > 1) {
                                            // determino i tag dell'azione
                                            var tagsAction = actionType[1].split(",");
                                            $.each(tagsAction, function (indexTag, valueTag) {
                                                if (valueTag === toSearch[2]) {
                                                    trovato = true;
                                                    return false;
                                                }
                                            });
                                        }
                                    }
                                }
                                if (trovato) {
                                    // significa che è un azione da far vedere
                                    condType = true;
                                    return false;
                                }
                            }
                        });
                    }

                    if (!condType) {
                        //   nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                        return true;
                    }

                    var condPartita = true;
                    if (partite.length > 0) {
                        condPartita = false;
                        $.each(partite, function (index, value) {
                            var fixtureId = infoAction[5].split("||")[0];
                            if (value === fixtureId) {
                                condPartita = true;
                                return false;
                            }
                        });
                    }
                    if (!condPartita || (!singleMatch && partite.length == 0)) {
                        //    nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                        return true;
                    }


                    var condTeamPlayer = true;
                    // verifica sulle squadre e giocatori
                    if (squadre.length === 0) {
                        // se non è specificata una squadra
                        if (giocatori.length === 0) {
                            // se non sono specificati giocatori
                            condTeamPlayer = true;
                        } else {

                            // se sono specificati dei giocatori
                            $.each(giocatori, function (index, value) {
                                var selectedPlayer = infoAction[1].split(",");
                                var trovato = false;
                                $.each(selectedPlayer, function (indexP, valueP) {
                                    if (value === valueP) {
                                        trovato = true;
                                        return false;
                                    }
                                });
                                condTeamPlayer = trovato;
                                if (condTeamPlayer) {
                                    return false;
                                }
                            });
                        }
                    } else {

                        // squadre non è vuoto
                        $.each(squadre, function (index, value) {
                            var selectedTeam = infoAction[4].split(",");
                            var trovato = false;
                            $.each(selectedTeam, function (indexT, valueT) {
                                if (value === valueT) {
                                    trovato = true;
                                    return false;
                                }
                            });
                            // se l'ho trovato allora la condizione è soddisfatta
                            condTeamPlayer = trovato;
                            if (!condTeamPlayer) {
                                // se il team dell'azione è diverso da quello selezionato verifico se sono stati specificati giocatori
                                if (giocatori.length > 0) {
                                    // se sono specificati dei giocatori
                                    $.each(giocatori, function (index, value) {
                                        var selectedPlayer = infoAction[1].split(",");
                                        var trovato = false;
                                        $.each(selectedPlayer, function (indexP, valueP) {
                                            if (value === valueP) {
                                                trovato = true;
                                                return false;
                                            }
                                        });
                                        condTeamPlayer = trovato;
                                    });
                                }
                            } else {
                                return false;
                            }
                        });
                    }

                    if (condTeamPlayer) {
                        // alle righe che corrispondono al filtro di ricerca aggiungo la classe eventToShow
                        countRowVisible++;
                        //   nodeInfoSearch.closest("tr").removeClass("uk-hidden");
                        nodeInfoSearch.closest("tr").addClass("eventToShow");
                    } else {
                        // nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        nodeInfoSearch.closest("tr").removeClass("eventToShow");
                    }


                });

                updateDataTable();
                if (!singleMatch) {
                    if (gamesSelected.length == 0) {
                        $("#videoContainer").width($("#media_playerHTML").width());
                        $("#videoContainer").height($("#media_playerHTML").height());
                        $("#videoContainer").addClass("blackout");
                        closeClipViewer();
                    } else {
                        $("#videoContainer").removeClass("blackout");
                    }
                }
            }

            function updateDataTable() {
                $.fn.dataTable.ext.search.push(
                    function (settings, data, dataIndex) {
                        return $(actionTable.row(dataIndex).node()).hasClass('eventToShow');
                    }
                );
                $.fn.dataTable.ext.search.push(
                    function (settings, data, dataIndex) {
                        return $(actionTableMobile.row(dataIndex).node()).hasClass('eventToShow');
                    }
                );
                actionTable.draw();
                actionTableMobile.draw();
                console.log("update");
            }

            var indexActionToPlay = -1; // messo a -1 per fixare problema se fai screenshot senza entrare almeno in una clip
            var isClipStopped = true;
            // imposato da timerClockHTML.set({time: 500, autostart: true});
            var timerClockHTML = $.timer(function () {

                if (playerHTML && !playerHTML.paused && !isClipStopped) {

                    nowTime = playerHTML.currentTime;
                    var timeToEnd = parseFloat(fineAzione - nowTime);
                    var stringTimeToEnd = "00";
                    var sec = timeToEnd;
                    if (timeToEnd > 59) {
                        var min = Math.floor(timeToEnd / 60);
                        if (min > 9) {
                            stringTimeToEnd = min;
                        } else {
                            stringTimeToEnd = "0" + min;
                        }
                        sec = timeToEnd % 60;
                    }
                    if (parseInt(sec) > 9) {
                        stringTimeToEnd += ":" + parseInt(sec);
                    } else {
                        stringTimeToEnd += ":0" + parseInt(sec);
                    }
                    $("#timeToEnd").html(" -" + stringTimeToEnd);
                    var seekSlider = document.getElementById('seek-slider');
                    seekSlider.value = parseInt(seekSlider.max - parseInt(timeToEnd * 2));
                    if (nowTime > fineAzione) {
                        playerHTML.pause();
                        this.stop();
                        
                        var actionsArray = [];
                        if (isRunningOnChecked) {
                            actionsArray = checkedActions;
                        } else {
                            actionsArray = selectedActions;
                        }
                        $('.currentClip').removeClass("currentClip");
                        if (indexActionToPlay < actionsArray.length - 1) {
                            indexActionToPlay++;
                            enableSwitchAction();
                            checkIfNeededToChangePage();
                            
                            sessionStorage['eventIndex'] = indexActionToPlay;

                            $('#rowEventId' + actionsArray[indexActionToPlay] +' td').addClass("currentClip");
                            var params = $("#startData" + actionsArray[indexActionToPlay]).val().split("||");
                            if (params.length > 14) {
                                goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]), $.trim(params[14]));
                            } else {
                                goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]));
                            }
                            updateTooltipSelectedActions("");
                        } else {
                            // DAFARE tentativo di cambiare tab, selezionare tutto e andare in play
                            if ($("#checkboxSelectAllEvents").prop("checked")) {
                                if ($(".paginate_button.next.disabled").length === 0) {
                                    if (isMobile) {
                                        $("#azioni_mobile_next a").trigger('click');
                                    } else {

                                        $("#azioni_next a").trigger('click');
                                    }
                                    $("#checkboxSelectAllEvents").attr("checked", "checked");
                                    $("#checkboxSelectAllEventsSmall").attr("checked", "checked");
                                    jsSelectAllEvents('azioni', true);
                                    startAction('', 0, isRunningOnChecked);
                                }
                            }
                        }
                    }
                }
            });

            function iOS() {

                var iDevices = [
                    'iPad Simulator',
                    'iPhone Simulator',
                    'iPod Simulator',
                    'iPad',
                    'iPhone',
                    'iPod'
                            //,
                            //'Mac68K',
                            //'MacPPC',
                            //'MacIntel'
                ];

                if (!!navigator.platform) {
                    while (iDevices.length) {
                        if (navigator.platform === iDevices.pop()) {
                            return true;
                        }
                    }
                }

                return false;
            }

            function isSafari() {
                return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            }

            //funzione che fa andare l'azione al secondo esatto del match e mette in pausa dopo il tempo di durata dell'azione.			
            function goToActionHTML(start, end, tempo, minutoInizio, durataAzione, nomeSquadra, playerTrim, descAzione, idevent) {
                $(".actionFocus").removeClass("actionFocus");

                playerHTML.currentTime = start;
                if (iOS() || isSafari()) {
                    playerHTML.addEventListener('canplay', function () {
                        playerHTML.currentTime = start;
                        if (playerHTML.currentTime === start) {
                            playerHTML.play();
                        }
                    });
                } else {
                    playerHTML.play();
                }
                fineAzione = end;
                timerClockHTML.set({time: 500, autostart: true});
                var player = '';
                if (playerTrim !== '') {
                    player = " - " + playerTrim;
                }
                var tagFocus = "";
                if (!isMobile) {
                    tagFocus = $("#azioni li").children("#azione").children("#tagAzione-" + idevent).text();
                } else {
                    tagFocus = $("#azioni_mobile li").children("#azione").children("#tagAzione-" + idevent).text();
                }
                desc = tempo + ' - min ' + minutoInizio + ' - ' + durataAzione + 's' + '<br>' + nomeSquadra + player + '<br>' + descAzione + ' ' + tagFocus;
                $('#nome_azione').html(desc);
            }

            //funzione che richiama il corretto goToAction in base al player attivo
            function goToAction(start, end, tempo, minutoInizio, durataAzione, nomeSquadra, playerTrim, descAzione, idevent, idVideo, videoName, videoPath, provider, idFixture, tactStart) {
                var tact = false;
                var hd = false;
                var minVideoQuality = -1;
            <c:forEach items="${mGame}" var="game" varStatus="status" >
                if ("${game.value.idFixture}" == idFixture) {
                    tact = ${game.value.tacticalVideo && !personalSource};
                    hd = ${game.value.hd && !personalSource};
                    minVideoQuality = ${game.value.minVideoQuality};
                }
            </c:forEach>
                var clipDuration = parseInt(end - start);
                // imposto lo slider delle azioni ( valore massimo + valore iniziale )
                var seekSlider = document.getElementById('seek-slider');
                seekSlider.max = clipDuration * 2;
                seekSlider.value = 0;
                seekSlider.dataset.initVideo = start;
                
                if (tact) {
                    $("#btnTACT").removeClass("uk-hidden");
                    if (tactActive && tactStart != 'undefined' && tactStart != '' && tactStart != "-1") {
                        start = tactStart;
                        end = parseInt(start) + clipDuration;
                    }
                } else {
                    if (!$("#btnTACT").hasClass("uk-hidden")) {
                        $("#btnTACT").addClass("uk-hidden");
                    }
                }
                if (hd) {
                    $("#btnHD").removeClass("uk-hidden");
                } else {
                    if (!$("#btnHD").hasClass("uk-hidden")) {
                        $("#btnHD").addClass("uk-hidden");
                    }
                }
                //$("#btnHD").addClass("uk-hidden");
                $('#playerType').html(provider);
                //initVideo(provider, videoName, idVideo); per multipartita
                curVideo = videoName;
                loadVideo(videoPath, hd, tact && tactActive, false);

                var splitDesc = descAzione.split("#");
                var totActionToPlay = 0;
                if (isRunningOnChecked) {
                    totActionToPlay = checkedActions.length;
                } else {
                    totActionToPlay = selectedActions.length;
                }
                if (totActionToPlay == 0) {
                    totActionToPlay = 1;
                }
                var intestazione = $("#clipXdiY").html().replace("X", (indexActionToPlay + 1)).replace("Y", totActionToPlay);
                //"Clip "+ (indexActionToPlay + 1) +" di "+ totActionToPlay;
                $("#spanNameAction").html(intestazione + "<strong>" + splitDesc[0] + "</strong>" + (splitDesc[1] !== "" ? ": " : "") + "<span class='tag'>" + splitDesc[1] + "</span>, " + splitDesc[2] + "<span id='timeToEnd'></span>");
                var tactStartOk = false;
                console.log("DIM: " +${mEventAll.size()});
                if (${mEventAll.size()} > 0 && tactActive && tact) {
            <c:forEach begin="0" end="${mEventAll.size()}" var="item" items="${mEventAll}">
                    if ("${item.mIdEvent}" == idevent) {
                        tactStartOk = "${item.getSecTactStart()}" != "undefined" && "${item.getSecTactStart()}" != "" && "${item.getSecTactStart()}" != "-1";
                <c:set var="list" value="${mEventAll.size()}"/>
                <c:set var="item" value="${mEventAll.size()}"/>
                    }
            </c:forEach>
                }
                if ((tact && tactActive && tactStartOk) || (!tact && tactActive) || !tactActive) {
                    goToActionHTML(start, end, tempo, minutoInizio, durataAzione, nomeSquadra, playerTrim, descAzione, idevent);
                }
            }

            function startAction(id, index, runCheckedActions) {
                // ha la classe nel momento in cui clicco la X di fianco alla barra delle clip
                if (indexActionToPlay === -1) {
                    indexActionToPlay = 0;
                }
                
                if (typeof runCheckedActions != 'undefined' && runCheckedActions) {
                    isRunningOnChecked = true;
                } else {
                    isRunningOnChecked = false;
                }
                
                var actionsArray = [];
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                    //console.log("checkedActions");
                } else {
                    actionsArray = selectedActions;
                    //console.log("selectedActions");
                }
                
                // controllo se ho cliccato su una riga che ha il checked
                if (typeof id != 'undefined' && id !== "") {
                    if (checkedActions.includes(id)) {
                        isRunningOnChecked = true;
                    }
                    
                    // controllo se devo mettere il tattico
                    checkIfNeededToEnableTactical(id);
                } else {
                    var tmpId = actionsArray[indexActionToPlay];
                    // controllo se devo mettere il tattico
                    checkIfNeededToEnableTactical(tmpId);
                }
                
                $("#spanNameAction").removeClass("uk-hidden");
                $('.currentClip').removeClass("currentClip");
                if (id === "") {
                    indexActionToPlay = index;
                    checkIfNeededToChangePage();
                    //console.log("indexActionToPlay: " + indexActionToPlay);
                    if (actionsArray.length > 0) {
                        $("input[id^=infoSearch] + i").addClass("uk-hidden");
                        $('#rowEventId' + actionsArray[indexActionToPlay] +' td').addClass("currentClip");
                        updateTooltipSelectedActions("");
                        if ($("#startData" + actionsArray[indexActionToPlay]).val().length > 0) {
                            var params = $("#startData" + actionsArray[indexActionToPlay]).val().split("||");
                            if (params.length > 14) {
                                goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]), $.trim(params[14]));
                            } else {
                                goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]));
                            }
                        }
                        enableSwitchAction();
                    }
                } else {
                    indexActionToPlay = actionsArray.indexOf(id);
                    //console.log("indexActionToPlay: " + indexActionToPlay + " for id: " + id);
                    checkIfNeededToChangePage();
                    
                    $('#rowEventId' + actionsArray[indexActionToPlay] +' td').addClass("currentClip");
                    var descr = $("#descrToShow" + id).val();
                    var splitDesc = descr.split("#");
                    var description = "<strong>" + splitDesc[0] + "</strong>" + (splitDesc[1] !== "" ? ": " : "") + "<span class='tag'>" + splitDesc[1] + "</span> " + splitDesc[2];
                    updateTooltipSelectedActions(description);
                    var stringParam = $("#startData" + id).val();
                    var params = stringParam.split("||");
                    if (params.length > 14) {
                        goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]), $.trim(params[14]));
                    } else {
                        goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]));
                    }
                    
                    enableSwitchAction();
                }
                
                sessionStorage['eventIndex'] = indexActionToPlay;
            }

            function checkIfNeededToEnableTactical(id) {
                if (tacticalEventIds.length > 0) {
                    if (tacticalEventIds.includes(id)) {
                        if (!$("#btnTACT").hasClass("HDon")) {
                            switchTACT();
                        }
                    } else { // per tutte le clip che non sono tattico allora disattivo
                        if ($("#btnTACT").hasClass("HDon")) {
                            switchHD();
                        }
                    }
                }
            }
            
            function exportActions() {
                if (checkedActions.length > 0) {
                    $("#exportActionsButton").prop("disabled", "disabled");
                    var actionSelected = "";
                    $.each(checkedActions, function (index, value) {
                        var params = [];
                        if (isMobile || iOS()) {
                            actionTableMobile.rows({ filter: 'applied' }).every(function() {
                                if (params.length == 0) {
                                    var data = this.data();
                                    if (value === data[actionTableMobile.columns().nodes().length - 1]) {
                                        params = data[actionTableMobile.columns().nodes().length - 2].split("#");
                                    }
                                } else {
                                    return;
                                }
                            });
                        } else {
                            actionTable.rows({ filter: 'applied' }).every(function() {
                                if (params.length == 0) {
                                    var data = this.data();
                                    if (value === data[actionTable.columns().nodes().length - 1]) {
                                        params = data[actionTable.columns().nodes().length - 2].split("#");
                                    }
                                } else {
                                    return;
                                }
                            });
                        }
                        if (checkedActions !== "") {
                            actionSelected += "-_-" + params[5];
                        } else {
                            actionSelected += params[5];
                        }
                    });
                    
                    // ora prima di iniziare rimetto le azioni nell'ordine giusto
                    // se ad esempio seleziono X clip e poi cambio l'ordinamento di una colonna
                    // viene tenuto l'ordine di prima: quindi qua controllo tutte le righe
                    var checkedActionsSorted = [];
                    actionTable.rows({ filter: 'applied' }).every(function(rowIdx, tableLoop, rowLoop) {
                        var data = this.data();
                        var id = data[actionTable.columns().nodes().length - 1];
                        var isChecked = (data[actionTable.columns().nodes().length - 3] === 'true');

                        if (isChecked) {
                            checkedActionsSorted.push("" + id);
                        }
                    });

                    var tactEnable = "";
                    var multi = $("#exportmulti:checked").length > 0;
                    var hd = $("#exportHD:checked").length > 0;
                    var tact = $("#exportTACT:checked").length > 0;
                    //l'apice manda in errore l'esportazione, per il momento lo tolgo
                    var namefile = $("#nameFileExport").val().replace("'", "").replace("/", " ");
                    var isTempoEffettivo = false;
                    var removeDuplicates = new URL(document.URL).searchParams.get("removeDuplicates");
                    if (typeof removeDuplicates !== 'undefined' && removeDuplicates !== null) {
                        isTempoEffettivo = true;
                    }
                    var ind = 0;
            <c:forEach var="game" items="${mGame}">
                    if (ind == 0) {
                        tactEnable = "${game.value.idFixture}" + "," + "${game.value.tacticalVideo}";
                    } else {
                        tactEnable = tactEnable + "," + "${game.value.idFixture}" + "," + "${game.value.tacticalVideo}";
                    }
                    ind = ind + 1;
            </c:forEach>

                    UIkit.notify("<spring:message code='menu.user.export.convalidazione'/>...", {status: 'success', timeout: 0});
                    $.ajax({
                        type: "POST",
                        url: "/sicstv/user/esportaAzioniPlaylistTv.htm",
                        //data: encodeURI("selectedActions=" + actionSelected + "&tactEnable=" + tactEnable + "&multi=" + multi + "&hd=" + hd + "&tact=" + tact + "&namefile=" + namefile),
                        data: encodeURI("playlistId=" + $("#mPlaylistTvId").val() + "&multi=" + multi + "&eventIds=" + checkedActionsSorted + "&nameFile=" + namefile + "&tactEnable=" + tactEnable + "&tact=" + tact + "&isTempoEffettivo=" + isTempoEffettivo + "&askedMaxQuality=" + $("#exportMaxQuality").is(":checked")),
                        cache: false,
                        success: function (id) {
                            if (id === "noAccess") {
                                UIkit.notify("<spring:message code='menu.user.no.access'/>", {status: 'danger', timeout: 2500});
                            } else if (id !== "") {
                                if (typeof timerProgressStatus != 'undefined') {
                                    timerProgressStatus.stop();
                                }
                                if (typeof sessionStorage['exportIdentifier'] == 'undefined') {
                                    sessionStorage['exportIdentifier'] = "";
                                }

                                sessionStorage['exportIdentifier'] = sessionStorage['exportIdentifier'] + "-" + id;
                                timerProgressStatus = $.timer(function () {
                                    if (!${mExportUseLambda}) {
                                        $.ajax({
                                            type: "GET",
                                            url: "/sicstv/user/isExportRunning.htm",
                                            data: encodeURI("identifier=" + sessionStorage['exportIdentifier']),
                                            cache: false,
                                            success: function (msg) {
                                                UIkit.notify.closeAll();
                                                if (msg === "true") {
                                                    $("#exportRunningNotify").removeClass("uk-hidden");
                                                } else {
                                                    $("#exportRunningNotify").addClass("uk-hidden");
                                                    updateUnseenPlaylist();
                                                    timerProgressStatus.stop();
    //                                                UIkit.notify("<spring:message code='playlist.download.end'/>", {status: 'success', timeout: 1000});

                                                    if (msg !== "false" && typeof sessionStorage['exportIdentifier'] !== "undefined") {
                                                        var strUrl = "/sicstv/user/downloadPlaylist.htm?id=" + msg;
                                                        $.ajax({
                                                            type: "GET",
                                                            url: strUrl,
                                                            contentType: "application/force-download",
                                                            cache: false,
                                                            success: function (msg) {
                                                                if (msg.substr(0, 4) === "true") {
                                                                    UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                                                                } else {
                                                                    UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                                                                    window.location.replace(msg.substr(5));
                                                                    $.unblockUI();
                                                                }
                                                            }
                                                        });
                                                        sessionStorage.removeItem('exportIdentifier');
                                                    }
                                                }
                                            }
                                        });
                                    } else {
                                        $.ajax({
                                            type: "GET",
                                            url: "/sicstv/user/isExportRunningLambda.htm",
                                            cache: false,
                                            success: function (exportInfo) {
                                                UIkit.notify.closeAll();
                                                if (exportInfo.trim().length > 0) {
                                                    var scriptToExecute = exportInfo.substring(exportInfo.indexOf('// START') + 8, exportInfo.indexOf('// END')).trim();
                                                    if (scriptToExecute && scriptToExecute.length > 0) {
                                                        eval(scriptToExecute);
                                                    }
                                                    
                                                    $("#exportRunningNotify").removeClass("uk-hidden");
                                                    if (typeof exportTippyElement === 'undefined') {
                                                        exportTippyElement = tippy("#exportRunningNotify", {
                                                            theme: 'light-border',
                                                            content: exportInfo,
                                                            maxWidth: 500,
                                                            allowHTML: true,
                                                            placement: 'bottom'
                                                        });
                                                    } else {
                                                        exportTippyElement.forEach(function(element) {
                                                            element.setContent(exportInfo);
                                                        });
                                                    }
                                                } else {
                                                    $("#exportRunningNotify").addClass("uk-hidden");
                                                    updateUnseenPlaylist();
                                                    timerProgressStatus.stop();
                                                }
                                            }
                                        });
                                    }
                                });
                                timerProgressStatus.set({time: 2000, autostart: true});

                            }
                            jsReloadUserLimits(); // ricarico i limiti
                            return true;
                        },
                        async: true
                    });

                }
            }
            
            function addToPlaylist(playlistId) {
                if (checkedActions.length > 0) {
                    var name = $('#namePlaylist').val();
                    var description = $('#descriptionPlaylist').val();
                    if (name === "" && typeof playlistId === 'undefined') {
                        UIkit.notify("<spring:message code='playlist.add.failed.no.name'/>", {status: 'warning', timeout: 1000});
                        return;
                    }
                    if (typeof playlistId === 'undefined') {
                        playlistId = "";
                    }
                    
                    var date = new Date();
                    var options = {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    };
                    var formatter = new Intl.DateTimeFormat('it-IT', options);
                    var formattedDate = formatter.format(date);
                    jsShowBlockUI();
                    $.ajax({
                        type: "POST",
                        url: "/sicstv/user/creaPlaylistTv.htm",
                        data: encodeURI("name=" + name + "&description=" + description + "&eventIds=" + checkedActions + "&playlistId=" + playlistId + "&creationDate=" + formattedDate.replace(',', '') + "&tactical=" + tactActive + "&currentPlaylistId=" + $("#mPlaylistTvId").val()),
                        cache: false,
                        success: function (result) {
                            $.unblockUI();
                            if (typeof result !== 'undefined') {
                                if (result === 'noAccess') {
                                    UIkit.notify("<spring:message code='playlist.add.no.access'/>", {status: 'warning', timeout: 1000});
                                } else if (result === 'limitExceeded') {
                                    UIkit.notify("<spring:message code='playlist.add.failed.limit'/>", {status: 'warning', timeout: 1000});
                                } else if (result !== 'false') {                                    
                                    // controllo se devo aggiungere nuovo bottone
                                    if (result.includes("|")) {
                                        var splitted = result.split("|");
                                        var newButton = document.createElement("button");
                                        newButton.setAttribute("onclick", "addToPlaylist('" + splitted[1] + "')");
                                        newButton.className = "uk-button uk-dropdown-close playlistTvButton";
                                        newButton.textContent = splitted[0];

                                        var wrapper = document.getElementById("playlistTvButtonDiv");
                                        wrapper.appendChild(newButton);
                                        UIkit.notify("<spring:message code='playlist.add.created.success'/>", {status: 'success', timeout: 1000});
                                    } else {
                                        UIkit.notify("<spring:message code='playlist.add.success'/>", {status: 'success', timeout: 1000});
                                    }
                                    
                                    // reset status
                                    $('#namePlaylist').val("");
                                    $('#descriptionPlaylist').val("");
                                    emptyCheckedActions();
                                    var currentTable;
                                    if (isMobile || iOS()) {
                                        currentTable = actionTableMobile;
                                    } else {
                                        currentTable = actionTable;
                                    }
                                    currentTable.page(currentTable.page()).draw(false);
                                    jsRefreshMatches('1'); // aggiorno tab playlist
                                    jsReloadUserLimits(); // ricarico i limiti
                                } else {
                                    UIkit.notify("<spring:message code='playlist.add.failed'/> (0xB)", {status: 'danger', timeout: 1000});
                                }
                            } else {
                                UIkit.notify("<spring:message code='playlist.add.failed'/> (0xA)", {status: 'danger', timeout: 1000});
                            }
                        }
                    });
                } else {
                    UIkit.notify("<spring:message code='playlist.add.no.clip'/>", {status: 'warning', timeout: 1000});
                }
            }
            
            // START PLAYLIST STUFF
            
            var clipStartVariation = 0;
            var clipEndVariation = 0;
            var clipStartMsec = 0;
            var clipStartSecond = 0;
            var clipStartMinute = 0;
            var clipStartSecondUpdate = 0;
            var clipStartMinuteUpdate = 0;
            var clipEndMsec = 0;
            var clipEndSecond = 0;
            var clipEndMinute = 0;
            var clipEndSecondUpdate = 0;
            var clipEndMinuteUpdate = 0;
            
            function openDurationChanger() {
                $("#modalModifyClips").addClass("uk-open");
                $("#modalModifyClips").removeClass("uk-hidden");
            }
            
            function closeDurationChanger() {
                $("#modalModifyClips").removeClass("uk-open");
                $("#modalModifyClips").addClass("uk-hidden");
            }
            
            function confirmDurationChange() {
                var startVariation = $("#eventsAdditionalStart").val() || "0";
                var endVariation = $("#eventsAdditionalEnd").val() || "0";
                
                if (startVariation && endVariation) {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/updateClipTv.htm",
                        cache: false,
                        data: encodeURI("playlistId=" + $("#mPlaylistTvId").val() + "&eventId=&note=&startSecVariation=" + startVariation + "&endSecVariation=" + endVariation + "&allClips=true"),
                        success: function (msg) {
                            if (msg === "tooLong") {
                                UIkit.notify("<spring:message code="video.record.error.too.long"/>", {status: 'danger', timeout: 1000});
                            } else {
                                window.location.reload();
                            }
                        },
                        error: function () {
                            UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                        }
                    });
                }
            }
            
            function openModifyClip(id) {
                jsLoadEditClipPage($("#mPlaylistTvId").val(), id);
                $("#modalModifyClip").addClass("uk-open");
                $("#modalModifyClip").removeClass("uk-hidden");
                stopVideo();
                return;
            
                var note = "";
                clipStartMsec = 0;
                clipEndMsec = 0;
                <c:forEach var="list" items="${mEvent}">
                    <c:forEach var="item" items="${list.value}">
                        if (${item.id} == id) {
                            note = "${item.mNote}";
                            clipStartMsec = parseInt("${item.mStart}");
                            clipStartSecond = parseInt("${item.getPeriod_start_second()}");
                            clipStartSecondUpdate = clipStartSecond;
                            clipStartMinute = parseInt("${item.getPeriod_start_minute()}");
                            clipStartMinuteUpdate = clipStartMinute;
                            clipEndMsec = parseInt("${item.mEnd}");
                            clipEndSecond = parseInt("${item.getPeriod_end_second()}");
                            clipEndSecondUpdate = clipEndSecond;
                            clipEndMinute = parseInt("${item.getPeriod_end_minute()}");
                            clipEndMinuteUpdate = clipEndMinute;
                        }
                    </c:forEach>
                </c:forEach>
                    
                $("#saveModifyClip").html("<button style='min-width:80px;' onclick='updateClip(" + id + ");' class='uk-text-large uk-text-bold uk-button js-modal-confirm'><spring:message code='playlist.save'/></button>");
                $("#modalModifyClip").addClass("uk-open");
                $("#modalModifyClip").removeClass("uk-hidden");
                $("#noteClipModify").val(note);
                $("#startMsClipModify").val(clipStartMsec);
                $("#endMsClipModify").val(clipEndMsec);
                
                $("#startClipDelta").html("0");
                $("#startClipMinuteSecond").html(getMinuteSecondLabel(clipStartSecond, clipStartMinute));
                $("#startClipMinuteSecondUpdate").html(getMinuteSecondLabel(clipStartSecondUpdate, clipStartMinuteUpdate));
                $("#endClipDelta").html("0");
                $("#endClipMinuteSecond").html(getMinuteSecondLabel(clipEndSecond, clipEndMinute));
                $("#endClipMinuteSecondUpdate").html(getMinuteSecondLabel(clipEndSecondUpdate, clipEndMinuteUpdate));
                clipStartVariation = 0;
                clipEndVariation = 0;
            }
            
            var maxClipVariation = 30;
            function modifyClipStart(seconds) {
                var variationMsec = (clipStartVariation + seconds) * 1000;
                var difference = clipEndMsec - (clipStartMsec + variationMsec);
                // almeno 1 secondo di clip e max di 30 secondi di "taglio" altrimenti non valida
                if (clipStartMsec + variationMsec > 0 && (clipStartVariation < maxClipVariation || seconds === -1) && difference > 1000) {
                    clipStartVariation = clipStartVariation + seconds;
                }
                
                var secondTmp = clipStartSecond;
                var minuteTmp = clipStartMinute;
                secondTmp = secondTmp - clipStartVariation;
                if (secondTmp < 0) { // passo al minuto prima
                    minuteTmp--;
                    clipStartSecondUpdate = 60 + secondTmp; // uso la + perchè 60 + -2 = 58
                } else if (secondTmp > 59) {// passo al minuto dopo
                    minuteTmp++; 
                    clipStartSecondUpdate = secondTmp - 60;
                } else {
                    clipStartSecondUpdate = secondTmp;
                }
                clipStartMinuteUpdate = minuteTmp;
                
                $("#startClipMinuteSecondUpdate").html(getMinuteSecondLabel(clipStartSecondUpdate, clipStartMinuteUpdate));
                $("#startClipDelta").html(clipStartVariation > 0 ? "+" + clipStartVariation : clipStartVariation);
            }
            
            function modifyClipEnd(seconds) {
                var variationMsec = (clipEndVariation + seconds) * 1000;
                var maxValue = playerHTML.duration * 1000;
                var difference = (clipEndMsec + variationMsec) - clipStartMsec;
                if (clipEndMsec + variationMsec < maxValue && (clipEndVariation < maxClipVariation || seconds === -1) && difference > 1000) {
                    clipEndVariation = clipEndVariation + seconds;
                }
                
                var secondTmp = clipEndSecond;
                var minuteTmp = clipEndMinute;
                secondTmp = secondTmp + clipEndVariation;
                if (secondTmp > 59) { // passo al minuto dopo
                    minuteTmp++;
                    clipEndSecondUpdate = secondTmp - 60;
                } else if (secondTmp < 0) {// passo al minuto prima
                    minuteTmp--;
                    clipEndSecondUpdate = 60 + secondTmp; // uso la + perchè 60 + -2 = 58
                } else {
                    clipEndSecondUpdate = secondTmp;
                }
                clipEndMinuteUpdate = minuteTmp;
                
                $("#endClipMinuteSecondUpdate").html(getMinuteSecondLabel(clipEndSecondUpdate, clipEndMinuteUpdate));
                $("#endClipDelta").html(clipEndVariation > 0 ? "+" + clipEndVariation : clipEndVariation);
            }
            
            function getMinuteSecondLabel(seconds, minutes) {
                var minute = minutes > 9 ? minutes : "0" + minutes;
                var second = seconds > 9 ? seconds : "0" + seconds;
                
                return minute + ":" + second;
            }
            
            function closeModifyClip() {
                $("#modalModifyClip").removeClass("uk-open");
                $("#modalModifyClip").addClass("uk-hidden");
            }
            
            function updateClip(id) {
                var playlistId = $("#mPlaylistTvId").val();
                var note = $("#noteClipModify").val();
//                var startMsec = clipStartMsec + (clipStartVariation * 1000);
//                var endMsec = clipEndMsec + (clipEndVariation * 1000);
                if (typeof playlistId !== 'undefined' && typeof note !== 'undefined') {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/updateClipTv.htm",
                        cache: false,
                        data: encodeURI("&playlistId=" + playlistId + "&eventId=" + id + "&note=" + note + "&startSecVariation=" + clipStartVariation + "&endSecVariation=" + clipEndVariation + "&startSecUpdated=" + clipStartSecondUpdate + "&startMinUpdated=" + clipStartMinuteUpdate + "&endSecUpdated=" + clipEndSecondUpdate + "&endMinUpdated=" + clipEndMinuteUpdate),
                        success: function (msg) {
                            if (msg === "tooLong") {
                                UIkit.notify("<spring:message code="video.record.error.too.long"/>", {status: 'danger', timeout: 1000});
                            } else {
                                window.location.reload();
                            }
                        },
                        error: function () {
                            UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                        }
                    });
                } else {
                    UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                }
            }
            
            function removeClipsFromPlaylist(id) {
                if (checkedActions.length > 0 || typeof id !== 'undefined') {
                    var eventIds = checkedActions;
                    if (typeof id !== 'undefined') {
                        eventIds = id;
                    }
                    
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/eliminaEventiPlaylistTv.htm",
                        data: encodeURI("id=" + $("#mPlaylistTvId").val() + "&eventIds=" + eventIds),
                        cache: false,
                        success: function (result) {
                            if (typeof result !== 'undefined') {
                                if (result !== 'false') {
                                    UIkit.notify("<spring:message code='playlist.delete.event.success'/>", {status: 'success', timeout: 1000});
                                    window.location.reload();
                                } else {
                                    UIkit.notify("<spring:message code='playlist.delete.event.failed'/>", {status: 'danger', timeout: 1000});
                                }
                            } else {
                                UIkit.notify("<spring:message code='playlist.delete.event.failed'/>", {status: 'danger', timeout: 1000});
                            }
                        }
                    });
                }
            }
            
            // END PLAYLIST STUFF

            function jsRefreshMatches(page) {
                var competition = "";
                var team = "";
                if (${not empty mCompetition.id}) {
                    competition = "${mCompetition.id}";
                }
                if (${not empty mTeam.id}) {
                    team = "${mTeam.id}";
                }
                refreshMatches('matches', 'partite', page, competition, team, null, null, true);
            }

            function switchMatchday(step) {

                var valDay = parseInt($("#mDay").val());
                if (step === "prev") {
                    valDay = valDay - 1;
                } else if (step === "next") {
                    valDay = valDay + 1;
                } else {
                    valDay = step;
                }
                
                if (valDay >= 1 && valDay <= parseInt($("#mMaxDay").val())) {
                    //jsLoadingAlpha("#matchDayCalendar");
                    $("#mDay").val(valDay);
                    jsRefreshMatchesCalendar($("#mCompetitionId").val());
                }
            }

            function jsSetButtonSelected(id, type, idBase, comment) {
                /**
                 * type = 0 ->  fondamentale
                 * type = 1 ->  tag
                 * type = 2 ->  squadre 
                 * type = 3 ->  giocatori
                 * type = 4 ->  partite
                 * type = 5 ->  quickSearch
                 * type = 6 ->  autore
                 
                 */
                
                // resetto le azioni selezionate
                emptyCheckedActions();
                //console.log("id: " + id + " type: " + type + " idBase: " + idBase + " comment: " + comment)
                $("#numCheckedEvents").html(0);
                $("#checkedActionsButton").addClass("uk-hidden");
                $("#checkboxSelectAllEvents").removeAttr("checked");
                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                $("#checkboxSelectAllEvents").prop("checked", false);
                $("#checkboxSelectAllEventsSmall").prop("checked", false);
                
                //console.log(id + " - " + type + " - " + idBase);
                if (openTag) {
                    openTag = false;
                } else if (removeFilter) {
                    removeFilter = false;
                    jsRemoveFilter(id, type);
                    applyFilterEvents(true);
                } else {
                    if ($("#" + id).hasClass('uk-button-success')) {
                        $("#" + id).removeClass("uk-button-success");
                        if (type === 2) {
                            $("#quickChoice" + id).removeClass("uk-button-success");
                        }
                    } else {
                        $("#" + id).addClass("uk-button-success");
                        if (type === 2) {
                            $("#quickChoice" + id).addClass("uk-button-success");
                        }
                    }

                    var val = $("#" + id).val();
                    if (type === 0) {
                        // evento
                        // determino se il bottone cliccato è presente o no tra i filtri
                        if ($.inArray(id, eventSelected) === -1) {
                            eventSelected.push(id);
                            $("[id^=" + id + "_]").each(function (i, obj) {
                                if ($.inArray(obj.id, eventSelected) === -1) {
                                    eventSelected.push(obj.id);
                                    if (typeof $("#" + obj.id).attr("disabled") === 'undefined') {
                                        $("#" + obj.id).attr("checked", "checked");
                                    }
                                }
                            });
                        } else {
                            eventSelected.splice($.inArray(id, eventSelected), 1);
                            $("[id^=" + id + "_]").each(function (i, obj) {
                                if ($.inArray(obj.id, eventSelected) !== -1) {
                                    eventSelected.splice($.inArray(obj.id, eventSelected), 1);
                                    $("#" + obj.id).removeAttr("checked");
                                }
                            });
                        }
                        updateNumEventAndTagCount(id);

                    } else if (type === 1) {
                        //tag
                        if ($.inArray(id, eventSelected) === -1) {
                            eventSelected.push(id);
                        } else {
                            eventSelected.splice($.inArray(id, eventSelected), 1);
                        }

                        updateNumEventAndTagCount(id);

                    } else if (type === 2) {
                        // squadra
                        if ($.inArray(val, teamSelected) === -1) {
                            teamSelected.push(val);
                        } else {
                            teamSelected.splice($.inArray(val, teamSelected), 1);
                        }

                        $("#numFilterPlayerTeam").html(teamSelected.length + playerSelected.length);
                        if (teamSelected.length + playerSelected.length > 0) {
                            $("#numFilterPlayerTeam").removeClass("uk-hidden");
                        } else {
                            $("#numFilterPlayerTeam").addClass("uk-hidden");
                        }
                    } else if (type === 3) {
                        //giocatore
                        if ($.inArray(val, playerSelected) === -1) {
                            playerSelected.push(val);
                        } else {
                            playerSelected.splice($.inArray(val, playerSelected), 1);
                        }
                        $("#numFilterPlayerTeam").html(teamSelected.length + playerSelected.length);
                        if (teamSelected.length + playerSelected.length > 0) {
                            $("#numFilterPlayerTeam").removeClass("uk-hidden");
                        } else {
                            $("#numFilterPlayerTeam").addClass("uk-hidden");
                        }
                    } else if (type === 4) {
                        // partite
                        if (typeof comment !== 'undefined') {
                            if (comment === 'all') {
                                gamesSelected = [];
                                document.querySelectorAll(".partiteSelezionate").forEach(function (element) {
                                    var id = element.id
                                    var val = $('#' + id).val();
                                    $('#' + id).addClass("uk-button-success");
                                    gamesSelected.push(val);
                                });
                            } else if (comment === 'none') {
                                gamesSelected = [];
                                document.querySelectorAll(".partiteSelezionate").forEach(function (element) {
                                    var id = element.id
                                    $('#' + id).removeClass("uk-button-success");
                                });
                            }
                        } else {
                            if ($.inArray(val, gamesSelected) === -1) {
                                gamesSelected.push(val);
                            } else {
                                gamesSelected.splice($.inArray(val, gamesSelected), 1);
                            }
                        }

                        $("#numMatchSelected").html(gamesSelected.length);
                        if (gamesSelected.length > 0) {
                            $("#numMatchSelected").removeClass("uk-hidden");
                        } else {
                            $("#numMatchSelected").addClass("uk-hidden");
                        }
                    }
                    applyFilterEvents(type === 0);
                }
                
                updateSelectedActionsArray(true);
                startAction('', 0, isRunningOnChecked);
            }

            // aggionare il numero di tag ed eventi selezionati
            function updateNumEventAndTagCount(id) {
                // se è click su un tag allora vado ad incrementare il contatore dell'evento e non quello totale
                var split = id.split("_");
                var key = split[0] + "_" + split[1];
                var numTag = 0;
                var numEvent = [];
                $.each(eventSelected, function (index, value) {
                    var splittedVal = value.split("_");
                    var keyTag = splittedVal[0] + "_" + splittedVal[1];
                    // determino quanti tag ho selezionato in base ai primi due valori di splittedVal e la key
                    if (splittedVal.length === 3 & key === keyTag) {
                        numTag += 1;
                    }
                    if ($.inArray(keyTag, numEvent) === -1) {
                        numEvent.push(keyTag);
                    }
                });
                //$("#numTagSelected" + key).html(numTag);
                if (numTag > 0) {
                    $("#numTagSelected" + key).removeClass("uk-hidden");
                } else {
                    $("#numTagSelected" + key).addClass("uk-hidden");
                }
                $("#numFilterEvents").html(numEvent.length);
                if (numEvent.length > 0) {
                    $("#numFilterEvents").removeClass("uk-hidden");
                } else {
                    $("#numFilterEvents").addClass("uk-hidden");
                }
            }

            function resetFilter(resetAll) {
                if (resetAll) {
                    $("#timeFirstButton").removeClass("uk-button-success");
                    $("#timeSecondButton").removeClass("uk-button-success");
                    $("#timeFirstQuarter").removeClass("uk-button-success");
                    $("#timeSecondQuarter").removeClass("uk-button-success");
                    $("#timeThirdQuarter").removeClass("uk-button-success");
                    $("#timeFourthQuarter").removeClass("uk-button-success");
                    // squadre
                    jsRemoveFilter('', 2);
                }
                // fondamentale
                jsRemoveFilter('', 1);
                // partite
                jsRemoveFilter('', 4);

                applyFilterEvents(true);
                $("#numEvents").addClass("uk-hidden");
                $("#numEventsSmall").addClass("uk-hidden");
            }

            function jsRemoveFilter(id, type) {
                /**
                 * type = 0 -> elimina tag
                 * type = 1 -> elimina fondamentale
                 * type = 2 -> elimina squadre 
                 * type = 3 -> elimina giocatori
                 * type = 4 -> elimina partite
                 * type = 5 -> elimina quickSearch
                 */

                if (type === 0) {
                    $("#" + id + " + div").find("input:checkbox").each(function () {
                        if ($.inArray($(this).prop('id'), eventSelected) !== -1) {
                            eventSelected.splice($.inArray($(this).prop('id'), eventSelected), 1);
                        }
                        $(this).prop('checked', false);
                    });
                    $("#numTagSelected" + id).addClass("uk-hidden");
                    if (eventSelected.length === 0) {
                        $("#numFilterEvents").addClass("uk-hidden");
                    } else {
                        var numEvent = [];
                        $.each(eventSelected, function (index, value) {
                            var valsplit = value.split("_");
                            var key = valsplit[0] + "_" + valsplit[1];
                            if ($.inArray(key, numEvent) === -1) {
                                numEvent.push(key);
                            }
                        });
                        $("#numFilterEvents").html(numEvent.length);
                    }
                } else if (type === 1) {
                    eventSelected = [];
                    $("#listAzioneRicerca").find("button").each(function () {
                        $(this).removeClass("uk-button-success");
                        jsRemoveFilter($(this).prop("id"), 0);
                    });
                    $("#numFilterEvents").html(0);
                    $("#numFilterEvents").addClass("uk-hidden");
                } else if (type === 2 || type === 3) {
                    teamSelected = [];
                    playerSelected = [];
                    $("#players").find("button").removeClass("uk-button-success");
                    $("#quickChoiceSpan").find("button").removeClass("uk-button-success");
                    $("#comboTeamSelection option:first").removeProp("selected");
                    $("#numFilterPlayerTeam").html(teamSelected.length + playerSelected.length);
                    $("#numFilterPlayerTeam").addClass("uk-hidden");
                } else if (type === 4) {
                    gamesSelected = [];
                    $("#divSelezionate").find("button").each(function () {
                        $(this).addClass("uk-button-success");
                        gamesSelected.push($(this).val());
                    });
                    $("#numMatchSelected").html(gamesSelected.length);
                    $("#numMatchSelected").removeClass("uk-hidden");
                } else if (type === 5) {
                    $("#quickSearchDiv button").each(function () {
                        $(this).removeClass("uk-button-success");
                    });
                }

            }

            function toggleTheatreMode() {
                // se è attiva la modalità cinema la tolgo
                if ($("#video").hasClass("uk-width-1-1")) {
                    $("#video").removeClass("uk-width-1-1");
                    $("#video").addClass("uk-width-5-10");
                    $("#bottomNav").appendTo("#video");
                    $("#bottomNav").addClass("uk-width-1-1");
                    $("#bottomNav").removeClass("uk-width-1-2");

                } else {
                    // se non è attiva la attivo
                    $("#video").addClass("uk-width-1-1");
                    $("#video").removeClass("uk-width-5-10");
                    $("#bottomNav").insertBefore("#sideNav");
                    $("#bottomNav").addClass("uk-width-1-2");
                    $("#bottomNav").removeClass("uk-width-1-1");
                }
            }

            function filterData() {
                readFilterMulti();
                filterMatches('partite', 1);
            }

            var isRunningOnChecked = false;
            var checkedActions = [];
            var selectedActions = [];
            var descrSelectedActions = [];
            function jsSelectAction(id) {
                //var descr = $("#descrToShow" + id).val();
                //var splitDesc = descr.split("#");
                //var description = splitDesc[0] + ": <span class='tag'>" + splitDesc[1] + "</span> " + splitDesc[2];
                if ($.inArray(id, selectedActions) === -1) {
                    selectedActions.push(id);
                    //descrSelectedActions.push(description);
                } else {
                    //descrSelectedActions.splice($.inArray(description, selectedActions), 1);
                    $("#checkboxSelectAllEvents").removeAttr("checked");
                    $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                    $("#checkboxSelectAllEvents").prop("checked", false);
                    $("#checkboxSelectAllEventsSmall").prop("checked", false);
                }
                // nuova gestione selezionati
                updateCheckedArray(id, false, false);
                verifyNextAndPreviousButton(); // se sono all'ultima azione e aggiungo una selezionata non mostra il tasto next attivo

                updateTooltipSelectedActions("");
            }

            function updateCheckedArray(idToCheck, allRows, allRowsCheckValue) {                
                //checkedActions = [];
                var currentTable;
                if (isMobile || iOS()) {
                    currentTable = actionTableMobile;
                } else {
                    currentTable = actionTable;
                }
                
                if (allRows && !allRowsCheckValue) {
                    emptyCheckedActions();
                }
                // in questo modo non passa tutte le righe che sono caricate nella datatable
                // ma fa solo il loop di quelle visibili e poi esce (sembrano essere in ordine quindi non dovrebbe creare problemi)
                var canContinue = true;
                currentTable.rows({ filter: 'applied' }).every(function(rowIdx, tableLoop, rowLoop) {
                    if (!canContinue) {
                        return false; // Interrompe l'iterazione e termina il ciclo
                    }

                    var idFound = false;
                    var data = this.data();
                    var id = data[currentTable.columns().nodes().length - 1];
                    var isChecked = (data[currentTable.columns().nodes().length - 3] === 'true');

                    if (typeof idToCheck !== 'undefined' && (id === idToCheck || allRows)) {
                        if (allRows) {
                            isChecked = allRowsCheckValue;
                            data[currentTable.columns().nodes().length - 3] = allRowsCheckValue ? "true" : "false";
                        } else {
                            isChecked = !isChecked; // così funziona sia per check che per uncheck
                            data[currentTable.columns().nodes().length - 3] = isChecked ? "true" : "false";
                        }
                        idFound = true;
                        
                        if (!allRows) { // se click singola checkbox controllo se devo aggiungere o togliere
                            if (checkedActions.includes(id) == true && !allRowsCheckValue) {
                                checkedActions.splice(checkedActions.indexOf(id), 1);
                            } else {
                                checkedActions.push(id);
                            }
                        } else {
                            if (allRowsCheckValue) { // se invece sto selezionando tutte le checkbox
                                if (checkedActions.includes(id) == false) { // se non è nella lista lo inserisco
                                    checkedActions.push(id);
                                }
                            }
                        }

                        if (allRows) { // bisogna mettere il check alle righe visibili
                            var isVisible = this.nodes().to$().is(':visible');
                            if (typeof isVisible !== 'undefined') {
                                $("#checkbox" + id).prop("checked", isChecked);
                            }
                        }
                        // this.data(data).draw(); // altrimenti non aggiorna il valore nella tabella
                    }
//                    if (typeof isChecked !== 'undefined') {
//                        if (checkedActions.includes(id, checkedActions) == true) {
//                            checkedActions.splice(checkedActions.indexOf(id), 1);
//                        } else {
//                            checkedActions.push(id);
//                        }
//                    }
                    if (idFound && !allRows) {
                        canContinue = false; // Imposta la variabile di controllo a false per interrompere il ciclo
                        return false; // Esce dalla funzione di callback senza effetto sull'iterazione del ciclo
                    }
                });
                
                $("#numCheckedEvents").html(checkedActions.length);
                if (checkedActions.length == 0) {
                    $("#checkedActionsButton").addClass("uk-hidden");
                } else {
                    $("#checkedActionsButton").removeClass("uk-hidden");
                }
            }
            
            function emptyCheckedActions() {
                if (checkedActions.length > 0) {
                    checkedActions = [];
                    $("#numCheckedEvents").html(checkedActions.length);
                    if (checkedActions.length == 0) {
                        $("#checkedActionsButton").addClass("uk-hidden");
                    } else {
                        $("#checkedActionsButton").removeClass("uk-hidden");
                    }
                    // tolgo la spunta da tutte le checkbox
                    $(".squaredCheckboxEvent").find("input[type='checkbox']").removeAttr("checked");

                    // bisogna ora sistemare le righe impostando il valore a false
                    var currentTable;
                    if (isMobile || iOS()) {
                        currentTable = actionTableMobile;
                    } else {
                        currentTable = actionTable;
                    }
                    currentTable.rows({filter: 'applied'}).every( function ( rowIdx, tableLoop, rowLoop ) {
                        var data = this.data();
                        data[currentTable.columns().nodes().length - 3] = "false";
                    });
                }
            }
            
            function updateTooltipSelectedActions(textTooltip) {
                if (textTooltip === "") {
                    $.each(descrSelectedActions, function (key, value) {
                        if (key >= indexActionToPlay) {
                            textTooltip += (key + 1) + ")" + value + "<br>";
                        }
                    });
                }
                $("#spanNameAction").attr("title", textTooltip);
                $("#spanNameAction").attr("data-cached-title", textTooltip);
            }

            function jsSelectAllEvents(element, addCheck) {

                if (addCheck) {
                    $("#checkboxSelectAllEvents").prop("checked", !$("#checkboxSelectAllEvents").prop("checked"));
                    $("#checkboxSelectAllEventsSmall").prop("checked", !$("#checkboxSelectAllEventsSmall").prop("checked"));
                }
                
                var checkboxSelectAllEventsValue = $("#checkboxSelectAllEvents").prop("checked");
                /*$("#" + element + " tbody").find("input:checkbox").each(function () {
                    console.log("mh")
                    if (!$(this).closest("tr").hasClass("uk-hidden")) {
                        var prevVal = $(this).prop("checked");
                        if (addCheck) {
                            if (checkboxSelectAllEventsValue) {
                                $(this).prop("checked", true);
                            } else {
                                $(this).prop("checked", false);
                            }
                        }
                        if (prevVal !== checkboxSelectAllEventsValue || !addCheck) {
                            jsSelectAction($(this).val());
                        }
                    }
                });*/
                jsShowBlockUI();
                setTimeout( function() {
                    updateCheckedArray(0, true, checkboxSelectAllEventsValue);
                    $.unblockUI();
                    
                    updateSelectedActionsArray(false);

                    // DAFARE
                    if (addCheck) {
                        if (!checkboxSelectAllEventsValue) {
                            $("#checkboxSelectAllEvents").removeAttr("checked");
                            $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                        } else {
                            $("#checkboxSelectAllEvents").attr("checked", "checked");
                            $("#checkboxSelectAllEventsSmall").attr("checked", "checked");
                        }
                    }
                }, 500);
            }
            
            function jsUpdateSelectedActions(element) {
                selectedActions = [];
                
                $("#" + element + " tbody").find("input:checkbox").each(function () {
                    if (!$(this).closest("tr").hasClass("uk-hidden")) {
                        var isChecked = $(this).prop("checked");
                        if (isChecked) {
                            jsSelectAction($(this).val());
                        }
                    }
                });
            }

            function emptySelectedActions() {
                $.each(selectedActions, function (key, value) {
                    $("#checkbox" + value).removeAttr("checked");
                    $("#checkbox" + value + "small").removeAttr("checked");
                });
                $("#checkboxSelectAllEvents").removeAttr("checked");
                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                $("#checkboxSelectAllEvents").prop("checked", false);
                $("#checkboxSelectAllEventsSmall").prop("checked", false);
                selectedActions = [];
            }

            function emptyAllActions() {
                $("#checkboxSelectAllEvents").removeAttr("checked");
                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                $("#checkboxSelectAllEvents").prop("checked", false);
                $("#checkboxSelectAllEventsSmall").prop("checked", false);
            }

            function jsDownloadExport(id) {

                var strUrl = "/sicstv/user/downloadPlaylist.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "true") {
                            UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                            window.location.replace(msg.substr(5));
                            $.unblockUI();
                        }
                    }
                });
                sessionStorage.removeItem('exportIdentifier');
            }

            function switchHD() {
                var tactBefore = tactActive;
                if ($("#btnHD").hasClass("HDon")) {
                    // se è in hd allora passo allo standard
                    hdActive = false;
                    $("#btnHD img").attr("src", "/sicstv/images/hd-dark.png");
                    $("#btnHD").removeClass("HDon");
                } else {
                    // se è sd passo all'HD
                    hdActive = true;
                    $("#btnHD img").attr("src", "/sicstv/images/hd-selected.png");
                    $("#btnHD").addClass("HDon");
                }
                tactActive = false;
                $("#btnTACT img").attr("src", "/sicstv/images/tact.png");
                $("#btnTACT").removeClass("HDon");
                var lastValue = 0;
                if (playerHTML) {
                    lastValue = playerHTML.currentTime;
                    if (tactBefore) {
                        lastValue = resyncTime(lastValue, false);
                    }
                }
                loadVideo(curVideoPath, hdActive, tactActive, true);
                if (lastValue !== 0 && !tactBefore) {
                    resumeVideoFromLastValue(lastValue);
                }
            }

            function switchTACT() {
                if ($("#btnTACT").hasClass("HDon")) {
                    // se è in hd allora passo allo standard
                    tactActive = false;
                    hdActive = true;
                    $("#btnTACT img").attr("src", "/sicstv/images/tact.png");
                    $("#btnTACT").removeClass("HDon");
                } else {
                    // se è sd passo a TACT
                    tactActive = true;
                    hdActive = false;
                    $("#btnTACT img").attr("src", "/sicstv/images/tact-selected.png");
                    $("#btnTACT").addClass("HDon");
                }
                $("#btnHD img").attr("src", "/sicstv/images/hd-dark.png");
                $("#btnHD").removeClass("HDon");
                var lastValue = 0;
                if (playerHTML) {
                    lastValue = playerHTML.currentTime;
                    if (tactActive) {
                        lastValue = resyncTime(lastValue, true);
                    } else {
                        lastValue = resyncTime(lastValue, false);
                    }
                }
                loadVideo(curVideoPath, hdActive, tactActive, true);
            }

            function resyncTime(startToSync, toTact) {
                var result = startToSync;
                //DISABILITATO
                //TODO --> PRIMA AGGIUNGERE CAMPI SU DB
                if (false) {
                    var tactStart = 0;
                    var tvStart = 0;
                    /*
                     
                     */
                    if (toTact) {

                    } else {

                    }
                    if (result < 0) {
                        result = 0;
                    }
                }
                return result;
            }

            function resumeVideoFromLastValue(start) {
                if (typeof playerHTML !== 'undefined') {
                    playerHTML.currentTime = start;
                    if (iOS() || isSafari()) {
                        playerHTML.addEventListener('canplay', function () {
                            playerHTML.currentTime = start;
                            if (playerHTML.currentTime === start) {
                                playerHTML.play();
                            }
                        });
                    } else {
                        playerHTML.play();
                    }
                    fineAzione = end;
                    timerClockHTML.set({time: 500, autostart: true});
                }
            }

            function changeButtonsVisibility(actions, isPlaylistTab) {

                if (actions) {
                    $("#playActions").removeClass("uk-hidden");
                    $("#doExportAction").removeClass("uk-hidden");
                    $("#selectedMatches").addClass("uk-hidden");
                    $("#addToMulti").addClass("uk-hidden");
                    $("#doAddToPlaylistAction").removeClass("uk-hidden");
                    $("#doRemoveFromPlaylistAction").removeClass("uk-hidden");
                    if (checkedActions.length > 0) {
                        $("#checkedActionsButton").removeClass("uk-hidden");
                    }
                } else {
                    $("#playActions").addClass("uk-hidden");
                    $("#doExportAction").addClass("uk-hidden");
                    $("#doAddToPlaylistAction").addClass("uk-hidden");
                    $("#selectedMatches").addClass("uk-hidden");
                    $("#doRemoveFromPlaylistAction").addClass("uk-hidden");
                    if (typeof isPlaylistTab === 'undefined') {
                        $("#addToMulti").removeClass("uk-hidden");
                    } else if (isPlaylistTab) {
                        $("#addToMulti").addClass("uk-hidden");
                    }
                    $("#checkedActionsButton").addClass("uk-hidden");
                }
            }

            function changeAction(next) {
                var newIndex = indexActionToPlay;
                var actionsArray = [];
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                } else {
                    actionsArray = selectedActions;
                }
                
                $('.currentClip').removeClass("currentClip");
                if (next) {
                    if (indexActionToPlay + 1 < actionsArray.length) {
                        indexActionToPlay++;
                        startAction('', indexActionToPlay, isRunningOnChecked);
                    }
                } else {
                    if (indexActionToPlay > 0) {
                        indexActionToPlay--;
                        startAction('', indexActionToPlay, isRunningOnChecked);
                    }
                }
                enableSwitchAction();
            }

            function enableSwitchAction() {
                checkPlayButton();
                verifyNextAndPreviousButton();
                $("#seek-slider").removeProp("disabled");
                $("#closeClipViewer").removeProp("disabled");
                isClipStopped = false;
            }
            
            function verifyNextAndPreviousButton() {
                var actionsArray = [];
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                } else {
                    actionsArray = selectedActions;
                }
            
                if (indexActionToPlay < actionsArray.length - 1) {
                    $("#nextActionSwitch").removeProp("disabled");
                } else {
                    $("#nextActionSwitch").prop("disabled", "disabled");
                }
                if (indexActionToPlay == 0) {
                    $("#prevActionSwitch").prop("disabled", "disabled");
                } else {
                    $("#prevActionSwitch").removeProp("disabled");
                }
            }

            function selectTeamPreferred() {
                var teamId = $("#comboTeamSelection option:selected").val();
                jsSetButtonSelected('team' + teamId, 2);
            }

            function showQuickSearch(type) {

                var valToSearch = "SICS2015_";
                if (${curUser.sportId == 1}) {
                    valToSearch = "BASKET2017_";
                }

                var splitType = type.split("#");
                resetFilter(false);
                if (splitType.length > 1) {
                    var splitTag = splitType[1].split("@");

                    $.each(splitTag, function (index, value) {
                        $("#" + valToSearch + splitType[0] + "_" + value).attr("checked", "checked");
                        //console.log("1. attivo " + valToSearch + splitType[0] + "_" + value);
                        jsSetButtonSelected(valToSearch + splitType[0] + "_" + value, 1);
                    });
                } else {
                    //console.log("2. attivo " + valToSearch + splitType[0]);
                    jsSetButtonSelected(valToSearch + splitType[0], 0);
                }

                //var ids="";
            <c:forEach items="${mGame}" var="game" varStatus="status" >
                if (ids.length > 0) {
                    ids += "-";
                }
                ids += "${game.value.idFixture}";
            </c:forEach>
                //console.log(type)
                var strUrl = "/sicstv/user/eventlist.htm?id=" + ids + "&event=" + escape(type);
    
                // resetto le azioni selezionate
                emptyCheckedActions();
                $("#numCheckedEvents").html(0);
                $("#checkedActionsButton").addClass("uk-hidden");
                
                $("#checkboxSelectAllEvents").removeAttr("checked");
                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                $("#checkboxSelectAllEvents").prop("checked", false);
                $("#checkboxSelectAllEventsSmall").prop("checked", false);
                
                updateSelectedActionsArray(true);
                startAction('', 0, isRunningOnChecked);
            }

            if (document.addEventListener) {
                document.addEventListener('contextmenu', function (e) {
                    e.preventDefault();
                }, false);
            } else {
                document.attachEvent('oncontextmenu', function () {
                    window.event.returnValue = false;
                });
            }

            function updatePlayerFromSeekSlider() {
                var seekSlider = document.getElementById('seek-slider');
                var initialClipSeconds = seekSlider.dataset.initVideo;
                var currentSliderValue = seekSlider.value;
                
                var timeToSet = (parseInt(initialClipSeconds) + parseFloat(currentSliderValue / 2));
                if (playerHTML.currentTime !== timeToSet) {
                    playerHTML.pause();
                    playerHTML.currentTime = timeToSet;
                    timerClockHTML.set({time: 500, autostart: true});
                }
            }
            
            function stopVideo() {
                if (!playerHTML.paused) {
                    playerHTML.pause();
                }
            }
            
            function resumeVideo() {
                if (playerHTML.paused) {
                    // uso un delay altrimenti se tengo premuto e mi muovo molto piano nella barra poi non esegue correttamente
                    setTimeout( function() {
                        playerHTML.play();
                    }, 250);
                }
            }
            
            function closeClipViewer() {
                $("#nextActionSwitch").prop("disabled", "disabled");
                $("#prevActionSwitch").prop("disabled", "disabled");
                $("#seek-slider").prop("disabled", "disabled");
                $("#closeClipViewer").prop("disabled", "disabled");
                
                var actionsArray = [];
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                } else {
                    actionsArray = selectedActions;
                }
                $('.currentClip').removeClass("currentClip");
                $("#spanNameAction").addClass("uk-hidden");
                var seekSlider = document.getElementById('seek-slider');
                seekSlider.value = 0;
                isClipStopped = true;
            }
            
            function updateSelectedActionsArray(resetTablePage) {
                selectedActions = [];
                if (isMobile || iOS()) {
                    actionTableMobile.rows({ filter: 'applied' }).every(function() {
                        var data = this.data();
                        // actionTableMobile.columns().nodes().length - 1 return the last column index
                        selectedActions.push(data[actionTableMobile.columns().nodes().length - 1]);
                    });
                    
                    // per risoluzione bug riporto a pagina iniziale della datatable
                    // inoltre aggiornando la pagina verrebbe fuori la spunta del play nella prima riga, quindi meglio tenere
                    if (actionTableMobile.page() !== 0 && resetTablePage) {
                        actionTableMobile.page(0).draw(false);
                    }
                } else {
                    actionTable.rows({ filter: 'applied' }).every(function() {
                        var data = this.data();
                        // actionTable.columns().nodes().length - 1 return the last column index
                        selectedActions.push(data[actionTable.columns().nodes().length - 1]);
                    });
                    
                    // per risoluzione bug riporto a pagina iniziale della datatable
                    // inoltre aggiornando la pagina verrebbe fuori la spunta del play nella prima riga, quindi meglio tenere
                    if (actionTable.page() !== 0 && resetTablePage) {
                        actionTable.page(0).draw(false);
                    }
                }
            }
            
            function checkIfNeededToChangePage() {
                var currentTable;
                if (isMobile || iOS()) {
                    currentTable = actionTableMobile;
                } else {
                    currentTable = actionTable;
                }
            
                var currentPageNumber = currentTable.page();
                var minIndexValue = currentPageNumber * currentTable.page.len();
                var maxIndexValue = currentPageNumber * currentTable.page.len() + currentTable.page.len();
                
                var tmpIndexActionToPlay = indexActionToPlay;
                if (isRunningOnChecked) {
                    tmpIndexActionToPlay = selectedActions.indexOf(checkedActions[indexActionToPlay]);
                }
                
                var indexPageToSelect = parseInt(tmpIndexActionToPlay / currentTable.page.len());
                
                if(indexPageToSelect!==currentPageNumber){
                    currentTable.page(indexPageToSelect).draw(false);
                }
            }
            
            function getScreenshot() {
                UIkit.notify("Creazione screenshot in corso...", {status: 'primary', timeout: 1000});
                var videoPath = "";
                // curVideoPath | curVideoHDPath | curVideoTACTPath
                var id = $('.HDon')[0].id;
                if (id !== 'undefined') {
                    if (id.includes('HD')) {
                        videoPath = curVideoHDPath;
                    } else if (id.includes('TACT')) {
                        videoPath = curVideoTACTPath;
                    }
                } else {
                    videoPath = curVideoPath;
                }
                
                if (videoPath !== "") {
                    var needToResume = true;
                    if (playerHTML.paused) {
                        needToResume = false;
                    } else {
                        playerHTML.pause();
                    }
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/videoTakeScreenshot.htm",
                        //data: encodeURI("selectedActions=" + actionSelected + "&tactEnable=" + tactEnable + "&multi=" + multi + "&hd=" + hd + "&tact=" + tact + "&namefile=" + namefile),
                        data: encodeURI("videoPath=" + videoPath.replaceAll("&", "-_-") + "&milliseconds=" + playerHTML.currentTime),
                        cache: false,
                        crossDomain: true,
                        success: function (imgPath) {
                            if (imgPath) {
                                var link = document.createElement("a");
                                link.href = imgPath;
                                link.download = "immagine.png";
                                link.click();
                                UIkit.notify("Screenshot creato con successo", {status: 'success', timeout: 1000});
                                
                                if (needToResume) {
                                    playerHTML.play();
                                }
                            }
                        },
                        error: function () {
                            UIkit.notify("Errore inaspettato durante la creazione dello screenshot", {status: 'danger', timeout: 1000});
                            if (needToResume) {
                                playerHTML.play();
                            }
                        },
                        async: true
                    });
                }
            }
            
            function checkPlayButton() {
                if (playerHTML.paused) {
                    $("#resumeStopButton").attr("title", "<spring:message code='video.play'/>");
                    $("#resumeStopButton i").attr("class", "uk-icon-play");
                } else {
                    $("#resumeStopButton").attr("title", "<spring:message code='video.stop'/>");
                    $("#resumeStopButton i").attr("class", "uk-icon-stop");
                }
            }
            
            function updateTableNavigation() {
                var actionsArray = [];
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                } else {
                    actionsArray = selectedActions;
                }

                var previousEventId = actionsArray[indexActionToPlay];
                updateSelectedActionsArray(false);
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                } else {
                    actionsArray = selectedActions;
                }
                if (actionsArray.indexOf(previousEventId) != -1) {
                    indexActionToPlay = actionsArray.indexOf(previousEventId);

                    $('.currentClip').removeClass("currentClip");
                    $('#rowEventId' + actionsArray[indexActionToPlay] +' td').addClass("currentClip");
                }
                verifyNextAndPreviousButton();
            }
            
            function checkExport() {
                if (checkedActions.length > 0) {
                    $("#exportActionNoEvents").addClass("uk-hidden");
                    var result = isValidExportRequest(checkedActions);
                    var showMainMessage = false;
                    
                    if (result.includes("TEMPO_EFFETTIVO")) {
                        $("#exportEffettivoWarning").removeClass("uk-hidden");
                    } else {
                        $("#exportEffettivoWarning").addClass("uk-hidden");
                    }
                    if (result.includes("AMOUNT")) {
                        showMainMessage = true;
                        $("#firstExportRequirement").attr("src", "/sicstv/images/cancel.png");
                    } else {
                        //$("#firstExportRequirement").attr("src", "/sicstv/images/confirm.png");
                        $("#firstExportRequirement").addClass("uk-hidden");
                        $("#firstExportRequirementLabel").addClass("uk-hidden");
                    }
                    if (result.includes("TOTAL_TIME")) {
                        showMainMessage = true;
                        $("#secondExportRequirement").attr("src", "/sicstv/images/cancel.png");
                    } else {
                        //$("#secondExportRequirement").attr("src", "/sicstv/images/confirm.png");
                        $("#secondExportRequirement").addClass("uk-hidden");
                        $("#secondExportRequirementLabel").addClass("uk-hidden");
                    }
                    if (result.includes("MAX_CLIP_TIME")) {
                        showMainMessage = true;
                        $("#thirdExportRequirement").attr("src", "/sicstv/images/cancel.png");
                    } else {
                        //$("#thirdExportRequirement").attr("src", "/sicstv/images/confirm.png");
                        $("#thirdExportRequirement").addClass("uk-hidden");
                        $("#thirdExportRequirementLabel").addClass("uk-hidden");
                    }
                    if (result.includes("MIN_CLIP_TIME")) {
                        showMainMessage = true;
                        $("#fourthExportRequirement").attr("src", "/sicstv/images/cancel.png");
                    } else {
                        //$("#fourthExportRequirement").attr("src", "/sicstv/images/confirm.png");
                        $("#fourthExportRequirement").addClass("uk-hidden");
                        $("#fourthExportRequirementLabel").addClass("uk-hidden");
                    }
                    
                    if (!showMainMessage) {
                        $("#exportActionsButton").removeAttr("disabled");
                        $("#exportRequirementDiv").addClass("uk-hidden");
                    } else {
                        $("#exportActionsButton").prop("disabled", "disabled");
                        $("#exportRequirementDiv").removeClass("uk-hidden");
                    }
                    
                    if (!$("#exportRunningNotify").hasClass("uk-hidden")) {
                        $("#exportRunningMessage").removeClass("uk-hidden");
                    } else {
                        $("#exportRunningMessage").addClass("uk-hidden");
                    }
                } else {
                    $("#exportActionNoEvents").removeClass("uk-hidden");                    
                }
            }

            function toggleVideoAudio() {
                playerHTML.muted = !playerHTML.muted;

                if (playerHTML.muted) {
                    $("#toggleAudioButton").find("i").attr("class", "uk-icon-volume-up");
                    $("#toggleAudioButton").attr("title", "<spring:message code="player.active.audio"/>");
                } else {
                    $("#toggleAudioButton").find("i").attr("class", "uk-icon-volume-off");
                    $("#toggleAudioButton").attr("title", "<spring:message code="player.disable.audio"/>");
                }
            }

        </script>
    </head>

    <body>
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>
        <!-- campi nascosti relativi alla partita nel caso sia unica e non multipartita-->

        <input type="hidden" id="mGame" value="${mGame}" />
        <input type="hidden" id="mGameAmount" value="${mGame.size()}" />
        <input type="hidden" id="mTeam" value="${mTeam}" />
        <input type="hidden" id="mGoals" value="${mGoals}" />
        <input type="hidden" id="idComp" value="${mCompetition.id}" />
        <input type="hidden" id="mEventFilter" value="${mEventFilter}" />
        <input type="hidden" id="idPlayer" value="${mPlayer.id}" /> 
        <input type="hidden" id="mEventPresent" value="${mEventPresent}" /> 
        <input type="hidden" id="mSer" value="${mSer}" /> 
        <input type="hidden" id="mPlaylistTvId" value="${mPlaylistTvId}"/>

        <div class="video-pre-container" id="preLoadDiv">
            <div>
                <span><spring:message code='menu.user.loading'/></span>
            </div>
            <div class="video-pre-progress-bar-container" style="text-align: center">
                <div class="video-pre-progress-bar" id="video-pre-bar" style="width: 0%">
                    <div style="width: 50vh; padding-top: 3px" id="video-pre-bar-value"></div>
                </div>
            </div>
        </div>
        
        <div class="uk-grid uk-hidden" id="centralGrid">

            <!--Video player-->
            <div id="video" class="uk-width-5-10 uk-responsive-width" >
                <div id="videoTools" >
                    <span id="clipXdiY" class="uk-hidden"><spring:message code='video.clipavanzamento'/></span>
                    <span id="spanNameAction" class="topBarVideo" title="  "></span>
                    <button id='btnHD' class="uk-hidden uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small" onclick="switchHD();" title="<spring:message code='tooltip.HD'/> ${mGame[0].hd}">
                        <img src='/sicstv/images/hd-dark.png'/>
                    </button>
                    <button id='btnTACT' class="uk-hidden uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small" onclick="switchTACT();" title="<spring:message code='tooltip.TACT'/>">
                        <img src='/sicstv/images/tact.png'/>
                    </button>
                    <button id='btnCinema' class="uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small" onclick="toggleTheatreMode();" title="<spring:message code='tooltip.cinema'/>">
                        <i class="uk-icon-desktop"></i>
                    </button>
                    <button id='btnCinema' class="uk-hidden uk-button uk-button-small uk-float-right" onclick="getScreenshot();" title="Cattura schermata video">
                        <i class="uk-icon-camera"></i>
                    </button>
                </div>
                <div id="videoContainer">
                    <!-- il video viene caricato qui da jQuery-->
                    <video id='media_playerHTML' class='uk-width-1-1' data-setup='{}' preload muted autoplay playsinline controls controlsList="nodownload" style='background: #000;'>
                    </video>
                </div>
                <div class="tagEvento video-player-tools" id="videoPlayerTools">
                    <button onclick="changeAction(false);" id="prevActionSwitch" class="uk-button uk-button-small uk-margin-small-left uk-margin-small-top uk-margin-small-bottom uk-hidden-medium uk-hidden-small" disabled title="<spring:message code='calendar.precedente'/>"><i class="uk-icon-angle-double-left"></i> </button>
                    <button onclick="resumeStopVideo();" id="resumeStopButton" class="uk-button uk-button-small uk-margin-small uk-hidden-small" title="<spring:message code='video.stop'/>" style="min-width: 33px"><i class="uk-icon-stop"></i></button>
                    <button onclick="changeAction(true);" id="nextActionSwitch" class="uk-button uk-button-small uk-margin-small uk-hidden-medium uk-hidden-small" disabled title="<spring:message code='calendar.successiva'/>"><i class="uk-icon-angle-double-right"></i> </button>
                    <button onclick="toggleVideoAudio();" id="toggleAudioButton" title="<spring:message code="player.active.audio"/>" class="uk-button uk-button-small uk-margin-small uk-hidden-small"><i class="uk-icon-volume-up"></i></button>
                    <input class="video-player-tools-child" data-initVideo="0" type="range" id="seek-slider" min="0" step="1" value="0" onmousedown="stopVideo()" onmouseup="resumeVideo()" oninput="updatePlayerFromSeekSlider()" onchange="updatePlayerFromSeekSlider()" ontouchend="updatePlayerFromSeekSlider()" disabled>
                    <button onclick="closeClipViewer()" id="closeClipViewer" class="uk-button uk-button-small uk-margin-small uk-hidden-medium uk-hidden-small" disabled><i class="uk-icon-close"></i></button>
                </div>
                <!-- filtro azioni / info partita / ricerca / analisi -->
                <div id="bottomNav" class="uk-width-1-1 uk-hidden" id="filter">
                    <ul id="bottomNavTab" class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#bottomNavContent'}">
                        <li id="liRicerca" class="uk-active"><a href="#"><spring:message code="video.ricerca"/></a></li>
                        <li id="liFiltri"><a href="#"><spring:message code="video.ricercaAvanzata"/> <span id="numFilterEvents" class="uk-badge uk-hidden">0</span></a></li>
                        <li id="liFiltroGiocatori"><a href="#"><spring:message code="video.filtrogiocatori"/> <span id="numFilterPlayerTeam" class="uk-badge uk-hidden">0</span></a></li>
                        <li id="liSelezionate"><a href="#"><spring:message code="video.selezionate"/> <span id="numMatchSelected" class="uk-badge">0</span></a></li>
                        <li id="liEventiSmall" class="uk-visible-small"><a href="#"><spring:message code="video.eventi"/><span id="numEventsSmall" class="uk-badge uk-hidden">0</span></a></li>						
                        <!--li id="liInfoMatch"><a href="#"><spring:message code="video.infopartita"/></a></li-->
                        <!--li><a href="#"><spring:message code="menu.area.analisi"/></a></li-->
                    </ul>
                    <span id="btnFiltriRapidi" class="uk-float-right">
                        <span>
                            <button id="sourceSics" class="uk-button buttonTag" value="-1" onclick="jsSetButtonSelected('sourceSics', 6);"><i><img width="16" height="16" src="/sicstv/images/tag.svg"/></i> SICS</button>
                            <button id="sourcePersonal" class="uk-button buttonTag" value="${curUser.groupsetId}" onclick="jsSetButtonSelected('sourcePersonal', 6);"><i><img width="16" height="16" src="/sicstv/images/tag.svg"/></i> <spring:message code="video.personale"/></button>
                        </span>
                        <span id="quickChoiceSpan" class="uk-form">
                            <c:choose>
                                <c:when test="${mListPlayers.size() == 2}">
                                    <c:forEach items="${mListPlayers}" var="list" varStatus="status" >
                                        <button id="quickChoiceteam${list.key.id}" class="uk-button buttonTag" onclick="jsSetButtonSelected('team${list.key.id}', 2);" value="${list.key.id}" title="${list.key.name}">
                                            ${list.key.name}
                                        </button>
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    <select id="comboTeamSelection" class="uk-form-select" onselect="selectTeamPreferred();">
                                        <option id="empty"></option>
                                        <c:forEach items="${mListPlayers}" var="list" varStatus="status" >
                                            <option id="optteam${list.key.id}" value="${list.key.id}" title="${list.key.name}">${list.key.name}</option>
                                        </c:forEach>
                                    </select>
                                </c:otherwise>
                            </c:choose>
                        </span>
                        <c:choose>
                            <c:when test="${mUser.sportId == '0'}">
                                <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}">
                                    <button id="selectedMatches" class="uk-button uk-button-small" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.selezionati'/>"><i class="uk-icon-bars"></i></button> 
                                    <!--<a href="#" id="selectedMatches" class="uk-button uk-button-small" ><i class="uk-icon-bars  "></i> 0 </a>-->
                                    <span id="spanSelAll" class="uk-dropdown">
                                        <ul class="uk-nav uk-nav-navbar">
                                            <li class="selAllSubnav"><a onclick="jsSetButtonSelected('', 4, 0, 'all');" style="text-align: left"><i class="uk-icon-check-square-o uk-margin-right"></i><spring:message code="menu.selezionatutto"/></a></li>
                                            <div class="uk-nav-divider"></div>
                                            <li class="selAllSubnav"><a onclick="jsSetButtonSelected('', 4, 0, 'none');" style="text-align: left"><i class="uk-icon-square-o uk-margin-right"></i><spring:message code="menu.deselezionatutto"/></a></li>
                                        </ul>							
                                    </span>
                                </span>
                                <button id="timeFirstButton" class="uk-button uk-button-small" onclick="jsSetButtonSelected('timeFirstButton', 5);" title="<spring:message code="video.primot"/>"><i class="uk-icon-hourglass-start  "></i></button>
                                <button id="timeSecondButton" class="uk-button uk-button-small" onclick="jsSetButtonSelected('timeSecondButton', 5);" title="<spring:message code="video.secondot"/>"><i class="uk-icon-hourglass-end  "></i></button>
                            </c:when>
                            <c:when test="${mUser.sportId == '1'}">
                                <button id="timeFirstQuarter" class="uk-button uk-button-small" onclick="jsSetButtonSelected('timeFirstQuarter', 5);" title="<spring:message code="video.primoQ"/>">1Q</button>
                                <button id="timeSecondQuarter" class="uk-button uk-button-small" onclick="jsSetButtonSelected('timeSecondQuarter', 5);" title="<spring:message code="video.secondoQ"/>">2Q</button>
                                <button id="timeThirdQuarter" class="uk-button uk-button-small" onclick="jsSetButtonSelected('timeThirdQuarter', 5);" title="<spring:message code="video.terzoQ"/>">3Q</button>
                                <button id="timeFourthQuarter" class="uk-button uk-button-small" onclick="jsSetButtonSelected('timeFourthQuarter', 5);" title="<spring:message code="video.quartoQ"/>">4Q</button>
                            </c:when>
                        </c:choose>



                        <button id="btnResetFiltri" class="uk-button uk-button-small uk-margin-small-right" onclick="resetFilter(true);" title="<spring:message code="video.resetfiltri"/>"><i class="uk-icon-reply  "></i></button>
                        <span onclick="applyFilterEvents(true);" id="btnApplica" class="uk-button uk-button-small uk-float-right uk-margin-right uk-hidden" >Applica</span>
                        <span onclick="resetFilter(true);" id="btnResetFiltri" class="uk-button uk-button-small uk-float-right  uk-margin-right uk-hidden" >Reset</span>
                    </span>
                    <ul id="bottomNavContent" class="uk-switcher uk-margin-small-left uk-margin-small-right uk-clear-both uk-text-center">
                        <li id="liRicercaContent" class="uk-text-center uk-margin-top">
                            <div id="quickSearchDiv uk-hidden-small" >
                                <c:forEach items="${filterToShow['SICS'][0]}" var="list" varStatus="status" >
                                    <button class='uk-button width-1-2-x uk-hidden-small uk-margin-small-top text-center-middle' <c:if test="${list.value[1] == 'false'}">disabled</c:if> onclick="showQuickSearch('${list.key}');"><i class="uk-icon-video-camera"></i><spring:message code="${list.value[0]}"/> (${list.value[2]})</button>
                                </c:forEach>
                            </div>
                            <div id="quickSearchDiv uk-visible-small " >
                                <c:forEach items="${filterToShow['SICS'][0]}" var="list" varStatus="status" >
                                    <button class='uk-button width-1-2 uk-visible-small uk-margin-small-top text-center-middle' <c:if test="${list.value[1] == 'false'}">disabled</c:if> onclick="showQuickSearch('${list.key}');"><i class="uk-icon-video-camera"></i><spring:message code="${list.value[0]}"/> (${list.value[2]})</button>
                                </c:forEach>
                            </div>
                        </li>

                        <li id="liFiltriContent" class="uk-active">
                            <c:if test="${mUser.sportId == '1'}">
                                <div id="resultDiv" style="float:right; margin: 5px;">
                                    <button id="btn2ptPos" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btn2ptPos', 5);" value="2PT+">2PT+</button>
                                    <button id="btn2ptNeg" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btn2ptNeg', 5);" value="2PT-">2PT-</button>
                                    <button id="btn3ptPos" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btn3ptPos', 5);" value="3PT+">3PT+</button>
                                    <button id="btn3ptNeg" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btn3ptNeg', 5);" value="3PT-">3PT-</button>
                                    <button id="btnFF" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btnFF', 5);" value="FF">FF</button>
                                    <button id="btnFS" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btnFS', 5);" value="FS">FS</button>
                                    <button id="btnPP" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btnPP', 5);" value="PP">PP</button>
                                    <button id="btnREC" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btnREC', 5);" value="REC">REC</button>

                                </div>
                            </c:if>
                            <div class="uk-margin-bottom" id="configButton">
                                <div id="ricercaAzione" class="ui-corner-all" >
                                    <!-- CONTENITORE EVENTI DELLA MASCHERA UTILIZZATA PER FARE L'ANALISI -->
                                    <div id="listAzioneRicerca">
                                        <c:set var="iteratorPanel" value="0"/>
                                        <c:forEach var="item" items="${mButtonToShow}">
                                            <c:if test="${iteratorPanel == 0}">
                                                <div uk-accordion data-uk-accordion="{collapse: false,showfirst: true}">
                                                </c:if>
                                                <c:if test="${iteratorPanel>0}">
                                                    <div uk-accordion data-uk-accordion="{collapse: false,showfirst: false}" class="uk-margin-remove">
                                                    </c:if>
                                                    <c:set var="chiave" value="${item.configName}"/>
                                                    <c:set var="chiaveTrim" value="${fn:replace(chiave,'.xml', '')}"/>
                                                    <c:choose>
                                                        <c:when test="${item.configName == 'SICS2015'}">
                                                            <span class="label-config uk-width-1-1 uk-accordion-title uk-margin-small-top uk-margin-bottom-remove">SICS</span>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <span class="label-config uk-width-1-1 uk-accordion-title uk-margin-small-top uk-margin-bottom-remove">${item.configName}</span>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="uk-accordion-content uk-width-1-1 uk-padding-remove">
                                                        <div id="tabs-${iteratorPanel}" class="actionFor${chiaveTrim} uk-accordion uk-text-left">
                                                            <c:set var="iteratorPanel" value="${iteratorPanel+1}"/>
                                                            <c:set var="tagID" value="0"/>
                                                            <c:forEach var="i" begin="0" end="1">
                                                                <div id="colonna${i}" class="width-1-2-x">
                                                                    <c:set var="iterator" value="0"/>
                                                                    <c:forEach var="itemKey" items="${item.sortedByNameKey}">
                                                                        <c:set var="modulo" value="${iterator % 2}"/>
                                                                        <c:set var="item1" value="${item.mapConfigButtons[itemKey]}"/>
                                                                        <c:if test="${mEventFilter.length() == 0 || (mEventFilter.length() > 0 && item1.enabled)}">
                                                                            <c:if test="${modulo == i}">
                                                                                <c:set var="idName" value="${chiave}_${item1.getCode()}"/>
                                                                                <c:choose>
                                                                                    <c:when test="${item1.getTag().size() > 0}">
                                                                                        <button id="${idName}" class="uk-button buttonTag " onclick="jsSetButtonSelected('${idName}', 0);" value="${game.value.homeTeamId}" title="${item1.descTrim(mLanguage)} (${item1.countAction})" <c:if test="${!item1.enabled}">disabled</c:if>>

                                                                                            <c:if test="${item1.enabled}">
                                                                                                <c:set var="tagID" value="${tagID+1}"/> 
                                                                                                <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down" onclick="openTag = true;"></i> 
                                                                                            </c:if>
                                                                                            ${item1.descTrim(mLanguage)} (${item1.countAction})<!--span id="numTagSelected${idName}" class="uk-hidden" onclick="jsRemoveFilter(${tagID+1},0);"></span-->
                                                                                        </button>
                                                                                        <c:choose>
                                                                                            <c:when test="${item1.enabled}">
                                                                                                <div id="tag${tagID}" class="uk-accordion-content uk-text-left">
                                                                                                    <div class="tagEvento uk-form"> 
                                                                                                        <!--risultati per tag della singola azione-->
                                                                                                        <c:forEach var="item2" items="${item1.getTag()}">
                                                                                                            <div class='uk-margin-small buttonTag'>
                                                                                                                <c:set var="tagCode" value="${chiave}_${item1.getCode()}_${item2.getCode()}"/>
                                                                                                                <input type="checkbox" id="${tagCode}" class="uk-margin-right" onclick="jsSetButtonSelected('${tagCode}', 1, '${chiave}_${item1.getCode()}');" <c:if test="${!item2.enabled}">disabled</c:if> value="${item2.descLan(mLanguage)}"/><label for="${tagCode}" title="${item2.descLan(mLanguage)}">${item2.descLan(mLanguage)}</label>
                                                                                                                </div>
                                                                                                        </c:forEach>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </c:when>
                                                                                            <c:otherwise>
                                                                                                <!-- va lasciato altrimenti non funziona apertura tag correttamente e si disallineano i bottoni-->
                                                                                                <div></div>
                                                                                            </c:otherwise>
                                                                                        </c:choose>
                                                                                    </c:when>
                                                                                    <c:otherwise>
                                                                                        <button id="${idName}" class="uk-button buttonTag "  onclick="jsSetButtonSelected('${idName}', 0);" title="${item1.descTrim()}" <c:if test="${!item1.enabled}">disabled</c:if>>
                                                                                                <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down uk-hidden"></i>
                                                                                            ${item1.descTrim(mLanguage)}   (${item1.countAction})
                                                                                        </button>
                                                                                        <div id="tag${tagID}" class="uk-accordion-content"></div>

                                                                                    </c:otherwise>
                                                                                </c:choose>
                                                                            </c:if>
                                                                            <c:set var="iterator" value="${iterator+1}"/>
                                                                        </c:if>
                                                                    </c:forEach>
                                                                </div>
                                                            </c:forEach>
                                                        </div></div>
                                                </div>
                                            </c:forEach>
                                        </div> 
                                    </div>
                                </div>
                        </li>
                        <li id="liFiltroGiocatoriContent">
                            <div class="uk-margin-bottom" id="players">

                                <!--label class="uk-text-bold"><spring:message code="video.squadre"/>:</label-->
                                <div id="filtroGiocatore" class="ui-corner-all uk-form uk-text-center">
                                    <c:forEach items="${mListPlayers}" var="list" varStatus="status" >
                                        <c:if test="${list.key.id == mTeam.id}">
                                            <div class="uk-margin">
                                                <span class="width-1-1-x">
                                                    <button id="team${list.key.id}" class="uk-button buttonTag uk-margin-top" onclick="jsSetButtonSelected('team${list.key.id}', 2);" value="${list.key.id}" title="${list.key.name}">${list.key.name} </button>
                                                </span>
                                                <div>
                                                    <c:forEach items="${list.value}" var="player" >
                                                        <span class="width-1-2-x">
                                                            <button id="playerButton${player.id}" class="uk-button buttonTag" onclick="jsSetButtonSelected('playerButton${player.id}', 3);" value="${player.id}" title="${player.matchNumber} - ${player.known_name}">${player.matchNumber} - ${player.known_name}</button>
                                                        </span>
                                                    </c:forEach>
                                                </div>
                                            </div>
                                        </c:if>
                                    </c:forEach>

                                    <c:forEach items="${mListPlayers}" var="list" varStatus="status" >
                                        <c:if test="${list.key.id != mTeam.id and ((fromTeamId == null and (mGame.size() == 1 or !mEventPresent)) or (fromTeamId != null and mGame.size() == 1))}">
                                            <div class="uk-margin">
                                                <span class="width-1-1-x" >
                                                    <button id="team${list.key.id}" class="uk-button buttonTag uk-margin-top" onclick="jsSetButtonSelected('team${list.key.id}', 2);" value="${list.key.id}" title="${list.key.name}">${list.key.name}</button>
                                                </span>
                                                <div>
                                                    <c:forEach items="${list.value}" var="player" >
                                                        <span class="width-1-2-x">
                                                            <button id="playerButton${player.id}" class="uk-button buttonTag" onclick="jsSetButtonSelected('playerButton${player.id}', 3);" value="${player.id}" title="${player.matchNumber} - ${player.known_name}">${player.matchNumber} - ${player.known_name}</button>
                                                        </span>
                                                    </c:forEach>
                                                </div>
                                            </div>
                                        </c:if>
                                    </c:forEach>
                                </div>
                            </div>
                        </li>

                        <li id="liSelezionateContent" class="uk-width-1-1 uk-text-center">
                            <div id="divSelezionate" class="uk-width-1-1 uk-text-center">
                                <c:forEach items="${mGame}" var="game" varStatus="status" >
                                    <div class="width-1-2-x">
                                        <button id="match${game.value.idFixture}" class="uk-button uk-margin-top uk-button-success partiteSelezionate" onclick="jsSetButtonSelected('match${game.value.idFixture}', 4);" value="${game.value.idFixture}" title="${game.value.homeTeam}-${game.value.awayTeam} ${game.value.dateString}"><b>${game.value.homeTeam}-${game.value.awayTeam} ${game.value.dateString}</b> </button>
                                    </div>
                                </c:forEach>
                            </div>
                        </li> 
                        <li id="liEventiSmallContent" class="uk-visible-small uk-width-1-1">
                            <table id="azioni_mobile" class="uk-overflow-container uk-visible-small table table-bordered table-hover uk-margin-all-small uk-table">
                                <thead>
                                    <tr>
                                        <th class="uk-hidden">order</th>
                                        <th class="uk-hidden-small uk-text-center">
                                            <span class="squaredCheckboxEvent uk-hidden-small"><input type="checkbox" id="checkboxSelectAllEvents" onclick="jsSelectAllEvents('azioni', true);"/>
                                                <label for="checkboxSelectAllEvents"></label>
                                            </span>
                                        </th>
                                        <th><spring:message code="video.descrizione"/></th>
                                        <c:if test="${mPlaylistTvId == null}">
                                        <th class="uk-hidden-small"><spring:message code="video.half"/></th>
                                        <th class="uk-hidden-small"><spring:message code="video.inizio"/></th>
                                        <th class="uk-hidden-small"><spring:message code="video.durata"/></th>
                                        <th class="uk-visible-small uk-text-center"><spring:message code="video.half"/><br/><spring:message code="video.inizio"/><br/><spring:message code="video.durata"/></th>
                                        </c:if>

                                        <th><spring:message code="video.squadra"/></th>
                                            <c:if test="${fn:length(mEventFilter)>0 || mGame.size() > 1}">
                                            <th><spring:message code="video.partita"/></th>
                                            </c:if>
                                            <!--c:if test="${matchPlayerId == 0}"-->
                                        <th class="uk-hidden-small"><spring:message code="video.giocatore"/></th>
                                        <!--/c:if-->
                                        <th class="uk-hidden-small"><spring:message code="video.esito"/></th>
                                        <c:if test="${showAuthor}">
                                            <th><spring:message code="video.autore"/></th>
                                        </c:if>
                                        <c:if test="${mPlaylistTvId != null}"><th></th></c:if>
                                        <th class="uk-hidden-small">checked</th>
                                        <th class="uk-hidden-small">infoSearch</th>
                                        <th class="uk-hidden-small">ID</th>
                                    </tr>
                                </thead>
                                <c:if test="${not empty mEvent}">
                                    <tbody>
                                        <c:set var="countEvents" value="0"/>
                                        <c:forEach var="list" items="${mEvent}">
                                            <c:forEach var="item" items="${list.value}" >
                                                <c:set var="countEvents" value="${countEvents + 1}"/>

                                                <tr class="uk-table-middle uk-margin-small-bottom uk-margin-small-top eventToShow" id="rowEventId${item.id}" data-id="${item.id}">
                                                    <td class="uk-hidden">${item.tableOrder}</td>
                                                    <td class="clickRow uk-text-center uk-hidden-small" style="width: 50px;">
                                                        <span class="squaredCheckboxEvent uk-hidden-small"><input type="checkbox" id="checkbox${item.id}small" onclick="jsSelectAction('${item.id}');" value="${item.id}"/>
                                                            <label for="checkbox${item.id}small"></label>
                                                            <!--																0							1						2						3								4                               5					6                           7							8				9				10					11                  12-->
                                                            <input type="hidden" id="startData${item.id}" value="${item.getSecStartAzione()}||${item.getSecEndAzione()}||${item.halfTrim(mLanguage)}||${item.getPeriod_start_minute()}||${item.getDurataAzioneMinSec(false)}||${item.getmTeam()}||${item.playerTrim()}||${item.getDescrAzioneToShow(mLanguage)}||${item.mIdEvent}||${item.videoId}||${item.videoName}||${item.videoPathS3}||${item.provider}||${item.idFixture}||${item.getSecTactStart()}" />
                                                            <input type="hidden" id="descrToShow${item.id}" value="${item.getDescrAzioneToShow(mLanguage)}" />
                                                        </span>
                                                    </td>
                                                    <td class="clickRow" style="width: 250px;" onclick="startAction('${item.id}', ${countEvents}, false);">
                                                        <div class="descrBox">
                                                            <span class="uk-float-left">${item.button.descTrim(mLanguage)}<br>
                                                                <c:set var="tagval" value=""/>
                                                                <c:forEach var="tag" items="${item.mTags}" >
                                                                    <c:if test="${not empty tag}">
                                                                        <c:if test="${not empty tagval}"><c:set var="tagval" value="${tagval}, "/></c:if>
                                                                        <c:set var="tagval" value="${tagval}${tag.descLan(mLanguage)}"/>
                                                                    </c:if>
                                                                </c:forEach>
                                                                <span class="tag">${tagval}</span>
                                                                <c:if test="${not empty item.mNote}">
                                                                    <c:if test="${not empty tagval}">
                                                                    <br>
                                                                    </c:if>
                                                                    <span class="tag">${item.mNote}</span>
                                                                </c:if>
                                                            </span>

                                                            <input type="hidden" id="infoSearch${item.id}" class="infoSearch" value="${item.getInfoSearch()}#${list.key}||${item.mIdEvent}#${item.getmResultReal()}" />
                                                            <!--<i class="uk-icon-play-circle uk-hidden uk-float-right uk-icon-small" style="color: green;"></i>-->
                                                            <span id="timeToMatch" style="display:none">${item.mStart}</span> <span id="timeToMatchEnd" style="display:none">${item.durataAzione}</span>
                                                        </div>
                                                    </td>
                                                    <c:if test="${mPlaylistTvId == null}">
                                                    <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents}, false);">${item.halfTrim(mLanguage)}</td>
                                                    <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents}, false);">${item.getStartAzione()}</td>
                                                    <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents}, false);">${item.durataAzione} </td>
                                                    <td class="clickRow uk-text-center uk-visible-small uk-text-center" onclick="startAction('${item.id}', ${countEvents}, false);">${item.halfTrim(mLanguage)}<br/>${item.getStartAzione()}<br/>${item.durataAzione}</td>
                                                    </c:if>
                                                    <td class="clickRow" onclick="startAction('${item.id}', ${countEvents}, false);">${item.mTeam} </td>
                                                    <c:if test="${fn:length(mEventFilter)>0  || mGame.size() > 1}">
                                                        <td class="clickRow" style="width: 200px;" onclick="startAction('${item.id}', ${countEvents}, false);">${item.getMatch()} </td>
                                                    </c:if>
                                                    <td class="clickRow uk-hidden-small" onclick="startAction('${item.id}', ${countEvents}, false);">${item.playerCamel()}</td>

                                                    <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents}, false);">${item.getmResultReal()}</td>
                                                    <c:if test="${showAuthor}">
                                                        <td class="clickRow" style="width: 100px;" onclick="startAction('${item.id}', ${countEvents}, false);">${item.user}</td>
                                                    </c:if>
                                                    <c:if test="${mPlaylistTvId != null}">
                                                        <td>
                                                            <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}">
                                                                <button class="uk-icon-hover uk-button uk-button-mini" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.options"/>"><i class="uk-icon-hover uk-icon-caret-down"></i></button>
                                                                <div class="uk-dropdown uk-dropdown-small uk-text-left">
                                                                    <ul class="uk-nav uk-nav-dropdown">
                                                                        <li><a <c:choose><c:when test="${mPlaylistTVShare != null && mPlaylistTVShare.editable == false}">class="disabled"</c:when><c:otherwise> onclick="openDurationChanger();"</c:otherwise></c:choose>><i class="uk-icon-pencil uk-margin-right"></i><spring:message code="playlist.change.all.durations"/></a></li>
                                                                        <div class="uk-nav-divider"></div>
                                                                        <li><a <c:choose><c:when test="${mPlaylistTVShare != null && mPlaylistTVShare.editable == false}">class="disabled"</c:when><c:otherwise> onclick="openModifyClip('${item.id}');"</c:otherwise></c:choose>><i class="uk-icon-pencil uk-margin-right"></i><spring:message code='playlist.edit.single.title'/></a></li>
                                                                        <li><a <c:choose><c:when test="${mPlaylistTVShare != null && mPlaylistTVShare.editable == false}">class="disabled"</c:when><c:otherwise> onclick="removeClipsFromPlaylist('${item.id}');"</c:otherwise></c:choose>><i class="uk-icon-trash uk-margin-right"></i><spring:message code='playlist.remove.single.title'/></a></li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </c:if>
                                                    <td class="uk-hidden-small">false</td>
                                                    <td class="uk-hidden-small">${item.getInfoSearch()}#${list.key}||${item.mIdEvent}#${item.getmResultReal()}</td>
                                                    <td class="uk-hidden-small">${item.id}</td>
                                                </tr>
                                            </c:forEach>
                                        </c:forEach>
                                    </tbody>
                                </c:if>
                            </table>
                        </li>
                        <li>
                            <div id="matchData">
                                <!-- dati partita caricati da JQuery -->
                            <!--	<p class="data"><b>${game.value.homeTeam} - ${game.value.awayTeam}</b><br><spring:message code="video.risultato"/> <b>${game.value.homeTeamScore}-${game.value.awayTeamScore}</b></br><spring:message code="video.data"/> <b>${game.value.dateFullString}</b> <br/><spring:message code="video.giornata"/> <b>${game.value.matchday}</b><br/><c:if test="${!empty game.value.refereeName}"> <spring:message code="video.arbitro"/> <b>${game.value.refereeName}</b></c:if><br/> <c:if test="${!empty game.value.assistant1Name}"> <spring:message code="video.assistenti"/> <b>${game.value.assistant1Name} - ${game.value.assistant2Name}</c:if></b></p> -->
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>

                <!--eventi / partite / calendario / playlistTv-->
                <div id="sideNav" class="uk-width-5-10 uk-hidden-small">

                    <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left uk-margin-bottom" data-uk-tab="{connect:'#sideNavContent'}">
                        <li id="liActions" class="uk-active"><a href="#" onclick="changeButtonsVisibility(true);"><spring:message code="video.eventi"/><span id="numEvents" class="uk-badge uk-hidden uk-margin-small-left">0</span></a></li>
                        <li id="liMatches" ><a href="#" onclick="changeButtonsVisibility(false);"><spring:message code="video.partite"/><span id="numMatches" class="uk-badge uk-hidden" style="margin-left: 5px;" onclick="jsRefreshMatches('1');">0</span></a></li>
                        <li id="liCalendario"><a href="#" onclick="changeButtonsVisibility(false);"><spring:message code="menu.user.calendario"/></a></li>
                        <li id="liPlaylistTv"><a href="#" onclick="changeButtonsVisibility(false, true);"><spring:message code="menu.user.playlist"/></a></li>
                    </ul>

                <span class="uk-float-right uk-margin-small-top uk-margin-small-right uk-hidden-small">
                    <!--<button id="playActions" class="uk-button uk-button-small" title="<spring:message code='tooltip.playeventi'/>" onclick="startAction('', 0);"><i class="uk-icon-play-circle uk-icon-small" style="color: green;"></i></button>-->
                    <button id="checkedActionsButton" class="uk-button uk-button-small uk-hidden" title="<spring:message code="eventi.selezionati"/>" onclick="startAction('', 0, true);"><i class="uk-icon-play-circle" style="color: green;"></i><span id="numCheckedEvents" style="margin-left: 5px;">0</span></i></button>
                    <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}" onclick="checkExport();">
                        <button id="doExportAction" class="uk-button uk-button-small" title="<spring:message code='tooltip.esporta'/>" ><i class="uk-icon-upload"></i></button>
                        <div id="dropdownExport" class="uk-dropdown uk-dropdown-small uk-text-left">
                            <div style="padding: 5px">
                                <center>
                                    <div>
                                        <h3 style="text-transform: uppercase; font-weight: bold"><spring:message code='tooltip.esporta'/></h3>
                                    </div>
                                </center>
                                <div style="display: flex; width: 40%; float: left">
                                    <spring:message code="playlist.descrizione"/>
                                </div>
                                <div style="display: flex; width: 60%">
                                    <input type="text" id="nameFileExport" value="${mPlaylistTv.name}" style="width: 100%"/>
                                </div>
                                <hr/>
                                <div>
                                    <div style="display: grid; 
                                        grid-template-columns: 40% 60%; 
                                        grid-template-rows: 1fr 1fr; 
                                        gap: 0px 0px; 
                                        grid-template-areas: 
                                          'options maxQuality'
                                          'options single'
                                          'options tactic';
                                        align-items: center; ">
                                        <div style="grid-area: options"><spring:message code="playlist.opzioni"/></div>
                                        <div style="grid-area: maxQuality">
                                            <input id="exportMaxQuality" type="checkbox"/><label for="exportMaxQuality"><spring:message code="esporta.massima.qualita"/></label>
                                        </div>
                                        <div style="grid-area: single">
                                            <input id="exportmulti" type="checkbox" value="multi"/><label for="exportmulti"><spring:message code='esporta.singoli'/></label>
                                        </div>
                                        <div style="grid-area: tactic">
                                            <input id="exportTACT" type="checkbox" name="quality" value="TACT" <c:if test="${!exportTactAvailable}">disabled</c:if>/><label for="exportTACT"><spring:message code="export.videoTattico"/></label>
                                        </div>
                                    </div>
                                </div>
                                <hr/>
                                <div id="exportRequirementDiv" class="uk-hidden">
                                    <div style="display: grid; 
                                        grid-template-columns: 40% 60%; 
                                        grid-template-rows: 1fr 1fr; 
                                        gap: 0px 0px; 
                                        grid-template-areas: 
                                          'requirements first'
                                          'requirements second'
                                          'requirements third'
                                          'requirements fourth';
                                        align-items: center; ">
                                        <div style="grid-area: requirements; color: red; font-size: 13px"><spring:message code="playlist.requisiti"/></div>
                                        <div style="grid-area: first">
                                            <img id="firstExportRequirement" height="14px" width="14px" style="margin-top: -3px" src="/sicstv/images/cancel.png"/>
                                            <label id="firstExportRequirementLabel" for="firstExportRequirement"><spring:message code='playlist.requisiti.1'/></label>
                                        </div>
                                        <div style="grid-area: second">
                                            <img id="secondExportRequirement" height="14px" width="14px" style="margin-top: -3px" src="/sicstv/images/cancel.png"/>
                                            <label id="secondExportRequirementLabel" for="secondExportRequirement"><spring:message code='playlist.requisiti.2'/></label>
                                        </div>
                                        <div style="grid-area: third">
                                            <img id="thirdExportRequirement" height="14px" width="14px" style="margin-top: -3px" src="/sicstv/images/cancel.png"/>
                                            <label id="thirdExportRequirementLabel" for="thirdExportRequirement"><spring:message code='playlist.requisiti.3'/></label>
                                        </div>
                                        <div style="grid-area: fourth">
                                            <img id="fourthExportRequirement" height="14px" width="14px" style="margin-top: -3px" src="/sicstv/images/cancel.png"/>
                                            <label id="fourthExportRequirementLabel" for="fourthExportRequirement"><spring:message code='playlist.requisiti.4'/></label>
                                        </div>
                                    </div>
                                    <hr/>
                                </div>
                                <div class="uk-hidden" id="exportEffettivoWarning" style="margin-bottom: 10px; color: #ffa126">
                                    <img height="14px" width="14px" style="margin-top: -3px" src="/sicstv/images/warning.png"/>
                                    <label><spring:message code='playlist.requisiti.1.tempo.effettivo'/></label>
                                </div>
                                <div class="uk-hidden" id="exportRunningMessage" style="margin-bottom: 10px; font-size: 20px">
                                    <label><spring:message code="player.user.export.title"/></label>
                                </div>
                                <button id="exportActionsButton" onclick="exportActions();" class="uk-button uk-dropdown-close" disabled><i class="uk-icon-cog uk-margin-right"></i><spring:message code="export.elabora"/></button>
                                <span id="exportActionNoEvents" class="uk-hidden" style="margin-left: 20px; color: red"><i class="uk-icon-warning"></i> <spring:message code="menu.user.export.no.clip.warning"/></span>
                            </div>
                        </div>
                    </div>
                    <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}">
                        <button id="doAddToPlaylistAction" class="uk-button uk-button-small" title="<spring:message code="playlist.add"/>"><i class="uk-icon-plus"></i></button>
                        <div id="dropdownPlaylist" class="uk-dropdown uk-dropdown-small uk-text-left">
                            <div style="padding: 5px">
                                <center>
                                    <div>
                                        <h3 style="text-transform: uppercase; font-weight: bold"><spring:message code='playlist.add'/></h3>
                                    </div>
                                </center>
                            </div>
                            <div style="display: flex; width: 40%; float: left">
                                <spring:message code="playlist.nome"/>
                            </div>
                            <div style="display: flex; width: 60%">
                                <input type="text" id="namePlaylist" maxlength="32" style="width: 100%"/>
                            </div>
                            <div style="display: flex; width: 40%; float: left; margin-top: 5px">
                                <spring:message code="playlist.descrizione"/>
                            </div>
                            <div style="display: flex; width: 60%; margin-top: 5px">
                                <input type="text" id="descriptionPlaylist" maxlength="255" style="width: 100%"/>
                            </div>
                            <button onclick="addToPlaylist();" class="uk-button uk-dropdown-close" style="margin-top: 10px"><spring:message code="playlist.savenew"/></button>
                            <div id="playlistTvButtonLi"<c:if test="${mPlaylistList == null || mPlaylistList.size() == 0}"> class="uk-hidden"</c:if>>
                                <div class="playlistTv">
                                    <hr>
                                    <center>
                                        <div>
                                            <h4 style="text-transform: uppercase; font-weight: bold">
                                                <spring:message code="playlist.fastadd"/>
                                                <p style="font-size: 10px; font-weight: normal; text-transform: none">(<spring:message code="playlist.clicca.per.aggiungere"/>)</p>
                                            </h4>
                                        </div>
                                    </center>
                                </div>
<!--                                <div id="playlistTvButtonDiv">
                                    <c:forEach var="item" items="${mPlaylistList}">
                                        <button onclick="addToPlaylist(${item.id});" class="uk-button uk-dropdown-close playlistTvButton" style="margin-right: 5px"><c:if test="${item.shared}"><i class="uk-icon-link"></i> </c:if>${item.name}</button>
                                    </c:forEach>
                                </div>-->
                            </div>
                        </div>
                    </div>
                    <c:if test="${not empty mPlaylistTvId}">
                    <button id="doRemoveFromPlaylistAction" class="uk-button uk-button-small" title="<spring:message code='playlist.remove.title'/>" onclick="removeClipsFromPlaylist()"><i class="uk-icon-trash"></i></button>
                    </c:if>
                    <button id="addToMulti" class="uk-button uk-button-small uk-hidden" title="<spring:message code='tooltip.multipartita'/>" onclick="jsGoToMultipartita('${personalSource}');"><i class="uk-icon-external-link"></i></button>
                    <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}" >
                        <button id="selectedMatches" class="uk-button uk-button-small uk-hidden" title="<spring:message code='tooltip.selezionati'/>"><i class="uk-icon-bars"></i> 0 </button> 
                        <!--<a href="#" id="selectedMatches" class="uk-button uk-button-small" ><i class="uk-icon-bars  "></i> 0 </a>-->
                        <span id="spanSelAll" class="uk-dropdown">
                            <ul class="uk-nav uk-nav-navbar">
                                <li class="selAllSubnav"><a onclick="jsSelectAll(true, 'matchesCalendarContent');"><i class="uk-icon-check-square-o uk-margin-right"></i><spring:message code="menu.selezionatutto"/></a></li>
                                <div class="uk-nav-divider"></div>
                                <li class="selAllSubnav"><a onclick="jsSelectAll(false, 'matchesCalendarContent');"><i class="uk-icon-square-o uk-margin-right"></i><spring:message code="menu.deselezionatutto"/></a></li>
                            </ul>							
                        </span>
                    </span>
                    <c:if test="${mGame.size() > 1}">
                    <button id="btnSearch" class="uk-button uk-button-small" data-uk-offcanvas="{target:'#divFilter'}" title="<spring:message code='tooltip.ricerca'/>"><i class="uk-icon-search"></i> </button>
                    </c:if>
                </span>

                <ul id="sideNavContent" class="uk-switcher uk-margin-all" style="max-height: 85vh; overflow: auto">

                    <li class="uk-active uk-hidden-small" id="eventi">


                        <table id="azioni" class="uk-hidden-small table table-bordered table-hover uk-margin-all-small uk-table">
                            <thead>
                                <tr>
                                    <th class="uk-hidden">order</th>
                                    <th class="uk-hidden-small uk-text-center">
                                        <span class="squaredCheckboxEvent uk-hidden-small"><input type="checkbox" id="checkboxSelectAllEventsSmall" onclick="jsSelectAllEvents('azioni', true);"/>
                                            <label for="checkboxSelectAllEventsSmall"></label>
                                        </span>
                                    </th>
                                    <th><spring:message code="video.descrizione"/></th>
                                    <th class="uk-hidden-small"><spring:message code="video.half"/></th>
                                    <th class="uk-hidden-small"><spring:message code="video.inizio"/></th>
                                    <th class="uk-hidden-small"><spring:message code="video.durata"/></th>
                                    <th class="uk-visible-small uk-text-center"><spring:message code="video.half"/><br/><spring:message code="video.inizio"/><br/><spring:message code="video.durata"/></th>

                                    <th><spring:message code="video.squadra"/></th>
                                        <c:if test="${fn:length(mEventFilter)>0 || mGame.size() > 1}">
                                        <th><spring:message code="video.partita"/></th>
                                        </c:if>
                                        <!--c:if test="${matchPlayerId == 0}"-->
                                    <th><spring:message code="video.giocatore"/></th>
                                    <!--/c:if-->
                                    <th class="uk-hidden-small"><spring:message code="video.esito"/></th>
                                    <c:if test="${showAuthor}">
                                        <th><spring:message code="video.autore"/></th>
                                    </c:if>
                                    <c:if test="${mPlaylistTvId != null}"><th></th></c:if>
                                    <th class="uk-hidden">checked</th>
                                    <th class="uk-hidden">infoSearch</th>
                                    <th class="uk-hidden">ID</th>
                                </tr>
                            </thead>
                            <c:if test="${not empty mEvent}">
                                <tbody>
                                    <c:set var="countEvents" value="0"/>
                                    <c:forEach var="list" items="${mEvent}">
                                        <c:forEach var="item" items="${list.value}" >
                                            <c:set var="countEvents" value="${countEvents + 1}"/>

                                            <tr class="uk-table-middle uk-margin-small-bottom uk-margin-small-top eventToShow" id="rowEventId${item.id}" data-id="${item.id}">
                                                <td class="uk-hidden">${item.tableOrder}</td>
                                                <td class="clickRow uk-text-center uk-hidden-small" style="width: 50px;">
                                                    <span class="squaredCheckboxEvent uk-hidden-small"><input type="checkbox" id="checkbox${item.id}" onclick="jsSelectAction('${item.id}');" value="${item.id}"/>
                                                        <label for="checkbox${item.id}"></label>
                                                        <!--																0							1						2						3								4                               5					6                           7							8				9				10					11                  12-->
                                                        <input type="hidden" id="startData${item.id}" value="${item.getSecStartAzione()}||${item.getSecEndAzione()}||${item.halfTrim(mLanguage)}||${item.getPeriod_start_minute()}||${item.getDurataAzioneMinSec(false)}||${item.getmTeam()}||${item.playerTrim()}||${item.getDescrAzioneToShow(mLanguage)}||${item.mIdEvent}||${item.videoId}||${item.videoName}||${item.videoPathS3}||${item.provider}||${item.idFixture}||${item.getSecTactStart()}" />
                                                        <input type="hidden" id="descrToShow${item.id}" value="${item.getDescrAzioneToShow(mLanguage)}" />
                                                    </span>
                                                </td>
                                                <td class="clickRow" style="width: 250px;" onclick="startAction('${item.id}', ${countEvents}, false);">
                                                    <div class="descrBox">
                                                        <span class="uk-float-left">${item.button.descTrim(mLanguage)}<br>
                                                            <c:set var="tagval" value=""/>
                                                            <c:forEach var="tag" items="${item.mTags}" >
                                                                <c:if test="${not empty tag}">
                                                                    <c:if test="${not empty tagval}"><c:set var="tagval" value="${tagval}, "/></c:if>
                                                                    <c:set var="tagval" value="${tagval}${tag.descLan(mLanguage)}"/>
                                                                </c:if>
                                                            </c:forEach>
                                                            <span class="tag">${tagval}</span>
                                                            <c:if test="${not empty item.mNote}">
                                                                <c:if test="${not empty tagval}">
                                                                <br>
                                                                </c:if>
                                                                <span class="tag">${item.mNote}</span>
                                                            </c:if>
                                                        </span>

                                                        <input type="hidden" id="infoSearch${item.id}" class="infoSearch" value="${item.getInfoSearch()}#${list.key}||${item.mIdEvent}#${item.getmResultReal()}" />
                                                        <!--<i class="uk-icon-play-circle uk-hidden uk-float-right uk-icon-small" style="color: green;"></i>-->
                                                        <span id="timeToMatch" style="display:none">${item.mStart}</span> <span id="timeToMatchEnd" style="display:none">${item.durataAzione}</span>
                                                    </div>
                                                </td>
                                                <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents}, false);">${item.halfTrim(mLanguage)}</td>
                                                <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents}, false);">${item.getStartAzione()}</td>
                                                <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents}, false);">${item.durataAzione} </td>
                                                <td class="clickRow uk-text-center uk-visible-small uk-text-center" onclick="startAction('${item.id}', ${countEvents}, false);">${item.halfTrim(mLanguage)}<br/>${item.getStartAzione()}<br/>${item.durataAzione}</td>
                                                <td class="clickRow" onclick="startAction('${item.id}', ${countEvents}, false);">${item.mTeam} </td>
                                                <c:if test="${fn:length(mEventFilter)>0  || mGame.size() > 1}">
                                                    <td class="clickRow" style="width: 200px;" onclick="startAction('${item.id}', ${countEvents}, false);">${item.getMatch()} </td>
                                                </c:if>
                                                <td class="clickRow" onclick="startAction('${item.id}', ${countEvents}, false);">${item.playerCamel()}</td>

                                                <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents}, false);">${item.getmResultReal()}</td>
                                                <c:if test="${showAuthor}">
                                                    <td class="clickRow" style="width: 100px;" onclick="startAction('${item.id}', ${countEvents}, false);">${item.user}</td>
                                                </c:if>
                                                <c:if test="${mPlaylistTvId != null}">
                                                    <td>
                                                        <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}">
                                                            <button class="uk-icon-hover uk-button uk-button-mini" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.options"/>"><i class="uk-icon-hover uk-icon-caret-down"></i></button>
                                                            <div class="uk-dropdown uk-dropdown-small uk-text-left">
                                                                <ul class="uk-nav uk-nav-dropdown">
                                                                    <li><a <c:choose><c:when test="${mPlaylistTVShare != null && mPlaylistTVShare.editable == false}">class="disabled"</c:when><c:otherwise> onclick="openDurationChanger();"</c:otherwise></c:choose>><i class="uk-icon-pencil uk-margin-right"></i><spring:message code="playlist.change.all.durations"/></a></li>
                                                                    <div class="uk-nav-divider"></div>
                                                                    <li><a <c:choose><c:when test="${mPlaylistTVShare != null && mPlaylistTVShare.editable == false}">class="disabled"</c:when><c:otherwise> onclick="openModifyClip('${item.id}')"</c:otherwise></c:choose>><i class="uk-icon-pencil uk-margin-right"></i><spring:message code='playlist.edit.single.title'/></a></li>
                                                                    <li><a <c:choose><c:when test="${mPlaylistTVShare != null && mPlaylistTVShare.editable == false}">class="disabled"</c:when><c:otherwise> onclick="removeClipsFromPlaylist('${item.id}')"</c:otherwise></c:choose>><i class="uk-icon-trash uk-margin-right"></i><spring:message code='playlist.remove.single.title'/></a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </c:if>
                                                <td class="uk-hidden">false</td>
                                                <td class="uk-hidden">${item.getInfoSearch()}#${list.key}||${item.mIdEvent}#${item.getmResultReal()}</td>
                                                <td class="uk-hidden">${item.id}</td>
                                            </tr>
                                        </c:forEach>
                                    </c:forEach>
                                </tbody>
                            </c:if>
                        </table>
                    </li>

                    <li id="partite" class=" ">
                        <!--Content loaded with Ajax -->
                    </li>

                    <li id="calendario" class=" ">
                        <!--Content loaded with Ajax-->

                    </li>
                    
                    <li id="playlistTv" class=" ">
                        <!--Content loaded with Ajax-->

                    </li>
                </ul>

                <div id="divFilter" class="uk-offcanvas">
                    <div class="uk-offcanvas-bar uk-offcanvas-bar-flip">
                        <%@ include file="searchMulti.jsp" %>
                    </div>
                </div>
            </div>
        </div>

        <div id="modalModifyClip" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog dynamicModalWidth" style="min-height: 0;min-width: 0;width: auto;height: auto;" id="dropdownModifyClip">
                <div id="editClipPage">
                </div>
            </div>
        </div>
                    
        <div id="modalModifyClips" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog dynamicModalWidth" style="min-height: 0;min-width: 0;width: auto;height: auto;">
                <div>
                    <span class="modalClose" onclick="closeDurationChanger();">&times;</span>
                    <h3><spring:message code="playlist.change.all.durations"/></h3>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <%--il 360 è messo per sicurezza (6 minuti) ma il limite è 5 minuti di clip quindi non dovrebbe mai servire--%>
                        <p>
                            <spring:message code="playlist.change.all.durations.start"/> <input id="eventsAdditionalStart" class="inputNumber" type="number" min="-360" max="120"> <spring:message code="playlist.seconds"/>
                        </p>
                        <p>
                            <spring:message code="playlist.change.all.durations.end"/> <input id="eventsAdditionalEnd" class="inputNumber" type="number" min="-360" max="120"> <spring:message code="playlist.seconds"/>
                        </p>
                    </div>
                    <div class="uk-modal-footer uk-text-right">
                        <button style="min-width:80px;" onclick="closeDurationChanger();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                        <button style="min-width:80px;" onclick="confirmDurationChange();" class="uk-text-large uk-text-bold uk-button uk-button-danger js-modal-confirm"><spring:message code='menu.conferma'/></button>
                    </div>
                </div>
            </div>
        </div>

    </body>
</html>
