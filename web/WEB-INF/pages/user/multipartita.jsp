<%-- 
    Document   : multipartita
    Created on : 17-ago-2015, 8.48.30
    Author     : MAC1
--%>

<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1"/>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />

		<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
		<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

		<title><spring:message code="theme.title"/></title>

		<script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
		<link rel="stylesheet" href="/sicstv/uikit/css/default.css" />
		<link rel="stylesheet" href="/sicstv/css/sics.css" />
		<script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>
		<script type="text/javascript" src="/sicstv/js/utils.js" ></script>
		<script typt="text/javascript" src="/sicstv/uikit/js/components/notify.min.js"></script>
		<link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css">
		<script type="text/javascript">

			$(document).ready(function () {
				loadFilterMulti();
			});

			function searchMulti() {
				var idComp = $("#selectCompetition").val();
				var idTeam = $("#selectTeam").val();
				var idMatchDay = $("#selectMatchDay").val();
                                var idCountry = new URL(document.URL).searchParams.get("countryId");
                                
                                var data = encodeURI("&idComp=" + idComp + "&idTeam=" + idTeam + "&idMatchDay=" + idMatchDay);
                                if (typeof idCountry === 'undefined') {
                                    data = encodeURI("&idComp=" + idComp + "&idTeam=" + idTeam + "&idMatchDay=" + idMatchDay + "&idCountry=" + idCountry);
                                }
				$.ajax({
					type: "GET",
					url: "/sicstv/user/searchMulti.htm",
					cache: false,
					data: data,
					success: function (msg) {
						$("#gameResult").html(msg);
						return false;
					}
				});
			}

			function loadFilterMulti() {
				var idComp = $("#selectCompetition").val();

				$.ajax({
					type: "GET",
					url: "/sicstv/user/loadFilterMulti.htm",
					data: encodeURI("idComp=" + idComp),
					cache: false,
					success: function (msg) {
						$("#filterDivMulti").html(msg);
						$('#selectCompetition option[value="' + idComp + '"]').attr('selected', 'selected');
						return false;
					}
				});
			}

			function addMatch() {
				var array = [];
				$("#tablemultiSelectTail tr").each(function () {
					$this = $(this);
					array.push($this.find('input[type="checkbox"]').val());
				});


				$("#tableGameResult tr").each(function () {
					$this = $(this);
					$checkedBoxes = $this.find('input[type="checkbox"]:checked');
					var found = $.inArray($this.find('input[type="checkbox"]').val(), array, 0);
					$checkedBoxes.each(function (i, checkbox) {
						if (found === -1) {
							var newRow = $this.html();
							$('#tablemultiSelectTail tr:last').after('<tr>' + newRow + '</tr>');
						}
						$(checkbox).prop("checked", false);
					});
				});
			}

			function removeMatch() {
				$("#tablemultiSelectTail tr").each(function () {
					$this = $(this);
					$checkedBoxes = $this.find('input[type="checkbox"]:checked');

					$checkedBoxes.each(function () {
						var newRow = $this.html();
						$this.remove();
					});
				});
			}
		</script>
	</head>
	<body>
		<%@ include file="header.jsp" %>
		<div id="filterDivMulti" class="uk-margin">
			<span style="margin: 15px">
				<span id="comboCompetition">Competizione</span>
				<select id="selectCompetition" class="uk-form-select">
					<option value='' selected='selected'>&nbsp;</option>
				</select>
			</span>
			<span style="margin: 15px">
				<span id="comboTeam">Squadra</span>
				<select id="selectTeam" class="uk-form-select">
					<option value='' selected='selected'>&nbsp;</option>
				</select>
			</span>
			<span style="margin: 15px">
				<span id="comboMatchDay">Giornata</span>
				<select id="selectMatchDay" class="uk-form-select">
					<option value='' selected='selected'>&nbsp;</option>
				</select>
			</span>
		</div>

		<div id="gameResult" class="uk-width-5-10" style="float: left;">
			<label class="uk-text-bold uk-margin-left">ELENCO PARTITE:</label>
			<table id="tableGameResult" class="uk-table uk-table-hover" style="border-collapse: collapse;">
				<thead>
					<tr>
						<th></th>
						<th><spring:message code="multi.data"/></th>
						<th><spring:message code="multi.partita"/></th>
						<th><spring:message code="multi.campionato"/></th>
						<th><spring:message code="multi.giornata"/></th>
						<th><spring:message code="multi.risultato"/></th>
					</tr>
				</thead>
				<tbody>

				</tbody>
			</table>
		</div>
		<div class="uk-width-1-10" style="float: left;">
			<button id="addMatch" class="uk-button uk-button-small uk-margin uk-block uk-align-center uk-margin-large-top" onclick="addMatch();" >Aggiungi</button>
			<button id="removeMatch" class="uk-button uk-button-small uk-margin uk-block  uk-align-center uk-margin-large-top" onclick="removeMatch();" >Rimuovi</button>
		</div>
		<div id="multiSelectTail" class="uk-width-4-10" style="float: right;">
			<label class="uk-text-bold uk-margin-left">ELENCO PARTITE MULTIPARTITA:</label>
			<table id="tablemultiSelectTail" class="uk-table uk-table-hover" style="border-collapse: collapse;">
				<thead>
					<tr>
						<th></th>
						<th><spring:message code="multi.data"/></th>
						<th><spring:message code="multi.partita"/></th>
						<th><spring:message code="multi.campionato"/></th>
						<th><spring:message code="multi.giornata"/></th>
						<th><spring:message code="multi.risultato"/></th>
					</tr>
				</thead>
				<tbody>

				</tbody>
			</table>
		</div>

	</body>
</html>
