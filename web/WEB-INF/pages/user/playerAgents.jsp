<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>

<script type="text/javascript">
    $(document).ready(function () {
        $("img.lazyBrief").lazyload({
            placeholder: "/sicstv/images/briefcase.svg"
        }).on("error", function () {
            // Questo codice verrà eseguito se l'immagine non potrà essere caricata
            $(this).attr("src", "/sicstv/images/briefcase.svg");
        });
        
        $("img.lazyCountry").lazyload({
        }).on("error", function () {
        });
    });

    function changeOrder() {
        var sort = $("#player-agents-orderby").val();
        $("#playerAgents").html('<center><div class="loader"></div></center>');
        $.ajax({
            type: "GET",
            url: "/sicstv/user/playerAgents.htm",
            cache: false,
            data: encodeURI("sort=" + sort),
            success: function (result) {
                $("#playerAgents").empty();
                $("#playerAgents").append(result);
                $("#player-agents-orderby").val(sort);
            },
            error: function () {
                UIkit.notify("<spring:message code="generic.error"/>", {status: 'danger', timeout: 1000});
            }
        });
    }

    function searchAgency() {
        $("#player-agents-container").find("[agent]").removeClass("uk-hidden");
        let searchText = $("#search-agents-input").val().toLowerCase();
        if (searchText) {
            $("#player-agents-container").find("[agent]").each(function (index, element) {
                let text = $(element).attr("agent").toLowerCase();
                if (!text.includes(searchText)) {
                    $(element).addClass("uk-hidden");
                }
            });
        }
    }
</script>

<div id="player-agents-container" style="margin-top: 5vh;">
    <div class="uk-flex uk-margin-left-big uk-margin-right-big">
        <span class="uk-form">
            <spring:message code="player.agent.sort"/>:
            <select id="player-agents-orderby" onchange="changeOrder();">
                <option value="1"><spring:message code="player.agent.sort.amount"/></option>
                <option value="2"><spring:message code="player.agent.sort.alphabetical"/></option>
            </select>
        </span>
        <div class="uk-margin-left-auto">
            <spring:message code="player.agent.search"/>:
            <input type="text" id="search-agents-input" oninput="searchAgency();" class="uk-form-width-medium" autocomplete="off">
        </div>
    </div>
    <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-small-1-5 uk-grid-width-medium-1-6 uk-grid-width-large-1-7 uk-grid-width-xlarge-1-12 uk-width-1-1 uk-margin-small-top">
        <c:forEach var="agent" items="${mPlayerAgents}">
            <li class="uk-margin-left uk-margin-top uk-margin-bottom" id="${agent.id}" agent="${agent.name}" onclick="location.href = '/sicstv/user/playerAgent.htm?agentId=${agent.id}'" style="min-height: 110px;">
                <a class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height:80px;">
                    <div class="competitionBox">
                        <img class="logoCompImg lazyBrief" style="width: 65px;" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/agentlogo/${agent.photo}.png" onerror="this.src='/sicstv/images/briefcase.svg'" alt="${agent.name}">
                    </div>
                    <p style="margin-bottom: 0; margin-top: 3px; text-transform: uppercase;">
                        <img width="20" height="20" class="lazyCountry" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${agent.countryLogo}.png" onerror="this.remove();" title="${agent.countryName}">
                        ${agent.name} (${agent.playerAmount})
                    </p>
                </a>
            </li>
        </c:forEach>
    </ul>
</div>