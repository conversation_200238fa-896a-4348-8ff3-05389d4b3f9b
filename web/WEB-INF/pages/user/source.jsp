<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>


	
	
		<c:forEach var="item" items="${sourceAll}">
			<c:choose>
				<c:when test="${item.id == curSource}">
					<option selected="selected" value="${item.id}">${item.name}</option>
				</c:when>
				<c:otherwise>
					<option value="${item.id}">${item.name}</option>
				</c:otherwise>
			</c:choose>
		</c:forEach>
	
