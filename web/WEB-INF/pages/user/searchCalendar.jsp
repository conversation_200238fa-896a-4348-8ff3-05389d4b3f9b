<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div>

    <script type="text/javascript">
        $(document).ready(function () {
//            var teamId = new URL(document.URL).searchParams.get("formTeamId");
//            if (typeof teamId === 'undefined' || teamId === null) {
//                teamId = new URL(document.URL).searchParams.get("teamId");
//            }
//            var playerId = new URL(document.URL).searchParams.get("playerId");

//            if ((typeof teamId === 'undefined' || teamId === null) && (typeof playerId === 'undefined' || playerId === null)) {
//                $(".removeDuplicatesLink").remove();
//            }
        });

        var currentPage = $("#mMatchDay").val();
        $("#mMatchDay").bind('keyup', function (event) {
            var keyCode = event.keyCode;
            // 38 = arrow up
            // 40 = arrow down
            // 10 = enter
            if (keyCode === 38 || keyCode === 40 || keyCode === 13) {
                changePage();
            }
        });
        $("#mMatchDay").bind('mouseup', function () {
            changePage();
        });
        // focus + cursor on last character
        var mMatchDay = document.getElementById("mMatchDay");
        // work around to set selection at the end of the number
        mMatchDay.type = "tel";
        var end = mMatchDay.value.length;
        mMatchDay.setSelectionRange(end, end);
        mMatchDay.type = "number";
        mMatchDay.focus();

        function changePage() {
            if (currentPage !== $("#mMatchDay").val()) { // ridundant calls
                if ($("#mMatchDay").val() >= parseInt($('#mMinDay').val()) && $("#mMatchDay").val() <= parseInt($('#mMaxDay').val())) {
                    currentPage = $("#mMatchDay").val();
                    switchMatchday(currentPage);
                    document.getElementById("mMatchDay").disabled = true;
                }
            }
        }
    </script>
    <input type="hidden" id="mMaxDay" value="${mMaxDay}" />
    <input type="hidden" id="mMinDay" value="${mMinDay}" />
    <input type="hidden" id="mDay" value="${mMaxDay}" />

    <c:set var="idPlayer" value=""/>
    <c:set var="removeDuplicates" value=""/>
    <c:if test="${mUser.isPlayerUser()}">
        <c:set var="idPlayer" value="${mUser.playerId}"/>
        <c:set var="removeDuplicates" value="&removeDuplicates=true"/>
    </c:if>
    <c:set var="removeDuplicates" value="&removeDuplicates=true"/>
    <table id="matchTable" class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
        <tbody>
            <tr>
                <td colspan="5">
                    <button onclick="switchMatchday(${mMinDay});" id="firstPageCal" class="uk-button uk-button-small" <c:if test='${mMatchDay == mMinDay}'> disabled</c:if> title="Prima pagina"><i class="uk-icon-step-backward"></i> </button>
                    <button onclick="switchMatchday('prev');" id="prevCal" class="uk-button uk-button-small" <c:if test='${mMatchDay == mMinDay}'> disabled</c:if> title="<spring:message code='calendar.precedente'/>"><i class="uk-icon-angle-double-left"></i> </button>
                    <button onclick="switchMatchday('next');" id="nextCal" class="uk-button uk-button-small" <c:if test='${mMatchDay == mMaxDay}'> disabled</c:if> title="<spring:message code='calendar.successiva'/>"><i class="uk-icon-angle-double-right"></i> </button>
                    <button onclick="switchMatchday(${mMaxDay});" id="lastPageCal" class="uk-button uk-button-small" <c:if test='${mMatchDay == mMaxDay}'> disabled</c:if> title="Ultima pagina"><i class="uk-icon-step-forward"></i> </button>
                    <span class="uk-margin-small-left"><spring:message code='calendar.giornata'/> </span>
                    &nbsp;
                    <input type="number" id="mMatchDay" value="${mMatchDay}" min="${mMinDay}" max="${mMaxDay}" class="uk-form-width-mini filter-select input-text-calendar-search"/>
                    <span id="loaderSpinnerCalendar" class="uk-margin-small-left"></span>
                    <span id="matchDayCalendar"></span>
                    <button onclick="changePage();" id="nextCal" class="uk-button uk-button-small uk-float-right" title="Cambia pagina"><i class="uk-icon-play"></i> </button>
                </td>
            </tr>
            <c:forEach items="${mMatches}" var="item">
                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                    <c:if test="${item.videoName != null}">
                        <td class="uk-text-center matchesColumnResult1" onclick="location.href = '/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=${idPlayer}&goals=false&event=&filter=&limit=50${removeDuplicates}'" class="uk-width-1-10 clickRow uk-text-center"><div id="dateBox"><b>${item.dateDayString}<br>${item.dateMonthString}</b></div></td>
                                </c:if>
                                <c:if test="${item.videoName == null}">
                        <td class="uk-text-center matchesColumnResult1" class="uk-width-1-10 clickRow uk-text-center"><div id="dateBox"><b>${item.dateDayString}<br>${item.dateMonthString}</b></div></td>
                                </c:if>

                    <c:if test="${item.videoName != null}">
                        <td class="clickRow" onclick="location.href = '/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=${idPlayer}&goals=false&event=&filter=&limit=50${removeDuplicates}'">
                            <a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=${idPlayer}&goals=false&event=&filter=&limit=50${removeDuplicates}">
                                <b>${item.homeTeam} - ${item.awayTeam}</b><br>
                                <span class="competitionDetail">${item.competitionName}, <spring:message code="calendar.giornata"/> ${item.matchday}</span>
                            </a>
                        </td>
                    </c:if>
                    <c:if test="${item.videoName == null}">
                        <td class="clickRow">
                            <a href="#">
                                <b>${item.homeTeam} - ${item.awayTeam}</b><br>
                                <span class="competitionDetail">${item.competitionName}, <spring:message code="calendar.giornata"/> ${item.matchday}</span>
                            </a>
                        </td>
                    </c:if>

                    <c:if test="${item.videoName != null}">
                        <td class="uk-text-center matchesColumnResult1 clickRow" onclick="location.href = '/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=${idPlayer}&goals=false&event=&filter=&limit=50${removeDuplicates}'"><span class="competitionResult">${item.homeTeamScore} - ${item.awayTeamScore}</span></td>
                    </c:if>
                    <c:if test="${item.videoName == null}">
                        <td class="uk-text-center matchesColumnResult1 clickRow"><span class="competitionResult">${item.homeTeamScore} - ${item.awayTeamScore}</span></td>
                    </c:if>

                    <td class="matchesColumnTag">
                        <c:if test="${item.fhd}">
                            <i><img src="/sicstv/images/FHD2.png"/></i>
                        </c:if>
                        <c:if test="${!item.fhd && item.hd}">
                            <i><img src="/sicstv/images/hd-dark.png"/></i>
                        </c:if>
                        <c:if test="${!item.fhd && !item.hd && item.videoName != null}">
                            <i><img src="/sicstv/images/standard.png"/></i>
                        </c:if>
                        <c:if test="${item.tacticalVideo}">
                            <i><img src="/sicstv/images/tact.png"/></i>
                        </c:if>
                        <c:choose>
                            <c:when test="${item.analysisLevel != null && item.analysisLevel == 3}">
                            <i><img width="16" height="16" src="/sicstv/images/tag_empty.svg" title="<spring:message code="match.base.data"/>"/></i>
                            </c:when>
                            <c:when test="${item.isTagged()}">
                            <i><img width="16" height="16" src="/sicstv/images/tag.svg" title="<spring:message code="match.full.data"/>"/></i>
                            </c:when>
                        </c:choose>
                    </td>
                    <td class="matchesColumnResult2">
                        <c:if test="${item.idFixture != null && item.videoName != null}">
                            <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}">
                                <button class="uk-icon-hover uk-button uk-button-mini" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.options"/>"><i class="uk-icon-hover uk-icon-caret-down"></i></button>
                                <div class="uk-dropdown uk-dropdown-small uk-text-left">
                                    <ul class="uk-nav uk-nav-dropdown">
                                        <li><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=${idPlayer}&goals=false&event=&filter=&limit=50${removeDuplicates}"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.play"/></a></li>
                                                <c:if test="${item.analysisLevel != 0 && item.analysisLevel != 3 && !mUser.isPlayerUser()}">
                                            <li class="possessionLink"><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=&goals=false&event=SICS-_-POS&filter=&limit=50&startClip=true"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.distinct.clip"/></a></li>
                                            <li class="removeDuplicatesLink"><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=&goals=false&event=&filter=&limit=50${removeDuplicates}&startClip=true"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.possession.clip"/></a></li>
                                                    <c:if test="${item.homeTeamScore > 0 || item.awayTeamScore > 0}">
                                                <li class="goalLink"><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=&goals=false&event=SICS-_-RTF&filter=&limit=50&startClip=true"><i class="uk-icon-play uk-margin-right"></i><spring:message code="video.goal"/></a></li>
                                                    </c:if>
                                            <li class="highlightsLink"><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=&goals=false&event=&filter=&limit=50&highlights=true&startClip=true"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.highlights"/></a></li>
                                                </c:if>
                                                <c:if test="${item.minVideoQuality == 0 && !mUser.isPlayerUser()}">
                                            <li><a onclick="jsDownload(${item.idFixture}, 0);
                                                    return false;"><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.download"/></a></li>
                                                </c:if>
                                                <c:if test="${item.hd && !mUser.isPlayerUser() && item.providerId != null && item.providerId != 3}">
                                            <li><a onclick="jsDownload(${item.idFixture}, 1);
                                                    return false;"><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.downloadHD"/></a></li>
                                                </c:if>
                                                <c:if test="${item.fhd && !mUser.isPlayerUser() && item.providerId != null && item.providerId != 3}">
                                            <li><a onclick="jsDownload(${item.idFixture}, 2);
                                                    return false;"><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.downloadFHD"/></a></li>
                                                </c:if>
                                                <c:if test="${item.tacticalVideo && !mUser.isPlayerUser()}">
                                            <li><a onclick="jsDownload(${item.idFixture}, 3);
                                                    return false;"><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.downloadTACT"/></a></li>
                                                </c:if>
                                                <!--li><a href="/sicstv/user/video.htm?id=${item.id}&idComp=${item.competitionId}&idTeam=${mTeam.id}&idPlayer=&goals=true&event=&filter="><i class="uk-icon-soccer-ball-o uk-margin-right"></i><spring:message code="menu.user.goal"/></a></li-->
                                        <c:if test="${item.isTagged() && mUser.sportId == '0'}">
                                            <li><a href="#" onclick="showTabellinoCalendar(${item.idFixture});" ><i class="uk-icon-table uk-margin-right"></i><spring:message code="menu.user.tabellino"/></a></li>
                                                </c:if>
                                                <c:if test="${item.isExistReport() && !mUser.isPlayerUser()}">
                                            <%--<li><a href="#" onclick="jsDownloadReport(${item.idFixture});"><i class="uk-icon-newspaper-o uk-margin-right"></i><spring:message code="menu.user.report"/></a></li>--%>
                                            <li><a href="http://server.sics.it/sicsdataanalytics/auth/matchStudio.htm?fixtureId=${item.idFixture}&playerIds=&language=${mUser.tvLanguage}" target="_blank"><i class="uk-icon-newspaper-o uk-margin-right"></i><spring:message code="menu.user.report"/></a></li>
                                                </c:if>
                                                <c:if test="${item.isTagged() && mUser.sportId == '0' && !mUser.isPlayerUser()}">
                                            <li><a href="#" onclick="jsGetGameData(${item.idFixture}, 'xml');" ><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.xml"/></a></li>
                                                </c:if>
                                                <c:if test="${item.isTagged() && mUser.sportId == '0' && !mUser.isPlayerUser()}">
                                            <li><a href="#" onclick="jsGetGameData(${item.idFixture}, 'json');" ><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.json"/></a></li>
                                                </c:if>
                                    </ul>
                                </div>
                            </div>
                            <span class="squaredCheckbox uk-hidden-small"><input type="checkbox" id="checkboxCal_${cbPrefix}${item.idFixture}" onclick="jsSelectMatch('${item.idFixture}');"  value="${item.idFixture}"><label for="checkboxCal_${cbPrefix}${item.idFixture}"></label></span>
                            <!--<span style="margin-left: 2px;"><input type="checkbox" id="checkbox_${cbPrefix}${item.idFixture}" onclick="jsSelectMatch('${item.idFixture}');"  value="${item.idFixture}"></span>-->
                        </c:if>
                    </td>
                </tr>
            </c:forEach>
        </tbody>
    </table>

    <!-- This is the modal -->
    <div id="divShowTabellinoCalendar" class="uk-modal">
        <div id="divTabellinoContent" class="uk-modal-dialog">
            <a class="uk-modal-close uk-close"></a>
            <div id="divOverflowCalendar" class="uk-overflow-container">

            </div>
        </div>
    </div>
</div>