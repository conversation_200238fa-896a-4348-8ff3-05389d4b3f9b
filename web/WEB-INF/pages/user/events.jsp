<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>

<c:if test="${not empty mEvent}">
    <table id="azioni" class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
        <thead>
            <tr>
                <td colspan="8">
                    <button onclick="showEventsList('');" id="firstPageEvents" class="uk-button uk-button-small " title="<spring:message code='calendar.prima'/>"><i class="uk-icon-step-backward"></i> </button>
                    <button onclick="showEventsList('prev');" id="prevPageEvents" class="uk-button uk-button-small " title="<spring:message code='calendar.precedente'/>"><i class="uk-icon-angle-double-left"></i> </button>
                    <button onclick="showEventsList('next');" id="nextPageEvents" class="uk-button uk-button-small" title="<spring:message code='calendar.successiva'/>"><i class="uk-icon-angle-double-right"></i> </button>
                    <span id="pageShow" class="uk-margin-small-left"></span>
                </td>
            </tr>
            <tr>
                <th class="uk-hidden-small uk-text-center">
                    <span class="squaredCheckboxEvent uk-hidden-small"><input type="checkbox" id="checkboxSelectAllEvents" onclick="jsSelectAllEvents('azioni');"/>
                        <label for="checkboxSelectAllEvents"></label>
                    </span>
                </th>
                <th><spring:message code="video.descrizione"/></th>
                <th><spring:message code="video.half"/></th>
                <th><spring:message code="video.inizio"/></th>
                <th><spring:message code="video.durata"/></th>
                <th><spring:message code="video.squadra"/></th>
                <th><spring:message code="video.giocatore"/></th>
                <th><spring:message code="video.esito"/></th>
                    <c:if test="${mButtonToShow.size() > 1}">
                    <th><spring:message code="video.autore"/></th>
                    </c:if>
            </tr>
        </thead>
        <tbody>
            <c:set var="countEvents" value="0"/>
            <c:forEach var="list" items="${mEvent}">
                <c:forEach var="item" items="${list.value}" >
                    <c:set var="countEvents" value="${countEvents + 1}"/>

                    <tr id="eventRows" class="uk-table-middle uk-margin-small-bottom uk-margin-small-top eventToShow <c:if test="${countEvents >25}">uk-hidden</c:if>">
                            <td class="clickRow uk-text-center uk-hidden-small" style="width: 50px;">
                                <span class="squaredCheckboxEvent uk-hidden-small"><input type="checkbox" id="checkbox${item.id}" onclick="jsSelectAction('${item.id}');" value="${item.id}"/>
                                <label for="checkbox${item.id}"></label>
                                <!--																0							1						2						3								4					5					6				7								8				9				10					11			-->
                                <input type="hidden" id="startData${item.id}" value="${item.getSecStartAzione()}||${item.getSecEndAzione()}||${item.halfTrim(mLanguage)}||${item.getPeriod_start_minute()}||${item.getDurataAzioneMinSec(false)}||${item.getmTeam()}||${item.playerTrim()}||${item.getDescrAzioneToShow()}||${item.mIdEvent}||${item.videoId}||${item.videoName}||${item.provider}" />
                                <input type="hidden" id="descrToShow${item.id}" value="${item.getDescrAzioneToShow()}" />
                            </span>
                        </td>
                        <td class="clickRow" style="width: 300px;" onclick="startAction('${item.id}', 0);">
                            <div id="descrBox">
                                <span class="uk-float-left">${item.button.desc}<br>
                                    <c:set var="tagval" value=""/>
                                    <c:forEach var="tag" items="${item.mTags}" >
                                        <c:if test="${not empty tag}">
                                            <c:if test="${not empty tagval}"><c:set var="tagval" value="${tagval}, "/></c:if>
                                            <c:set var="tagval" value="${tagval}${tag.desc}"/>
                                        </c:if>
                                    </c:forEach>
                                    <span class="tag">${tagval}</span>
                                </span>

                                <input type="hidden" id="infoSearch${item.id}" class="infoSearch" value="${item.getInfoSearch()}#${list.key}||${item.mIdEvent}" />
                                <i class="uk-icon-play-circle uk-hidden uk-float-right uk-icon-small" style="color: green;"></i>
                                <span id="timeToMatch" style="display:none">${item.mStart}</span> <span id="timeToMatchEnd" style="display:none">${item.durataAzione}</span>
                            </div>
                        </td>
                        <td class="clickRow uk-text-center" onclick="startAction('startData${item.id}', 0);">${item.halfTrim(mLanguage)}</td>
                        <td class="clickRow uk-text-center" onclick="startAction('startData${item.id}', 0);">${item.getStartAzione()}</td>
                        <td class="clickRow uk-text-center" onclick="startAction('startData${item.id}', 0);">${item.durataAzione} </td>
                        <td class="clickRow" onclick="startAction('startData${item.id}', 0);">${item.mTeam} </td>
                        <td class="clickRow" onclick="startAction('startData${item.id}', 0);">${item.playerCamel()}</td>
                        <c:if test="${mButtonToShow.size() > 1}">
                            <td class="clickRow" style="width: 100px;" onclick="startAction('startData${item.id}', 0);">${item.user}</td>
                        </c:if>
                        <td class="clickRow uk-text-center" onclick="startAction('startData${item.id}', 0);">${item.getmResultReal()}</td>
                    </tr>
                </c:forEach>
            </c:forEach>
        <input type="hidden" value="${countEvents}" id="totEvents"/>
    </tbody>
</table>
</c:if>


