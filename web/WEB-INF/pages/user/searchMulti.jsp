<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>


<table>
    <tr>
        <td><label id="comboDateFrom" class="filter-select-label uk-contrast"><spring:message code="lib.dataDa"/></label></td>
        <td><input type="text" id="inputDateFromMulti" class="uk-form-width-medium  filter-select" data-uk-datepicker="{format:'DD/MM/YYYY'}"/></td>
    </tr>
    <tr>
        <td><label id="comboDateTo" class="filter-select-label uk-contrast"><spring:message code="lib.dataA"/> </label></td>
        <td><input type="text" id="inputDateToMulti" class="uk-form-width-medium filter-select" data-uk-datepicker="{format:'DD/MM/YYYY'}"/></td>
    </tr>
    <tr>
        <td><label id="lblModule" class="filter-select-label uk-contrast"><spring:message code="menu.user.modulo"/></label></td>
        <td>
            <div  class="uk-form-select uk-button uk-form uk-form-width-medium" data-uk-form-select>
                <span id="spanSelectModule"></span>
                <select id="selectModuleMulti">
                    <option value=""></option>
                    <c:forEach var="module" items="${modules}">
                        <option value="${module.descr}">${module.descr}</option>
                    </c:forEach>
                </select>
            </div>
        </td>
    </tr>
    <c:if test="${mTeam.id > -1}">
    <tr>
        <td><label id="lblModuleVs" class="filter-select-label uk-contrast"><spring:message code="menu.user.moduloVS"/></label></td>
        <td>
            <div  class="uk-form-select uk-button uk-form uk-form-width-medium" data-uk-form-select>
                <span id="spanSelectModuleVs"></span>
                <select id="selectModuleVsMulti">
                    <option value=""></option>
                    <c:forEach var="module" items="${modules}">
                        <option value="${module.descr}">${module.descr}</option>
                    </c:forEach>
                </select>
            </div>
        </td>
    </tr>
    
    <tr>
        <td><label id="lblScore" class="filter-select-label uk-contrast"><spring:message code="menu.user.risultato"/></label></td>
        <td>
            <div  class="uk-form-select uk-button uk-form uk-form-width-medium" data-uk-form-select>
                <span id="spanSelectScore"></span>
                <select id="selectScoreMulti" >
                    <option value=""></option>
                    <option value="V"><spring:message code="menu.user.vittorie"/></option>
                    <option value="N"><spring:message code="menu.user.pareggi"/></option>
                    <option value="P"><spring:message code="menu.user.sconfitte"/></option>
                </select>
            </div>
        </td>
    </tr>
    <tr>
        <td><label id="lblHomeAway" class="filter-select-label uk-contrast"><spring:message code="menu.user.casatrasferta"/></label></td>
        <td>
            <div  class="uk-form-select uk-button uk-form uk-form-width-medium" data-uk-form-select>
                <span id="spanSelectHomeAway"></span>
                <select id="selectHomeAwayMulti">
                    <option value=""></option>
                    <option value="home"><spring:message code="menu.user.incasa"/></option>
                    <option value="away"><spring:message code="menu.user.intrasferta"/></option>
                </select>
            </div>
        </td>
    </tr>
</c:if>
    <tr>
        <td></td>
        <td><button id="applyFilter" class="uk-button uk-button-small uk-block uk-align-left uk-modal-close uk-margin-top" onclick="filterData();"><spring:message code="video.ricerca"/></button></td>
    </tr>
</table>

