<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">
        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>
        
        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/flat/flat.css?<%=System.currentTimeMillis()%>" rel="stylesheet">
        
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>
        
        <script type="text/javascript">
            // logout ajax
            var allGames = [];
            var allPlaylists = [];
            var zipSelected = 0;
            var canDeleteSelected = 0;
            var isMyHome = true;
            var isPersonalArchive = true;
            var selectedPlaylist = new Map();
            var selectedGame = new Map();
            var currentPlaylistId;
            
            $(document).ready(function () {
                initSearchFilter();
            });
            
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);
            
            jsLoadPlaylistTv(true);
            
            function showPlaylistDetails(id) {
                currentPlaylistId = id;
                $('.currentClip').removeClass("currentClip");
                $('#playlistTvRowId' + id + ' td:not(:first-child)').addClass("currentClip");
                
                id = "azioniPlaylist" + id;
                document.querySelectorAll(".playlistTvDetails").forEach(function (element) {
                    var tmpId = element.id;
                    if (tmpId !== id) {
                        $('#' + tmpId).addClass("uk-hidden");
                    } else {
                        $('#' + tmpId).removeClass("uk-hidden");
                        
                        // Make the table rows sortable using jQuery UI's sortable() method
//                        $('#' + tmpId + ' tbody').sortable({
//                            // Update the DataTable order when the user finishes dragging and dropping
//                            update: function(event, ui) {
//                                $.blockUI({
//                                    message: '<div class="loader"></div>',
//                                    css: {
//                                        border: 'none',
//                                        padding: '0',
//                                        backgroundColor: 'transparent',
//                                        textAlign: 'center',
//                                        fontFamily: 'Arial, sans-serif',
//                                        display: 'flex',
//                                        justifyContent: 'center',
//                                        alignItems: 'center'
//                                    },
//                                    overlayCSS: {
//                                        backgroundColor: '#545454'
//                                    }
//                                });
//                                
//                                // Get the new order of the rows
//                                const newOrder = $(this).sortable("toArray", { attribute: "data-id" });
//                                
//                                $.ajax({
//                                    type: "POST",
//                                    url: "/sicstv/user/updateClipTvOrder.htm",
//                                    cache: false,
//                                    data: encodeURI("&playlistId=" + currentPlaylistId + "&eventIdsWithOrder=" + newOrder.slice(1)),
//                                    success: function (msg) {
//                                        if (msg === "true") {
//                                            UIkit.notify("<spring:message code="playlist.update.success"/>", {status: 'success', timeout: 1000});
//                                        } else {
//                                            UIkit.notify("<spring:message code="playlist.update.error"/>", {status: 'danger'});
//                                            $('#' + tmpId + ' tbody').sortable('cancel');
//                                        }
//                                        $.unblockUI();
//                                    },
//                                    error: function () {
//                                        UIkit.notify("<spring:message code="playlist.update.error"/>", {status: 'danger'});
//                                        $('#' + tmpId + ' tbody').sortable('cancel');
//                                        $.unblockUI();
//                                    }
//                                });
//                            }
//                        });
                    }
                });
            }
            
            function restorePlaylistTvEvent(playlistId, eventId) {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/restorePlaylistTvEvent.htm",
                    cache: false,
                    data: encodeURI("playlistId=" + playlistId + "&eventId=" + eventId),
                    success: function (msg) {
                        if (msg === "true") {
                            UIkit.notify("<spring:message code="playlist.restore.success"/>", {status: 'success', timeout: 1000});
                            jsShowBlockUI();
                            reloadEventList();
                        } else {
                            UIkit.notify("<spring:message code="playlist.restore.error"/>", {status: 'danger', timeout: 1000});
                        }
                    },
                    error: function () {
                        UIkit.notify("<spring:message code="playlist.restore.error"/>", {status: 'danger', timeout: 1000});
                    }
                });
            }
            
            function deletePlaylistTvEvent(playlistId, eventId) {
                if (window.confirm("<spring:message code="playlist.event.deleted.permanent.question"/>")) {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/deletePlaylistTvEvent.htm",
                        cache: false,
                        data: encodeURI("id=" + playlistId + "&eventIds=" + eventId),
                        success: function (msg) {
                            if (msg === "true") {
                                UIkit.notify("<spring:message code="playlist.permanent.delete.success"/>", {status: 'success', timeout: 1000});
                                jsShowBlockUI();
                                reloadEventList();
                            } else {
                                UIkit.notify("<spring:message code="playlist.permanent.delete.error"/>", {status: 'danger', timeout: 1000});
                            }
                        },
                        error: function () {
                            UIkit.notify("<spring:message code="playlist.permanent.delete.error"/>", {status: 'danger', timeout: 1000});
                        }
                    });
                }
            }
            
            // NB: IL .load() RICARICA LE VARIABILI DI SPRING
            // USANDO INVECE LA CHIAMATA AJAX E IL .html() PER AGGIORNARE, LE VARIABILI NON VENGONO AGGIORNATE
            function reloadEventList() {
                $.ajax({
                    url: document.URL,
                    type: 'GET',
                    dataType: 'html',
                    success: function(data) {
                        var updatedContent = $(data).find('#playlistTvEventList').html();
                        $('#playlistTvEventList').html(updatedContent);
                        reloadPage(false); // ricarica la parte di sinistra per aggiornare l'ultimo update
                        
                        $.unblockUI();
                    },
                    error: function(xhr, status, error) {
                        console.log(xhr.status + ' ' + xhr.statusText);
                        $.unblockUI();
                    }
                });
            }
        </script>
    </head>
    <body>
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>
        
        <div id="breadcrumb">
            <ul class="uk-breadcrumb uk-margin-left">
                <li style="text-transform: uppercase;"><a href="/sicstv/user/myhome.htm"><spring:message code="menu.user.mysicstv"/></a></li>
                <li style="text-transform: uppercase;"><spring:message code="menu.user.playlistTv"/></li>
            </ul>
        </div>
        
        <div style="display: flex; justify-content: space-between; margin-top: 10px">
            <div id="playlistTv" class="dynamicWidth" style="margin-top: -15px">

            </div>
            <div id="playlistTvEventList" class="dynamicWidth hideForMobile">
                <c:set var="countEvents" value="1"/>
                <c:forEach var="list" items="${mPlaylistWithActions}">
                    <table id="azioniPlaylist${list.key}" class="uk-table uk-table-hover uk-table-striped playlistTvDetails<c:if test="${countEvents != 0}"> uk-hidden</c:if>" style="border-collapse: collapse; margin-top: 0px">
                        <c:set var="countEvents" value="${countEvents + 1}"/>
                        <tbody>
                            <tr>
                                <td class="playlistTvTitle"><spring:message code="video.descrizione"/></td>
                                <td class="playlistTvTitle"><spring:message code="video.half"/></td>
                                <td class="playlistTvTitle"><spring:message code="video.inizio"/></td>
                                <td class="playlistTvTitle"><spring:message code="video.durata"/></td>
                                <td class="playlistTvTitle"><spring:message code="video.squadra"/></td>
                                <td class="playlistTvTitle"><spring:message code="video.partita"/></td>
                                <td class="playlistTvTitle"><spring:message code="video.giocatore"/></td>
                                <td class="playlistTvTitle"><spring:message code="video.esito"/></v>
                                <td class="playlistTvTitle"><spring:message code="video.autore"/></td>
                            </tr>
                            <c:forEach var="item" items="${list.value}">
                                <tr class="uk-table-middle uk-margin-small-bottom uk-margin-small-top eventToShow playlistTvRow <c:if test="${item.deleted != null && item.deleted}">deletedClip</c:if>" id="rowEventId${item.id}" data-id="${item.id}">
                                    <td style="width: 250px;">
                                        <div class="descrBox">
                                            <span class="uk-float-left">${item.button.descTrim(mLanguage)}<br>
                                                <c:set var="tagval" value=""/>
                                                <c:forEach var="tag" items="${item.mTags}" >
                                                    <c:if test="${not empty tag}">
                                                        <c:if test="${not empty tagval}"><c:set var="tagval" value="${tagval}, "/></c:if>
                                                        <c:set var="tagval" value="${tagval}${tag.descLan(mLanguage)}"/>
                                                    </c:if>
                                                </c:forEach>
                                                <c:if test="${not empty tagval}">
                                                    <span class="tag">${tagval}</span>
                                                    <br>
                                                </c:if>
                                                <c:if test="${not empty item.mNote}">
                                                    <c:if test="${not empty tagval}">
                                                    <br>
                                                    </c:if>
                                                    <span class="tag">${item.mNote}</span>
                                                </c:if>
                                                <c:if test="${item.deleted != null && item.deleted}">
                                                    <span class="tag">(<spring:message code="playlist.event.deleted"/>. <a onclick="restorePlaylistTvEvent(${list.key}, ${item.id})"><spring:message code="playlist.event.deleted.restore"/>.</a><br><a onclick="deletePlaylistTvEvent(${list.key}, ${item.id})"><spring:message code="playlist.event.deleted.permanent"/>.</a>)</span>
                                                </c:if>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="uk-text-center uk-hidden-small">${item.halfTrim(mLanguage)}</td>
                                    <td class="uk-text-center uk-hidden-small">${item.getStartAzione()}</td>
                                    <td class="uk-text-center uk-hidden-small">${item.durataAzione}</td>
                                    <td >${item.mTeam}</td>
                                    <td style="width: 200px;">${item.getMatch()} </td>
                                    <td >${item.playerCamel()}</td>
                                    <td class="uk-text-center uk-hidden-small">${item.getmResultReal()}</td>
                                    <td style="width: 100px;">${item.user}</td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </c:forEach>
            </div>
        </div>
    </body>
</html>
