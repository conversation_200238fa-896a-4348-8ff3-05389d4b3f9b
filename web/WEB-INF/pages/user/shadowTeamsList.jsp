<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/css/flat/flat.css" rel="stylesheet">
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />

        <!-- Keep here: needed for teamStats.jsp -->
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/3.2.1/css/fixedColumns.dataTables.min.css"/>
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdn.datatables.net/fixedcolumns/3.2.1/js/dataTables.fixedColumns.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.colVis.min.js"></script>
        <!-- Keep here: needed for teamStats.jsp for buttons (export) -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
        <script src="https://cdn.datatables.net/colreorder/1.7.0/js/dataTables.colReorder.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>

        <!-- D3.js Library for canvas/svg -->
        <script src="https://d3js.org/d3.v5.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.1/d3-tip.min.js"></script>
        <script src="/sicstv/js/sics-canvas-shadow-teams.js?<%=System.currentTimeMillis()%>"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <style>
            .uk-table td {
                font-size: 17px !important;
            }
        </style>
        
        <script type="text/javascript">
            $(document).ready(function () {

            });

            function editShadowTeam(id) {
                var link = "/sicstv/user/shadowTeams.htm?id=";
                if (typeof id !== "undefined") {
                    link += id;
                }
                location.href = link;
            }

            function deleteShadowTeam(id) {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/deleteShadowTeam.htm",
                    cache: false,
                    data: encodeURI("id=" + id),
                    success: function (result) {
                        if (result === "ko") {
                            UIkit.notify("<spring:message code="generic.error"/>", {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="shadow.team.deleted"/>", {status: 'success', timeout: 1000});
                            location.reload();
                        }
                    }
                });
            }
        </script>
    </head>
    <body>
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>

        <div id="breadcrumb">
            <ul class="uk-breadcrumb uk-margin-left ">
                <li><spring:message code="shadow.teams"/></li>
            </ul>
        </div>

        <div class="uk-flex">
            <div class="uk-width-2-10 uk-margin-left" style="border-right: 1px solid black">
                <div class="uk-margin-small-top">
                    <center>
                        <h3 class="uk-text-uppercase"><spring:message code="shadow.teams"/></h3>
                    </center>
                </div>
            </div>
            <div id="container" class="uk-width-8-10" style="padding: 5px">
                <div>
                    <button class="uk-button uk-button-large" onclick="editShadowTeam();"><spring:message code="shadow.team.add"/></button>
                </div>
                <table id="shadowTeamList" class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-width-1-1">
                    <thead>
                        <tr>
                            <th><spring:message code="shadow.team.name"/></th>
                            <th><spring:message code="shadow.team.module"/></th>
                            <th><spring:message code="shadow.team.owner"/></th>
                            <th><spring:message code="shadow.team.shared"/></th>
                            <th><spring:message code="shadow.team.actions"/></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="shadowTeam" items="${mShadowTeams}">
                            <tr>
                                <td style="vertical-align: middle">
                                    <a href="/sicstv/user/shadowTeams.htm?id=${shadowTeam.id}">${shadowTeam.name}</a>
                                </td>
                                <td style="vertical-align: middle">${shadowTeam.getModuleName()}</td>
                                <td style="vertical-align: middle">${shadowTeam.userName}</td>
                                <td style="vertical-align: middle">
                                    <c:choose>
                                        <c:when test="${shadowTeam.shared != null && shadowTeam.shared}">
                                            <spring:message code="shadow.team.shared"/>
                                        </c:when>
                                        <c:otherwise>
                                            <spring:message code="shadow.team.personal"/>
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td style="vertical-align: middle">
                                    <i class="uk-icon-pencil cursor-pointer" onclick="editShadowTeam(${shadowTeam.id});"></i>
                                    <i class="uk-icon-trash cursor-pointer uk-margin-left" onclick="deleteShadowTeam(${shadowTeam.id});"></i>
                                </td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
    </body>
</html>