<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="display" uri="http://displaytag.sf.net" %>

<div style="padding: 5px">
    <div>
        <button class="uk-button uk-button-large" onclick="editPlayerPersonal();"><spring:message code="private.db.add.player"/></button>
        <br/>
        Show All <input id="showAllCheckbox" type="checkbox" onclick="loadPlayerPersonal();" ${mAll != null && mAll ? 'checked' : ''}/>
    </div>
    <table id="playerPersonalList" class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-width-1-1">
        <thead>
            <tr>
                <th><spring:message code="private.db.photo"/></th>
                <th><spring:message code="private.db.name"/></th>
                <th><spring:message code="private.db.team"/></th>
                <th><spring:message code="private.db.owner"/></th>
                <th><spring:message code="private.db.shared"/></th>
                <th><spring:message code="private.db.actions"/></th>
            </tr>
        </thead>
        <tbody>
            <c:forEach var="player" items="${mPlayers}">
                <tr <c:if test="${player.id != null}">style="background-color: #ffe7a3 !important"</c:if>>
                    <c:if test="${player.id != null}">
                        <td>
                            <img width="35px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${player.photo}.png" onerror="this.src='/sicstv/images/user_gray.png'"/>
                        </td>
                    </c:if>
                    <c:if test="${player.id == null}">
                        <td>
                            <img width="35px" src="/sicstv/images/user_gray.png"/>
                        </td>
                    </c:if>
                    <td style="vertical-align: middle">${player.first_name} ${player.last_name}</td>
                    <td style="vertical-align: middle">${player.teamName}</td>
                    <td style="vertical-align: middle">${player.userName}</td>
                    <td style="vertical-align: middle">
                        <c:choose>
                            <c:when test="${player.shared != null && player.shared}">
                                <spring:message code="private.db.shared"/>
                            </c:when>
                            <c:otherwise>
                                <spring:message code="private.db.personal"/>
                            </c:otherwise>
                        </c:choose>
                    </td>
                    <c:if test="${player.id != null}">
                        <td></td>
                    </c:if>
                    <c:if test="${player.id == null}">
                        <td style="vertical-align: middle">
                            <i class="uk-icon-pencil cursor-pointer" onclick="editPlayerPersonal(${player.personalId});"></i>
                            <i class="uk-icon-trash cursor-pointer uk-margin-left" onclick="deletePlayerPersonal(${player.personalId});"></i>
                        </td>
                    </c:if>
                </tr>
            </c:forEach>
        </tbody>
    </table>
</div>