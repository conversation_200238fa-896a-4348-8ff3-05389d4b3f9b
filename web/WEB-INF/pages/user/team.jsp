<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/amcharts.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/css/flat/flat.css" rel="stylesheet">
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />

        <!-- Keep here: needed for teamStats.jsp -->
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/3.2.1/css/fixedColumns.dataTables.min.css"/>
        <script src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdn.datatables.net/fixedcolumns/3.2.1/js/dataTables.fixedColumns.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.colVis.min.js"></script>
        <!-- Keep here: needed for teamStats.jsp for buttons (export) -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
        <script src="https://cdn.datatables.net/colreorder/1.7.0/js/dataTables.colReorder.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <!-- D3.js Library for canvas/svg -->
        <script src="https://d3js.org/d3.v5.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.1/d3-tip.min.js"></script>
        <script src="/sicstv/js/sics-canvas-player.js?<%=System.currentTimeMillis()%>"></script>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>

        <!-- AmCharts -->
        <script src="https://cdn.amcharts.com/lib/5/index.js"></script>
        <script src="https://cdn.amcharts.com/lib/5/xy.js"></script>
        <script src="https://cdn.amcharts.com/lib/5/radar.js"></script>
        <script src="https://cdn.amcharts.com/lib/5/themes/Animated.js"></script>

        <script src="/sicstv/js/jscolor.min.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        
        <script type="text/javascript">
            jscolor.presets.default = {
                position: 'right',
                palette: [
                    '#ff8080', '#ffff80', '#80ff80', '#00ff80', '#80ffff', '#0080ff', '#ff80c0', '#ff80ff',
                    '#ff0000', '#ffff00', '#80ff00', '#00ff40', '#00ffff', '#0080c0', '#8080c0', '#ff00ff',
                    '#804040', '#ff8040', '#00ff00', '#008080', '#004080', '#8080ff', '#800040', '#ff0080',
                    '#800000', '#ff8000', '#008000', '#008040', '#0000ff', '#0000a0', '#800080', '#8000ff',
                    '#400000', '#804000', '#004000', '#004040', '#000080', '#000040', '#400040', '#400080',
                    '#000000', '#808000', '#808040', '#999999', '#408080', '#c0c0c0', '#404040', '#ffffff'
                ]
            };

            var teamStatsMessage = "<spring:message code="team.stats.note"/>";
            var pageStatus = [];
            var totMatches = [];
            var numMatches = [];
            var matchPerPage = 25;
            var generalPage = false;
            var matchesLoaded = false;
            var lastDivCompetitions;
            var dropzone;

            $(document).ready(function () {
                // Configura Dropzone
                Dropzone.autoDiscover = false;

                // gestione serie D
            <c:set var="isSerieD" value="${false}"/>
            <c:forEach var="item" items="${formCompetitionTeam}">
                <c:if test="${item.id == 12}">
                    <c:set var="isSerieD" value="${true}"/>
                </c:if>
            </c:forEach>
            <c:if test="${isSerieD && mGirone == null}">
                $("#competitions").find("p").each(function (index, element) {
                    var text = $(element).text();
                    if (text.startsWith("SERIE D")) {
                        var relativeDiv = $(element).prev("div");
                        var divId = $(relativeDiv).attr("id");
                        if (divId.startsWith("12")) {
                            var groupId = divId.replace("12-", "");
                            if (!isNaN(parseInt(groupId))) {
                                $(relativeDiv).click();
                                return;
                            }
                        }
                    }
                });
            </c:if>

                generalPage = $("#generalPageTeam").val();
//                if (generalPage === "false") {
//                    jsRefreshMatches(1);
//                }
                jsRefreshMatches(1);
                initSearchFilter();
                readFilterMulti(true);
                currentFilterMulti['team'] = $("#idTeam").val();
                //filterMatches('allMatches', 1);
                lastDivCompetitions = $('#divCompetitions').html();

                if ($(window).width() <= 768) {
                    $("#liTeamInfo").addClass("uk-active");
                    $("#liTeamInfoContent").addClass("uk-active");
                    // tolto perchè ora mostro sempre le partite anche se non filtrato per competizione
//                    if (generalPage === 'false') {
//                        $("#calendarTab1").removeClass("uk-active");
//                        $("#matches").removeClass("uk-active");
//                    } else {
//                        $("#competitionTab").removeClass("uk-active");
//                        $("#competitions").removeClass("uk-active");
//                    }
                } else {
                    // tolto perchè ora mostro sempre le partite anche se non filtrato per competizione
//                    if (generalPage === 'false') {
//                        $("#calendarTab1").addClass("uk-active");
//                        $("#matches").addClass("uk-active");
//                    } else {
//                        $("#competitionTab").addClass("uk-active");
//                        $("#competitions").addClass("uk-active");
//                    }

                    $("#liTeamInfo").removeClass("uk-active");
                    $("#liTeamInfoContent").removeClass("uk-active");
                }
                changePanel('');

                // evidenzio la competizione selezionata se disponibile
                var selectedCompetition = new URL(document.URL).searchParams.get("formCompetitionId");
                var selectedGroupId = new URL(document.URL).searchParams.get("groupId");
                if (typeof selectedGroupId !== 'undefined' && selectedGroupId !== null && typeof selectedCompetition !== 'undefined' && selectedCompetition !== null) {
                    $("#" + selectedCompetition + "-" + selectedGroupId).addClass("uk-button-active");
                } else if (typeof selectedCompetition !== 'undefined' && selectedCompetition !== null) {
                    $("#" + selectedCompetition).addClass("uk-button-active");
                } else {
                    $("#-1").addClass("uk-button-active");
                }

                // per qualche motivo le checkbox vengono resettate
                // fix temporanea -> uso timer di 500ms per poi impostarle
                var idsToCheck = $("#gameIds").val();
                if (idsToCheck !== '') {
                    selectedMatches = idsToCheck.split(",");
                }

            <c:if test="${mElementToClick != null}">
                $("#${mElementToClick}").click();
            </c:if>
            <c:if test="${mElementToClick == null && !personalSource}">
                $("#filterSicsOverviewButton").click();
            </c:if>
            <c:if test="${mElementToClick == null && personalSource}">
                $("#filterSicsSearchButton").click();
            </c:if>

            <c:if test="${!generalPageTeam}">
                loadMatchesResume();
            </c:if>

                drawField('canvasContainer', pitch);
            <c:if test="${mTeamPlayerPoints.size() > 0}">
                <c:forEach var="position" items="${mTeamPlayerPoints}">
                drawPointTeam(${position.x}, ${position.y}, '${mTeamColor}');
                </c:forEach>
            </c:if>

                // bind click for class playtime-check 
                $(".playtime-check").on("click", function (e) {
                    var playtimeValue = parseInt($(this).attr("playtime"));
                    var competitionId = $(this).attr("competitionid");
                    var hasPlayed = $(this).attr("hasplayed") === "true";
                    // se playtimeValue è nan lo prendo dall'elemento padre
                    if (isNaN(playtimeValue)) {
                        parseInt($(this).parent().attr("playtime"));
                    }
                    var playerId = $(this).attr("id");
                    var checkPlaytime = ${param.checkPlaytime == 'false'};

                    if (checkPlaytime && competitionId !== null && competitionId !== "" && (playtimeValue <= 0 || !hasPlayed)) {
                        e.preventDefault();
                        showNoDataModal(playerId);
                    } else {
                        var strUrl = $(this).attr("redirect-link");
                        location.href = strUrl;
                    }
                });
            });

            function loadMatchesResume() {
                if (matchesLoaded) {
                    // carico ora se posso i dati delle ultime 5 partite
                    if ($("#matchTablematches > tbody > tr:not(:first-child)").length > 0) {
                        var itemToAdd = [];

                        $("#matchTablematches > tbody > tr:not(:first-child)").each(function () {
                            if (itemToAdd.length < 5) {
                                var date = $(this).find("td").eq(0).find("div").find("b").html().replaceAll("<br>", " ");

                                var teams = $(this).find("td").eq(1).find("a").find("b").html();
                                var splitted = teams.split(" - ");
                                var side = "";
                                if (splitted[0] === "${mTeam.name}") {
                                    side = "1";
                                } else if (splitted[1] === "${mTeam.name}") {
                                    side = "2";
                                }

                                if (side !== "") {
                                    var scoreHome = 0;
                                    var scoreAway = 0;
                                    var scores = $(this).find("td").eq(2).find("span").html();
                                    splitted = scores.split(" - ");
                                    if (side === "1") {
                                        scoreHome = parseInt(splitted[0]);
                                        scoreAway = parseInt(splitted[1]);
                                    } else {
                                        scoreAway = parseInt(splitted[0]);
                                        scoreHome = parseInt(splitted[1]);
                                    }

                                    if (!isNaN(scoreHome) && !isNaN(scoreAway)) {
                                        if (scoreHome > scoreAway) {
                                            var img = $("<img>");
                                            img.attr("width", "25px");
                                            img.attr("src", "/sicstv/images/won.png");
                                            img.attr("title", date + " | " + teams);

                                            itemToAdd.push(img);
                                        } else if (scoreHome === scoreAway) {
                                            var img = $("<img>");
                                            img.attr("width", "25px");
                                            img.attr("src", "/sicstv/images/match-draw.png");
                                            img.attr("title", date + " | " + teams);

                                            itemToAdd.push(img);
                                        } else {
                                            var img = $("<img>");
                                            img.attr("width", "25px");
                                            img.attr("src", "/sicstv/images/lost.png");
                                            img.attr("title", date + " | " + teams);

                                            itemToAdd.push(img);
                                        }
                                    }
                                }
                            }
                        });

                        if (itemToAdd.length > 0) {
                            // itemToAdd.reverse();
                            $.each(itemToAdd, function (index, value) {
                                var element = value;
                                $("#andamentoParagraph").after(element);
                                $("#andamentoParagraphMobile").after(element.clone());
                            });
                        }
                    } else {
                        var notAvailable = $("<p>");
                        notAvailable.attr("style", "font-size: 14px; margin: 0");
                        notAvailable.html("N/A");
                        $("#andamentoParagraph").after(notAvailable);
                        $("#andamentoParagraph").after(notAvailable.clone());
                    }
                } else {
                    setTimeout(loadMatchesResume, 250);
                }
            }

            var arrayMatch = [];
            /*
             function jsDownload(id) {
             
             var strUrl = "/sicstv/user/download.htm?id=" + id;
             $.ajax({
             type: "GET",
             url: strUrl,
             cache: false,
             success: function (msg) {
             if (msg.substr(0, 4) === "true") {
             UIkit.notify(msg.substr(5), {status: 'danger'});
             } else {
             UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success'})
             window.location.replace(msg.substr(5));
             }
             }
             });
             }
             */

            // logout ajax
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);

            function jsRefreshMatches(page) {
                var idC = $("#idComp").val();
                var idT = $("#idTeam").val();
                refreshMatches('matches', 'matches', page, idC, idT);
            }

            function switchMatchday(step) {

                var valDay = parseInt($("#mDay").val());
                if (step === "prev") {
                    valDay = valDay - 1;
                } else if (step === "next") {
                    valDay = valDay + 1;
                } else {
                    valDay = step;
                }

                if (valDay >= 1 && valDay <= parseInt($("#mMaxDay").val())) {
                    //jsLoadingAlpha("#matchDayCalendar");
                    $("#mDay").val(valDay);
                    jsRefreshMatchesCalendar(${mComp.id});
                }
            }

            function addMatch(id, check) {
                if ($("#" + check).is(':checked')) {
                    arrayMatch.push(id);
                } else {
                    arrayMatch.splice($.inArray(id, arrayMatch), 1);
                }
            }

            function submitMulti() {
                var ids = "";
                var idT = $("#idTeam").val();
                $.each(arrayMatch, function (index, value) {
                    if (!ids.trim()) {
                        ids = value;
                    } else {
                        ids = ids + '-' + value;
                    }
                });
                location.href = '/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=' + ids + "&idComp=-1&idTeam" + idT + "=&idPlayer=&goals=false&event=&filter=&limit=50";
            }

            function filterData() {
                readFilterMulti(false);
                filterMatches('matches', 1, '');
            }

            function showTeamEvents(type) {
                var idT = $("#idTeam").val();
                var idC = $("#idComp").val();
                if (idC === '') {
                    idC = "-1";
                }
                var filtro = $("#gameFilter").val();
                if (typeof filtro === 'undefined') {
                    filtro = "";
                }
                var gameIds = $("#gameIds").val();
                if (typeof gameIds === 'undefined') {
                    gameIds = "";
                }
                var groupId = new URL(document.URL).searchParams.get("groupId");
                if (typeof groupId === 'undefined' || !groupId || groupId === null) {
                    groupId = "";
                }
                var strUrl = "/sicstv/user/video.htm?personal=${personalSource}&id=&idComp=" + idC + "&idTeam=" + idT + "&idPlayer=&goals=false&event=" + escape(type) + "&filter=" + escape(filtro) + "&limit=50&gameIds=" + gameIds + "&groupId=" + groupId;
                location.href = strUrl;
            }

            function changePanel(element) {
                var panelName = $(".filterSelect option:selected").val();
                var onlyOneConfig = ${filterToShow.size() == 1};

                if (element.length > 0) {
                    panelName = $("#" + element).val();
                    $(".configChoiceMobile").removeClass("uk-button-success");
                    $("#" + element).addClass("uk-button-success");
                }
                var divContent1 = "<div class='uk-float-left panelDiv panelSicsDiv'>", divContent2 = "<div class='uk-float-right panelDiv panelSicsDiv'>";
            <c:forEach items="${filterToShow}" var="list" varStatus="status" >
                if ('${list.key}' === panelName || onlyOneConfig) {
                    if ('${list.key}' === "SICS") {
                        $(".filterGroupset").addClass("uk-hidden");
                        $("#filterSicsSearchDiv").removeClass("uk-hidden");
                        $("#filterSicsSearchDiv > button:not(:first-child)").each(function (index, element) {
                            $("#" + element.id).removeProp("disabled");
                        });
                        $("#filterSicsTeamStatsButton").removeProp("disabled");
                    } else {
                        showSicsFilters('search');
                        $("#filterSicsSearchDiv > button:not(:first-child)").each(function (index, element) {
                            $("#" + element.id).prop("disabled", "disabled");
                        });
                        $("#filterSicsTeamStatsButton").prop("disabled", "disabled");
                        $("#filterSicsSearch").addClass("uk-hidden");
                        $(".filterGroupset").removeClass("uk-hidden");
                        $("#filterSicsSearchAdvanced").addClass("uk-hidden");
                        $("#filterSicsTeamStats").addClass("uk-hidden");
                        $("#filterSicsTeamStatsPlayers").addClass("uk-hidden");
                <c:forEach items="${list.value}" var="object">
                    <c:set var="counter" value="1"></c:set>

                    <c:forEach items="${object}" var="btn">

                        var content = "<button class='uk-button teamFilterButton'";
                        if ('${btn.value[1]}' === 'false') {
                            content += "disabled ";
                        }
                        content += "onclick=\"showTeamEvents('${list.key}-_-${btn.key}');\" title='${btn.value[0]}'><i class='uk-icon-video-camera' ></i>${btn.value[0]} (${btn.value[2]})</button>";
                        <c:choose>
                            <c:when test="${counter % 2 == 0}">
                        divContent1 += content;
                                <c:set var="counter" value="1"></c:set>
                            </c:when>
                            <c:when test="${counter % 2 != 0}">
                        divContent2 += content;
                                <c:set var="counter" value="2"></c:set>
                            </c:when>
                        </c:choose>
                    </c:forEach>
                </c:forEach>
                        divContent1 += "</div>";
                        divContent2 += "</div>";

                        $(".filterGroupset").html(divContent1 + divContent2);
                    }
                }
            </c:forEach>

            }

            function jsFilterGames(removeFilters) {
                if (removeFilters) {
                    var currentUrl = window.location.href;
                    if (currentUrl.includes("gameIds")) {
                        currentUrl = currentUrl.substring(0, currentUrl.indexOf("&gameIds"));
                    }
                    window.location.href = currentUrl;
                } else if (selectedMatches.length > 0) {
                    var currentUrl = window.location.href;
                    if (currentUrl.includes("gameIds")) {
                        currentUrl = currentUrl.substring(0, currentUrl.indexOf("&gameIds"));
                    }
                    window.location.href = currentUrl + "&gameIds=" + selectedMatches;
                }
            }

            function jsSetButtonSelected(id, type, idBase, comment) {
                /**
                 * type = 0 ->  fondamentale
                 * type = 1 ->  tag
                 * type = 2 ->  squadre 
                 * type = 3 ->  giocatori
                 * type = 4 ->  partite
                 * type = 5 ->  quickSearch
                 * type = 6 ->  autore
                 
                 */

                // resetto le azioni selezionate
                //console.log("id: " + id + " type: " + type + " idBase: " + idBase + " comment: " + comment)
                $("#numCheckedEvents").html(0);
                $("#checkedActionsButton").addClass("uk-hidden");
                $("#checkboxSelectAllEvents").removeAttr("checked");
                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
                $("#checkboxSelectAllEvents").prop("checked", false);
                $("#checkboxSelectAllEventsSmall").prop("checked", false);

                //console.log(id + " - " + type + " - " + idBase);
                if (openTag) {
                    openTag = false;
                } else if (removeFilter) {
                    removeFilter = false;
                    jsRemoveFilter(id, type);
                    applyFilterEvents(true);
                } else {
                    if ($("#" + id).hasClass('uk-button-success')) {
                        $("#" + id).removeClass("uk-button-success");
                        if (type === 2) {
                            $("#quickChoice" + id).removeClass("uk-button-success");
                        }
                    } else {
                        $("#" + id).addClass("uk-button-success");
                        if (type === 2) {
                            $("#quickChoice" + id).addClass("uk-button-success");
                        }
                    }

                    var val = $("#" + id).val();
                    if (type === 0) {
                        // evento
                        // determino se il bottone cliccato è presente o no tra i filtri
                        if ($.inArray(id, eventSelected) === -1) {
                            eventSelected.push(id);
                            $("[id^=" + id + "_]").each(function (i, obj) {
                                if ($.inArray(obj.id, eventSelected) === -1) {
                                    eventSelected.push(obj.id);
                                    if (typeof $("#" + obj.id).attr("disabled") === 'undefined') {
                                        $("#" + obj.id).attr("checked", "checked");
                                    }
                                }
                            });
                        } else {
                            eventSelected.splice($.inArray(id, eventSelected), 1);
                            $("[id^=" + id + "_]").each(function (i, obj) {
                                if ($.inArray(obj.id, eventSelected) !== -1) {
                                    eventSelected.splice($.inArray(obj.id, eventSelected), 1);
                                    $("#" + obj.id).removeAttr("checked");
                                }
                            });
                        }
                        updateNumEventAndTagCount(id);

                    }
                    applyFilterEvents(type === 0);
                }
            }

            var advancedFilter = new Map();
            function showSicsFilters(type) {
                if (type === 'search') {
                    $("#filterSicsOverview").addClass("uk-hidden");
                    $("#filterSicsOverviewButton").removeClass("uk-button-active");
                    $("#filterSicsSearchAdvanced").addClass("uk-hidden");
                    $("#filterSicsSearchAdvancedButton").removeClass("uk-button-active");
                    $("#filterSicsSearchTactical").addClass("uk-hidden");
                    $("#filterSicsSearchTacticalButton").removeClass("uk-button-active");
                    $("#filterSicsTeamStats").addClass("uk-hidden");
                    $("#filterSicsTeamStatsPlayers").addClass("uk-hidden");
                    $("#filterSicsTeamStatsButton").removeClass("uk-button-active");
                    $("#filterSicsSearch").removeClass("uk-hidden");
                    $("#filterSicsSearchButton").addClass("uk-button-active");
                    $("#filterSelectedActions").addClass("uk-hidden");
                } else if (type === 'advanced') {
                    $("#filterSicsOverview").addClass("uk-hidden");
                    $("#filterSicsOverviewButton").removeClass("uk-button-active");
                    $("#filterSicsSearch").addClass("uk-hidden");
                    $("#filterSicsSearchButton").removeClass("uk-button-active");
                    $("#filterSicsTeamStats").addClass("uk-hidden");
                    $("#filterSicsTeamStatsPlayers").addClass("uk-hidden");
                    $("#filterSicsTeamStatsButton").removeClass("uk-button-active");
                    $("#filterSicsSearchAdvanced").removeClass("uk-hidden");
                    $("#filterSicsSearchAdvancedButton").addClass("uk-button-active");
                    $("#filterSicsSearchTactical").addClass("uk-hidden");
                    $("#filterSicsSearchTacticalButton").removeClass("uk-button-active");
                    $("#filterSelectedActions").removeClass("uk-hidden");

                    if (advancedFilter.size === 0) {
                        // resetto le checkbox per filtro advanced -> se entro filtrato nella pagina video e torno indietro
                        // rimangono le checkbox sugli input dei filtri selezionati
                        setTimeout(function () {
                            $("#filterSicsSearchAdvanced").find("input").prop("checked", false);
                        }, 250);
                    }
                } else if (type === 'tactical') {
                    $("#filterSicsOverview").addClass("uk-hidden");
                    $("#filterSicsOverviewButton").removeClass("uk-button-active");
                    $("#filterSicsSearch").addClass("uk-hidden");
                    $("#filterSicsSearchButton").removeClass("uk-button-active");
                    $("#filterSicsTeamStats").addClass("uk-hidden");
                    $("#filterSicsTeamStatsPlayers").addClass("uk-hidden");
                    $("#filterSicsTeamStatsButton").removeClass("uk-button-active");
                    $("#filterSicsSearchAdvanced").addClass("uk-hidden");
                    $("#filterSicsSearchAdvancedButton").removeClass("uk-button-active");
                    $("#filterSicsSearchTactical").removeClass("uk-hidden");
                    $("#filterSicsSearchTacticalButton").addClass("uk-button-active");
                    $("#filterSelectedActions").removeClass("uk-hidden");

                    if (advancedFilter.size === 0) {
                        // resetto le checkbox per filtro advanced -> se entro filtrato nella pagina video e torno indietro
                        // rimangono le checkbox sugli input dei filtri selezionati
                        setTimeout(function () {
                            $("#filterSicsSearchAdvanced").find("input").prop("checked", false);
                        }, 250);
                    }
                } else if (type === 'teamStats') {
                    $("#filterSicsOverview").addClass("uk-hidden");
                    $("#filterSicsOverviewButton").removeClass("uk-button-active");
                    $("#filterSicsSearchAdvanced").addClass("uk-hidden");
                    $("#filterSicsSearchAdvancedButton").removeClass("uk-button-active");
                    $("#filterSicsSearchTactical").addClass("uk-hidden");
                    $("#filterSicsSearchTacticalButton").removeClass("uk-button-active");
                    $("#filterSicsSearch").addClass("uk-hidden");
                    $("#filterSicsSearchButton").removeClass("uk-button-active");
                    $("#filterSicsTeamStats").removeClass("uk-hidden");
                    $("#filterSicsTeamStatsPlayers").addClass("uk-hidden");
                    $("#filterSicsTeamStatsButton").addClass("uk-button-active");
                    $("#filterSelectedActions").addClass("uk-hidden");
                    if ($("#teamStatsContainermatches").length <= 0) {
                        jsLoadTeamStats("filterSicsTeamStats");
                    }
                } else if (type === 'teamStatsPlayers') {
                    $("#filterSicsOverview").addClass("uk-hidden");
                    $("#filterSicsOverviewButton").removeClass("uk-button-active");
                    $("#filterSicsSearchAdvanced").addClass("uk-hidden");
                    $("#filterSicsSearchAdvancedButton").removeClass("uk-button-active");
                    $("#filterSicsSearchTactical").addClass("uk-hidden");
                    $("#filterSicsSearchTacticalButton").removeClass("uk-button-active");
                    $("#filterSicsSearch").addClass("uk-hidden");
                    $("#filterSicsSearchButton").removeClass("uk-button-active");
                    $("#filterSicsTeamStats").addClass("uk-hidden");
                    $("#filterSicsTeamStatsPlayers").removeClass("uk-hidden");
                    $("#filterSicsTeamStatsButton").addClass("uk-button-active");
                    $("#filterSelectedActions").addClass("uk-hidden");
                    if ($("#teamStatsContainerplayers").length <= 0) {
                        jsLoadTeamStatsPlayers("filterSicsTeamStatsPlayers");
                    }
                } else if (type === 'overview') {
                    $("#filterSicsSearchAdvanced").addClass("uk-hidden");
                    $("#filterSicsSearchAdvancedButton").removeClass("uk-button-active");
                    $("#filterSicsSearchTactical").addClass("uk-hidden");
                    $("#filterSicsSearchTacticalButton").removeClass("uk-button-active");
                    $("#filterSicsSearch").addClass("uk-hidden");
                    $("#filterSicsSearchButton").removeClass("uk-button-active");
                    $("#filterSicsTeamStats").addClass("uk-hidden");
                    $("#filterSicsTeamStatsPlayers").addClass("uk-hidden");
                    $("#filterSicsTeamStatsButton").removeClass("uk-button-active");
                    $("#filterSelectedActions").addClass("uk-hidden");
                    $("#filterSicsOverview").removeClass("uk-hidden");
                    $("#filterSicsOverviewButton").addClass("uk-button-active");

                    if ($("#teamOverviewContainer").length === 0) {
                        loadTeamOverview("filterSicsOverview");
                    }
                }

                // ri-sistemo la parte di destra
                $("#divMatches").removeClass("uk-hidden");
                $("#divTeamInfo").addClass("uk-width-6-10");
                $("#divTeamInfo").removeClass("uk-width-10-10");
                $(".dt-button.uk-button-active").removeClass("uk-button-active");
            }

            function addRemoveAdvancedFilter(tagCode) {
                if (event.target.tagName !== 'I') { // In questo modo funziona solo se clicco il button
                    var generalTagFound = false;
                    var tagToFound = tagCode;
                    if (tagCode.includes("-")) {
                        tagToFound = tagCode.substring(0, tagCode.indexOf("-"));
                    }

                    [...advancedFilter.keys()].forEach(key => {
                        if (key === tagToFound) {
                            generalTagFound = true;
                        }
                    });

                    if (tagCode.includes("-")) { // AMM-0 AMM-1...
                        if (generalTagFound) { // ho già lo stesso tag oppure altri tag
                            var mapValue = advancedFilter.get(tagToFound);
                            if (mapValue.includes(tagCode)) { // devo togliere tag
                                mapValue = mapValue.replace("@" + tagCode, '');
                                mapValue = mapValue.replace(tagCode + "@", '');
                                mapValue = mapValue.replace(tagCode, '');

                                $("#SICS2015_" + tagToFound + "_" + tagCode).removeAttr("checked");
                            } else { // devo aggiungere il tag
                                mapValue += "@" + tagCode;
                                $("#SICS2015_" + tagToFound + "_" + tagCode).attr("checked", "checked");
                            }

                            if (mapValue === "") {
                                advancedFilter.delete(tagToFound);
                            } else {
                                advancedFilter.set(tagToFound, mapValue);
                            }
                        } else { // la categoria non esiste: devo crearla
                            advancedFilter.set(tagToFound, tagCode);
                            $("#SICS2015_" + tagToFound + "_" + tagCode).attr("checked", "checked");
                        }
                    } else { // AMM ATT..
                        if (generalTagFound) {
                            advancedFilter.delete(tagToFound);
                            document.querySelectorAll("." + tagToFound).forEach(function (element) {
                                var id = element.id;
                                if (!element.disabled) {
                                    $('#' + id).removeAttr("checked");
                                }
                            });
                        } else {
//                            advancedFilter.set(tagToFound, "");
                            var atLeastOneAdded = false;
                            document.querySelectorAll("." + tagToFound).forEach(function (element) {
                                var id = element.id;
                                if (!element.disabled) {
                                    if (!$('#' + id).attr("checked")) {
                                        atLeastOneAdded = true;
                                        var functionToExecute = $('#' + id).attr("onclick");
                                        eval(functionToExecute);
                                    }
                                }
                            });

                            // se ho tutti i tag disabilitati e clicco sul tasto
                            // non succede nulla, invece deve selezionare tutti i tag
                            if (!atLeastOneAdded) {
                                advancedFilter.set(tagToFound, "");
                            }
                        }
                    }
                    checkSelectedButtonAdvanced();
                }
            }

            function checkSelectedButtonAdvanced() {
                $("#filterSicsSearchAdvanced").find(".uk-button-active").removeClass("uk-button-active");
                [...advancedFilter.keys()].forEach(key => {
                    $("#SICS2015-_-" + key).addClass("uk-button-active");
                });

                $("#filterSelectedActionsAmount").html(" " + advancedFilter.size);
                if (advancedFilter.size > 0) {
                    $("#filterSelectedActions").addClass("uk-button-confirm");
                } else {
                    $("#filterSelectedActions").removeClass("uk-button-confirm");
                }
            }

            function applyAdvancedFilter() {
                if (advancedFilter.size > 0) {
                    var filter = "";

                    [...advancedFilter.keys()].forEach(key => {
                        // devo guardare se devo applicare il filtro su TUTTA la categoria
                        var allChecked = true;
                        document.querySelectorAll("." + key).forEach(function (element) {
                            var id = element.id;
                            if (!$('#' + id).attr("checked") && $('#' + id).attr("disabled") !== 'disabled') {
                                allChecked = false;
                            }
                        });

                        if (allChecked) {
                            filter += (filter === "" ? "" : "|") + "SICS-_-" + key;
                        } else {
                            filter += (filter === "" ? "" : "|") + "SICS-_-" + key + "#" + advancedFilter.get(key);
                        }
//                        filter += (filter === "" ? "" : "|") + "SICS-_-" + key + "#" + advancedFilter.get(key);
                    });

                    showTeamEvents(filter);
                }
            }

            function openReportModal() {
                $("#reportModal").addClass("uk-open");
                $("#reportModal").removeClass("uk-hidden");

            }

            function closeReportModal() {
                $("#reportModal").removeClass("uk-open");
                $("#reportModal").addClass("uk-hidden");
            }

            function generateReport() {
                var teamId = ${mTeam.id};
                var competitions = "";
                $("#reportModalCompetitions > ul > li > button.uk-button-active").each(function (el) {
                    if (competitions !== "")
                        competitions += ",";
                    competitions += $(this).attr("data-value");
                });
                var competitionSelected = competitions;
                if (competitionSelected.includes(",")) {
                    competitionSelected = -1;
                }
                var groupId = "";

                if (competitions.length === 0) { // potrei aver selezionato solo un girone
                    $("#reportModalCompetitionsGironi > ul > li > button.uk-button-active").each(function (el) {
                        competitions = $(this).attr("data-value");
                    });
                    if (competitions.includes("-")) {
                        groupId = competitions.substring(competitions.indexOf("-") + 1, competitions.length);
                        competitions = competitions.substring(0, competitions.indexOf("-"));
                        competitionSelected = competitions;
                    }
                }

                var selectedColumns = [];
                // prendo i parametri selezionati nel profilo
                $("#teamOverviewColumnsButtonContent > li").find("input:checked").each(function () {
                    var columnIndex = $(this).attr("data-value");
                    if (typeof columnIndex !== 'undefined') {
                        selectedColumns.push(columnIndex);
                    }
                });
                if (!selectedColumns.includes("1")) {
                    selectedColumns.push("1");
                }

                var dateFrom = "", dateTo = "";
                dateFrom = $("#reportModalFrom").val();
                dateTo = $("#reportModalTo").val();

                if (typeof teamId !== 'undefined' && teamId !== '' && competitions !== '') {
                    window.location.href = "/sicstv/user/reportTeam.htm?teamId=" + teamId + "&competitionSelected=" + competitionSelected + "&competitions=" + competitions + "&statsTypeIds=" + selectedColumns + "&groupId=" + groupId + "&from=" + dateFrom + "&to=" + dateTo;
                } else {
                    UIkit.notify("<spring:message code="team.report.unexpected.error"/>", {status: 'danger', timeout: 1000});
                }
            }

            function addTeamToFavorites(id) {
                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/aggiungiAPreferiti.htm",
                    data: encodeURI("teamId=" + id),
                    cache: false,
                    success: function (result) {
                        if (result === 'ok') {
                            UIkit.notify("<spring:message code='favorites.add.success'/>", {status: 'success', timeout: 1000});

                            $("#manageFavoritesButton").attr("onclick", "removeTeamFromFavorites(" + id + ")");
                            $("#manageFavoritesButton").addClass("uk-button-active");
                            jsReloadFavorites();
                        } else {
                            UIkit.notify("<spring:message code='favorites.add.failed'/>", {status: 'warning', timeout: 1000});
                        }
                    }
                });
            }

            function removeTeamFromFavorites(id) {
                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/rimuoviDaiPreferiti.htm",
                    data: encodeURI("teamId=" + id),
                    cache: false,
                    success: function (result) {
                        if (result === 'ok') {
                            UIkit.notify("<spring:message code='favorites.delete.success'/>", {status: 'success', timeout: 1000});

                            $("#manageFavoritesButton").attr("onclick", "addTeamToFavorites(" + id + ")");
                            $("#manageFavoritesButton").removeClass("uk-button-active");
                            jsReloadFavorites();
                        } else {
                            UIkit.notify("<spring:message code='favorites.delete.failed'/>", {status: 'warning', timeout: 1000});
                        }
                    }
                });
            }

            function removeAllChecked(element) {
                var checked = $(element).hasClass("uk-button-active");
                $("#reportModalCompetitions > center > button").removeClass("uk-button-active");
                $("#reportModalCompetitionsGironi > center > button").removeClass("uk-button-active");

                if (checked) {
                    $(element).addClass("uk-button-active");
                }
            }

            function removeAllCheckedGironi() {
                $("#reportModalCompetitionsGironi > center > button").removeClass("uk-button-active");
            }

            function manageUploadLogo(teamId) {
                if ($(event.target).attr("disabled")) {
                    UIkit.notify("<spring:message code="upload.logo.permission.denied"/>", {status: 'danger', timeout: 1000});
                    return;
                }

                if (typeof dropzone === "undefined") {
                    dropzone = initializeLogoDropzone("uploadLogoDropzone");
                }

                dropzone.options.url = dropzone.options.baseUrl + "teamId=" + teamId;
                $("#upload-logo-modal").addClass("uk-open");
                $("#upload-logo-modal").removeClass("uk-hidden");
            }

            function closeUploadLogo() {
                $("#upload-logo-modal").removeClass("uk-open");
                $("#upload-logo-modal").addClass("uk-hidden");
            }

            function askForColorUpdate(picker) {
                if (picker.toRGBAString()) {
                    var color = picker.toRGBAString();
                    color = color.replace("rgba(", "").replace(")", "");
                    var alpha = 255;
                    var red = color.split(",")[0];
                    var green = color.split(",")[1];
                    var blue = color.split(",")[2];
                    if (alpha && red && green && blue) {
                        if (window.confirm("Vuoi aggiornare il colore del team?")) {
                            $.ajax({
                                type: "GET",
                                url: "/sicstv/user/updateTeamColor.htm",
                                cache: false,
                                data: encodeURI("teamId=${mTeam.id}&color=" + alpha + "x" + red + "x" + green + "x" + blue),
                                success: function (msg) {
                                    if (msg === "ok") {
                                        UIkit.notify("Colore aggiornato", {status: 'success', timeout: 1000});
                                    } else {
                                        UIkit.notify("Errore nell'aggiormamento del colore", {status: 'danger', timeout: 2500});
                                    }
                                },
                                error: function () {
                                    UIkit.notify("Errore nell'aggiormamento del colore", {status: 'danger', timeout: 2500});
                                }
                            });
                        }
                    }
                }
            }
            
            function manageFullTeamSwitch() {
                // if fullTeamSwitch is checked add parameter "checkPlaytime" with value true, otherwise remove the parameter
                var currentUrl = new URL(window.location.href);
                var isChecked = $("#fullTeamSwitch").is(":checked");

                if (isChecked) {
                    currentUrl.searchParams.set("checkPlaytime", "false");
                } else {
                    currentUrl.searchParams.delete("checkPlaytime");
                }

                window.location.href = currentUrl.toString();
            }

            function showNoDataModal(playerId) {
                var modal = UIkit.modal.confirm("<spring:message code="player.no.data.message"/>", function () {
                    // On confirm, redirect to generic player page
                    window.location.href = '/sicstv/user/player.htm?personal=${personalSource}&playerId=' + playerId;
                }, function () {
                    // On cancel, do nothing
                }, {
                    labels: {
                        'Ok': '<spring:message code="menu.conferma"/>',
                        'Cancel': '<spring:message code="menu.annulla"/>'
                    },
                    style: 'height: auto'
                });
            }
        </script>

        <style>
            .container {
                display: flex;
                width: 100%;
            }

            .box2 {
                width: 50%;
                height: 100%;
                float: left;
            }

            .box2-header {
                width: 50%;
                height: 50%;
                float: left;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .box3-header {
                width: 33%;
                height: 50%;
                float: left;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .box3 {
                width: 33%;
                height: 50%;
                float: left;
            }

            .box100 {
                width: 100%;
                height: auto;
                float: left;
            }

            .box97 {
                width: 97%;
                height: auto;
                float: left;
            }

            .box70 {
                width: 70%;
                height: auto;
                float: left;
            }

            .box30 {
                width: 30%;
                height: auto;
                float: left;
            }

            .large-div {
                vertical-align: middle
            }

            .tableHeader {
                text-align: center;
                height: 10px;
            }

            .tableRow {
                text-align: center;
            }
            
            @media print {
                body * {
                    visibility:hidden;
                }
            }
        </style>
    </head>

    <body>

        <%@ include file="header.jsp" %>

        <div class="uk-grid">

            <div id="divTeamInfo" class="uk-width-6-10 uk-hidden-small">
                <input type="hidden" value="${mComp.id}" id="idComp"/>
                <input type="hidden" value="${mTeam.id}" id="idTeam"/>
                <input type="hidden" value="${generalPageTeam}" id="generalPageTeam"/>
                <input type="hidden" value="${gameIds}" id="gameIds"/>
                <div id="breadcrumb">
                    <c:choose>
                        <c:when test="${!generalPageTeam}">
                            <ul class="uk-breadcrumb uk-margin-left ">
                                <c:if test="${personalSource}">
                                    <li>
                                        <a href="/sicstv/user/mycompetition.htm"><spring:message code="user.competitions"/></a>
                                    </li>
                                </c:if>
                                <c:if test="${!personalSource}">
                                    <c:if test="${!mCountry.internationalCompetition}">
                                        <li>
                                            <a href="/sicstv/user/home.htm?country=${!mCountry.internationalCompetition}"><spring:message code="menu.user.countries"/></a>
                                        </li>
                                        <li>
                                            <a href="/sicstv/user/country.htm?countryId=${mCountry.id}">${mCountry.name}</a>
                                        </li>
                                    </c:if>
                                    <c:if test="${mCountry.internationalCompetition}">
                                        <li>
                                            <a href="/sicstv/user/home.htm?country=${!mCountry.internationalCompetition}"><spring:message code="menu.user.internationalCompetitions"/></a>
                                        </li>
                                        <li>
                                            <a href="/sicstv/user/country.htm?intCompId=${mCountry.id}">${mCountry.name}</a>
                                        </li>
                                    </c:if>
                                </c:if>
                                <li><a href="/sicstv/user/competition.htm?formCompetitionId=${mComp.id}">${mComp.name}</a></li>
                                <li>
                                    <a href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${mComp.id}&formTeamId=${mTeam.id}">${mTeam.name}</a> <i id="showCurrentFilter"></i>
                                </li>
                                <c:if test="${mGirone != null}">
                                    <li style="text-transform: uppercase">${mGirone.groupName}</li>
                                    </c:if>
                            </ul>
                        </c:when>
                        <c:otherwise>
                            <ul class="uk-breadcrumb uk-margin-left ">
                                <li>${mTeam.name}</li>
                            </ul>
                        </c:otherwise>
                    </c:choose>    
                </div>
                <div id="teamData" class="uk-text-center uk-margin-all">
                    <div class="container">
                        <div class="large-div uk-position-relative" style="flex-basis: 22%; display: flex; justify-content: center; align-items: center;">
                            <center>
                                <c:if test="${personalSource || mUser.groupsetId == 2}">
                                    <span onclick="manageUploadLogo(${mTeam.id});" class="uk-float-left uk-margin-remove uk-position-absolute" style="top: 3%; right: 15%;" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='upload.team.logo'/>"><i class="uk-icon-upload" ${mTeam.groupsetId == -1 && mUser.groupsetId != 2 ? "disabled" : ""}></i></span>
                                    </c:if>
                                    <c:if test="${mUser.groupsetId == 2}">
                                    <span class="uk-float-left uk-margin-remove uk-position-absolute" style="top: 90%; right: 0%;">
                                        <button class="uk-button uk-button-small" data-jscolor="{width:250, paletteCols:12, value:'${mTeamColor}', onChange:'askForColorUpdate(this);'}">Click</button>
                                    </span>
                                </c:if>
                                <img height="150px" width="150px" class="imgTeamLogo" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeam.logo}.png" alt="${mTeam.name}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'">
                                <p style="font-size: 16px; margin-bottom: 0">${mTeam.name}</p>
                                <p style="font-size: 10px; margin: 0">${mComp.name}</p>
                            </center>
                        </div>
                        <div style="flex-basis: 56%">
                            <div class="<c:if test="${!generalPageTeam}">box3-header</c:if><c:if test="${generalPageTeam}">box2-header</c:if>">
                                    <center>
                                            <p style="font-size: 14px;"><spring:message code="team.partite"/><br>
                                        <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="/sicstv/images/lineup_new.png"><br>
                                        <span style="font-size: 15px" id="totalMatchesAmount">${mTeam.vittorie + mTeam.pareggi + mTeam.sconfitte}</span><br>
                                </center>
                            </div>
                            <c:if test="${!generalPageTeam}">
                                <div class="box3-header">
                                    <center>
                                        <p style="font-size: 14px; margin: 0" id="andamentoParagraph"><spring:message code="team.andamento"/></p>
                                    </center>
                                </div>
                            </c:if>
                            <div class="<c:if test="${!generalPageTeam}">box3-header</c:if><c:if test="${generalPageTeam}">box2-header</c:if>">
                                    <center>
                                            <p style="font-size: 14px;"><spring:message code="team.reti"/><br>
                                        <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="/sicstv/images/ball.png"><br>
                                        <c:if test="${mTeam.golFatti >= mTeam.golSubiti}">
                                            <span style="font-size: 15px">${mTeam.golFatti} - ${mTeam.golSubiti} (<span style="color: limegreen">${mTeam.getDiffGoal()}</span>)</span>
                                        </c:if>
                                        <c:if test="${mTeam.golSubiti > mTeam.golFatti}">
                                            <span style="font-size: 15px">${mTeam.golFatti} - ${mTeam.golSubiti} (<span style="color: red">${mTeam.getDiffGoal()}</span>)</span>
                                        </c:if>
                                </center>
                            </div>
                            <div class="box3-header">
                                <center>
                                    <p style="font-size: 14px; margin: 0"><spring:message code="team.vittorie"/></p>
                                    <span style="font-size: 25px; color: limegreen">${mTeam.vittorie}</span><br>
                                    <span style="font-size: 10px" title="<spring:message code="team.vittorie.title"/>">(${mTeam.vittorieInCasa} - ${mTeam.vittorie - mTeam.vittorieInCasa})</span>
                                </center>
                            </div>
                            <div class="box3-header">
                                <center>
                                    <p style="font-size: 14px; margin: 0"><spring:message code="team.pareggi"/></p>
                                    <span style="font-size: 25px; color: gray">${mTeam.pareggi}</span><br>
                                    <span style="font-size: 10px" title="<spring:message code="team.pareggi.title"/>">(${mTeam.pareggiInCasa} - ${mTeam.pareggi - mTeam.pareggiInCasa})</span>
                                </center>
                            </div>
                            <div class="box3-header">
                                <center>
                                    <p style="font-size: 14px; margin: 0"><spring:message code="team.sconfitte"/></p>
                                    <span style="font-size: 25px; color: red">${mTeam.sconfitte}</span><br>
                                    <span style="font-size: 10px" title="<spring:message code="team.sconfitte.title"/>">(${mTeam.sconfitteInCasa} - ${mTeam.sconfitte - mTeam.sconfitteInCasa})</span>
                                </center>
                            </div>
                        </div>
                        <div class="large-div" style="flex-basis: 22%; text-align: right !important" id="canvasContainer">
                            <p style="margin: 0; width: 150px; float: right; text-align: center; font-size: 14px;"><spring:message code="team.main.module"/></p>
                            <!--<img height="150px" width="150px" src="data:image/jpeg;base64,${mTeamMostUsedModuleImage}" style="margin-top: 3px; margin-bottom: 3px">-->
                            <p style="margin: 0; width: 150px; float: right; text-align: center; font-weight: bold; font-size: 15px;">${mTeamMostUsedModule.replace("-", " - ")}</p>
                        </div>
                    </div>
                    <c:if test="${filterToShow.size() > 0}">
                        <div id="teamStats" style="margin-top: 10px;">
                            <div>
                                <div class="uk-text-left" style="margin: 2px" id="filterSicsSearchDiv">
                                    <button class="uk-button uk-button-active" id="filterSicsSearchButton" onclick="showSicsFilters('search');
                                            saveTab('filterSicsSearchButton', ${mTeam.id});" title=""><spring:message code="video.ricerca"/></button>
                                    <button class="uk-button" id="filterSicsSearchAdvancedButton" onclick="showSicsFilters('advanced');
                                            saveTab('filterSicsSearchAdvancedButton', ${mTeam.id});" title=""><spring:message code="video.ricercaAvanzata"/></button>
                                    <c:if test="${sicsTacticalFilter != null}">
                                        <button class="uk-button" id="filterSicsSearchTacticalButton" onclick="showSicsFilters('tactical');
                                                saveTab('filterSicsSearchTacticalButton', ${mTeam.id});" title=""><spring:message code="video.ricercaTattica"/></button>
                                    </c:if>
                                    <button id="filterSelectedActions" class="uk-button uk-hidden" onclick="applyAdvancedFilter()" title="<spring:message code='filtri.team.azioni.selezionate'/>"><i class="uk-icon-filter"></i><span id="filterSelectedActionsAmount"> 0</span></button>
                                    <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}">
                                        <button class="uk-button ${gameIdsAmount > 0 ? 'uk-button-active' : ''}" id="filterSicsTeamStatsButton"><spring:message code='menu.user.statistiche.lower'/></button> 
                                        <span id="spanTeamStatsDropdown" class="uk-dropdown">
                                            <ul class="uk-nav uk-nav-navbar">
                                                <li class="selAllSubnav"><a id="filterSicsTeamStatsLink" onclick="showSicsFilters('teamStats'); saveTab('filterSicsTeamStatsLink', ${mTeam.id});"><i class="uk-margin-right"></i><spring:message code="team.stats.partite"/></a></li>
                                                <li class="uk-nav-divider"></li>
                                                <li class="selAllSubnav"><a id="filterSicsTeamStatsPlayersLink" onclick="showSicsFilters('teamStatsPlayers');
                                                        saveTab('filterSicsTeamStatsPlayersLink', ${mTeam.id});"><i class="uk-margin-right"></i><spring:message code="team.stats.giocatori"/></a></li>
                                            </ul>
                                        </span>
                                    </span>
                                    <button class="uk-button" id="generateReportButton" onclick="openReportModal();" title="<spring:message code="menu.report.team"/>"><i class="uk-icon-file-text-o"></i><span style="margin-left: 5px"><spring:message code="menu.report"/></span></button>
                                    <button class="uk-button" id="filterSicsOverviewButton" onclick="showSicsFilters('overview');
                                            saveTab('filterSicsOverviewButton', ${mTeam.id});" title="<spring:message code="menu.panoramica"/>"><span style="margin-left: 5px"><spring:message code="menu.panoramica"/></span></button>
                                        <c:choose>
                                            <c:when test="${filterToShow.size() > 1}">
                                            <div class="uk-text-right" style="float: right">
                                                <span style="margin-right:5px;"><spring:message code="menu.configurazione"/></span>
                                                <div class="uk-button uk-form-select uk-margin-small-bottom" data-uk-form-select>
                                                    <span></span>
                                                    <i class="uk-icon-caret-down"></i>
                                                    <select onchange="changePanel('');" class='filterSelect'>

                                                        <c:forEach items="${filterToShow}" var="map" varStatus="status" >
                                                            <option value="${map.key}">
                                                                <c:choose>
                                                                    <c:when test="${map.key == 'SICS2015'}">
                                                                        SICS
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        ${map.key}
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </option>
                                                        </c:forEach>
                                                    </select>
                                                </div>
                                            </div>
                                        </c:when>
                                        <c:otherwise>
                                            <c:forEach items="${filterToShow}" var="map" varStatus="status" >
                                                <c:choose>
                                                    <c:when test="${map.key == 'SICS2015'}">

                                                    </c:when>
                                                    <c:when test="${map.key == 'SICS'}">

                                                    </c:when>
                                                    <c:otherwise>
                                                        <span style="margin-right:5px;"><spring:message code="menu.configurazione"/></span> ${map.key}
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:forEach>
                                        </c:otherwise>
                                    </c:choose>
                                    <div class="uk-text-right" style="float: right; display: inline-flex; align-items: center">
                                        <div class="uk-margin-right" style="display: inline-flex; align-items: center;">
                                            <input type="checkbox" id="fullTeamSwitch" class="uk-switch"
                                                   style="width: 30px"
                                                   onchange="manageFullTeamSwitch()" ${param.checkPlaytime == 'false' ? 'checked' : ''}/>
                                            <label for="fullTeamSwitch"><spring:message code="team.show.full.team"/></label>
                                        </div>

                                        <c:choose>
                                            <c:when test="${mTeamInFavorites}">
                                            <button id="manageFavoritesButton" onclick="removeTeamFromFavorites(${mTeam.id})" class="uk-button uk-button-small uk-button-active" title="<spring:message code="favorites.remove"/>"><i class="uk-icon-heart"></i></button>
                                            </c:when>
                                            <c:otherwise>
                                            <button id="manageFavoritesButton" onclick="addTeamToFavorites(${mTeam.id})" class="uk-button uk-button-small" title="<spring:message code="favorites.add"/>"><i class="uk-icon-heart"></i></button>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>
                            </div>

                            <div id="filterDiv">
                                <div class="filterSicsDiv" id="filterSicsSearch">
                                    <c:if test="${!personalSource}">
                                        <div class="uk-margin-small-top center uk-width-1-1">
                                            <button class='uk-button teamFilterButton panelSicsDiv' onclick="showTeamEvents('SICS-_-TV');" ${mPersonalEvents == 0 ? 'disabled' : ''}><i class="uk-icon-video-camera"></i><spring:message code="lib.personal.clips"/> (${mPersonalEvents})</button>
                                        </div>
                                    </c:if>
                                    <div class="uk-float-left panelSicsDiv">
                                        <c:forEach items="${filterToShow['SICS'][0]}" var="btn">
                                            <button class='uk-button teamFilterButton' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="showTeamEvents('SICS-_-${btn.key}');" title="<spring:message code='${btn.value[0]}'/>"><i class="uk-icon-video-camera" ></i><spring:message code="${btn.value[0]}"/> (${btn.value[2]})</button>
                                        </c:forEach>
                                    </div>
                                    <div class="uk-float-right panelSicsDiv">
                                        <c:forEach items="${filterToShow['SICS'][1]}" var="btn">
                                            <button class='uk-button teamFilterButton' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="showTeamEvents('SICS-_-${btn.key}');" title="<spring:message code='${btn.value[0]}'/>"><i class="uk-icon-video-camera" ></i><spring:message code="${btn.value[0]}"/> (${btn.value[2]})</button>
                                        </c:forEach>
                                    </div>
                                </div>
                                <div class="uk-text-center filterGroupset">

                                </div>
                                <c:if test="${sicsAdvancedFilter != null}">
                                    <div uk-accordion data-uk-accordion="{collapse: false,showfirst: false}" class="uk-text-center uk-hidden" id="filterSicsSearchAdvanced">
                                        <c:set var="counter" value="0"/>
                                        <c:forEach items="${sicsAdvancedFilter}" var="btn">
                                            <c:set var="counter" value="${counter + 1}"/>
                                            <c:choose>
                                                <c:when test="${not fn:containsIgnoreCase(btn.key, '-')}">
                                                    <!-- Ho un evento padre -->
                                                    <c:set var="chiave" value="${btn.key}"/>
                                                    <c:set var="padreDisabled" value="${btn.value[1] == 'false'}"/>
                                                    <c:set var="chiaveCounter" value="0"/>

                                                    <c:if test="${counter > 1}">
                                                        <!-- Chiudo i div dei sotto eventi -->
                                                    </div>
                                                </div>
                                            </div>
                                            </span>
                                        </c:if>
                                        <span class="width-1-2-x">
                                            <button id="SICS2015-_-${chiave}" class='uk-button' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="addRemoveAdvancedFilter('${chiave}');" title="${btn.value[0]}">
                                                    <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down"></i>
                                                ${btn.value[0]} (${btn.value[2]})
                                            </button>
                                        </c:when>
                                        <c:otherwise>
                                            <!-- Ho un sotto evento -->
                                            <c:set var="figlioDisabled" value="${btn.value[2] == '0'}"/>
                                            <c:choose>
                                                <c:when test="${chiaveCounter == 0}">
                                                    <!-- Devo prima creare il div iniziale -->
                                                    <div data-wrapper="true" style="overflow: hidden; height: 0px; position: relative;" aria-expanded="false">
                                                        <div id="tag${chiaveCounter + 1}" class="uk-accordion-content uk-text-left">
                                                            <div class="tagEvento uk-form">
                                                                <div class="uk-margin-small buttonTag">
                                                                    <input type="checkbox" class="${chiave}" id="SICS2015_${chiave}_${btn.key}" class="uk-margin-right" onclick="addRemoveAdvancedFilter('${btn.key}');" value="${btn.value[0]}" <c:if test="${figlioDisabled}">disabled</c:if>>
                                                                    <label for="SICS2015_${chiave}_${btn.key}" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</label>
                                                                    <!--<button class='uk-button' style="width: 100%" <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="addRemoveAdvancedFilter('${btn.key}');" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</button>-->
                                                                    </div>
                                                            </c:when>
                                                            <c:otherwise>
                                                                <!-- Basta aggiungere il tasto -->
                                                                <div class="uk-margin-small buttonTag">
                                                                    <!--<button class='uk-button' style="width: 100%" <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="addRemoveAdvancedFilter('${btn.key}');" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</button>-->
                                                                    <input type="checkbox" class="${chiave}" id="SICS2015_${chiave}_${btn.key}" class="uk-margin-right" onclick="addRemoveAdvancedFilter('${btn.key}');" value="${btn.value[0]}" <c:if test="${figlioDisabled}">disabled</c:if>>
                                                                    <label for="SICS2015_${chiave}_${btn.key}" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</label>
                                                                </div>
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <c:set var="chiaveCounter" value="${chiaveCounter + 1}"/>
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:forEach>
                                        </div>
                                    </div>
                                </div>
                            </span>
                        </div>
                    </c:if>
                    <c:if test="${sicsTacticalFilter != null}">
                        <div uk-accordion data-uk-accordion="{collapse: false,showfirst: false}" class="uk-text-center uk-hidden" id="filterSicsSearchTactical">
                            <c:set var="counterTactical" value="0"/>
                            <c:forEach items="${sicsTacticalFilter}" var="btn">
                                <c:set var="counterTactical" value="${counterTactical + 1}"/>
                                <c:choose>
                                    <c:when test="${not fn:containsIgnoreCase(btn.key, '-')}">
                                        <!-- Ho un evento padre -->
                                        <c:set var="chiaveTactical" value="${btn.key}"/>
                                        <c:set var="padreDisabledTactical" value="${btn.value[1] == 'false'}"/>
                                        <c:set var="chiaveCounterTactical" value="0"/>

                                        <c:if test="${counterTactical > 1}">
                                            <!-- Chiudo i div dei sotto eventi -->
                                        </div>
                                    </div>
                                </div>
                            </span>
                        </c:if>
                        <span class="width-1-2-x">
                            <button id="SICS2015-_-${chiaveTactical}" class='uk-button' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="addRemoveAdvancedFilter('${chiaveTactical}');" title="${btn.value[0]}" style="height: 40px">
                                    <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down"></i>
                                ${btn.value[0]} (${btn.value[2]})
                            </button>
                        </c:when>
                        <c:otherwise>
                            <!-- Ho un sotto evento -->
                            <c:set var="figlioDisabledTactical" value="${btn.value[2] == '0'}"/>
                            <c:choose>
                                <c:when test="${chiaveCounterTactical == 0}">
                                    <!-- Devo prima creare il div iniziale -->
                                    <div data-wrapper="true" style="overflow: hidden; height: 0px; position: relative;" aria-expanded="false">
                                        <div id="tag${chiaveCounterTactical + 1}" class="uk-accordion-content uk-text-left">
                                            <div class="tagEvento uk-form">
                                                <div class="uk-margin-small buttonTag">
                                                    <input type="checkbox" class="${chiaveTactical}" id="SICS2015_${chiaveTactical}_${btn.key}" class="uk-margin-right" onclick="addRemoveAdvancedFilter('${btn.key}');" value="${btn.value[0]}" <c:if test="${figlioDisabledTactical}">disabled</c:if>>
                                                    <label for="SICS2015_${chiaveTactical}_${btn.key}" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</label>
                                                    <!--<button class='uk-button' style="width: 100%" <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="addRemoveAdvancedFilter('${btn.key}');" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</button>-->
                                                    </div>
                                            </c:when>
                                            <c:otherwise>
                                                <!-- Basta aggiungere il tasto -->
                                                <div class="uk-margin-small buttonTag">
                                                    <!--<button class='uk-button' style="width: 100%" <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="addRemoveAdvancedFilter('${btn.key}');" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</button>-->
                                                    <input type="checkbox" class="${chiaveTactical}" id="SICS2015_${chiaveTactical}_${btn.key}" class="uk-margin-right" onclick="addRemoveAdvancedFilter('${btn.key}');" value="${btn.value[0]}" <c:if test="${figlioDisabledTactical}">disabled</c:if>>
                                                    <label for="SICS2015_${chiaveTactical}_${btn.key}" title="${btn.value[0]}">${btn.value[0]} (${btn.value[2]})</label>
                                                </div>
                                            </c:otherwise>
                                        </c:choose>
                                        <c:set var="chiaveCounterTactical" value="${chiaveCounterTactical + 1}"/>
                                    </c:otherwise>
                                </c:choose>
                            </c:forEach>
                        </div>
                    </div>
                </div>
            </span>
        </div>
    </c:if>
    <div uk-accordion data-uk-accordion="{collapse: false,showfirst: false}" class="uk-text-center uk-hidden" id="filterSicsTeamStats" style='font: 12px "Roboto", sans-serif'>
        <center><div class="loader"></div></center>
    </div>
    <div uk-accordion data-uk-accordion="{collapse: false,showfirst: false}" class="uk-text-center uk-hidden" id="filterSicsTeamStatsPlayers" style='font: 12px "Roboto", sans-serif'>
        <center><div class="loader"></div></center>
    </div>
    <div id="filterSicsOverview" class="uk-hidden" style="min-height: 10vh">
        <center><div class="loader"></div></center>
    </div>
</div>
</div>
</c:if>
<div>
    <c:forEach var="map" items="${mPlayers}">

        <div style="margin-top: -10px">
            <c:choose>
                <c:when test="${map.key == 'P'}">
                    <span class="titlePlayerStats"><spring:message code="ruoli.portieri"/></span>
                </c:when>
                <c:when test="${map.key == 'D'}">
                    <span class="titlePlayerStats"><spring:message code="ruoli.difensori"/></span>
                </c:when>
                <c:when test="${map.key == 'C' }">
                    <c:choose>
                        <c:when test="${mUser.sportId == '0'}">
                            <span class="titlePlayerStats"><spring:message code="ruoli.centrocampisti"/></span>
                        </c:when>
                        <c:when test="${mUser.sportId == '1'}">
                            <span class="titlePlayerStats"><spring:message code="ruoli.basket.centro"/></span>
                        </c:when>
                    </c:choose>
                </c:when>
                <c:when test="${map.key == 'A'}">
                    <span class="titlePlayerStats"><spring:message code="ruoli.attaccanti"/></span>
                </c:when>
                <c:when test="${map.key == 'PM'}">
                    <span class="titlePlayerStats"><spring:message code="ruoli.basket.playmaker"/></span>
                </c:when>
                <c:when test="${map.key == 'GT'}">
                    <span class="titlePlayerStats"><spring:message code="ruoli.basket.guardia"/></span>
                </c:when>
                <c:when test="${map.key == 'AG'}">
                    <span class="titlePlayerStats"><spring:message code="ruoli.basket.alagrande"/></span>
                </c:when>
                <c:when test="${map.key == 'AP'}">
                    <span class="titlePlayerStats"><spring:message code="ruoli.basket.alapiccola"/></span>
                </c:when>
            </c:choose>

            <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-10 uk-width-1-1 uk-margin-small-top" >
                <c:forEach var="item" items="${map.value}">
                    <c:set var="transferred" value="${!personalSource and mTeam != null and !mTeam.nationalTeam and mLastTeamMap.get(item.id) != null and mTeam.mainTeamId != mLastTeamMap.get(item.id).mainTeamId}"/>

                    <li class="uk-margin-left playtime-check" playtime="${item.playtime}" competitionid="<c:if test="${mComp.id != null}">${mComp.id}</c:if>" hasplayed="<c:if test="${mComp.id != null}">${item.containsCompetitionId(mComp.id)}</c:if>" id="${item.id}" redirect-link="/sicstv/user/player.htm?personal=${personalSource}&playerId=${item.id}<c:choose><c:when test="${mComp.id != null}">&competitionId=${mComp.id}</c:when><c:otherwise></c:otherwise></c:choose><c:choose><c:when test="${mTeam.id != null}">&teamId=${mTeam.id}</c:when><c:otherwise></c:otherwise></c:choose><c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>" style="min-height: 200px;">
                        <a type="button" class="uk-text-center zoomOnHover" style="margin-top: 5px;max-height: 150px;">
                            <div class="competitionBox" style="${transferred && mCurrentSeason == mLastSeasonId ? 'opacity: 0.3' : ''}">
                                <span style="text-align: right !important; height: 2px; margin: 0; margin-right: -8px">
                                    <c:if test="${mPlayersInFavorites.contains(item.id)}">
                                        <i class="uk-icon-heart" title="<spring:message code="favorites.player.in"/>"></i><br/>
                                        <c:set var="extraCss" value=""/>
                                    </c:if>
                                    <c:if test="${mPlayersInWatchlist.contains(item.id)}">
                                        <i class="uk-icon-eye" title="<spring:message code="watchlist.player.in"/>"></i><br/>
                                        <c:set var="extraCss" value=""/>
                                    </c:if>
                                    <c:if test="${mCurrentSeason == mLastSeasonId}">
                                        <c:if test="${transferred}">
                                            <img src="/sicstv/images/trasferito.png" title="<spring:message code="player.last.game.with"/>"/>
                                            <c:set var="extraCss" value=""/>
                                        </c:if>
                                    </c:if>
                                </span>
                                <c:choose>
                                    <c:when test="${item.photo!=null}">
                                        <img style="${extraCss}" class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${item.photo}.png" alt="${item.last_name} ${item.first_name}" class="uk-margin-top uk-margin-bottom" width="200">
                                    </c:when>
                                    <c:otherwise>
                                        <img style="width: 70px; height: 85px; ${extraCss}" class="logoCompImg" src="/sicstv/images/user_gray.png" alt="${item.last_name} ${item.first_name}" class="uk-margin-top uk-margin-bottom">
                                    </c:otherwise>
                                </c:choose>
                            </div>
                            <span class="uk-text-medium" style="margin: 0">#${item.numero}</span><br/>
                            <span style="font-size: 11px; margin: 0"><b>${item.last_name}</b></span><br/>
                            <span style="font-size: 11px; margin: 0">${item.first_name}</span><br/>
                            <span style="font-size: 11px; margin: 0; margin-bottom: 5px">${item.getBornDateYear()}</span>
                        </a>
                    </li>
                </c:forEach>
            </ul>
        </div>
    </c:forEach>
</div>
</div>
</div>

<div id="divMatches" class="uk-width-4-10">

    <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#matchesCalendarContent',pos:'bottom-left'}">
        <li id="liTeamInfo"><a class="uk-visible-small"> ${mTeam.name}</a></li>
        <li id="competitionTab"><a><spring:message code="user.competitions"/></a></li>
        <li id="calendarTab1" class="uk-active"><a><c:if test="${!generalPageTeam}">${mComp.name}</c:if><c:if test="${generalPageTeam}"><spring:message code="menu.user.calendario"/></c:if> <span id="numMatches" class="uk-badge uk-hidden " onclick="jsRefreshMatches('1');"></span></a><div id="matchesList" class="uk-margin-left uk-margin-top"></div></li>
                <c:if test="${!generalPageTeam}">
            <li id="calendarTab2"><a><spring:message code="menu.user.calendario"/></a></li>
                </c:if>
        <!-- <li id="calendarTab3"><a><spring:message code="menu.user.incontri"/> <span id="numAllMatches" class="uk-badge uk-badge-notification uk-hidden uk-hidden-small" onclick="jsRefreshMatches('1');">0</span></a></li> -->
    </ul>
    <c:if test="${!generalPageTeam}">
        <span class="uk-float-right uk-margin-small-top uk-margin-small-right uk-hidden-small">
            <button id="addToMulti" class="uk-button uk-button-small" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.multipartita'/>" onclick="jsGoToMultipartita('${personalSource}', ${mTeam.id});"><i class="uk-icon-external-link  "></i></button>
            <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}" >
                <button id="selectedMatches" class="uk-button uk-button-small ${gameIdsAmount > 0 ? 'uk-button-active' : ''}" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.selezionati'/>"><i class="uk-icon-bars"></i> ${gameIdsAmount != null ? gameIdsAmount : 0} </button> 
                <!--<a href="#" id="selectedMatches" class="uk-button uk-button-small" ><i class="uk-icon-bars  "></i> 0 </a>-->
                <span id="spanSelAll" class="uk-dropdown">
                    <ul class="uk-nav uk-nav-navbar">
                        <li class="selAllSubnav"><a onclick="jsFilterGames(true);"><spring:message code="menu.selezionatutto"/></a></li>
                        <li class="uk-nav-divider"></li>
                        <li class="selAllSubnav"><a onclick="jsSelectAll(false, 'matchesCalendarContent');"><spring:message code="menu.deselezionatutto"/></a></li>
                    </ul>
                </span>
            </span>
            <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}" >
                <button id="filterGames" class="uk-button uk-button-small ${gameIdsAmount > 0 ? 'uk-button-active' : ''}" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='filtri.team.partite.selezionate'/>"><i class="uk-icon-filter"></i></button> 
                <span id="spanFilterGames" class="uk-dropdown">
                    <ul class="uk-nav uk-nav-navbar">
                        <li class="selAllSubnav"><a onclick="jsFilterGames(false);"><spring:message code="filtri.team.partite.applica"/></a></li>
                        <li class="uk-nav-divider"></li>
                        <li class="selAllSubnav"><a onclick="jsFilterGames(true);"><spring:message code="filtri.team.partite.rimuovi"/></a></li>
                    </ul>
                </span>
            </span>
            <!--<c:if test="${gameIdsAmount > 0}">
                <button id="filterGames" class="uk-button uk-button-small uk-button-active" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='filtri.team.partite.rimuovi'/>" onclick="jsFilterGames(true);"><i class="uk-icon-filter"></i></button>
            </c:if>
            <c:if test="${gameIdsAmount == 0}">
                <button id="filterGames" class="uk-button uk-button-small" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='filtri.team.partite.applica'/>" onclick="jsFilterGames(false);"><i class="uk-icon-filter"></i></button>
            </c:if>-->
            <button id="btnSearch" class="uk-button uk-button-small" data-uk-offcanvas="{target:'#divFilter'}" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.ricerca'/>"><i class="uk-icon-search  "></i> </button>
        </span>
    </c:if>
    <ul id="matchesCalendarContent" class="uk-switcher">
        <li id="liTeamInfoContent" class="uk-visible-small">
            <div class="uk-text-center">
                <div class="container">
                    <div style="flex-basis: 61%; display: flex; justify-content: center; align-items: center">
                        <center>
                            <img height="150px" width="150px" class="imgTeamLogo" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeam.logo}.png" alt="${item.name}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${mPlayer.last_name} ${mPlayer.first_name}">
                            <p style="font-size: 16px; margin-bottom: 0">${mTeam.name}</p>
                            <p style="font-size: 10px; margin: 0">${mComp.name}</p>
                        </center>
                    </div>
                    <div class="large-div" style="flex-basis: 39%">
                        <p style="margin: 0; text-align: center; font-size: 14px;"><spring:message code="team.main.module"/></p>
                        <img height="150px" width="150px" src="data:image/jpeg;base64,${mTeamMostUsedModuleImage}" style="margin-top: 3px; margin-bottom: 3px">
                        <p style="margin: 0; text-align: center; font-weight: bold; font-size: 15px;">${mTeamMostUsedModule.replace("-", " - ")}</p>
                    </div>
                </div>

                <div id="teamStats" style="margin-top: 10px;">
                    <div style="flex-basis: 100%">
                        <div class="box3-header">
                            <center>
                                <p style="font-size: 14px;"><spring:message code="team.partite"/><br>
                                    <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="/sicstv/images/lineup_new.png"><br>
                                    <span style="font-size: 15px" id="totalMatchesAmount">${mTeam.vittorie + mTeam.pareggi + mTeam.sconfitte}</span><br>
                            </center>
                        </div>
                        <div class="box3-header">
                            <center>
                                <p style="font-size: 14px; margin: 0" id="andamentoParagraphMobile"><spring:message code="team.andamento"/></p>
                            </center>
                        </div>
                        <div class="box3-header">
                            <center>
                                <p style="font-size: 14px;"><spring:message code="team.reti"/><br>
                                    <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="/sicstv/images/ball.png"><br>
                                    <c:if test="${mTeam.golFatti >= mTeam.golSubiti}">
                                        <span style="font-size: 15px">${mTeam.golFatti} - ${mTeam.golSubiti} (<span style="color: limegreen">${mTeam.getDiffGoal()}</span>)</span>
                                    </c:if>
                                    <c:if test="${mTeam.golSubiti > mTeam.golFatti}">
                                        <span style="font-size: 15px">${mTeam.golFatti} - ${mTeam.golSubiti} (<span style="color: red">${mTeam.getDiffGoal()}</span>)</span>
                                    </c:if>
                            </center>
                        </div>
                        <div class="box3-header">
                            <center>
                                <p style="font-size: 14px; margin: 0"><spring:message code="team.vittorie"/></p>
                                <span style="font-size: 25px; color: limegreen">${mTeam.vittorie}</span><br>
                                <span style="font-size: 10px" title="<spring:message code="team.vittorie.title"/>">(${mTeam.vittorieInCasa} - ${mTeam.vittorie - mTeam.vittorieInCasa})</span>
                            </center>
                        </div>
                        <div class="box3-header">
                            <center>
                                <p style="font-size: 14px; margin: 0"><spring:message code="team.pareggi"/></p>
                                <span style="font-size: 25px; color: gray">${mTeam.pareggi}</span><br>
                                <span style="font-size: 10px" title="<spring:message code="team.pareggi.title"/>">(${mTeam.pareggiInCasa} - ${mTeam.pareggi - mTeam.pareggiInCasa})</span>
                            </center>
                        </div>
                        <div class="box3-header">
                            <center>
                                <p style="font-size: 14px; margin: 0"><spring:message code="team.sconfitte"/></p>
                                <span style="font-size: 25px; color: red">${mTeam.sconfitte}</span><br>
                                <span style="font-size: 10px" title="<spring:message code="team.sconfitte.title"/>">(${mTeam.sconfitteInCasa} - ${mTeam.sconfitte - mTeam.sconfitteInCasa})</span>
                            </center>
                        </div>
                    </div>

                    <c:if test="${filterToShow.size() > 1}"> 
                        <div class="uk-text-center" style="border: 1px solid; padding: 5px;margin-top:10px;">
                            <c:forEach items="${filterToShow}" var="map" varStatus="status">
                                <button id="configChoice${map.key.replace(' ','_')}" onclick="changePanel('configChoice${map.key.replace(' ','_')}')" class="configChoiceMobile uk-button <c:if test="${map.key == 'SICS'}"> uk-button-success </c:if>" value="${map.key}">${map.key}</button>
                            </c:forEach>
                        </div>
                    </c:if>

                    <c:if test="${mUser.sportId == '0'}">
                        <div class="uk-margin-top">
                            <div class="uk-text-center filterSicsDiv">
                                <div class="panelDiv panelSicsDiv">
                                    <c:forEach items="${filterToShow['SICS'][0]}" var="btn">
                                        <button class='uk-button teamFilterButton' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="showTeamEvents('SICS-_-${btn.key}');" title="<spring:message code='${btn.value[0]}'/>"><i class="uk-icon-video-camera" ></i><spring:message code="${btn.value[0]}"/></button>
                                        </c:forEach>
                                </div>
                                <div class="panelDiv panelSicsDiv">
                                    <c:forEach items="${filterToShow['SICS'][1]}" var="btn">
                                        <button class='uk-button teamFilterButton' <c:if test="${btn.value[1] == 'false'}">disabled</c:if> onclick="showTeamEvents('SICS-_-${btn.key}');" title="<spring:message code='${btn.value[0]}'/>"><i class="uk-icon-video-camera" ></i><spring:message code="${btn.value[0]}"/></button>
                                        </c:forEach>
                                </div>
                            </div>                                        
                        </div>
                    </c:if>
                </div>
                <div>
                    <table id="tablePlayersTeam" class='uk-table uk-table-striped uk-text-center'>

                        <th class='uk-text-center'><spring:message code="team.ruolo"/></th>
                        <th class='uk-text-center'>#</th>
                        <th class='uk-text-center'><spring:message code="team.giocatore"/></th>
                        <th class='uk-text-center'><spring:message code="team.anno"/></th>

                        <tbody>
                            <c:forEach var="map" items="${mPlayers}">

                                <c:forEach var="item" items="${map.value}">
                                    <tr>
                                        <td>${item.ruolo}</td>
                                        <td>${item.numero}</td>
                                        <td><a href="/sicstv/user/player.htm?personal=${personalSource}&playerId=${item.id}<c:choose><c:when test="${mComp.id != null}">&competitionId=${mComp.id}</c:when><c:otherwise></c:otherwise></c:choose><c:choose><c:when test="${mTeam.id != null}">&teamId=${mTeam.id}</c:when><c:otherwise></c:otherwise></c:choose><c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>">${item.last_name} ${item.first_name}</a></td>
                                        <td>${item.getBornDateYear()}</td>
                                    </tr>
                                </c:forEach>
                            </c:forEach>

                        </tbody>
                    </table>
                </div>
            </div>
        </li>
        <li id="competitions">
            <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-6 uk-width-1-1 uk-margin-small-top" >
                <c:forEach var="item" items="${formCompetitionTeam}">
                    <c:if test="${item.id != 12}">
                        <li class="uk-margin-left" onclick="location.href = '/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${item.id}&formTeamId=${mTeam.id}'" style="min-height: 155px;">
                            <a class="uk-text-center zoomOnHover" style="margin-top: 5px" href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${item.id}&formTeamId=${mTeam.id}">
                                <div class="competitionBox" id="${item.id}">
                                    <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                </div>
                                <p style="margin-bottom: 0; margin-top: 3px;">${item.name}</p>
                            </a>
                        </li>
                    </c:if>
                </c:forEach>
                <c:if test="${formCompetitionTeam.size() > 1}">
                    <li class="uk-margin-left" onclick="location.href = '/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=-1&formTeamId=${mTeam.id}'" style="min-height: 155px;">
                        <a style="display: grid; place-items: center" class="uk-text-center zoomOnHover" style="margin-top: 5px" href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=-1&formTeamId=${mTeam.id}">
                            <div class="competitionBox" style="display: grid; grid-template-areas: 'first second' 'third fourth'; grid-template-rows: 42.5px 42.5px; grid-template-columns: 42.5px 42.5px; place-items: center; margin-top: 5px" id="-1">
                                <c:set var="counter" value="${0}"/>
                                <c:forEach var="item" items="${formCompetitionTeam}">
                                    <c:if test="${counter < 4}">
                                        <c:set var="counter" value="${counter + 1}"/>
                                        <img src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                    </c:if>
                                </c:forEach>
                            </div>
                            <p style="margin-bottom: 0; margin-top: 3px;"><spring:message code="menu.tutte"/></p>
                        </a>
                    </li>
                </c:if>
                <c:forEach var="item" items="${mGironi}">
                    <li class="uk-margin-left" onclick="location.href = '/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${item.id}&formTeamId=${mTeam.id}&groupId=${item.groupId}'" style="min-height: 155px;">
                        <a class="uk-text-center zoomOnHover" style="margin-top: 5px" href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${item.id}&formTeamId=${mTeam.id}&groupId=${item.groupId}">
                            <div class="competitionBox" id="${item.id}-${item.groupId}">
                                <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                            </div>
                            <p style="margin-bottom: 0; margin-top: 3px;">${item.name} - ${item.groupName}</p>
                        </a>
                    </li>
                </c:forEach>
            </ul>
        </li>
        <li id="matches" ></li>
            <c:if test="${!generalPageTeam}">
            <li id="calendario"></li>
            </c:if>
        <!-- <li id="allMatches" ></li> -->
    </ul>

    <div id="divFilter" class="uk-offcanvas">
        <div class="uk-offcanvas-bar uk-offcanvas-bar-flip">
            <%@ include file="searchMulti.jsp" %>
        </div>
    </div>
</div>
</div>

<div id="reportModal" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
    <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
        <div>
            <span class="modalClose" onclick="closeReportModal();">&times;</span>
        </div>
        <p style="font-size: 20px; text-align: center; margin-bottom: 0"><spring:message code="team.report.competition.title"/></p>
        <p style="font-size: 12px; text-align: center; margin-top: 0">(<spring:message code="team.report.click.title"/>)</p>
        <div id="reportModalCompetitions">
            <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-6 uk-width-1-1 uk-margin-small-top">
                <c:forEach var="item" items="${formCompetitionTeam}">
                    <li class="uk-margin-left" style="min-height: 155px; width: auto">
                        <button style="height: 100%; width: 130px" onclick="removeAllCheckedGironi();
                                jsManageActive(this)" class="uk-button <c:if test="${(mComp.id == null || item.id == mComp.id) && mGirone == null}">uk-button-active</c:if>" data-value="${item.id}">
                            <img class="logoCompImg uk-margin-top" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                            <p style="margin-bottom: 0; margin-top: 10px; line-height: normal">${item.name}</p>
                        </button>
                    </li>
                </c:forEach>
            </ul>
        </div>
        <c:if test="${mGironi != null && mGironi.size() > 0}">
            <p style="font-size: 20px; text-align: center; margin-bottom: 0"><spring:message code="menu.user.gironi"/></p>
            <p style="font-size: 12px; text-align: center; margin-top: 0">(<spring:message code="menu.user.singolo.girone"/>)</p>
            <div id="reportModalCompetitionsGironi">
                <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-6 uk-width-1-1 uk-margin-small-top">
                    <c:forEach var="item" items="${mGironi}">
                        <li class="uk-margin-left" style="min-height: 155px; width: auto">
                            <button style="height: 100%; width: 130px" onclick="removeAllChecked(this);
                                    jsManageActive(this)" class="uk-button <c:if test="${item.groupId == mGirone.groupId}">uk-button-active</c:if>" data-value="${item.id}-${item.groupId}">
                                <img class="logoCompImg uk-margin-top" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                <p style="margin-bottom: 0; margin-top: 10px; line-height: normal">${item.name} - ${item.groupName}</p>
                            </button>
                        </li>
                    </c:forEach>
                </ul>
            </div>
        </c:if>
        <div class="uk-flex justify-content-center align-items-center uk-margin-bottom">
            <label class="uk-margin-right-small"><spring:message code="lib.dataDa"/></label>
            <input id="reportModalFrom" autocomplete="off" class="ui-state-default ui-corner-all inputSpacer3 uk-float-left" type="text" data-uk-datepicker="{format:'DD/MM/YYYY'}" size="20" style="text-align:center"/>
            <label class="uk-margin-left uk-margin-right-small"><spring:message code="lib.dataA"/></label>
            <input id="reportModalTo" autocomplete="off" class="ui-state-default ui-corner-all inputSpacer3 uk-float-left" type="text" data-uk-datepicker="{format:'DD/MM/YYYY'}" size="20" style="text-align:center"/>
        </div>
        <div class="uk-modal-footer uk-text-right uk-width-1-1">
            <button style="min-width:80px;" onclick="generateReport();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.genera"/></button>
            <button style="min-width:80px;" onclick="closeReportModal();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
            <span id="saveModifyPlaylist"></span>
        </div>
    </div>
</div>

<div id="upload-logo-modal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
    <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
        <div>
            <span class="modalClose" onclick="closeUploadLogo();">&times;</span>
            <h3><spring:message code="upload.team.logo"/></h3>
            <div class="uk-margin uk-modal-content uk-width-1-1">
                <form action="/" class="dropzone" id="uploadLogoDropzone">
                    <div class="dz-message">
                        <h2><spring:message code="menu.upload.selectlogo"/></h2>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
    
    
</body>
</html>