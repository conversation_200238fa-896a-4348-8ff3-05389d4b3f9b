<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/css/flat/flat.css" rel="stylesheet">
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />

        <!-- Keep here: needed for teamStats.jsp -->
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/3.2.1/css/fixedColumns.dataTables.min.css"/>
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdn.datatables.net/fixedcolumns/3.2.1/js/dataTables.fixedColumns.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.colVis.min.js"></script>
        <!-- Keep here: needed for teamStats.jsp for buttons (export) -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
        <script src="https://cdn.datatables.net/colreorder/1.7.0/js/dataTables.colReorder.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>
        
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>
        
        <style>
            .title {
                background: #f27e46;
                color: #ffffff;
                text-transform: uppercase;
                font-size: 12px;
                height: 20px;
                vertical-align: middle;
                padding: 8px 12px 0px 12px;
                margin: 0;
            }

            .orangeBorder {
                border-color: #f27e46;
                border-width: 0px 2px 2px 2px;
                border-style: solid solid solid solid;
                padding: 5px 5px 5px 5px;
                overflow-x: auto;
            }

            @media print {
                body * {
                    visibility:hidden;
                }
            }
        </style>

        <script type="text/javascript">
            var teamStatsMessage = "<spring:message code="team.stats.note"/>";
            var pageStatus = [];
            var totMatches = [];
            var numMatches = [];
            var matchPerPage = 25;
            var dropzone;

            $(document).ready(function () {
                // Configura Dropzone
                Dropzone.autoDiscover = false;
                
            <c:if test="${mElementToClick != null}">
                $("#${mElementToClick}").click();
            </c:if>

                jsRefreshMatches(1);
                initSearchFilter();
                if ($(window).width() <= 768) {
                    $("#teamsTab").addClass("uk-active");
                    $("#teamsContent").addClass("uk-active");
                    $("#calendarTab1").removeClass("uk-active");
                    $("#matches").removeClass("uk-active");
                } else {
                    $("#calendarTab1").addClass("uk-active");
                    $("#matches").addClass("uk-active");
                    $("#teamsTab").removeClass("uk-active");
                }

                var idsToCheck = $("#gameIds").val();
                if (idsToCheck !== '') {
                    selectedMatches = idsToCheck.split(",");
                }

            <c:if test="${mHasGironi && mGirone == null}">
                <c:if test="${isSingoloGirone}">
                setTimeout(function () {
                    $("#detailLevel").val(100);
                }, 100);
                </c:if>
                <c:if test="${!isSingoloGirone}">
                setTimeout(function () {
                    $("#detailLevel").val(200);
                }, 100);
                </c:if>
            </c:if>
            <c:if test="${mHasGironi && mGirone != null}">
                setTimeout(function () {
                    $("#detailLevel").val(${mGirone.groupId});
                }, 100);
            </c:if>
            <c:if test="${mHasGironi && mGirone == null && mComp.id == 12}">
                setTimeout(function () {
                    $("#detailLevel").val(1);
                    changeDetailLevel();
                }, 100);
            </c:if>

                // sistemo mysics.tv
                if (${personalSource} === true) {
                    $("#statsTab").remove();
                    $("#searchTab").remove();
                    $("#rankingTabLi").remove();
                }
            });

            /*
             function jsDownload(id) {
             
             var strUrl = "/sicstv/user/download.htm?id=" + id;
             
             $.ajax({
             type: "GET",
             url: strUrl,
             cache: false,
             success: function (msg) {
             if (msg.substr(0, 4) === "true") {
             UIkit.notify(msg.substr(5), {status: 'danger'});
             } else {
             UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success'});
             window.location.replace(msg.substr(5));
             }
             }
             });
             }
             */


            // logout ajax
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);

            function jsRefreshMatches(page) {


                var idC = $("#idComp").val();
                refreshMatches('matchesList', 'matches', page, idC);
            }

            function switchMatchday(step) {

                var valDay = parseInt($("#mDay").val());
                if (step === "prev") {
                    valDay = valDay - 1;
                } else if (step === "next") {
                    valDay = valDay + 1;
                } else {
                    valDay = step;
                }

                if (valDay >= 1 && valDay <= parseInt($("#mMaxDay").val())) {
                    jsLoadingAlpha("#matchDayCalendar");
                    $("#mDay").val(valDay);
                    jsRefreshMatchesCalendar(${mComp.id});
                    jsLoadingOff("#matchDayCalendar");
                }
            }

            function filterData() {
                readFilterMulti(false);
                filterMatches('matches', 1);
            }

            function jsFilterGames(removeFilters) {
                if (removeFilters) {
                    var currentUrl = window.location.href;
                    if (currentUrl.includes("gameIds")) {
                        currentUrl = currentUrl.substring(0, currentUrl.indexOf("&gameIds"));
                    }
                    window.location.href = currentUrl;
                } else if (selectedMatches.length > 0) {
                    var currentUrl = window.location.href;
                    if (currentUrl.includes("gameIds")) {
                        currentUrl = currentUrl.substring(0, currentUrl.indexOf("&gameIds"));
                    }
                    window.location.href = currentUrl + "&gameIds=" + selectedMatches;
                }
            }

            function resetView() {
                $("#tabMenu").removeClass("uk-hidden");
                $("#competitions").addClass("uk-width-6-10");
                $("#competitions").removeClass("uk-width-10-10");
                $(".dt-button.uk-button-active").removeClass("uk-button-active");
            }

            function showSicsFilters(type) {
                if (type === 'teams') {
                    setTimeout(function () {
                        $("#teamStatsLi").removeClass("uk-hidden");
                        $("#teamStatsLi").attr("aria-hidden", "false");
                        $("#teamStatsLi").addClass("uk-active");

                        $("#playerStatsLi").addClass("uk-hidden");
                        $("#playerStatsLi").attr("aria-hidden", "true");
                        $("#playerStatsLi").removeClass("uk-active");
                    }, 100);

                    if ($("#teamStatsContainerteams").length <= 0) {
                        jsLoadTeamStats("teamStatsLi");
                    }
                } else if (type === 'players') {
                    setTimeout(function () {
                        $("#teamStatsLi").addClass("uk-hidden");
                        $("#teamStatsLi").attr("aria-hidden", "true");
                        $("#teamStatsLi").removeClass("uk-active");

                        $("#playerStatsLi").removeClass("uk-hidden");
                        $("#playerStatsLi").attr("aria-hidden", "false");
                        $("#playerStatsLi").addClass("uk-active");
                    }, 100);

                    if ($("#teamStatsContainercompetitionPlayers").length <= 0) {
                        jsLoadTeamStatsCompetitionPlayers("playerStatsLi");
                    }
                }
            }

            function hideAllStats() {
                $("#teamStatsLi").addClass("uk-hidden");
                $("#playerStatsLi").addClass("uk-hidden");
            }

            function changeDetailLevel() {
                var levelValue = $("#detailLevel").val();
                var basePath = location.href;
                if (basePath.indexOf("&groupId") !== -1) {
                    basePath = basePath.substring(0, basePath.indexOf("&groupId"));
                }
                if (basePath.indexOf("&group") !== -1) {
                    basePath = basePath.substring(0, basePath.indexOf("&group"));
                }

                /*if (levelValue === '100') {
                    location.href = basePath + "&group=false";
                } else*/ if (levelValue === '200') {
                    location.href = basePath;
                } else {
                    location.href = basePath + "&groupId=" + levelValue;
                }
            }

            function loadEventsTab(force, teamChanged) {
                if ($("#eventsTabLiContent").length === 0 || force) {
                    var from = $("#dateFrom").val();
                    var to = $("#dateTo").val();
                    var teamSelected = $("#competitionTeam").val();
                    var playerSelected = $("#competitionTeamPlayer").val();

                    if (typeof from === 'undefined' || !from) {
                        from = "";
                    }
                    if (typeof to === 'undefined' || !to) {
                        to = "";
                    }
                    if (typeof teamSelected === 'undefined' || !teamSelected) {
                        teamSelected = "";
                    }
                    if (typeof playerSelected === 'undefined' || !playerSelected || teamChanged) {
                        playerSelected = "";
                    }

                    var params = "competitionId=" + $("#idComp").val() + "&from=" + from + "&to=" + to + "&teamId=" + teamSelected + "&playerId=" + playerSelected;
                    if (typeof sessionStorage['competitionEventsParams'] !== 'undefined'
                            && $("#eventsTabLiContent").length === 0
                            && (typeof sessionStorage['competitionEventsId'] !== 'undefined' && sessionStorage['competitionEventsId'] === $("#idComp").val())) {
                        params = sessionStorage['competitionEventsParams'];
                    }
                    sessionStorage['competitionEventsParams'] = params;
                    sessionStorage['competitionEventsId'] = $("#idComp").val();

                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/competitionEvents.htm",
                        data: encodeURI(params),
                        cache: false,
                        success: function (msg) {
                            $("#eventsTabLi").html(msg);
                            $.unblockUI();
                        }
                    });
                }
            }

            function loadRankingTab(force) {
                if ($("#competitionRankingContainer").length === 0 || force) {
                    var from = $("#dateFrom").val();
                    var to = $("#dateTo").val();

                    if (typeof from === 'undefined' || !from) {
                        from = "";
                    }
                    if (typeof to === 'undefined' || !to) {
                        to = "";
                    }

                    var params = "competitionId=" + $("#idComp").val() + "&groupId=${mGirone.groupId}&from=" + from + "&to=" + to;
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/getCompetitionRanking.htm",
                        data: encodeURI(params),
                        cache: false,
                        success: function (msg) {
                            $("#competitionRankingTabLi").html(msg);
                            $.unblockUI();
                        }
                    });
                }
            }
            
            function manageUploadLogo(competitionId) {
                if ($(event.target).attr("disabled")) {
                    UIkit.notify("<spring:message code="upload.logo.permission.denied"/>", {status: 'danger', timeout: 1000});
                    return;
                }
                
                if (typeof dropzone === "undefined") {
                    dropzone = initializeLogoDropzone("uploadLogoDropzone");
                }
                
                dropzone.options.url = dropzone.options.baseUrl + "competitionId=" + competitionId;
                $("#upload-logo-modal").addClass("uk-open");
                $("#upload-logo-modal").removeClass("uk-hidden");
            }
            
            function closeUploadLogo() {
                $("#upload-logo-modal").removeClass("uk-open");
                $("#upload-logo-modal").addClass("uk-hidden");
            }
        </script>
    </head>
    <body>

        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>

        <div>
            <div class="uk-grid ">
                <div id="competitions" class="uk-width-6-10 uk-hidden-small">
                    <input type="hidden" value="${mComp.id}" id="idComp"/>
                    <input type="hidden" value="${gameIds}" id="gameIds"/>
                    <input type="hidden" value="${mUser.savedSeason}" id="pageSeasonId"/>
                    <div id="breadcrumb">
                        <span onclick="manageUploadLogo(${mComp.id})" class="uk-float-right uk-margin-remove" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='upload.competition.logo'/>"><i class="uk-icon-upload" ${mUser.groupsetId != 2 ? "disabled" : ""}></i></span>
                        <ul class="uk-breadcrumb uk-margin-left">
                            <c:if test="${!personalSource}">
                                <c:if test="${!mCountry.internationalCompetition}">
                                    <li>
                                        <a href="/sicstv/user/home.htm?country=${!mCountry.internationalCompetition}"><spring:message code="menu.user.countries"/></a>
                                    </li>
                                    <li>
                                        <a href="/sicstv/user/country.htm?countryId=${mCountry.id}">${mCountry.name}</a>
                                    </li>
                                </c:if>
                                <c:if test="${mCountry.internationalCompetition}">
                                    <li>
                                        <a href="/sicstv/user/home.htm?country=${!mCountry.internationalCompetition}"><spring:message code="menu.user.internationalCompetitions"/></a>
                                    </li>
                                    <li>
                                        <a href="/sicstv/user/country.htm?intCompId=${mCountry.id}">${mCountry.name}</a>
                                    </li>
                                    <c:if test="${mParameterCountry != null}">
                                        <li>
                                            <a href="/sicstv/user/country.htm?countryId=${mParameterCountry.id}">${mParameterCountry.name}</a>
                                        </li>
                                    </c:if>
                                </c:if>
                            </c:if>
                            <c:if test="${personalSource}">
                                <li>
                                    <a href="/sicstv/user/mycompetition.htm"><spring:message code="user.competitions"/></a>
                                </li>
                            </c:if>
                            <li>
                                <a href="/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${mComp.id}">${mComp.name}</a> <i id="showCurrentFilter"></i>
                            </li>
                            <c:if test="${mGirone != null}">
                                <li style="text-transform: uppercase">${mGirone.groupName}</li>   
                                </c:if>
                        </ul>
                    </div>

                    <div>
                        <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#competitionsContent'}">
                            <li class="uk-active"><a id="teamsTab" onclick="resetView();
                                    saveTab('teamsTab', ${mComp.id});"><spring:message code="menu.user.squadre"/></a></li>
                            <li onclick="hideAllStats()" id="statsTab">
                                <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}">
                                    <spring:message code='menu.user.statistiche'/>
                                    <span id="spanTeamStatsDropdown" class="uk-dropdown">
                                        <ul class="uk-nav uk-nav-navbar">
                                            <li class="selAllSubnav"><a id="filterSicsTeamStatsLink" onclick="showSicsFilters('teams');
                                                    saveTab('filterSicsTeamStatsLink', ${mComp.id});"><i class="uk-margin-right"></i><spring:message code="team.stats.team"/></a></li>
                                            <li class="uk-nav-divider"></li>
                                            <li class="selAllSubnav"><a id="filterSicsTeamStatsPlayersLink" onclick="showSicsFilters('players');
                                                    saveTab('filterSicsTeamStatsPlayersLink', ${mComp.id});"><i class="uk-margin-right"></i><spring:message code="team.stats.giocatori"/></a></li>
                                        </ul>
                                    </span>
                                </span>
                                <!--<a id="statisticsTab" onclick="jsLoadTeamStats('teamStatsLi'); saveTab('statisticsTab', ${mComp.id});"><spring:message code="menu.user.statistiche"/><div class="competition-statistics-new-div"><div class="competition-statistics-new-text">NEW</div></div></a>-->
                            </li>
                            <li id="searchTab"><a id="eventsTab" onclick="resetView();
                                    saveTab('eventsTab', ${mComp.id}); loadEventsTab();" style="text-transform: uppercase; padding-right: 15px"><spring:message code="video.ricerca"/></a></li>
                            <c:if test="${mComp.ranking && (!mHasGironi || mGirone != null)}">
                                <li id="rankingTabLi"><a id="rankingTab" onclick="resetView();
                                        saveTab('rankingTab', ${mComp.id}); loadRankingTab();" style="text-transform: uppercase; padding-right: 15px"><spring:message code="menu.user.classifica"/></a></li>
                            </c:if>
                        </ul>

                        <c:if test="${mHasGironi}">
                            <div class="uk-float-right uk-margin-small-right">
                                <span class="uk-form">
                                    <spring:message code="menu.user.livello.dettaglio"/>:
                                    <select id="detailLevel" onchange="changeDetailLevel();">
                                        <c:if test="${mComp.id != 12}">
                                            <option value="100" <c:if test="${mGirone == null && isSingoloGirone}">selected</c:if>><spring:message code="menu.user.competizione"/></option>
                                            <option value="200" <c:if test="${mGirone == null && !isSingoloGirone}">selected</c:if>><spring:message code="menu.user.gironi"/></option>
                                                <option disabled>----- ----- ----- -----</option>
                                        </c:if>
                                        <c:forEach var="girone" items="${mGironi.keySet()}">
                                            <option value="${mGironi.get(girone)}" <c:if test="${mGirone != null && mGirone.groupId == mGironi.get(girone)}">selected</c:if>>${girone}</option>
                                        </c:forEach>
                                    </select>
                                </span>
                            </div>
                        </c:if>

                        <ul id="competitionsContent" class="uk-switcher uk-margin">
                            <li>
                                <c:choose>
                                    <c:when test="${!mHasGironi || (mGroup != null && !mGroup)}">
                                        <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-10 uk-width-1-1 uk-margin-small-top" >
                                            <c:forEach var="item" items="${mTeams}">
                                                <li class="uk-margin-left" id="${item.id}" onclick="location.href = '/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${mComp.id}&formTeamId=${item.id}'" style="min-height: 155px;">
                                                    <a class="uk-text-center zoomOnHover" style="margin-top: 5px" href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${mComp.id}&formTeamId=${item.id}">
                                                        <div class="competitionBox">
                                                            <span style="text-align: right !important; height: 2px; margin: 0">
                                                                <c:if test="${mTeamsInFavorites.contains(item.id)}">
                                                                    <i class="uk-icon-heart" title="<spring:message code="favorites.team.in"/>"></i>
                                                                </c:if>
                                                            </span>
                                                            <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${item.name}">
                                                        </div>
                                                        <p style="margin-bottom: 0; margin-top: 3px;">
                                                            ${item.name}
                                                        </p>
                                                    </a>
                                                </li>
                                            </c:forEach>
                                        </ul>
                                    </c:when>
                                    <c:otherwise>
                                        <c:forEach var="girone" items="${mTeamsGrouped.keySet()}">
                                        <center>
                                            <p class="title" style="text-transform: uppercase; margin: 15px 20px 0px 0px;">
                                                <a href="/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${mComp.id}&groupId=${mGironi.get(girone)}" style="color: white; text-decoration: underline">${girone}</a>
                                            </p>
                                        </center>
                                        <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-grid-width-medium-1-4 uk-grid-width-large-1-5 uk-grid-width-xlarge-1-10 uk-width-1-1 uk-margin-small-top" >
                                            <c:forEach var="item" items="${mTeamsGrouped.get(girone)}">
                                                <li class="uk-margin-left" id="${item.id}" onclick="location.href = '/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${mComp.id}&formTeamId=${item.id}&groupId=${mGironi.get(girone)}'" style="min-height: 155px;">
                                                    <a class="uk-text-center zoomOnHover" style="margin-top: 5px" href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${mComp.id}&formTeamId=${item.id}&groupId=${mGironi.get(girone)}">
                                                        <div class="competitionBox">
                                                            <span style="text-align: right !important; height: 2px; margin: 0">
                                                                <c:if test="${mTeamsInFavorites.contains(item.id)}">
                                                                    <i class="uk-icon-heart" title="<spring:message code="favorites.team.in"/>"></i>
                                                                </c:if>
                                                            </span>
                                                            <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${item.name}">
                                                        </div>
                                                        <p style="margin-bottom: 0; margin-top: 3px;">${item.name}</p>
                                                    </a>
                                                </li>
                                            </c:forEach>
                                        </ul>
                                    </c:forEach>
                                </c:otherwise>
                            </c:choose>
                            </li>
                            <li id="teamStatsLi">
                            </li>
                            <li id="eventsTabLi">
                            </li>
                            <c:if test="${mComp.ranking && (!mHasGironi || mGirone != null)}">
                                <li id="competitionRankingTabLi">
                                </li>
                            </c:if>
                            <li id="playerStatsLi">
                            </li>
                        </ul>
                    </div>

                </div>

                <div id="tabMenu" class="uk-width-4-10">

                    <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#matchesCalendarContent'}" id="calendarTab">
                        <li id="teamsTab"  ><a class="uk-visible-small"><spring:message code="menu.user.squadre"/></a></li>
                        <li id="calendarTab1" class="uk-active">
                            <a>${mComp.name}
                                <span id="numMatches" class="uk-badge uk-hidden" onclick="jsRefreshMatches('1');">0</span>
                            </a>
                            <div id="matchesList" class="uk-margin-top uk-margin-left"></div>
                        </li>
                        <li id="calendarTab3"><a><spring:message code="menu.user.calendario"/></a></li>

                    </ul>

                    <span class="uk-float-right uk-margin-small-top uk-margin-small-right uk-hidden-small">
                        <button id="addToMulti" class="uk-button uk-button-small" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.multipartita'/>" onclick="jsGoToMultipartita('${personalSource}');"><i class="uk-icon-external-link  "></i></button>
                        <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}" >
                            <button id="selectedMatches" class="uk-button uk-button-small ${gameIdsAmount > 0 ? 'uk-button-active' : ''}" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.selezionati'/>"><i class="uk-icon-bars"></i> ${gameIdsAmount != null ? gameIdsAmount : 0} </button> 
                            <!--<a href="#" id="selectedMatches" class="uk-button uk-button-small" ><i class="uk-icon-bars  "></i> 0 </a>-->
                            <span id="spanSelAll" class="uk-dropdown">
                                <ul class="uk-nav uk-nav-navbar">
                                    <li class="selAllSubnav"><a onclick="jsSelectAll(true, 'matchesCalendarContent');"><spring:message code="menu.selezionatutto"/></a></li>
                                    <li class="uk-nav-divider"></li>
                                    <li class="selAllSubnav"><a onclick="jsSelectAll(false, 'matchesCalendarContent');"><spring:message code="menu.deselezionatutto"/></a></li>
                                </ul>							
                            </span>
                        </span>
                        <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}" >
                            <button id="filterGames" class="uk-button uk-button-small ${gameIdsAmount > 0 ? 'uk-button-active' : ''}" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='filtri.team.partite.selezionate'/>"><i class="uk-icon-filter"></i></button> 
                            <span id="spanFilterGames" class="uk-dropdown">
                                <ul class="uk-nav uk-nav-navbar">
                                    <li class="selAllSubnav"><a onclick="jsFilterGames(false);"><spring:message code="filtri.team.partite.applica"/></a></li>
                                    <li class="uk-nav-divider"></li>
                                    <li class="selAllSubnav"><a onclick="jsFilterGames(true);"><spring:message code="filtri.team.partite.rimuovi"/></a></li>
                                </ul>							
                            </span>
                        </span>
                        <button id="btnSearch" class="uk-button uk-button-small" data-uk-offcanvas="{target:'#divFilter'}" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.ricerca'/>"><i class="uk-icon-search  "></i> </button>
                    </span>

                    <ul id="matchesCalendarContent" class="uk-switcher">
                        <li id="teamsContent" class="uk-active uk-visible-small">
                            <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-width-1-1 uk-margin-small-top" >
                                <c:forEach var="item" items="${mTeams}">
                                    <li class="competitionBox uk-margin-left uk-margin-top" id="${item.id}">
                                        <a class="uk-text-center uk-margin-top" href="/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${mComp.id}&formTeamId=${item.id}">
                                            <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" alt="${item.name}">
                                            <p data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                        </a>
                                    </li>
                                </c:forEach>
                            </ul>
                        </li>
                        <li id="matches"></li>
                        <li id="calendario"></li>
                    </ul>

                    <div id="divFilter" class="uk-offcanvas">
                        <div class="uk-offcanvas-bar uk-offcanvas-bar-flip">
                            <%@ include file="searchMulti.jsp" %>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="upload-logo-modal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeUploadLogo();">&times;</span>
                    <h3><spring:message code="upload.competition.logo"/></h3>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <form action="/" class="dropzone" id="uploadLogoDropzone">
                            <div class="dz-message">
                                <h2><spring:message code="menu.upload.selectlogo"/></h2>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>