<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div>
    
    <script type="text/javascript">
        function getDateFormatted(id, stringDate) {
            var date = new Date(stringDate);
            var formatted = date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear() + " " + date.getHours() + ":" + date.getMinutes();
            
            $("#playlistTvCreationDate" + id).html(formatted);
        }
        
        var clipEditPlayerHTML, timerClockHTMLEdit, seekSliderEdit;
        var startAzione, endAzione;
        $(document).ready(function () {
            startAzione = $('#mSecStart').val();
            endAzione = $('#mSecEnd').val();
            
            var videoHtmlToAdd = '<source src="${mVideoPath}" type="video/mp4">';
            $('#clipEditPlayerHTML').html(videoHtmlToAdd);
            clipEditPlayerHTML = $('#clipEditPlayerHTML').get(0);
            clipEditPlayerHTML.className += ' hideSeekBar';
            clipEditPlayerHTML.currentTime = startAzione;
            clipEditPlayerHTML.addEventListener("click", (event) => {
                if (event.detail === 1) { // 1 click
                    if (clipEditPlayerHTML.paused) {
                        if (clipEditPlayerHTML.currentTime >= seekSliderEdit.dataset.endVideo) {
                            clipEditPlayerHTML.currentTime = seekSliderEdit.dataset.initVideo;
                            window.clearTimeout(timerClockHTMLEdit);
                            timerClockHTMLEdit.set({time: 500, autostart: true});
                        }
                        clipEditPlayerHTML.play();
                    } else {
                        clipEditPlayerHTML.pause();
                    }
                    checkPlayButtonClip();
                }
            });
            
            var clipDuration = parseInt(endAzione - startAzione);
            seekSliderEdit = document.getElementById('seek-slider-edit-clip');
            seekSliderEdit.max = clipDuration * 2;
            seekSliderEdit.value = 0;
            seekSliderEdit.dataset.initVideo = startAzione;
            seekSliderEdit.dataset.endVideo = endAzione;
            updateClipDuration();
            
            timerClockHTMLEdit = $.timer(function () {
                if (clipEditPlayerHTML && !clipEditPlayerHTML.paused) {
                    var nowTime = clipEditPlayerHTML.currentTime;
                    var timeToEnd = parseFloat(seekSliderEdit.dataset.endVideo - nowTime);
                    //var valueToSet = clipEditPlayerHTML.currentTime + parseInt(seekSliderEdit.dataset.initVideo) + parseInt(seekSliderEdit.min);
                    var valueToSet = parseInt(seekSliderEdit.max - parseInt(timeToEnd * 2));
                    seekSliderEdit.value = parseInt(valueToSet);
                    updateClipTimePassed();
                    if (nowTime > seekSliderEdit.dataset.endVideo) {
                        clipEditPlayerHTML.pause();
                        checkPlayButtonClip();
                        this.stop();
                    }
                }
            });
            timerClockHTMLEdit.set({time: 500, autostart: true});
        });
        
        var maxClipVariation = 120, maxClipVariationEnd = 120;
        function modifyClipStart(seconds) {
            if (seconds !== 0) {
                if (parseInt(seekSliderEdit.dataset.initVideo) + seconds >= 0) {
                    if (Math.abs(parseInt(startAzione) - parseInt(seekSliderEdit.dataset.initVideo)) < maxClipVariation || seconds > 0) {
                        seekSliderEdit.dataset.initVideo = parseInt(seekSliderEdit.dataset.initVideo) + seconds;

                        seconds = seconds * 2;
                        seekSliderEdit.min = parseInt(seekSliderEdit.min) + seconds;
                        //seekSliderEdit.value = parseInt(seekSliderEdit.value) + seconds;
                        seekSliderEdit.value = seekSliderEdit.min;
                        clipEditPlayerHTML.currentTime = seekSliderEdit.dataset.initVideo;
                        clipEditPlayerHTML.pause();
                        /*if (clipEditPlayerHTML.paused) {
                            clipEditPlayerHTML.play();
                            timerClockHTMLEdit.set({time: 500, autostart: true});
                        }*/
                        //console.log("newTime: " + clipEditPlayerHTML.currentTime + " min: " + seekSliderEdit.min + " currentTime: " + clipEditPlayerHTML.currentTime);
                        updateClipDuration();
                        checkPlayButtonClip();
                        updateClipTimePassed();
                    }
                }
            }
        }
        
        function cutClipStart() {
            var seconds;
            if (parseInt(seekSliderEdit.min) >= 0) {
                seconds = (parseInt(seekSliderEdit.value) - parseInt(seekSliderEdit.min)) / 2;
            } else {
                seconds = Math.abs(parseInt(seekSliderEdit.min) - parseInt(seekSliderEdit.value)) / 2;
            }
            modifyClipStart(seconds);
        }

        function modifyClipEnd(seconds) {
            if (seconds !== 0) {
                var maxValue = clipEditPlayerHTML.duration;
                if (parseInt(maxValue) > (parseInt(seekSliderEdit.dataset.endVideo) + seconds)) {
                    if (Math.abs(parseInt(endAzione) - parseInt(seekSliderEdit.dataset.endVideo)) < maxClipVariationEnd || seconds < 0) {
                        seekSliderEdit.dataset.endVideo = parseInt(seekSliderEdit.dataset.endVideo) + seconds;
                        /*if (seekSliderEdit.value === seekSliderEdit.max) {
                            if (seconds > 0) {
                                clipEditPlayerHTML.currentTime = clipEditPlayerHTML.currentTime - seconds;
                            } else {
                                clipEditPlayerHTML.currentTime = clipEditPlayerHTML.currentTime + seconds;
                            }
                        }*/
                        clipEditPlayerHTML.pause();
                        clipEditPlayerHTML.currentTime = seekSliderEdit.dataset.endVideo;
                        seconds = seconds * 2;
                        seekSliderEdit.max = parseInt(seekSliderEdit.max) + seconds;
                        /*if (seekSliderEdit.value === seekSliderEdit.max) {
                            seekSliderEdit.value = parseInt(seekSliderEdit.value) + seconds;
                        }*/
                        seekSliderEdit.value = seekSliderEdit.max;
                        updateClipDuration();
                        checkPlayButtonClip();
                        updateClipTimePassed();
                    }
                }
            }
        }
        
        function cutClipEnd() {
            var seconds;
            if (parseInt(seekSliderEdit.min) >= 0) {
                seconds = (parseInt(seekSliderEdit.value) - parseInt(seekSliderEdit.max)) / 2;
            } else {
                seconds = -(parseInt(seekSliderEdit.max) - parseInt(seekSliderEdit.value)) / 2;
            }
            modifyClipEnd(seconds);
            // clipEditPlayerHTML.currentTime = clipEditPlayerHTML.currentTime - 1.5;
        }

        function updateClipDuration() {
            var timeToEnd = Math.ceil(parseFloat(seekSliderEdit.dataset.endVideo - seekSliderEdit.dataset.initVideo));
            var stringTimeToEnd = "00";
            var sec = timeToEnd;
            if (timeToEnd > 59) {
                var min = Math.floor(timeToEnd / 60);
                if (min > 9) {
                    stringTimeToEnd = min;
                } else {
                    stringTimeToEnd = "0" + min;
                }
                sec = timeToEnd % 60;
            }
            if (parseInt(sec) > 9) {
                stringTimeToEnd += ":" + parseInt(sec);
            } else {
                stringTimeToEnd += ":0" + parseInt(sec);
            }
            $(".clipDurationFormatted").html(stringTimeToEnd);
        }
        
        function updateClipTimePassed() {
            /*var totalDuration = parseFloat(seekSliderEdit.dataset.endVideo - seekSliderEdit.dataset.initVideo);
            var timeToEnd = parseFloat(seekSliderEdit.dataset.endVideo - parseFloat(clipEditPlayerHTML.currentTime));
            var stringTimeToEnd = "00";
            var sec = parseInt(totalDuration - timeToEnd); // secondi al contrario. Es.: 30 sec clip e 2 passati: sec = 28. 30 - 28 = 2 sec*/
            var stringTimeToEnd = "00";
            var sec = parseInt((parseInt(seekSliderEdit.value) - parseInt(seekSliderEdit.min)) / 2); // secondi al contrario. Es.: 30 sec clip e 2 passati: sec = 28. 30 - 28 = 2 sec
            if (sec > 59) {
                var min = Math.floor(sec / 60);
                if (min > 9) {
                    stringTimeToEnd = min;
                } else {
                    stringTimeToEnd = "0" + min;
                }
                sec = sec % 60;
            }
            if (parseInt(sec) > 9) {
                stringTimeToEnd += ":" + parseInt(sec);
            } else {
                stringTimeToEnd += ":0" + parseInt(sec);
            }
            $(".clipDurationTimePassed").html(stringTimeToEnd);
        }

        function updateEditPlayerFromSeekSlider() {
            var initialClipSeconds = startAzione;
            var currentSliderValue = seekSliderEdit.value;

            /*var timeToSet = (parseInt(initialClipSeconds) + parseFloat(currentSliderValue / 2));
            if (timeToSet < 0) { // se sto aggiungendo secondi all'inizio della clip vado in negativo
                timeToSet = timeToSet + parseInt(startAzione);
            }*/
            var timeToSet = (parseInt(initialClipSeconds) + parseFloat(currentSliderValue / 2));
            /*if (timeToSet < 0 || seekSliderEdit.min < 0) { // se sto aggiungendo secondi all'inizio della clip vado in negativo
                console.log("timeToSet before: " + timeToSet);
                timeToSet = timeToSet + parseInt(startAzione);
            }*/
            if (clipEditPlayerHTML.currentTime !== timeToSet) {
                clipEditPlayerHTML.pause();
                clipEditPlayerHTML.currentTime = timeToSet;
                window.clearTimeout(timerClockHTMLEdit);
                timerClockHTMLEdit.set({time: 500, autostart: true});
            }
            updateClipTimePassed();
            checkPlayButtonClip();
        }

        function stopEditVideo() {
            if (!clipEditPlayerHTML.paused) {
                clipEditPlayerHTML.pause();
            }
            checkPlayButtonClip();
        }

        function resumeEditVideo() {
            if (clipEditPlayerHTML.paused) {
                // uso un delay altrimenti se tengo premuto e mi muovo molto piano nella barra poi non esegue correttamente
                setTimeout( function() {
                    clipEditPlayerHTML.play();
                    checkPlayButtonClip();
                }, 250);
            }
        }
        
        function resumeStopVideoClip() {
            if (!clipEditPlayerHTML.paused) {
                clipEditPlayerHTML.pause();
            } else {
                // verifico se devo tornare alla prima clip
                if (indexActionToPlay === (selectedActions.length - 1)) {
                    var params = $("#startData" + selectedActions[indexActionToPlay]).val().split("||");
                    if (clipEditPlayerHTML.currentTime > parseInt(params[1])) { // se il tempo attuale � maggiore della fine dell'ultima clip
                        startAction('', 0);
                    }
                }
                clipEditPlayerHTML.play();
            }
            checkPlayButtonClip();
        }
        
        function checkPlayButtonClip() {
            if (clipEditPlayerHTML.paused) {
                $("#resumeStopButton").attr("title", "<spring:message code='video.play'/>");
                $("#resumeStopButton i").attr("class", "uk-icon-play");
            } else {
                $("#resumeStopButton").attr("title", "<spring:message code='video.stop'/>");
                $("#resumeStopButton i").attr("class", "uk-icon-stop");
            }
        }
        
        function updateClipEdit(id) {
            var playlistId = $("#mPlaylistTvId").val();
            var note = $("#noteClipModify").val();
            var clipStartVariation = parseFloat(startAzione) - parseFloat(seekSliderEdit.dataset.initVideo);
            var clipEndVariation = parseFloat(seekSliderEdit.dataset.endVideo) - parseFloat(endAzione);
            if (typeof playlistId !== 'undefined' && typeof note !== 'undefined') {
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/updateClipTv.htm",
                    cache: false,
                    data: encodeURI("&playlistId=" + playlistId + "&eventId=" + id + "&note=" + note + "&startSecVariation=" + clipStartVariation + "&endSecVariation=" + clipEndVariation),
                    success: function (msg) {
                        if (msg === "tooLong") {
                            UIkit.notify("<spring:message code="video.record.error.too.long"/>", {status: 'danger', timeout: 1000});
                        } else {
                            sessionStorage['loadEventIndex'] = true;
                            window.location.reload();
                        }
                    },
                    error: function () {
                        UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
                    }
                });
            } else {
                UIkit.notify("<spring:message code="menu.user.errorUpdatingPlaylist"/>", {status: 'danger', timeout: 1000});
            }
        }
    </script>
    
    <input type="hidden" id="mVideopath" value="${mVideoPath}"/>
    <input type="hidden" id="mSecStart" value="${mAzione.isTactical != null && mAzione.isTactical ? mAzione.getSecTactStart() : mAzione.getSecStartAzione()}"/>
    <input type="hidden" id="mSecEnd" value="${mAzione.isTactical != null && mAzione.isTactical ? (mAzione.getSecTactStart() + (mAzione.getSecEndAzione() - mAzione.getSecStartAzione())) : mAzione.getSecEndAzione()}"/>
    <input type="hidden" id="mPlaylistTvId" value="${mPlaylistId}"/>
    
    <span class="modalClose" onclick="closeModifyClip();">&times;</span>
    <video id="clipEditPlayerHTML" class="uk-width-1-1" data-setup="{}" preload muted autoplay playsinline controls controlsList="nodownload" style='background: #000; margin-top: 15px'>
    </video>
    <div class="video-player-tools uk-hidden-small uk-hidden-medium" id="videoPlayerTools">
        <button onclick="modifyClipStart(-1);" class="uk-button uk-button-small uk-margin-small-left uk-margin-small-top uk-margin-small-bottom uk-hidden-small" title="<spring:message code='video.aggiungi.uno'/>"><i class="uk-icon-angle-double-left"></i></button>
        <button onclick="cutClipStart();" class="uk-button uk-button-small uk-margin-small uk-hidden-small" title="<spring:message code='video.imposta.inizio'/>">|</button>
        <button onclick="modifyClipStart(1);" class="uk-button uk-button-small uk-margin-small uk-hidden-small" title="<spring:message code='video.togli.uno'/>"><i class="uk-icon-angle-double-right"></i></button>
        <button onclick="resumeStopVideoClip();" id="resumeStopButton" class="uk-button uk-button-small uk-margin-small uk-hidden-small" title="<spring:message code='video.stop'/>" style="min-width: 33px"><i class="uk-icon-stop"></i></button>
        <span class="clipDurationTimePassed" style="margin-left: 5px">00:00</span>
        <input class="video-player-tools-child" data-initVideo="0" data-endVideo="0" type="range" id="seek-slider-edit-clip" min="0" step="1" value="0" onmousedown="stopEditVideo()" oninput="updateEditPlayerFromSeekSlider()" onchange="updateEditPlayerFromSeekSlider()" ontouchend="updateEditPlayerFromSeekSlider()">
        <span class="clipDurationFormatted">00:00</span>
        <button onclick="modifyClipEnd(-1);" class="uk-button uk-button-small uk-margin-small-left uk-margin-small-top uk-margin-small-bottom uk-hidden-small" title="<spring:message code='video.togli.uno'/>"><i class="uk-icon-angle-double-left"></i></button>
        <button onclick="cutClipEnd();" class="uk-button uk-button-small uk-margin-small uk-hidden-small" title="<spring:message code='video.imposta.fine'/>">|</button>
        <button onclick="modifyClipEnd(1);" class="uk-button uk-button-small uk-margin-small uk-hidden-small" title="<spring:message code='video.aggiungi.uno'/>"><i class="uk-icon-angle-double-right"></i></button>
    </div>
    <div class="uk-visible-small-and-medium">
        <div class="tagEvento video-player-tools" id="videoPlayerTools">
            <span class="clipDurationTimePassed">00:00</span>
            <input class="video-player-tools-child" data-initVideo="0" data-endVideo="0" type="range" id="seek-slider-edit-clip" min="0" step="1" value="0" onmousedown="stopEditVideo()" oninput="updateEditPlayerFromSeekSlider()" onchange="updateEditPlayerFromSeekSlider()" ontouchend="updateEditPlayerFromSeekSlider()">
            <span class="clipDurationFormatted">00:00</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
            <div>
                <button onclick="modifyClipStart(-1);" class="uk-button uk-button-small uk-margin-small-left uk-margin-small-top uk-margin-small-bottom" title="<spring:message code='video.aggiungi.uno'/>"><i class="uk-icon-angle-double-left"></i></button>
                <button onclick="cutClipStart();" class="uk-button uk-button-small uk-margin-small" title="<spring:message code='video.imposta.inizio'/>">|</button>
                <button onclick="modifyClipStart(1);" class="uk-button uk-button-small uk-margin-small" title="<spring:message code='video.togli.uno'/>"><i class="uk-icon-angle-double-right"></i></button>
            </div>
            <div class="uk-text-right">
                <button onclick="modifyClipEnd(-1);" class="uk-button uk-button-small uk-margin-small-left uk-margin-small-top uk-margin-small-bottom" title="<spring:message code='video.togli.uno'/>"><i class="uk-icon-angle-double-left"></i></button>
                <button onclick="cutClipEnd();" class="uk-button uk-button-small uk-margin-small" title="<spring:message code='video.imposta.fine'/>">|</button>
                <button onclick="modifyClipEnd(1);" class="uk-button uk-button-small uk-margin-small" title="<spring:message code='video.aggiungi.uno'/>"><i class="uk-icon-angle-double-right"></i></button>
            </div>
        </div>
    </div>
    
    <div class="uk-margin uk-modal-content uk-width-1-1">
        <label class="uk-text-bold uk-text-large" for="noteClipModify"><spring:message code="video.note"/>:</label>
    </div>
    <div class="uk-margin uk-modal-content uk-width-1-1">
        <input id="noteClipModify" class="uk-input uk-width-1-1 uk-form-large" type="text" aria-label="Input" maxlength="255" value="${mEvent.event_note}">
    </div>

    <div class="uk-margin uk-modal-content uk-width-1-1 uk-text-right">
        <button id="cancelModifyClip" style="min-width:80px;" onclick="closeModifyClip();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
        <button style="min-width:80px;" onclick="updateClipEdit(${mEvent.eventId});" class="uk-text-large uk-text-bold uk-button js-modal-confirm"><spring:message code="playlist.save"/></button>
    </div>
</div>