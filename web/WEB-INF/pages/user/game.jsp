<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

        <script typt="text/javascript" src="/sicstv/uikit/js/components/notify.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script type="text/javascript" src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" ></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css">
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        <script type="text/javascript">
            function closeDelete() {
                $("#menuDelete").removeClass("uk-hidden");
                $("#modalDeleteConfirm").removeClass("uk-open");
                $("#modalDeleteConfirm").addClass("uk-hidden");
                $("#modalDeleteConfirmText").html("");
            }

            function jsDownloadGame(id) {
                var strUrl = "/sicstv/user/download.htm?id=" + id + "&type=0";
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "true") {
                            UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                            window.location.replace(msg.substr(5));
                        }
                    }
                });
            }

            function askDelete() {
                $("#menuDelete").addClass("uk-hidden");
                $("#modalDeleteConfirmText").html('<spring:message code="menu.deleting"/>:<br>' + 1 + ' <spring:message code="menu.user.incontri"/><br><spring:message code="menu.areyousure"/>');
                $("#modalDeleteConfirm").removeClass("uk-hidden");
                $("#modalDeleteConfirm").addClass("uk-open");
            }

            function shareLinkGame(id) {
                const fallbackCopyTextToClipboardGame = str => {
                    var textArea = document.createElement("textarea");
                    textArea.value = str;

                    // Avoid scrolling to bottom
                    textArea.style.top = "0";
                    textArea.style.left = "0";
                    textArea.style.position = "fixed";

                    document.body.appendChild(textArea);
                    textArea.focus();
                    textArea.select();

                    try {
                        var successful = document.execCommand('copy');
                        var msg = successful ? 'successful' : 'unsuccessful';
                        UIkit.notify("<spring:message code="menu.user.linkCopiato"/>", {status: 'success', timeout: 1000});
                    } catch (err) {
                        UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger', timeout: 1000});
                        return Promise.reject('The Clipboard API is not available.');
                    }
                    document.body.removeChild(textArea);
                }

                const copyToClipboardGame = str => {
                    if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
                        UIkit.notify("<spring:message code="menu.user.linkCopiato"/>", {status: 'success', timeout: 1000});
                        return navigator.clipboard.writeText(str);
                    } else {
                        fallbackCopyTextToClipboardGame(str);
                    }
                    //UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger'});
                    //return Promise.reject('The Clipboard API is not available.');
                };
                var strUrl = "/sicstv/user/shareLinkGame.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        console.log(msg);
                        if (msg.substr(0, 5) === "false") {
                            UIkit.notify("<spring:message code="menu.user.errorSharing"/>", {status: 'danger', timeout: 1000});
                        } else {
                            //COPIA msg negli appunti
                            copyToClipboardGame(msg);
                        }
                    },
                    async: false
                });
            }
            function deletePlaylistAndGameSelected() {
                var strUrl = "/sicstv/user/deleteGamesAndPlaylists.htm?idPlaylists=&idGames=" + ${mGame.getIdFixture()};
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "false") {
                            UIkit.notify("<spring:message code="menu.user.errorDeleting"/>", {status: 'danger', timeout: 1000});
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            UIkit.notify(1 + " <spring:message code="menu.user.gameDeleted"/>", {status: 'success', timeout: 1000});
                            setTimeout(() => {
                                window.location.replace("/sicstv/user/mysicstv.htm");
                            }, 1000);
                        }
                    }
                });
                $("#menuDelete").removeClass("uk-hidden");
                closeDelete();
            }
        </script>
    </head>
    <body style="background:#F5F5F5;">
        <input type="hidden" value="${mGame.homeTeam} - ${mGame.awayTeam}" id="gameName"/>
        <input type="hidden" value="${mGame.homeTeam}-${mGame.awayTeam}||${mGame.competitionName}||${mGame.getDateDayString()} ${mGame.getDateMonthString()} ${mGame.getDateYearString()}" id="gameDetails"/>
        <div id="videoContainer" class="uk-width-1-1">
            <!--button id='btnCinema' class="uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small" onclick="toggleTheatreMode();" title="<spring:message code='tooltip.cinema'/>">
                <i class="uk-icon-desktop"></i>
            </button-->
            <video id="media_player1" class='uk-width-1-1' data-setup='{}' autoplay controls>
                <source src="${mGame.getVideoPathS3()}" type='video/mp4'/>
            </video>
        </div>
        <!--ul id="matchDataTab" class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#bottomNavContent'}">
            <li><a href="#"  class="uk-active"><spring:message code="playlist.informazioni"/></a></li>
        </ul-->
        <div id="menuDelete" class="uk-navbar orangecolor" style="vertical-align:middle;">
            <c:if test="${mGame.isCanDelete()}">
                <button style="min-width:50px;" id="btnDeletePlGm" class="uk-button uk-button-danger uk-margin-all uk-float-right" onclick="askDelete();">
                    <i class="uk-icon-medium uk-icon-trash"></i>
                    <span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.user.delete"/></span>
                </button>
            </c:if>
            <button style="min-width:50px;" id="btnDownloadPlGm" class="uk-button uk-margin-all uk-float-right" onclick="jsDownloadGame('${mGame.getIdFixture()}');">
                <i class="uk-icon-medium uk-icon-download"></i>
                <span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.user.download"/></span>
            </button>
            <button style="min-width:50px;" id="btnSharePlGm" class="uk-button uk-margin-all uk-float-right" onclick="shareLinkGame('${mGame.getIdFixture()}');">
                <i class="uk-icon-medium uk-icon-share"></i>
                <span class="uk-visible-large uk-margin-small-left"><spring:message code="menu.share"/></span>
            </button>
        </div>
        <div id="playlistInfo" class="uk-margin-bottom uk-width-1-1 uk-text-truncate">
            <div class="uk-margin-left uk-margin-right uk-width-1-1" style="text-transform: uppercase;">
                <div id="infoTable">
                    <div class="uk-margin-top" style="height: 30px;vertical-align: middle;">
                        <div class="uk-text-bold"><spring:message code="game.descrizione" /></div>
                        <div><a class="headerSubnav uk-text-bold" href="/sicstv/user/video.htm?personal=true&id=&fixtureId=${mGame.idFixture}&idComp=${mGame.competitionId}&idTeam=&idPlayer=&goals=false&event=&filter=&limit=10000">${mGame.homeTeam} - ${mGame.awayTeam}</a>
                            <!--button class="uk-button uk-margin-large-left uk-text-bold uk-hidden-small" onclick="shareLinkGame(${mGame.idFixture})"><spring:message code="menu.share"/> - <spring:message code="game.playlist.shareLink"/></button-->
                        </div>
                    </div>
                    <div class="uk-margin-top" style="height: 30px;vertical-align: middle;">
                        <div class="uk-text-bold"><spring:message code="game.competition" /></div>
                        <div>${mGame.competitionName}</div>
                    </div>
                    <c:if test="${!mGame.getOwnerName().equals('')}">
                        <div class="uk-margin-top">
                            <div class="uk-text-bold">
                                <spring:message code="playlist.caricatoDa" />
                            </div>
                            <div>
                                ${mGame.getOwnerName()}
                            </div>
                        </div>
                    </c:if>
                    <div class="uk-margin-top" style="height: 30px;vertical-align: middle;">
                        <div class="uk-text-bold"><spring:message code="game.date" /></div>
                        <div>${mGame.getDateDayString()} ${mGame.getDateMonthString()} ${mGame.getDateYearString()}</div>
                    </div>
                    <div class="uk-margin-top" style="height: 30px;vertical-align: middle;">
                        <div class="uk-text-bold"><spring:message code="menu.type"/>:</div>
                        <div><spring:message code="video.partita" /></div>
                    </div>
                </div>
            </div>
        </div>
    </body>
</html>