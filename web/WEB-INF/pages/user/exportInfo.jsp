<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<c:if test="${mIsEmpty == null}">
    <script type="text/javascript">
        // START
        <c:forEach var="export" items="${mExports}">
            <c:if test="${export.expInfo != null && export.expInfo.playlist != null && export.expInfo.playlist.id != -1}">
                var strUrl${export.expInfo.playlist.id} = "/sicstv/user/downloadPlaylist.htm?id=${export.expInfo.playlist.id}";
                $.ajax({
                    type: "GET",
                    url: strUrl${export.expInfo.playlist.id},
                    contentType: "application/force-download",
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "true") {
                            UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                            window.location.replace(msg.substr(5));
                            $.unblockUI();
                        }
                    }
                });
            </c:if>
        </c:forEach>
        // END
    </script>
    
    <div style="width: 100%">
        <center><h3 style="font-weight: bold; text-transform: uppercase"><spring:message code='menu.user.export.lista'/></h3></center>
        <div>
            <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                <tbody>
                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                        <th style="width: 30%; font-size: 11px"><spring:message code='menu.user.export.nome'/></th>
                        <th style="width: 50%; font-size: 11px; text-align: center" colspan="2"><spring:message code='menu.user.export.avanzamento'/></th>
                        <th style="font-size: 11px"><spring:message code='menu.user.export.tempo.trascorso'/></th>
                        <th style="font-size: 11px"><spring:message code='menu.user.export.tempo.stimato'/></th>
                    </tr>
                    <c:forEach var="export" items="${mExports}">
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                            <td style="text-overflow: ellipsis; overflow: hidden; max-width: 100px; width: 100px">${export.getExportName()}</td>
                            <td style="width: 80%">
                                <div class="video-pre-progress-bar-container" style="text-align: center; width: 100%">
                                    <div class="video-pre-progress-bar" id="video-pre-bar" style="width: ${export.getProgressPercentageBar()}"></div>
                                    <div style="width: 100%; padding-top: 3px; margin-top: -20px" id="video-pre-bar-value">${export.getProgressPercentage()}</div>
                                </div>
                            </td>
                            <td>${export.getDoneOnTotal()}</td>
                            <td>${export.getElapsedTime()}</td>
                            <td>${export.getEstimatedTime()}</td>
                        </tr>
                    </c:forEach>
                </tbody>
            </table>
        </div>
    </div>
</c:if>