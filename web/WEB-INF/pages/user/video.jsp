<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%@ page trimDirectiveWhitespaces="true" %>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, user-scalable=no">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />

        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>

        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript" ></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript" ></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/toggle.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <script src="/sicstv/js/jquery.timer.js" type="text/javascript" ></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        
        <!-- Keep here: needed for teamStats.jsp for buttons (export) -->
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css"  >
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.0/css/all.min.css" rel="stylesheet"/>
        <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet"/>
        <link href="/sicstv/css/videojs.markers.min.css" rel="stylesheet"/>
        <script src="/sicstv/js/video.js"></script>
        <script src="/sicstv/js/videojs-offset.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/js/videojs-markers.min.js?<%=System.currentTimeMillis()%>"></script>
        <script src="https://cdn.jsdelivr.net/npm/videojs-overlay@3.1.0/dist/videojs-overlay.min.js"></script>
        <link href="https://cdn.jsdelivr.net/npm/videojs-overlay@3.1.0/dist/videojs-overlay.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Titillium+Web:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700&display=swap" rel="stylesheet">
        
        <script src="/sicstv/js/export-utils.js?<%=System.currentTimeMillis()%>"></script>
        
        <!-- D3.js Library for canvas/svg -->
        <script src="https://d3js.org/d3.v5.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.0/d3-tip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>
        <script src="/sicstv/js/sics-canvas.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/js/sics-canvas-shots.js?<%=System.currentTimeMillis()%>"></script>
        
        <style>
            @media print {
                body * {
                    visibility:hidden;
                }
            }
        </style>
        
        <script type="text/javascript" defer>
            // PRE CARICAMENTO PAGINA
            function moveProgressBar(newValue) {
                var elem = document.getElementById("video-pre-bar");
                var width = parseInt(elem.style.width.replace("%", ""));
                var id = setInterval(frame, 5);
                function frame() {
                    if (width === 100) {
                        clearInterval(id);
                        $("#preLoadDiv").addClass("animazioneFadeOut");
                        
                        // Opzionale: gestisci l'evento di completamento dell'animazione
                        var preLoadDiv = document.getElementById("preLoadDiv");
                        preLoadDiv.addEventListener("transitionend", function() {
                            $("#preLoadDiv").addClass("uk-hidden");
                            $("#centralGrid").removeClass("uk-hidden");
                        });
                    }
                    if (width >= 50) {
                        elem.style.color = "#ffffff";
                    } else {
                        elem.style.color = "#000000";
                    }
                    if (width >= newValue) {
                        clearInterval(id);
                    } else {
                        width++;
                        elem.style.width = width + '%';
                        $("#video-pre-bar-value").html(width + '%');
                    }
                }
            }
            
            function waitProgressBar() {
                setTimeout(function() {
                    var elem = document.getElementById("video-pre-bar");
                    if (elem === null) {
                        waitProgressBar();
                    } else {
                        // può capitare che non arrivi al 20% perchè ad un certo punto inizia il codice javascript
                        // e sembra bloccare tutte le modifiche della pagina
                        moveProgressBar(20);
                    }
                }, 100);
            }
            
            waitProgressBar();
            // PRE CARICAMENTO PAGINA
        </script>
        
        <script type="text/javascript">
            var isPageVideo = true;
            var personalSource = '${personalSource}';
            var pageStatus = [];
            var totMatches = [];
            var numMatches = [];
            var matchPerPage = 25;
            var ids = "";

            var openTag = false;
            var removeFilter = false;
            var eventSelected = [];
            var teamSelected = [];
            var playerSelected = [];
            var gamesSelected = [];
            var posizioniSelected = [];
            var angoliSelected = [];
            var posizioniPortaSelected = [];
            var ultimaOperazione = 0;
            var numFiltri = 0;
            var singleMatch = false;
            var highlights = ${not empty highlights};
            var ipo = ${not empty ipo};
            var curVideoPath = "", curVideoHDPath = "", curVideoTACTPath = "";
            var curVideo = "";

            var pageHomeCompetition = "home.htm";
            var playerHTML;
            var videoJsPlayer;
            var hdActive = false;
            var tactActive = false;
            var tactStartTime;
            var actionStartTime;
            var currentVideoDuration;
            var inizioAzione, fineAzione;

            var countRowVisible = 0;

            var filtredEvents = false;
            var playerFilter = false;
            var currentFilterEvent = "";
            var currentLimit = 1;

            var actionTable;
            
            var currentGameId;
            var clipDurationMap = new Map();
            var eventMap = new Map();
            var fixtureMap = new Map(), teamMap = new Map();
            var filteredRequest = false;
            var removeExactDuplicates = false;
            
            var pageTranslation = new Map();
            var videoOverlay;
            
            var teamColors = new Map();
            var teamTextColors = new Map();
            <c:forEach items="${mListPlayers.keySet()}" var="team" varStatus="status" >
                teamColors.set('${team.id}', '${team.color}');
                teamTextColors.set('${team.id}', '${team.textColor}');
            </c:forEach>

            $("#numEventsSmall").addClass("uk-hidden");            
            $(document).on("click", "button.vjs-fullscreen-control", function () {
                if (typeof videoJsPlayer !== "undefined") {
                    // per sicurezza aggiungo qualche controllo
                    videoJsPlayer.focus();
                }
            })
            
            document.addEventListener("keydown", function(event) {
                // Verifica se il tasto premuto è la barra spaziatrice (codice 32)
                if (event.keyCode === 32) {
                    // Verifica se l'elemento attivo è un campo di input
                    const activeElement = document.activeElement;
                    if (activeElement.tagName === "INPUT" && (activeElement.type === "text" || activeElement.type === "password" || activeElement.type === "search")) {
                        // Se siamo in un campo di input, lascia che la barra spaziatrice venga gestita normalmente
                        return;
                    }
                    
                    event.preventDefault();
                    if (videoJsPlayer !== null && typeof videoJsPlayer !== 'undefined') {
                        resumeStopVideo();
                    }
                } else if (event.keyCode === 37) { // freccia sinistra
                    // Verifica se l'elemento attivo è un campo di input
                    const activeElement = document.activeElement;
                    if (activeElement.tagName === "INPUT" && (activeElement.type === "text" || activeElement.type === "password" || activeElement.type === "search")) {
                        // Se siamo in un campo di input, lascia che la barra spaziatrice venga gestita normalmente
                        return;
                    }
                    
                    event.preventDefault();
                    if (videoJsPlayer !== null && typeof videoJsPlayer !== 'undefined') {
                        if (videoJsPlayer.currentTime() <= 5) {
                            videoJsPlayer.currentTime(0);
                        } else {
                            videoJsPlayer.currentTime(videoJsPlayer.currentTime() - 5);
                        }
                    }
                } else if (event.keyCode === 39) { // freccia destra
                    // Verifica se l'elemento attivo è un campo di input
                    const activeElement = document.activeElement;
                    if (activeElement.tagName === "INPUT" && (activeElement.type === "text" || activeElement.type === "password" || activeElement.type === "search")) {
                        // Se siamo in un campo di input, lascia che la barra spaziatrice venga gestita normalmente
                        return;
                    }
                    
                    event.preventDefault();
                    if (videoJsPlayer !== null && typeof videoJsPlayer !== 'undefined') {
                        if ((videoJsPlayer.currentTime() + 5) <= currentVideoDuration) {
                            videoJsPlayer.currentTime(videoJsPlayer.currentTime() + 5);
                        } else {
                            videoJsPlayer.currentTime(currentVideoDuration);
                        }
                    }
                } else if (event.keyCode === 38) { // freccia su
                    // Verifica se l'elemento attivo è un campo di input
                    const activeElement = document.activeElement;
                    if (activeElement.tagName === "INPUT" && (activeElement.type === "text" || activeElement.type === "password" || activeElement.type === "search")) {
                        // Se siamo in un campo di input, lascia che la barra spaziatrice venga gestita normalmente
                        return;
                    }
                    
                    event.preventDefault();
                    if (videoJsPlayer !== null && typeof videoJsPlayer !== 'undefined') {
                        changeAction(false);
                    }
                } else if (event.keyCode === 40) { // freccia giu
                    // Verifica se l'elemento attivo è un campo di input
                    const activeElement = document.activeElement;
                    if (activeElement.tagName === "INPUT" && (activeElement.type === "text" || activeElement.type === "password" || activeElement.type === "search")) {
                        // Se siamo in un campo di input, lascia che la barra spaziatrice venga gestita normalmente
                        return;
                    }
                    
                    event.preventDefault();
                    if (videoJsPlayer !== null && typeof videoJsPlayer !== 'undefined') {
                        changeAction(true);
                    }
                } else if (event.keyCode === 88) { // tasto X
                    // Verifica se l'elemento attivo è un campo di input
                    const activeElement = document.activeElement;
                    if (activeElement.tagName === "INPUT" && (activeElement.type === "text" || activeElement.type === "password" || activeElement.type === "search")) {
                        // Se siamo in un campo di input, lascia che la barra spaziatrice venga gestita normalmente
                        return;
                    }
                    
                    if (typeof $("#increaseActionEnd").attr("disabled") === 'undefined') { // se il tasto è attivo
                        increaseClipEnd();
                    }
                } else if (event.keyCode === 90) { // tasto Z
                    // Verifica se l'elemento attivo è un campo di input
                    const activeElement = document.activeElement;
                    if (activeElement.tagName === "INPUT" && (activeElement.type === "text" || activeElement.type === "password" || activeElement.type === "search")) {
                        // Se siamo in un campo di input, lascia che la barra spaziatrice venga gestita normalmente
                        return;
                    }
                    
                    if (typeof $("#increaseActionStart").attr("disabled") === 'undefined') { // se il tasto è attivo
                        increaseClipStart();
                    }
                }
            });

            var isMobile = false; //initiate as false
            // device detection
            if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent)
                    || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substr(0, 4))) {
                isMobile = true;
            }

            // Define some vars required later
            var videoWidth, videoHeight, videoRatio;
            var isPlaylistTv = false;
            var tacticalEventIds = [];
            var atLeastOne3DShot = false;
            
            $(document).ready(function () {
                //moveProgressBar(20);                
                pageTranslation.set("redCircle", "<spring:message code='menu.posizionale.shot.parato'/>");
                pageTranslation.set("yellowCircle", "<spring:message code='menu.posizionale.shot.fuori'/>");
                pageTranslation.set("greenCircle", "<spring:message code='menu.posizionale.shot.rete'/>");
                pageTranslation.set("blockedShots", "<spring:message code='menu.posizionale.shot.blocked'/>");
                
                pageTranslation.set("positional.modality.half", "<spring:message code="positional.modality.half"/>");
                pageTranslation.set("positional.modality.zone", "<spring:message code="positional.modality.zone"/>");
                pageTranslation.set("positional.modality.channel", "<spring:message code="positional.modality.channel"/>");
                pageTranslation.set("positional.modality.zone.channel", "<spring:message code="positional.modality.zone.channel"/>");
                pageTranslation.set("positional.modality.area", "<spring:message code="positional.modality.area"/>");
                
                <c:forEach var="eventId" items="${mTacticalEventIdList}">
                    tacticalEventIds.push('${eventId}');
                </c:forEach>
                <c:forEach var="list" items="${mAllEvents}">
                    <c:forEach var="item" items="${list.value}">
                        clipDurationMap.set('${item.id}', "${item.getDurataAzioneMinSec(false)}");
                        eventMap.set('${item.id}', eval(${item.getJsonForVideo(mLanguage)}));
                    </c:forEach>
                </c:forEach>
                <c:forEach var="item" items="${mGame.keySet()}">
                    fixtureMap.set(${item}, eval(${mGame.get(item).getJsonForVideo()}));
                </c:forEach>
                <c:forEach items="${mListPlayers.keySet()}" var="team" varStatus="status" >
                    teamMap.set(${team.id}, eval(${team.getJson()}));
                </c:forEach>
                if ($("#mPlaylistTvId").val() !== '') {
                    isPlaylistTv = true;
                }
                if ('${personalSource}' == 'true') {
                    pageHomeCompetition = "mycompetition.htm";
                }
                // messaggio errore 
                $('#azioni').on('error.dt', function (e, settings, techNote, message) {
                    console.log('DataTables error: ', message);
                });
                jsRefreshMatches('1');
                actionTable = $('#azioni').DataTable({
                    "data": eval(${mTableData}),
                    "columns": [
                        {
                            // Checkbox
                            data: "1",
                            render: function (data) {
                                return '<label for="checkbox' + data + '"><input class="checkbox-big skip-start-action" type="checkbox" id="checkbox' + data + '" onclick="jsSelectAction(\'' + data + '\');" value="' + data + '"/><label for="checkbox' + data + '"></label></label>';
                            },
                            createdCell: function (td, cellData, rowData, row, col) {
                                $(td).addClass('skip-start-action uk-text-center');
                            }
                        },
                        {
                            // Description
                            data: "4",
                            render: function (data, type, row) {
                                var event = row["4"];
                                var tags = row["5"];
                                var notes = row["6"];
                                return '<div class="descrBox"><span class="uk-float-left">' + event + '<br/><span class="tag">' + tags + '</span><span class="tag">' + notes + '</span></span></div>';
                            },
                            createdCell: function (td, cellData, rowData, row, col) {
                                $(td).css('width', '250px');
                            }
                        },
                        {
                            // Half
                            data: "7",
                            createdCell: function (td, cellData, rowData, row, col) {
                                $(td).addClass('uk-text-center');
                            }
                        },
                        {
                            // Start Action
                            data: "8",
                            createdCell: function (td, cellData, rowData, row, col) {
                                $(td).addClass('uk-text-center');
                            }
                        },
                        {
                            // Duration
                            data: "9",
                            createdCell: function (td, cellData, rowData, row, col) {
                                $(td).addClass('uk-text-center');
                            }
                        },
                        {
                            // Team
                            data: "10",
                            createdCell: function (td, cellData, rowData, row, col) {
                                $(td).addClass('uk-text-center');
                            }
                        },
                        <c:if test="${fn:length(mEventFilter) > 0 || mGame.size() > 1}">
                        {
                            // Match
                            data: "11",
                            render: function (data, type, row) {
                                var match = row["11"];
                                var matchDate = row["12"];
                                var matchDay = row["13"];
                                return match + '<br/><span class="tag">' + matchDate + ', Matchday: ' + matchDay + '</span>';
                            },
                            createdCell: function (td, cellData, rowData, row, col) {
                                $(td).css('width', '200px');
                            }
                        },
                        </c:if>
                        {
                            // Players
                            data: "14"
                        },
                        {
                            // Result
                            data: "15",
                            createdCell: function (td, cellData, rowData, row, col) {
                                $(td).addClass('uk-text-center');
                            }
                        }
                        <c:if test="${showAuthor}">
                        ,{
                            // User
                            data: "16",
                            createdCell: function (td, cellData, rowData, row, col) {
                                $(td).css('width', '100px');
                            }
                        }
                        </c:if>
                    ],
                    "createdRow": function (row, data, index) {
                        // Add attributes to the <tr> dynamically
                        const rowId = data[1]; // Use the first column as the unique identifier
                        const overlayText = data[2]; // Use the second column for the overlay text

                        // Add ID attribute
                        $(row).attr('id', 'rowEventId' + rowId);

                        // Add overlay-text attribute
                        $(row).attr('overlay-text', overlayText);

                        // Classes
                        $(row).attr('class', 'uk-table-middle uk-margin-small-bottom uk-margin-small-top');

                        // Add onclick event
                        $(row).attr('onclick', 'startAction("' + rowId + '", ' + data[3] + ', false);');
                    },
                    "columnDefs": [
                        {type: 'date-euro', targets: 1}
                    ],
                    "dom": 'lBfrtip',
                    "buttons": [
                        {
                            text: '<spring:message code='team.stats.esporta'/>',
                            attr: {
                                class: 'uk-button uk-hidden',
                                id: 'actionTableExportButton'
                            },
                            extend: 'collection',
                            buttons: [
                                {
                                    extend: 'excelHtml5',
                                    exportOptions: {
                                        columns: ':visible'
                                    },
                                    title: 'SICS.tv-Events'
                                }, {
                                    extend: 'csv',
                                    exportOptions: {
                                        columns: ':visible'
                                    },
                                    title: 'SICS.tv-Events'
                                }
                            ]
                        }
                    ],
                    "paging": true,
                    "lengthChange": true,
                    "pageLength": 15,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "ext.errMode": "none",
                    "order": [[0, "asc"]],
                    "responsive": true,
                    "lengthMenu" : [10, 15, 25, 50, 100]
                });
                $('#azioni').removeClass("uk-hidden");
                
                actionTable.on('draw.dt', function() {
                    //console.log("going to show id " + indexActionToPlay);
                    //$("#infoSearch" + selectedActions[indexActionToPlay] + " + i").removeClass("uk-hidden");
                    var actionsArray = [];
                    if (isRunningOnChecked) {
                        actionsArray = checkedActions;
                    } else {
                        actionsArray = selectedActions;
                    }
                    if (indexActionToPlay >= 0 && actionsArray.length > 0) {
                        //checkIfNeededToChangePage();
                        $('#rowEventId' + actionsArray[indexActionToPlay] + ' td').addClass("currentClip");
                        highlightElementById("" + actionsArray[indexActionToPlay]);
                    }

                    // sistemo le azioni con il check
                    if (checkedActions.length > 0) {
                        checkedActions.forEach(function (id) {
                            $("#checkbox" + id).attr("checked", "checked");
                        });
                    }
                });

                //moveProgressBar(60);
                singleMatch = ${fn:length(mGame)==1};
                filtredEvents = ${fn:length(mEventFilter)>0};
                playerFilter = ${mPlayer.id > 0};
                // $("#quickChoiceSpan").remove();
                if (filtredEvents) {
                    currentFilterEvent = '${mEventFilter}';
                    currentLimit = ${mCurrentLimit};
                    
                    if (!playerFilter ||
                            (!currentFilterEvent.startsWith('SICS2015-_-TIF')
                            && currentFilterEvent !== 'SICS2015-_-RTF'
                            )) {
                        $("#inCareerButton").remove();
                    }

                    $("#liRicerca").remove();
                    $("#liRicercaContent").remove();
                    if (singleMatch) {
                        $("#liSelezionate").remove();
                        $("#liSelezionateContent").remove();
                        $("#selectedMatches").remove();
                    }
                    $("#timeFirstButton").remove();
                    $("#timeSecondButton").remove();
                    $("#timeFirstQuarter").remove();
                    $("#timeSecondQuarter").remove();
                    $("#timeThirdQuarter").remove();
                    $("#timeFourthQuarter").remove();
                    $("#liCalendario").remove();
                    $("#calendario").remove();

                    $("#liFiltri").addClass("uk-active");
                    $("#liFiltriContent").addClass("uk-active");
                } else if (singleMatch) {
                    $("#liSelezionate").remove();
                    $("#liSelezionateContent").remove();
                    $("#selectedMatches").remove();
                    $("#inCareerButton").remove();
                } else {
                    $("#liCalendario").remove();
                    $("#liInfoMatch").remove();
                    $("#calendario").remove();
                    $("#matchData").remove();
                    $("#inCareerButton").remove();
                    var idPl = $("#mPlayerId").val();
                    if (typeof idPl !== 'undefined') {
                        jsSetButtonSelected('playerButton' + idPl, 3);
                    }
                }
                if (${empty filterToShow} || ${empty filterToShow['SICS'][0]}) {
                    $("#liRicerca").remove();
                    $("#liRicercaContent").remove();
                    if (${empty mButtonToShow}) {
                        $("#liFiltri").remove();
                        $("#liFiltriContent").remove();
                        $("#liFiltriTactical").remove();
                        $("#liFiltriTacticalContent").remove();
                        // serve per clip personali
                        $("#liFiltroGiocatori").addClass("uk-hidden");
                        $("#liFiltroGiocatoriContent").addClass("uk-hidden");
                        $("#btnFiltriRapidi").remove();
                        $("#liEventiSmall").remove();
                        $("#liEventiSmallContent").remove();
                        $("#doExportAction").remove();
                        $("#doExportActionExcel").remove();
                        $("#removeDuplicatesButton").remove();
                        $("#videoPlayerTools").children().not("#record-button").remove();
                        
                        if (${empty mAllEvents}) {
                            $("#liActions").remove();
                            $("#eventi").remove();
                            $("#liMatches").addClass("uk-active");
                            $("#partite").addClass("uk-active");
                        }
                    } else {
                        $("#liFiltri").addClass("uk-active");
                        $("#liFiltriContent").addClass("uk-active");
                        $("#sourceSics").remove();
                        $("#sourcePersonal").remove();
                    }
                }
                if (${mUser.isPlayerUser()}) {
                    $("#liMatches").remove();
                    $("#partite").remove();
                    $("#liCalendario").remove();
                    $("#calendario").remove();
                }
                initPageVideo();
                //moveProgressBar(80);
//                jsComponentsView();
                initSearchFilter();
                $("#bottomNav").removeClass("uk-hidden");
                if (isMobile || iOS()) {
                    hdActive = false;
                }
                //console.log(personalSource);
                if (personalSource === 'true') {
                    $("#sourceSics").remove();
                    $("#sourcePersonal").remove();
                }
                
                // verifico se sono entrato filtrato in pagina
                // i filtri possono essere: multipartita, team, player, evento
                filteredRequest = ${filteredRequest};
                drawField("posizionaleContainer");
                drawShotField("posizionaleShotsContainer");
                handleResize();
                if (filteredRequest) {
                    $("#closeClipViewer").remove();
                    $("#closeClipViewer2").remove();
                    drawBackgroundArrow(pitch.baseLayer, pitch);
                } else {
                    drawEvents();   // mostro scritta "please choose a filter"
                }
                
                if (highlights || (${not empty usr} && ${fn:length(usr)>0}) || (${not empty psw} && ${fn:length(psw)>0}) || isPlaylistTv) {
                    $("#comboTeamSelection").remove();
                    $("#liMatches").remove();
                    $("#partite").remove();
                    $("#liCalendario").remove();
                    $("#calendario").remove();
                    $("#btnSearch").remove();
                    $("#quickChoiceSpan").addClass("uk-hidden");
                    $("#sourceSics").remove();
                    $("#sourcePersonal").remove();
                    $("#liRicerca").remove();
                    $("#liFiltri").remove();
                    $("#liFiltriTactical").remove();
                    // serve per clip personali
                    $("#liFiltroGiocatori").addClass("uk-hidden");
                    $("#liFiltroGiocatoriContent").addClass("uk-hidden");
                    $("#liFiltriContent").remove();
                    $("#liRicercaContent").remove();
                    $("#liFiltriTacticalContent").remove();
                    $("#liEventiSmall").attr("aria-expanded", "true");
                    $("#liEventiSmall").addClass("uk-active");
                    $("#liEventiSmallContent").addClass("uk-active");
                    $("#liEventiSmallContent").attr("aria-expanded", "true");
                    $("#liEventiSmallContent").attr("aria-hidden", "false");
                }
                if (${not empty matchPlayerId}) {
                    // se filtrato per giocatore tolgo alcune cose che non bisogna mostrare
                    // serve per clip personali
                    $("#liFiltroGiocatori").addClass("uk-hidden");
                    $("#liFiltroGiocatoriContent").addClass("uk-hidden");
                    $("#quickChoiceSpan").addClass("uk-hidden");
                    
                    // attivo di default la rimozione "smart" dei duplicati
                    manageRemoveExactDuplicates();
                }
                if (${not empty fromTeamId}) {
                    $("#quickChoiceSpan").remove();
                }
                
                updateSelectedActionsArray(true);
                var removeDuplicates = new URL(document.URL).searchParams.get("removeDuplicates");
                if (typeof removeDuplicates !== 'undefined' && removeDuplicates !== null) {
                    // $("#bottomNavTab").addClass("uk-hidden");
                    // $("#bottomNavContent").addClass("uk-hidden");
                    applyFilterEvents();
                    updateSelectedActionsArray(false);
                    // startAction('', 0, isRunningOnChecked);
                } else if ($('#mEventPresent').val() === 'true' || $('#mSer').val() !== '' || isPlaylistTv || highlights || filteredRequest) { // se arrivo filtrato mostro le clip
                    startAction('', 0, isRunningOnChecked);
                }
                
                // screenshot function stuff		
		// Add a listener to wait for the 'loadedmetadata' state so the video's dimensions can be read
		playerHTML.addEventListener('loadedmetadata', function() {
                    // Calculate the ratio of the video's width to height
                    videoRatio = playerHTML.videoWidth / playerHTML.videoHeight;
                    // Define the required width as 100 pixels smaller than the actual video's width
                    videoWidth = playerHTML.videoWidth - 100;
                    // Calculate the height based on the video's width and the ratio
                    videoHeight = parseInt(videoWidth / videoRatio, 10);
                    
                    currentVideoDuration = playerHTML.duration;
		}, false);
                
                if (isPlaylistTv) {
                    playerHTML.addEventListener("click", (event) => {
                        if (event.detail === 1) { // 1 click
                            resumeStopVideo();
                        } else if (event.detail === 2) {
                            if (document.fullscreenElement !== null) { // sono in fullscreen
                                playerHTML.webkitExitFullScreen();
                            } else {
                                playerHTML.requestFullscreen();
                            }
                            if (playerHTML.paused) {
                                playerHTML.play();
                            }
                        }
                    });
                    
                    $("#timeFirstButton").addClass("uk-hidden");
                    $("#timeSecondButton").addClass("uk-hidden");
                    $("#btnResetFiltri").addClass("uk-hidden");
                    $("#closeClipViewer").addClass("uk-hidden");
                }
                
                if (isPlaylistTv || filtredEvents) {
                    $("#liSelezionate").click(); // mostro il toolbar delle partite selezionate
                }
                
                // se riordino la Datatable devo anche riordinare l'array delle azioni
                actionTable.on('order.dt', function() {
                    var actionsArray = [];
                    if (isRunningOnChecked) {
                        actionsArray = checkedActions;
                    } else {
                        actionsArray = selectedActions;
                    }
                    
                    var previousEventId = actionsArray[indexActionToPlay];
                    updateSelectedActionsArray(false);
                    if (isRunningOnChecked) {
                        actionsArray = checkedActions;
                    } else {
                        actionsArray = selectedActions;
                    }
                    if (actionsArray.indexOf(previousEventId) != -1) {
                        indexActionToPlay = actionsArray.indexOf(previousEventId);
                        
                        $('.currentClip').removeClass("currentClip");
                        $('#rowEventId' + actionsArray[indexActionToPlay] + ' td').addClass("currentClip");
                        highlightElementById("" + actionsArray[indexActionToPlay]);
                    }
                    verifyNextAndPreviousButton();
                });
                
                var eventAmountMap = new Map();
                actionTable.rows().every(function (rowIdx, tableLoop, rowLoop) {
                    var eventId = $(this.node()).attr("id").replace("rowEventId", "");
                    var eventData = eventMap.get("" + eventId);
                    
                    var tmpActionType = eventData.config.replace(".xml", "");
                    tmpActionType += "_" + eventData.button.cod;
                    var tags = "";
                    $.each(eventData.mTags, function (index, tag) {
                        if (tags) tags += ",";
                        tags += tag.code;
                    });
                    if (tags) {
                        tmpActionType += "||" + tags;
                    }
                    
                    var eventList = tmpActionType.substring(tmpActionType.indexOf("||") + 2);
                    var eventListSplitted = eventList.split(",");
                    $.each(eventListSplitted, function (indexP, valueP) {
                        if (valueP) {
                            if (typeof eventAmountMap.get(valueP) === 'undefined') {
                                eventAmountMap.set(valueP, 1);
                            } else {
                                eventAmountMap.set(valueP, (eventAmountMap.get(valueP) + 1));
                            }
                        }
                    });
                });
                
                $("#listAzioneRicerca").find(".width-1-2-x").find(".tagEvento").find(".buttonTag").find(".uk-margin-right").each(function() {
                    var id = $(this).attr("id");
                    var splitted = id.split("_");
                    id = splitted[splitted.length - 1];
                    if (id.includes("-GEN")) {
                        id = splitted[0].substring(1) + "_" + id.replace("-GEN", ""); // ICS2015_ATT
                    }

                    var label = $("label[for='" + $(this).attr('id') + "']");
                    var labelHtmlContent = label.html();
                    if (typeof eventAmountMap.get(id) !== 'undefined') {
                        label.html(labelHtmlContent + " (" + eventAmountMap.get(id) + ")");
                        
                        var isDisabled = $(this).attr("disabled")
                        if (typeof isDisabled !== "undefined") {
                            $(this).removeAttr("disabled");
                        }
                    } else {
                        label.html(labelHtmlContent + " (0)");
                    }
                });
                $("#liFiltriTacticalContent").find(".width-1-2-x").find(".tagEvento").find(".buttonTag").find(".uk-margin-right").each(function() {
                    var id = $(this).attr("id");
                    var splitted = id.split("_");
                    id = splitted[splitted.length - 1];
                    if (id.includes("-GEN")) {
                        id = splitted[0].substring(1) + "_" + id.replace("-GEN", ""); // ICS2015_ATT
                    }

                    var label = $("label[for='" + $(this).attr('id') + "']");
                    var labelHtmlContent = label.html();
                    if (typeof eventAmountMap.get(id) !== 'undefined') {
                        label.html(labelHtmlContent + " (" + eventAmountMap.get(id) + ")");
                        
                        var isDisabled = $(this).attr("disabled")
                        if (typeof isDisabled !== "undefined") {
                            $(this).removeAttr("disabled");
                        }
                    } else {
                        label.html(labelHtmlContent + " (0)");
                    }
                });
                
                var removeDuplicates = new URL(document.URL).searchParams.get("removeDuplicates");
                if (typeof removeDuplicates !== 'undefined' && removeDuplicates !== null) {
                    $("#removeDuplicatesButton").addClass("uk-button-active");
                    $("#removeDuplicatesButton").attr("title", "<spring:message code='tooltip.separa.clip'/>");
                }
                var allSeasons = new URL(document.URL).searchParams.get("allSeasons");
                if (typeof allSeasons !== 'undefined' && allSeasons !== null) {
                    $("#inCareerButton").addClass("uk-button-active");
                }
                
                tippy('#btnInfo', {
                    theme: 'light',
                    content: '\
                    <div>\n\
                        <img src="/sicstv/images/keyboard_shortcuts.png"/><br/>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px; background-color: #008c22"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.shortcuts.video.space"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px; background-color: #aa0000"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.shortcuts.video.left.arrow"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px; background-color: #003aaa"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.shortcuts.video.right.arrow"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px; background-color: #aa5900"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.shortcuts.video.up.arrow"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px; background-color: #aa009d"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.shortcuts.video.down.arrow"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px; background-color: #aaa200"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.shortcuts.video.x.key"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px; background-color: #0098aa"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.shortcuts.video.z.key"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px; background-color: #494949"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.shortcuts.video.ctrl.1"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px; background-color: #494949"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.shortcuts.video.ctrl.2"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px; background-color: #494949"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.shortcuts.video.ctrl.3"/></div>\n\
                        </div>\n\
                    </div>\n\
                    ',
                    allowHTML: true,
                    placement: 'bottom',
                });
        
                tippy('#btnInfoPosizionale', {
                    theme: 'light',
                    content: '\
                    <div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 15px; height: 15px; background-color: #ffffff; border: 1px solid black"></div>\n\
                            <div style="margin-left: 5px"><spring:message code="menu.user.duelli.dribbling"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px;"><img src="/sicstv/images/triangle.png" style="margin-right: 2px"/></div>\n\
                            <div><spring:message code="menu.user.falli"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px;"><i class="uk-icon-arrow-right" style="color: #3672ff"></i></div>\n\
                            <div><spring:message code="menu.user.passaggi.riusciti"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px;"><i class="uk-icon-arrow-right" style="color: #ff4545"></i></div>\n\
                            <div><spring:message code="menu.user.tiri"/></div>\n\
                        </div>\n\
                        <div style="display: flex; padding-left: 5px; margin-top: 5px;">\n\
                            <div style="width: 20px; height: 20px;"><i class="uk-icon-arrow-right"></i></div>\n\
                            <div><spring:message code="menu.user.trasmissione"/></div>\n\
                        </div>\n\
                    </div>\n\
                    ',
                    allowHTML: true,
                    placement: 'bottom',
                });
        
                // per click su tasto "In carriera", tengo visualizzata la tab "Posizionale" se serve
                if (typeof localStorage["videoSelectedTab"] !== "undefined") {
                    $("#" + localStorage["videoSelectedTab"]).click();
                    localStorage.removeItem("videoSelectedTab");
                }
                
                // se non ho neanche un evento con le coordinate 3D, tolgo la tab del posizionale porta
                eventMap.forEach(function (event) {
                    if (event.mShot3D.isDefault === false) {
                        atLeastOne3DShot = true;
                        return;
                    }
                });
                if (!atLeastOne3DShot) {
                    $("#liPosizionaleShots").addClass("uk-hidden");
                }
                
                var currentExtraEvents = new URL(document.URL).searchParams.get("extraEvent");
                if (typeof currentExtraEvents !== 'undefined' && currentExtraEvents) {
                    var events = currentExtraEvents.split("|");
                    events.forEach(function (event) {
                        if (event.includes("#")) {
                            // tags
                            var tags = event.split("#")[1].split("@");
                            var eventType = event.split("#")[0].replace("SICS-_-", "");
                            tags.forEach(function (tag) {
                                $("#addEvent_" + eventType + "_" + tag).click();
                                $("#addEvent_" + eventType + "_" + tag).attr("checked", "checked");
                            });
                        } else {
                            // tutti i tag
                            var elementId = event.replace("SICS-_-", "").split("@")[0];
                            $("#addEvent_" + elementId).click();
                        }
                    });
                }
                var currentEvents = new URL(document.URL).searchParams.get("event");
                if (typeof currentEvents !== 'undefined' && currentEvents) {
                    var events = currentEvents.split("|");
                    events.forEach(function (event) {
                        if (event.includes("#")) {
                            // tags
                            var tags = event.split("#")[1].split("@");
                            var eventType = event.split("#")[0].replace("SICS-_-", "");
                            tags.forEach(function (tag) {
                                tag = tag.replace("{0}", "").replace("{1}", "").replace("{2}", "");
                                $("#addEvent_" + eventType + "_" + tag).attr("disabled", "disabled");
                            });
                        } else {
                            // tutti i tag
                            var elementId = event.replace("SICS-_-", "").split("@")[0];
                            $("#addEvent_" + elementId).attr("disabled", "disabled");
                        }
                    });
                } else {
                    $("#liAggiungiEvento").addClass("uk-hidden");
                    $("#liAggiungiStagione").addClass("uk-hidden");
                }
                
                // non ha senso mostrare la selezione delle stagioni se ho chiesto una partita specifica
                var currentFixtureId = new URL(document.URL).searchParams.get("fixtureId");
                if (typeof currentFixtureId !== 'undefined' && currentFixtureId) {
                    $("#liAggiungiStagione").addClass("uk-hidden");
                }
                
                // non ha senso mostrare la selezione delle stagioni se ho chiesto un evento specifico
                var currentEventIds = new URL(document.URL).searchParams.get("eventIds");
                if (typeof currentEventIds !== 'undefined' && currentEventIds) {
                    $("#liAggiungiStagione").addClass("uk-hidden");
                    $("#bottomNav").addClass("uk-hidden");      // tolgo filtri
                }
                
                // gestisco il fatto che se clicci sul tasto play non vanno più gli altri tasti
                $("#videoContainer").on("click", function () {
                    setTimeout(function () {
                        $("#media_playerHTML_html5_api").focus();
                    }, 100);
                });
                
                var btna = addPlayerNewButton({
                    player: videoJsPlayer,
                    content: "<img width='15px' src='/sicstv/images/back-forward-player.svg'/>",
                    insertBeforeClassName: "vjs-play-control",
                    id: "fastForward"
                });
                btna.onclick = function () {
                    if (videoJsPlayer !== null && typeof videoJsPlayer !== 'undefined' && videoJsPlayer.src()) {
                        if (videoJsPlayer.currentTime() <= 5) {
                            videoJsPlayer.currentTime(0);
                        } else {
                            videoJsPlayer.currentTime(videoJsPlayer.currentTime() - 5);
                        }
                    }
                };
                var btnb = addPlayerNewButton({
                    player: videoJsPlayer,
                    content: "<img width='15px' src='/sicstv/images/fast-forward-player.svg'/>",
                    insertBeforeClassName: "vjs-volume-panel",
                    id: "backForward"
                });
                btnb.onclick = function () {
                    if (videoJsPlayer !== null && typeof videoJsPlayer !== 'undefined' && videoJsPlayer.src()) {
                        if ((videoJsPlayer.currentTime() + 5) <= videoJsPlayer.duration()) {
                            videoJsPlayer.currentTime(videoJsPlayer.currentTime() + 5);
                        } else {
                            videoJsPlayer.currentTime(videoJsPlayer.duration());
                        }
                    }
                };
                var btnc = addPlayerNewButton({
                    player: videoJsPlayer,
                    content: "<img width='15px' src='/sicstv/images/stopwatch.svg'/>",
                    insertBeforeClassName: "vjs-playback-rate",
                    id: "stopwatch"
                });
                btnc.onclick = function () {
                    openGotoModal();
                };
                
                // extra seasons load
                var currentExtraSeasons = new URL(document.URL).searchParams.get("extraSeasonIds");
                if (typeof currentExtraSeasons !== 'undefined' && currentExtraSeasons) {
                    var extraSeasonIds = currentExtraSeasons.split(",");
                    extraSeasonIds.forEach(function (element) {
                        jsManageActive($("#season" + element));
                    });
                    
                    manageExtraSeasons();
                }
                
                moveProgressBar(100);
                checkModality();
                
                if (${empty matchPlayerId}) {
                    // di base mostro sempre tutta la partita da 0
                    // così attivo il live del posizionale
                    closeClipViewer();
                    // imposto a 0 altrimenti mi posiziona alla prima clip
                    videoJsPlayer.currentTime(0);
                }
                
                var startClip = new URL(document.URL).searchParams.get("startClip");
                if (typeof startClip !== 'undefined' && startClip !== null && startClip) {
                    startAction('', 0, isRunningOnChecked);
                }
                
                <c:if test="${fromTeamId != null}">
                    if (isClipStopped) {
                        // solo la prima volta clicco, altrimenti potrebbe fare doppio click
                        // quindi mette e toglie il filtro
                        videoJsPlayer.one('loadedmetadata', function() {
                            jsSetButtonSelected('team${fromTeamId}', 2);
                        });
                    }
                </c:if>

                // controllo per valore aggiunta secondi
                var increaseTimeValue = sessionStorage["increaseTimeStart"];
                if (typeof increaseTimeValue !== "undefined" && increaseTimeValue) {
                    setIncreaseTime(parseInt(increaseTimeValue));
                } else {
                    sessionStorage["increaseTimeStart"] = 5;
                }
            });
            
            function resumeStopVideo() {
                if (!videoJsPlayer.paused()) {
                    videoJsPlayer.pause();
                } else {
                    // verifico se devo tornare alla prima clip
                    if (selectedActions.length > 0 && indexActionToPlay === (selectedActions.length - 1)) {
                        var params = $("#startData" + selectedActions[indexActionToPlay]).val().split("||");
                        if (videoJsPlayer.currentTime() > parseInt(params[1])) { // se il tempo attuale è maggiore della fine dell'ultima clip
                            startAction('', 0);
                        }
                    }
                    videoJsPlayer.play();
                }
            }

            function updateBreadcrumb(videoname) {
                var breadcrumb = "";
                if (${mGameVideo.size() > 0}) {
                    var testGame = "";
                    var competitionName = "";
                    var competitionId = "";
                    var homeTeamId = "";
                    var homeTeamName = "";
                    var awayTeamId = "";
                    var awayTeamName = "";
                    var dateString = "";
            <c:forEach var="game" items="${mGameVideo}">
                    testGame = "<c:out value="${game.key}"/>";
                    testGame = testGame.replace("&#039;", "'");
                    if (testGame === videoname) {
                        competitionId = "<c:out value="${game.value.competitionId}"/>";
                        competitionName = "<c:out value="${game.value.competitionName}"/>";
                        homeTeamId = "<c:out value="${game.value.homeTeamId}"/>";
                        homeTeamName = "<c:out value="${game.value.homeTeam}"/>";
                        awayTeamId = "<c:out value="${game.value.awayTeamId}"/>";
                        awayTeamName = "<c:out value="${game.value.awayTeam}"/>";
                        dateString = "<c:out value="${game.value.getDateString()}"/>";
                    }
            </c:forEach>
                    if (dateString !== '' && competitionId !== '' && competitionName !== '' && homeTeamId !== '' && homeTeamName !== '' && awayTeamId !== '' && awayTeamName !== '') {
                        if (filtredEvents) {
                            if (playerFilter) {
                                breadcrumb = "<ul class='uk-breadcrumb uk-margin-left'>";
                                <c:if test="${mGirone != null}">
                                    <c:if test="${mUser.isPlayerUser()}">
                                        breadcrumb += "<li style='text-transform: uppercase'>${mGirone.groupName}</li>";
                                    </c:if>
                                    <c:if test="${!mUser.isPlayerUser()}">
                                        breadcrumb += "<li style='text-transform: uppercase'><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&groupId=${mGirone.groupId}'>${mGirone.groupName}</a></li>";
                                    </c:if>
                                </c:if>
                                breadcrumb += "<li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'>${mPlayer.last_name} ${mPlayer.first_name}</a></li></ul>";
                            } else {
                                breadcrumb = "<ul class='uk-breadcrumb uk-margin-left'>";
                                <c:if test="${mUser.isPlayerUser()}">
                                    breadcrumb += "<li>" + competitionName + "</li>";
                                </c:if>
                                <c:if test="${!mUser.isPlayerUser()}">
                                    breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "'>" + competitionName + "</a></li>";
                                </c:if>
                                <c:if test="${mGirone != null}">
                                    <c:if test="${mUser.isPlayerUser()}">
                                        breadcrumb += "<li style='text-transform: uppercase'>${mGirone.groupName}</li>";
                                    </c:if>
                                    <c:if test="${!mUser.isPlayerUser()}">
                                        breadcrumb += "<li style='text-transform: uppercase'><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&groupId=${mGirone.groupId}'>${mGirone.groupName}</a></li>";
                                    </c:if>
                                </c:if>
                                <c:if test="${mUser.isPlayerUser()}">
                                    breadcrumb += "<li>" + homeTeamName + " - " + awayTeamName + " - " + dateString + "</li></ul>";
                                </c:if>
                                <c:if test="${!mUser.isPlayerUser()}">
                                    breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&formTeamId=" + homeTeamId + "'>" + homeTeamName + "</a> - <a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&formTeamId=" + awayTeamId + "'>" + awayTeamName + "</a> - " + dateString + "</li></ul>";
                                </c:if>
                            }
                        } else {
                            breadcrumb = "<ul class='uk-breadcrumb uk-margin-left'>";
                            <c:if test="${mUser.isPlayerUser()}">
                                breadcrumb += "<li>" + competitionName + "</li>";
                            </c:if>
                            <c:if test="${!mUser.isPlayerUser()}">
                                breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "'>" + competitionName + "</a></li>";
                            </c:if>
                            <c:if test="${mGirone != null}">
                                <c:if test="${mUser.isPlayerUser()}">
                                    breadcrumb += "<li style='text-transform: uppercase'>${mGirone.groupName}</li>";
                                </c:if>
                                <c:if test="${!mUser.isPlayerUser()}">
                                    breadcrumb += "<li style='text-transform: uppercase'><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&groupId=${mGirone.groupId}'>${mGirone.groupName}</a></li>";
                                </c:if>
                            </c:if>
                            if (${not empty mPlayer.id}) {
                                breadcrumb += "<li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'><span>${mPlayer.last_name} ${mPlayer.first_name}</span></a></li>"
                            }
                            <c:if test="${fromTeamId != null}">
                                <c:if test="${mUser.isPlayerUser()}">
                                    breadcrumb += "<li>${mTeam.name}</li>";
                                </c:if>
                                <c:if test="${!mUser.isPlayerUser()}">
                                    breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&formTeamId=${mTeam.id}'>${mTeam.name}</a></li>";
                                </c:if>
                            </c:if>
                            <c:if test="${mUser.isPlayerUser()}">
                                breadcrumb += "<li>" + homeTeamName + " - " + awayTeamName + " - " + dateString + "</li></ul>";
                            </c:if>
                            <c:if test="${!mUser.isPlayerUser()}">
                                breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&formTeamId=" + homeTeamId + "'>" + homeTeamName + "</a> - <a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=" + competitionId + "&formTeamId=" + awayTeamId + "'>" + awayTeamName + "</a> - " + dateString + "</li></ul>";
                            </c:if>
                        }
                    }
                } else {
                    if (filtredEvents) {
                        if (playerFilter) {
                            breadcrumb = "<ul class='uk-breadcrumb uk-margin-left'>";
                            <c:if test="${mGirone != null}">
                                <c:if test="${mUser.isPlayerUser()}">
                                    breadcrumb += "<li style='text-transform: uppercase'>${mGirone.groupName}</li>";
                                </c:if>
                                <c:if test="${!mUser.isPlayerUser()}">
                                    breadcrumb += "<li style='text-transform: uppercase'><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${mCompetition.id}&groupId=${mGirone.groupId}'>${mGirone.groupName}</a></li>";
                                </c:if>
                            </c:if>
                            breadcrumb += "<li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'>${mPlayer.known_name} </a> - <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/></li></ul>";
                        } else {
                            breadcrumb = "<ul class='uk-breadcrumb uk-margin-left'>";
                            if (${not empty mCompetition.name}) {
                                <c:if test="${mUser.isPlayerUser()}">
                                    breadcrumb += "<li>${mCompetition.name}</li>";
                                </c:if>
                                <c:if test="${!mUser.isPlayerUser()}">
                                    breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${mCompetition.id}'>${mCompetition.name}</a></li>";
                                </c:if>
                                <c:if test="${mGirone != null}">
                                    <c:if test="${mUser.isPlayerUser()}">
                                        breadcrumb += "<li style='text-transform: uppercase'>${mGirone.groupName}</li>";
                                    </c:if>
                                    <c:if test="${!mUser.isPlayerUser()}">
                                        breadcrumb += "<li style='text-transform: uppercase'><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${mCompetition.id}&groupId=${mGirone.groupId}'>${mGirone.groupName}</a></li>";
                                    </c:if>
                                </c:if>
                            }
                            <c:if test="${mUser.isPlayerUser()}">
                                breadcrumb += "<li>${mTeam.name} - <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/> </li></ul>";
                            </c:if>
                            <c:if test="${!mUser.isPlayerUser()}">
                                breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=-1&formTeamId=${mTeam.id}'>${mTeam.name}</a> - <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/> </li></ul>";
                            </c:if>
                        }
                    }
                }
                if (breadcrumb !== "") {
                    $("#breadcrumb").html(breadcrumb);
                }
            }

            function initPageVideo() {
                singleMatch = ${fn:length(mGame)==1};
                var primo = true;
                if (${mGame.size() > 0}) {
                <c:forEach items="${mGame}" var="game" varStatus="status">
                    gamesSelected.push('${game.value.idFixture}');
                    if (singleMatch) {
                        $('body').prepend("<input type='hidden' id='mCompetitionId' value='${game.value.competitionId}' /><input type = 'hidden' id = 'mDay' value = '${game.value.matchday}' />");
                    }
                    if (primo) {
                        currentGameId = ${game.value.idFixture};
                        primo = false;
                        var breadcrumb = "";

                        if (filtredEvents) {
                            if (playerFilter) {
                                breadcrumb = "<div id='breadcrumb' class='uk-width-1-1'><ul class='uk-breadcrumb uk-margin-left'>";
                                breadcrumb += "<li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'>${mPlayer.last_name} ${mPlayer.first_name}</a>" + "</li></ul></div>";
                            } else {
                                breadcrumb = "<div id='breadcrumb' class='uk-width-1-1'><ul class='uk-breadcrumb uk-margin-left'>";
                                breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}'>${game.value.competitionName}</a></li>";
                                breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}&formTeamId=${game.value.homeTeamId}'>${game.value.homeTeam}</a> - <a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}&formTeamId=${game.value.awayTeamId}'>${game.value.awayTeam}</a> - ${game.value.getDateString()}</li></ul></div>";
                            }

                        } else {
                            breadcrumb = "<div id='breadcrumb' class='uk-width-1-1'><ul class='uk-breadcrumb uk-margin-left'>";
                            breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}'>${game.value.competitionName}</a></li>";
                            if (${not empty mPlayer.id}) {
                                breadcrumb += "<li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'><span>${mPlayer.last_name} ${mPlayer.first_name}</span></a></li>"
                            }
                            breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}&formTeamId=${game.value.homeTeamId}'>${game.value.homeTeam}</a> - <a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=${game.value.competitionId}&formTeamId=${game.value.awayTeamId}'>${game.value.awayTeam}</a> - ${game.value.getDateString()}</li></ul></div>";
                        }
                        $('#video').prepend(breadcrumb);

                        
                        curVideo = "${game.value.videoName}";
                        curVideoPath = "${game.value.getVideoPathS3()}";
                        if (${game.value.hd && !personalSource}) {
                            curVideoHDPath = "${game.value.getVideoPathHDS3()}";
                            curVideoPath = curVideoHDPath;
                            if (${game.value.minVideoQuality == 0}) {
                                $("#btnHD").removeClass("uk-hidden");
                            }
                        }
                        if (${game.value.tacticalVideo && !personalSource}) {
                            curVideoTACTPath = "${game.value.getVideoPathTACTS3()}";
                            $("#btnTACT").removeClass("uk-hidden");
                        }
                        if (${game.value.minVideoQuality} === 1) {
                            loadVideo(curVideoHDPath, true, false, true);
                        } else {
                            loadVideo(curVideoPath, false, false, true);
                        }
                        $("<div id='playerType' hidden>${game.value.providerId}</div>").insertAfter("#mGame");
                    }
                </c:forEach>
                } else {
                    if (filtredEvents) {
                        if (playerFilter) {
                            breadcrumb = "<div id='breadcrumb' class='uk-width-1-1'>" +
                                    "<ul class='uk-breadcrumb uk-margin-left'><li><a href='/sicstv/user/player.htm?personal=${personalSource}&playerId=${mPlayer.id}'>${mPlayer.known_name} </a> - <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/></li></ul></div>";
                        } else {
                            breadcrumb = "<div id='breadcrumb' class='uk-width-1-1'><ul class='uk-breadcrumb uk-margin-left'>";
                            if (${not empty mCompetition.name}) {
                                breadcrumb += "<li><a href='/sicstv/user/competition.htm?personal=${personalSource}&formCompetitionId=${mCompetition.id}'>${mCompetition.name}</a></li>";
                            }
                            breadcrumb += "<li><a href='/sicstv/user/team.htm?personal=${personalSource}&formCompetitionId=-1&formTeamId=${mTeam.id}'>${mTeam.name}</a>  - <spring:message code='${filterText}'/> <input type='hidden' id='gameFilter' value='${gameFilter}'/> </li></ul></div>";
                        }
                    }
                    $('#video').prepend(breadcrumb);
                }

                $("#numMatchSelected").html(gamesSelected.length);
                
                videoJsPlayer.on("ended", function() {
                    console.log("Loading next clip...");
                    if (videoJsPlayer && !isClipStopped) {
                        var actionsArray = [];
                        if (isRunningOnChecked) {
                            actionsArray = checkedActions;
                        } else {
                            actionsArray = selectedActions;
                        }
                        $('.currentClip').removeClass("currentClip");
                        if (indexActionToPlay < actionsArray.length - 1) {
                            indexActionToPlay++;
                            enableSwitchAction();
                            checkIfNeededToChangePage();

                            $('#rowEventId' + actionsArray[indexActionToPlay] +' td').addClass("currentClip");
                            highlightElementById("" + actionsArray[indexActionToPlay]);
                            updateTooltipSelectedActions("");
                            checkMarkersAndLogo();

                            var eventData = eventMap.get("" + actionsArray[indexActionToPlay]);
                            goToAction($.trim(eventData.mStart / 1000), $.trim(eventData.mEnd / 1000),
                                    $.trim(eventData.tempoGioco), $.trim(eventData._period_start_minute),
                                    $.trim(eventData.durataAzione), $.trim(eventData.mTeam),
                                    $.trim(eventData.mPlayer.toLowerCase()), $.trim(eventData.descrAzione), $.trim(eventData.mIdEvent),
                                    $.trim(eventData.videoId), $.trim(eventData.videoName), $.trim(eventData.videoPathS3),
                                    $.trim(eventData.provider), $.trim(eventData.idFixture), $.trim(eventData.mTactStart));
                        } else {
                            // DAFARE tentativo di cambiare tab, selezionare tutto e andare in play
                            if ($("#checkboxSelectAllEvents").prop("checked")) {
                                if ($(".paginate_button.next.disabled").length === 0) {
                                    $("#azioni_next a").trigger('click');
                                    $("#checkboxSelectAllEvents").attr("checked", "checked");
                                    jsSelectAllEvents('azioni', true);
                                    startAction('', 0, isRunningOnChecked);
                                }
                            }
                        }
                    }
                });
                videoJsPlayer.on("fullscreenchange", function() {
                    manageVideoOverlay();
                });
            }

            function loadVideo(videoname, playHD, playTACT, force) {
                if (typeof videoJsPlayer === 'undefined') {
                    var hdEnable = !$("#btnHD").hasClass("uk-hidden");
                    if (hdEnable) {
                        playHD = true;
                        if (!isMobile && !iOS()) {
                            if ($("#btnHD").hasClass("HDon")) {
                                // se è in hd allora passo allo standard
                                hdActive = false;
                                $("#btnHD img").attr("src", "/sicstv/images/hd-dark.png");
                                $("#btnHD").removeClass("HDon");
                            } else {
                                // se è sd passo all'HD
                                hdActive = true;
                                $("#btnHD img").attr("src", "/sicstv/images/hd-selected.png");
                                $("#btnHD").addClass("HDon");
                            }
                            tactActive = false;
                            $("#btnTACT img").attr("src", "/sicstv/images/tact.png");
                            $("#btnTACT").removeClass("HDon");
                        }
                    }
                    var videoHtmlToAdd = '<source src="' + videoname + '" type="video/mp4">';
                    $('#media_playerHTML').html(videoHtmlToAdd);
                    playerHTML = $('#media_playerHTML').get(0);
                    if (isPlaylistTv) {
                        playerHTML.className += ' hideSeekBar';
                    }
                }
                if (videoname !== curVideoPath || force) {
                    var hd = !$("#btnHD").hasClass("uk-hidden");
                    var tact = !$("#btnTACT").hasClass("uk-hidden");
                    $("#btnHD").addClass("uk-hidden");
                    $("#btnTACT").addClass("uk-hidden");
                    curVideoPath = videoname;
                    var splitVideName = curVideo.split(".");
                    updateBreadcrumb(splitVideName[0]);
                    var tactV = false;
                    var hdV = false;
                    var minVideoQuality = -1;
            <c:forEach items="${mGame}" var="game" varStatus="status" >
                    tactV = ${game.value.tacticalVideo && !personalSource};
                    hdV = ${game.value.hd && !personalSource};
                    minVideoQuality = ${game.value.minVideoQuality};
                    if ("${game.value.videoName}" === curVideo) {
                        if (hdV) {
                            curVideoHDPath = "${game.value.getVideoPathHDS3()}";
                            if (minVideoQuality === 1) {
                                $("#btnHD").addClass("uk-hidden");
                            } else {
                                $("#btnHD").removeClass("uk-hidden");
                            }
                        }
                        if (tactV) {
                            curVideoTACTPath = "${game.value.getVideoPathTACTS3()}";
                            $("#btnTACT").removeClass("uk-hidden");
                        }
                    }
            </c:forEach>
                    console.log("TACT " + curVideoTACTPath);
                    console.log("SD " + curVideoPath);
                    console.log("HD " + curVideoHDPath);
                    var lastPlaybackRate = 1;
                    if (typeof videoJsPlayer !== 'undefined' && typeof videoJsPlayer.getCache() !== 'undefined') {
                        var lastPlaybackRate = videoJsPlayer.getCache().lastPlaybackRate;
                    }
                    
                    videoJsPlayer = videojs("media_playerHTML");
                    // videoJsPlayer.offset();
                    videoJsPlayer.fluid(true); // resize al contenuto
                    videoJsPlayer.playbackRates([0.5, 1, 2, 3]);
                    videoJsPlayer.autoplay(true);
                    videoJsPlayer.controlBar.removeChild(videoJsPlayer.controlBar.getChild('PictureInPictureToggle'));  // toglie tasto per minimizzare il video
                    videoJsPlayer.controlBar.removeChild(videoJsPlayer.controlBar.getChild('remainingTimeDisplay'));    // toglie tempo rimanente clip
                    if (typeof videoJsPlayer.markers === "function") {
                        videoJsPlayer.markers({
                            markerStyle: {
                               'border-radius': '30%'
                            },
                            markerTip:{
                               display: true,
                               text: function(marker) {
                                  return marker.text;
                               },
                               time: function(marker) {
                                  return marker.time;
                               }
                            },
                            breakOverlay:{
                               display: false,
                               displayTime: 3,
                               style:{
                                  'width':'100%',
                                  'height': '20%',
                                  'background-color': 'rgba(0,0,0,0.7)',
                                  'color': 'white',
                                  'font-size': '17px'
                               },
                               text: function(marker) {
                                  return marker.overlayText;
                               }
                            },
                            onMarkerClick: function(marker) {},
                            onMarkerReached: function(marker) {},
                            markers: []
                        });
                    }
                    videoJsPlayer.one('loadedmetadata', function() {
                        videoJsPlayer.playbackRate(lastPlaybackRate);
                    });
                    
//                    setTimeout(function() {
//                        // dovrebbe esserci un evento ma non ho tempo di trovarlo
//                        // tipo onLoad o qualcosa del genere
//                        videoJsPlayer.playbackRate(lastPlaybackRate);
//                    }, 500);
                    
                    if (playTACT && curVideoTACTPath.length > 0) {
                        if (videoJsPlayer) {
                            try {
                                console.log("LOADING Tactical...");
                                curVideoPath = curVideoTACTPath;
                                videoJsPlayer.src(curVideoTACTPath);
                                loadMarkers();
                            } catch (ex) {
                                console.error(ex);
                            }
                        }
                    } else if (playHD && curVideoHDPath.length > 0) {
                        if (videoJsPlayer) {
                            try {
                                console.log("LOADING HD...");
                                curVideoPath = curVideoHDPath;
                                videoJsPlayer.src(curVideoHDPath);
                                loadMarkers();
                            } catch (ex) {
                                console.error(ex);
                            }
                        }
                    } else {
                        if (videoJsPlayer) {
                            try {
                                console.log("LOADING SD...");
                                videoJsPlayer.src(curVideoPath);
                                loadMarkers();
                            } catch (ex) {
                                console.error(ex);
                            }
                        }
                    }
                    // porto ad inizio del primo tempo se sincronizzato
                    if (isClipStopped) {
                        // potrei star caricando la pagina iniziale... in tal caso devo controllare se poi vedrò le clip
                        var startClip = new URL(document.URL).searchParams.get("startClip");
                        if ($('#mEventPresent').val() === 'true' || $('#mSer').val() !== '' || isPlaylistTv || highlights || filteredRequest) {
                            return;
                        }
                        if (typeof startClip !== 'undefined' && startClip !== null && startClip) {
                            return;
                        }
                        
                        var fixture = fixtureMap.get(currentGameId);
                        if (typeof fixture !== "undefined" && fixture !== null && typeof fixture.fixtureDetails !== "undefined") {
                            if (typeof fixture.fixtureDetails.startTime1 !== "undefined") {
                                videoJsPlayer.currentTime(Math.round(fixture.fixtureDetails.startTime1 / 1000) - 3);
                            }
                        }
                    }

                    if (hd && minVideoQuality == 0) {
                        $("#btnHD").removeClass("uk-hidden");
                    }
                    if (tact) {
                        $("#btnTACT").removeClass("uk-hidden");
                    }
                    //$("#btnHD").addClass("uk-hidden");
                    
                    checkTabellino();
                }
            }

            function loadMarkers() {
                videoJsPlayer.markers.removeAll();
                videoJsPlayer.one('loadedmetadata', function() {
                    if (isClipStopped) {
                        eventMap.forEach(function (event) {
                            // per multipartita
                            if (event.idFixture === currentGameId) {
                                var time = Math.round(event.mStart / 1000);
                                var duration = Math.round((event.mEnd - event.mStart) / 1000);
                                var extraClass = "";
                                if (fixtureMap.has(currentGameId)) {
                                    if (fixtureMap.get(currentGameId).homeTeamId === event._idTeam) {
                                        extraClass = " upper-icon";
                                    }
                                }
                                
                                if (event._idType === 25) {
                                    // rete fatta (no autogol)
                                    var isAutogol = false;
                                    var baseClass = "ball-icon";
                                    $.each(event.mTags, function (index, tag) {
                                        if (tag.code === "RTF-15") {
                                            isAutogol = true;
                                            baseClass = "autogol-icon";
                                        }
                                    });
                                    
                                    videoJsPlayer.markers.add([{time: time, duration: duration, text: event.descrAzioneOverlay, class: baseClass + extraClass}]);
                                } else if (event._idType === 32) {
                                    // espulsione
                                    var isDoubleYellowCard = false;
                                    $.each(event.mTags, function (index, tag) {
                                        if (tag.code === "ESP-3") {
                                            isDoubleYellowCard = true;
                                        }
                                    });

                                    if (isDoubleYellowCard) {
                                        videoJsPlayer.markers.add([{time: time, duration: duration, text: event.descrAzioneOverlay, class: 'redcard-double-yellow-icon' + extraClass}]);
                                    } else {
                                        videoJsPlayer.markers.add([{time: time, duration: duration, text: event.descrAzioneOverlay, class: 'redcard-icon' + extraClass}]);
                                    }
                                } else if (event._idType === 29) {
                                    // sostituzione
                                    videoJsPlayer.markers.add([{time: time, duration: duration, text: event.descrAzioneOverlay, class: 'substitution-icon' + extraClass}]);
                                } else if (event._idType === 5) {
                                    // rigore parato
                                    var isGoal = false, isPenalty = false;
                                    $.each(event.mTags, function (index, tag) {
                                        if (tag.code === "TIF-2") {
                                            isGoal = true;
                                        } else if (tag.code === "TIF-10") {
                                            isPenalty = true;
                                        }
                                    });
                                    
                                    if (!isGoal && isPenalty) {
                                        videoJsPlayer.markers.add([{time: time, duration: duration, text: event.descrAzioneOverlay, class: 'missed-penalty-icon' + extraClass}]);
                                    }
                                } /*else if (event._idType === 26) {
                                    // autogol
                                    var isPenalty = false;
                                    $.each(event.mTags, function (index, tag) {
                                        if (tag.code === "RTS-15") {
                                            isPenalty = true;
                                        }
                                    });
                                    
                                    if (isPenalty) {
                                        videoJsPlayer.markers.add([{time: time, duration: duration, text: event.descrAzioneOverlay, class: 'autogol-icon' + extraClass}]);
                                    }
                                }*/
                            }
                        });
                    }
                    
                    // loghi team (piccoli e grandi)
                    if (fixtureMap.has(currentGameId)) {
                        var homeTeam = teamMap.get(fixtureMap.get(currentGameId).homeTeamId);
                        var awayTeam = teamMap.get(fixtureMap.get(currentGameId).awayTeamId);
                        
                        if (homeTeam && awayTeam) {
                            $("#home-logo-big").remove();
                            $("#away-logo-big").remove();
                            $("#both-logo-small").remove();
                            
                            // grandi
                            addPlayerNewImage({
                                player: videoJsPlayer,
                                content: "<img style='background: transparent' height='32px' width='32px' src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + homeTeam.logo + ".png' title='" + homeTeam.name + "'>",
                                insertBeforeClassName: "vjs-progress-control",
                                id: "home-logo-big"
                            });
                            addPlayerNewImage({
                                player: videoJsPlayer,
                                content: "<img style='background: transparent' height='32px' width='32px' src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + awayTeam.logo + ".png' title='" + awayTeam.name + "'>",
                                insertBeforeClassName: "vjs-progress-control",
                                id: "away-logo-big"
                            });
                            
                            addPlayerNewImage({
                                player: videoJsPlayer,
                                content: "<img style='background: transparent' height='16px' width='16px' src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + homeTeam.logo + ".png' title='" + homeTeam.name + "'><img style='background: transparent' height='16px' width='16px' src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + awayTeam.logo + ".png' title='" + awayTeam.name + "'>",
                                insertBeforeClassName: "vjs-progress-control",
                                id: "both-logo-small"
                            });
                        }
                    }
                    
                    checkMarkersAndLogo();
                });
            }
            
            function checkMarkersAndLogo() {
                if (isClipStopped) {
                    $("#home-logo-big").addClass("uk-hidden");
                    $("#away-logo-big").addClass("uk-hidden");
                    $("#both-logo-small").removeClass("uk-hidden");
                    
                    $(".vjs-marker").removeClass("uk-hidden");
                } else {
                    $("#home-logo-big").addClass("uk-hidden");
                    $("#away-logo-big").addClass("uk-hidden");
                    $("#both-logo-small").addClass("uk-hidden");
                    
                    $(".vjs-marker").addClass("uk-hidden");
                    
                    var actionsArray = [];
                    if (isRunningOnChecked) {
                        actionsArray = checkedActions;
                    } else {
                        actionsArray = selectedActions;
                    }
                    
                    if (actionsArray[indexActionToPlay]) {
                        var event = eventMap.get(actionsArray[indexActionToPlay]);
                        if (fixtureMap.has(currentGameId)) {
                            if (fixtureMap.get(currentGameId).homeTeamId === event._idTeam) {
                                $("#home-logo-big").removeClass("uk-hidden");
                            } else {
                                $("#away-logo-big").removeClass("uk-hidden");
                            }
                        }
                    }
                }
            }
            
            function initVideoHTML(videoname) {
                try {
                    videoJsPlayer.src(videoname);
                    var videoHtmlToAdd = '<source src="' + videoname + '" type="video/mp4">';
                    $('#media_playerHTML').html(videoHtmlToAdd);
                    playerHTML = $('#media_playerHTML').get(0);

                    if (isPlaylistTv) {
                        playerHTML.className += ' hideSeekBar';
                    }
                    curVideoPath = videoname;
                } catch (ex) {
                    console.error(ex);
                }
            }

            // logout ajax
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);

            function selezionaDeseleziona() {
                //Se sto selezionando tutto
                if (ultimaOperazione == 1) {
                    $("#input_azione input").iCheck('check');
                    ultimaOperazione = 2;
                } else {
                    //Se sto deselezionando tutto
                    if (ultimaOperazione == 2) {
                        $("#input_azione input").iCheck('uncheck');
                        ultimaOperazione = 1;
                    }
                }
            }
            
            function applyFilterEvents(clearQuickSearch, recalcPlayerCounter) {                
                //emptySelectedActions();
                if (clearQuickSearch) {
                    jsRemoveFilter('', 5);
                }
                var squadre = [];
                var azioni = [];
                var sorgenti = [];
                var tempo = [];
                var giocatori = [];
                var esito = [];
                var partite = [];
                var posizioni = [];
                var angoli = [];
                var posizioniPorta = [];

                $.each(teamSelected, function (index, value) {
                    squadre.push(value);
                });
                //Seleziono le azioni
                $.each(eventSelected, function (index, value) {
                    azioni.push(value);
                });
                //Seleziono i sorgenti
                if ($("#sourceSics").hasClass("uk-button-success")) {
                    sorgenti.push($("#sourceSics").val());
                }
                if ($("#sourcePersonal").hasClass("uk-button-success")) {
                    sorgenti.push($("#sourcePersonal").val());
                }
                //Seleziono il tempo
                if ($("#timeFirstButton").hasClass('uk-button-success') || $("#timeFirstQuarter").hasClass('uk-button-success')) {
                    tempo.push('1');
                }
                if ($("#timeSecondButton").hasClass('uk-button-success') || $("#timeSecondQuarter").hasClass('uk-button-success')) {
                    tempo.push('2');
                }
                if ($("#timeThirdQuarter").hasClass('uk-button-success')) {
                    tempo.push('3');
                }
                if ($("#timeFourthQuarter").hasClass('uk-button-success')) {
                    tempo.push('4');
                }
                //Seleziono i giocatori
                $.each(playerSelected, function (index, value) {
                    giocatori.push(value);
                });

                //Seleziono le partite
                $.each(gamesSelected, function (index, value) {
                    partite.push(value);
                });
                
                //Seleziono le posizioni
                $.each(posizioniSelected, function (index, value) {
                    posizioni.push(value);
                });
                
                //Seleziono gli angoli
                $.each(angoliSelected, function (index, value) {
                    angoli.push(value);
                });
                
                //Seleziono le posizioni della porta
                $.each(posizioniPortaSelected, function (index, value) {
                    posizioniPorta.push(value);
                });

                // Selezione esito
                if ($("#btn2ptPos").hasClass('uk-button-success')) {
                    esito.push("2PT+");
                }
                if ($("#btn2ptNeg").hasClass('uk-button-success')) {
                    esito.push("2PT-");
                }
                if ($("#btn3ptPos").hasClass('uk-button-success')) {
                    esito.push("3PT+");
                }
                if ($("#btn3ptNeg").hasClass('uk-button-success')) {
                    esito.push("3PT-");
                }
                if ($("#btnFF").hasClass('uk-button-success')) {
                    esito.push("FF");
                }
                if ($("#btnFS").hasClass('uk-button-success')) {
                    esito.push("FS");
                }
                if ($("#btnPP").hasClass('uk-button-success')) {
                    esito.push("PP");
                }
                if ($("#btnREC").hasClass('uk-button-success')) {
                    esito.push("REC");
                }

                countRowVisible = 0;
                var playerEventAmount = new Map();
                zoneEventAmount = new Map();
                angleEventAmount = new Map();
                zoneShotEventAmount = new Map();
                
                var hasFilter = $("#bottomNavContent").find("button.uk-button-success").length > 0 || $("#sourcePersonal").hasClass("uk-button-success");
                
                //console.log(squadre, azioni, sorgenti, tempo, giocatori, esito, partite, posizioni, angoli, posizioniPorta);                
                actionTable.rows().every(function (rowIdx, tableLoop, rowLoop) {
                    var eventId = $(this.node()).attr("id").replace("rowEventId", "");
                    var eventData = eventMap.get("" + eventId);
                    
                    /* SICS2017_PIF||PIF-1,PIF-2#9012,9013#1#1#108#2345#2pt+,pp -> 
                     * [0]=PIF||PIF-1,PIF-2
                     * [1]=9012,9013 -> giocatori
                     * [2]=1 -> half
                     * [3]=1 -> sics/pers
                     * [4]=108 -> squadra
                     * [5]= 2345 -> fixtureId
                     * [6]= 2PT+ -> esito*/
                    var condEsito = true;

                    if (esito.length > 0) {
                        condEsito = false;
                        var resultSplitted = eventData.mResultReal.split(",");
                        $.each(esito, function (index, value) {
                            $.each(resultSplitted, function (indexEsito, valueEsito) {
                                if (valueEsito === value) {
                                    condEsito = true;
                                    return false;
                                }
                            });
                        });
                    }
                    if (!condEsito) {
                        //  $(this).closest("tr").addClass("uk-hidden");
                        $(this.node()).removeClass("eventToShow");
                        return true;
                    }
                    var condHalf = true;
                    if (tempo.length > 0) {
                        condHalf = false;
                        $.each(tempo, function (index, value) {
                            if (parseInt(value) === eventData.mHalf) {
                                condHalf = true;
                                return false;
                            }
                        });
                    }
                    if (!condHalf) {
                        //   nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        $(this.node()).removeClass("eventToShow");
                        return true;
                    }
                    var condSource = true;
                    if (sorgenti.length > 0) {
                        condSource = false;
                        $.each(sorgenti, function (index, value) {
                            if (value === eventData.groupsetId) {                        
                                condSource = true;
                                return false;
                            }
                        });
                    }
                    if (!condSource) {
                        //   nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        $(this.node()).removeClass("eventToShow");
                        return true;
                    }
                    var condType = true;
                    if (azioni.length > 0) {
                        condType = false;
                        $.each(azioni, function (index, value) {
                            // split del tipo che è strutturato: SICS2015_ATT_ATT-4 oppure SICS2105_ATT
                            var toSearch = value.split("_");
                            var size = toSearch.length;
                            var trovato = false;
                            if (size > 1) {
                                // individuo il tipo dell'azione che cerchiamo
                                var tmpActionType = eventData.config.replace(".xml", "");
                                tmpActionType += "_" + eventData.button.cod;
                                var tags = "";
                                $.each(eventData.mTags, function (index, tag) {
                                    if (tags) tags += ",";
                                    tags += tag.code;
                                });
                                if (tags) {
                                    tmpActionType += "||" + tags;
                                }
                                
                                var actionType = tmpActionType.split("||");
                                /* una volta splittato ho: es. SICS2017_PIF||PIF-1,PIF-2
                                 * actionType[0] = SICS2015_PIF
                                 * actionType[1] = PIF-1,PIF-2
                                 * */
                                var configTypePair = actionType[0].split("_");
                                if (configTypePair[0] === toSearch[0]) {

                                    if (size === 2) {
                                        // se la size è 2 allora è un fondamentale e quindi lo confronto con il tipo dell'azione
                                        if (actionType.length === 1) {
                                            trovato = configTypePair[1] === toSearch[1];
                                        }
                                    } else if (size === 3) {
                                        // se la size è 3 allora è un tag quindi vado a confrontare con i tag dell'azione
                                        if (typeof actionType[1] === "undefined" && toSearch[2].includes("-GEN")) {
                                            if (actionType[0].replace(toSearch[0] + "_", "") === toSearch[1]) {
                                                trovato = true;
                                            }
                                        } else if (actionType.length > 1) {
                                            // determino i tag dell'azione
                                            var tagsAction = actionType[1].split(",");
                                            $.each(tagsAction, function (indexTag, valueTag) {
                                                if (valueTag === toSearch[2]) {
                                                    trovato = true;
                                                    return false;
                                                }
                                            });
                                        }
                                    }
                                }
                                if (trovato) {
                                    // significa che è un azione da far vedere
                                    condType = true;
                                    return false;
                                }
                            }
                        });
                    }

                    if (!condType) {
                        //   nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        $(this.node()).removeClass("eventToShow");
                        return true;
                    }

                    var condPartita = true;
                    if (partite.length > 0) {
                        condPartita = false;
                        $.each(partite, function (index, value) {
                            var fixtureId = eventData.idFixture;
                            if (parseInt(value) === fixtureId) {
                                condPartita = true;
                                return false;
                            }
                        });
                    }
                    if (!condPartita || (!singleMatch && partite.length == 0)) {
                        //    nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        $(this.node()).removeClass("eventToShow");
                        return true;
                    }

                    // prima di verificare i giocatori aggiorno i contatori
                    var tmpPlayers = "";
                    Object.keys(eventData._player).forEach((value, key) => {
                        if (tmpPlayers) {
                            tmpPlayers += ",";
                        }
                        tmpPlayers += value;
                    });
                    var eventPlayerList = tmpPlayers.split(",");
                    $.each(eventPlayerList, function (indexP, valueP) {
                        if (valueP) {
                            if (typeof playerEventAmount.get(valueP) === 'undefined') {
                                playerEventAmount.set(valueP, 1);
                            } else {
                                playerEventAmount.set(valueP, (playerEventAmount.get(valueP) + 1));
                            }
                        }
                    });
                    
                    var condTeamPlayer = true;
                    // verifica sulle squadre e giocatori
                    if (squadre.length === 0) {
                        // se non è specificata una squadra
                        if (giocatori.length === 0) {
                            // se non sono specificati giocatori
                            condTeamPlayer = true;
                        } else {
                            // se sono specificati dei giocatori
                            var atLeastOneValid = false;
                            $.each(giocatori, function (index, value) {
                                var selectedPlayer = tmpPlayers.split(",");
                                $.each(selectedPlayer, function (indexP, valueP) {
                                    if (value === valueP) {
                                        atLeastOneValid = true;
                                        return false;
                                    }
                                });
                            });
                            condTeamPlayer = atLeastOneValid;
                        }
                    } else {

                        // squadre non è vuoto
                        $.each(squadre, function (index, value) {
                            var selectedTeam = eventData._idTeam;
                            var trovato = false;
                            if (parseInt(value) === selectedTeam) {
                                trovato = true;
                            }
                            // se l'ho trovato allora la condizione è soddisfatta
                            condTeamPlayer = trovato;
                            if (!condTeamPlayer) {
                                // se il team dell'azione è diverso da quello selezionato verifico se sono stati specificati giocatori
                                if (giocatori.length > 0) {
                                    // se sono specificati dei giocatori
                                    var atLeastOneValid = false;
                                    $.each(giocatori, function (index, value) {
                                        var selectedPlayer = tmpPlayers.split(",");
                                        $.each(selectedPlayer, function (indexP, valueP) {
                                            if (value === valueP) {
                                                atLeastOneValid = true;
                                                return false;
                                            }
                                        });
                                    });
                                    condTeamPlayer = atLeastOneValid;
                                }
                            } else {
                                return false;
                            }
                        });
                    }

                    if (condTeamPlayer) {
                        // se filtro dal "Search" e poi premo per resettare i filtri, le colonne del filtro
                        // hanno di già la classe "eventToShow", quindi se la colonna è valida, prima di tutto
                        // tolgo la classe, poi SE bisogna viene ri-aggiunta
                        $(this.node()).removeClass("eventToShow");
                        // alle righe che corrispondono al filtro di ricerca aggiungo la classe eventToShow
                        countRowVisible++;
                        //   nodeInfoSearch.closest("tr").removeClass("uk-hidden");
                        if (hasFilter || !$(this.node()).hasClass("hideMe")) {
                            $(this.node()).addClass("eventToShow");
                        }
                    } else {
                        // nodeInfoSearch.closest("tr").addClass("uk-hidden");
                        $(this.node()).removeClass("eventToShow");
                        return true;
                    }

                    if ($(this.node()).hasClass("eventToShow")) {
                        var condAngolo = true;
                        if (angoli.length > 0) {
                            condAngolo = false;
                            $.each(angoli, function (index, value) {
                                // prima di controllare l'angolo verifico che il punto sia valido
                                // ovvero che abbia sia la posizione iniziale che finale e che i punti
                                // siano diversi
                                var pos = eventData.mActionPosNormalized;
                                var posEnd = eventData.mActionPosEndNormalized;
                                if (pos && !pos.isDefault && posEnd && !posEnd.isDefault) {
                                    if (pos.x !== posEnd.x || pos.y !== posEnd.y) {
                                        var angle = parseInt(value);
                                        if (eventData.mActionAngleZone === angle) {
                                            // SOTTO ALLA CONDIZIONE SULLA POSIZIONE C'E' IL CODICE
                                            // PER I CONTATORI DELLE ZONE, DA LASCIARE COME ULTIMA COSA
                                            condAngolo = true;
                                            return false;
                                        }
                                    }
                                }
                            });
                        }
                        if (!condAngolo) {
                            $(this.node()).removeClass("eventToShow");
                            return true;
                        }

                        var condPosizione = true;
                        var atLeastOneValid = true;
                        if (posizioni.length > 0) {                            
                            condPosizione = false, atLeastOneValid = false;
                            var eventAlreadyCounted = new Map();
                            $.each(posizioni, function (index, value) {
                                var posToCheck, secondaryPosToCheck = "2|";
                                // zoneId usato per contare quanti eventi in quel "settore"
                                var splitted = value.split("-_-");
                                var zoneId = parseInt(splitted[0]);
                                value = splitted[1];
                                if (value.startsWith("1|")) {
                                    posToCheck = {...eventData.mActionPosNormalized};
                                } else {
                                    posToCheck = {...eventData.mActionPosEndNormalized};
                                    secondaryPosToCheck = "1|";
                                }

                                if (value.includes(";")) {
                                    if (posToCheck && !posToCheck.isDefault) {
                                        var coords = value.replace("1|", "").replace("2|", "");
                                        var validCoordinates = coords.split("||");
                                        validCoordinates.forEach(function (coordinate) {
                                            coords = coordinate.split(";");
                                            var x = parseFloat(coords[0]);
                                            var x1 = parseFloat(coords[2]) + x;
                                            var y = parseFloat(coords[1]);
                                            var y1 = parseFloat(coords[3]) + y;
                                            if (validCoordinates.length > 1) {
                                                // mi arrivano le coordinate giuste
                                                x1 = parseFloat(coords[2]);
                                                y1 = parseFloat(coords[3]);
                                            }
    //                                        console.log(parseFloat(coords[0]), parseFloat(coords[1]), parseFloat(coords[2]), parseFloat(coords[3]), eventData);
                                            if (posToCheck.x >= x && posToCheck.x <= x1 &&
                                                    posToCheck.y >= y && posToCheck.y <= y1) {

                                                var atLeastOneOpposite = false;
                                                $.each(posizioni, function (index, value) {
                                                    var splitted = value.split("-_-");
                                                    var nextZoneId = parseInt(splitted[0]);
                                                    value = splitted[1];
                                                    if (value.startsWith(secondaryPosToCheck)) {
                                                        atLeastOneOpposite = true;
                                                        posToCheck = (secondaryPosToCheck === "1|" ? {...eventData.mActionPosNormalized} : {...eventData.mActionPosEndNormalized});
                                                        if (value.includes(";")) {
                                                            if (posToCheck && !posToCheck.isDefault) {
                                                                var coords = value.replace("1|", "").replace("2|", "");
                                                                var validCoordinatesAgain = coords.split("||");
                                                                validCoordinatesAgain.forEach(function (coordinate) {
                                                                    coords = coordinate.split(";");
                                                                    var x = parseFloat(coords[0]);
                                                                    var x1 = parseFloat(coords[2]) + x;
                                                                    var y = parseFloat(coords[1]);
                                                                    var y1 = parseFloat(coords[3]) + y;
                                                                    if (validCoordinatesAgain.length > 1) {
                                                                        // mi arrivano le coordinate giuste
                                                                        x1 = parseFloat(coords[2]);
                                                                        y1 = parseFloat(coords[3]);
                                                                    }
                                    //                                console.log(parseFloat(coords[0]), parseFloat(coords[1]), parseFloat(coords[2]), parseFloat(coords[3]));
                                                                    if (posToCheck.x >= x && posToCheck.x <= x1 &&
                                                                            posToCheck.y >= y && posToCheck.y <= y1) {

                                                                        var valueToAdd = 1;
                                                                        if (zoneId === nextZoneId) {
                                                                            // se from e to sono nella stessa cella aggiungo 0.5
                                                                            // dato che passa di qua sia per il punto di inizio che di arrivo
                                                                            // 22/04: dato che ora uso una mappa che segna quali eventi ho già contato
                                                                            // non serve più aggiungere 0.5 due volte, aggiungo 1 una volta e basta
                                                                            // valueToAdd = 0.5;
                                                                        }

                                                                        if (!eventAlreadyCounted.has(zoneId)) {
                                                                            eventAlreadyCounted.set(zoneId, []);
                                                                        }
                                                                        if (!eventAlreadyCounted.get(zoneId).includes(eventData)) {
                                                                            var alreadyAdded = zoneEventAmount.has(zoneId);
                                                                            if (!alreadyAdded) {
                                                                                zoneEventAmount.set(zoneId, []);
                                                                            }
//                                                                            if (alreadyAdded) {
//                                                                                zoneEventAmount.set(zoneId, zoneEventAmount.get(zoneId) + valueToAdd);
//                                                                            } else {
//                                                                                zoneEventAmount.set(zoneId, valueToAdd);
//                                                                            }
                                                                            zoneEventAmount.get(zoneId).push(eventData._id);
                                                                            eventAlreadyCounted.get(zoneId).push(eventData);
                                                                        }

                                                                        atLeastOneValid = true;
                                                                        condPosizione = true;
                                                                        return false;
                                                                    }
                                                                });
                                                            }
                                                        } else {
                                                            console.warn("Unable to check position. value not valid", value);
                                                        }
                                                    }
                                                });

                                                if (!atLeastOneOpposite) {
                                                    if (!eventAlreadyCounted.has(zoneId)) {
                                                        eventAlreadyCounted.set(zoneId, []);
                                                    }
                                                    if (!eventAlreadyCounted.get(zoneId).includes(eventData)) {
                                                        var alreadyAdded = zoneEventAmount.has(zoneId);
                                                        if (!alreadyAdded) {
                                                            zoneEventAmount.set(zoneId, []);
                                                        }
//                                                        if (alreadyAdded) {
//                                                            zoneEventAmount.set(zoneId, zoneEventAmount.get(zoneId) + 1);
//                                                        } else {
//                                                            zoneEventAmount.set(zoneId, 1);
//                                                        }
                                                        zoneEventAmount.get(zoneId).push(eventData._id);
                                                        eventAlreadyCounted.get(zoneId).push(eventData);
                                                    }

                                                    atLeastOneValid = true;
                                                    condPosizione = true;
                                                    return false;
                                                }
                                            }
                                        });
                                    }
                                } else {
                                    console.warn("Unable to check position. value not valid", value);
                                }
                            });
                        }
                    }
                    if (!atLeastOneValid) {
                        $(this.node()).removeClass("eventToShow");
                        return true;
                    }
                    
                    var condPosizionePorta = true;
                    if (posizioniPorta.length > 0) {
                        condPosizionePorta = false;
                        var isBlocked = false;
                        eventData.mTags.forEach((tag) => {
                            if (tag.code && (tag.code === 'TIF-1' || tag.code === 'TIS-1')) {
                                isBlocked = true;
                            }
                        });
                        var atLeastOneFilter = $("#listAzioneRicerca").find("input:checked").length > 0
                                || $("#liFiltriTacticalContent").find("input:checked").length > 0
                                || ($("#listAzioneRicerca").find("button").length === 1 && $("#listAzioneRicerca").find("button").attr("id").includes("TIS"))
                                || ($("#listAzioneRicerca").find("button").length === 1 && $("#listAzioneRicerca").find("button").attr("id").includes("RTS"));
                        if (!(atLeastOneFilter || (eventData.mType !== "TIS" && eventData.mType !== "RTS"))) {
                            isBlocked = true;
                        } else if (!filteredRequest && !atLeastOneFilter && eventData.mType === "TIF") {
                            eventData.mTags.forEach((tag) => {
                                if (tag.code && tag.code === 'TIF-2') {
                                    isBlocked = true;
                                }
                            });
                        }
                        if (!isBlocked) {
                            $.each(posizioniPorta, function (index, value) {
                                var splitted = value.split("-_-");
                                var zoneId = parseInt(splitted[0]);
                                var pos = eventData.mShot3DNormalized;
                                value = splitted[1];
                                var posToCheck = {...pos};
                                if (posToCheck && !posToCheck.isDefault) {
                                    if (((eventData.mType === 'TIF' || eventData.mType === 'RTF') && posToCheck.x < 52.5) ||
                                            ((eventData.mType === 'TIS' || eventData.mType === 'RTS') && posToCheck.x < 52.5)) {
                                        posToCheck.y = 68 - posToCheck.y;
                                    }
//                                    if (eventData.mType === 'TIF') {
//                                        posToCheck.x = 68 - posToCheck.x;
//                                    }
                                    // se fuori porta, metto come se fossero pali
                                    // hack temporaneo
//                                    if (posToCheck.y < 30.34) {
//                                        posToCheck.y = 30.34;
//                                    }
//                                    if (posToCheck.y >= 37.66) {
//                                        // perchè sotto fa < al posto di <=
//                                        posToCheck.y = 37.65;
//                                    }
//                                    if (posToCheck.z > 2.44) {
//                                        posToCheck.z = 2.44;
//                                    }
                                    var coords = value.replace("1|", "").replace("2|", "").split(";");
                                    var x = parseFloat(coords[0]);
                                    var x1 = parseFloat(coords[2]) + x;
                                    var y = parseFloat(coords[1]);
                                    var y1 = y - parseFloat(coords[3]);

                                    if (posToCheck.y >= x && posToCheck.y <= x1 &&
                                            posToCheck.z >= y1 && posToCheck.z <= y) {
                                        var alreadyAdded = zoneShotEventAmount.has(zoneId);
                                        if (!alreadyAdded) {
                                            zoneShotEventAmount.set(zoneId, []);
                                        }
                                        zoneShotEventAmount.get(zoneId).push(eventData._id);
//                                        if (alreadyAdded) {
//                                            zoneShotEventAmount.set(zoneId, zoneShotEventAmount.get(zoneId) + 1);
//                                        } else {
//                                            zoneShotEventAmount.set(zoneId, 1);
//                                        }

                                        condPosizionePorta = true;
                                        return false;
                                    }
                                }
                            });
                        }
                    }
                    if (!condPosizionePorta) {
                        $(this.node()).removeClass("eventToShow");
                        return true;
                    }
                    
                    // DA LASCIARE QUA COME ULTIMO ALTRIMENTI NON CALCOLA CORRETTAMENTE
                    // NON SERVE RICONTROLLARE I PUNTI PERCHE' SE NON FOSSE VALIDO QUA NON
                    // CI ARRIVO
                    if (angoli.length > 0) {
                        $.each(angoli, function (index, value) {
                            var angle = parseInt(value);
                            if (eventData.mActionAngleZone === angle) {
                                var alreadyAdded = angleEventAmount.has(angle);
                                if (!alreadyAdded) {
                                    angleEventAmount.set(angle, []);
                                }
                                angleEventAmount.get(angle).push(eventData._id);
//                                if (alreadyAdded) {
//                                    angleEventAmount.set(angle, angleEventAmount.get(angle) + 1);
//                                } else {
//                                    angleEventAmount.set(angle, 1);
//                                }
                            }
                        });
                    }
                });

                updateDataTable();
                
                // solo quando filtro per team e non ci sono eventi
                var hasFilter = $("#bottomNavContent").find("button.uk-button-success:not(.teamAllButton)").length > 0
                        || $("#sourcePersonal").hasClass("uk-button-success");
                var isRemoveDuplicates = new URL(document.URL).searchParams.get("removeDuplicates");
                if (!hasFilter && !filteredRequest && !ipo && isRemoveDuplicates !== null) {
                    removeDuplicatesV2(actionTable.rows({ filter: 'applied' }));
                    updateDataTable();
                }
                if (removeExactDuplicates && !ipo) {
                    removeDuplicatesSmart(actionTable.rows({ filter: 'applied' }));
                    updateDataTable();
                }
                
                updateZoneCounters();
                updateAngleCounters();
                updateZoneShotCounters();
                // giocatori.length === 0 perchè se hai un filtro sui giocatori e poi cambi evento
                // carica i numeri filtrati anche per giocatore. Quindi poi se tu togli tutti i giocatori
                // posso ricalcolare i contatori
                if (recalcPlayerCounter || giocatori.length === 0) {
                    // aggiorno il contatore con il valore della mappa
                    $("#filtroGiocatore").find(".width-1-2-x").find(".uk-button").each(function() {
                        var htmlContent = $(this).html();
                        var playerId = $(this).val();
                        if (typeof playerEventAmount.get(playerId) !== 'undefined') {
                            $(this).html(htmlContent.substring(0, htmlContent.indexOf("(")) + " (" + playerEventAmount.get(playerId) + ")");
                        } else {
                            $(this).html(htmlContent.substring(0, htmlContent.indexOf("(")) + " (0)");
                        }
                    });
                }
                
//                if (!singleMatch) {
//                    if (gamesSelected.length == 0) {
//                        $("#videoContainer").width($("#media_playerHTML").width());
//                        $("#videoContainer").height($("#media_playerHTML").height());
//                        $("#videoContainer").addClass("blackout");
//                        closeClipViewer();
//                    } else {
//                        $("#videoContainer").removeClass("blackout");
//                    }
//                }
            }

            function updateDataTable() {
                $.fn.dataTable.ext.search.push(
                    function (settings, data, dataIndex) {
                        return $(actionTable.row(dataIndex).node()).hasClass('eventToShow');
                    }
                );
                actionTable.draw();
                console.log("update");
            }

            var indexActionToPlay = -1; // messo a -1 per fixare problema se fai screenshot senza entrare almeno in una clip
            var isClipStopped = true;

            function iOS() {

                var iDevices = [
                    'iPad Simulator',
                    'iPhone Simulator',
                    'iPod Simulator',
                    'iPad',
                    'iPhone',
                    'iPod'
                            //,
                            //'Mac68K',
                            //'MacPPC',
                            //'MacIntel'
                ];

                if (!!navigator.platform) {
                    while (iDevices.length) {
                        if (navigator.platform === iDevices.pop()) {
                            return true;
                        }
                    }
                }

                return false;
            }

            function isSafari() {
                return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            }

            //funzione che fa andare l'azione al secondo esatto del match e mette in pausa dopo il tempo di durata dell'azione.			
            function goToActionHTML(start, end, tempo, minutoInizio, durataAzione, nomeSquadra, playerTrim, descAzione, idevent) {
                $(".actionFocus").removeClass("actionFocus");
                
//                playerHTML.currentTime = start;
//                if (iOS() || isSafari()) {
//                    playerHTML.addEventListener('canplay', function () {
//                        playerHTML.currentTime = start;
//                        if (playerHTML.currentTime === start) {
//                            playerHTML.play();
//                        }
//                    });
//                } else {
//                    playerHTML.play();
//                }
                fineAzione = end;
                inizioAzione = start;
                var player = '';
                if (playerTrim !== '') {
                    player = " - " + playerTrim;
                }
                var tagFocus = $("#azioni li").children("#azione").children("#tagAzione-" + idevent).text();
                desc = tempo + ' - min ' + minutoInizio + ' - ' + durataAzione + 's' + '<br>' + nomeSquadra + player + '<br>' + descAzione + ' ' + tagFocus;
                $('#nome_azione').html(desc);
                
                //videoJsPlayer.offset();
                //videoJsPlayer.tagAttributes['manualUpdate'] = true;
                //console.log(videoJsPlayer._offsetStart + " - " + videoJsPlayer._offsetEnd + " NEW ONES: " + start + " - " + end + " TIME IS: " + videoJsPlayer.currentTime());
                videoJsPlayer.offset({
                    start: start,
                    end: end,
                    restart_beginning: true //Should the video go to the beginning when it ends
                });
                manageVideoOverlay();
                videoJsPlayer.currentTime(0);
                videoJsPlayer.play();
                
                // controllo per rec se attivato tolgo dato che non posso fare record dentro a più clip
                if (isRecording) {
                    UIkit.notify("<spring:message code="video.record.disabled"/>", {status: 'warning', timeout: 2500});
                    manageRec(true);
                }
                
                //console.log("AFTER: " + videoJsPlayer._offsetStart + " - " + videoJsPlayer._offsetEnd + " TIME IS: " + videoJsPlayer.currentTime() + " CACHE: " + videoJsPlayer.getCache().currentTime);
//                setTimeout(function() {
//                    videoJsPlayer.play();
//                }, 500);
            }

            function manageVideoOverlay() {
                if (typeof videoOverlay !== "undefined") {
                    if (videoOverlay.get().length > 0) {
                        videoOverlay.get().forEach(function (element) {
                            if (!element.isDisposed()) {
                                element.dispose();
                            }
                        });
                    }
                }

                if (videoJsPlayer.isFullscreen()) {
                    var overlayContent = $(".currentClip").parent().attr("overlay-text");
                    if (overlayContent) {
                        videoOverlay = videoJsPlayer.overlay({
                            content: 'Event Title',
                            align: 'bottom-left',
                            attachToControlBar: true,
                            class: 'video-title',
                            overlays: [{
                              content: overlayContent,
                              start: 0,
                              end: 3
                            }]
                        });
                    }
                }
            }
            
            //funzione che richiama il corretto goToAction in base al player attivo
            function goToAction(start, end, tempo, minutoInizio, durataAzione, nomeSquadra, playerTrim, descAzione, idevent, idVideo, videoName, videoPath, provider, idFixture, tactStart) {
                tactStartTime = parseInt(tactStart) / 1000;
                actionStartTime = parseInt(start);
                var tact = false;
                var hd = false;
                var minVideoQuality = -1;
            <c:forEach items="${mGame}" var="game" varStatus="status" >
                if ("${game.value.idFixture}" == idFixture) {
                    tact = ${game.value.tacticalVideo && !personalSource};
                    hd = ${game.value.hd && !personalSource};
                    minVideoQuality = ${game.value.minVideoQuality};
                    currentGameId = ${game.value.idFixture};
                }
            </c:forEach>
                var clipDuration = parseInt(end - start);
//                // imposto lo slider delle azioni ( valore massimo + valore iniziale )
//                var seekSlider = document.getElementById('seek-slider');
//                seekSlider.max = clipDuration * 2;
//                seekSlider.value = 0;
//                seekSlider.dataset.initVideo = start;
                
                if (tact) {
                    $("#btnTACT").removeClass("uk-hidden");
                    if (tactActive && tactStart != 'undefined' && tactStart != '' && tactStart != "-1") {
                        start = tactStartTime;
                        end = tactStartTime + clipDuration;
                    }
                } else {
                    if (!$("#btnTACT").hasClass("uk-hidden")) {
                        $("#btnTACT").addClass("uk-hidden");
                    }
                }
                if (hd && minVideoQuality === 0) {
                    $("#btnHD").removeClass("uk-hidden");
                } else {
                    if (!$("#btnHD").hasClass("uk-hidden")) {
                        $("#btnHD").addClass("uk-hidden");
                    }
                }
                //$("#btnHD").addClass("uk-hidden");
                $('#playerType').html(provider);
                //initVideo(provider, videoName, idVideo); per multipartita
                curVideo = videoName;
                loadVideo(videoPath, hd, tact && tactActive, false);

                var splitDesc = descAzione.split("#");
                var totActionToPlay = 0;
                if (isRunningOnChecked) {
                    totActionToPlay = checkedActions.length;
                } else {
                    totActionToPlay = selectedActions.length;
                }
                if (totActionToPlay == 0) {
                    totActionToPlay = 1;
                }
                var intestazione = $("#clipXdiY").html().replace("X", (indexActionToPlay + 1)).replace("Y", totActionToPlay);
                //"Clip "+ (indexActionToPlay + 1) +" di "+ totActionToPlay;
                $("#spanNameAction").html(intestazione + "<strong>" + splitDesc[0] + "</strong>" + (splitDesc[1] !== "" ? ": " : "") + "<span class='tag'>" + splitDesc[1] + "</span>, " + splitDesc[2] + "<span id='timeToEnd'></span>");
                var tactStartOk = true;
                console.log("DIM: " +${mEventAll.size()});
                if ((tact && tactActive && tactStartOk) || (!tact && tactActive) || !tactActive) {
                    goToActionHTML(start, end, tempo, minutoInizio, durataAzione, nomeSquadra, playerTrim, descAzione, idevent);
                }
            }

            function startAction(id, index, runCheckedActions) {
                if (typeof event !== "undefined" && typeof event.target !== "undefined" && event.target) {
                    if ($(event.target).hasClass("skip-start-action")) {
                        return;
                    }
                }
            
                // ha la classe nel momento in cui clicco la X di fianco alla barra delle clip
                var isFirstClip = false;
                if (indexActionToPlay === -1) {
                    indexActionToPlay = 0;
                    isFirstClip = true;
                }
                
                if (typeof runCheckedActions != 'undefined' && runCheckedActions) {
                    isRunningOnChecked = true;
                } else {
                    isRunningOnChecked = false;
                }
                
                var actionsArray = [];
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                    //console.log("checkedActions");
                } else {
                    actionsArray = selectedActions;
                    //console.log("selectedActions");
                }
                
                // controllo se ho cliccato su una riga che ha il checked
                if (typeof id != 'undefined' && id !== "") {
                    if (checkedActions.includes(id)) {
                        isRunningOnChecked = true;
                    }
                    
                    // controllo se devo mettere il tattico
                    checkIfNeededToEnableTactical(id);
                } else {
                    var tmpId = actionsArray[indexActionToPlay];
                    // controllo se devo mettere il tattico
                    checkIfNeededToEnableTactical(tmpId);
                }
                
                $("#spanNameAction").removeClass("uk-hidden");
                $('.currentClip').removeClass("currentClip");
                // verifico se devono ancora essere creati gli elementi
                drawEventsIfNeeded();
                enableSwitchAction();   // riattiva visualizzazione clip
                checkModality();
                if (id === "") {
                    checkIfNeededToChangePage();
                    //console.log("indexActionToPlay: " + indexActionToPlay);
                    if (actionsArray.length > 0) {
                        $("input[id^=infoSearch] + i").addClass("uk-hidden");
                        indexActionToPlay = index;
                        $('#rowEventId' + actionsArray[indexActionToPlay] +' td').addClass("currentClip");
                        highlightElementById("" + actionsArray[indexActionToPlay]);
                        updateTooltipSelectedActions("");
                        checkMarkersAndLogo();
                        
                        var eventData = eventMap.get("" + actionsArray[indexActionToPlay]);
                        goToAction($.trim(eventData.mStart / 1000), $.trim(eventData.mEnd / 1000),
                                $.trim(eventData.tempoGioco), $.trim(eventData._period_start_minute),
                                $.trim(eventData.durataAzione), $.trim(eventData.mTeam),
                                $.trim(eventData.mPlayer.toLowerCase()), $.trim(eventData.descrAzione), $.trim(eventData.mIdEvent),
                                $.trim(eventData.videoId), $.trim(eventData.videoName), $.trim(eventData.videoPathS3),
                                $.trim(eventData.provider), $.trim(eventData.idFixture), $.trim(eventData.mTactStart));
                                
                        checkPersonalEvent(eventData);
                    }
                } else {
                    var previousIndexActionToPlay = indexActionToPlay;
                    indexActionToPlay = actionsArray.indexOf(id);
                    //console.log("indexActionToPlay: " + indexActionToPlay + " for id: " + id);
                    checkIfNeededToChangePage();
                    $('#rowEventId' + actionsArray[indexActionToPlay] +' td').addClass("currentClip");
                    highlightElementById("" + actionsArray[indexActionToPlay]);
                    updateTooltipSelectedActions("");
                    checkMarkersAndLogo();
                    
                    // se riclicco sulla stessa torno solo ad inizio video
                    if (!isFirstClip && actionsArray[previousIndexActionToPlay] === id) {
                        if (videoJsPlayer._offsetEnd > 0) {
                            videoJsPlayer.currentTime(0);
                        } else {
                            var eventData = eventMap.get("" + actionsArray[indexActionToPlay]);
                            goToAction($.trim(eventData.mStart / 1000), $.trim(eventData.mEnd / 1000),
                                    $.trim(eventData.tempoGioco), $.trim(eventData._period_start_minute),
                                    $.trim(eventData.durataAzione), $.trim(eventData.mTeam),
                                    $.trim(eventData.mPlayer.toLowerCase()), $.trim(eventData.descrAzione), $.trim(eventData.mIdEvent),
                                    $.trim(eventData.videoId), $.trim(eventData.videoName), $.trim(eventData.videoPathS3),
                                    $.trim(eventData.provider), $.trim(eventData.idFixture), $.trim(eventData.mTactStart));
                        }
                    } else {
                        var eventData = eventMap.get("" + actionsArray[indexActionToPlay]);
                        goToAction($.trim(eventData.mStart / 1000), $.trim(eventData.mEnd / 1000),
                                $.trim(eventData.tempoGioco), $.trim(eventData._period_start_minute),
                                $.trim(eventData.durataAzione), $.trim(eventData.mTeam),
                                $.trim(eventData.mPlayer.toLowerCase()), $.trim(eventData.descrAzione), $.trim(eventData.mIdEvent),
                                $.trim(eventData.videoId), $.trim(eventData.videoName), $.trim(eventData.videoPathS3),
                                $.trim(eventData.provider), $.trim(eventData.idFixture), $.trim(eventData.mTactStart));
                                
                        checkPersonalEvent(eventData);
                    }
                }
                
                checkTabellino();
            }
            
            function checkPersonalEvent(eventData) {
                if (eventData._idType === 1156) {
                    // mostro i tasti per modificare/eliminare le clip personali
                    $("#edit-rec-button").removeClass("uk-hidden");
                    $("#remove-rec-button").removeClass("uk-hidden");
                } else {
                    $("#edit-rec-button").addClass("uk-hidden");
                    $("#remove-rec-button").addClass("uk-hidden");
                }
            }
            
            function checkTabellino() {
                setTimeout(function () { // serve per quando clicchi sul tasto "Tabellino"
                    if (typeof currentGameId !== 'undefined') {
                        if ($("#liTabellino").hasClass("uk-active")) {
                            if ($("#containerTabellino").length === 0) {
                                generateTabellino();
                            } else {
                                var currentTabellinoGameId = $("#mTabellinoGameId").val();
                                if (currentTabellinoGameId !== "" + currentGameId + "") { // stringa
                                    generateTabellino();
                                }
                            }
                        }
                    } else {
                        UIkit.notify("Unexpected Error. gameId not validated.", {status: 'warning', timeout: 1000});
                    }
                }, 50);
            }
            
            function generateTabellino() {
                jsLoadingAlpha("#divTabellinoContent");
                $.ajax({
                    type: "GET",
                    url: "/sicstv/user/generateTabellino.htm",
                    data: "idGame=" + currentGameId,
                    cache: false,
                    success: function (msg) {
                        jsLoadingOff("#divTabellinoContent");
                        $("#divTabellinoContent").html(msg);
                    }
                });
            }

            function checkIfNeededToEnableTactical(id) {
                if (tacticalEventIds.length > 0) {
                    if (tacticalEventIds.includes(id)) {
                        if (!$("#btnTACT").hasClass("HDon")) {
                            switchTACT();
                        }
                    } else { // per tutte le clip che non sono tattico allora disattivo
                        if ($("#btnTACT").hasClass("HDon")) {
                            switchHD();
                        }
                    }
                }
            }
            
            function exportActions() {
                var removeDuplicates = new URL(document.URL).searchParams.get("removeDuplicates");
                if (checkedActions.length <= 250 || (typeof removeDuplicates !== 'undefined' && removeDuplicates !== null)) {
                    $("#exportActionsButton").prop("disabled", "disabled");
                    var actionSelected = "";
//                    $.each(checkedActions, function (index, value) {
//                        var eventData = eventMap.get("" + value);
//                        actionSelected += "-_-" + (eventData.idFixture + "||" + eventData.mIdEvent);
                        
//                        var params = [];
//                        actionTable.rows({ filter: 'applied' }).every(function() {
//                            if (params.length == 0) {
//                                var eventId = $(this.node()).attr("id").replace("rowEventId", "");
//                                var eventData = eventMap.get("" + eventId);
//                                
//                                var data = this.data();
//                                if (value === eventId) {
//                                    params = data[actionTable.columns().nodes().length - 2].split("#");
//                                }
//                            } else {
//                                return;
//                            }
//                        });
//                        if (checkedActions !== "") {
//                            actionSelected += "-_-" + params[5];
//                        } else {
//                            actionSelected += params[5];
//                        }
//                    });
                    
                    // ora prima di iniziare rimetto le azioni nell'ordine giusto
                    // se ad esempio seleziono X clip e poi cambio l'ordinamento di una colonna
                    // viene tenuto l'ordine di prima: quindi qua controllo tutte le righe
                    actionTable.rows({ filter: 'applied' }).every(function() {
                        var eventId = $(this.node()).attr("id").replace("rowEventId", "");
                        var value = eventMap.get("" + eventId);

                        var id = "" + value._id;
                        var isChecked = checkedActions.includes(id);

                        if (isChecked) {
                            var eventData = eventMap.get("" + id);
                            actionSelected += "-_-" + (eventData.idFixture + "||" + eventData.mIdEvent);
                        }
                    });

                    var tactEnable = "";
                    var multi = $("#exportmulti:checked").length > 0;
                    var hd = $("#exportHD:checked").length > 0;
                    var tact = $("#exportTACT:checked").length > 0;
                    //l'apice manda in errore l'esportazione, per il momento lo tolgo
                    var namefile = $("#nameFileExport").val().replace("'", "");
                    var isTempoEffettivo = false;
                    if (typeof removeDuplicates !== 'undefined' && removeDuplicates !== null) {
                        isTempoEffettivo = true;
                    }
                    var ind = 0;
            <c:forEach var="game" items="${mGame}">
                    if (ind == 0) {
                        tactEnable = "${game.value.idFixture}" + "," + "${game.value.tacticalVideo}";
                    } else {
                        tactEnable = tactEnable + "," + "${game.value.idFixture}" + "," + "${game.value.tacticalVideo}";
                    }
                    ind = ind + 1;
            </c:forEach>

                    UIkit.notify("<spring:message code='menu.user.export.convalidazione'/>...", {status: 'success', timeout: 0});
                    $.ajax({
                        type: "POST",
                        url: "/sicstv/user/esportaAzioni.htm",
                        //data: encodeURI("selectedActions=" + actionSelected + "&tactEnable=" + tactEnable + "&multi=" + multi + "&hd=" + hd + "&tact=" + tact + "&namefile=" + namefile),
                        data: encodeURI("selectedActions=" + actionSelected + "&tactEnable=" + tactEnable + "&multi=" + multi + "&tact=" + tact + "&namefile=" + namefile + "&isTempoEffettivo=" + isTempoEffettivo + "&askedMaxQuality=" + $("#exportMaxQuality").is(":checked")),
                        cache: false,
                        success: function (id) {
                            UIkit.notify.closeAll();
                            if (id !== "" && id !== "tooMany" && id !== "limitReached" && id !== "noAccess") {
                                if (typeof timerProgressStatus != 'undefined') {
                                    timerProgressStatus.stop();
                                }
                                if (typeof sessionStorage['exportIdentifier'] == 'undefined') {
                                    sessionStorage['exportIdentifier'] = "";
                                }

                                sessionStorage['exportIdentifier'] = sessionStorage['exportIdentifier'] + "-" + id;
                                timerProgressStatus = $.timer(function () {
                                    if (!${mExportUseLambda}) {
                                        $.ajax({
                                            type: "GET",
                                            url: "/sicstv/user/isExportRunning.htm",
                                            data: encodeURI("identifier=" + sessionStorage['exportIdentifier']),
                                            cache: false,
                                            success: function (msg) {
                                                if (msg === "true") {
                                                    $("#exportRunningNotify").removeClass("uk-hidden");
                                                } else {
                                                    $("#exportRunningNotify").addClass("uk-hidden");
                                                    updateUnseenPlaylist();
                                                    timerProgressStatus.stop();
    //                                                UIkit.notify("<spring:message code='playlist.download.end'/>", {status: 'success', timeout: 1000});

                                                    if (msg !== "false" && typeof sessionStorage['exportIdentifier'] !== "undefined") {
                                                        var strUrl = "/sicstv/user/downloadPlaylist.htm?id=" + msg;
                                                        $.ajax({
                                                            type: "GET",
                                                            url: strUrl,
                                                            contentType: "application/force-download",
                                                            cache: false,
                                                            success: function (msg) {
                                                                if (msg.substr(0, 4) === "true") {
                                                                    UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                                                                } else {
                                                                    UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                                                                    window.location.replace(msg.substr(5));
                                                                    $.unblockUI();
                                                                }
                                                            }
                                                        });
                                                        sessionStorage.removeItem('exportIdentifier');
                                                    }
                                                }
                                            }
                                        });
                                    } else {
                                        $.ajax({
                                            type: "GET",
                                            url: "/sicstv/user/isExportRunningLambda.htm",
                                            cache: false,
                                            success: function (exportInfo) {
                                                UIkit.notify.closeAll();
                                                if (exportInfo.trim().length > 0) {
                                                    var scriptToExecute = exportInfo.substring(exportInfo.indexOf('// START') + 8, exportInfo.indexOf('// END')).trim();
                                                    if (scriptToExecute && scriptToExecute.length > 0) {
                                                        eval(scriptToExecute);
                                                    }
                                                    
                                                    $("#exportRunningNotify").removeClass("uk-hidden");
                                                    if (typeof exportTippyElement === 'undefined') {
                                                        exportTippyElement = tippy("#exportRunningNotify", {
                                                            theme: 'light-border',
                                                            content: exportInfo,
                                                            maxWidth: 500,
                                                            allowHTML: true,
                                                            placement: 'bottom'
                                                        });
                                                    } else {
                                                        exportTippyElement.forEach(function(element) {
                                                            element.setContent(exportInfo);
                                                        });
                                                    }
                                                } else {
                                                    $("#exportRunningNotify").addClass("uk-hidden");
                                                    updateUnseenPlaylist();
                                                    timerProgressStatus.stop();
                                                }
                                            }
                                        });
                                    }
                                });
                                timerProgressStatus.set({time: 2000, autostart: true});
                                
                                jsReloadUserLimits(); // ricarico i limiti
                            } else if (id === "tooMany") {
                                UIkit.notify("<spring:message code='playlist.download.limit.reached'/>.<br><spring:message code='playlist.download.limit.reached.solution'/>", {status: 'danger', timeout: 2500});
                            } else if (id === "limitReached") {
                                UIkit.notify("<spring:message code='playlist.export.limit.reached'/>", {status: 'danger', timeout: 2500});
                            } else if (id === "noAccess") {
                                UIkit.notify("<spring:message code='menu.user.no.access'/>", {status: 'danger', timeout: 2500});
                            }
                            return true;
                        },
                        async: true
                    });
                } else if (checkedActions.length > 250) {
                    UIkit.notify("<spring:message code='playlist.download.limit.reached'/>.<br><spring:message code='playlist.download.limit.reached.solution'/>", {status: 'danger', timeout: 2500});
                }
            }
            
            function addToPlaylist(playlistId) {
                if (checkedActions.length > 0) {
                    var name = $('#namePlaylist').val();
                    var description = $('#descriptionPlaylist').val();
                    if (name === "" && typeof playlistId === 'undefined') {
                        UIkit.notify("<spring:message code='playlist.add.failed.no.name'/>", {status: 'warning', timeout: 1000});
                        return;
                    }
                    if (typeof playlistId === 'undefined') {
                        playlistId = "";
                    }
                    
                    var date = new Date();
                    var options = {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit',
                      second: '2-digit',
                      hour12: false
                    };
                    var formatter = new Intl.DateTimeFormat('it-IT', options);
                    var formattedDate = formatter.format(date);
                    jsShowBlockUI();
                    $.ajax({
                        type: "POST",
                        url: "/sicstv/user/creaPlaylistTv.htm",
                        data: encodeURI("name=" + name + "&description=" + description + "&eventIds=" + checkedActions + "&playlistId=" + playlistId + "&creationDate=" + formattedDate.replace(',', '') + "&tactical=" + tactActive + "&currentPlaylistId=" + $("#mPlaylistTvId").val()),
                        cache: false,
                        success: function (result) {
                            $.unblockUI();
                            if (typeof result !== 'undefined') {
                                if (result === 'noAccess') {
                                    UIkit.notify("<spring:message code='playlist.add.no.access'/>", {status: 'warning', timeout: 1000});
                                } else if (result === 'limitExceeded') {
                                    UIkit.notify("<spring:message code='playlist.add.failed.limit'/>", {status: 'warning', timeout: 1000});
                                } else if (result !== 'false') {                                    
                                    // controllo se devo aggiungere nuovo bottone
                                    if (result.includes("|")) {
//                                        var splitted = result.split("|");
//                                        var newButton = document.createElement("button");
//                                        newButton.setAttribute("onclick", "addToPlaylist('" + splitted[1] + "')");
//                                        newButton.className = "uk-button uk-dropdown-close playlistTvButton";
//                                        newButton.textContent = splitted[0];
//
//                                        var wrapper = document.getElementById("playlistTvButtonDiv");
//                                        wrapper.appendChild(newButton);
                                        UIkit.notify("<spring:message code='playlist.add.created.success'/>", {status: 'success', timeout: 1000});
                                    } else {
                                        UIkit.notify("<spring:message code='playlist.add.success'/>", {status: 'success', timeout: 1000});
                                    }
                                    
                                    // reset status
                                    $('#namePlaylist').val("");
                                    $('#descriptionPlaylist').val("");
                                    emptyCheckedActions();
                                    actionTable.page(actionTable.page()).draw(false);
                                    jsRefreshMatches('1'); // aggiorno tab playlist
                                    jsReloadUserLimits(); // ricarico i limiti
                                } else {
                                    UIkit.notify("<spring:message code='playlist.add.failed'/> (0xB)", {status: 'danger', timeout: 1000});
                                }
                            } else {
                                UIkit.notify("<spring:message code='playlist.add.failed'/> (0xA)", {status: 'danger', timeout: 1000});
                            }
                        }
                    });
                } else {
                    UIkit.notify("<spring:message code='playlist.add.no.clip'/>", {status: 'warning', timeout: 1000});
                }
            }

            function jsRefreshMatches(page) {
                var competition = "";
                var team = "";
                if (${not empty mCompetition.id}) {
                    competition = "${mCompetition.id}";
                }
                if (${not empty mTeam.id}) {
                    team = "${mTeam.id}";
                }
                refreshMatches('matches', 'partite', page, competition, team, null, null, true);
            }

            function switchMatchday(step) {

                var valDay = parseInt($("#mDay").val());
                if (step === "prev") {
                    valDay = valDay - 1;
                } else if (step === "next") {
                    valDay = valDay + 1;
                } else {
                    valDay = step;
                }
                
                if (valDay >= 1 && valDay <= parseInt($("#mMaxDay").val())) {
                    //jsLoadingAlpha("#matchDayCalendar");
                    $("#mDay").val(valDay);
                    jsRefreshMatchesCalendar($("#mCompetitionId").val());
                }
            }

            function jsSetButtonSelected(id, type, idBase, comment) {
                /**
                 * type = 0  ->  fondamentale
                 * type = 1  ->  tag
                 * type = 2  ->  squadre 
                 * type = 3  ->  giocatori
                 * type = 4  ->  partite
                 * type = 5  ->  quickSearch
                 * type = 6  ->  autore
                 * type = 7  ->  posizione
                 * type = 8  ->  angolo
                 * type = 9  ->  posizione porta
                 * type = 10 ->  azioni fatte / subite
                 */
                
                // tolgo tutti i tasti selezionati del quick search dato che sto filtrando per altre cose
                if (!$("#liRicerca").hasClass("uk-active") && !$("#liFiltriTactical").hasClass("uk-active")) {
                    $("#quickSearchDiv").find("button.uk-button-active").removeClass("uk-button-active");
                }
                
                // re-imposto la tab "Azioni" come selezionata se non lo è già
                if (!$("#liPosizionale").hasClass("uk-active") && !$("#liPosizionaleShots").hasClass("uk-active")) { // per posizionale lascio tab
                    if (!$("#liActions").hasClass("uk-active")) {
                        $("#liActions").click();
                    }
                }

                // resetto le azioni selezionate
                emptyCheckedActions();
                //console.log("id: " + id + " type: " + type + " idBase: " + idBase + " comment: " + comment)
                $("#numCheckedEvents").html(0);
                $("#checkedActionsButton").addClass("uk-hidden");
                $("#checkboxSelectAllEvents").prop("checked", false);

                var isTacticalClick = id.startsWith("TACT-");
                if (isTacticalClick) {
                    id = id.replace("TACT-", "");
                }

                //console.log(id + " - " + type + " - " + idBase);
                if (openTag) {
                    openTag = false;
                } else if (removeFilter) {
                    removeFilter = false;
                    jsRemoveFilter(id, type);
                    applyFilterEvents(true, type !== 3);

                    updateSelectedActionsArray(true);
                    startAction('', 0, isRunningOnChecked);
                } else {
                    var tmpId = id;
                    var splitted = tmpId.split("_");
                    if (splitted.length > 2) { // cioè ho tipo SICS2015_ASS_ASS-0
                        tmpId = splitted[0] + "_" + splitted[1];
                    }

                    if ($("#" + tmpId).hasClass('uk-button-success')) {
                        $("#" + tmpId).removeClass("uk-button-success");
                        $("#TACT-" + tmpId).removeClass("uk-button-success");
                        if (type === 2 || type === 3) {
                            $("." + tmpId).removeClass("uk-button-success");
                        }
                        if (type === 2) {
                            $("#quickChoice" + tmpId).removeClass("uk-button-success");
                        }
                    } else {
                        $("#" + tmpId).addClass("uk-button-success");
                        $("#TACT-" + tmpId).addClass("uk-button-success");
                        if (type === 2 || type === 3) {
                            $("." + tmpId).addClass("uk-button-success");
                        }
                        if (type === 2) {
                            $("#quickChoice" + tmpId).addClass("uk-button-success");
                        }
                    }

                    var val = $("#" + id).val();
                    if (type === 0) {
                        // evento
                        // determino se il bottone cliccato è presente o no tra i filtri
                        if ($.inArray(id, eventSelected) === -1) {
                            eventSelected.push(id);
                            $("[id^=" + id + "_]").each(function (i, obj) {
                                if ($.inArray(obj.id, eventSelected) === -1) {
                                    eventSelected.push(obj.id);
                                    if (typeof $("#" + obj.id).attr("disabled") === 'undefined') {
                                        $("#" + obj.id).attr("checked", "checked");
                                    }
                                }
                            });
                            if (isTacticalClick) {
                                $("[id^=TACT-" + id + "_]").each(function (i, obj) {
                                    if ($.inArray(obj.id, eventSelected) === -1) {
                                        eventSelected.push(obj.id);
                                        if (typeof $("#" + obj.id).attr("disabled") === 'undefined') {
                                            $("#" + obj.id).attr("checked", "checked");
                                        }
                                    }
                                });
                            }
                        } else {
                            eventSelected.splice($.inArray(id, eventSelected), 1);
                            $("[id^=" + id + "_]").each(function (i, obj) {
                                if ($.inArray(obj.id, eventSelected) !== -1) {
                                    eventSelected.splice($.inArray(obj.id, eventSelected), 1);
                                    $("#" + obj.id).removeAttr("checked");
                                }
                            });
                            if (isTacticalClick) {
                                $("[id^=TACT-" + id + "_]").each(function (i, obj) {
                                    if ($.inArray(obj.id, eventSelected) !== -1) {
                                        eventSelected.splice($.inArray(obj.id, eventSelected), 1);
                                        $("#" + obj.id).removeAttr("checked");
                                    }
                                });
                            }
                        }
                        updateNumEventAndTagCount(id);

                    } else if (type === 1) {
                        //tag
                        if ($.inArray(id, eventSelected) === -1) {
                            eventSelected.push(id);
                        } else {
                            eventSelected.splice($.inArray(id, eventSelected), 1);
                        }
                        // per tattico
                        if (isTacticalClick) {
                            $("#" + id).attr("checked", !$("#" + id).attr("checked"));
                        } else {
                            $("#TACT-" + id).attr("checked", !$("#TACT-" + id).attr("checked"));
                        }

                        updateNumEventAndTagCount(id);

                    } else if (type === 2) {
                        // squadra
                        if ($.inArray(val, teamSelected) === -1) {
                            teamSelected.push(val);
                        } else {
                            teamSelected.splice($.inArray(val, teamSelected), 1);
                        }

                        $("#numFilterPlayerTeam").html(teamSelected.length + playerSelected.length);
                        if (teamSelected.length + playerSelected.length > 0) {
                            $("#numFilterPlayerTeam").removeClass("uk-hidden");
                        } else {
                            $("#numFilterPlayerTeam").addClass("uk-hidden");
                        }
                        
                        if (teamSelected.length === 2) {
                            // se entrambe selezionate mostro visualizzazione base
                            jsRemoveFilter('', 2);
                        }
                    } else if (type === 3) {
                        //giocatore
                        if ($.inArray(val, playerSelected) === -1) {
                            playerSelected.push(val);
                        } else {
                            playerSelected.splice($.inArray(val, playerSelected), 1);
                        }
                        $("#numFilterPlayerTeam").html(teamSelected.length + playerSelected.length);
                        if (teamSelected.length + playerSelected.length > 0) {
                            $("#numFilterPlayerTeam").removeClass("uk-hidden");
                        } else {
                            $("#numFilterPlayerTeam").addClass("uk-hidden");
                        }
                        
                        // se ho filtro team devo toglierlo
                        if (teamSelected.length > 0) {
                            var elementClasses = $("#" + id).attr("class").split(" ");
                            var playerTeamId;
                            elementClasses.forEach(function(value, index) {
                                if (value.startsWith("teamId")) {
                                    playerTeamId = value.replace("teamId", "");
                                    return false;
                                }
                            });
                            
                            if (typeof playerTeamId !== 'undefined' && playerTeamId) {
                                jsSetButtonSelected('team' + playerTeamId, 2);
                            }
                        }
                    } else if (type === 4) {
                        // partite
                        if (typeof comment !== 'undefined') {
                            if (comment === 'all') {
                                gamesSelected = [];
                                document.querySelectorAll(".partiteSelezionate").forEach(function (element) {
                                    var id = element.id
                                    var val = $('#' + id).val();
                                    $('#' + id).addClass("uk-button-success");
                                    gamesSelected.push(val);
                                });
                            } else if (comment === 'none') {
                                gamesSelected = [];
                                document.querySelectorAll(".partiteSelezionate").forEach(function (element) {
                                    var id = element.id
                                    $('#' + id).removeClass("uk-button-success");
                                });
                            }
                        } else {
                            if ($.inArray(val, gamesSelected) === -1) {
                                gamesSelected.push(val);
                            } else {
                                gamesSelected.splice($.inArray(val, gamesSelected), 1);
                            }
                        }

                        $("#numMatchSelected").html(gamesSelected.length);
                        if (gamesSelected.length > 0) {
                            $("#numMatchSelected").removeClass("uk-hidden");
                        } else {
                            $("#numMatchSelected").addClass("uk-hidden");
                        }
                    } else if (type === 7) {
                        // posizione
                        if (typeof comment !== 'undefined') {
                            if ($.inArray(comment, posizioniSelected) === -1) {
                                posizioniSelected.push(comment);
                            } else {
                                posizioniSelected.splice($.inArray(comment, posizioniSelected), 1);
                            }
                        } else {
                            console.warn("posizione can't be found. comment is undefined");
                        }
                        drawBackgroundArrow(pitch.baseLayer, pitch);
                    } else if (type === 8) {
                        // angolo
                        if (typeof comment !== 'undefined') {
                            if ($.inArray(comment, angoliSelected) === -1) {
                                angoliSelected.push(comment);
                            } else {
                                angoliSelected.splice($.inArray(comment, angoliSelected), 1);
                            }
                        } else {
                            console.warn("angolo can't be found. comment is undefined");
                        }
                        drawBackgroundArrow(pitch.baseLayer, pitch);
                    } else if (type === 9) {
                        // posizione porta
                        if (typeof comment !== 'undefined') {
                            if ($.inArray(comment, posizioniPortaSelected) === -1) {
                                posizioniPortaSelected.push(comment);
                            } else {
                                posizioniPortaSelected.splice($.inArray(comment, posizioniPortaSelected), 1);
                            }
                        } else {
                            console.warn("posizione porta can't be found. comment is undefined");
                        }
                    }

                    // scrivo gli eventi sulla tab giocatori
                    // resetto prima il testo dei tasti
                    $(".teamAllButton").each(function () {
                        var htmlContent = $(this).html();
                        if (htmlContent.includes("(")) {
                            $(this).html(htmlContent.substring(0, htmlContent.indexOf("(")));
                            $(this).attr("title", htmlContent.substring(0, htmlContent.indexOf("(")));
                        }
                    });
                    $("#listAzioneRicerca").find(".uk-button-success").removeClass("uk-button-success");
                    $("#liFiltriTacticalContent").find(".uk-button-success").removeClass("uk-button-success");

                    if (eventSelected.length > 0) {
                        var validFilters = 0;
                        var filterString = "", allFilterString = "";
                        var alreadyAdded = [];
                        eventSelected.forEach(function (element) {
                            var tmpId = element;
                            var splitted = tmpId.split("_");
                            if (splitted.length > 2) { // cioè ho tipo SICS2015_ASS_ASS-0
                                tmpId = splitted[0] + "_" + splitted[1];
                            }

                            if ($("#" + tmpId).is("button")) { // extra check
                                $("#" + tmpId).addClass("uk-button-success");
                                $("#TACT-" + tmpId).addClass("uk-button-success");
                                var baseName = $("#" + tmpId).attr("base-name");
                                if (typeof baseName !== "undefined") {
                                    baseName = baseName.replaceAll("  ", " ");
                                    if (!alreadyAdded.includes(baseName)) {
                                        alreadyAdded.push(baseName);
                                        if (typeof baseName !== 'undefined') {
                                            allFilterString += (allFilterString === "" ? "" : ", ") + baseName;
                                            if (validFilters < 3) {
                                                filterString += (filterString === "" ? "" : ", ") + baseName;
                                                validFilters++;
                                            } else {
                                                if (!filterString.includes("...")) {
                                                    filterString += "...";
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        });

                        $(".teamAllButton").each(function () {
                            var htmlContent = $(this).html();
                            $(this).html($(this).html() + "(" + filterString + ")");
                            $(this).attr("title", "(" + allFilterString + ")");
                        });
                    }
                    
                    if (typeof comment !== "undefined" && comment === "skipApply") {
                        return;
                    }
                    
                    applyFilterEvents(type === 0, type !== 3);
                    // posizionale
                    updateSelectedActionsArray(true);
                    drawEvents();
                    // se non ci sono filtri allora torno al video
                    var currentTableRows = actionTable.rows({filter: 'applied'})[0].length;
                    var tableRows = actionTable.rows()[0].length;
                    // aggiungo anche controllo per verificare se c'è qualche tasto attivato
                    var quickFiltersAmount = $("#btnFiltriRapidi .uk-button-success").length;
                    var otherFiltersAmount = $("#bottomNavContent .uk-button-success").length;
                    
                    if (currentTableRows === tableRows && !filteredRequest && quickFiltersAmount === 0 && otherFiltersAmount === 0) {
                        backToVideoMode();
                    } else {
                        startAction('', 0, isRunningOnChecked);
                    }
                }
            }

            // aggionare il numero di tag ed eventi selezionati
            function updateNumEventAndTagCount(id) {
                // se è click su un tag allora vado ad incrementare il contatore dell'evento e non quello totale
                var split = id.split("_");
                var key = split[0] + "_" + split[1];
                var numTag = 0;
                var numEvent = [];
                $.each(eventSelected, function (index, value) {
                    var splittedVal = value.split("_");
                    var keyTag = splittedVal[0] + "_" + splittedVal[1];
                    // determino quanti tag ho selezionato in base ai primi due valori di splittedVal e la key
                    if (splittedVal.length === 3 & key === keyTag) {
                        numTag += 1;
                    }
                    if ($.inArray(keyTag, numEvent) === -1) {
                        numEvent.push(keyTag);
                    }
                });
                //$("#numTagSelected" + key).html(numTag);
                if (numTag > 0) {
                    $("#numTagSelected" + key).removeClass("uk-hidden");
                } else {
                    $("#numTagSelected" + key).addClass("uk-hidden");
                }
                $("#numFilterEvents").html(numEvent.length);
                if (numEvent.length > 0) {
                    $("#numFilterEvents").removeClass("uk-hidden");
                } else {
                    $("#numFilterEvents").addClass("uk-hidden");
                }
            }

            function resetFilter(resetAll) {
                if (resetAll) {
                    $("#timeFirstButton").removeClass("uk-button-success");
                    $("#timeSecondButton").removeClass("uk-button-success");
                    $("#timeFirstQuarter").removeClass("uk-button-success");
                    $("#timeSecondQuarter").removeClass("uk-button-success");
                    $("#timeThirdQuarter").removeClass("uk-button-success");
                    $("#timeFourthQuarter").removeClass("uk-button-success");
                    // squadre
                    jsRemoveFilter('', 2);
                    // quick search (a mano perchè ci sono problemi)
                    $("#quickSearchDiv button").each(function () {
                        $(this).removeClass("uk-button-active");
                    });
                }
                // fondamentale
                jsRemoveFilter('', 1);
                // partite
                jsRemoveFilter('', 4);

                applyFilterEvents(true, true);
                $("#numEvents").addClass("uk-hidden");
                $("#numEventsSmall").addClass("uk-hidden");
                
                updateSelectedActionsArray(true);
                startAction('', 0, isRunningOnChecked);
                drawEvents();
            }

            function jsRemoveFilter(id, type) {
                /**
                 * type = 0 -> elimina tag
                 * type = 1 -> elimina fondamentale
                 * type = 2 -> elimina squadre 
                 * type = 3 -> elimina giocatori
                 * type = 4 -> elimina partite
                 * type = 5 -> elimina quickSearch
                 */

                if (type === 0) {
                    $("#" + id + " div").find("input:checkbox").each(function () {
                        if ($.inArray($(this).prop('id'), eventSelected) !== -1) {
                            eventSelected.splice($.inArray($(this).prop('id'), eventSelected), 1);
                        }
                        $(this).prop('checked', false);
                    });
                    $("#numTagSelected" + id).addClass("uk-hidden");
                    if (eventSelected.length === 0) {
                        $("#numFilterEvents").addClass("uk-hidden");
                    } else {
                        var numEvent = [];
                        $.each(eventSelected, function (index, value) {
                            var valsplit = value.split("_");
                            var key = valsplit[0] + "_" + valsplit[1];
                            if ($.inArray(key, numEvent) === -1) {
                                numEvent.push(key);
                            }
                        });
                        $("#numFilterEvents").html(numEvent.length);
                    }
                } else if (type === 1) {
                    eventSelected = [];
                    $("#listAzioneRicerca").find("button").each(function () {
                        $(this).removeClass("uk-button-success");
                        jsRemoveFilter($(this).prop("id"), 0);
                    });
                    $("#liFiltriTacticalContent").find("button").each(function () {
                        $(this).removeClass("uk-button-success");
                        jsRemoveFilter($(this).prop("id"), 0);
                    });
                    $("#numFilterEvents").html(0);
                    $("#numFilterEvents").addClass("uk-hidden");
                } else if (type === 2 || type === 3) {
                    teamSelected = [];
                    playerSelected = [];
                    $("#players").find("button").removeClass("uk-button-success");
                    $("#quickChoiceSpan").find("button").removeClass("uk-button-success");
                    $("#comboTeamSelection option:first").removeProp("selected");
                    $("#numFilterPlayerTeam").html(teamSelected.length + playerSelected.length);
                    $("#numFilterPlayerTeam").addClass("uk-hidden");
                } else if (type === 4) {
                    gamesSelected = [];
                    $("#divSelezionate").find("button").each(function () {
                        $(this).addClass("uk-button-success");
                        gamesSelected.push($(this).val());
                    });
                    $("#numMatchSelected").html(gamesSelected.length);
                    $("#numMatchSelected").removeClass("uk-hidden");
                } else if (type === 5) {
                    $("#quickSearchDiv button").each(function () {
                        $(this).removeClass("uk-button-success");
                    });
                }

            }

            function toggleTheatreMode() {
                // se è attiva la modalità cinema la tolgo
                if ($("#video").hasClass("uk-width-1-1")) {
                    $("#video").removeClass("uk-width-1-1");
                    $("#video").addClass("uk-width-5-10");
                    $("#bottomNav").appendTo("#video");
                    $("#bottomNav").addClass("uk-width-1-1");
                    $("#bottomNav").removeClass("uk-width-1-2");

                } else {
                    // se non è attiva la attivo
                    $("#video").addClass("uk-width-1-1");
                    $("#video").removeClass("uk-width-5-10");
                    $("#bottomNav").insertBefore("#sideNav");
                    $("#bottomNav").addClass("uk-width-1-2");
                    $("#bottomNav").removeClass("uk-width-1-1");
                }
            }

            function filterData() {
                readFilterMulti();
                filterMatches('partite', 1);
            }

            var isRunningOnChecked = false;
            var checkedActions = [];
            var selectedActions = [];
            var descrSelectedActions = [];
            function jsSelectAction(id) {
                //var descr = $("#descrToShow" + id).val();
                //var splitDesc = descr.split("#");
                //var description = splitDesc[0] + ": <span class='tag'>" + splitDesc[1] + "</span> " + splitDesc[2];
                if ($.inArray(id, selectedActions) === -1) {
                    selectedActions.push(id);
                    //descrSelectedActions.push(description);
                } else {
                    //descrSelectedActions.splice($.inArray(description, selectedActions), 1);
                    $("#checkboxSelectAllEvents").prop("checked", false);
                }
                // nuova gestione selezionati
                updateCheckedArray(id, false, false);
                verifyNextAndPreviousButton(); // se sono all'ultima azione e aggiungo una selezionata non mostra il tasto next attivo

                updateTooltipSelectedActions("");
            }

            function updateCheckedArray(idToCheck, allRows, allRowsCheckValue) {
                if (allRows && !allRowsCheckValue) {
                    emptyCheckedActions();
                }
                
                if (!allRows) {
                    var event = eventMap.get("" + idToCheck);
                    var id = "" + event._id;
                    
                    if (checkedActions.includes(id) == true && !allRowsCheckValue) {
                        checkedActions.splice(checkedActions.indexOf(id), 1);
                    } else {
                        checkedActions.push(id);
                    }
                    event.visible = (typeof event.visible === 'undefined' || !event.visible);
                    $("#checkbox" + id).prop("checked", event.visible);
                } else {
                    // prendo quello che è presente in tabella
                    actionTable.rows({ filter: 'applied' }).every(function() {
                        var eventId = $(this.node()).attr("id").replace("rowEventId", "");
                        var value = eventMap.get("" + eventId);
                        
                        var id = "" + value._id;
                        if (typeof idToCheck !== 'undefined' && (id === idToCheck || allRows)) {
                            if (allRowsCheckValue) { // se invece sto selezionando tutte le checkbox
                                if (checkedActions.includes(id) == false) { // se non è nella lista lo inserisco
                                    checkedActions.push(id);
                                }
                                value.visible = true;
                            } else {
                                value.visible = !value.visible;
                            }
                            $("#checkbox" + id).prop("checked", value.visible);
                        }
                    });
                }
                // in questo modo non passa tutte le righe che sono caricate nella datatable
                // ma fa solo il loop di quelle visibili e poi esce (sembrano essere in ordine quindi non dovrebbe creare problemi)
//                var canContinue = true;
//                actionTable.rows({ filter: 'applied' }).every(function() {
//                    if (!canContinue) {
//                        return false; // Interrompe l'iterazione e termina il ciclo
//                    }
//
//                    var idFound = false;
//                    var data = this.data();
//                    var id = data[actionTable.columns().nodes().length - 1];
//                    var isChecked = (data[actionTable.columns().nodes().length - 3] === 'true');
//
//                    if (typeof idToCheck !== 'undefined' && (id === idToCheck || allRows)) {
//                        if (allRows) {
//                            isChecked = allRowsCheckValue;
//                            data[actionTable.columns().nodes().length - 3] = allRowsCheckValue ? "true" : "false";
//                        } else {
//                            isChecked = !isChecked; // così funziona sia per check che per uncheck
//                            data[actionTable.columns().nodes().length - 3] = isChecked ? "true" : "false";
//                        }
//                        idFound = true;
//                        
//                        if (!allRows) { // se click singola checkbox controllo se devo aggiungere o togliere
//                            if (checkedActions.includes(id) == true && !allRowsCheckValue) {
//                                checkedActions.splice(checkedActions.indexOf(id), 1);
//                            } else {
//                                checkedActions.push(id);
//                            }
//                        } else {
//                            if (allRowsCheckValue) { // se invece sto selezionando tutte le checkbox
//                                if (checkedActions.includes(id) == false) { // se non è nella lista lo inserisco
//                                    checkedActions.push(id);
//                                }
//                            }
//                        }
//
//                        if (allRows) { // bisogna mettere il check alle righe visibili
//                            var isVisible = this.nodes().to$().is(':visible');
//                            if (typeof isVisible !== 'undefined') {
//                                $("#checkbox" + id).prop("checked", isChecked);
//                            }
//                        }
//                        // this.data(data).draw(); // altrimenti non aggiorna il valore nella tabella
//                    }
////                    if (typeof isChecked !== 'undefined') {
////                        if (checkedActions.includes(id, checkedActions) == true) {
////                            checkedActions.splice(checkedActions.indexOf(id), 1);
////                        } else {
////                            checkedActions.push(id);
////                        }
////                    }
//                    if (idFound && !allRows) {
//                        canContinue = false; // Imposta la variabile di controllo a false per interrompere il ciclo
//                        return false; // Esce dalla funzione di callback senza effetto sull'iterazione del ciclo
//                    }
//                });
                
                $("#numCheckedEvents").html(checkedActions.length);
                if (checkedActions.length == 0) {
                    $("#checkedActionsButton").addClass("uk-hidden");
                } else {
                    $("#checkedActionsButton").removeClass("uk-hidden");
                }
            }
            
            function emptyCheckedActions() {
                if (checkedActions.length > 0) {
                    checkedActions = [];
                    $("#numCheckedEvents").html(checkedActions.length);
                    if (checkedActions.length == 0) {
                        $("#checkedActionsButton").addClass("uk-hidden");
                    } else {
                        $("#checkedActionsButton").removeClass("uk-hidden");
                    }
                    // tolgo la spunta da tutte le checkbox
                    $(".squaredCheckboxEvent").find("input[type='checkbox']").removeAttr("checked");

                    // bisogna ora sistemare le righe impostando il valore a false
//                    actionTable.rows({filter: 'applied'}).every( function ( rowIdx, tableLoop, rowLoop ) {
//                        var eventId = $(this.node()).attr("id").replace("rowEventId", "");
//                        var eventData = eventMap.get("" + eventId);
//                        
//                        eventData.visible = false;
//                    });
                }
            }
            
            function updateTooltipSelectedActions(textTooltip) {
                if (textTooltip === "") {
                    $.each(descrSelectedActions, function (key, value) {
                        if (key >= indexActionToPlay) {
                            textTooltip += (key + 1) + ")" + value + "<br>";
                        }
                    });
                }
                $("#spanNameAction").attr("title", textTooltip);
                $("#spanNameAction").attr("data-cached-title", textTooltip);
            }

            function jsSelectAllEvents(element, addCheck, restart) {                
                var checkboxSelectAllEventsValue = $("#checkboxSelectAllEvents").prop("checked");
                jsShowBlockUI();
                setTimeout(function() {
                    updateCheckedArray(0, true, checkboxSelectAllEventsValue);
                    $.unblockUI();
                    updateSelectedActionsArray(false);

                    if (typeof restart !== 'undefined' && restart) {
                        startAction('', 0, true);
                    }
                }, 100);
            }
            
            function jsUpdateSelectedActions(element) {
                selectedActions = [];
                
                $("#" + element + " tbody").find("input:checkbox").each(function () {
                    if (!$(this).closest("tr").hasClass("uk-hidden")) {
                        var isChecked = $(this).prop("checked");
                        if (isChecked) {
                            jsSelectAction($(this).val());
                        }
                    }
                });
            }

            function emptySelectedActions() {
                $.each(selectedActions, function (key, value) {
                    $("#checkbox" + value).removeAttr("checked");
                    $("#checkbox" + value + "small").removeAttr("checked");
                });
                $("#checkboxSelectAllEvents").prop("checked", false);
                selectedActions = [];
            }

            function emptyAllActions() {
                $("#checkboxSelectAllEvents").prop("checked", false);
            }

            function jsDownloadExport(id) {

                var strUrl = "/sicstv/user/downloadPlaylist.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "true") {
                            UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                            window.location.replace(msg.substr(5));
                            $.unblockUI();
                        }
                    }
                });
                sessionStorage.removeItem('exportIdentifier');
            }

            function switchHD() {
                var tactBefore = tactActive;
                if ($("#btnHD").hasClass("HDon")) {
                    // se è in hd allora passo allo standard
                    hdActive = false;
                    $("#btnHD img").attr("src", "/sicstv/images/hd-dark.png");
                    $("#btnHD").removeClass("HDon");
                } else {
                    // se è sd passo all'HD
                    hdActive = true;
                    $("#btnHD img").attr("src", "/sicstv/images/hd-selected.png");
                    $("#btnHD").addClass("HDon");
                }
                tactActive = false;
                $("#btnTACT img").attr("src", "/sicstv/images/tact.png");
                $("#btnTACT").removeClass("HDon");
                var lastValue = 0;
                if (videoJsPlayer) {
                    lastValue = videoJsPlayer.currentTime();
                    if (tactBefore) {
                        lastValue = resyncTime(lastValue, false);
                    }
                }
                loadVideo(curVideoPath, hdActive, tactActive, true);
//                if (lastValue !== 0 && !tactBefore) {
//                    resumeVideoFromLastValue(lastValue);
//                }
                if (videoJsPlayer) {
                    if (inizioAzione && fineAzione && !isClipStopped) {
                        if (tactActive && tactStartTime) {
                            fineAzione = parseInt(tactStartTime) + (parseInt(fineAzione) - parseInt(inizioAzione));
                            inizioAzione = parseInt(tactStartTime);
                        } else {
                            fineAzione = parseInt(actionStartTime) + (parseInt(fineAzione) - parseInt(inizioAzione));
                            inizioAzione = parseInt(actionStartTime);
                        }
                        videoJsPlayer.offset({
                            start: inizioAzione,
                            end: fineAzione,
                            restart_beginning: false //Should the video go to the beginning when it ends
                        });
                    }
                    setTimeout(function() {
                        videoJsPlayer.currentTime(lastValue);
                    }, 500);
                }
            }

            function switchTACT() {
                if ($("#btnTACT").hasClass("HDon")) {
                    // se è in hd allora passo allo standard
                    tactActive = false;
                    hdActive = true;
                    $("#btnTACT img").attr("src", "/sicstv/images/tact.png");
                    $("#btnTACT").removeClass("HDon");
                } else {
                    // se è sd passo a TACT
                    tactActive = true;
                    hdActive = false;
                    $("#btnTACT img").attr("src", "/sicstv/images/tact-selected.png");
                    $("#btnTACT").addClass("HDon");
                }
                $("#btnHD img").attr("src", "/sicstv/images/hd-dark.png");
                $("#btnHD").removeClass("HDon");
                var lastValue = 0;
                if (videoJsPlayer) {
                    lastValue = videoJsPlayer.currentTime();
                    if (tactActive) {
                        lastValue = resyncTime(lastValue, true);
                    } else {
                        lastValue = resyncTime(lastValue, false);
                    }
                }
                loadVideo(curVideoPath, hdActive, tactActive, true);
                if (videoJsPlayer) {
                    if (inizioAzione && fineAzione && !isClipStopped) {
                        if (tactActive && tactStartTime) {
                            fineAzione = parseInt(tactStartTime) + (parseInt(fineAzione) - parseInt(inizioAzione));
                            inizioAzione = parseInt(tactStartTime);
                        } else {
                            fineAzione = parseInt(actionStartTime) + (parseInt(fineAzione) - parseInt(inizioAzione));
                            inizioAzione = parseInt(actionStartTime);
                        }
                        videoJsPlayer.offset({
                            start: inizioAzione,
                            end: fineAzione,
                            restart_beginning: false //Should the video go to the beginning when it ends
                        });
                    }
                    setTimeout(function() {
                        videoJsPlayer.currentTime(lastValue);
                    }, 500);
                }
            }

            function resyncTime(startToSync, toTact) {
                var result = startToSync;
                //DISABILITATO
                //TODO --> PRIMA AGGIUNGERE CAMPI SU DB
                if (false) {
                    var tactStart = 0;
                    var tvStart = 0;
                    /*
                     
                     */
                    if (toTact) {
                        
                    } else {
                        
                    }
                    if (result < 0) {
                        result = 0;
                    }
                }
                return result;
            }

            function resumeVideoFromLastValue(start) {
                if (typeof videoJsPlayer !== 'undefined') {
                    videoJsPlayer.currentTime(start);
//                    if (iOS() || isSafari()) {
//                        playerHTML.addEventListener('canplay', function () {
//                            playerHTML.currentTime = start;
//                            if (playerHTML.currentTime === start) {
//                                playerHTML.play();
//                            }
//                        });
//                    } else {
//                        playerHTML.play();
//                    }
                    fineAzione = end;
                    inizioAzione = start;
                }
            }

            function changeButtonsVisibility(actions, isPlaylistTab, isPosizionaleTab, isPosizionaleShotsTab) {
            
                if (typeof isPosizionaleTab !== "undefined" || isPosizionaleTab) {
                    $("#btnInfoPosizionale").removeClass("uk-hidden");
                    $("#inCareerButton").removeClass("uk-hidden");
                } else {
                    $("#btnInfoPosizionale").addClass("uk-hidden");
                    $("#inCareerButton").addClass("uk-hidden");
                }

                if (actions) {
                    $("#playActions").removeClass("uk-hidden");
                    $("#doExportAction").removeClass("uk-hidden");
                    $("#doExportActionExcel").removeClass("uk-hidden");
                    $("#inCareerButton").removeClass("uk-hidden");
                    $("#removeDuplicatesButton").removeClass("uk-hidden");
                    $("#selectedMatches").addClass("uk-hidden");
                    $("#addToMulti").addClass("uk-hidden");
                    $("#doAddToPlaylistAction").removeClass("uk-hidden");
                    $("#doRemoveFromPlaylistAction").removeClass("uk-hidden");
                    if (checkedActions.length > 0) {
                        $("#checkedActionsButton").removeClass("uk-hidden");
                    }
                } else {
                    $("#playActions").addClass("uk-hidden");
                    $("#doExportAction").addClass("uk-hidden");
                    $("#doExportActionExcel").addClass("uk-hidden");
                    $("#removeDuplicatesButton").addClass("uk-hidden");
                    $("#doAddToPlaylistAction").addClass("uk-hidden");
                    $("#selectedMatches").addClass("uk-hidden");
                    $("#doRemoveFromPlaylistAction").addClass("uk-hidden");
                    if (typeof isPlaylistTab === 'undefined') {
                        $("#addToMulti").removeClass("uk-hidden");
                    } else if (isPlaylistTab) {
                        $("#addToMulti").addClass("uk-hidden");
                    }
                    $("#checkedActionsButton").addClass("uk-hidden");
                }
            }

            function changeAction(next) {
                var newIndex = indexActionToPlay;
                var actionsArray = [];
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                } else {
                    actionsArray = selectedActions;
                }
                
                $('.currentClip').removeClass("currentClip");
                if (next) {
                    if (indexActionToPlay + 1 < actionsArray.length) {
                        indexActionToPlay++;
                        startAction('', indexActionToPlay, isRunningOnChecked);
                    }
                } else {
                    if (indexActionToPlay > 0) {
                        indexActionToPlay--;
                        startAction('', indexActionToPlay, isRunningOnChecked);
                    }
                }
                enableSwitchAction();

            }

            function enableSwitchAction() {
                verifyNextAndPreviousButton();
                //$("#seek-slider").removeProp("disabled");
                $("#closeClipViewer").removeProp("disabled");
                $("#increaseActionStart").removeProp("disabled");
                $("#increaseActionEnd").removeProp("disabled");
                isClipStopped = false;
            }
            
            function verifyNextAndPreviousButton() {
                var actionsArray = [];
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                } else {
                    actionsArray = selectedActions;
                }
            
                if (indexActionToPlay < actionsArray.length - 1) {
                    $("#nextActionSwitch").removeProp("disabled");
                } else {
                    $("#nextActionSwitch").prop("disabled", "disabled");
                }
                if (indexActionToPlay == 0) {
                    $("#prevActionSwitch").prop("disabled", "disabled");
                } else {
                    $("#prevActionSwitch").removeProp("disabled");
                }
            }

            function selectTeamPreferred() {
                var teamId = $("#comboTeamSelection option:selected").val();
                jsSetButtonSelected('team' + teamId, 2);
            }

            function showQuickSearch(type) {

                // tolgo tutti i tasti selezionati del cerca
                $("#quickSearchDiv").find("button.uk-button-active").removeClass("uk-button-active");
                if (event.target.tagName === "I") {
                    $(event.target).parent().addClass("uk-button-active");
                } else {
                    $(event.target).addClass("uk-button-active");
                }

                var valToSearch = "SICS2015_";
                if (${curUser.sportId == 1}) {
                    valToSearch = "BASKET2017_";
                }

                var splitType = type.split("#");
                resetFilter(false);
                if (splitType.length > 1) {
                    var splitTag = splitType[1].split("@");

                    $.each(splitTag, function (index, value) {
                        $("#" + valToSearch + splitType[0] + "_" + value).attr("checked", "checked");
                        //console.log("1. attivo " + valToSearch + splitType[0] + "_" + value);
                        jsSetButtonSelected(valToSearch + splitType[0] + "_" + value, 1);
                    });
                } else {
                    //console.log("2. attivo " + valToSearch + splitType[0]);
                    jsSetButtonSelected(valToSearch + splitType[0], 0);
                }

                //var ids="";
            <c:forEach items="${mGame}" var="game" varStatus="status" >
                if (ids.length > 0) {
                    ids += "-";
                }
                ids += "${game.value.idFixture}";
            </c:forEach>
                //console.log(type)
                var strUrl = "/sicstv/user/eventlist.htm?id=" + ids + "&event=" + escape(type);
    
                // resetto le azioni selezionate
                emptyCheckedActions();
                $("#numCheckedEvents").html(0);
                $("#checkedActionsButton").addClass("uk-hidden");
                
                $("#checkboxSelectAllEvents").prop("checked", false);
                
                updateSelectedActionsArray(true);
                startAction('', 0, isRunningOnChecked);
            }

            if (document.addEventListener) {
                document.addEventListener('contextmenu', function (e) {
                    e.preventDefault();
                }, false);
            } else {
                document.attachEvent('oncontextmenu', function () {
                    window.event.returnValue = false;
                });
            }

            function updatePlayerFromSeekSlider() {
                var seekSlider = document.getElementById('seek-slider');
                var initialClipSeconds = seekSlider.dataset.initVideo;
                var currentSliderValue = seekSlider.value;
                
                var timeToSet = (parseInt(initialClipSeconds) + parseFloat(currentSliderValue / 2));
                if (videoJsPlayer.currentTime() !== timeToSet) {
                    videoJsPlayer.pause();
                    videoJsPlayer.currentTime(timeToSet);
                }
            }
            
            function stopVideo() {
                if (!videoJsPlayer.paused()) {
                    videoJsPlayer.pause();
                }
            }
            
            function resumeVideo() {
                if (videoJsPlayer.paused()) {
                    // uso un delay altrimenti se tengo premuto e mi muovo molto piano nella barra poi non esegue correttamente
                    setTimeout( function() {
                        videoJsPlayer.play();
                    }, 250);
                }
            }
            
            function closeClipViewer() {
                $("#nextActionSwitch").prop("disabled", "disabled");
                $("#prevActionSwitch").prop("disabled", "disabled");
                //$("#seek-slider").prop("disabled", "disabled");
                $("#increaseActionStart").prop("disabled", "disabled");
                $("#increaseActionEnd").prop("disabled", "disabled");
                $("#closeClipViewer").prop("disabled", "disabled");
                
                var actionsArray = [];
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                } else {
                    actionsArray = selectedActions;
                }
                $('.currentClip').removeClass("currentClip");
                $("#spanNameAction").addClass("uk-hidden");
//                var seekSlider = document.getElementById('seek-slider');
//                seekSlider.value = 0;
                
                var currentTimeToSet;
                if (typeof videoJsPlayer.startOffset === "function") {
                    // per le partite non taggate non viene mai fatto l'offset quindi non esiste
                    // la funzione startOffset()
                    currentTimeToSet = videoJsPlayer.startOffset() + videoJsPlayer.currentTime();
                }
                videoJsPlayer.offset();
                videoJsPlayer.play();
                isClipStopped = true;
                checkModality();
                checkMarkersAndLogo();
                
                // c'è qualcosa che non va con il currentTime... senza il timer non va (probabilmente c'è qualcosa che lo risetta a 0)
                if (typeof currentTimeToSet !== "undefined") {
                    setTimeout(function () {
                        videoJsPlayer.currentTime(currentTimeToSet);
                    }, 200);
                }
            }
            
            function updateSelectedActionsArray(resetTablePage) {
                selectedActions = [];
                actionTable.rows({ filter: 'applied' }).every(function() {
                    var eventId = $(this.node()).attr("id").replace("rowEventId", "");
                    // actionTable.columns().nodes().length - 1 return the last column index
                    selectedActions.push(eventId);
                });

                // per risoluzione bug riporto a pagina iniziale della datatable
                // inoltre aggiornando la pagina verrebbe fuori la spunta del play nella prima riga, quindi meglio tenere
                if (actionTable.page() !== 0 && resetTablePage) {
                    actionTable.page(0).draw(false);
                }
            }
            
            function checkIfNeededToChangePage() {            
                var currentPageNumber = actionTable.page();
                var minIndexValue = currentPageNumber * actionTable.page.len();
                var maxIndexValue = currentPageNumber * actionTable.page.len() + actionTable.page.len();
                
                var tmpIndexActionToPlay = indexActionToPlay;
                if (isRunningOnChecked) {
                    tmpIndexActionToPlay = selectedActions.indexOf(checkedActions[indexActionToPlay]);
                }
                
                var indexPageToSelect = parseInt(tmpIndexActionToPlay / actionTable.page.len());
                
                if(indexPageToSelect!==currentPageNumber){
                    actionTable.page(indexPageToSelect).draw(false);
                }
            }
            
            function getScreenshot() {
                UIkit.notify("Creazione screenshot in corso...", {status: 'primary', timeout: 1000});
                var videoPath = "";
                // curVideoPath | curVideoHDPath | curVideoTACTPath
                var id = $('.HDon')[0].id;
                if (id !== 'undefined') {
                    if (id.includes('HD')) {
                        videoPath = curVideoHDPath;
                    } else if (id.includes('TACT')) {
                        videoPath = curVideoTACTPath;
                    }
                } else {
                    videoPath = curVideoPath;
                }
                
                if (videoPath !== "") {
                    var needToResume = true;
                    if (videoJsPlayer.paused()) {
                        needToResume = false;
                    } else {
                        videoJsPlayer.pause();
                    }
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/videoTakeScreenshot.htm",
                        //data: encodeURI("selectedActions=" + actionSelected + "&tactEnable=" + tactEnable + "&multi=" + multi + "&hd=" + hd + "&tact=" + tact + "&namefile=" + namefile),
                        data: encodeURI("videoPath=" + videoPath.replaceAll("&", "-_-") + "&milliseconds=" + (videoJsPlayer.currentTime() + videoJsPlayer.startOffset())),
                        cache: false,
                        crossDomain: true,
                        success: function (imgPath) {
                            if (imgPath) {
                                var link = document.createElement("a");
                                link.href = imgPath;
                                link.download = "immagine.png";
                                link.click();
                                UIkit.notify("Screenshot creato con successo", {status: 'success', timeout: 1000});
                                
                                if (needToResume) {
                                    videoJsPlayer.play();
                                }
                            }
                        },
                        error: function () {
                            UIkit.notify("Errore inaspettato durante la creazione dello screenshot", {status: 'danger', timeout: 1000});
                            if (needToResume) {
                                videoJsPlayer.play();
                            }
                        },
                        async: true
                    });
                }
            }
            
            function exportEventExcel() {
                $("#actionTableExportButton").click();
                $("button.buttons-excel").addClass("uk-hidden");
                $("button.buttons-excel").click();
                $("#actionTableExportButton").click();
            }
            
            function exportEventCsv() {
                exportEvents();
//                $("#actionTableExportButton").click();
//                $("button.buttons-csv").addClass("uk-hidden");
//                $("button.buttons-csv").click();
//                $("#actionTableExportButton").click();
            }
            
            function manageCareer() {
                var allSeasons = new URL(document.URL).searchParams.get("allSeasons");
                
                if ($("#liPosizionaleShots").hasClass("uk-active")) {
                    localStorage["videoSelectedTab"] = "liPosizionaleShots";
                } else if ($("#liPosizionale").hasClass("uk-active")) {
                    localStorage["videoSelectedTab"] = "liPosizionale";
                }
                
                if (typeof allSeasons === 'undefined' || allSeasons === null) {
                    window.location.replace(document.URL + "&allSeasons=true");
                } else {
                    window.location.replace(document.URL.replace("&allSeasons=true", ""));
                }
            }
            
            function manageRemoveDuplicates() {
                var removeDuplicates = new URL(document.URL).searchParams.get("removeDuplicates");
                if (typeof removeDuplicates === 'undefined' || removeDuplicates === null) {
                    window.location.replace(document.URL + "&removeDuplicates=true");
                } else {
                    window.location.replace(document.URL.replace("&removeDuplicates=true", ""));
                }
            }
            
            function manageRemoveExactDuplicates() {
                removeExactDuplicates = !removeExactDuplicates;
                $("#exactDuplicatesButton").toggleClass("uk-button-active");
                
                applyFilterEvents();
            }
            
            function increaseClipStart() {
                if ((videoJsPlayer.startOffset() || videoJsPlayer.startOffset() === 0) && (videoJsPlayer.endOffset() || videoJsPlayer.endOffset() === 0)) {
                    var secondsToAdd = parseInt(sessionStorage["increaseTimeStart"]);
                    var startOffset = videoJsPlayer.startOffset();
                    var endOffset = videoJsPlayer.endOffset();
                    
                    if (startOffset - secondsToAdd >= 0) {
                        startOffset = startOffset - secondsToAdd;
                    } else {
                        startOffset = 0;
                    }
                    
                    inizioAzione = startOffset; // aggiornamento che serve se cambio tattico o hd
                    //videoJsPlayer.offset();
                    videoJsPlayer.offset({
                        start: startOffset,
                        end: endOffset,
                        restart_beginning: true //Should the video go to the beginning when it ends
                    });
                    videoJsPlayer.currentTime(0);
                    //videoJsPlayer.play();
                }
            }
            
            function increaseClipEnd() {
                if ((videoJsPlayer.startOffset() || videoJsPlayer.startOffset() === 0) && (videoJsPlayer.endOffset() || videoJsPlayer.endOffset() === 0)) {
                    var secondsToAdd = parseInt(sessionStorage["increaseTimeStart"]);
                    var startOffset = videoJsPlayer.startOffset();
                    var endOffset = videoJsPlayer.endOffset();
                    
                    if ((endOffset + secondsToAdd) <= currentVideoDuration) {
                        endOffset = endOffset + secondsToAdd;
                    } else {
                        endOffset = currentVideoDuration;
                    }
                    
                    fineAzione = endOffset; // aggiornamento che serve se cambio tattico o hd
                    //videoJsPlayer.offset();
                    videoJsPlayer.offset({
                        start: startOffset,
                        end: endOffset,
                        restart_beginning: true //Should the video go to the beginning when it ends
                    });
                    videoJsPlayer.play();
                    //videoJsPlayer.currentTime(0);
                }
            }

            function setIncreaseTime(seconds) {
                $("#increaseTimeStart").text(seconds);
                sessionStorage["increaseTimeStart"] = seconds;
            }
            
            function checkExport() {
                if (checkedActions.length > 0) {
                    $("#exportActionNoEvents").addClass("uk-hidden");
                    var result = isValidExportRequest(checkedActions);
                    var showMainMessage = false;
                    
                    if (result.includes("TEMPO_EFFETTIVO")) {
                        $("#exportEffettivoWarning").removeClass("uk-hidden");
                    } else {
                        $("#exportEffettivoWarning").addClass("uk-hidden");
                    }
                    if (result.includes("AMOUNT")) {
                        showMainMessage = true;
                        $("#firstExportRequirement").attr("src", "/sicstv/images/cancel.png");
                    } else {
                        //$("#firstExportRequirement").attr("src", "/sicstv/images/confirm.png");
                        $("#firstExportRequirement").addClass("uk-hidden");
                        $("#firstExportRequirementLabel").addClass("uk-hidden");
                    }
                    if (result.includes("TOTAL_TIME")) {
                        showMainMessage = true;
                        $("#secondExportRequirement").attr("src", "/sicstv/images/cancel.png");
                    } else {
                        //$("#secondExportRequirement").attr("src", "/sicstv/images/confirm.png");
                        $("#secondExportRequirement").addClass("uk-hidden");
                        $("#secondExportRequirementLabel").addClass("uk-hidden");
                    }
                    if (result.includes("MAX_CLIP_TIME")) {
                        showMainMessage = true;
                        $("#thirdExportRequirement").attr("src", "/sicstv/images/cancel.png");
                    } else {
                        //$("#thirdExportRequirement").attr("src", "/sicstv/images/confirm.png");
                        $("#thirdExportRequirement").addClass("uk-hidden");
                        $("#thirdExportRequirementLabel").addClass("uk-hidden");
                    }
                    if (result.includes("MIN_CLIP_TIME")) {
                        showMainMessage = true;
                        $("#fourthExportRequirement").attr("src", "/sicstv/images/cancel.png");
                    } else {
                        //$("#fourthExportRequirement").attr("src", "/sicstv/images/confirm.png");
                        $("#fourthExportRequirement").addClass("uk-hidden");
                        $("#fourthExportRequirementLabel").addClass("uk-hidden");
                    }
                    
                    if (!showMainMessage) {
                        $("#exportActionsButton").removeAttr("disabled");
                        $("#exportRequirementDiv").addClass("uk-hidden");
                    } else {
                        $("#exportActionsButton").prop("disabled", "disabled");
                        $("#exportRequirementDiv").removeClass("uk-hidden");
                    }
                    
                    if (!$("#exportRunningNotify").hasClass("uk-hidden")) {
                        $("#exportRunningMessage").removeClass("uk-hidden");
                    } else {
                        $("#exportRunningMessage").addClass("uk-hidden");
                    }
                } else {
                    $("#exportActionNoEvents").removeClass("uk-hidden");
                }
            }
            
            var advancedFilter = new Map();
            function addRemoveAdvancedFilter(tagCode) {
                if (typeof event === "undefined" || event.target.tagName !== 'I') { // In questo modo funziona solo se clicco il button
                    tagCode = tagCode.replace("SICS2015_", "");
                    
                    var generalTagFound = false;
                    var tagToFound = tagCode;
                    if (tagCode.includes("-")) {
                        tagToFound = tagCode.substring(0, tagCode.indexOf("-"));
                    }

                    [...advancedFilter.keys()].forEach(key => {
                        if (key === tagToFound) {
                            generalTagFound = true;
                        }
                    });

                    if (tagCode.includes("-")) { // AMM-0 AMM-1...
                        if (generalTagFound) { // ho già lo stesso tag oppure altri tag
                            var mapValue = advancedFilter.get(tagToFound);
                            if (mapValue.includes(tagCode)) { // devo togliere tag
                                mapValue = mapValue.replace("@" + tagCode, '');
                                mapValue = mapValue.replace(tagCode + "@", '');
                                mapValue = mapValue.replace(tagCode, '');

                                $("#addEvent_" + tagToFound + "_" + tagCode).removeAttr("checked");
                            } else { // devo aggiungere il tag
                                mapValue += "@" + tagCode;
                                $("#addEvent_" + tagToFound + "_" + tagCode).attr("checked", "checked");
                            }

                            if (mapValue === "") {
                                advancedFilter.delete(tagToFound);
                            } else {
                                advancedFilter.set(tagToFound, mapValue);
                            }
                        } else { // la categoria non esiste: devo crearla
                            advancedFilter.set(tagToFound, tagCode);
                            $("#addEvent_" + tagToFound + "_" + tagCode).attr("checked", "checked");
                        }
                    } else { // AMM ATT..
                        if (generalTagFound) {
                            advancedFilter.delete(tagToFound);
                            document.querySelectorAll("." + tagToFound).forEach(function (element) {
                                var id = element.id;
                                if (!element.disabled) {
                                    $('#' + id).removeAttr("checked");
                                }
                            });
                        } else {
                            var atLeastOneAdded = false;
                            document.querySelectorAll("." + tagToFound).forEach(function (element) {
                                var id = element.id;
                                if (!element.disabled) {
                                    if (!$('#' + id).attr("checked")) {
                                        atLeastOneAdded = true;
                                        var functionToExecute = $('#' + id).attr("onclick");
                                        eval(functionToExecute);
                                    }
                                }
                            });

                            // se ho tutti i tag disabilitati e clicco sul tasto
                            // non succede nulla, invece deve selezionare tutti i tag
                            if (!atLeastOneAdded) {
                                advancedFilter.set(tagToFound, "");
                            }
                        }
                    }
                    checkSelectedButtonAdvanced();
                }
            }
            
            function checkSelectedButtonAdvanced() {
                $("#tabs-addEvent").find(".uk-button-active").removeClass("uk-button-active");
                [...advancedFilter.keys()].forEach(key => {
                    $("#addEvent_" + key).addClass("uk-button-active");
                });

                $("#btnAggiungiEventi").removeClass("uk-hidden");
                $("#addEventAmount").html(advancedFilter.size);
            }
            
            function manageExtraSeasons() {
                var amount = $("#seasons-container").find("button.uk-button-active").length;

                if (amount > 0) {
                    $("#btnAggiungiStagioni").removeClass("uk-hidden");
                    $("#addSeasonAmount").html(amount);
                } else {
                    var currentExtraSeasons = new URL(document.URL).searchParams.get("extraSeasonIds");
                    if (typeof currentExtraSeasons === 'undefined' || currentExtraSeasons === null) {
                        $("#btnAggiungiStagioni").addClass("uk-hidden");
                    }
                    
                    $("#addSeasonAmount").html(amount);
                }
            }
            
            function applyExtraEvents() {
                if (advancedFilter.size > 0) {
                    var filter = "";

                    [...advancedFilter.keys()].forEach(key => {
                        // devo guardare se devo applicare il filtro su TUTTA la categoria
                        var allChecked = true;
                        document.querySelectorAll("." + key).forEach(function (element) {
                            var id = element.id;
                            if (!$('#' + id).attr("checked") && $('#' + id).attr("disabled") !== 'disabled') {
                                allChecked = false;
                            }
                        });

                        if (allChecked) {
                            filter += (filter === "" ? "" : "|") + "SICS-_-" + key;
                        } else {
                            filter += (filter === "" ? "" : "|") + "SICS-_-" + key + "#" + advancedFilter.get(key);
                        }
                    });
                    
                    var currentExtraEvents = new URL(document.URL).searchParams.get("extraEvent");
                    
                    if (typeof currentExtraEvents === 'undefined' || currentExtraEvents === null) {
                        window.location.replace(document.URL + "&extraEvent=" + escape(filter));
                    } else {
                        window.location.replace(document.URL.replace("&extraEvent=" + escape(currentExtraEvents), "&extraEvent=" + escape(filter)));
                    }
                }
            }
            
            function applyExtraSeasons() {
                var selectedSeasons = $("#seasons-container").find("button.uk-button-active");
                if (selectedSeasons.length > 0) {
                    var extraSeasonIds = [];
                    selectedSeasons.each(function (index, element) {
                        var seasonId = $(element).attr("seasonid");
                        if (notEmpty(seasonId)) {
                            extraSeasonIds.push(seasonId);
                        }
                    });
                    
                    if (extraSeasonIds.length > 0) {
                        var currentExtraSeasons = new URL(document.URL).searchParams.get("extraSeasonIds");
                    
                        if (typeof currentExtraSeasons === 'undefined' || currentExtraSeasons === null) {
                            window.location.replace(document.URL + "&extraSeasonIds=" + escape(extraSeasonIds.join(",")));
                        } else {
                            window.location.replace(document.URL.replace("&extraSeasonIds=" + escape(currentExtraSeasons), "&extraSeasonIds=" + escape(extraSeasonIds.join(","))));
                        }
                    }
                } else {
                    var currentExtraSeasons = new URL(document.URL).searchParams.get("extraSeasonIds");
                    if (typeof currentExtraSeasons !== 'undefined' && currentExtraSeasons) {
                        window.location.replace(document.URL.replace("&extraSeasonIds=" + escape(currentExtraSeasons), ""));
                    }
                }
            }

            var isRecording = false, recStart, recEnd, recCurrentEventId;
            function manageRec(forceStop) {
                recCurrentEventId = undefined;
                
                if (!isRecording) {
                    recStart = videoJsPlayer.currentTime();
//                    $("#record-button").html($("#record-button").html());
//                    $("#rec-status").attr("src", "/sicstv/images/switch_on.svg");
                } else {
                    recEnd = videoJsPlayer.currentTime();
//                    $("#record-button").html($("#record-button").html());
//                    $("#rec-status").attr("src", "/sicstv/images/switch_off.svg");
                    
                    if (typeof forceStop === "undefined" || !forceStop) {
                        loadRecTeams();
                        openRecModal();
                    }
                }
                
                $("#record-button").toggleClass("uk-button-danger");
                isRecording = !isRecording;
            }
            
            function loadRecTeams() {
                $('#rec-team-select option').remove();
                $('#rec-player-select option').remove();

                $('#rec-team-select').append($('<option>'));
                $("#filtroGiocatore").find("button.teamAllButton").each(function (index, element) {
                    var buttonId = $(element).attr("id");
                    var teamId, teamName;
                    if (buttonId.includes("team")) {
                        teamId = buttonId.replace("team", "");
                        teamName = $(element).attr("teamname");
                        if (teamId && teamName) {
                            // console.log(teamId, teamName);
                            $('#rec-team-select').append($('<option>', {
                                value: teamId,
                                text: teamName
                            }));
                        }
                    }
                });
            }
            
            function loadRecPlayers() {
                var teamId = $("#rec-team-select").val();
                $('#rec-player-select option').remove();
                $('#rec-player-select').append($('<option>'));
                $("#filtroGiocatore").find("button.teamId" + teamId).each(function (index, element) {
                    var playerButtonId = $(element).attr("id");
                    var playerId, playerName;
                    if (playerButtonId.includes("playerButton")) {
                        playerId = playerButtonId.replace("playerButton", "");
                        playerName = $(element).attr("title").split(" - ")[1];
                        if (playerId && playerName) {
                            // console.log(playerId, playerName);
                            $('#rec-player-select').append($('<option>', {
                                value: playerId,
                                text: playerName
                            }));
                        }
                    }
                });
            }
            
            function openRecModal(start, end) {
                $("#recModal").addClass("uk-open");
                $("#recModal").removeClass("uk-hidden");
                
                if (typeof start === "undefined") {
                    $("#rec-from").val(parseInt(recStart));
                    $("#rec-to").val(parseInt(recEnd));
                } else {
                    $("#rec-from").val(parseInt(start));
                    $("#rec-to").val(parseInt(end));
                }
                
                videoJsPlayer.pause();
            }
            
            function closeRecModal() {
                $("#recModal").removeClass("uk-open");
                $("#recModal").addClass("uk-hidden");
                
                if (videoJsPlayer.paused()) {
                    videoJsPlayer.play();
                }
            }
            
            function openGotoModal() {
                if (isClipStopped) {
                    $("#gotoModal").addClass("uk-open");
                    $("#gotoModal").removeClass("uk-hidden");

                    var fixture = fixtureMap.get(currentGameId);
                    var hideSelect = false;
                    if (typeof fixture !== "undefined" && fixture !== null) {
                        if (typeof fixture.fixtureDetails !== "undefined") {
                            $("#goto-half-container").removeClass("uk-hidden");
                            $('#goto-half option').remove();
                            if (typeof fixture.fixtureDetails.startTime1 !== "undefined") {
                                $('#goto-half').append($('<option>', {
                                    value: fixture.fixtureDetails.startTime1,
                                    text: "<spring:message code="video.primot"/>"
                                }));
                            }
                            if (typeof fixture.fixtureDetails.startTime2 !== "undefined") {
                                $('#goto-half').append($('<option>', {
                                    value: fixture.fixtureDetails.startTime2,
                                    text: "<spring:message code="video.secondot"/>"
                                }));
                            }
                            if (typeof fixture.fixtureDetails.startTime3 !== "undefined") {
                                $('#goto-half').append($('<option>', {
                                    value: fixture.fixtureDetails.startTime3,
                                    text: "<spring:message code="video.terzot"/>"
                                }));
                            }
                            if (typeof fixture.fixtureDetails.startTime4 !== "undefined") {
                                $('#goto-half').append($('<option>', {
                                    value: fixture.fixtureDetails.startTime4,
                                    text: "<spring:message code="video.quartot"/>"
                                }));
                            }
                        } else {
                            hideSelect = true;
                        }
                    } else {
                        hideSelect = true;
                    }

                    if (hideSelect) {
                        $("#goto-half-container").addClass("uk-hidden");
                    }

                    videoJsPlayer.pause();
                } else {
                    UIkit.notify("<spring:message code="goto.warning"/>", {status: 'danger', timeout: 1000});
                }
            }
            
            function closeGotoModal() {
                $("#gotoModal").removeClass("uk-open");
                $("#gotoModal").addClass("uk-hidden");
                
                if (videoJsPlayer.paused()) {
                    videoJsPlayer.play();
                }
            }
            
            function goto() {
                var minute = parseInt($("#goto-minutes").val());
                var second = parseInt($("#goto-seconds").val());
                var half = $("#goto-half").val();
                
                if (isNaN(minute)) {
                    minute = 0;
                }
                if (isNaN(second)) {
                    second = 0;
                }
                
                
                if (half === "") {
                    // se non ho selezionato il tempo devo verificare se ho la sincronizzazione
                    var fixture = fixtureMap.get(currentGameId);
                    var isSynchronized = false;
                    if (typeof fixture !== "undefined" && fixture !== null && typeof fixture.fixtureDetails !== "undefined") {
                        if (typeof fixture.fixtureDetails.startTime1 !== "undefined") {
                            isSynchronized = true;
                        }
                    }
                    
                    if (isSynchronized) {
                        second += Math.round(fixture.fixtureDetails.startTime1 / 1000);
                    }
                } else {
                    // ho selezionato un tempo
                    second += Math.round(parseInt(half) / 1000);
                }
                
                // aggiungo minuti
                second += minute * 60;
                videoJsPlayer.currentTime(second);
                closeGotoModal();
            }
            
            function createPersonalEvent() {
                var title, from, to, team, players, fixtureId;
                title = $("#rec-title").val();
                from = parseInt($("#rec-from").val());
                to = parseInt($("#rec-to").val());
                team = $("#rec-team-select").val();
                players = $("#rec-player-select").val();
                fixtureId = currentGameId;
                
                if (title && title.length > 0
                        && from >= 0 && !isNaN(from) && !isNaN(to)
                        && to > from
                        && team && team.length > 0
                        && fixtureId) {
                    if (players === null) {
                        players = [];
                    }
                    
                    if (to - from > 600) {
                        UIkit.notify("<spring:message code="video.record.clip.duration.error"/>", {status: 'warning', timeout: 2500});
                        return;
                    }
                    
                    if (typeof recCurrentEventId === "undefined") {
                        from += Math.round(videoJsPlayer._offsetStart);
                        to += Math.round(videoJsPlayer._offsetStart);
                        
                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/createPersonalClip.htm",
                            data: encodeURI("fixtureId=" + fixtureId + "&start=" + (from * 1000) + "&end=" + (to * 1000) + "&team=" + team + "&players=" + players.join(",") + "&title=" + title),
                            success: function (result) {
                                if (result === "ok") {
                                    UIkit.notify("<spring:message code="video.record.save.success"/>", {status: 'success', timeout: 1000});
                                } else if (result === "tooLong") {
                                    UIkit.notify("<spring:message code="video.record.error.too.long"/>", {status: 'danger', timeout: 1000});
                                } else {
                                    UIkit.notify("<spring:message code="video.record.save.error"/>", {status: 'danger', timeout: 1000});
                                }
                                closeRecModal();
                            }
                        });
                    } else {
                        // update
                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/updatePersonalClip.htm",
                            data: encodeURI("eventId=" + recCurrentEventId + "&fixtureId=" + fixtureId + "&start=" + (from * 1000) + "&end=" + (to * 1000) + "&team=" + team + "&players=" + players.join(",") + "&title=" + title),
                            success: function (result) {
                                if (result === "ok") {
                                    UIkit.notify("<spring:message code="video.record.update.success"/>", {status: 'success', timeout: 1000});
                                    location.reload();
                                } else if (result === "tooLong") {
                                    UIkit.notify("<spring:message code="video.record.error.too.long"/>", {status: 'danger', timeout: 1000});
                                } else {
                                    UIkit.notify("<spring:message code="video.record.save.error"/>", {status: 'danger', timeout: 1000});
                                }
                            }
                        });
                    }
                } else {
                    UIkit.notify("<spring:message code="video.record.params.error"/>", {status: 'warning', timeout: 2500});
                }
            }
            
            function deleteRec() {
                if (confirm("Are you sure you want to delete this clip?")) {
                    var actionsArray = [];
                    if (isRunningOnChecked) {
                        actionsArray = checkedActions;
                    } else {
                        actionsArray = selectedActions;
                    }
                    
                    var eventId = actionsArray[indexActionToPlay];
                    if (eventId) {
                        var eventData = eventMap.get("" + eventId);
                        if (eventData && eventData._idType === 1156) {
                            $.ajax({
                                type: "GET",
                                url: "/sicstv/user/deletePersonalClip.htm",
                                data: encodeURI("eventId=" + eventId),
                                success: function (result) {
                                    if (result === "ok") {
                                        UIkit.notify("<spring:message code="video.record.delete.success"/>", {status: 'success', timeout: 1000});
                                        location.reload();
                                    } else {
                                        UIkit.notify("<spring:message code="video.record.save.error"/>", {status: 'danger', timeout: 1000});
                                    }
                                }
                            });
                        }
                    }
                }
            }
            
            function editRec() {
                var actionsArray = [];
                if (isRunningOnChecked) {
                    actionsArray = checkedActions;
                } else {
                    actionsArray = selectedActions;
                }

                var eventId = actionsArray[indexActionToPlay];
                if (eventId) {
                    var eventData = eventMap.get("" + eventId);
                    if (eventData) {
                        recCurrentEventId = eventData._id;
                        
                        loadRecTeams();
                        $("#rec-title").val(eventData.mNote);
                        $("#rec-team-select").val(eventData._idTeam);
                        loadRecPlayers();
                        if (Object.keys(eventData._player).length > 0) {
                            var selectedPlayerIds = [];
                            Object.keys(eventData._player).forEach((value, key) => {
                                selectedPlayerIds.push("" + value + "");
                            });
                            console.log(selectedPlayerIds);
                            $("#rec-player-select").val(selectedPlayerIds);
                        }
                        openRecModal(eventData._period_start_second + (eventData._period_start_minute * 60), eventData._period_end_second + (eventData._period_end_minute * 60));
                    }
                }
            }
        </script>
    </head>

    <body>
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>
        <!-- campi nascosti relativi alla partita nel caso sia unica e non multipartita-->

        <input type="hidden" id="mGame" value="${mGame}" />
        <input type="hidden" id="mGameAmount" value="${mGame.size()}" />
        <input type="hidden" id="mTeam" value="${mTeam}" />
        <input type="hidden" id="mGoals" value="${mGoals}" />
        <input type="hidden" id="idComp" value="${mCompetition.id}" />
        <input type="hidden" id="mEventFilter" value="${mEventFilter}" />
        <input type="hidden" id="idPlayer" value="${mPlayer.id}" /> 
        <input type="hidden" id="mEventPresent" value="${mEventPresent}" /> 
        <input type="hidden" id="mSer" value="${mSer}" /> 
        <input type="hidden" id="mPlaylistTvId" value="${mPlaylistTvId}"/>
        
        <div class="video-pre-container" id="preLoadDiv">
            <div>
                <span><spring:message code='menu.user.loading'/></span>
            </div>
            <div class="video-pre-progress-bar-container" style="text-align: center">
                <div class="video-pre-progress-bar" id="video-pre-bar" style="width: 0%">
                    <div style="width: 50vh; padding-top: 3px" id="video-pre-bar-value"></div>
                </div>
            </div>
        </div>
        
        <div class="uk-grid uk-hidden" id="centralGrid">

            <!--Video player-->
            <div id="video" class="uk-width-5-10 uk-responsive-width" >
                <div id="videoTools" >
                    <span id="clipXdiY" class="uk-hidden"><spring:message code='video.clipavanzamento'/></span>
                    <span id="spanNameAction" class="topBarVideo" title="  "></span>
                    <button id='btnHD' class="uk-hidden uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small" onclick="switchHD();" title="<spring:message code='tooltip.HD'/> ${mGame[0].hd}">
                        <img src='/sicstv/images/hd-dark.png'/>
                    </button>
                    <button id='btnTACT' class="uk-hidden uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small" onclick="switchTACT();" title="<spring:message code='tooltip.TACT'/>">
                        <img src='/sicstv/images/tact.png'/>
                    </button>
                    <button id='btnInfo' class="uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small">
                        <i class="uk-icon-info-circle"></i>
                    </button>
                </div>
                <div id="videoContainer">
                    <!-- il video viene caricato qui da jQuery-->
                    <video id='media_playerHTML' class='uk-width-1-1 video-js vjs-default-skin' controls preload muted autoplay>
                        <div class="video-title">Your Title Here</div>
                    </video>
<!--                    <video id='media_playerHTML' class='uk-width-1-1 video-js' data-setup='{ "playbackRates": [0.5, 1, 1.5, 2] }' preload muted autoplay playsinline controls controlsList="nodownload" style='background: #000;'>
                        <source src="https://d2xqftweek72uv.cloudfront.net/Atalanta-Inter-SA-G11-20231104-HD.mp4?Expires=1699864925&Signature=zAEQ6yTiAiDqrJUO4Cvm0W03jlVsFF9KbxWNmCZYradQmYPVregDOfJLe4MlqJIXdJyxwXvdpEUBqug8lLF3yedpTAsMD0WUT9nt-ZK5BalndZkQAxEcUeUEWl0HrtyKYv2J~~QZNqjI4QhcrGIxffEMfVooCC3JfXjMTBbmS2Fmm4h5bBqLvSvVxyfSRNmxiJ0sKPl4XeWoKe~pSgAPKAeQkXodY8-0vff0lGOEz~wn5jHULE8sWWBk8kLv7ldQ~n4WKv67ef1r6Y18l7SSe4Y4uYvsSAx~sN9xdc7WgIOYhR4fX7mvkcknY8eSAz8Ny94zCNcXP~5Ns1-LW1x6Iw__&Key-Pair-Id=K1SRL0K7SLP3VU" type="video/mp4">
                    </video>-->
                </div>
                <div class="tagEvento" id="videoPlayerTools">
                    <button onclick="changeAction(false);" id="prevActionSwitch" class="uk-button uk-button-small uk-margin-small-left uk-margin-small-top uk-margin-small-bottom uk-hidden-medium uk-hidden-small" disabled title="<spring:message code='calendar.precedente'/>"><i class="uk-icon-angle-double-left"></i> </button>
                    <button onclick="changeAction(true);" id="nextActionSwitch" class="uk-button uk-button-small uk-margin-small uk-hidden-medium uk-hidden-small" disabled title="<spring:message code='calendar.successiva'/>"><i class="uk-icon-angle-double-right"></i> </button>
                    <button onclick="increaseClipStart();" id="increaseActionStart" class="uk-button uk-button-small uk-margin-small uk-hidden-medium uk-hidden-small" disabled title="<spring:message code='video.add.time.start'/>"><-</button>
                    <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click', pos:'bottom-right'}">
                        <button class="uk-button uk-button-small uk-hidden-medium uk-hidden-small"
                                title="<spring:message code='video.add.time.start'/>">+<span
                                id="increaseTimeStart">5</span></button>
                        <div class="uk-dropdown uk-dropdown-small">
                            <ul class="uk-nav uk-nav-dropdown">
                                <li><a onclick="setIncreaseTime(1);">1s</a></li>
                                <li><a onclick="setIncreaseTime(3);">3s</a></li>
                                <li><a onclick="setIncreaseTime(5);">5s</a></li>
                                <li><a onclick="setIncreaseTime(10);">10s</a></li>
                                <li><a onclick="setIncreaseTime(30);">30s</a></li>
                                <li><a onclick="setIncreaseTime(60);">60s</a></li>
                            </ul>
                        </div>
                    </div>
                    <button onclick="increaseClipEnd();" id="increaseActionEnd" class="uk-button uk-button-small uk-margin-small uk-hidden-medium uk-hidden-small" disabled title="<spring:message code='video.add.time.end'/>">-></button>
                    <!--<img class="uk-margin-left cursor-pointer" onclick="manageRec();" height="50px" width="50px" src="/sicstv/images/rec.svg"> <img id="rec-status" height="40px" width="40px" src="/sicstv/images/switch_off.svg">-->
                    <button id="record-button" onclick="manageRec();" default-title="REC" class="uk-button uk-button-small uk-margin-small-left uk-margin-small-top uk-margin-small-bottom uk-hidden-medium uk-hidden-small uk-padding-left uk-padding-right" style="padding-right: 0"><i class="uk-icon-circle uk-text-danger"></i> REC</button>
                    <input class="video-player-tools-child uk-hidden" data-initVideo="0" type="range" id="seek-slider" min="0" step="1" value="0">
                    <button onclick="resetAllFilters(true); resetFilter(true); closeClipViewer();" id="closeClipViewer" class="uk-button uk-button-small uk-margin-small uk-hidden-medium uk-hidden-small" style="float: right" disabled><i class="uk-icon-close"></i></button>
                    <button onclick="deleteRec();" id="remove-rec-button" class="uk-button uk-button-small uk-margin-small uk-float-right uk-hidden uk-margin-right"><i class="uk-icon-trash"></i></button>
                    <button onclick="editRec();" id="edit-rec-button" class="uk-button uk-button-small uk-margin-small uk-float-right uk-hidden uk-margin-small-right"><i class="uk-icon-edit"></i></button>
                </div>
                <!-- filtro azioni / info partita / ricerca / analisi -->
                <div id="bottomNav" class="uk-width-1-1 uk-hidden">
                    <ul id="bottomNavTab" class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#bottomNavContent'}">
                        <li id="liRicerca" class="uk-active"><a href="#"><spring:message code="video.ricerca"/></a></li>
                        <li id="liFiltri"><a href="#"><spring:message code="video.ricercaAvanzata"/> <span id="numFilterEvents" class="uk-badge uk-hidden">0</span></a></li>
                        <c:if test="${mTacticalButtonToShow != null}">
                        <li id="liFiltriTactical"><a href="#"><spring:message code="video.ricercaTattica"/></a></li>
                        </c:if>
                        <li id="liFiltroGiocatori"><a href="#"><spring:message code="video.filtrogiocatori"/> <span id="numFilterPlayerTeam" class="uk-badge uk-hidden">0</span></a></li>
                        <li id="liAggiungiEvento"><a href="#"><spring:message code="video.add.event"/></a></li>
                        <li id="liAggiungiStagione"><a href="#"><spring:message code="video.add.season"/></a></li>
                        <li id="liSelezionate"><a href="#"><spring:message code="video.partite"/> <span id="numMatchSelected" class="uk-badge">0</span></a></li>
                        <li id="liEventiSmall" class="uk-visible-small"><a href="#"><spring:message code="video.eventi"/><span id="numEventsSmall" class="uk-badge uk-hidden">0</span></a></li>
                        <!--li id="liInfoMatch"><a href="#"><spring:message code="video.infopartita"/></a></li-->
                        <!--li><a href="#"><spring:message code="menu.area.analisi"/></a></li-->
                    </ul>
                    <span id="btnFiltriRapidi" class="uk-float-right">
                        <span id="quickChoiceSpan" class="uk-form">
                            <c:choose>
                                <c:when test="${mListPlayers.size() == 2}">
                                    <c:forEach items="${mListPlayers}" var="list" varStatus="status">
                                        <button id="quickChoiceteam${list.key.id}" class="uk-button" onclick="jsSetButtonSelected('team${list.key.id}', 2);" value="${list.key.id}" title="${list.key.name}">
                                            <img style="background: transparent" height="15px" width="15px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${list.key.logo}.png"/>
                                            <span>${list.key.name}</span>
                                        </button>
                                    </c:forEach>
                                </c:when>
                            </c:choose>
                        </span>
                        <c:choose>
                            <c:when test="${mUser.sportId == '0'}">
                                <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}">
                                    <button id="selectedMatches" class="uk-button" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.selezionati'/>"><i class="uk-icon-bars"></i></button> 
                                    <!--<a href="#" id="selectedMatches" class="uk-button uk-button-small" ><i class="uk-icon-bars  "></i> 0 </a>-->
                                    <span id="spanSelAll" class="uk-dropdown">
                                        <ul class="uk-nav uk-nav-navbar">
                                            <li class="selAllSubnav"><a onclick="jsSetButtonSelected('', 4, 0, 'all');" style="text-align: left"><i class="uk-icon-check-square-o uk-margin-right"></i><spring:message code="menu.selezionatutto"/></a></li>
                                            <li class="uk-nav-divider"></li>
                                            <li class="selAllSubnav"><a onclick="jsSetButtonSelected('', 4, 0, 'none');" style="text-align: left"><i class="uk-icon-square-o uk-margin-right"></i><spring:message code="menu.deselezionatutto"/></a></li>
                                        </ul>							
                                    </span>
                                </span>
                                <button id="timeFirstButton" class="uk-button" onclick="jsSetButtonSelected('timeFirstButton', 5);" title="<spring:message code="video.primot"/>"><i class="uk-icon-hourglass-start"></i></button>
                                <button id="timeSecondButton" class="uk-button uk-margin-small-right" onclick="jsSetButtonSelected('timeSecondButton', 5);" title="<spring:message code="video.secondot"/>"><i class="uk-icon-hourglass-end"></i></button>
                                <button id="exactDuplicatesButton" class="uk-button uk-margin-small-right" onclick="manageRemoveExactDuplicates();"><img height="20px" width="20px" src="/sicstv/images/no-exact-duplicates.svg"></button>
                            </c:when>
                            <c:when test="${mUser.sportId == '1'}">
                                <button id="timeFirstQuarter" class="uk-button" onclick="jsSetButtonSelected('timeFirstQuarter', 5);" title="<spring:message code="video.primoQ"/>">1Q</button>
                                <button id="timeSecondQuarter" class="uk-button" onclick="jsSetButtonSelected('timeSecondQuarter', 5);" title="<spring:message code="video.secondoQ"/>">2Q</button>
                                <button id="timeThirdQuarter" class="uk-button" onclick="jsSetButtonSelected('timeThirdQuarter', 5);" title="<spring:message code="video.terzoQ"/>">3Q</button>
                                <button id="timeFourthQuarter" class="uk-button uk-margin-small-right" onclick="jsSetButtonSelected('timeFourthQuarter', 5);" title="<spring:message code="video.quartoQ"/>">4Q</button>
                            </c:when>
                        </c:choose>
                        <span>
                            <button id="sourceSics" class="uk-button" value="-1" onclick="jsSetButtonSelected('sourceSics', 6);"><i><img width="16" height="16" src="/sicstv/images/tag.svg"/></i> SICS</button>
                            <button id="sourcePersonal" class="uk-button" value="${curUser.groupsetId}" onclick="jsSetButtonSelected('sourcePersonal', 6);"><i><img width="16" height="16" src="/sicstv/images/tag.svg"/></i> <spring:message code="video.personale"/></button>
                        </span>
                        
                        <!--<button id="btnResetFiltri" class="uk-button" onclick="resetFilter(true);" title="<spring:message code="video.resetfiltri"/>"><i class="uk-icon-reply"></i></button>-->
                        <button id="btnAggiungiEventi" class="uk-button uk-button-confirm uk-hidden" onclick="applyExtraEvents();" title="<spring:message code="video.add.event"/>"><i class="uk-icon-filter"></i><span id="addEventAmount" class="uk-margin-small-left"></span></button>
                        <button id="btnAggiungiStagioni" class="uk-button uk-button-confirm uk-hidden" onclick="applyExtraSeasons();" title="<spring:message code="menu.apply.filter"/>"><i class="uk-icon-filter"></i><span id="addSeasonAmount" class="uk-margin-small-left"></span></button>
                        <span onclick="applyFilterEvents(true, true);" id="btnApplica" class="uk-button uk-button-small uk-float-right uk-margin-right uk-hidden">Applica</span>
                        <span onclick="resetFilter(true);" id="btnResetFiltri" class="uk-button uk-button-small uk-float-right uk-margin-right uk-hidden">Reset</span>
                    </span>
                    <ul id="bottomNavContent" class="uk-switcher uk-margin-small-left uk-margin-small-right uk-clear-both uk-text-center">
                        <li id="liRicercaContent" class="uk-text-center uk-margin-top">
                            <div id="quickSearchDiv" class="uk-hidden-small" >
                                <c:forEach items="${filterToShow['SICS'][0]}" var="list" varStatus="status" >
                                    <button class='uk-button width-1-2-x uk-hidden-small uk-margin-small-top text-center-middle' <c:if test="${list.value[1] == 'false'}">disabled</c:if> onclick="showQuickSearch('${list.key}');"><i class="uk-icon-video-camera"></i><spring:message code="${list.value[0]}"/> (${list.value[2]})</button>
                                </c:forEach>
                            </div>
                            <div id="quickSearchDiv" class="uk-visible-small" >
                                <c:forEach items="${filterToShow['SICS'][0]}" var="list" varStatus="status" >
                                    <button class='uk-button width-1-2 uk-visible-small uk-margin-small-top text-center-middle' <c:if test="${list.value[1] == 'false'}">disabled</c:if> onclick="showQuickSearch('${list.key}');"><i class="uk-icon-video-camera"></i><spring:message code="${list.value[0]}"/> (${list.value[2]})</button>
                                </c:forEach>
                            </div>
                        </li>

                        <li id="liFiltriContent" class="uk-active">
                            <c:if test="${mUser.sportId == '1'}">
                                <div id="resultDiv" style="float:right; margin: 5px;">
                                    <button id="btn2ptPos" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btn2ptPos', 5);" value="2PT+">2PT+</button>
                                    <button id="btn2ptNeg" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btn2ptNeg', 5);" value="2PT-">2PT-</button>
                                    <button id="btn3ptPos" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btn3ptPos', 5);" value="3PT+">3PT+</button>
                                    <button id="btn3ptNeg" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btn3ptNeg', 5);" value="3PT-">3PT-</button>
                                    <button id="btnFF" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btnFF', 5);" value="FF">FF</button>
                                    <button id="btnFS" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btnFS', 5);" value="FS">FS</button>
                                    <button id="btnPP" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btnPP', 5);" value="PP">PP</button>
                                    <button id="btnREC" class="uk-button uk-button-small" onclick="jsSetButtonSelected('btnREC', 5);" value="REC">REC</button>

                                </div>
                            </c:if>
                            <div class="uk-margin-bottom" id="configButton">
                                <div id="ricercaAzione" class="ui-corner-all" >
                                    <!-- CONTENITORE EVENTI DELLA MASCHERA UTILIZZATA PER FARE L'ANALISI -->
                                    <div id="listAzioneRicerca">
                                        <c:set var="iteratorPanel" value="0"/>
                                        <c:forEach var="item" items="${mButtonToShow}">
                                            <c:if test="${iteratorPanel == 0}">
                                                <div uk-accordion data-uk-accordion="{collapse: false,showfirst: true}">
                                                </c:if>
                                                <c:if test="${iteratorPanel>0}">
                                                    <div uk-accordion data-uk-accordion="{collapse: false,showfirst: false}" class="uk-margin-remove">
                                                    </c:if>
                                                    <c:set var="chiave" value="${item.configName}"/>
                                                    <c:set var="chiaveTrim" value="${fn:replace(chiave,'.xml', '')}"/>
                                                    <c:choose>
                                                        <c:when test="${item.configName == 'SICS2015'}">
                                                            <span class="label-config uk-width-1-1 uk-accordion-title uk-margin-small-top uk-margin-bottom-remove">SICS</span>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <span class="label-config uk-width-1-1 uk-accordion-title uk-margin-small-top uk-margin-bottom-remove">${item.configName}</span>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="uk-accordion-content uk-width-1-1 uk-padding-remove">
                                                        <div id="tabs-${iteratorPanel}" class="actionFor${chiaveTrim} uk-accordion uk-text-left">
                                                            <c:set var="iteratorPanel" value="${iteratorPanel+1}"/>
                                                            <c:set var="tagID" value="0"/>
                                                            <c:forEach var="i" begin="0" end="1">
                                                                <div id="colonna${i}" class="width-1-2-x">
                                                                    <c:set var="iterator" value="0"/>
                                                                    <c:forEach var="itemKey" items="${item.sortedByNameKey}">
                                                                        <c:set var="modulo" value="${iterator % 2}"/>
                                                                        <c:set var="item1" value="${item.mapConfigButtons[itemKey]}"/>
                                                                        <c:if test="${mEventFilter.length() == 0 || (mEventFilter.length() > 0 && item1.enabled)}">
                                                                            <c:if test="${modulo == i}">
                                                                                <c:set var="idName" value="${chiave}_${item1.getCode()}"/>
                                                                                <c:choose>
                                                                                    <c:when test="${item1.getTag().size() > 0}">
                                                                                        <button id="${idName}" class="uk-button buttonTag" onclick="jsSetButtonSelected('${idName}', 0);" base-name="${item1.descTrim(mLanguage)}" value="${game.value.homeTeamId}" title="${item1.descTrim(mLanguage)} (${item1.countAction})" <c:if test="${!item1.enabled}">disabled</c:if>>

                                                                                            <c:if test="${item1.enabled}">
                                                                                                <c:set var="tagID" value="${tagID+1}"/> 
                                                                                                <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down" onclick="openTag = true;"></i> 
                                                                                            </c:if>
                                                                                            ${item1.descTrim(mLanguage)} (${item1.countAction})<!--span id="numTagSelected${idName}" class="uk-hidden" onclick="jsRemoveFilter(${tagID+1},0);"></span-->
                                                                                        </button>
                                                                                        <c:choose>
                                                                                            <c:when test="${item1.enabled}">
                                                                                                <div id="tag${tagID}" class="uk-accordion-content uk-text-left">
                                                                                                    <div class="tagEvento uk-form"> 
                                                                                                        <!--risultati per tag della singola azione-->
                                                                                                        <c:forEach var="item2" items="${item1.getTag()}">
                                                                                                            <div class='uk-margin-small buttonTag'>
                                                                                                                <c:set var="tagCode" value="${chiave}_${item1.getCode()}_${item2.getCode()}"/>
                                                                                                                <input type="checkbox" id="${tagCode}" class="uk-margin-right" onclick="jsSetButtonSelected('${tagCode}', 1, '${chiave}_${item1.getCode()}');" <c:if test="${!item2.enabled}">disabled</c:if> value="${item2.descLan(mLanguage)}"/><label for="${tagCode}" title="${item2.descLan(mLanguage)}">${item2.descLan(mLanguage)}</label>
                                                                                                                </div>
                                                                                                        </c:forEach>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </c:when>
                                                                                            <c:otherwise>
                                                                                                <!-- va lasciato altrimenti non funziona apertura tag correttamente e si disallineano i bottoni-->
                                                                                                <div></div>
                                                                                            </c:otherwise>
                                                                                        </c:choose>
                                                                                    </c:when>
                                                                                    <c:otherwise>
                                                                                        <button id="${idName}" class="uk-button buttonTag"  onclick="jsSetButtonSelected('${idName}', 0);" base-name="${item1.descTrim(mLanguage)}" title="${item1.descTrim()}" <c:if test="${!item1.enabled}">disabled</c:if>>
                                                                                                <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down uk-hidden"></i>
                                                                                            ${item1.descTrim(mLanguage)}   (${item1.countAction})
                                                                                        </button>
                                                                                        <div id="tag${tagID}" class="uk-accordion-content"></div>

                                                                                    </c:otherwise>
                                                                                </c:choose>
                                                                            </c:if>
                                                                            <c:set var="iterator" value="${iterator+1}"/>
                                                                        </c:if>
                                                                    </c:forEach>
                                                                </div>
                                                            </c:forEach>
                                                        </div></div>
                                                </div>
                                            </c:forEach>
                                        </div> 
                                    </div>
                                </div>
                        </li>
                        
                        <c:if test="${mTacticalButtonToShow != null}">
                        <li id="liFiltriTacticalContent" class="uk-text-center uk-margin-top">
                            <c:set var="iteratorPanel" value="0"/>
                            <c:forEach var="item" items="${mTacticalButtonToShow}">
                                <c:if test="${iteratorPanel == 0}">
                                    <div uk-accordion data-uk-accordion="{collapse: false,showfirst: true}">
                                    </c:if>
                                    <c:if test="${iteratorPanel>0}">
                                        <div uk-accordion data-uk-accordion="{collapse: false,showfirst: false}" class="uk-margin-remove">
                                        </c:if>
                                        <c:set var="chiave" value="${item.configName}"/>
                                        <c:set var="chiaveTrim" value="${fn:replace(chiave,'.xml', '')}"/>
                                        <c:choose>
                                            <c:when test="${item.configName == 'SICS2015'}">
                                                <span class="label-config uk-width-1-1 uk-accordion-title uk-margin-small-top uk-margin-bottom-remove">SICS</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="label-config uk-width-1-1 uk-accordion-title uk-margin-small-top uk-margin-bottom-remove">${item.configName}</span>
                                            </c:otherwise>
                                        </c:choose>
                                        <div class="uk-accordion-content uk-width-1-1 uk-padding-remove">
                                            <div id="tabs-${iteratorPanel}" class="actionFor${chiaveTrim} uk-accordion uk-text-left">
                                                <c:set var="iteratorPanel" value="${iteratorPanel+1}"/>
                                                <c:set var="tagID" value="0"/>
                                                <c:forEach var="i" begin="0" end="1">
                                                    <div id="colonna${i}" class="width-1-2-x">
                                                        <c:set var="iterator" value="0"/>
                                                        <c:forEach var="itemKey" items="${item.sortedByNameKey}">
                                                            <c:set var="modulo" value="${iterator % 2}"/>
                                                            <c:set var="item1" value="${item.mapConfigButtons[itemKey]}"/>
                                                            <c:if test="${mEventFilter.length() == 0 || (mEventFilter.length() > 0 && item1.enabled)}">
                                                                <c:if test="${modulo == i}">
                                                                    <c:set var="idName" value="TACT-${chiave}_${item1.getCode()}"/>
                                                                    <c:choose>
                                                                        <c:when test="${item1.getTag().size() > 0}">
                                                                            <button id="${idName}" class="uk-button buttonTag" onclick="jsSetButtonSelected('${idName}', 0);" base-name="${item1.descTrim(mLanguage)}" value="${game.value.homeTeamId}" title="${item1.descTrim(mLanguage)} (${item1.countAction})" <c:if test="${!item1.enabled}">disabled</c:if>>

                                                                                <c:if test="${item1.enabled}">
                                                                                    <c:set var="tagID" value="${tagID+1}"/> 
                                                                                    <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down" onclick="openTag = true;"></i> 
                                                                                </c:if>
                                                                                ${item1.descTrim(mLanguage)} (${item1.countAction})<!--span id="numTagSelected${idName}" class="uk-hidden" onclick="jsRemoveFilter(${tagID+1},0);"></span-->
                                                                            </button>
                                                                            <c:choose>
                                                                                <c:when test="${item1.enabled}">
                                                                                    <div id="tag${tagID}" class="uk-accordion-content uk-text-left">
                                                                                        <div class="tagEvento uk-form"> 
                                                                                            <!--risultati per tag della singola azione-->
                                                                                            <c:forEach var="item2" items="${item1.getTag()}">
                                                                                                <div class='uk-margin-small buttonTag'>
                                                                                                    <c:set var="tagCode" value="TACT-${chiave}_${item1.getCode()}_${item2.getCode()}"/>
                                                                                                    <input type="checkbox" id="${tagCode}" class="uk-margin-right" onclick="jsSetButtonSelected('${tagCode}', 1, '${chiave}_${item1.getCode()}');" <c:if test="${!item2.enabled}">disabled</c:if> value="${item2.descLan(mLanguage)}"/><label for="${tagCode}" title="${item2.descLan(mLanguage)}">${item2.descLan(mLanguage)}</label>
                                                                                                    </div>
                                                                                            </c:forEach>
                                                                                        </div>
                                                                                    </div>
                                                                                </c:when>
                                                                                <c:otherwise>
                                                                                    <!-- va lasciato altrimenti non funziona apertura tag correttamente e si disallineano i bottoni-->
                                                                                    <div></div>
                                                                                </c:otherwise>
                                                                            </c:choose>
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            <button id="${idName}" class="uk-button buttonTag"  onclick="jsSetButtonSelected('${idName}', 0);" base-name="${item1.descTrim(mLanguage)}" title="${item1.descTrim()}" <c:if test="${!item1.enabled}">disabled</c:if>>
                                                                                    <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down uk-hidden"></i>
                                                                                ${item1.descTrim(mLanguage)}   (${item1.countAction})
                                                                            </button>
                                                                            <div id="tag${tagID}" class="uk-accordion-content"></div>

                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </c:if>
                                                                <c:set var="iterator" value="${iterator+1}"/>
                                                            </c:if>
                                                        </c:forEach>
                                                    </div>
                                                </c:forEach>
                                            </div>
                                        </div>
                                    </div>
                                </c:forEach>
                        </li>
                        </c:if>
                        
                        <li id="liFiltroGiocatoriContent">
                            <div class="uk-margin-bottom" id="players">

                                <!--label class="uk-text-bold"><spring:message code="video.squadre"/>:</label-->
                                <div id="filtroGiocatore" class="ui-corner-all uk-form uk-text-center">
                                    <c:forEach items="${mListPlayers}" var="list" varStatus="status" >
                                        <c:if test="${list.key.id == mTeam.id}">
                                            <div class="uk-margin">
                                                <span class="width-1-1-x">
                                                    <button id="team${list.key.id}" class="uk-button buttonTag uk-margin-top teamAllButton" onclick="jsSetButtonSelected('team${list.key.id}', 2);" value="${list.key.id}" teamname="${list.key.name}" title="${list.key.name}">${list.key.name} </button>
                                                </span>
                                                <div>
                                                    <c:forEach items="${list.value}" var="player" >
                                                        <span class="width-1-2-x">
                                                            <button id="playerButton${player.id}" class="uk-button buttonTag teamId${list.key.id}" onclick="jsSetButtonSelected('playerButton${player.id}', 3);" value="${player.id}" title="${player.matchNumber} - ${player.known_name}">${player.matchNumber} - ${player.known_name} (${player.actionAmount})</button>
                                                        </span>
                                                    </c:forEach>
                                                </div>
                                            </div>
                                        </c:if>
                                    </c:forEach>

                                    <c:forEach items="${mListPlayers}" var="list" varStatus="status" >
                                        <c:if test="${list.key.id != mTeam.id and ((fromTeamId == null and (mGame.size() == 1 or !mEventPresent)) or (fromTeamId != null and mGame.size() == 1))}">
                                            <div class="uk-margin">
                                                <span class="width-1-1-x" >
                                                    <button id="team${list.key.id}" class="uk-button buttonTag uk-margin-top teamAllButton" onclick="jsSetButtonSelected('team${list.key.id}', 2);" value="${list.key.id}" teamname="${list.key.name}" title="${list.key.name}">${list.key.name} </button>
                                                </span>
                                                <div>
                                                    <c:forEach items="${list.value}" var="player" >
                                                        <span class="width-1-2-x">
                                                            <button id="playerButton${player.id}" class="uk-button buttonTag teamId${list.key.id}" onclick="jsSetButtonSelected('playerButton${player.id}', 3);" value="${player.id}" title="${player.matchNumber} - ${player.known_name}">${player.matchNumber} - ${player.known_name} (${player.actionAmount})</button>
                                                        </span>
                                                    </c:forEach>
                                                </div>
                                            </div>
                                        </c:if>
                                    </c:forEach>
                                </div>
                            </div>
                        </li>
                        <li id="liAggiungiEventoContent" class="uk-width-1-1 uk-text-center">
                            <div uk-accordion data-uk-accordion="{collapse: false,showfirst: true}">
                                <span class="label-config uk-width-1-1 uk-accordion-title uk-margin-small-top uk-margin-bottom-remove uk-text-uppercase"><spring:message code="video.add.event"/></span>
                                <div class="uk-accordion-content uk-width-1-1 uk-padding-remove">
                                    <div id="tabs-addEvent" class="actionFor${chiaveTrim} uk-accordion uk-text-left">
                                        <c:set var="iteratorPanel" value="${iteratorPanel+1}"/>
                                        <c:set var="tagID" value="0"/>
                                        <c:forEach var="i" begin="0" end="1">
                                            <div id="colonna${i}" class="width-1-2-x">
                                                <c:set var="iterator" value="0"/>
                                                <c:forEach var="item1" items="${mAllEventButtons}">
                                                    <c:set var="modulo" value="${iterator % 2}"/>
                                                    <c:if test="${modulo == i}">
                                                        <c:set var="idName" value="${chiave}_${item1.getCode()}"/>
                                                        <c:choose>
                                                            <c:when test="${item1.getTag().size() > 0}">
                                                                <button class="uk-button buttonTag" id="addEvent_${item1.getCode()}" onclick="addRemoveAdvancedFilter('${item1.getCode()}');" base-name="${item1.descTrim(mLanguage)}" title="${item1.descTrim(mLanguage)}" <c:if test="${!item1.enabled}">disabled</c:if>>
                                                                    <c:if test="${item1.enabled}">
                                                                        <c:set var="tagID" value="${tagID+1}"/> 
                                                                        <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down" onclick="openTag = true;"></i> 
                                                                    </c:if>
                                                                    ${item1.descTrim(mLanguage)}
                                                                </button>
                                                                <c:choose>
                                                                    <c:when test="${item1.enabled}">
                                                                        <div id="tag${tagID}" class="uk-accordion-content uk-text-left">
                                                                            <div class="tagEvento uk-form"> 
                                                                                <!--risultati per tag della singola azione-->
                                                                                <c:forEach var="item2" items="${item1.getTag()}">
                                                                                    <div class='uk-margin-small buttonTag'>
                                                                                        <c:set var="tagCode" value="addEvent_${item1.getCode()}_${item2.getCode()}"/>
                                                                                        <input type="checkbox" class="uk-margin-right ${item1.getCode()}" id="${tagCode}" onclick="addRemoveAdvancedFilter('${item2.getCode()}')" <c:if test="${!item2.enabled}">disabled</c:if> value="${item2.descLan(mLanguage)}"/>
                                                                                        ${item2.descLan(mLanguage)}
                                                                                    </div>
                                                                                </c:forEach>
                                                                            </div>
                                                                        </div>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <!-- va lasciato altrimenti non funziona apertura tag correttamente e si disallineano i bottoni-->
                                                                        <div></div>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                        </c:when>
                                                        <c:otherwise>
                                                                <button class="uk-button buttonTag" id="addEvent_${item1.getCode()}" onclick="addRemoveAdvancedFilter('${idName}');" base-name="${item1.descTrim(mLanguage)}" title="${item1.descTrim()}" <c:if test="${!item1.enabled}">disabled</c:if>>
                                                                    <i class="uk-float-left uk-accordion-title uk-button-mini uk-icon uk-icon-caret-down uk-hidden"></i>
                                                                    ${item1.descTrim(mLanguage)}
                                                                </button>
                                                                <div class="uk-accordion-content"></div>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </c:if>
                                                    <c:set var="iterator" value="${iterator+1}"/>
                                                </c:forEach>
                                            </div>
                                        </c:forEach>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li id="liAggiungiStagioneContent" class="uk-width-1-1 uk-text-center">
                            <div class="uk-margin-top" id="seasons-container">
                                <c:forEach var="season" varStatus="status" items="${mAllSeasons}">
                                    <div class="width-1-2-x">
                                        <button id="season${season.id}" seasonid="${season.id}" class="uk-button uk-margin-top-small" onclick="jsManageActive(this); manageExtraSeasons();" ${season.visible != null && !season.visible ? 'disabled' : ''}><b>${season.name}</b></button>
                                    </div>
                                </c:forEach>
                            </div>
                        </li>
                        <li id="liSelezionateContent" class="uk-width-1-1 uk-text-center">
                            <div id="divSelezionate" class="uk-width-1-1 uk-text-center">
                                <c:forEach items="${mGame}" var="game" varStatus="status" >
                                    <div class="width-1-2-x">
                                        <button id="match${game.value.idFixture}" class="uk-button uk-margin-top uk-button-success partiteSelezionate" onclick="jsSetButtonSelected('match${game.value.idFixture}', 4);" value="${game.value.idFixture}" title="${game.value.homeTeam}-${game.value.awayTeam} ${game.value.dateString}"><b>${game.value.homeTeam}-${game.value.awayTeam} ${game.value.dateString}</b> </button>
                                    </div>
                                </c:forEach>
                            </div>
                        </li>
                        <li>
                            <div id="matchData">
                                <!-- dati partita caricati da JQuery -->
                            <!--	<p class="data"><b>${game.value.homeTeam} - ${game.value.awayTeam}</b><br><spring:message code="video.risultato"/> <b>${game.value.homeTeamScore}-${game.value.awayTeamScore}</b></br><spring:message code="video.data"/> <b>${game.value.dateFullString}</b> <br/><spring:message code="video.giornata"/> <b>${game.value.matchday}</b><br/><c:if test="${!empty game.value.refereeName}"> <spring:message code="video.arbitro"/> <b>${game.value.refereeName}</b></c:if><br/> <c:if test="${!empty game.value.assistant1Name}"> <spring:message code="video.assistenti"/> <b>${game.value.assistant1Name} - ${game.value.assistant2Name}</c:if></b></p> -->
                            </div>
                        </li>
                        </ul>
                    </div>
                </div>

                <!--eventi / partite / calendario / playlistTv-->
                <div id="sideNav" class="uk-width-5-10 uk-hidden-small">

                    <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left uk-margin-bottom" data-uk-tab="{connect:'#sideNavContent'}">
                        <li id="liActions" class="uk-active"><a href="#" onclick="changeButtonsVisibility(true);"><spring:message code="video.eventi"/><span id="numEvents" class="uk-badge uk-hidden uk-margin-small-left">0</span></a></li>
                        <li id="liTabellino"><a href="#" onclick="checkTabellino();"><spring:message code="menu.user.tabellino"/></a></li>
                        <li id="liMatches" ><a href="#" onclick="changeButtonsVisibility(false);"><spring:message code="video.partite"/><span id="numMatches" class="uk-badge uk-hidden" style="margin-left: 5px;" onclick="jsRefreshMatches('1');">0</span></a></li>
                        <li id="liCalendario"><a href="#" onclick="changeButtonsVisibility(false);"><spring:message code="menu.user.calendario"/></a></li>
                        <li id="liPlaylistTv"><a href="#" onclick="changeButtonsVisibility(false, true);"><spring:message code="menu.user.playlist"/></a></li>
                        <li id="liPosizionale"><a href="#" onclick="changeButtonsVisibility(false, true, true);"><spring:message code="menu.user.posizionale"/></a></li>
                        <li id="liPosizionaleShots"><a href="#" onclick="changeButtonsVisibility(false, true, true, true);"><spring:message code="menu.user.posizionale.shots"/></a></li>
                    </ul>

                <span class="uk-float-right uk-margin-small-top uk-margin-small-right uk-hidden-small">
                    <!--<button id="playActions" class="uk-button uk-button-small" title="<spring:message code='tooltip.playeventi'/>" onclick="startAction('', 0);"><i class="uk-icon-play-circle uk-icon-small" style="color: green;"></i></button>-->
                    <button id="checkedActionsButton" class="uk-button uk-button-small uk-hidden" title="<spring:message code="eventi.selezionati"/>" onclick="startAction('', 0, true);"><i class="uk-icon-play-circle" style="color: green;"></i><span id="numCheckedEvents" style="margin-left: 5px;">0</span></i></button>
                    <button id="inCareerButton" class="uk-button uk-button-small" onclick="manageCareer()" title="<spring:message code='menu.video.in.carriera'/>"><spring:message code='menu.video.in.carriera'/></button>
                    <button id="removeDuplicatesButton" class="uk-button uk-button-small" onclick="manageRemoveDuplicates()" title="<spring:message code='tooltip.raggruppa.clip'/>"><img height="15px" width="15px" src="/sicstv/images/group-event.png"></button>
                    <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}">
                        <button id="doExportActionExcel" class="uk-button uk-button-small" title="<spring:message code='tooltip.esporta.excel'/>"><img height="12px" width="12px" src="/sicstv/images/excel-file.png"></button>
                        <div class="uk-dropdown uk-dropdown-small uk-text-left">
                            <center>
                                <div>
                                    <h3 style="text-transform: uppercase; font-weight: bold">
                                        <spring:message code='tooltip.esporta.excel'/>
                                        <p style="font-size: 10px; font-weight: normal; text-transform: none">(<spring:message code='menu.user.export.excel.subtitle'/>)</p>
                                    </h3>
                                </div>
                            </center>
                            <div style="display: flex">
                                <!--<button style="width: 50%" onclick="exportEventExcel();" class="uk-button uk-button-small" title="<spring:message code='tooltip.esporta.excel'/>"> <spring:message code='tooltip.excel'/></button>-->
                                <button style="width: 100%" onclick="exportEventCsv();" class="uk-button uk-button-small" title="<spring:message code='tooltip.esporta.excel'/>"> <spring:message code='tooltip.csv'/></button>
                            </div>
                        </div>
                    </div>
                    <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}" onclick="checkExport();">
                        <button id="doExportAction" class="uk-button uk-button-small" title="<spring:message code='tooltip.esporta'/>" ><i class="uk-icon-upload"></i></button>
                        <div id="dropdownExport" class="uk-dropdown uk-dropdown-small uk-text-left">
                            <div style="padding: 5px">
                                <center>
                                    <div>
                                        <h3 style="text-transform: uppercase; font-weight: bold"><spring:message code='tooltip.esporta'/></h3>
                                    </div>
                                </center>
                                <div style="display: flex; width: 40%; float: left">
                                    <spring:message code="playlist.descrizione"/>
                                </div>
                                <div style="display: flex; width: 60%">
                                    <input type="text" id="nameFileExport" style="width: 100%"/>
                                </div>
                                <hr/>
                                <div>
                                    <div style="display: grid; 
                                        grid-template-columns: 40% 60%; 
                                        grid-template-rows: 1fr 1fr; 
                                        gap: 0px 0px; 
                                        grid-template-areas: 
                                          'options maxQuality'
                                          'options single'
                                          'options tactic';
                                        align-items: center; ">
                                        <div style="grid-area: options"><spring:message code="playlist.opzioni"/></div>
                                        <div style="grid-area: maxQuality">
                                            <input id="exportMaxQuality" type="checkbox"/><label for="exportMaxQuality"><spring:message code="esporta.massima.qualita"/></label>
                                        </div>
                                        <div style="grid-area: single">
                                            <input id="exportmulti" type="checkbox" value="multi"/><label for="exportmulti"><spring:message code='esporta.singoli'/></label>
                                        </div>
                                        <div style="grid-area: tactic">
                                            <input id="exportTACT" type="checkbox" name="quality" value="TACT" <c:if test="${!exportTactAvailable}">disabled</c:if>/><label for="exportTACT"><spring:message code="export.videoTattico"/></label>
                                        </div>
                                    </div>
                                </div>
                                <hr/>
                                <div id="exportRequirementDiv" class="uk-hidden">
                                    <div style="display: grid; 
                                        grid-template-columns: 40% 60%; 
                                        grid-template-rows: 1fr 1fr; 
                                        gap: 0px 0px; 
                                        grid-template-areas: 
                                          'requirements first'
                                          'requirements second'
                                          'requirements third'
                                          'requirements fourth';
                                        align-items: center; ">
                                        <div style="grid-area: requirements; color: red; font-size: 13px"><spring:message code="playlist.requisiti"/></div>
                                        <div style="grid-area: first">
                                            <img id="firstExportRequirement" height="14px" width="14px" style="margin-top: -3px" src="/sicstv/images/cancel.png"/>
                                            <label id="firstExportRequirementLabel" for="firstExportRequirement"><spring:message code='playlist.requisiti.1'/></label>
                                        </div>
                                        <div style="grid-area: second">
                                            <img id="secondExportRequirement" height="14px" width="14px" style="margin-top: -3px" src="/sicstv/images/cancel.png"/>
                                            <label id="secondExportRequirementLabel" for="secondExportRequirement"><spring:message code='playlist.requisiti.2'/></label>
                                        </div>
                                        <div style="grid-area: third">
                                            <img id="thirdExportRequirement" height="14px" width="14px" style="margin-top: -3px" src="/sicstv/images/cancel.png"/>
                                            <label id="thirdExportRequirementLabel" for="thirdExportRequirement"><spring:message code='playlist.requisiti.3'/></label>
                                        </div>
                                        <div style="grid-area: fourth">
                                            <img id="fourthExportRequirement" height="14px" width="14px" style="margin-top: -3px" src="/sicstv/images/cancel.png"/>
                                            <label id="fourthExportRequirementLabel" for="fourthExportRequirement"><spring:message code='playlist.requisiti.4'/></label>
                                        </div>
                                    </div>
                                    <hr/>
                                </div>
                                <div class="uk-hidden" id="exportEffettivoWarning" style="margin-bottom: 10px; color: #ffa126">
                                    <img height="14px" width="14px" style="margin-top: -3px" src="/sicstv/images/warning.png"/>
                                    <label><spring:message code='playlist.requisiti.1.tempo.effettivo'/></label>
                                </div>
                                <div class="uk-hidden" id="exportRunningMessage" style="margin-bottom: 10px; font-size: 20px">
                                    <label><spring:message code="player.user.export.title"/></label>
                                </div>
                                <button id="exportActionsButton" onclick="exportActions();" class="uk-button uk-dropdown-close" disabled><i class="uk-icon-cog uk-margin-right"></i><spring:message code="export.elabora"/></button>
                                <span id="exportActionNoEvents" class="uk-hidden" style="margin-left: 20px; color: red"><i class="uk-icon-warning"></i> <spring:message code="menu.user.export.no.clip.warning"/></span>
                            </div>
                        </div>
                    </div>
                    <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}">
                        <button id="doAddToPlaylistAction" class="uk-button uk-button-small" title="<spring:message code="playlist.add"/>"><i class="uk-icon-plus"></i></button>
                        <div id="dropdownPlaylist" class="uk-dropdown uk-dropdown-small uk-text-left">
                            <div style="padding: 5px">
                                <center>
                                    <div>
                                        <h3 style="text-transform: uppercase; font-weight: bold"><spring:message code='playlist.add'/></h3>
                                    </div>
                                </center>
                            </div>
                            <div style="display: flex; width: 40%; float: left">
                                <spring:message code="playlist.nome"/>
                            </div>
                            <div style="display: flex; width: 60%">
                                <input type="text" id="namePlaylist" maxlength="32" style="width: 100%"/>
                            </div>
                            <div style="display: flex; width: 40%; float: left; margin-top: 5px">
                                <spring:message code="playlist.descrizione"/>
                            </div>
                            <div style="display: flex; width: 60%; margin-top: 5px">
                                <input type="text" id="descriptionPlaylist" maxlength="255" style="width: 100%"/>
                            </div>
                            <button onclick="addToPlaylist();" class="uk-button uk-dropdown-close" style="margin-top: 10px"><spring:message code="playlist.savenew"/></button>
                            <div id="playlistTvButtonLi"<c:if test="${mPlaylistList == null || mPlaylistList.size() == 0}"> class="uk-hidden"</c:if> style="max-height: 355px; overflow: auto; padding-right: 5px">
                                <div class="playlistTv">
                                    <hr>
                                    <center>
                                        <div>
                                            <h4 style="text-transform: uppercase; font-weight: bold">
                                                <spring:message code="playlist.fastadd"/>
                                                <p style="font-size: 10px; font-weight: normal; text-transform: none">(<spring:message code="playlist.clicca.per.aggiungere"/>)</p>
                                            </h4>
                                        </div>
                                    </center>
                                </div>
<!--                                <div id="playlistTvButtonDiv">
                                    <c:forEach var="item" items="${mPlaylistList}">
                                        <button onclick="addToPlaylist(${item.id});" class="uk-button uk-dropdown-close playlistTvButton" style="margin-right: 5px"><c:if test="${item.shared}"><i class="uk-icon-link"></i> </c:if>${item.name}</button>
                                    </c:forEach>
                                </div>-->
                            </div>
                        </div>
                    </div>
                    <c:if test="${not empty mPlaylistTvId}">
                    <button id="doRemoveFromPlaylistAction" class="uk-button uk-button-small" title="<spring:message code='playlist.remove.title'/>" onclick="removeClipsFromPlaylist()"><i class="uk-icon-trash"></i></button>
                    </c:if>
                    <button id="addToMulti" class="uk-button uk-button-small uk-hidden" title="<spring:message code='tooltip.multipartita'/>" onclick="jsGoToMultipartita('${personalSource}');"><i class="uk-icon-external-link"></i></button>
                    <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}" >
                        <button id="selectedMatches" class="uk-button uk-button-small uk-hidden" title="<spring:message code='tooltip.selezionati'/>"><i class="uk-icon-bars"></i> 0 </button> 
                        <!--<a href="#" id="selectedMatches" class="uk-button uk-button-small" ><i class="uk-icon-bars  "></i> 0 </a>-->
                        <span id="spanSelAll" class="uk-dropdown">
                            <ul class="uk-nav uk-nav-navbar">
                                <li class="selAllSubnav"><a onclick="jsSelectAll(true, 'matchesCalendarContent');"><i class="uk-icon-check-square-o uk-margin-right"></i><spring:message code="menu.selezionatutto"/></a></li>
                                <li class="uk-nav-divider"></li>
                                <li class="selAllSubnav"><a onclick="jsSelectAll(false, 'matchesCalendarContent');"><i class="uk-icon-square-o uk-margin-right"></i><spring:message code="menu.deselezionatutto"/></a></li>
                            </ul>
                        </span>
                    </span>
                    <c:if test="${mGame.size() > 1}">
                    <!--<button id="btnSearch" class="uk-button uk-button-small" data-uk-offcanvas="{target:'#divFilter'}" title="<spring:message code='tooltip.ricerca'/>"><i class="uk-icon-search"></i> </button>-->
                    </c:if>
                    <button id='btnInfoPosizionale' class="uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small uk-hidden">
                        <i class="uk-icon-info-circle"></i>
                    </button>
                </span>

                <ul id="sideNavContent" class="uk-switcher uk-margin-all" style="max-height: 85vh; overflow: auto">
                    <li class="uk-active" id="eventi">
                        <table id="azioni" class="uk-hidden table table-bordered table-hover uk-margin-all-small uk-table">
                            <thead>
                                <tr>
                                    <th class="uk-text-center">
                                        <label for="checkboxSelectAllEvents">
                                            <input class="checkbox-big" type="checkbox" id="checkboxSelectAllEvents" onclick="jsSelectAllEvents('azioni', true);"/>
                                            <label for="checkboxSelectAllEvents"></label>
                                        </label>
                                    </th>
                                    <th><spring:message code="video.descrizione"/></th>
                                    <th><spring:message code="video.half"/></th>
                                    <th><spring:message code="video.inizio"/></th>
                                    <th><spring:message code="video.durata"/></th>
                                    <th><spring:message code="video.squadra"/></th>
                                    <c:if test="${fn:length(mEventFilter) > 0 || mGame.size() > 1}">
                                    <th><spring:message code="video.partita"/></th>
                                    </c:if>
                                    <th><spring:message code="video.giocatore"/></th>
                                    <th><spring:message code="video.esito"/></th>
                                    <c:if test="${showAuthor}">
                                        <th><spring:message code="video.autore"/></th>
                                    </c:if>
                                </tr>
                            </thead>
                        </table>
                    </li>

                    <li id="liTabellinoContent" class=" ">
                        <div id="divTabellino" class="uk-width-1-1 uk-text-center">
                            <div id="divTabellinoContent" class="uk-overflow-container" style="margin-top: 15px">
                                
                            </div>
                        </div>
                    </li>
                    
                    <li id="partite" class=" ">
                        <!--Content loaded with Ajax -->
                    </li>

                    <li id="calendario" class=" ">
                        <!--Content loaded with Ajax-->

                    </li>
                    
                    <li id="playlistTv" class=" ">
                        <!--Content loaded with Ajax-->

                    </li>
                    <li id="posizionale" class=" ">
                        <div>
                            <div style="text-align: center; font-size: 15px">
                                <span id="posizionaleModalityFilter" class="uk-hidden" style="text-transform: uppercase; font-weight: bold">Filter Mode</span>
                                <span id="posizionaleModalityVideo" class="uk-hidden" style="text-transform: uppercase; font-weight: bold">Game Mode</span>
                            </div>
                            <div id="posizionaleContainer" style="text-align: center"></div>
                            <div style="display: flex; padding-left: 6%">
                                <div style="width: 50%">
                                    <button class="uk-button" onclick="changeOptions(this, 'orientation');" title="<spring:message code="menu.user.zome.campo"/>"><img src="/sicstv/images/half-pos.png" width="20px"/></button>
                                    <button class="uk-button" onclick="resetAllFilters();" title="<spring:message code="video.resetfiltri.posizionale"/>"><i class="uk-icon-reply"></i></button>
                                    <button class="uk-button" onclick="takeBasePitchScreen(document.getElementById('basePitch'), 'sicstv-screenshot.png');" title="<spring:message code="video.take.screenshot"/>"><i class="uk-icon-camera"></i></button>
                                    <button class="uk-button uk-hidden" id="closeClipViewer2" onclick="backToVideoMode();" title="<spring:message code="menu.user.torna.video"/>"><i class="uk-icon-close"></i></button>
                                    <span class="uk-margin-small-left" id="posizionaleHelper"><spring:message code="positional.modality.half"/></span>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li id="posizionaleShots" class=" ">
                        <div>
                            <div style="text-align: center; font-size: 15px">
                                <span id="posizionaleShotsModalityFilter" class="uk-hidden" style="text-transform: uppercase; font-weight: bold">Filter Mode</span>
                                <span id="posizionaleShotsModalityVideo" class="uk-hidden" style="text-transform: uppercase; font-weight: bold">Game Mode</span>
                            </div>
                            <div id="posizionaleShotsContainer" style="text-align: center"></div>
                            <div style="display: flex; padding-left: 6%">
                                <div style="width: 50%">
                                    <button class="uk-button" onclick="resetShotFilters();" title="<spring:message code="video.resetfiltri.posizionale"/>"><i class="uk-icon-reply"></i></button>
                                    <button class="uk-button" onclick="takeBasePitchShotsScreen(document.getElementById('basePitchShots'), 'sicstv-screenshot.png');" title="<spring:message code="video.take.screenshot"/>"><i class="uk-icon-camera"></i></button>
                                    <button class="uk-button" onclick="managePercentageView();" id="posizionaleShotsPercentage"><i class="uk-icon-percent"></i></button>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>

                <div id="divFilter" class="uk-offcanvas">
                    <div class="uk-offcanvas-bar uk-offcanvas-bar-flip">
                        <%@ include file="searchMulti.jsp" %>
                    </div>
                </div>
            </div>
        </div>

        <div id="modalModifyClip" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog dynamicModalWidth" style="min-height: 0;min-width: 0;width: auto;height: auto;" id="dropdownModifyClip">
                <div id="editClipPage">
                </div>
            </div>
        </div>
                    
        <div id="recModal" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 25%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeRecModal();">&times;</span>
                </div>
                <div class="container" style="min-height: 25vh; max-height: 90vh;">
                    <ul class="uk-nav uk-nav-dropdown">
                        <li>
                            <span style="font-size: 15px; font-weight: bold; text-transform: uppercase"><spring:message code="video.record.title"/></span>
                        </li>
                        <li class="uk-margin-top">
                            <div class="uk-flex justify-content-center align-items-center">
                                <div class="uk-width-2-10">
                                    <spring:message code="video.record.input.description"/> <span style="color: red">*</span>
                                </div>
                                <div class="uk-width-8-10">
                                    <input type="text" id="rec-title" maxlength="64"/>
                                </div>
                            </div>
                        </li>
                        <li class="uk-margin-top">
                            <div class="uk-flex align-items-center">
                                <div class="uk-width-2-10">
                                    <spring:message code="video.record.input.from"/>
                                </div>
                                <div class="uk-width-2-10">
                                    <input class="uk-width-1-1" type="number" id="rec-from" min="0"/>
                                </div>
                                
                                <div class="uk-width-1-10 uk-margin-left">
                                    <spring:message code="video.record.input.to"/>
                                </div>
                                <div class="uk-width-2-10">
                                    <input class="uk-width-1-1" type="number" id="rec-to" min="0"/>
                                </div>
                            </div>
                        </li>
                        <li class="uk-margin-top">
                            <div class="uk-flex justify-content-center align-items-center">
                                <div class="uk-width-2-10">
                                    <spring:message code="video.record.input.team"/> <span style="color: red">*</span>
                                </div>
                                <div class="uk-width-8-10">
                                    <div class="uk-form">
                                        <select class="uk-width-5-10" id="rec-team-select" onchange="loadRecPlayers();">
                                            
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </li>
                        <li class="uk-margin-top">
                            <div class="uk-flex justify-content-center align-items-center">
                                <div class="uk-width-2-10">
                                    <spring:message code="video.record.input.player"/>
                                </div>
                                <div class="uk-width-8-10">
                                    <div class="uk-form">
                                        <select class="uk-width-5-10 skip-hover" style="min-height: 150px;" id="rec-player-select" multiple>
                                            
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <button style="min-width:80px;" onclick="closeRecModal();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                    <button style="min-width:80px;" onclick="createPersonalEvent();" class="uk-text-large uk-button js-modal-confirm"><spring:message code="menu.conferma"/></button>
                </div>
            </div>
        </div>
                
        <div id="gotoModal" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 25%;width:auto;height:auto;margin-top:20%">
                <div>
                    <span class="modalClose" onclick="closeGotoModal();">&times;</span>
                </div>
                <div class="container" style="min-height: 3vh; max-height: 90vh;">
                    <ul class="uk-nav uk-nav-dropdown">
                        <li>
                            <div class="uk-flex justify-content-center align-items-center">
                                <div class="uk-width-2-10">
                                    <div class="uk-form" class="uk-width-1-1">
                                        <select class="skip-hover" id="goto-half" class="uk-width-1-1">
                                            
                                        </select>
                                    </div>
                                </div>
                                <div class="uk-margin-left uk-width-2-10">
                                    <input id="goto-minutes" type="number" min="0" class="uk-width-1-1">
                                </div>
                                <span class="uk-margin-left-small">-</span>
                                <div class="uk-margin-left-small uk-width-2-10">
                                    <input id="goto-seconds" type="number" min="0" class="uk-width-1-1">
                                </div>
                                <div class="uk-margin-left-small uk-width-2-10">
                                    <button onclick="goto();" class="uk-margin-left uk-button js-modal-confirm"><spring:message code="video.goto"/></button>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </body>
</html>
