<%-- 
    Document   : filterMulti
    Created on : 18-ago-2015, 14.50.30
    Author     : MAC1
--%>

<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <span class="uk-margin-left">
				<span id="comboCompetition">Competizione</span>
				<select id="selectCompetition" onchange="loadFilterMulti();" class="uk-form-select">
					<option value='' selected='selected'>&nbsp;</option>
					<c:forEach items="${mCompetition}" var="comp">

						<option value="${comp.id}">${comp.name}</option>
					</c:forEach>
				</select>
			</span>
			<span class="uk-margin-left">
				<span id="comboTeam">Squadra</span>
				<select id="selectTeam" class="uk-form-select">
					<option value='' selected='selected'>&nbsp;</option>
					<c:forEach items="${mTeam}" var="team">
						<option value="${team.id}">${team.name}</option>
					</c:forEach>
				</select>
			</span>
			<span class="uk-margin-left" >
				<span id="comboMatchDay">Giornata</span>
				<select id="selectMatchDay" class="uk-form-select">
					<option value='' selected='selected'>&nbsp;</option>
					<c:forEach var="i" begin="1" end="${mMaxDay}">
						<option value="${i}">${i}</option>
					</c:forEach>

				</select>
			</span>

			<span onclick="searchMulti();" class="uk-button" style="float: right">Applica</span>
			

