<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/flat/flat.css?<%=System.currentTimeMillis()%>" rel="stylesheet">

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>

        <script type="text/javascript">
            var pageStatus = [];
            var totMatches = [];
            var numMatches = [];
            var matchPerPage = 25;
            var dropzone;

            $(document).ready(function () {
                // Configura Dropzone
                Dropzone.autoDiscover = false;
                
                jsRefreshMatches(1);
                initSearchFilter();
                if ($(window).width() <= 768) {
                    $("#liCompetions").addClass("uk-active");
                    $("#tabMatches").removeClass("uk-active");
                } else {
                    $("#tabMatches").addClass("uk-active");
                    $("#matches").addClass("uk-active");
                    $("#liCompetions").removeClass("uk-active");
                }
            });

            // logout ajax
            function ajaxSessionTimeout() {
                window.location.replace("/sicstv/auth/login.htm");
            }
            !function ($) {
                $.ajaxSetup({statusCode: {901: ajaxSessionTimeout}});
            }(window.jQuery);

            function jsRefreshMatches(page) {

                refreshMatches("matchesList", "matches", page);
            }
            /*
            function jsDownload(id) {

                var strUrl = "/sicstv/user/download.htm?id=" + id;
                $.ajax({
                    type: "GET",
                    url: strUrl,
                    cache: false,
                    success: function (msg) {
                        if (msg.substr(0, 4) === "true") {
                            UIkit.notify(msg.substr(5), {status: 'danger'});
                        } else {
                            UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success'});
                            window.location.replace(msg.substr(5));
                        }
                    }
                });
            }
            */

            function filterData() {
                readFilterMulti(false);
                filterMatches('matches', 1);
            }
            
            function manageUploadLogo(competitionId) {
                if ($(event.target).attr("disabled")) {
                    UIkit.notify("<spring:message code="upload.logo.permission.denied"/>", {status: 'danger', timeout: 1000});
                    return;
                }
                
                if (typeof dropzone === "undefined") {
                    dropzone = initializeLogoDropzone("uploadLogoDropzone");
                }
                
                dropzone.options.url = dropzone.options.baseUrl + "competitionId=" + competitionId;
                $("#upload-logo-modal").addClass("uk-open");
                $("#upload-logo-modal").removeClass("uk-hidden");
            }
            
            function closeUploadLogo() {
                $("#upload-logo-modal").removeClass("uk-open");
                $("#upload-logo-modal").addClass("uk-hidden");
            }
        </script>
    </head>
    <body>

        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>

        <div>
            <div class="uk-grid">

                <div id="competitions" class="uk-width-6-10 uk-hidden-small">

                    <div id="breadcrumb">
                        <ul class="uk-breadcrumb uk-margin-left">
                            <li><spring:message code="user.competitions"/> <i id="showCurrentFilter"></i></li>
                        </ul>
                    </div>

                    <div class="uk-flex">
                        <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-5 uk-width-1-1 uk-margin-small-top" >
                            <c:forEach var="item" items="${formCompetitionAll}">
                                <c:choose>
                                    <c:when test="${item.visible}">
                                        <li class="competitionBox uk-margin-left uk-margin-top uk-position-relative" id="${item.id}">
                                            <span onclick="manageUploadLogo(${item.id})" class="uk-float-right uk-margin-remove uk-position-absolute" style="top: 3%; right: 3%" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='upload.competition.logo'/>"><i class="uk-icon-upload" ${item.groupsetId == -1 && mUser.groupsetId != 2 ? "disabled" : ""}></i></span>
                                            <a class="uk-text-center uk-margin-top" href="/sicstv/user/competition.htm?personal=true&formCompetitionId=${item.id}">
                                                <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                <p data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                            </a>
                                        </li>
                                    </c:when>
                                    <c:otherwise>
                                        <li class="competitionBoxDisabled uk-margin-left uk-margin-top" id="${item.id}">
                                            <span class="uk-text-center uk-margin-top ">
                                                <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                <p data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                            </span>
                                        </li>
                                    </c:otherwise>

                                </c:choose>
                            </c:forEach>

                        </ul>
                    </div>

                </div>

                <div id="tabMenu" class="uk-width-4-10">

                    <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#matchesCalendarContent'}" id='calendarTab'>
                        <li id="liCompetions">
                            <a class="uk-visible-small"><spring:message code="user.competitions"/></a>
                        </li>
                        <li id="tabMatches">
                            <a><spring:message code="menu.user.incontri"/>
                                <span id="numMatches" class="uk-badge uk-hidden" onclick="jsRefreshMatches('1');">0</span>
                            </a>
                            <div id="matchesList" class="uk-margin-top uk-margin-left"></div>
                        </li>
                    </ul>

                    <span class="uk-float-right uk-margin-small-top uk-margin-small-right">
                        <button id="addToMulti" class="uk-button uk-button-small" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.multipartita'/>" onclick="jsGoToMultipartita('${personalSource}');"><i class="uk-icon-external-link  "></i></button>
                        <span class="uk-position-relative" data-uk-dropdown="{mode:'click',pos:'top-left'}" >
                            <button id="selectedMatches" class="uk-button uk-button-small" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.selezionati'/>"><i class="uk-icon-bars  "></i> 0 </button> 
                            <!--<a href="#" id="selectedMatches" class="uk-button uk-button-small" ><i class="uk-icon-bars  "></i> 0 </a>-->
                            <span id="spanSelAll" class="uk-dropdown">
                                <ul class="uk-nav uk-nav-navbar">
                                    <li class="selAllSubnav"><a onclick="jsSelectAll(true, 'matchesCalendarContent');"><spring:message code="menu.selezionatutto"/></a></li>
                                    <li class="uk-nav-divider"></li>
                                    <li class="selAllSubnav"><a onclick="jsSelectAll(false, 'matchesCalendarContent');"><spring:message code="menu.deselezionatutto"/></a></li>
                                </ul>							
                            </span>
                        </span>
                        <button id="btnSearch" class="uk-button uk-button-small" data-uk-offcanvas="{target:'#divFilter'}" data-uk-tooltip="{pos:'bottom-left'}" title="<spring:message code='tooltip.ricerca'/>"><i class="uk-icon-search  "></i> </button>
                    </span>

                    <ul id="matchesCalendarContent" class="uk-switcher">
                        <li class="uk-visible-small">
                            <ul class="uk-thumbnav uk-flex-center uk-grid-width-1-4 uk-width-1-1 uk-margin-small-top" >
                                <c:forEach var="item" items="${formCompetitionAll}">
                                    <c:choose>
                                        <c:when test="${item.visible}">
                                            <li class="competitionBox uk-margin-left uk-margin-top" id="${item.id}">
                                                <a class="uk-text-center uk-margin-top" href="/sicstv/user/competition.htm?personal=true&formCompetitionId=${item.id}">
                                                    <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                    <p  data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                                </a>
                                            </li>
                                        </c:when>
                                        <c:otherwise>
                                            <li class="competitionBoxDisabled uk-margin-left uk-margin-top" id="${item.id}">
                                                <span class="uk-text-center uk-margin-top ">
                                                    <img class="logoCompImg" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${item.logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${item.name}">
                                                    <p  data-uk-tooltip="{pos:'bottom'}" title="${item.name}">${item.name}</p>
                                                </span>
                                            </li>
                                        </c:otherwise>

                                    </c:choose>
                                </c:forEach>

                            </ul>
                        </li>
                        <li id="matches"></li>
                        <!--li id="filter"><%@ include file="searchMulti.jsp" %></li-->
                    </ul>

                    <div id="divFilter" class="uk-offcanvas">
                        <div class="uk-offcanvas-bar uk-offcanvas-bar-flip">
                            <%@ include file="searchMulti.jsp" %>
                        </div>
                    </div>

                </div>
            </div>
        </div>
                        
        <div id="upload-logo-modal" class="uk-modal uk-hidden uk-text-left" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
                <div>
                    <span class="modalClose" onclick="closeUploadLogo();">&times;</span>
                    <h3><spring:message code="upload.competition.logo"/></h3>
                    <div class="uk-margin uk-modal-content uk-width-1-1">
                        <form action="/" class="dropzone" id="uploadLogoDropzone"></form>
                    </div>
                </div>
            </div>
        </div>
    </body>

</html>
