<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <title><spring:message code="theme.title"/></title>
        <link rel="icon" href="/sicstv/images/favicon.ico">

        <script src="/sicstv/js/jquery-1.8.0.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/uikit.min.js" type="text/javascript"></script>

        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js?<%=System.currentTimeMillis()%>"></script>

        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>

        <link href="/sicstv/css/flat/flat.css" rel="stylesheet">
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />

        <!-- Keep here: needed for teamStats.jsp -->
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.1/css/buttons.dataTables.min.css"/>
        <link rel="stylesheet" href="https://cdn.datatables.net/fixedcolumns/3.2.1/css/fixedColumns.dataTables.min.css"/>
        <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdn.datatables.net/fixedcolumns/3.2.1/js/dataTables.fixedColumns.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.colVis.min.js"></script>
        <!-- Keep here: needed for teamStats.jsp for buttons (export) -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.print.min.js"></script>
        <script src="https://cdn.datatables.net/colreorder/1.7.0/js/dataTables.colReorder.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
        <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>

        <!-- D3.js Library for canvas/svg -->
        <script src="https://d3js.org/d3.v5.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.1/d3-tip.min.js"></script>
        <script src="/sicstv/js/sics-canvas-shadow-teams.js?<%=System.currentTimeMillis()%>"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>

        <script src="https://cdnjs.cloudflare.com/ajax/libs/canvg/3.0.10/umd.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

        <style>
            .player text {
                font-family: Arial, sans-serif;
            }
            .player rect {
                stroke: #000;
                stroke-width: 2;
            }
            .plus-icon {
                font-size: 15px;
                font-weight: bold;
                cursor: pointer;
                display: none;
            }
            #autocompleteTeamPlayerShadowTeam > div.uk-dropdown, #autocompleteTeamShadowTeam > div.uk-dropdown, #autocompletePersonalPlayerShadowTeam > div.uk-dropdown {
                width: 100%;
            }

            @media print {
                #svgContainer, #svgContainer * {
                    visibility: visible;
                }
                #svgContainer {
                    position: absolute;
                    left: 0;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    width: 690px;
                    height: 850px;
                    margin: auto;
                }
                #filter-container, #sicsHeader, #breadcrumb, .uk-icon-trash {
                    display: none;
                }
            }
        </style>

        <script type="text/javascript">
            var resultPlayers = new Map();
            var isCopyMode = false, copyModeColor = "";

            $(document).ready(function () {
                drawVerticalField('svgContainer');
                changeFormazione();

            <c:if test="${mShadowTeam != null}">
                $("#formazioneSelect").val(${mShadowTeam.module});
                changeFormazione();
                <c:if test="${mShadowTeam.shared != null && mShadowTeam.shared}">
                $("#sharedCheck").attr("checked", "checked");
                </c:if>
            </c:if>

            <c:if test="${mLoadedPlayers != null}">
                <c:forEach var="player" items="${mLoadedPlayers}">
                shadowTeamPlayerPersonalMap.set(${player.personalId}, JSON.parse('${player.getJson()}'));
                shadowTeamPlayerPersonalMap.get(${player.personalId}).bornDateYearLast2Digit = "${player.getBornDateYearLast2Digit()}";
                </c:forEach>
            </c:if>

                shadowTeamPlayerPersonalMap.forEach(function (value, key) {
                    insertPlayer(true, key, value.index);
                });

                $.UIkit.autocomplete($('#autocompleteTeamPlayerShadowTeam'), {
                    'minLength': 3,
                    'delay': 750,
                    'hoverClass': 'uk-hover',
                    'source': function (release) {
                        $("#searchSpinnerShadowTeamPlayer").removeClass("uk-hidden");
                        setTimeout(function () {
                            if (!lock) {
                                var array = [];
                                lock = true;
                                isTeam = false;
                                isPlayer = false;
                                isCompetition = false;
                                isCountry = false;
                                $.ajax({
                                    type: "GET",
                                    url: "/sicstv/user/searchPlayer.htm",
                                    dataType: "json",
                                    data: "pattern=" + $("#teamPlayerAutoShadowTeam").val(),
                                    success: function (data) {
                                        var strf = $("#teamPlayerAutoShadowTeam").val();
                                        var re = new RegExp(strf, "ig");
                                        $.each(data, function (arrKey, arrValue) {
                                            isPlayer = true;
                                            var str = arrValue.last_name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                            var knownName = "";
                                            if (arrValue.known_name.indexOf(arrValue.last_name) === -1) {
                                                knownName = " (" + arrValue.known_name.replace(re, "<b>" + strf + "</b>").toUpperCase() + ")";
                                            }
                                            arrValue.currentIndex = currentIndex;
                                            var bornDate = arrValue.bornDate.split("-")[0];
                                            var obj = {id: arrValue.id, value: arrValue.known_name, seasons: arrValue.seasonList, lastSeason: arrValue.seasonName, name: str, namel: arrValue.last_name.replace(re, "<b>" + strf + "</b>").toUpperCase(), namef: arrValue.first_name.replace(re, "<b>" + strf + "</b>").toUpperCase(), namek: knownName, borny: bornDate, team: false, player: true, competition: false, country: false};
                                            array.push(obj);
                                            resultPlayers.set(arrValue.id, arrValue);
                                        });
                                        lock = false;
                                        release(array);
                                        $("#searchSpinnerShadowTeamPlayer").addClass("uk-hidden");
                                    }
                                });
                            }
                        }, 100);
                    }
                });

                $.UIkit.autocomplete($('#autocompletePersonalPlayerShadowTeam'), {
                    'minLength': 3,
                    'delay': 750,
                    'hoverClass': 'uk-hover',
                    'source': function (release) {
                        $("#searchSpinnerShadowTeamPersonalPlayer").removeClass("uk-hidden");
                        setTimeout(function () {
                            if (!lock) {
                                var array = [];
                                lock = true;
                                isTeam = false;
                                isPlayer = false;
                                isCompetition = false;
                                isCountry = false;
                                $.ajax({
                                    type: "GET",
                                    url: "/sicstv/user/searchPersonalPlayer.htm",
                                    dataType: "json",
                                    data: "pattern=" + $("#teamPersonalPlayerAutoShadowTeam").val(),
                                    success: function (data) {
                                        var strf = $("#teamPersonalPlayerAutoShadowTeam").val();
                                        var re = new RegExp(strf, "ig");
                                        $.each(data, function (arrKey, arrValue) {
                                            isPlayer = true;
                                            var str = arrValue.last_name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                            var knownName = "";
                                            if (arrValue.known_name.indexOf(arrValue.last_name) === -1) {
                                                knownName = " (" + arrValue.known_name.replace(re, "<b>" + strf + "</b>").toUpperCase() + ")";
                                            }
                                            arrValue.currentIndex = currentIndex;
                                            var bornDate = arrValue.bornDate.split("-")[0];
                                            var obj = {id: arrValue.id, value: arrValue.known_name, name: str, namel: arrValue.last_name.replace(re, "<b>" + strf + "</b>").toUpperCase(), namef: arrValue.first_name.replace(re, "<b>" + strf + "</b>").toUpperCase(), namek: knownName, borny: bornDate, team: false, player: true, competition: false, country: false};
                                            array.push(obj);
                                            resultPlayers.set(arrValue.id, arrValue);
                                        });
                                        lock = false;
                                        release(array);
                                        $("#searchSpinnerShadowTeamPersonalPlayer").addClass("uk-hidden");
                                    }
                                });
                            }
                        }, 100);
                    }
                });

                $.UIkit.autocomplete($('#autocompleteTeamShadowTeam'), {
                    'minLength': 3,
                    'delay': 750,
                    'hoverClass': 'uk-hover',
                    'source': function (release) {
                        $("#searchSpinnerShadowTeam").removeClass("uk-hidden");
                        $("#teamPlayerContainer").empty();
                        setTimeout(function () {
                            if (!lock) {
                                var array = [];
                                lock = true;
                                isTeam = false;
                                isPlayer = false;
                                isCompetition = false;
                                isCountry = false;
                                $.ajax({
                                    type: "GET",
                                    url: "/sicstv/user/searchTeam.htm",
                                    dataType: "json",
                                    data: "pattern=" + $("#teamAutoShadowTeam").val(),
                                    success: function (data) {
                                        var strf = $("#teamAutoShadowTeam").val();
                                        var re = new RegExp(strf, "ig");
                                        $.each(data, function (arrKey, arrValue) {
                                            isTeam = true;
                                            var str = arrValue.name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                            var obj = {id: arrValue.id, value: arrValue.name, seasons: arrValue.seasonList, lastSeason: arrValue.seasonName, name: str, team: true, player: false, competition: false, country: false, countryName: arrValue.countryName};
                                            array.push(obj);
                                        });
                                        lock = false;
                                        release(array);
                                        $("#searchSpinnerShadowTeam").addClass("uk-hidden");
                                    }
                                });
                            }
                        }, 100);
                    }
                });

                var tmpElement;
            <c:forEach var="detail" items="${mShadowTeamDetails}">
                tmpElement = d3.select("circle[index='${detail.modulePosition}']");
                <c:if test="${detail.modulePositionCoords != null}">
                tmpElement.attr("cx", "${detail.modulePositionCoords}".split(";")[0]);
                tmpElement.attr("cy", "${detail.modulePositionCoords}".split(";")[1]);
                </c:if>
                <c:if test="${detail.modulePositionColor != null}">
                tmpElement.attr("fill", "#${detail.modulePositionColor}");
                </c:if>
            </c:forEach>
                repaintPlayers();
            });

            function openShadowTeamPlayerManager(color) {
                if (typeof color === "undefined") {
                    $("#display-color").addClass("uk-hidden");
                } else {
                    $("#display-color").removeClass("uk-hidden");
                    $("#circle-color").val(color);
                }

                $("#shadowTeamPlayerModal").addClass("uk-open");
                $("#shadowTeamPlayerModal").removeClass("uk-hidden");

                temporaryPlayerMap = new Map();
                temporaryPlayerPersonalMap = new Map();
            }

            function closeShadowTeamPlayerManager(confirm) {
                $("#shadowTeamPlayerModal").removeClass("uk-open");
                $("#shadowTeamPlayerModal").addClass("uk-hidden");

                if (typeof confirm !== "undefined" && confirm) {
                    if (!$("#display-color").hasClass("uk-hidden")) {
                        d3.select("circle[index='" + currentIndex + "']").attr("fill", $("#circle-color").val());
                        repaintPlayers();
                    }
                }
            }

            function loadPlayersByTeam(teamId) {
                if (typeof teamId !== "undefined") {
                    $.ajax({
                        type: "GET",
                        url: "/sicstv/user/searchPlayerByTeam.htm",
                        dataType: "json",
                        data: "teamId=" + teamId,
                        success: function (data) {
                            var currentRole = "";
                            var newDiv = "";
                            var roleIndex = 0;
                            $("#teamPlayerContainer").empty();
                            data.forEach(function (player) {
                                resultPlayers.set(player.id, player);

                                var role;
                                if ("${mUser.tvLanguage}" === "it") {
                                    role = player.descrizione_ruolo_it;
                                } else if ("${mUser.tvLanguage}" === "fr") {
                                    role = player.descrizione_ruolo_fr;
                                } else if ("${mUser.tvLanguage}" === "es") {
                                    role = player.descrizione_ruolo_es;
                                } else {
                                    role = player.descrizione_ruolo_en;
                                }

                                if (currentRole !== role) {
                                    newDiv += (currentRole === "" ? "" : "</div>");
                                    currentRole = role;
                                    if (roleIndex % 2 === 0) {
                                        if (roleIndex > 0) {
                                            newDiv += "</div>";
                                        }
                                        newDiv += "<div class='row'>";
                                    }
                                    roleIndex++;
                                    newDiv += "<div class='column'><div class='uk-width-10-10'><center><h3>" + currentRole + "</h3></center></div>";
                                }
                                var footImage = "<img class='uk-margin-small-left' width='20px' src='/sicstv/images/foot-none.png'><br>";
                                if (typeof player.foot_it !== "undefined" && player.foot_it !== null) {
                                    if (player.foot_it === 'DESTRO') {
                                        footImage = "<img class='uk-margin-small-left' width='20px' src='/sicstv/images/foot-right.png'><br>";
                                    } else if (player.foot_it === 'SINISTRO') {
                                        footImage = "<img class='uk-margin-small-left' width='20px' src='/sicstv/images/foot-left.png'><br>";
                                    } else if (player.foot_it === 'ENTRAMBI') {
                                        footImage = "<img class='uk-margin-small-left' width='20px' src='/sicstv/images/foot.png'><br>";
                                    }
                                }
                                newDiv += "<div class='row center-vertically cursor-pointer uk-margin-small-top gray-hover' onclick='insertPlayer(false, " + player.id + ")'><img width='20px' src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/" + player.photo + ".png' onerror='this.src=\"/sicstv/images/user_gray.png\"'/>" + player.known_name + " (" + player.bornDateYearLast2Digit + ") " + footImage + "</div>";
                            });
                            $("#teamPlayerContainer").append(newDiv);
                        }
                    });
                }
            }

            function saveShadowTeam() {
                var name = $("#shadowTeamName").val();
                var module = $("#formazioneSelect").val();
                var coords = [], colors = [];
                var shared = $("#sharedCheck").is(":checked");
                var id = "";
            <c:if test="${mShadowTeam != null}">
                id = "${mShadowTeam.id}";
            </c:if>
                var json = [];
                shadowTeamIndexPlayers.forEach(function (key, value) {
                    var counter = 0;
                    key.forEach(function (playerId) {
                        var player = shadowTeamPlayerMap.get(playerId);
                        player.index = value;
                        player.sort = counter;

                        var tmpPlayer = {personalId: player.personalId, id: player.id, first_name: player.first_name, last_name: player.last_name, known_name: player.known_name,
                            photo: player.photo, bornDate: player.bornDate, height: player.height, footId: player.footId, idPosition: player.idPosition, teamName: player.teamName,
                            shared: player.shared, index: player.index, sort: player.sort};
                        json.push(tmpPlayer);

                        counter++;
                    });
                });
                shadowTeamIndexPlayersPersonal.forEach(function (key, value) {
                    var counter = 0;
                    key.forEach(function (playerId) {
                        var player = shadowTeamPlayerPersonalMap.get(playerId);
                        player.index = value;
                        player.sort = counter;

                        var tmpPlayer = {personalId: player.personalId, id: player.id, first_name: player.first_name, last_name: player.last_name, known_name: player.known_name,
                            photo: player.photo, bornDate: player.bornDate, height: player.height, footId: player.footId, idPosition: player.idPosition, teamName: player.teamName,
                            shared: player.shared, index: player.index, sort: player.sort};
                        json.push(tmpPlayer);

                        counter++;
                    });
                });

                d3.selectAll(".circle-draggable").each(function () {
                    var tmpCoords = d3.select(this).attr("cx") + ";" + d3.select(this).attr("cy");
                    coords.push(tmpCoords);
                    colors.push(d3.select(this).attr("fill").replace("#", ""));
                });

                jsShowBlockUI();
                $.ajax({
                    type: "POST",
                    url: "/sicstv/user/saveShadowTeam.htm",
                    data: encodeURI("id=" + id + "&name=" + name + "&module=" + module + "&coords=" + coords.join("|") + "&colors=" + colors.join("|") + "&json=" + JSON.stringify(json) + "&shared=" + shared),
                    success: function (result) {
                        $.unblockUI();
                        if (result === "ko") {
                            UIkit.notify("<spring:message code="generic.error"/>", {status: 'danger', timeout: 1000});
                        } else {
                            UIkit.notify("<spring:message code="shadow.team.updated"/>", {status: 'success', timeout: 1000});
                            location.href = "/sicstv/user/shadowTeams.htm?id=" + result;
                        }
                    }
                });
            }

            function exportShadowTeam() {
                window.print();
            }

            function manageCopyFormat() {
                isCopyMode = !isCopyMode;
                $("#paint-brush-container").toggleClass("uk-button-active");
                $("#paint-brush").toggleClass("invert-color");

                if (!isCopyMode) {
                    copyModeColor = "";
                    $("#paint-brush-color").attr("style", "");
                    // UIkit.notify("<spring:message code="shadow.team.copy.mode.disabled"/>", {status: 'success', timeout: 1000});
                } else {
                    // UIkit.notify("<spring:message code="shadow.team.copy.mode.enabled"/>", {status: 'success', timeout: 1000});
                }
            }
        </script>
    </head>
    <body>
        <!-- inclusione componenti header -->
        <%@ include file="header.jsp" %>

        <div id="breadcrumb">
            <ul class="uk-breadcrumb uk-margin-left">
                <li>
                    <a href="/sicstv/user/shadowTeamsList.htm"><spring:message code="shadow.team"/></a>
                    - ${mShadowTeam != null ? mShadowTeam.name : ''}
                </li>
            </ul>
        </div>

        <div class="uk-flex">
            <div class="uk-width-2-10" style="border-right: 1px solid black" id="filter-container">
                <div class="uk-flex-column center uk-margin-small-top">
                    <button class="uk-button uk-button-large uk-width-6-10" onclick="location.href = '/sicstv/user/shadowTeams.htm?id='"><spring:message code="shadow.team.add"/></button>
                    <button class="uk-button uk-button-large uk-width-6-10 uk-margin-small-top" onclick="saveShadowTeam();"><spring:message code="shadow.team.save.changes"/></button>
                    <button class="uk-button uk-button-large uk-width-6-10 uk-margin-small-top uk-text-uppercase" onclick="exportShadowTeam();"><spring:message code="menu.export.shadow.team"/></button>
                </div>
                <center><h2 class="uk-text-uppercase uk-margin-top"><spring:message code="shadow.team.informations"/></h2></center>
                <div class="uk-margin-left">
                    <div class="uk-form uk-margin-top">
                        <div class="uk-flex">
                            <div class="uk-width-3-10 center-vertically">
                                <spring:message code="shadow.team.name"/>
                            </div>
                            <div class="uk-margin-right uk-width-7-10 uk-flex align-items-center">
                                <input id="shadowTeamName" type="text" value="${mShadowTeam != null ? mShadowTeam.name : ''}"/>
                            </div>
                        </div>
                    </div>
                    <!--                    <div class="uk-form uk-margin-top">
                                            <div class="uk-margin-left cursor-pointer" onclick="manageCopyFormat();">
                                                <button id="paint-brush-container" class="uk-button">
                                                    <img id="paint-brush" class='uk-margin-small-left' width='25px' src='/sicstv/images/paint-brush.svg'>
                                                </button>
                                            </div>
                                        </div>-->
                    <div class="uk-form uk-margin-top">
                        <div class="uk-flex">
                            <div class="uk-margin-right">
                                <input type="checkbox" id="sharedCheck"/>
                            </div>
                            <div class="uk-width-7-10">
                                <spring:message code="shadow.team.share.description"/>
                            </div>
                        </div>
                    </div>
                    <div class="uk-form uk-margin-top">
                        <div class="uk-flex">
                            <div class="uk-margin-right">
                                <input type="checkbox" id="paint-brush-checkbox" onchange="manageCopyFormat();"/>
                            </div>
                            <div class="uk-width-7-10">
                                Copy Position Color
                            </div>
                            <div class="uk-width-2-10" id="paint-brush-color"></div>
                        </div>
                    </div>
                </div>
                <hr/>
                <center><h2 class="uk-text-uppercase uk-margin-top"><spring:message code="menu.filters"/></h2></center>
                <div class="uk-flex center-vertically uk-margin-left">
                    <p class="uk-width-2-10 uk-margin-bottom-remove"><spring:message code="shadow.team.lineup"/></p>
                    <div class="uk-form uk-width-8-10">
                        <select id="formazioneSelect" onchange="changeFormazione();" class="uk-form-select uk-width-9-10">
                            <option value="0">3412</option>
                            <option value="1">3421</option>
                            <option value="2">343</option>
                            <option value="3">3511</option>
                            <option value="4">352</option>
                            <option value="5">4141</option>
                            <option value="6">4222</option>
                            <option value="7">4231</option>
                            <option value="8">442</option>
                            <option value="9">4411</option>
                            <option value="10">451</option>
                            <option value="11">433</option>
                            <option value="12">4312</option>
                            <option value="13">4321</option>
                            <option value="14">532</option>
                            <option value="15">541</option>
                        </select>
                    </div>
                </div>
                <hr/>
                <div class="uk-margin-left">
                    <p class="uk-margin-bottom"><spring:message code="shadow.team.display"/></p>
                    <div class="uk-form uk-margin-small-bottom">
                        <div class="uk-flex">
                            <div class="uk-margin-right">
                                <input type="checkbox" checked id="photoCheck" onchange="repaintPlayers();"/>
                            </div>
                            <div class="uk-width-7-10">
                                <spring:message code="shadow.team.photo"/>
                            </div>
                        </div>
                    </div>
                    <div class="uk-form uk-margin-small-bottom">
                        <div class="uk-flex">
                            <div class="uk-margin-right">
                                <input type="checkbox" checked id="ageCheck" onchange="repaintPlayers();"/>
                            </div>
                            <div class="uk-width-7-10">
                                <spring:message code="shadow.team.age"/>
                            </div>
                        </div>
                    </div>
                    <div class="uk-form uk-margin-small-bottom">
                        <div class="uk-flex">
                            <div class="uk-margin-right">
                                <input type="checkbox" checked id="heightCheck" onchange="repaintPlayers();"/>
                            </div>
                            <div class="uk-width-7-10">
                                <spring:message code="shadow.team.height"/>
                            </div>
                        </div>
                    </div>
                    <div class="uk-form uk-margin-small-bottom">
                        <div class="uk-flex">
                            <div class="uk-margin-right">
                                <input type="checkbox" checked id="teamCheck" onchange="repaintPlayers();"/>
                            </div>
                            <div class="uk-width-7-10">
                                <spring:message code="shadow.team.team"/>
                            </div>
                        </div>
                    </div>
                </div>
                <hr/>
                <div class="center">
                    <button class="uk-button uk-button-large uk-width-6-10" onclick="currentIndex = 11;
                            openShadowTeamPlayerManager();"><spring:message code="shadow.team.add.players"/></button>
                </div>
            </div>
            <div id="svgContainer" class="uk-width-8-10 center">

            </div>
        </div>

        <div id="shadowTeamPlayerModal" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div class="uk-modal-dialog" style="min-height: 0; min-width: 0; max-width: 25%; width: auto; height: auto; margin-right: 60%">
                <div>
                    <span class="modalClose" onclick="closeShadowTeamPlayerManager(false);
                            cancelInsert();">&times;</span>
                </div>
                <div class="container uk-margin-top" style="min-height: 35vh; max-height: 90vh;">
                    <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-float-left" data-uk-tab="{connect:'#searchContent'}">
                        <li aria-expanded="true" class="uk-active"><a><spring:message code="shadow.team.search.player"/></a></li>
                        <li aria-expanded="false"><a><spring:message code="shadow.team.search.player.personal"/></a></li>
                        <li aria-expanded="false"><a><spring:message code="shadow.team.add.from.team"/></a></li>
                    </ul>
                    <ul id="searchContent" class="uk-switcher uk-padding-top">
                        <li>
                            <div>
                                <div id="autocompleteTeamPlayerShadowTeam" class="uk-autocomplete uk-form uk-width-10-10">
                                    <div class="uk-flex">
                                        <input type="text" id="teamPlayerAutoShadowTeam" class="uk-width-10-10" placeholder="<spring:message code='video.giocatore'/>" style="text-transform: lowercase"/>
                                        <div id="searchSpinnerShadowTeamPlayer" class="spinner-small uk-hidden center-vertically" style="margin-left: 5px"><div class="loader-small"></div></div>
                                    </div>
                                    <script type="text/autocomplete">
                                        <ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left">
                                        {{#isPlayer}}
                                        <li><i class="uk-icon-user"></i>&nbsp;<b><spring:message code='search.players'/></b><hr></li>
                                        {{~items}}
                                        {{#$item.player}}
                                        <li class="gray-hover" data-value="{{ $item.value }}" data-seasons="{{ $item.seasons }}" onclick="insertPlayer(false, {{ $item.id }});" >
                                        <a>{{{ $item.name }}} <span class="sCapitalize">{{{ $item.namef }}} </span> {{{ $item.namek }}} ({{{ $item.borny }}})</a>
                                        </li>
                                        {{ /$item.player }}
                                        {{/items}}
                                        <li>&nbsp;</li>
                                        {{/isPlayer }}
                                        </ul>
                                    </script>
                                </div>
                            </div>
                        </li>

                        <li>
                            <div>
                                <div id="autocompletePersonalPlayerShadowTeam" class="uk-autocomplete uk-form uk-width-10-10">
                                    <div class="uk-flex">
                                        <input type="text" id="teamPersonalPlayerAutoShadowTeam" class="uk-width-10-10" placeholder="<spring:message code='video.giocatore'/>" style="text-transform: lowercase"/>
                                        <div id="searchSpinnerShadowTeamPersonalPlayer" class="spinner-small uk-hidden center-vertically" style="margin-left: 5px"><div class="loader-small"></div></div>
                                    </div>
                                    <script type="text/autocomplete">
                                        <ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left">
                                        {{#isPlayer}}
                                        <li><i class="uk-icon-user"></i>&nbsp;<b><spring:message code='search.players'/></b><hr></li>
                                        {{~items}}
                                        {{#$item.player}}
                                        <li class="gray-hover" data-value="{{ $item.value }}" onclick="insertPlayer(true, {{ $item.id }});" >
                                        <a>{{{ $item.name }}} <span class="sCapitalize">{{{ $item.namef }}} </span> {{{ $item.namek }}} ({{{ $item.borny }}})</a>
                                        </li>
                                        {{ /$item.player }}
                                        {{/items}}
                                        <li>&nbsp;</li>
                                        {{/isPlayer }}
                                        </ul>
                                    </script>
                                </div>
                            </div>
                        </li>

                        <li>
                            <div>
                                <div id="autocompleteTeamShadowTeam" class="uk-autocomplete uk-form uk-width-10-10">
                                    <div class="uk-flex">
                                        <input type="text" id="teamAutoShadowTeam" class="uk-width-10-10" placeholder="<spring:message code='player.report.team'/>" style="text-transform: lowercase"/>
                                        <div id="searchSpinnerShadowTeam" class="spinner-small uk-hidden center-vertically" style="margin-left: 5px"><div class="loader-small"></div></div>
                                    </div>
                                    <script type="text/autocomplete">
                                        <ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left">
                                        {{#isTeam}}
                                        <li><i class="uk-icon-shield"></i>&nbsp;<b><spring:message code='search.teams'/></b><hr></li>
                                        {{~items}}
                                        {{#$item.team}}
                                        <li class="gray-hover" data-value="{{ $item.value }}" data-seasons="{{ $item.seasons }}" onclick="loadPlayersByTeam({{ $item.id }});" >
                                        <a>{{{ $item.name }}} ({{{ $item.countryName }}})</a>
                                        </li>
                                        {{/$item.team }}
                                        {{/items }}
                                        <li>&nbsp;</li>
                                        {{/isTeam }}
                                    </script>
                                    <div id="teamPlayerContainer" style="max-height: 60vh; overflow-y: auto">

                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div class="uk-modal-footer uk-text-right uk-width-1-1">
                    <div class="uk-float-left uk-flex" id="display-color" style="justify-content: center; align-items: center">
                        Color: 
                        <input type="color" id="circle-color" value="#ffffff" style="border-width: 0px; cursor: pointer;" list="presetColors">
                        <datalist id="presetColors">
                            <option>#27AAE1</option>
                            <option>#DE3524</option>
                            <option>#43FF30</option>
                            <option>#F1FF29</option>
                            <option>#B26CFF</option>
                        </datalist>
                    </div>
                    <button style="min-width:80px;" onclick="closeShadowTeamPlayerManager(false); cancelInsert();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.annulla"/></button>
                    <button style="min-width:80px;" onclick="closeShadowTeamPlayerManager(true);" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.conferma"/></button>
                </div>
            </div>
        </div>
    </body>
</html>