<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<c:if test="${mReloadPage != null && mReloadPage}">
    <script>
        location.reload();
    </script>
</c:if>

<c:forEach var="item" items="${seasonAll}">
    <c:choose>
        <c:when test="${item.id == curSeas}">
            <option selected="selected" value="${item.id}">${item.name}</option>
        </c:when>
        <c:otherwise>
            <option value="${item.id}">${item.name}</option>
        </c:otherwise>
    </c:choose>
</c:forEach>