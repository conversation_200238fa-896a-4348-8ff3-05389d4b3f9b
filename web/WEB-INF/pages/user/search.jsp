<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div>
    <!--input type="hidden" id="mMaxDay" value="${mMaxDay}" /-->
    <input type="hidden" id="mDay" value="${mMaxDay}" />
    <input type="hidden" id="mNumMatches" value="${mNumMatches}"/>
    <script>

        $(document).ready(function () {
            pageStatus['${parent}'] = parseInt('${currentPage}');
            totMatches['${parent}'] = $("#totMatches${parent}").val();
            numMatches['${parent}'] = parseInt('${mNumMatches}');
            navigationButtonStatus('${parent}');

//            var teamId = new URL(document.URL).searchParams.get("formTeamId");
//            if (typeof teamId === 'undefined' || teamId === null) {
//                teamId = new URL(document.URL).searchParams.get("teamId");
//            }
//            var playerId = new URL(document.URL).searchParams.get("playerId");

//            if ((typeof teamId === 'undefined' || teamId === null) && (typeof playerId === 'undefined' || playerId === null)) {
//                $(".removeDuplicatesLink").remove();
//            }
        });

        function loadMatches(step, parent, filter) {

            var totPartite = totMatches[parent];
            var pageCur = parseInt(pageStatus[parent]);
            if (step === 'next') {
                // se � uguale a 25 potrei avere ancora partite altrimenti no
                if (totPartite == matchPerPage) {
                    pageCur += 1;
                }

            } else if (step === 'prev') {
                pageCur -= 1;
                if (pageCur < 1) {
                    pageCur = 1;
                }
            } else {
                pageCur = 1;
            }
            pageStatus[parent] = pageCur;
            navigationButtonStatus(parent);

            if (filter) {
                filterMatches(parent, pageCur, '');
            } else {
                jsRefreshMatches(pageCur);
            }
        }

        function navigationButtonStatus(parent) {
            if (numMatches[parent] < 25) {
                $("#next" + parent).attr("disabled", "disabled");
            } else {
                $("#next" + parent).removeAttr("disabled");
            }
            if (pageStatus[parent] == 1) {
                $("#prev" + parent).attr("disabled", "disabled");
                $("#firstPageEvents" + parent).attr("disabled", "disabled");
            } else {
                $("#prev" + parent).removeAttr("disabled");
                $("#firstPageEvents" + parent).removeAttr("disabled");
            }
        }

        function jsDownloadReport(id) {
            var strUrl = "/sicstv/user/downloadReport.htm?id=" + id;
            $.ajax({
                type: "GET",
                url: strUrl,
                cache: false,
                success: function (msg) {
                    if (msg.substr(0, 4) === "true") {
                        UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                    } else {
                        UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                        window.location.replace(msg.substr(5));
                        $.unblockUI();
                    }
                }
            });
        }

        function jsDownloadPlayerReport(id) {
            var strUrl = "/sicstv/user/downloadPlayerReport.htm?id=" + id;
            $.ajax({
                type: "GET",
                url: strUrl,
                cache: false,
                success: function (msg) {
                    $.unblockUI();
                    if (msg.substr(0, 4) === "true") {
                        UIkit.notify(msg.substr(5), {status: 'danger', timeout: 1000});
                    } else {
                        UIkit.notify("<spring:message code="menu.user.downloading"/>", {status: 'success', timeout: 1000});
                        window.location.replace(msg.substr(5));
                    }
                }
            });
        }
    </script>
    <!--  <div id="showCurrentFilter"></div> -->
    <input type="hidden" value="${currentPage}" id="mCurrentPage"/>
    <input type="hidden" value="${parent}" id="mParent"/>
    <c:set var="count" value="0"/>
    <c:set var="searchColumns" value="5"/>
    <c:if test="${mMinutesPlayed != null}">
        <c:set var="searchColumns" value="6"/>
    </c:if>
    <c:set var="idPlayer" value=""/>
    <c:set var="removeDuplicates" value=""/>
    <c:if test="${mUser.isPlayerUser()}">
        <c:set var="idPlayer" value="${mUser.playerId}"/>
        <c:set var="removeDuplicates" value="&removeDuplicates=true"/>
    </c:if>
    <c:set var="removeDuplicates" value="&removeDuplicates=true"/>
    <table id="matchTable${parent}" class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;  height: 100%; width: 100%; box-sizing: content-box">
        <tbody>
            <tr>
                <td colspan="${searchColumns}">
                    <button onclick="loadMatches('next', '${parent}',${filter});" id="next${parent}" class="uk-button uk-button-small " title="<spring:message code='calendar.precedente'/>"><i class="uk-icon-angle-double-left  "></i> </button>
                    <button onclick="loadMatches('prev', '${parent}',${filter});" id="prev${parent}" class="uk-button uk-button-small" title="<spring:message code='calendar.successiva'/>"><i class="uk-icon-angle-double-right  "></i> </button>
                    <button onclick="loadMatches('', '${parent}',${filter});" id="firstPageEvents${parent}" class="uk-button uk-button-small " title="<spring:message code='calendar.prima'/>"><i class="uk-icon-step-forward"></i> </button>
                    <span id="loaderSpinner" class="uk-margin-small-left"> ${dateInterval} </span>
                </td>
            </tr>

            <c:forEach items="${mMatches}" var="item">
                <c:set var="count" value="${count+1}"/>

                <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                    <c:if test="${item.videoName != null}">
                        <td class="uk-text-center matchesColumnResult1" onclick="
                                location.href = '/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=${idPlayer}&goals=false&event=&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>${removeDuplicates}'" class="uk-width-1-10 clickRow uk-text-center">
                                <div id="dateBox">
                                    <b>${item.dateDayString}<br>${item.dateMonthString}</b>
                            </div>
                        </td>
                    </c:if>
                    <c:if test="${item.videoName == null}">
                        <td class="uk-text-center matchesColumnResult1" class="uk-width-1-10 clickRow uk-text-center">
                            <div id="dateBox">
                                <b>${item.dateDayString}<br>${item.dateMonthString}</b>
                            </div>
                        </td>
                    </c:if>

                    <c:if test="${item.videoName != null}">
                        <td class="clickRow" onclick="
                                location.href = '/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=${idPlayer}&goals=false&event=&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>${removeDuplicates}'">
                            <a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=${idPlayer}&goals=false&event=&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>${removeDuplicates}">
                                <b>${item.homeTeam} - ${item.awayTeam}</b>
                                <br>
                                <span class="competitionDetail">${item.competitionName}, <spring:message code="calendar.giornata"/> ${item.matchday}</span>
                            </a>
                        </td>
                    </c:if>
                    <c:if test="${item.videoName == null}">
                        <td class="clickRow">
                            <a href="#">
                                <b>${item.homeTeam} - ${item.awayTeam}</b>
                                <br>
                                <span class="competitionDetail">${item.competitionName}, <spring:message code="calendar.giornata"/> ${item.matchday}</span>
                            </a>
                        </td>
                    </c:if>

                    <c:if test="${mMinutesPlayed != null}">
                        <td class="uk-text-center matchesColumnTag">
                            <span class="competitionMinutes">
                                <c:set var="temporaryValue" value="${mMinutesPlayed.get(item.idFixture)}"/>
                                <c:if test="${temporaryValue != null}">
                                    ${temporaryValue}'
                                </c:if>
                            </span>
                        </td>
                    </c:if>

                    <c:if test="${item.videoName != null}">
                        <td class="uk-text-center matchesColumnResult1 clickRow" onclick="location.href = '/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=${idPlayer}&goals=false&event=&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>${removeDuplicates}'">
                            <span class="competitionResult">${item.homeTeamScore} - ${item.awayTeamScore}</span>
                        </td>
                    </c:if>
                    <c:if test="${item.videoName == null}">
                        <td class="uk-text-center matchesColumnResult1 clickRow">
                            <span class="competitionResult">${item.homeTeamScore} - ${item.awayTeamScore}</span>
                        </td>
                    </c:if>

                    <td class="matchesColumnTag">
                        <c:if test="${item.fhd}">
                            <i><img src="/sicstv/images/FHD2.png"/></i>
                        </c:if>
                        <c:if test="${!item.fhd && item.hd}">
                            <i><img src="/sicstv/images/hd-dark.png"/></i>
                        </c:if>
                        <c:if test="${!item.fhd && !item.hd && item.videoName != null}">
                            <i><img src="/sicstv/images/standard.png"/></i>
                        </c:if>
                        <c:if test="${item.tacticalVideo}">
                            <i><img src="/sicstv/images/tact.png"/></i>
                        </c:if>
                        <c:choose>
                            <c:when test="${item.analysisLevel != null && item.analysisLevel == 3}">
                            <i><img width="16" height="16" src="/sicstv/images/tag_empty.svg" title="<spring:message code="match.base.data"/>"/></i>
                            </c:when>
                            <c:when test="${item.isTagged()}">
                            <i><img width="16" height="16" src="/sicstv/images/tag.svg" title="<spring:message code="match.full.data"/>"/></i>
                            </c:when>
                        </c:choose>
                    </td>
                    <td class="matchesColumnResult2">

                        <c:if test="${item.idFixture != null && item.videoName != null}">
                            <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}">
                                <button class="uk-icon-hover uk-button uk-button-mini" title="<spring:message code="menu.user.options"/>"><i class="uk-icon-hover uk-icon-caret-down"></i></button>
                                <div class="uk-dropdown uk-dropdown-small uk-text-left">
                                    <ul class="uk-nav uk-nav-dropdown">
                                        <c:if test="${!mUser.isPlayerUser()}">
                                            <li><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=${idPlayer}&goals=false&event=&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>${removeDuplicates}"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.play"/></a></li>
                                                <c:choose>
                                                    <c:when test="${matchPlayerId != null}">
                                                    <li><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=${mTeam.id}&idPlayer=${matchPlayerId}&goals=false&event=&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.play.player"/></a></li>
                                                        </c:when>
                                                        <c:when test="${mTeam.id != null}">
                                                    <li><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=${mTeam.id}&idPlayer=${matchPlayerId}&goals=false&event=&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>&removeDuplicates=true"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.play.team"/></a></li>
                                                        </c:when>
                                                    </c:choose>
                                                </c:if>
                                                <c:if test="${mUser.isPlayerUser()}">
                                            <li><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=${mTeam.id}&idPlayer=${matchPlayerId}&goals=false&event=&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>${removeDuplicates}"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.play.player"/></a></li>
                                                </c:if>
                                                <c:if test="${item.analysisLevel != 0 && item.analysisLevel != 3 && !mUser.isPlayerUser()}">
                                            <li class="possessionLink"><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=&goals=false&event=SICS-_-POS&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>&startClip=true"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.distinct.clip"/></a></li>
                                            <li class="removeDuplicatesLink"><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=&goals=false&event=&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>${removeDuplicates}&startClip=true"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.possession.clip"/></a></li>
                                                    <c:if test="${item.homeTeamScore > 0 || item.awayTeamScore > 0}">
                                                <li class="goalLink"><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=&goals=false&event=SICS-_-RTF&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>&startClip=true"><i class="uk-icon-play uk-margin-right"></i><spring:message code="video.goal"/></a></li>
                                                    </c:if>
                                            <li class="highlightsLink"><a href="/sicstv/user/video.htm?personal=${personalSource}&id=&fixtureId=${item.idFixture}&idComp=${item.competitionId}&idTeam=&idPlayer=&goals=false&event=&filter=&limit=50<c:if test="${mGirone != null}">&groupId=${mGirone.groupId}</c:if>&highlights=true&startClip=true"><i class="uk-icon-play uk-margin-right"></i><spring:message code="menu.user.highlights"/></a></li>
                                                </c:if>
                                                <c:if test="${item.minVideoQuality == 0 && !mUser.isPlayerUser()}">
                                            <li><a onclick="jsDownload(${item.idFixture}, 0);
                                                    return false;"><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.download"/></a></li>
                                                </c:if>
                                                <c:if test="${item.hd && !mUser.isPlayerUser() && item.providerId != null && item.providerId != 3}">
                                            <li><a onclick="jsDownload(${item.idFixture}, 1);
                                                    return false;"><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.downloadHD"/></a></li>
                                                </c:if>
                                                <c:if test="${item.fhd && !mUser.isPlayerUser() && item.providerId != null && item.providerId != 3}">
                                            <li><a onclick="jsDownload(${item.idFixture}, 2);
                                                    return false;"><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.downloadFHD"/></a></li>
                                                </c:if>
                                                <c:if test="${item.tacticalVideo && !mUser.isPlayerUser()}">
                                            <li><a onclick="jsDownload(${item.idFixture}, 3);
                                                    return false;"><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.downloadTACT"/></a></li>
                                                </c:if>
                                                        <!--li><a href="/sicstv/user/video.htm?id=${item.id}&idComp=${item.competitionId}&idTeam=${mTeam.id}&idPlayer=${matchPlayerId}&goals=true&event=&filter="><i class="uk-icon-soccer-ball-o uk-margin-right"></i><spring:message code="menu.user.goal"/></a></li-->
                                        <c:if test="${item.isTagged() && mUser.sportId == '0'}">
                                            <li><a href="#" onclick="showTabellino(${item.idFixture});" ><i class="uk-icon-table uk-margin-right"></i><spring:message code="menu.user.tabellino"/></a></li>
                                                </c:if>
                                                <c:if test="${item.isExistReport() && item.isTagged() && !mUser.isPlayerUser()}">
                                                    <c:if test="${matchPlayerId == null}">
                                                        <li><a href="http://server.sics.it/sicsdataanalytics/auth/matchStudio.htm?fixtureId=${item.idFixture}&playerIds=&language=${mUser.tvLanguage}" target="_blank"><i class="uk-icon-newspaper-o uk-margin-right"></i><spring:message code="menu.user.report"/></a></li>
                                                        <%--<li><a href="#" onclick="jsDownloadReport(${item.idFixture});"><i class="uk-icon-newspaper-o uk-margin-right"></i><spring:message code="menu.user.report"/></a></li>--%>
                                                    </c:if>
                                                    <c:if test="${matchPlayerId != null}">
                                                        <li><a href="https://server.sics.it/sicsdataanalytics/auth/playerStudio.htm?fixtureId=${item.idFixture}&playerIds=${matchPlayerId}&language=${mUser.tvLanguage}" target="_blank"><i class="uk-icon-newspaper-o uk-margin-right"></i><spring:message code="menu.user.player.studio"/></a></li>
                                                    </c:if>
                                                </c:if>
                                                <c:if test="${item.isExistReport() && item.isTagged() && mUser.isPlayerUser()}">
                                            <li><a href="https://server.sics.it/sicsdataanalytics/auth/playerStudio.htm?fixtureId=${item.idFixture}&playerIds=${mUser.playerId}&language=${mUser.tvLanguage}" target="_blank"><i class="uk-icon-newspaper-o uk-margin-right"></i><spring:message code="menu.user.player.studio"/></a></li>
                                                </c:if>
                                                <c:if test="${item.isTagged() && mUser.sportId == '0' && !mUser.isPlayerUser()}">
                                            <li><a href="#" onclick="jsGetGameData(${item.idFixture}, 'xml');" ><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.xml"/></a></li>
                                                </c:if>
                                                <c:if test="${item.isTagged() && mUser.sportId == '0' && !mUser.isPlayerUser()}">
                                            <li><a href="#" onclick="jsGetGameData(${item.idFixture}, 'json');" ><i class="uk-icon-download uk-margin-right"></i><spring:message code="menu.user.json"/></a></li>
                                                </c:if>
                                    </ul>
                                </div>
                            </div>
                            <span class="squaredCheckbox uk-hidden-small"><input type="checkbox" id="checkbox_${cbPrefix}${item.idFixture}" onclick="jsSelectMatch('${item.idFixture}');" value="${item.idFixture}"><label for="checkbox_${cbPrefix}${item.idFixture}"></label></span>
                            </c:if>
                    </td>
                </tr>
            </c:forEach>
        </tbody>
    </table>
    <input type="hidden" id="totMatches${parent}" value="${count}"/>

    <!-- This is the modal -->
    <div id="divShowTabellino" class="uk-modal">
        <div id="divTabellinoContent" class="uk-modal-dialog" style="min-height:0;min-width: 0;max-width: 50%;width:auto;height:auto;">
            <a class="uk-modal-close uk-close"></a>
            <div id="divOverflow" class="uk-overflow-container">

            </div>
        </div>
    </div>

</div>