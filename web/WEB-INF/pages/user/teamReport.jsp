<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
<script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

<script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
<script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
<script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
<script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
<script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>

<script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
<script src="/sicstv/uikit/js/components/tooltip.js"></script>

<link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
<link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
<link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >

<!-- D3.js Library for canvas/svg -->
<script src="https://d3js.org/d3.v5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.1/d3-tip.min.js"></script>
<script src="/sicstv/js/sics-canvas-player.js?<%=System.currentTimeMillis()%>"></script>

<div class="hideOnPrint" style="width: 100%"><%@ include file="header.jsp" %></div>
<div id="teamReport">
    
    <script type="text/javascript">
        $('link[rel=stylesheet][href*="fonts.googleapis.com"]').remove();
        $(document).ready(function () {
            initSearchFilter();
            
            drawField('canvasContainer', pitch);
            <c:if test="${mTeamPlayerPoints.size() > 0}">
                <c:forEach var="position" items="${mTeamPlayerPoints}">
                    drawPointTeam(${position.x}, ${position.y}, '${mTeamColor}');
                </c:forEach>
            </c:if>
        });
    </script>
    
    <style>
        @page {
            size: A4;
        }
        
        @media screen {
            #teamReport {
                position: absolute;
                left: 25%;
            }
        }
        
        @media print {
            .hideOnPrint {
                display: none !important;
                height: 0 !important;
            }
            
            html, body {
                width: 210mm;
                height: 297mm;
            }
        }
        
        #teamReport {
            max-width: 794px;
        }
        
        .tableHeader {
            text-align: center;
            height: 10px;
        }
        
        .tableRow {
            text-align: center;
        }
        
        .page {
            height: 1108px;
            padding-top: 15px;
            padding-right: 15px;
            padding-left: 15px;
        }
        
        .red {
            background-color: #EB5A10 !important;
        }
        
        .black {
            background-color: #000000 !important;
        }
        
        .redQuad {
            width: 50px;
            height: 100%;
        }
        
        .headerQuad {
            width: 100%;
            height: 90px;
        }
        
        .container {
            display: flex;
            width: 100%;
        }
        
        .box2 {
            width: 50%;
            height: 50%;
            float: left;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .box3 {
            width: 33%;
            height: 50%;
            float: left;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .large-div {
            vertical-align: middle
        }
        
        .container-progress-bar {
            width: 100%;
            background-color: #e5e5e5 !important;
            border-radius: 0.4rem;
            float: right;
            display: flex;
        }

        .progress {
/*            background-color: green;*/
            height: 1.5rem;
            padding: 0;
            border-radius: 0.4rem;
            display: flex;
            justify-content: flex-start;
        }

        .progress span {
            font-weight: 700;
            text-align: center;
        }
        
    </style>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/1.3.3/jspdf.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    
    <c:choose>
        <c:when test="${mRanking == null || mRanking.keySet().size() == 0}">
            <div class="page">
                <img height="40px" width="150px" src="/sicstv/images/logosics.png" style="margin-bottom: 15px; float: left">
                <div style="height: 35px; width: 1px; background-color: #adadad !important; float: left; margin-left: 5px; margin-right: 5px"></div>
                <p style="float: left; font-size: 25px; margin-top: 8px;">Team.Studio</p>
                <div class="container">
                    <div class="large-div" style="flex-basis: 22%; display: flex; justify-content: center; align-items: center;">
                        <center>
                            <img height="150px" width="150px" class="imgTeamLogo" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeam.logo}.png" alt="${item.name}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'">
                            <p style="font-size: 16px; margin-bottom: 0">${mTeam.name}</p>
                            <c:set var="competitionList" value=""/>
                            <c:forEach var="competition" items="${mCompetitions}">
                                <c:if test="${!competitionList.isEmpty()}">
                                    <c:set var="competitionList" value="${competitionList} / "/>
                                </c:if>
                                <c:set var="competitionList" value="${competitionList}${competition.name}"/>
                            </c:forEach>
                            <p style="font-size: 10px; margin: 0">${competitionList}</p>
                        </center>
                    </div>
                    <div style="flex-basis: 56%">
                        <div class="box2">
                            <center>
                                <p style="font-size: 14px;"><spring:message code="team.partite"/><br>
                                <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="/sicstv/images/lineup_new.png"><br>
                                <span style="font-size: 15px">${mTeam.vittorie + mTeam.pareggi + mTeam.sconfitte}</span><br>
                            </center>
                        </div>
                        <div class="box2">
                            <center>
                                <p style="font-size: 14px;"><spring:message code="team.reti"/><br>
                                <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="/sicstv/images/ball.png"><br>
                                <c:if test="${mTeam.golFatti >= mTeam.golSubiti}">
                                    <span style="font-size: 15px">${mTeam.golFatti} - ${mTeam.golSubiti} (<span style="color: limegreen !important">${mTeam.getDiffGoal()}</span>)</span>
                                </c:if>
                                <c:if test="${mTeam.golSubiti > mTeam.golFatti}">
                                    <span style="font-size: 15px">${mTeam.golFatti} - ${mTeam.golSubiti} (<span style="color: red !important">${mTeam.getDiffGoal()}</span>)</span>
                                </c:if>
                            </center>
                        </div>
                        <div class="box3">
                            <center>
                                <p style="font-size: 14px; margin: 0"><spring:message code="team.vittorie"/></p>
                                <span style="font-size: 25px; color: limegreen !important">${mTeam.vittorie}</span><br>
                                <span style="font-size: 10px" title="<spring:message code="team.vittorie.title"/>">(${mTeam.vittorieInCasa} - ${mTeam.vittorie - mTeam.vittorieInCasa})</span>
                            </center>
                        </div>
                        <div class="box3">
                            <center>
                                <p style="font-size: 14px; margin: 0"><spring:message code="team.pareggi"/></p>
                                <span style="font-size: 25px; color: gray !important">${mTeam.pareggi}</span><br>
                                <span style="font-size: 10px" title="<spring:message code="team.pareggi.title"/>">(${mTeam.pareggiInCasa} - ${mTeam.pareggi - mTeam.pareggiInCasa})</span>
                            </center>
                        </div>
                        <div class="box3">
                            <center>
                                <p style="font-size: 14px; margin: 0"><spring:message code="team.sconfitte"/></p>
                                <span style="font-size: 25px; color: red !important">${mTeam.sconfitte}</span><br>
                                <span style="font-size: 10px" title="<spring:message code="team.sconfitte.title"/>">(${mTeam.sconfitteInCasa} - ${mTeam.sconfitte - mTeam.sconfitteInCasa})</span>
                            </center>
                        </div>
                    </div>
                    <div class="large-div" style="flex-basis: 22%; text-align: right !important" id="canvasContainer">
                        <p style="margin: 0; width: 150px; float: right; text-align: center; font-size: 14px;"><spring:message code="team.main.module"/></p>
<!--                        <img height="150px" width="150px" src="data:image/jpeg;base64,${mTeamMostUsedModuleImage}" style="margin-top: 3px; margin-bottom: 3px">-->
                        <p style="margin: 0; width: 150px; float: right; text-align: center; font-weight: bold; font-size: 15px;">${mTeamMostUsedModule.replace("-", " - ")}</p>
                    </div>
                </div>
                <center><p style="font-size: 16px; font-weight: bold; color: red !important; margin-top: 25px"><spring:message code="player.report.no.data"/></p></center>
            </div>
        </c:when>
        <c:otherwise>
            <c:set var="teamList" value=""/>
            <c:forEach var="team" items="${mTeams}">
                <c:if test="${!teamList.isEmpty()}">
                    <c:set var="teamList" value="${teamList} / "/>
                </c:if>
                <c:set var="teamList" value="${teamList}${team.name}"/>
            </c:forEach>
            <c:set var="competitionList" value=""/>
            <c:forEach var="competition" items="${mCompetitions}">
                <c:if test="${!competitionList.isEmpty()}">
                    <c:set var="competitionList" value="${competitionList} / "/>
                </c:if>
                <c:set var="competitionList" value="${competitionList}${competition.name}"/>
            </c:forEach>

            <div class="page">
                <img height="40px" width="150px" src="/sicstv/images/logosics.png" style="margin-bottom: 15px; float: left">
                <div style="height: 35px; width: 1px; background-color: #adadad !important; float: left; margin-left: 5px; margin-right: 5px"></div>
                <p style="float: left; font-size: 25px; margin-top: 8px;">Team.Studio</p>
                <button onclick="document.title='${mTeam.name}-${mDay}'; window.print(); return false;" class="uk-button hideOnPrint" style="float: right"><spring:message code="report.save.pdf"/></button>
                <div class="container">
                    <div class="large-div" style="flex-basis: 22%; display: flex; justify-content: center; align-items: center;">
                        <center>
                            <img height="150px" width="150px" class="imgTeamLogo" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeam.logo}.png" alt="${item.name}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'">
                            <p style="font-size: 16px; margin-bottom: 0">${mTeam.name}</p>
                            <p style="font-size: 10px; margin: 0">${competitionList}</p>
                        </center>
                    </div>
                    <div style="flex-basis: 56%">
                        <div class="box2">
                            <center>
                                <p style="font-size: 14px;"><spring:message code="team.partite"/><br>
                                <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="/sicstv/images/lineup_new.png"><br>
                                <span style="font-size: 15px">${mTeam.vittorie + mTeam.pareggi + mTeam.sconfitte}</span><br>
                            </center>
                        </div>
                        <div class="box2">
                            <center>
                                <p style="font-size: 14px;"><spring:message code="team.reti"/><br>
                                <img style="margin-top: 2px; margin-bottom: 2px;" height="30px" width="40px" src="/sicstv/images/ball.png"><br>
                                <c:if test="${mTeam.golFatti >= mTeam.golSubiti}">
                                    <span style="font-size: 15px">${mTeam.golFatti} - ${mTeam.golSubiti} (<span style="color: limegreen !important">${mTeam.getDiffGoal()}</span>)</span>
                                </c:if>
                                <c:if test="${mTeam.golSubiti > mTeam.golFatti}">
                                    <span style="font-size: 15px">${mTeam.golFatti} - ${mTeam.golSubiti} (<span style="color: red !important">${mTeam.getDiffGoal()}</span>)</span>
                                </c:if>
                            </center>
                        </div>
                        <div class="box3">
                            <center>
                                <p style="font-size: 14px; margin: 0"><spring:message code="team.vittorie"/></p>
                                <span style="font-size: 25px; color: limegreen !important">${mTeam.vittorie}</span><br>
                                <span style="font-size: 10px" title="<spring:message code="team.vittorie.title"/>">(${mTeam.vittorieInCasa} - ${mTeam.vittorie - mTeam.vittorieInCasa})</span>
                            </center>
                        </div>
                        <div class="box3">
                            <center>
                                <p style="font-size: 14px; margin: 0"><spring:message code="team.pareggi"/></p>
                                <span style="font-size: 25px; color: gray !important">${mTeam.pareggi}</span><br>
                                <span style="font-size: 10px" title="<spring:message code="team.pareggi.title"/>">(${mTeam.pareggiInCasa} - ${mTeam.pareggi - mTeam.pareggiInCasa})</span>
                            </center>
                        </div>
                        <div class="box3">
                            <center>
                                <p style="font-size: 14px; margin: 0"><spring:message code="team.sconfitte"/></p>
                                <span style="font-size: 25px; color: red !important">${mTeam.sconfitte}</span><br>
                                <span style="font-size: 10px" title="<spring:message code="team.sconfitte.title"/>">(${mTeam.sconfitteInCasa} - ${mTeam.sconfitte - mTeam.sconfitteInCasa})</span>
                            </center>
                        </div>
                    </div>
                    <div class="large-div" style="flex-basis: 22%; text-align: right !important" id="canvasContainer">
                        <p style="margin: 0; width: 150px; float: right; text-align: center; font-size: 14px;"><spring:message code="team.main.module"/></p>
<!--                        <img height="150px" width="150px" src="data:image/jpeg;base64,${mTeamMostUsedModuleImage}" style="margin-top: 3px; margin-bottom: 3px">-->
                        <p style="margin: 0; width: 150px; float: right; text-align: center; font-weight: bold; font-size: 15px;">${mTeamMostUsedModule.replace("-", " - ")}</p>
                    </div>
                </div>
                <c:if test="${mLoadProfile}">
                    <div style="margin-top: 20px">
                    <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse; margin-bottom: 5px; margin-top: 0px">
                        <tbody>
                            <!-- Colonne -->
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                                <td class="tableHeader"><spring:message code="report.player.statistica"/></td>
                                <td class="tableHeader"><spring:message code="report.player.posizione"/></td>
                                <td class="tableHeader"><spring:message code="report.team.valore"/></td>
                                <td class="tableHeader" style="width: 45%"></td>
                                <td class="tableHeader"><spring:message code="report.team.media.campionato"/></td>
                                <td class="tableHeader"><spring:message code="report.team.valore.massimo"/></td>
                            </tr>
                            <c:set var="rowCounter" value="${0}"/>
                            <c:set var="maxRows" value="${25}"/>
                            <c:set var="pages" value="${1}"/>
                            <c:forEach var="statName" items="${mCompetitionAverage.keySet()}">
                                <c:set var="rowCounter" value="${rowCounter + 1}"/>
                                <c:if test="${rowCounter >= maxRows}">
                                    <c:set var="maxRows" value="${34}"/>
                                    <c:set var="rowCounter" value="${0}"/>
                                    </tbody></table></div><c:if test="${pages == 1}"></div></c:if>
                                    <div class="page"><table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;"><tbody>
                                    <!-- Colonne -->
                                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                                        <td class="tableHeader"><spring:message code="report.player.statistica"/></td>
                                        <td class="tableHeader"><spring:message code="report.player.posizione"/></td>
                                        <td class="tableHeader"><spring:message code="report.team.valore"/></td>
                                        <td class="tableHeader" style="width: 45%"></td>
                                        <td class="tableHeader"><spring:message code="report.team.media.campionato"/></td>
                                        <td class="tableHeader"><spring:message code="report.team.valore.massimo"/></td>
                                    </tr>
                                    <c:set var="pages" value="${pages + 1}"/>
                                </c:if>
                                
                                <c:set var="row" value="${mTeamStats.get(statName)}"/>
                                <c:set var="maxStatValue" value="${mTables.get(statName).get(0).eventAmountAverage}"/>
                                <c:set var="minStatValue" value="${mTables.get(statName).get(mTables.get(statName).size() - 1).eventAmountAverage}"/>

                                <c:if test="${maxStatValue != null}">
                                    <c:set var="rowPercentage" value="${row.getEventPercentage(maxStatValue, minStatValue)}"/>
                                    <tr>
                                        <c:if test="${row.filter != null}">
                                            <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${isMultiCompetition == 'true' ? -1 : competitionIdList}&idTeam=${mTeam.id}&idPlayer=&goals=false&event=${row.getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                            <td><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.encodeString(link)}" style="text-decoration: underline">${statName} (${row.getEventAmountFormatted()})</a></td>
                                        </c:if>
                                        <c:if test="${row.filter == null}">
                                            <td>${statName} (${row.getEventAmountFormatted()})</td>
                                        </c:if>
                                        <td class="tableRow">${row.index}�</td>
                                        <td class="tableRow">${row.getEventAmountAverage1Decimal()}</td>
                                        <td>
                                            <div class="container-progress-bar">
                                                <c:set var="averagePercentage" value="${row.getPercentageByValues(mCompetitionAverage.get(statName), maxStatValue, minStatValue)}"/>

                                                <div style="margin-left: ${averagePercentage}%; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                                <c:set var="color" value="${row.statsTypeIsOpposite ? '#e85f5f' : 'green'}"/>
                                                <c:set var="halfAverage" value="${averagePercentage / 2}"/>
                                                <c:set var="secondHalfAverage" value="${(100 - averagePercentage) / 2}"/>
                                                <c:choose>
                                                    <c:when test="${rowPercentage <= halfAverage}">
                                                        <c:set var="color" value="${row.statsTypeIsOpposite ? 'green' : '#e85f5f'}"/>
                                                    </c:when>
                                                    <c:when test="${rowPercentage <= (averagePercentage - 1)}">
                                                        <c:set var="color" value="${row.statsTypeIsOpposite ? '#5ab550' : '#e1e85f'}"/>
                                                    </c:when>
                                                    <c:when test="${rowPercentage <= (averagePercentage + secondHalfAverage)}">
                                                        <c:set var="color" value="${row.statsTypeIsOpposite ? '#e1e85f' : '#5ab550'}"/>
                                                    </c:when>
                                                </c:choose>
                                                <div class="progress" statsTypeIsOpposite="${row.statsTypeIsOpposite}" halfAverage="${halfAverage}" secondHalfAverage="${secondHalfAverage}" rowPercentage="${rowPercentage}" style="margin-left: -${averagePercentage}%; width: ${rowPercentage}%; background-color: ${color} !important">
                                                </div>
                                            </div>
                                        </td>
                                        <td class="tableRow">${mStatsFunctions.valueWith1Decimal(mCompetitionAverage.get(statName))}</td>
                                        <td class="tableRow">${mTables.get(statName).get(0).getEventAmountAverage1Decimal()}</td>
                                    </tr>
                                </c:if>
                            </c:forEach>
                        </tbody>
                    </table>
                    </div>
                    <c:if test="${pages == 1}"></div></c:if>
                    <div class="page">
                    </c:if>
                    <c:set var="counter" value="${0}"/>
                    <c:forEach var="table" items="${mRanking.keySet()}">
                        <c:if test="${!mRanking.get(table).isEmpty()}">
                            <c:if test="${!mLoadProfile and counter == 0}">
                                <!--</div>-->
                            </c:if>
                            <c:if test="${counter > 0}">
                                </div><div class="page">
                            </c:if>
                            <p style="text-align: center; font-size: 25px; width: 100%; margin: 0">
                                <img height="75px" width="75px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${mCompetitionMap.get(table).logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${mCompetitionMap.get(table).name}">
                                <b>${mCompetitionMap.get(table).name}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if></b> RANKING
                            </p>
                            <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                                <tbody>
                                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                                        <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                                        <td class="tableHeader" style="text-align: left"><spring:message code="player.report.team"/></td>
                                        <td class="tableHeader"><spring:message code="team.report.totale.punti"/></td>
                                        <td class="tableHeader"><spring:message code="team.report.totale.partite"/></td>
                                        <td class="tableHeader"><spring:message code="team.report.punti.penalita"/></td>
                                        <td class="tableHeader"><spring:message code="team.report.totale.vinte"/></td>
                                        <td class="tableHeader"><spring:message code="team.report.totale.pari"/></td>
                                        <td class="tableHeader"><spring:message code="team.report.totale.perse"/></td>
                                        <td class="tableHeader"><spring:message code="team.report.totale.goal"/></td>
                                        <td class="tableHeader"><spring:message code="team.report.totale.goal.subiti"/></td>
                                        <td class="tableHeader"><spring:message code="team.report.totale.goal.differenza"/></td>
                                    </tr>
                                    <c:set var="rowCounter" value="${0}"/>
                                    <c:set var="maxRows" value="${30}"/>
                                    <c:if test="${counter == 0}">
                                        <c:set var="maxRows" value="${20}"/>
                                    </c:if>
                                    <c:forEach var="row" items="${mRanking.get(table)}">
                                        <c:if test="${rowCounter >= maxRows}">
                                            <c:set var="counter" value="${counter + 1}"/>
                                            <c:set var="rowCounter" value="${0}"/>
                                            <c:set var="maxRows" value="${35}"/>
                                            <c:if test="${counter == 0}">
                                                </tbody></table></div></div><div class="page">
                                            </c:if>
                                            <c:if test="${counter > 0}">
                                                </tbody></table></div><div class="page">
                                            </c:if>
                                            <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;"><tbody>
                                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                                                <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                                                <td class="tableHeader" style="text-align: left"><spring:message code="player.report.team"/></td>
                                                <td class="tableHeader"><spring:message code="team.report.totale.punti"/></td>
                                                <td class="tableHeader"><spring:message code="team.report.totale.partite"/></td>
                                                <td class="tableHeader"><spring:message code="team.report.punti.penalita"/></td>
                                                <td class="tableHeader"><spring:message code="team.report.totale.vinte"/></td>
                                                <td class="tableHeader"><spring:message code="team.report.totale.pari"/></td>
                                                <td class="tableHeader"><spring:message code="team.report.totale.perse"/></td>
                                                <td class="tableHeader"><spring:message code="team.report.totale.goal"/></td>
                                                <td class="tableHeader"><spring:message code="team.report.totale.goal.subiti"/></td>
                                                <td class="tableHeader"><spring:message code="team.report.totale.goal.differenza"/></td>
                                            </tr>           
                                        </c:if>
                                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" <c:if test="${row.teamId == mTeam.id}">style="background-color: orange !important"</c:if>>
                                            <td style="vertical-align: middle">
                                                <c:choose>
                                                    <c:when test="${row.index == 1}">
                                                        <img height="20px" width="20px" src="/sicstv/images/1st.png">
                                                    </c:when>
                                                    <c:when test="${row.index == 2}">
                                                        <img height="20px" width="20px" src="/sicstv/images/2nd.png">
                                                    </c:when>
                                                    <c:when test="${row.index == 3}">
                                                        <img height="20px" width="20px" src="/sicstv/images/3rd.png">
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${row.index}
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                            <td style="vertical-align: middle"><img height="20px" width="20px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.teamLogo}.png" alt="${row.teamName}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'"> ${row.teamName}</td>
                                            <td style="vertical-align: middle; text-align: center">${row.totalPoints}</td>
                                            <td style="vertical-align: middle; text-align: center">${row.totalMatchPlayed}</td>
                                            <td style="vertical-align: middle; text-align: center">${row.penalityPoints}</td>
                                            <td style="vertical-align: middle; text-align: center">${row.totalWin}</td>
                                            <td style="vertical-align: middle; text-align: center">${row.totalEqual}</td>
                                            <td style="vertical-align: middle; text-align: center">${row.totalLost}</td>
                                            <td style="vertical-align: middle; text-align: center">${row.totalGoal}</td>
                                            <td style="vertical-align: middle; text-align: center">${row.totalGoalSuffered}</td>
                                            <td style="vertical-align: middle; text-align: center">${row.totalGoalDifference}</td>
                                        </tr>
                                        <c:set var="rowCounter" value="${rowCounter + 1}"/>
                                    </c:forEach>
                                </tbody>
                            </table>
                            <c:set var="counter" value="${counter + 1}"/>
                        </c:if>
                    </c:forEach>
            </div>
<!--            <div class="page">
                <p style="text-align: center; font-size: 25px; width: 100%; margin: 0; text-transform: uppercase; font-weight: bold"><spring:message code="team.stats.giocatori"/></p>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                            <td class="tableHeader" style="font-size: 15px; font-weight: bold">${mRole.get("P").toUpperCase()}</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                            <td><spring:message code="team.giocatore"/></td>
                            <td><spring:message code="team.report.partite.giocate"/></td>
                            <td>${mAllStatsType.get(1).desc}</td>
                            <td>${mAllStatsType.get(79).desc}</td>
                            <td>${mAllStatsType.get(59).desc}</td>
                        </tr>
                        <c:forEach var="player" items="${mRoleAndPlayer.get('P')}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                                <td>${mPlayerStat.get(player).get(1).playerJerseyNumber} - ${mPlayerStat.get(player).get(1).playerKnownName}</td>
                                <td>${mPlayerStat.get(player).get(1).fixtureAmount}</td>
                                <td>${mPlayerStat.get(player).get(1).getEventAmountFormatted()}</td>
                                <td>${mPlayerStat.get(player).get(79).getEventAmountFormatted()}</td>
                                <td>${mPlayerStat.get(player).get(59).getEventAmountFormatted()}</td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                            <td class="tableHeader" style="font-size: 15px; font-weight: bold">${mRole.get("D").toUpperCase()}</td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                            <td><spring:message code="team.giocatore"/></td>
                            <td><spring:message code="team.report.partite.giocate"/></td>
                            <td>${mAllStatsType.get(1).desc}</td>
                        </tr>
                        <c:forEach var="player" items="${mRoleAndPlayer.get('D')}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                                <td>${mPlayerStat.get(player).get(1).playerJerseyNumber} - ${mPlayerStat.get(player).get(1).playerKnownName}</td>
                                <td>${mPlayerStat.get(player).get(1).fixtureAmount}</td>
                                <td>${mPlayerStat.get(player).get(1).getEventAmountFormatted()}</td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                            <td class="tableHeader" style="font-size: 15px; font-weight: bold">${mRole.get("C").toUpperCase()}</td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                            <td><spring:message code="team.giocatore"/></td>
                            <td><spring:message code="team.report.partite.giocate"/></td>
                            <td>${mAllStatsType.get(1).desc}</td>
                        </tr>
                        <c:forEach var="player" items="${mRoleAndPlayer.get('C')}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                                <td>${mPlayerStat.get(player).get(1).playerJerseyNumber} - ${mPlayerStat.get(player).get(1).playerKnownName}</td>
                                <td>${mPlayerStat.get(player).get(1).fixtureAmount}</td>
                                <td>${mPlayerStat.get(player).get(1).getEventAmountFormatted()}</td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important">
                            <td class="tableHeader" style="font-size: 15px; font-weight: bold">${mRole.get("A").toUpperCase()}</td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                            <td><spring:message code="team.giocatore"/></td>
                            <td><spring:message code="team.report.partite.giocate"/></td>
                            <td>${mAllStatsType.get(1).desc}</td>
                        </tr>
                        <c:forEach var="player" items="${mRoleAndPlayer.get('A')}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: white !important">
                                <td>${mPlayerStat.get(player).get(1).playerJerseyNumber} - ${mPlayerStat.get(player).get(1).playerKnownName}</td>
                                <td>${mPlayerStat.get(player).get(1).fixtureAmount}</td>
                                <td>${mPlayerStat.get(player).get(1).getEventAmountFormatted()}</td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
            </div>-->
            <c:if test="${!mLoadProfile and counter <= 1}">
                </div>
            </c:if>
            <c:forEach var="competition" items="${mAnalysisTeam.keySet()}">
            <div class="page">
                <div class="headerQuad" style="margin-top: 2px">
                    <div class="red redQuad"><div style="transform: rotate(-90deg);margin-right: -55px;margin-left: -50px;"><span style="font-weight: bold">Team.Studio</span></div></div>
                    <div class="black" style="width: 390px; height: 5px; margin-left: 50px; margin-top: -5px;"></div>
                    <div class="red" style="width: 45px; height: 5px; margin-left: 440px; margin-top: -5px;"></div>
                    <img height="40px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${mCompetitionMap.get(competition).logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${mCompetitionMap.get(competition).name}" style="margin-top: -45px; margin-left: 50px">
                    <p style="margin-top: -40px; margin-left: 90px; font-size: 16px; font-weight: bold;"><spring:message code="team.report.analisi.duelli"/></p>
                    <div class="black" style="width: 714px; height: 45px; margin-left: 50px; margin-top: -83px;"></div>
                    <img height="40px" width="80px" src="/sicstv/images/logosics.png" style="margin-top: -35px; margin-left: 600px">
                    <p style="margin-top: 26px; margin-left: 90px; font-size: 12px;">${mCompetitionMap.get(competition).name}</p>
                    <p style="margin-top: -95px; margin-left: 90px; font-size: 12px; font-weight: bold; color: white !important; max-width: 500px"><spring:message code="menu.area.analisi"/> ${mCompetitionMap.get(competition).name}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if> (${mSeason.name})</p>
                </div>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 150px"><spring:message code="player.report.team"/></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(48).desc}</td>
                            <td style="font-size: 15px; text-align: center">% ${mAllStatsType.get(50).desc}</td>
                            <td style="font-size: 15px; text-align: center">% ${mAllStatsType.get(54).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(44).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(46).desc}</td>
                        </tr>
                        <c:set var="entries" value="${mStatsFunctions.getStatsSortedByStatsId(mAnalysisTeam.get(competition), 48, mTeam.id)}"/>
                        <c:set var="rows" value="${entries.keySet()}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:set var="doubleRow" value="${0}"/>
                        <c:forEach var="teamStat" items="${rows}">
                        <c:set var="row" value="${mAnalysisTeam.get(competition).get(teamStat)}"/>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" <c:if test="${row.get(1).teamId == mTeam.id}">style="background-color: orange !important"</c:if>>
                            <td>
                                <c:choose>
                                    <c:when test="${(entries.get(teamStat) + 1) == 1}">
                                        <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 2}">
                                        <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 3}">
                                        <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                    </c:when>
                                    <c:otherwise>
                                        ${(entries.get(teamStat) + 1)}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td style="width: 210px; padding: 0; vertical-align: middle"><img height="15px" width="15px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.get(1).teamLogo}.png" title="${row.get(1).teamName}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'"> ${row.get(1).teamName}</td>
                            <c:if test="${row.get(48).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(48).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(48).encodeString(link)}" style="text-decoration: underline">${row.get(48).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(48).filter == null}">
                                <td style="text-align: center">${row.get(48).getEventAmountFormatted()}</td>
                            </c:if>
                            <td style="text-align: center">${mStatsFunctions.valueWithoutDecimals(row.get(50).eventAmount / row.get(48).eventAmount * 100)} %</td>
                            <td style="text-align: center">${mStatsFunctions.valueWithoutDecimals(row.get(54).eventAmount / row.get(53).eventAmount * 100)} %</td>
                            <c:if test="${row.get(44).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(44).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(44).encodeString(link)}" style="text-decoration: underline">${row.get(44).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(44).filter == null}">
                                <td style="text-align: center">${row.get(44).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(46).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(46).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(46).encodeString(link)}" style="text-decoration: underline">${row.get(46).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(46).filter == null}">
                                <td style="text-align: center">${row.get(46).getEventAmountFormatted()}</td>
                            </c:if>
                        </tr>
                        <c:set var="doubleRow" value="${doubleRow + (row.get(1).teamName.length() > 99 ? 1 : 0)}"/>
                        </c:forEach>
                    </tbody>
                </table>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 150px"><spring:message code="team.giocatore"/><br><span style="font-size: 8px">(${mTeam.name})</span></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(48).desc}</td>
                            <td style="font-size: 15px; text-align: center">% ${mAllStatsType.get(50).desc}</td>
                            <td style="font-size: 15px; text-align: center">% ${mAllStatsType.get(54).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(44).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(46).desc}</td>
                        </tr>
                        <c:set var="rowCounter" value="${1}"/>
                        <c:set var="rows" value="${mStatsFunctions.getPlayerStatsSortedByStatsId(mAnalysisTeamPlayers.get(competition), 48)}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:forEach var="playerStat" items="${rows}">
                        <c:if test="${rowCounter < (20 - (doubleRow / 7))}">
                            <c:set var="row" value="${mAnalysisTeamPlayers.get(competition).get(playerStat)}"/>
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td>
                                    <c:choose>
                                        <c:when test="${rowCounter == 1}">
                                            <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                        </c:when>
                                        <c:when test="${rowCounter == 2}">
                                            <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                        </c:when>
                                        <c:when test="${rowCounter == 3}">
                                            <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                        </c:when>
                                        <c:otherwise>
                                            ${rowCounter}
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td style="width: 210px">${row.get(1).playerKnownName} (${row.get(1).playerRoleCode})</td>
                                <c:if test="${row.get(48).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(48).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(48).encodeString(link)}" style="text-decoration: underline">${row.get(48).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(48).filter == null}">
                                    <td style="text-align: center">${row.get(48).getEventAmountFormatted()}</td>
                                </c:if>
                                <td style="text-align: center">${mStatsFunctions.valueWithoutDecimals(row.get(50).eventAmount / row.get(48).eventAmount * 100)} %</td>
                                <td style="text-align: center">${mStatsFunctions.valueWithoutDecimals(row.get(54).eventAmount / row.get(53).eventAmount * 100)} %</td>
                                <c:if test="${row.get(44).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(44).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(44).encodeString(link)}" style="text-decoration: underline">${row.get(44).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(44).filter == null}">
                                    <td style="text-align: center">${row.get(44).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(46).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(46).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(46).encodeString(link)}" style="text-decoration: underline">${row.get(46).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(46).filter == null}">
                                    <td style="text-align: center">${row.get(46).getEventAmountFormatted()}</td>
                                </c:if>
                            </tr>
                            <c:set var="rowCounter" value="${rowCounter + 1}"/>
                        </c:if>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
            <div class="page">
                <div class="headerQuad" style="margin-top: 2px">
                    <div class="red redQuad"><div style="transform: rotate(-90deg);margin-right: -55px;margin-left: -50px;"><span style="font-weight: bold">Team.Studio</span></div></div>
                    <div class="black" style="width: 390px; height: 5px; margin-left: 50px; margin-top: -5px;"></div>
                    <div class="red" style="width: 45px; height: 5px; margin-left: 440px; margin-top: -5px;"></div>
                    <img height="40px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${mCompetitionMap.get(competition).logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${mCompetitionMap.get(competition).name}" style="margin-top: -45px; margin-left: 50px">
                    <p style="margin-top: -40px; margin-left: 90px; font-size: 16px; font-weight: bold;"><spring:message code="team.report.analisi.falli"/></p>
                    <div class="black" style="width: 714px; height: 45px; margin-left: 50px; margin-top: -83px;"></div>
                    <img height="40px" width="80px" src="/sicstv/images/logosics.png" style="margin-top: -35px; margin-left: 600px">
                    <p style="margin-top: 26px; margin-left: 90px; font-size: 12px;">${mCompetitionMap.get(competition).name}</p>
                    <p style="margin-top: -95px; margin-left: 90px; font-size: 12px; font-weight: bold; color: white !important; max-width: 500px"><spring:message code="menu.area.analisi"/> ${mCompetitionMap.get(competition).name}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if> (${mSeason.name})</p>
                </div>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 175px"><spring:message code="player.report.team"/></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(57).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(58).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(75).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(76).desc}</td>
                        </tr>
                        <c:set var="entries" value="${mStatsFunctions.getStatsSortedByStatsId(mAnalysisTeam.get(competition), 57, mTeam.id)}"/>
                        <c:set var="rows" value="${entries.keySet()}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:forEach var="teamStat" items="${rows}">
                        <c:set var="row" value="${mAnalysisTeam.get(competition).get(teamStat)}"/>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" <c:if test="${row.get(1).teamId == mTeam.id}">style="background-color: orange !important"</c:if>>
                            <td>
                                <c:choose>
                                    <c:when test="${(entries.get(teamStat) + 1) == 1}">
                                        <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 2}">
                                        <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 3}">
                                        <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                    </c:when>
                                    <c:otherwise>
                                        ${(entries.get(teamStat) + 1)}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td style="width: 210px; padding: 0; vertical-align: middle"><img height="15px" width="15px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.get(1).teamLogo}.png" title="${row.get(1).teamName}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'"> ${row.get(1).teamName}</td>
                            <c:if test="${row.get(57).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(57).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(57).encodeString(link)}" style="text-decoration: underline">${row.get(57).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(57).filter == null}">
                                <td style="text-align: center">${row.get(57).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(58).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(58).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(58).encodeString(link)}" style="text-decoration: underline">${row.get(58).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(58).filter == null}">
                                <td style="text-align: center">${row.get(58).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(75).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(75).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(75).encodeString(link)}" style="text-decoration: underline">${row.get(75).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(75).filter == null}">
                                <td style="text-align: center">${row.get(75).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(76).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(76).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(76).encodeString(link)}" style="text-decoration: underline">${row.get(76).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(76).filter == null}">
                                <td style="text-align: center">${row.get(76).getEventAmountFormatted()}</td>
                            </c:if>
                        </tr>
                        </c:forEach>
                    </tbody>
                </table>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 175px"><spring:message code="team.giocatore"/><br><span style="font-size: 8px">(${mTeam.name})</span></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(57).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(58).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(75).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(76).desc}</td>
                        </tr>
                        <c:set var="rowCounter" value="${1}"/>
                        <c:set var="rows" value="${mStatsFunctions.getPlayerStatsSortedByStatsId(mAnalysisTeamPlayers.get(competition), 57)}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:forEach var="playerStat" items="${rows}">
                        <c:set var="row" value="${mAnalysisTeamPlayers.get(competition).get(playerStat)}"/>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                            <td>
                                <c:choose>
                                    <c:when test="${rowCounter == 1}">
                                        <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                    </c:when>
                                    <c:when test="${rowCounter == 2}">
                                        <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                    </c:when>
                                    <c:when test="${rowCounter == 3}">
                                        <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                    </c:when>
                                    <c:otherwise>
                                        ${rowCounter}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td style="width: 210px">${row.get(1).playerKnownName} (${row.get(1).playerRoleCode})</td>
                            <c:if test="${row.get(57).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(57).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(57).encodeString(link)}" style="text-decoration: underline">${row.get(57).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(57).filter == null}">
                                <td style="text-align: center">${row.get(57).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(58).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(58).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(58).encodeString(link)}" style="text-decoration: underline">${row.get(58).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(58).filter == null}">
                                <td style="text-align: center">${row.get(58).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(75).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(75).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(75).encodeString(link)}" style="text-decoration: underline">${row.get(75).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(75).filter == null}">
                                <td style="text-align: center">${row.get(75).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(76).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(76).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(76).encodeString(link)}" style="text-decoration: underline">${row.get(76).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(76).filter == null}">
                                <td style="text-align: center">${row.get(76).getEventAmountFormatted()}</td>
                            </c:if>
                        </tr>
                        <c:set var="rowCounter" value="${rowCounter + 1}"/>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
            <div class="page">
                <div class="headerQuad" style="margin-top: 2px">
                    <div class="red redQuad"><div style="transform: rotate(-90deg);margin-right: -55px;margin-left: -50px;"><span style="font-weight: bold">Team.Studio</span></div></div>
                    <div class="black" style="width: 390px; height: 5px; margin-left: 50px; margin-top: -5px;"></div>
                    <div class="red" style="width: 45px; height: 5px; margin-left: 440px; margin-top: -5px;"></div>
                    <img height="40px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${mCompetitionMap.get(competition).logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${mCompetitionMap.get(competition).name}" style="margin-top: -45px; margin-left: 50px">
                    <p style="margin-top: -40px; margin-left: 90px; font-size: 16px; font-weight: bold;"><spring:message code="team.report.analisi.passaggi"/></p>
                    <div class="black" style="width: 714px; height: 45px; margin-left: 50px; margin-top: -83px;"></div>
                    <img height="40px" width="80px" src="/sicstv/images/logosics.png" style="margin-top: -35px; margin-left: 600px">
                    <p style="margin-top: 26px; margin-left: 90px; font-size: 12px;">${mCompetitionMap.get(competition).name}</p>
                    <p style="margin-top: -95px; margin-left: 90px; font-size: 12px; font-weight: bold; color: white !important; max-width: 500px"><spring:message code="menu.area.analisi"/> ${mCompetitionMap.get(competition).name}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if> (${mSeason.name})</p>
                </div>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 125px"><spring:message code="player.report.team"/></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(10).desc}</td>
                            <td style="font-size: 15px; text-align: center">% ${mAllStatsType.get(12).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(8).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(26).desc}</td>
                            <td style="font-size: 15px; text-align: center">% ${mAllStatsType.get(28).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(24).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(20).desc}</td>
                        </tr>
                        <c:set var="entries" value="${mStatsFunctions.getStatsSortedByStatsId(mAnalysisTeam.get(competition), 10, mTeam.id)}"/>
                        <c:set var="rows" value="${entries.keySet()}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:set var="doubleRow" value="${0}"/>
                        <c:forEach var="teamStat" items="${rows}">
                        <c:set var="row" value="${mAnalysisTeam.get(competition).get(teamStat)}"/>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" <c:if test="${row.get(1).teamId == mTeam.id}">style="background-color: orange !important"</c:if>>
                            <td>
                                <c:choose>
                                    <c:when test="${(entries.get(teamStat) + 1) == 1}">
                                        <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 2}">
                                        <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 3}">
                                        <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                    </c:when>
                                    <c:otherwise>
                                        ${(entries.get(teamStat) + 1)}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td style="width: 210px; padding: 0; vertical-align: middle"><img height="15px" width="15px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.get(1).teamLogo}.png" title="${row.get(1).teamName}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'"> ${row.get(1).teamName}</td>
                            <c:if test="${row.get(10).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(10).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(10).encodeString(link)}" style="text-decoration: underline">${row.get(10).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(10).filter == null}">
                                <td style="text-align: center">${row.get(10).getEventAmountFormatted()}</td>
                            </c:if>
                            <td style="text-align: center">${mStatsFunctions.valueWithoutDecimals(row.get(12).eventAmount / row.get(10).eventAmount * 100)} %</td>
                            <c:if test="${row.get(8).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(8).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(8).encodeString(link)}" style="text-decoration: underline">${row.get(8).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(8).filter == null}">
                                <td style="text-align: center">${row.get(8).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(26).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(26).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(26).encodeString(link)}" style="text-decoration: underline">${row.get(26).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(26).filter == null}">
                                <td style="text-align: center">${row.get(26).getEventAmountFormatted()}</td>
                            </c:if>
                            <td style="text-align: center">${mStatsFunctions.valueWithoutDecimals(row.get(28).eventAmount / row.get(26).eventAmount * 100)} %</td>
                            <c:if test="${row.get(24).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(24).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(24).encodeString(link)}" style="text-decoration: underline">${row.get(24).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(24).filter == null}">
                                <td style="text-align: center">${row.get(24).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(20).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(20).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(20).encodeString(link)}" style="text-decoration: underline">${row.get(20).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(20).filter == null}">
                                <td style="text-align: center">${row.get(20).getEventAmountFormatted()}</td>
                            </c:if>
                        </tr>
                        <c:set var="doubleRow" value="${doubleRow + (row.get(1).teamName.length() > 99 ? 1 : 0)}"/>
                        </c:forEach>
                    </tbody>
                </table>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 125px"><spring:message code="team.giocatore"/><br><span style="font-size: 8px">(${mTeam.name})</span></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(10).desc}</td>
                            <td style="font-size: 15px; text-align: center">% ${mAllStatsType.get(12).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(8).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(26).desc}</td>
                            <td style="font-size: 15px; text-align: center">% ${mAllStatsType.get(28).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(24).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(20).desc}</td>
                        </tr>
                        <c:set var="rowCounter" value="${1}"/>
                        <c:set var="rows" value="${mStatsFunctions.getPlayerStatsSortedByStatsId(mAnalysisTeamPlayers.get(competition), 10)}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:forEach var="playerStat" items="${rows}">
                        <c:if test="${rowCounter < (20 - (doubleRow / 7))}">
                            <c:set var="row" value="${mAnalysisTeamPlayers.get(competition).get(playerStat)}"/>
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td>
                                    <c:choose>
                                        <c:when test="${rowCounter == 1}">
                                            <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                        </c:when>
                                        <c:when test="${rowCounter == 2}">
                                            <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                        </c:when>
                                        <c:when test="${rowCounter == 3}">
                                            <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                        </c:when>
                                        <c:otherwise>
                                            ${rowCounter}
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td style="width: 210px">${row.get(1).playerKnownName} (${row.get(1).playerRoleCode})</td>
                                <c:if test="${row.get(10).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(10).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(10).encodeString(link)}" style="text-decoration: underline">${row.get(10).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(10).filter == null}">
                                    <td style="text-align: center">${row.get(10).getEventAmountFormatted()}</td>
                                </c:if>
                                <td style="text-align: center">${mStatsFunctions.valueWithoutDecimals(row.get(12).eventAmount / row.get(10).eventAmount * 100)} %</td>
                                <c:if test="${row.get(8).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(8).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(8).encodeString(link)}" style="text-decoration: underline">${row.get(8).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(8).filter == null}">
                                    <td style="text-align: center">${row.get(8).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(26).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(26).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(26).encodeString(link)}" style="text-decoration: underline">${row.get(26).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(26).filter == null}">
                                    <td style="text-align: center">${row.get(26).getEventAmountFormatted()}</td>
                                </c:if>
                                <td style="text-align: center">${mStatsFunctions.valueWithoutDecimals(row.get(28).eventAmount / row.get(26).eventAmount * 100)} %</td>
                                <c:if test="${row.get(24).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(24).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(24).encodeString(link)}" style="text-decoration: underline">${row.get(24).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(24).filter == null}">
                                    <td style="text-align: center">${row.get(24).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(20).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(20).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(20).encodeString(link)}" style="text-decoration: underline">${row.get(20).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(20).filter == null}">
                                    <td style="text-align: center">${row.get(20).getEventAmountFormatted()}</td>
                                </c:if>
                            </tr>
                            <c:set var="rowCounter" value="${rowCounter + 1}"/>
                        </c:if>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
            <div class="page">
                <div class="headerQuad" style="margin-top: 2px">
                    <div class="red redQuad"><div style="transform: rotate(-90deg);margin-right: -55px;margin-left: -50px;"><span style="font-weight: bold">Team.Studio</span></div></div>
                    <div class="black" style="width: 390px; height: 5px; margin-left: 50px; margin-top: -5px;"></div>
                    <div class="red" style="width: 45px; height: 5px; margin-left: 440px; margin-top: -5px;"></div>
                    <img height="40px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${mCompetitionMap.get(competition).logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${mCompetitionMap.get(competition).name}" style="margin-top: -45px; margin-left: 50px">
                    <p style="margin-top: -40px; margin-left: 90px; font-size: 16px; font-weight: bold;"><spring:message code="team.report.analisi.tiri"/></p>
                    <div class="black" style="width: 714px; height: 45px; margin-left: 50px; margin-top: -83px;"></div>
                    <img height="40px" width="80px" src="/sicstv/images/logosics.png" style="margin-top: -35px; margin-left: 600px">
                    <p style="margin-top: 26px; margin-left: 90px; font-size: 12px;">${mCompetitionMap.get(competition).name}</p>
                    <p style="margin-top: -95px; margin-left: 90px; font-size: 12px; font-weight: bold; color: white !important; max-width: 500px"><spring:message code="menu.area.analisi"/> ${mCompetitionMap.get(competition).name}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if> (${mSeason.name})</p>
                </div>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 125px"><spring:message code="player.report.team"/></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(2).desc}</td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.in.porta"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.da.area"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.da.fuori"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.da.area.piccola"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.di.testa"/></td>
                        </tr>
                        <c:set var="entries" value="${mStatsFunctions.getStatsSortedByStatsId(mAnalysisTeam.get(competition), 2, mTeam.id)}"/>
                        <c:set var="rows" value="${entries.keySet()}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:set var="doubleRow" value="${0}"/>
                        <c:forEach var="teamStat" items="${rows}">
                        <c:set var="row" value="${mAnalysisTeam.get(competition).get(teamStat)}"/>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" <c:if test="${row.get(1).teamId == mTeam.id}">style="background-color: orange !important"</c:if>>
                            <td>
                                <c:choose>
                                    <c:when test="${(entries.get(teamStat) + 1) == 1}">
                                        <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 2}">
                                        <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 3}">
                                        <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                    </c:when>
                                    <c:otherwise>
                                        ${(entries.get(teamStat) + 1)}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td style="width: 210px; padding: 0; vertical-align: middle"><img height="15px" width="15px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.get(1).teamLogo}.png" title="${row.get(1).teamName}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'"> ${row.get(1).teamName}</td>
                            <c:if test="${row.get(2).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(2).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(2).encodeString(link)}" style="text-decoration: underline">${row.get(2).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(2).filter == null}">
                                <td style="text-align: center">${row.get(2).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(3).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(3).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(3).encodeString(link)}" style="text-decoration: underline">${row.get(3).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(3).filter == null}">
                                <td style="text-align: center">${row.get(3).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(4).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(4).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(4).encodeString(link)}" style="text-decoration: underline">${row.get(4).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(4).filter == null}">
                                <td style="text-align: center">${row.get(4).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(5).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(5).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(5).encodeString(link)}" style="text-decoration: underline">${row.get(5).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(5).filter == null}">
                                <td style="text-align: center">${row.get(5).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(62).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(62).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(62).encodeString(link)}" style="text-decoration: underline">${row.get(62).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(62).filter == null}">
                                <td style="text-align: center">${row.get(62).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(6).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(6).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(6).encodeString(link)}" style="text-decoration: underline">${row.get(6).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(6).filter == null}">
                                <td style="text-align: center">${row.get(6).getEventAmountFormatted()}</td>
                            </c:if>
                        </tr>
                        <c:set var="doubleRow" value="${doubleRow + (row.get(1).teamName.length() > 99 ? 1 : 0)}"/>
                        </c:forEach>
                    </tbody>
                </table>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 125px"><spring:message code="team.giocatore"/><br><span style="font-size: 8px">(${mTeam.name})</span></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(2).desc}</td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.in.porta"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.da.area"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.da.fuori"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.da.area.piccola"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.di.testa"/></td>
                        </tr>
                        <c:set var="rowCounter" value="${1}"/>
                        <c:set var="rows" value="${mStatsFunctions.getPlayerStatsSortedByStatsId(mAnalysisTeamPlayers.get(competition), 2)}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:forEach var="playerStat" items="${rows}">
                        <c:if test="${rowCounter < (20 - (doubleRow / 7))}">
                            <c:set var="row" value="${mAnalysisTeamPlayers.get(competition).get(playerStat)}"/>
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td>
                                    <c:choose>
                                        <c:when test="${rowCounter == 1}">
                                            <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                        </c:when>
                                        <c:when test="${rowCounter == 2}">
                                            <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                        </c:when>
                                        <c:when test="${rowCounter == 3}">
                                            <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                        </c:when>
                                        <c:otherwise>
                                            ${rowCounter}
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td style="width: 210px">${row.get(1).playerKnownName} (${row.get(1).playerRoleCode})</td>
                                <c:if test="${row.get(2).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(2).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(2).encodeString(link)}" style="text-decoration: underline">${row.get(2).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(2).filter == null}">
                                    <td style="text-align: center">${row.get(2).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(3).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(3).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(3).encodeString(link)}" style="text-decoration: underline">${row.get(3).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(3).filter == null}">
                                    <td style="text-align: center">${row.get(3).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(4).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(4).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(4).encodeString(link)}" style="text-decoration: underline">${row.get(4).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(4).filter == null}">
                                    <td style="text-align: center">${row.get(4).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(5).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(5).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(5).encodeString(link)}" style="text-decoration: underline">${row.get(5).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(5).filter == null}">
                                    <td style="text-align: center">${row.get(5).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(62).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(62).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(62).encodeString(link)}" style="text-decoration: underline">${row.get(62).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(62).filter == null}">
                                    <td style="text-align: center">${row.get(62).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(6).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(6).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(6).encodeString(link)}" style="text-decoration: underline">${row.get(6).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(6).filter == null}">
                                    <td style="text-align: center">${row.get(6).getEventAmountFormatted()}</td>
                                </c:if>
                            </tr>
                            <c:set var="rowCounter" value="${rowCounter + 1}"/>
                        </c:if>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
            <div class="page">
                <div class="headerQuad" style="margin-top: 2px">
                    <div class="red redQuad"><div style="transform: rotate(-90deg);margin-right: -55px;margin-left: -50px;"><span style="font-weight: bold">Team.Studio</span></div></div>
                    <div class="black" style="width: 390px; height: 5px; margin-left: 50px; margin-top: -5px;"></div>
                    <div class="red" style="width: 45px; height: 5px; margin-left: 440px; margin-top: -5px;"></div>
                    <img height="40px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${mCompetitionMap.get(competition).logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${mCompetitionMap.get(competition).name}" style="margin-top: -45px; margin-left: 50px">
                    <p style="margin-top: -40px; margin-left: 90px; font-size: 16px; font-weight: bold;"><spring:message code="team.report.analisi.goal"/></p>
                    <div class="black" style="width: 714px; height: 45px; margin-left: 50px; margin-top: -83px;"></div>
                    <img height="40px" width="80px" src="/sicstv/images/logosics.png" style="margin-top: -35px; margin-left: 600px">
                    <p style="margin-top: 26px; margin-left: 90px; font-size: 12px;">${mCompetitionMap.get(competition).name}</p>
                    <p style="margin-top: -95px; margin-left: 90px; font-size: 12px; font-weight: bold; color: white !important; max-width: 500px"><spring:message code="menu.area.analisi"/> ${mCompetitionMap.get(competition).name}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if> (${mSeason.name})</p>
                </div>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 125px"><spring:message code="player.report.team"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.reti"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.da.area"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.da.fuori.area"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.reti.palla.inattiva"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.su.rigore"/></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(21).desc}</td>
                        </tr>
                        <c:set var="entries" value="${mStatsFunctions.getStatsSortedByStatsId(mAnalysisTeam.get(competition), 63, mTeam.id)}"/>
                        <c:set var="rows" value="${entries.keySet()}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:set var="doubleRow" value="${0}"/>
                        <c:forEach var="teamStat" items="${rows}">
                        <c:set var="row" value="${mAnalysisTeam.get(competition).get(teamStat)}"/>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" <c:if test="${row.get(1).teamId == mTeam.id}">style="background-color: orange !important"</c:if>>
                            <td>
                                <c:choose>
                                    <c:when test="${(entries.get(teamStat) + 1) == 1}">
                                        <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 2}">
                                        <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 3}">
                                        <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                    </c:when>
                                    <c:otherwise>
                                        ${(entries.get(teamStat) + 1)}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td style="width: 210px; padding: 0; vertical-align: middle"><img height="15px" width="15px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.get(1).teamLogo}.png" title="${row.get(1).teamName}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'"> ${row.get(1).teamName}</td>
                            <c:if test="${row.get(63).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(63).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(63).encodeString(link)}" style="text-decoration: underline">${row.get(63).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(63).filter == null}">
                                <td style="text-align: center">${row.get(63).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(64).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(64).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(64).encodeString(link)}" style="text-decoration: underline">${row.get(64).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(64).filter == null}">
                                <td style="text-align: center">${row.get(64).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(66).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(66).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(66).encodeString(link)}" style="text-decoration: underline">${row.get(66).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(66).filter == null}">
                                <td style="text-align: center">${row.get(66).getEventAmountFormatted()}</td>
                            </c:if>
                            <td style="text-align: center">${Math.round(1.0 * row.get(93).eventAmount + row.get(91).eventAmount + row.get(90).eventAmount + row.get(92).eventAmount + row.get(94).eventAmount)}</td>
                            <c:if test="${row.get(70).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(70).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(70).encodeString(link)}" style="text-decoration: underline">${row.get(70).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(70).filter == null}">
                                <td style="text-align: center">${row.get(70).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(21).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(21).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(21).encodeString(link)}" style="text-decoration: underline">${row.get(21).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(21).filter == null}">
                                <td style="text-align: center">${row.get(21).getEventAmountFormatted()}</td>
                            </c:if>
                        </tr>
                        <c:set var="doubleRow" value="${doubleRow + (row.get(1).teamName.length() > 99 ? 1 : 0)}"/>
                        </c:forEach>
                    </tbody>
                </table>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 125px"><spring:message code="team.giocatore"/><br><span style="font-size: 8px">(${mTeam.name})</span></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.reti"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.da.area"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.da.fuori.area"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.reti.palla.inattiva"/></td>
                            <td style="font-size: 15px; text-align: center"><spring:message code="team.report.analisi.su.rigore"/></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(21).desc}</td>
                        </tr>
                        <c:set var="rowCounter" value="${1}"/>
                        <c:set var="rows" value="${mStatsFunctions.getPlayerStatsSortedByStatsId(mAnalysisTeamPlayers.get(competition), 63)}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:forEach var="playerStat" items="${rows}">
                        <c:if test="${rowCounter < (20 - (doubleRow / 7))}">
                            <c:set var="row" value="${mAnalysisTeamPlayers.get(competition).get(playerStat)}"/>
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td>
                                    <c:choose>
                                        <c:when test="${rowCounter == 1}">
                                            <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                        </c:when>
                                        <c:when test="${rowCounter == 2}">
                                            <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                        </c:when>
                                        <c:when test="${rowCounter == 3}">
                                            <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                        </c:when>
                                        <c:otherwise>
                                            ${rowCounter}
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td style="width: 210px">${row.get(1).playerKnownName} (${row.get(1).playerRoleCode})</td>
                                <c:if test="${row.get(63).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(63).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(63).encodeString(link)}" style="text-decoration: underline">${row.get(63).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(63).filter == null}">
                                    <td style="text-align: center">${row.get(63).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(64).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(64).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(64).encodeString(link)}" style="text-decoration: underline">${row.get(64).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(64).filter == null}">
                                    <td style="text-align: center">${row.get(64).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(66).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(66).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(66).encodeString(link)}" style="text-decoration: underline">${row.get(66).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(66).filter == null}">
                                    <td style="text-align: center">${row.get(66).getEventAmountFormatted()}</td>
                                </c:if>
                                <td style="text-align: center">${Math.round(1.0 * row.get(93).eventAmount + row.get(91).eventAmount + row.get(90).eventAmount + row.get(92).eventAmount + row.get(94).eventAmount)}</td>
                                <c:if test="${row.get(70).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(70).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(70).encodeString(link)}" style="text-decoration: underline">${row.get(70).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(70).filter == null}">
                                    <td style="text-align: center">${row.get(70).getEventAmountFormatted()}</td>
                                </c:if>
                                <c:if test="${row.get(21).filter != null}">
                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(21).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                    <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(21).encodeString(link)}" style="text-decoration: underline">${row.get(21).getEventAmountFormatted()}</a></td>
                                </c:if>
                                <c:if test="${row.get(21).filter == null}">
                                    <td style="text-align: center">${row.get(21).getEventAmountFormatted()}</td>
                                </c:if>
                            </tr>
                            <c:set var="rowCounter" value="${rowCounter + 1}"/>
                        </c:if>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
            <div class="page">
                <div class="headerQuad" style="margin-top: 2px">
                    <div class="red redQuad"><div style="transform: rotate(-90deg);margin-right: -55px;margin-left: -50px;"><span style="font-weight: bold">Team.Studio</span></div></div>
                    <div class="black" style="width: 390px; height: 5px; margin-left: 50px; margin-top: -5px;"></div>
                    <div class="red" style="width: 45px; height: 5px; margin-left: 440px; margin-top: -5px;"></div>
                    <img height="40px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${mCompetitionMap.get(competition).logo}.png" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png'" alt="${mCompetitionMap.get(competition).name}" style="margin-top: -45px; margin-left: 50px">
                    <p style="margin-top: -40px; margin-left: 90px; font-size: 16px; font-weight: bold;"><spring:message code="team.report.analisi.parate"/></p>
                    <div class="black" style="width: 714px; height: 45px; margin-left: 50px; margin-top: -83px;"></div>
                    <img height="40px" width="80px" src="/sicstv/images/logosics.png" style="margin-top: -35px; margin-left: 600px">
                    <p style="margin-top: 26px; margin-left: 90px; font-size: 12px;">${mCompetitionMap.get(competition).name}</p>
                    <p style="margin-top: -95px; margin-left: 90px; font-size: 12px; font-weight: bold; color: white !important; max-width: 500px"><spring:message code="menu.area.analisi"/> ${mCompetitionMap.get(competition).name}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if> (${mSeason.name})</p>
                </div>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 175px"><spring:message code="player.report.team"/></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(59).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(98).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(99).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(60).desc}</td>
                        </tr>
                        <c:set var="entries" value="${mStatsFunctions.getStatsSortedByStatsId(mAnalysisTeam.get(competition), 59, mTeam.id)}"/>
                        <c:set var="rows" value="${entries.keySet()}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:forEach var="teamStat" items="${rows}">
                        <c:set var="row" value="${mAnalysisTeam.get(competition).get(teamStat)}"/>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" <c:if test="${row.get(1).teamId == mTeam.id}">style="background-color: orange !important"</c:if>>
                            <td>
                                <c:choose>
                                    <c:when test="${(entries.get(teamStat) + 1) == 1}">
                                        <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 2}">
                                        <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                    </c:when>
                                    <c:when test="${(entries.get(teamStat) + 1) == 3}">
                                        <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                    </c:when>
                                    <c:otherwise>
                                        ${(entries.get(teamStat) + 1)}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td style="width: 210px; padding: 0; vertical-align: middle"><img height="15px" width="15px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.get(1).teamLogo}.png" title="${row.get(1).teamName}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'"> ${row.get(1).teamName}</td>
                            <c:if test="${row.get(59).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(59).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(59).encodeString(link)}" style="text-decoration: underline">${row.get(59).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(59).filter == null}">
                                <td style="text-align: center">${row.get(59).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(98).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(98).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(98).encodeString(link)}" style="text-decoration: underline">${row.get(98).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(98).filter == null}">
                                <td style="text-align: center">${row.get(98).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(99).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(99).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(99).encodeString(link)}" style="text-decoration: underline">${row.get(99).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(99).filter == null}">
                                <td style="text-align: center">${row.get(99).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(60).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=&goals=false&event=${row.get(60).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(60).encodeString(link)}" style="text-decoration: underline">${row.get(60).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(60).filter == null}">
                                <td style="text-align: center">${row.get(60).getEventAmountFormatted()}</td>
                            </c:if>
                        </tr>
                        </c:forEach>
                    </tbody>
                </table>
                <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                    <tbody>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top" style="background: #f5f5f5 !important;">
                            <td class="tableHeader" style="text-align: left; width: 10px">#</td>
                            <td style="font-size: 18px; text-transform: uppercase; width: 175px"><spring:message code="team.giocatore"/><br><span style="font-size: 8px">(${mTeam.name})</span></td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(59).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(98).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(99).desc}</td>
                            <td style="font-size: 15px; text-align: center">${mAllStatsType.get(60).desc}</td>
                        </tr>
                        <c:set var="rowCounter" value="${1}"/>
                        <c:set var="rows" value="${mStatsFunctions.getPlayerStatsSortedByStatsId(mAnalysisTeamPlayers.get(competition), 59)}"/>
                        <c:if test="${rows.size() == 0}">
                            <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                <td colspan="9" style="text-align: center"><spring:message code="player.no.data"/></td>
                            </tr>
                        </c:if>
                        <c:forEach var="playerStat" items="${rows}">
                        <c:set var="row" value="${mAnalysisTeamPlayers.get(competition).get(playerStat)}"/>
                        <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                            <td>
                                <c:choose>
                                    <c:when test="${rowCounter == 1}">
                                        <img height="15px" width="15px" src="/sicstv/images/1st.png">
                                    </c:when>
                                    <c:when test="${rowCounter == 2}">
                                        <img height="15px" width="15px" src="/sicstv/images/2nd.png">
                                    </c:when>
                                    <c:when test="${rowCounter == 3}">
                                        <img height="15px" width="15px" src="/sicstv/images/3rd.png">
                                    </c:when>
                                    <c:otherwise>
                                        ${rowCounter}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td style="width: 210px">${row.get(1).playerKnownName} (${row.get(1).playerRoleCode})</td>
                            <c:if test="${row.get(59).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(59).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(59).encodeString(link)}" style="text-decoration: underline">${row.get(59).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(59).filter == null}">
                                <td style="text-align: center">${row.get(59).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(98).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(98).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(98).encodeString(link)}" style="text-decoration: underline">${row.get(98).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(98).filter == null}">
                                <td style="text-align: center">${row.get(98).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(99).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(99).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(99).encodeString(link)}" style="text-decoration: underline">${row.get(99).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(99).filter == null}">
                                <td style="text-align: center">${row.get(99).getEventAmountFormatted()}</td>
                            </c:if>
                            <c:if test="${row.get(60).filter != null}">
                                <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${row.get(1).competitionId}&idTeam=${row.get(1).teamId}&idPlayer=${row.get(1).playerId}&goals=false&event=${row.get(60).getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                <td style="text-align: center"><a onclick="jsShowBlockUI();" href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.get(60).encodeString(link)}" style="text-decoration: underline">${row.get(60).getEventAmountFormatted()}</a></td>
                            </c:if>
                            <c:if test="${row.get(60).filter == null}">
                                <td style="text-align: center">${row.get(60).getEventAmountFormatted()}</td>
                            </c:if>
                        </tr>
                        <c:set var="rowCounter" value="${rowCounter + 1}"/>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
                        
            <c:set var="counter" value="${0}"/>
            <c:forEach var="table" items="${mFinalTables.get(competition).keySet()}">
                <c:if test="${counter % 8 == 0}">
                    <!-- Cambio pagina -->
                    <c:if test="${counter > 0}">
                    </div>
                    </c:if>
                    <div class="page">
                        <div class="headerQuad" style="margin-top: 2px">
                            <div class="red redQuad"><div style="transform: rotate(-90deg);margin-right: -55px;margin-left: -50px;"><span style="font-weight: bold">Team.Studio</span></div></div>
                            <div class="black" style="width: 390px; height: 5px; margin-left: 50px; margin-top: -5px;"></div>
                            <div class="red" style="width: 45px; height: 5px; margin-left: 440px; margin-top: -5px;"></div>
                            <img height="40px" width="40px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeam.logo}.png" title="${mTeam.name}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'" style="margin-top: -45px; margin-left: 50px">
                            <p style="margin-top: -40px; margin-left: 90px; font-size: 16px; font-weight: bold;">${mTeam.name}</p>
                            <div class="black" style="width: 714px; height: 45px; margin-left: 50px; margin-top: -83px;"></div>
                            <img height="40px" width="80px" src="/sicstv/images/logosics.png" style="margin-top: -35px; margin-left: 600px">
                            <p style="margin-top: -40px; margin-left: 90px; font-size: 12px; font-weight: bold; color: white !important; max-width: 500px"><spring:message code="menu.area.analisi"/> ${mCompetitionMap.get(competition).name}<c:if test="${mGirone != null}"> - ${mGirone.groupName}</c:if> (${mSeason.name})</p>
                        </div>
                </c:if>

                <c:if test="${counter % 2 == 0}">
                        <div style="display: flex; margin-top: 5px;">
                </c:if>
                        <div style="flex: 1;<c:if test="${counter % 2 != 0}"> margin-left: 5px; margin-top: 0px;</c:if>">
                            <p style="margin-bottom: 1px; font-size: 15px"><strong>${table}</strong></p>
                            <div class="black" style="width: 80%; height: 5px;"></div>
                            <div class="red" style="width: 20%; height: 5px; margin-left: 80%; margin-top: -5px;"></div>
                            <table class="uk-table uk-table-hover uk-table-striped" style="border-collapse: collapse;">
                                <tbody>
                                    <tr class="uk-table-middle uk-margin-large-bottom uk-margin-large-top">
                                        <td class="tableHeader"></td>
                                        <td class="tableHeader"><spring:message code="player.report.team"/></td>
                                        <td class="tableHeader"><spring:message code="player.report.total"/></td>
                                        <c:if test="${mFinalTables.get(competition).get(table).get(0).statsTypeId != 1}">
                                        <td class="tableHeader"><spring:message code="player.report.average.90"/></td>
                                        </c:if>
                                    </tr>
                                    <c:forEach var="row" items="${mFinalTables.get(competition).get(table)}">
                                        <c:set var="trStyle" value=""/>
                                        <c:if test="${row.teamId == mTeam.id}">
                                            <c:set var="trStyle" value="${trStyle}background-color: orange !important;"/>
                                        </c:if>
                                        <c:if test="${row.index == 3}">
                                            <c:set var="trStyle" value="${trStyle}border-bottom: 2px solid black !important;"/>
                                        </c:if>
                                        <tr style="${trStyle}">
                                            <td style="padding: 0; vertical-align: middle">
                                                <center>
                                                <c:choose>
                                                    <c:when test="${row.index == 1}">
                                                        ${row.getIndexForTable()}
                                                        <!--<img height="15px" width="15px" src="/sicstv/images/1st.png">-->
                                                    </c:when>
                                                    <c:when test="${row.index == 2}">
                                                        ${row.getIndexForTable()}
                                                        <!--<img height="15px" width="15px" src="/sicstv/images/2nd.png">-->
                                                    </c:when>
                                                    <c:when test="${row.index == 3}">
                                                        ${row.getIndexForTable()}
                                                        <!--<img height="15px" width="15px" src="/sicstv/images/3rd.png">-->
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${row.getIndexForTable()}
                                                    </c:otherwise>
                                                </c:choose>
                                                </center>
                                            </td>
                                            <td style="padding: 0; vertical-align: middle">
                                                <img height="18px" width="18px" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.teamLogo}.png" title="${row.teamName}" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png'">
                                                ${row.teamName}
                                            </td>
                                            <c:choose>
                                                <c:when test="${row.filter != null && row.teamId == mTeam.id && row.eventAmount > 0}">
                                                    <c:set var="link" value="/sicstv/user/video.htm?personal=false&id=&idComp=${mCompetitionMap.get(competition).id}"/>
                                                    <c:set var="link" value="${link}&idTeam=${mTeam.id}"/>
                                                    <c:set var="link" value="${link}&idPlayer=&goals=false&event=${row.getFilterEncoded()}&filter=&limit=50&from=${mFrom}&to=${mTo}"/>
                                                    <td class="tableRow"><a href="/sicstv/user/goTo.htm?seasonId=${mSeason.id}&link=${row.encodeString(link)}" style="text-decoration: underline; font-weight: bold">${row.getEventAmountFormatted()}</a></td>
                                                </c:when>
                                                <c:otherwise>
                                                    <td class="tableRow">${row.getEventAmountForTable()}</td>
                                                </c:otherwise>
                                            </c:choose>
                                            <c:if test="${row.statsTypeId != 1}">
                                            <td class="tableRow">${row.getEventAmountAverage90Minutes2Decimals()}</td>
                                            </c:if>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </div>

                <c:if test="${counter % 2 != 0}">
                    </div>
                </c:if>
                <c:if test="${(counter + 1) == mFinalTables.get(competition).keySet().size()}">
                    <c:if test="${counter % 2 == 0}">
                    <div style="flex: 1;<c:if test="${counter % 2 != 0}"> margin-left: 5px; margin-top: 0px;</c:if>"></div></div>
                    </c:if>
                </c:if>

                <c:set var="counter" value="${counter + 1}"/>
            </c:forEach>
            <c:if test="${not empty mFinalTables.get(competition)}">
                </div>
            </c:if>
            </c:forEach>
        </c:otherwise>
    </c:choose>
    
</div>