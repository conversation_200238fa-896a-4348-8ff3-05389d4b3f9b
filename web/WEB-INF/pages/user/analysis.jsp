<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
	<head>
		<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
		<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
		<meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
		<title><spring:message code="theme.title"/></title>
		<script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
		<!--script type="text/javascript" src="/sicstv/js/jquery.cookie.js"></script-->
		<script type="text/javascript" src="/sicstv/js/jquery-ui-1.10.3.custom.js"></script>
		<link href="/sicstv/styles/darkness/jquery-ui-1.10.3.custom.css" rel="stylesheet" type="text/css" media="screen"  />
		<script type="text/javascript" src="/sicstv/js/i18n/jquery.ui.datepicker-it.js"></script>
		<link href="/sicstv/styles/main.css" rel="stylesheet" type="text/css"/>
		<link href="/sicstv/styles/analysisCss.css" rel="stylesheet" type="text/css"/>
		<link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
		<script type="text/javascript" src="/sicstv/js/utils.js" ></script>
		<script type="text/javascript">
			$(document).ready(function () {

				function jsListPlayers() {
					jsLoadingAlpha("#divListPlayer");
					$.ajax({
						type: "GET",
						url: "/sicstv/user/players.htm",
						cache: false,
						data: encodeURI("fixtureId=2888-2950-2929"),
						success: function (msg) {
							jsLoadingOff("#divListPlayer");
							$("#divListPlayer").html(msg);
							return false;
						}
					});
				}
				jsListPlayers();
			});
		</script>

	</head>
	<body>
		<div id="container">
			<%@ include file="header.jsp" %>
			
			<div id="divListPlayer" style="min-width: 400px; min-height: 400px"></div>

		</div>
		<%@ include file="footer.jsp" %>
	</body>
</html>
