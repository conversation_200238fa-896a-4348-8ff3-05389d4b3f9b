
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<c:forEach var="item" items="${mExportStatus}">
    <tr class="tableInProgress">
        <c:choose>
            <c:when test="${item.getProgressExport() < 100}">
                <td class="uk-text-center playlistColumnResult1">
                    <div id="dateBoxPlaylist">
                        <b>${item.getDateDayString()} <br>${item.getDateMonthString()} </b>
                    </div>
                </td>
                <td style="vertical-align: middle;">${item.nameFile}</td>
                <td></td>
                <td><i>${item.getProgressExport()}%</i>
                    <i class="uk-icon-spinner uk-icon-small uk-icon-spin uk-margin-right uk-float-right"></i></td>
                </c:when>
                <c:otherwise>
                <td class="uk-text-center playlistColumnResult1">
                    <div id="dateBoxPlaylist" <c:if test="${!item.playlist.isZip()}">onclick="openPlaylist(${item.playlist.id});"</c:if>>
                        <b>${item.playlist.getDateDayString()} <br>${item.playlist.getDateMonthString()} </b>
                    </div>
                </td>

                <td style="vertical-align: middle;">
                    <div <c:if test="${!item.playlist.isZip()}">onclick="openPlaylist(${item.playlist.id});"</c:if>>
                        <b>${item.playlist.name}</b>
                        <br />
                        <span class="playlistDetail" >
                            <c:choose>
                                <c:when test="${item.playlist.videoPublic=='-1'}">
                                    ${item.playlist.groupsetName}
                                </c:when>
                                <c:otherwise>
                                    ${item.playlist.uploadFirstName}&nbsp;${item.playlist.uploadLastName}
                                </c:otherwise>
                            </c:choose>
                        </span>
                    </div>
                </td>
                <td>

                </td>
                <td>
                    <c:choose>
                        <c:when test="${item.playlist.isZip()}">
                            <i <c:if test="${!item.playlist.isZip()}">onclick="openPlaylist(${item.playlist.id});"</c:if> class="uk-icon-small uk-icon-file-zip-o uk-margin-right"></i>
                        </c:when>
                        <c:otherwise>
                            <i <c:if test="${!item.playlist.isZip()}">onclick="openPlaylist(${item.playlist.id});"</c:if> class="uk-icon-small uk-icon-youtube-play uk-margin-right"></i>
                        </c:otherwise>
                    </c:choose>

                    <a onclick="jsDownload(${item.playlist.id});
                            return false;"><i class="uk-icon-small uk-icon-download uk-margin-left "></i></a>
                </td>
            </c:otherwise>
        </c:choose>
    </tr>
</c:forEach>



