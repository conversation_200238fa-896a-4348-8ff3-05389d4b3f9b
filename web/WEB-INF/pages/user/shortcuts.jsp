<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<button class="uk-button" id="shortcutsOpenButton" title="<spring:message code='shortcuts.tooltip'/>" style="margin-top: 5px; margin-right: 5px;" onclick="openShortcutsModal();"><i class="uk-icon-star uk-icon-small"></i></button>

<div id="shortcutsModal" class="shortcuts-content uk-hidden" style="padding: 5px 10px 10px 10px">
    <div style="margin-bottom: 10px">
        <center>
            <span style="font-size: 20px; text-transform: uppercase; font-weight: bold">
                <spring:message code='shortcuts'/>
                <img src='/sicstv/images/down-right-arrow.png'/>
            </span>
            <div style="float: right">
                <span class="modalClose" onclick="closeShortcutsModal();" style="top: -3px; right: 10px">&times;</span>
            </div>
        </center>
        <div class="shortcut-add">
            <button id="shortcutAddButton" onclick="openShortcutsModalManage();" class="uk-button" style="width: 100%"><spring:message code='menu.add'/><span style="float: right"><img src='/sicstv/images/down-right-arrow.png'/></span></button>

            <div id="shortcutsModalManage" class="<c:if test="${!mShortcuts.isEmpty()}">uk-hidden</c:if>">
                <div style="display: grid; grid-template-areas: 'name nameInput' 'group groupInput'; grid-template-columns: 30% 70%; align-items: center">
                    <div style="margin-top: 10px; grid-area: name">
                        <span><spring:message code='menu.name'/></span>
                    </div>
                    <div style="margin-top: 10px; grid-area: nameInput">
                        <input id="shortcutsModalName" type="text" value="" style="width: 91%">
                    </div>
                    <div style="margin-top: 10px; grid-area: group">
                        <span><spring:message code='shortcuts.group'/></span>
                    </div>
                    <div style="margin-top: 10px; grid-area: groupInput; max-width: 172.5px" id="shortcutsModalGroupSelect">
                        <span class="uk-form">
                            <select id="shortcutsModalGroup" class="uk-form-select selectItem" style="width: 75%">
                                <c:if test="${mShortcuts.isEmpty()}">
                                    <option value="GEN"><spring:message code='shortcuts.group.generic'/></option>
                                </c:if>
                                <c:forEach var="group" items="${mShortcuts.keySet()}">
                                    <option value="${group}">
                                        <c:choose>
                                            <c:when test="${group.equals('GEN')}">
                                                <spring:message code='shortcuts.group.generic'/>
                                            </c:when>
                                            <c:otherwise>
                                                ${group}
                                            </c:otherwise>
                                        </c:choose>
                                    </option>
                                </c:forEach>
                            </select>
                        </span>
                        <button class="uk-button" onclick="openShortcutsNewGroup()" title="<spring:message code='shortcut.new.group'/>">+</button>
                    </div>
                    <div style="margin-top: 10px; grid-area: groupInput; max-width: 172.5px" class="uk-hidden" id="shortcutsModalGroupNew">
                        <input id="shortcutsModalGroupInput" type="text" value="" style="width: 71%">
                        <button class="uk-button" onclick="closeShortcutsNewGroup()" title="<spring:message code='menu.annulla'/>">&times;</button>
                    </div>
                </div>
                <div style="margin-top: 10px">
                    <button class="uk-button" onclick="saveShortcuts();"><spring:message code='menu.save'/></button>
                    <c:if test="${mLinkInShortcuts}">
                        <button class="uk-button" onclick="removeShortcuts();"><spring:message code='menu.remove'/></button>
                    </c:if>
                </div>
            </div>
        </div>
    </div>

    <c:if test="${!mShortcuts.isEmpty()}">
        <ul class="uk-nav">
            <c:forEach var="group" items="${mShortcuts.keySet()}">
                <li style="min-width: 10em; margin-top: 15px">
                    <div style="text-transform: uppercase; margin-bottom: 10px;">
                        <i class="uk-icon-folder"></i>
                        <c:choose>
                            <c:when test="${group.equals('GEN')}">
                                <b><spring:message code='shortcuts.group.generic'/></b>
                            </c:when>
                            <c:otherwise>
                                <b>${group.replace("_", " ")}</b>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </li>
                <hr/>
                <li style="min-width: 10em" class="shortcut-hover">
                    <c:forEach var="link" items="${mShortcuts.get(group)}">
                        <div class="center-vertically clickRow" onclick="location.href= '${link.link}'" style="padding-left: 10%; padding-right: 10%; margin-bottom: 3px; height: 25px">
                            <a href="${link.link}">
                                ${link.name}
                            </a>
                            <div style="margin-left: auto" class="clickRow" onclick="deleteShortcut(${link.id})"><i class="uk-icon-trash"></i></div>
                        </div>
                    </c:forEach>
                </li>
            </c:forEach>
        </ul>
    </c:if>
</div>