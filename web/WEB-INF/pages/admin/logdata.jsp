<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <title><spring:message code="theme.title"/></title>
        <script src="https://code.jquery.com/jquery-1.12.4.js"></script>
        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
        <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
        <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css"/>
        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css"  />
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        <link rel="icon" href="/sicstv/images/favicon.ico"/>
        <script type="text/javascript" src="/sicstv/js/utils.js" ></script>
        <script type="text/javascript">

            /*var _gaq = _gaq || [];
            _gaq.push(['_setAccount', 'UA-********-1']);
            _gaq.push(['_trackPageview']);

            (function () {
                var ga = document.createElement('script');
                ga.type = 'text/javascript';
                ga.async = true;
                ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
                var s = document.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(ga, s);
            })();
*/
            jQuery(document).ready(function () {
                //jsListUser();
                $("#linkLogData").addClass("uk-active");
            });

            /*jQuery(document).ready(function () {
             
             $.datepicker.setDefaults($.datepicker.regional[ "it" ]);
             
             $("#datepickerFrom").datepicker({
             showOn: 'button',
             buttonText: 'Data',
             buttonImage: '/sicstv/images/calendar.gif',
             buttonImageOnly: true,
             onSelect: function (dateText) {
             $('#formDateFrom').val(dateText);
             }
             });
             
             $("#datepickerTo").datepicker({
             showOn: 'button',
             buttonText: 'Data',
             buttonImage: '/sicstv/images/calendar.gif',
             buttonImageOnly: true,
             onSelect: function (dateText) {
             $('#formDateTo').val(dateText);
             }
             });
             
             });*/

            function jsListUser() {
                //jsLoadingAlpha("#divList");
                var action = $("#actionSelect").val();
                $.ajax({
                    type: "GET",
                    url: "/sicstv/admin/loglist.htm",
                    data: encodeURI("formDateFrom=" + $('#formDateFrom').val() + "&formDateTo=" + $('#formDateTo').val() + "&formIdVideo=" + $('#formIdVideo').val() + "&formUserName=" + $('#formUserName').val() + "&formIdAction=" + action + "&formApplication=" + $("#applicationSelect").val()),
                    cache: false,
                    success: function (msg) {
                        //jsLoadingOff("#divList");
                        $("#divList").html(msg);
                        return false;
                    }
                });
            }
        </script>

    </head>
    <body>
        <div id="container">
            <%@ include file="header.jsp" %>
            <div id="matchFilter" class="uk-margin-top">
                <span class="uk-margin-all uk-hidden"><spring:message code="menu.idvideo"/></span>
                <input id="formIdVideo" value="${formIdVideo}" type="text" class="ui-state-default ui-corner-all inputSpacer2 uk-hidden" style="text-align:center;margin-left:2px;"/>
                <span class="uk-margin-all"><spring:message code="menu.username"/></span>
                <input id="formUserName" value="${formUserName}" type="text" class="ui-state-default ui-corner-all inputSpacer2" style="text-align:center;margin-left:2px;"/>
                <span class="uk-margin-all"><spring:message code="menu.seleziona.azione"/></span>
                <select id="actionSelect">
                    <option value="" selected></option>
                    <option value="LOGIN">Login</option>
                    <option value="LOGIN_CORRECT">Login Correct</option>
                    <option value="LOGIN_ERROR">Login Error</option>
                    <option value="LOGOUT">Logout</option>
                    <option value="LOGOUT_SESSION">Logout Session</option>
                    <option value="LOGOUT_TIMEOUT">Logout Timeout</option>
                    <option value="VIDEO_DOWNLOAD">Video Download</option>
                    <option value="VIDEO_DOWNLOAD_COMPLETE">Video Download Complete</option>
                    <option value="VIDEO_DOWNLOAD_ERROR">Video Download Error</option>
                    <option value="VIDEO_DOWNLOAD_ERROR_SIZE">Video Download Error Size</option>
                    <option value="VIDEO_DOWNLOAD_STOP">Video Download Stop</option>
                    <option value="VIDEO_UPLOAD_COMPLETE">Video Upload Complete</option>
                    <option value="VIDEO_STREAM">Video Stream</option>
                </select>
                <span class="uk-margin-all"><spring:message code="lib.dataDa"/></span>
                <input id="formDateFrom" class="ui-state-default ui-corner-all inputSpacer3" 
                       type="text" value="${formDateFrom}" size="20" style="text-align:center;"/>
                <input id="datepickerFrom" type="hidden"/>
                <span class="uk-margin-all"><spring:message code="lib.dataA"/></span>
                <input  id="formDateTo" class="ui-state-default ui-corner-all inputSpacer3" 
                        type="text" value="${formDateTo}" size="20" style="text-align:center;"/>
                <input id="datepickerTo" type="hidden"/>
                <span class="uk-margin-all"><spring:message code="menu.application"/></span>
                <select id="applicationSelect">
                    <option value="" selected></option>
                    <option value="VM">VideoMatch</option>
                    <option value="sicstv">SICS.tv</option>
                    <!--option value="feeds">LiveFeeds</option>
                    <option value="Dynamic">Dynamic</option>
                    <option value="Presenter">VMPresenter</option>
                    <option value="SDA">SICSDataAccess</option-->
                </select>
                <button class="uk-button uk-button-small uk-margin-all" onclick="jsListUser();">
                    <spring:message code='menu.conferma'/>
                </button>
            </div>		
            <!-- Area restituzione risultato  -->
            <div id="divList" class="uk-width-1-1"></div>
        </div>
        <%@ include file="footer.jsp" %>
    </body>
</html>


