<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<script>
    function killSession(userId) {
        if (window.confirm("Sicuro di voler forzare la disconnessione?")) {
            $.ajax({
                type: "GET",
                url: "/sicstv/admin/killSession.htm",
                cache: false,
                data: encodeURI("&userId=" + userId),
                success: function (msg) {
                    if (msg === "true") {
                        UIkit.notify("Sessione Distrutta Correttamente", {status: 'success', timeout: 1000});
                    } else {
                        UIkit.notify("Sessione NON Distrutta", {status: 'danger', timeout: 2500});
                    }
                },
                error: function () {
                    UIkit.notify("Errore nella ri-lettura dei dati. Rotta 'homeStartExport.htm'", {status: 'danger', timeout: 2500});
                }
            });
        }
    }
</script>

<div>
    <center><p style="margin: 0; font-weight: bold; text-transform: uppercase;"><spring:message code="menu.admin.totale.sessioni"/>: ${mTotalSessions}</p></center>
    
    <table class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1">
        <thead>
            <tr>
                <th>Utente</th>
                <th style="text-align: center"><spring:message code="menu.admin.num.sessioni"/></th>
                <th style="text-align: center">Multi Login</th>
                <th style="text-align: center"><spring:message code="menu.admin.azioni"/></th>
            </tr>
        </thead>
        <tbody>
            <c:forEach var="userId" items="${mSessions.keySet()}">
                <tr>
                    <td style="vertical-align: middle;">
                        <a onclick="openUserDetail(${mUsers.get(userId).id})">${mUsers.get(userId).firstName} ${mUsers.get(userId).lastName}</a>
                    </td>
                    <td style="vertical-align: middle; text-align: center">
                        ${mSessions.get(userId).size()}
                    </td>
                    <td style="vertical-align: middle; text-align: center">
                        ${mUsers.get(userId).multi_login}
                    </td>
                    <td style="text-align: center">
                        <button class="uk-button" onclick="killSession(${userId})" color="red"><spring:message code="menu.admin.disconnetti"/></button>
                    </td>
                </tr>
            </c:forEach>
        </tbody>
    </table>
</div>