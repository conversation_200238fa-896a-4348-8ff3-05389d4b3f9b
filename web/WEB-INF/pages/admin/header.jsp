<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
<script src="/sicstv/js/blockUI.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
<script type="text/javascript">
    window.addEventListener('beforeunload', function (event) {
        // questo evento viene richiamato ogni volta prima che si esce dalla pagina
        // quindi sia per navigazione avanti e indietro, che per reload della pagina
        jsShowBlockUI();
    });
</script>

<div id="topheader" class="uk-container">
    <a class="uk-navbar-brand" href="/sicstv/user/home.htm"><img style='width:150px;' src="/sicstv/<spring:message code='theme.logo'/>" title="SICS.tv" alt="SICS.tv"></a>
    <ul class="uk-navbar-nav">
        <li>
            <ul class="uk-subnav uk-subnav-pill uk-subnav-line uk-hidden-small uk-margin-remove">
                <li id="linkDashboard"> <a href="/sicstv/admin/home.htm?timeFilter=2" class="headerSubnav uk-button">Dashboard</a></li>
                <li id="linkAgentPayment"> <a href="/sicstv/admin/agentPayment.htm" class="headerSubnav uk-button"><spring:message code="menu.elenco.pagamenti"/></a></li>
                <c:if test="${mUser.isUserAdmin()}">
                    <li id="linkUserData"> <a href="/sicstv/admin/userdata.htm" class="headerSubnav uk-button"><spring:message code="menu.elenco.utenti"/></a></li>
                    <li id="linkLogData"><a href="/sicstv/admin/logdata.htm" class="headerSubnav uk-button"><spring:message code="menu.azioni.utenti"/></a></li>
                    <li id="linkCompetitionData"><a href="/sicstv/admin/competitionDetails.htm?timeFilter=3" class="headerSubnav uk-button">Competition Checker</a></li>
                </c:if>
                <li><a href="/sicstv/auth/logout.htm" class="headerSubnav uk-button"><spring:message code="menu.logout"/></a></li>
            </ul>
        </li>
    </ul>
</div>
