<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <title><spring:message code="theme.title"/></title>
        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <link rel="icon" href="/sicstv/images/favicon.ico"/>
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>

        <script>

            jQuery(document).ready(function () {
                $('#loglistTable').DataTable({
                    "columnDefs": [
                        {type: 'date-euro', targets: 0}
                    ],
                    "paging": true,
                    "lengthChange": true,
                    "pageLength": 100,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "order": [[0, "desc"]]
                });
            });

        </script>
    </head>
    <body>
        <table id="loglistTable" class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1">
            <thead>
                <tr>
                    <th><spring:message code="video.data"/></th>
                    <th><spring:message code="menu.username"/></th>
                    <th><spring:message code="menu.idvideo"/></th>
                    <th><spring:message code="menu.seleziona.azione"/></th>
                    <th><spring:message code="menu.application"/></th>
                </tr>
            </thead>
            <tbody>
                <c:forEach var="item" items="${mLogDataList}">
                    <tr>
                        <td>
                            ${item.getDateString()}
                        </td>
                        <td>
                            ${item.loginname}
                        </td>
                        <td>
                            ${item.video}
                        </td>
                        <td>
                            ${item.action}
                        </td>
                        <td>
                            ${item.application}
                        </td>
                    </tr>
                </c:forEach>
            </tbody>
        </table>
    </body>
</html>


