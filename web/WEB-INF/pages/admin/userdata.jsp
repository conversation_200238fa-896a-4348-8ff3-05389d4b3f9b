<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <title><spring:message code="theme.title"/></title>
        <script src="https://code.jquery.com/jquery-1.12.4.js"></script>
        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
        <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
        <link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css"/>
        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css"  />
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        <script type="text/javascript" src="/sicstv/js/utils.js" ></script>
        <link rel="icon" href="/sicstv/images/favicon.ico"/>
        <script type="text/javascript">

            var _gaq = _gaq || [];
            _gaq.push(['_setAccount', 'UA-********-1']);
            _gaq.push(['_trackPageview']);

            (function () {
                var ga = document.createElement('script');
                ga.type = 'text/javascript';
                ga.async = true;
                ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
                var s = document.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(ga, s);
            })();

            jQuery(document).ready(function () {
                jsListUser();
                $("#linkUserData").addClass("uk-active");

            });
            /*jQuery(document).ready(function () {
             
             $.datepicker.setDefaults($.datepicker.regional[ "it" ]);
             $("#datepickerFrom").datepicker({
             showOn: 'button',
             buttonText: 'Data',
             buttonImage: '/sicstv/images/calendar.gif',
             buttonImageOnly: true,
             cache: false,
             onSelect: function (dateText) {
             $('#formDateFrom').val(dateText);
             }
             });
             
             $("#datepickerTo").datepicker({
             showOn: 'button',
             buttonText: 'Data',
             buttonImage: '/sicstv/images/calendar.gif',
             buttonImageOnly: true,
             onSelect: function (dateText) {
             $('#formDateTo').val(dateText);
             }
             });
             jsListUser();
             });*/


            function jsListUser() {
                jsLoadingAlpha("#divUserList");
                $.ajax({
                    type: "GET",
                    url: "/sicstv/admin/userlist.htm",
                    data: encodeURI("formDateFrom=" + $('#formDateFrom').val() + "&formDateTo=" + $('#formDateTo').val() + "&formNome=" + $('#formNome').val() + "&formCognome=" + $('#formCognome').val() + "&formApplication=" + $("#applicationSelect").val()),
                    cache: false,
                    success: function (msg) {
                        jsLoadingOff("#divUserList");
                        $("#divUserList").html(msg);
                        return false;
                    }
                });
            }


        </script>

    </head>
    <body>
        <div id="container">
            <%@ include file="header.jsp" %>
            <div id="matchFilter" class="uk-margin-top">
                &nbsp Nome
                <input id="formNome" value="${formNome}" type="text" class="ui-state-default ui-corner-all inputSpacer2" 
                       style="width:60px; text-align:center;margin-left:2px;" />
                &nbsp Cognome
                <input id="formCognome" value="${formCognome}" type="text" class="ui-state-default ui-corner-all inputSpacer2" 
                       style="width:60px; text-align:center;margin-left:2px;"/>
                <!--
                &nbsp Ruolo
                <select id="selectRole">
                    <option value="" selected></option>
                    <option value="ROLE_USER">Utente</option>
                    <option value="ROLE_ADMIN">Amministratore</option>
                </select>
                -->
                <span class="uk-margin-all"><spring:message code="lib.dataDa"/></span>
                <input type="date" id="formDateFrom" name="formDateFrom" value="${formDateFrom}">
                    <!--
                    <input id="formDateFrom" class="ui-state-default ui-corner-all inputSpacer3" 
                           type="text" value="${formDateFrom}" size="20" style="text-align:center;"/>
                    <input id="datepickerFrom" type="hidden"/>
                    -->
                    <span class="uk-margin-all"><spring:message code="lib.dataA"/></span>
                    <input type="date" id="formDateTo" name="formDateTo" value="${formDateTo}">
                    <!--
                    <input  id="formDateTo" class="ui-state-default ui-corner-all inputSpacer3" 
                            type="text" value="${formDateTo}" size="20" style="text-align:center;"/>
                    <input id="datepickerTo" type="hidden"/>
                    -->
                    <span class="uk-margin-all"><spring:message code="menu.application"/></span>
                    <select id="applicationSelect">
                        <option value="VM" selected="selected">VideoMatch</option>
                        <option value="sicstv">SICS.tv</option>
                        <!--option value="feeds">LiveFeeds</option>
                        <option value="Dynamic">Dynamic</option>
                        <option value="Presenter">VMPresenter</option>
                        <option value="SDA">SICSDataAccess</option-->
                    </select>

                    <button class="uk-button uk-button-small uk-margin-all" onclick="jsListUser();">
                        <spring:message code='menu.conferma'/>
                    </button>
            </div>		
            <!-- Area restituzione risultato  -->
            <div id="divUserList"></div>
        </div>
        <%@ include file="footer.jsp" %>
    </body>
</html>


