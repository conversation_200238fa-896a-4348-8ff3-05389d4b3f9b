<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1"/>
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
            <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
            <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
            <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
            <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
            <title><spring:message code="theme.title"/></title>
            <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
            <link rel="icon" href="/sicstv/images/favicon.ico"/>
            <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>

            <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
            <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

            <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
            <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
            <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>

            <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/tooltip.js"></script>

            <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
            <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
            <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet"/>

            <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
            <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
            <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>

            <!-- D3.js Library for canvas/svg -->
            <script src="https://d3js.org/d3.v5.min.js"></script>
            <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.1/d3-tip.min.js"></script>
            <script src="/sicstv/js/sics-canvas-player.js?<%=System.currentTimeMillis()%>"></script>

            <script type="text/javascript">
                $(document).ready(function () {
                    var basePitch = {...pitch};
                <c:forEach var="module" items="${mModules.keySet()}">
                    pitch${module} = {...basePitch};
                    pitch${module}.id += "${module}";
                    pitch = pitch${module};
                    drawField('canvasContainer${module}', pitch);

                    <c:forEach var="point" items="${mModules.get(module)}" varStatus="status">
                    pitch.pointsDrawed = 1;
                    drawPoint(${point.x}, ${point.y}, 1, 1, ${status.index + 1});
                    </c:forEach>
                </c:forEach>
                });
            </script>
    </head>
    <body>
        <%@ include file="header.jsp" %>

        <div class="uk-flex uk-padding-left uk-padding-right uk-padding-top uk-width-1-1">
            <c:forEach var="module" items="${mModules.keySet()}">
                <div class="uk-width-1-10 uk-padding-right">
                    <p class="uk-margin-bottom-remove uk-text-center uk-text-bold uk-text-medium">${module}</p>
                    <div id="canvasContainer${module}">

                    </div>
                    <c:forEach var="point" items="${mModules.get(module)}" varStatus="status">
                        <p class="uk-margin-bottom-remove uk-text-center uk-text-medium uk-margin-top-small"><span class="uk-text-bold">${status.index + 1}</span>: ${point.x};${point.y}</p>
                    </c:forEach>
                </div>
            </c:forEach>
        </div>
    </body>
</html>