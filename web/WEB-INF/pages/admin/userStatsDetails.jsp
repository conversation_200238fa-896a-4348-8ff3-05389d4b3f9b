<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <title><spring:message code="theme.title"/></title>
        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
        <script type="text/javascript" src="/sicstv/js/utils.js" ></script>
        <link rel="icon" href="/sicstv/images/favicon.ico"/>
         <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
    </head>
        
    <script>
        jQuery(document).ready(function () {
            $('a.mcPagerLink, th.mcPagerLink a').click(function () {
                var strPageD = $(this).attr('href').split(/\?/)[0];
                var strDataD = $(this).attr('href').split(/\?/)[1];
                $.ajax({
                    type: 'GET',
                    url: strPageD,
                    data: encodeURI(strDataD),
                    success: function (msg) {
                        $("#download").html(msg);
                    }
                });
                return false; // to stop link
            });
            
            $('#downloadTable').DataTable({
                "columnDefs": [
                    {type: 'date-euro', targets: 1},
                    {type: 'date-euro', targets: 4}
                ],
                "paging": true,
                "lengthChange": true,
                "pageLength": 100,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "order": [[1, "desc"]]
                
            });
        });
    </script>

    <body>
        <div id="download">
            <table id="downloadTable" class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th>Video</th>
                            <c:choose>
                                <c:when test="${downloadDetails}">
                                <th>Download date</th>
                                </c:when>
                                <c:otherwise>
                                <th>Streaming date</th>
                                </c:otherwise>
                            </c:choose>

                        <th>Locali</th>
                        <th>Ospiti</th>
                        <th>Data match</th>
                    </tr>
                </thead>
                <tbody>
                    <c:forEach var="item" items="${mStats}">

                        <tr>
                            <td>
                                ${item.video}
                            </td>
                            <td>
                                ${item.getDateString()}
                            </td>
                            <td>
                                ${item.homeTeam}
                            </td>
                            <td>
                                ${item.awayTeam}
                            </td>
                            <td>
                                ${item.getGameDateString()}
                            </td>
                        </tr>
                    </c:forEach>
                </tbody>
            </table>
        </div>

        <%@ include file="footer.jsp" %>
    </body>
</html>


