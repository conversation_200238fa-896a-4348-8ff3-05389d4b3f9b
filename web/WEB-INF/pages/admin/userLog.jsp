<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div style="height: 100%; overflow-y: auto; overflow-x: hidden">
    <span class="modalClose" onclick="closeUserLog();">&times;</span>
    <center>
        Log file: ${mLogFile}
        <input onchange="showUserLog();" id="userLogData" class="ui-state-default ui-corner-all inputSpacer3 uk-float-left" type="text" value="${mLogData}" data-uk-datepicker="{format:'DD/MM/YYYY'}" size="20" style="text-align:center"/>
    </center>
    
    <table class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1">
        <thead>
            <tr>
                <th>Data</th>
                <th>Link</th>
                <th>Utente</th>
            </tr>
        </thead>
        <tbody>
            <c:forEach var="log" items="${mLogs}">
                <tr>
                    <td style="vertical-align: middle;">
                        ${log.getDateString()}
                    </td>
                    <td style="vertical-align: middle;">
                        ${log.link}
                    </td>
                    <td style="vertical-align: middle;">
                        ${log.userFirstName}
                    </td>
                </tr>
            </c:forEach>
        </tbody>
    </table>
    
    <div class="uk-modal-footer uk-text-right uk-width-1-1">
        <button style="min-width:80px;" onclick="closeUserLog();" class="uk-text-large uk-button uk-modal-close"><spring:message code="menu.close"/></button>
    </div>
</div>