<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1"/>
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
            <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
            <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
            <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
            <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
            <title><spring:message code="theme.title"/></title>
            <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
            <link rel="icon" href="/sicstv/images/favicon.ico"/>
            <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>

            <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
            <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

            <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
            <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
            <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>

            <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/tooltip.js"></script>

            <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
            <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
            <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" />

            <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
            <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
            <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>

            <script>
                var userTable;
                $(document).ready(function () {
                    $("#linkAgentPayment").addClass("uk-active");

                    userTable = $('#userTable').DataTable({
                        "dom": 'frtip',
                        "columnDefs": [
                            {
                                "type": "date-euro",
                                "targets": [5, 7, 8],
                                render: function (data, type, row) {
                                    // in questo caso Michele vuole che le date vuote siano le pi� "piccole"
                                    // solitamente al posto di '01/01/2000' c'� '99/99/9999'
                                    if (data === '') {
                                        return type === 'sort' ? '01/01/2000' : data;
                                    }
                                    return data;
                                }
                            }
                        ],
                        "lengthChange": true,
                        "searching": true,
                        "ordering": true,
                        "info": true,
                        "autoWidth": false,
                        "ext.errMode": "none",
                        "fixedHeader": true,
                        "lengthMenu": [20],
                        "pagingType": 'simple',
                        "stateSave": true
                    });

                    reloadTotals();
                    userTable.on("draw.dt", function () {
                        reloadTotals();
                    });
                });

                function reloadTotals() {
                    $("#totalContainer").empty();
                    $("#overdue").remove();

                    var firstTotal = 0, secondTotal = 0, thirdTotal = 0, thirdOverdueTotal = 0, fourthTotal = 0;
                    userTable.rows({filter: 'applied'}).every(function () {
                        var data = this.data();
                        if (data[9]) {
                            firstTotal += parseFloat(data[9]);
                        }
                        if (data[10]) {
                            secondTotal += parseFloat(data[10]);
                        }
                        if (data[11]) {
                            thirdTotal += parseFloat(data[11]);
                            if (data[7]) {
                                const [day, month, year] = data[7].split('/').map(Number);
                                const parsedDate = new Date(year, month - 1, day); // month is 0-indexed
                                const currentDate = new Date();
                                if (parsedDate < currentDate) {
                                    thirdOverdueTotal += parseFloat(data[11]);
                                }
                            }
                        }
                        if (data[12]) {
                            fourthTotal += parseFloat(data[12]);
                        }
                    });
                    firstTotal = Math.round(firstTotal * 100) / 100;
                    secondTotal = Math.round(secondTotal * 100) / 100;
                    thirdTotal = Math.round(thirdTotal * 100) / 100;
                    fourthTotal = Math.round(fourthTotal * 100) / 100;
                    thirdOverdueTotal = Math.round(thirdOverdueTotal * 100) / 100;

                    var firstTotalMargin = 0, secondTotalMargin = 0, thirdTotalMargin = 0, fourthTotalMargin = 0;
                    firstTotalMargin += parseFloat($("tr.odd:first-child").find("td:nth-child(1)").css("width").replace("px", "")) + 20;
                    firstTotalMargin += parseFloat($("tr.odd:first-child").find("td:nth-child(2)").css("width").replace("px", "")) + 20;
                    firstTotalMargin += parseFloat($("tr.odd:first-child").find("td:nth-child(3)").css("width").replace("px", "")) + 20;
                    firstTotalMargin += parseFloat($("tr.odd:first-child").find("td:nth-child(4)").css("width").replace("px", "")) + 20;
                    firstTotalMargin += parseFloat($("tr.odd:first-child").find("td:nth-child(5)").css("width").replace("px", "")) + 20;
                    firstTotalMargin += parseFloat($("tr.odd:first-child").find("td:nth-child(6)").css("width").replace("px", "")) + 20;
                    firstTotalMargin += parseFloat($("tr.odd:first-child").find("td:nth-child(7)").css("width").replace("px", "")) + 20;
                    firstTotalMargin += parseFloat($("tr.odd:first-child").find("td:nth-child(8)").css("width").replace("px", "")) + 20;
                    firstTotalMargin += parseFloat($("tr.odd:first-child").find("td:nth-child(9)").css("width").replace("px", "")) + 20;

//                    secondTotalMargin += parseFloat($("tr.odd:first-child").find("td:nth-child(11)").css("width").replace("px", "")) + 20;

                    var firstTotalWidth = parseFloat($("tr.odd:first-child").find("td:nth-child(10)").css("width").replace("px", "")) + 20;
                    var secondTotalWidth = parseFloat($("tr.odd:first-child").find("td:nth-child(11)").css("width").replace("px", "")) + 20;
                    var thirdTotalWidth = parseFloat($("tr.odd:first-child").find("td:nth-child(12)").css("width").replace("px", "")) + 20;
                    var fourthTotalWidth = parseFloat($("tr.odd:first-child").find("td:nth-child(13)").css("width").replace("px", "")) + 20;

                    $("#totalContainer").append("<div class='center' style='background-color: orange; margin-left: " + firstTotalMargin + "px; width: " + firstTotalWidth + "px'>" + formatNumber(firstTotal) + "</div>");
                    $("#totalContainer").append("<div class='center' style='background-color: orange; margin-left: " + secondTotalMargin + "px; width: " + secondTotalWidth + "px'>" + formatNumber(secondTotal) + "</div>");
                    $("#totalContainer").append("<div class='center' style='background-color: orange; margin-left: " + thirdTotalMargin + "px; width: " + thirdTotalWidth + "px'>" + formatNumber(thirdTotal) + "</div>");
                    $("#totalContainer").append("<div class='center' style='background-color: orange; margin-left: " + fourthTotalMargin + "px; width: " + fourthTotalWidth + "px'>" + formatNumber(fourthTotal) + "</div>");
                    $("#container").append("<div id='overdue' class='center' style='background-color: orange; margin-left: " + (firstTotalMargin + firstTotalWidth + secondTotalWidth) + "px; width: " + thirdTotalWidth + "px'>Overdue: " + formatNumber(thirdOverdueTotal) + "</div>");
                }

                function formatNumber(number) {
                    let roundedNumber = Math.round(number);

                    // Format the number to include thousands separator
                    return roundedNumber.toLocaleString('en-US', {
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 3
                    }).replace(",", ".");
                }
            </script>
    </head>
    <body>
        <%@ include file="header.jsp" %>

        <div id="container" style="padding: 10px">
            <table id="userTable" class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1">
                <thead>
                    <tr>
                        <c:set var="counter" value="${0}"/>
                        <c:forEach var="column" items="${mTableContent.headers}">
                            <th <c:if test="${counter == 1 or counter == 3 or counter == 4 or counter == 5 or counter == 6 or counter == 7}">style="text-align: center"</c:if>>
                                ${column}
                            </th>
                            <c:set var="counter" value="${counter + 1}"/>
                        </c:forEach>
                    </tr>
                </thead>
                <tbody>
                    <c:forEach var="rowNumber" items="${mTableContent.rows.keySet()}">
                        <tr>
                            <c:forEach var="cell" items="${mTableContent.rows.get(rowNumber)}">
                                <td style="<c:if test="${cell.color != null}">background-color: #${cell.color};</c:if><c:if test="${cell.align != null}">text-align: ${cell.align}</c:if>">
                                    ${cell.content}
                                </td>
                            </c:forEach>
                        </tr>
                    </c:forEach>
                </tbody>
            </table>
            <div class="uk-flex" id="totalContainer">

            </div>
        </div>
    </body>
</html>