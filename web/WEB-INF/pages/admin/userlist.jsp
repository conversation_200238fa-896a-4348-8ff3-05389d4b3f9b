<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <title><spring:message code="theme.title"/></title>
        <script src="https://code.jquery.com/jquery-1.12.4.js"></script>
        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
        <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <link rel="icon" href="/sicstv/images/favicon.ico"/>

        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>

    </head>

    <script>

        jQuery(document).ready(function () {
            //$("#example1").DataTable();
            $('#userlistTable').DataTable({
                "paging": true,
                "lengthChange": true,
                "pageLength": 100,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "order": [[1, "asc"]]

            });
            jsPg();
        }
        );

        function jsPg() {
            $('a.mcPagerLink, th.mcPagerLink a').click(function () {
                var Page = $(this).attr('href').split(/\?/)[0];
                var Data = $(this).attr('href').split(/\?/)[1];
                $.ajax({
                    type: 'GET',
                    url: Page,
                    data: encodeURI(Data),
                    success: function (msg) {
                        $("#userList").html(msg);
                    }
                });
                return false; // to stop link
            });
        }

        function jsDetDownload(val, name, surname) {
            //jsLoadingAlpha("#detUserD");
            $.ajax({
                type: "GET",
                url: "/sicstv/admin/userDownload.htm",
                data: encodeURI("formId=" + val),
                cache: false,
                success: function (msg) {
                    //jsLoadingOff("#detUserD");
                    $("#detUserD").html(msg);
                    $("#detUserD").removeClass("uk-hidden");
                    $("#detUserS").addClass("uk-hidden");
                    /*  $("#resultD").dialog({
                     autoOpen: true,
                     closeOnEscape: true,
                     width: "500",
                     height: "auto",
                     modal: true,
                     resizable: false,
                     title: "Dettagli Download " + name + '_' + surname
                     });*/
                }
            });
        }

        function jsDetStreaming(val, name, surname) {
            //jsLoadingAlpha("#detUserS");
            $.ajax({
                type: "GET",
                url: "/sicstv/admin/userStreaming.htm",
                data: encodeURI("formId=" + val),
                cache: false,
                success: function (msg) {
                    jsLoadingOff("#detUserS");
                    $("#detUserS").html(msg);
                    $("#detUserS").removeClass("uk-hidden");
                    $("#detUserD").addClass("uk-hidden");
                    /*   $("#resultS").dialog({
                     autoOpen: true,
                     closeOnEscape: true,
                     width: "500",
                     height: "auto",
                     modal: true,
                     resizable: false,
                     title: "Dettagli Streaming " + name + '_' + surname,
                     });*/
                    return false;
                }

            });
        }

    </script>
    <body>
        <div id="userList">
            <table id="userlistTable" class="table table-bordered table-hover">
                <thead>
                    <tr>
                        <th>Nome</th>
                        <th>Cognome</th>
                        <th>Ruolo</th>
                        <th>Email</th>
                        <th>Nome gruppo</th>
                        <th>Username</th>
                        <th>Numero Download</th>
                        <th>Numero Streaming</th>
                    </tr>
                </thead>
                <tbody>
                    <c:forEach var="item" items="${mUserList}">
                        <tr>
                            <td>
                                ${item.firstName} 
                            </td>
                            <td>
                                ${item.lastName}
                            </td>
                            <td>
                                ${item.role}
                            </td>
                            <td>
                                ${item.email}
                            </td>
                            <td>
                                ${item.groupsetName}
                            </td>
                            <td>
                                ${item.username}
                            </td>
                            <td>
                                <a href="#detUserD" onclick="jsDetDownload(${item.id}, '${item.firstName}', '${item.lastName}');">${item.numDownload}</a>
                            </td>
                            <td>
                                <a href="#detUserS" onclick="jsDetStreaming(${item.id}, '${item.firstName}', '${item.lastName}');">${item.numStreaming}</a>
                            </td>
                        </tr>
                    </c:forEach>
                </tbody>
            </table>

        </div>
        <!-- Dialog dettagli Streaming e Download singolo utente -->
        <div id="resultD" >
            <div id="detUserD"></div>
        </div>	

        <div id="resultS">
            <div id="detUserS"></div>
        </div>	

    </body>
</html>
