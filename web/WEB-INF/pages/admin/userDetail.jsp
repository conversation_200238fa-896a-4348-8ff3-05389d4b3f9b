<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title><spring:message code="theme.title"/></title>
        <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
        <link rel="icon" href="/sicstv/images/favicon.ico"/>
        
        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>
        
        <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        
        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js"></script>
        
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.css?<%=System.currentTimeMillis()%>" />
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        
        <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
        <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
        <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>
        
        <style>
            .block {
                width: 50%;
                float: left;
            }
            
            .container {
                display: flex;
                width: 100%;
            }

            .box3-header {
                width: 33%;
                height: 50%;
                float: left;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            
            .box2-header {
                width: 48%;
                height: 50%;
                float: left;
                justify-content: center;
                align-items: center;
            }
            
            .title {
                background: #f27e46;
                color: #ffffff;
                text-transform: uppercase;
                font-size: 12px;
                height: 20px;
                vertical-align: middle;
                padding: 8px 12px 0px 12px;
                margin: 0;
            }
            
            .dataTables_wrapper, .orangeBorder {
                border-color: #f27e46;
                border-width: 0px 2px 2px 2px;
                border-style: solid solid solid solid;
                padding: 5px 5px 5px 5px;
                overflow-x: auto;
            }
        </style>
        
        <script>
            var tables;
            var compTable;
            // Array to track the ids of the details displayed rows
            var detailRows = [];
            var exportTable;
            
            $(document).ready(function () {                
                tables = $('.uk-table:not(.uk-table-comp,.uk-table-export)').DataTable({
                    "dom": 'frtip',
                    "columnDefs": [
                        { "type": "date-euro", "targets": 0 }
                    ],
                    "lengthChange": true,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "ext.errMode": "none",
                    "order": [[0, "desc"]],
                    "fixedHeader": true,
                    "lengthMenu" : [10],
                    "pagingType": 'simple'
                });
                
                compTable = $('.uk-table-comp').DataTable({
                    "dom": 'frtip',
                    "columnDefs": [
                        { "type": "date-euro", "targets": 0 }
                    ],
                    "lengthChange": true,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "ext.errMode": "none",
                    "order": [[1, "asc"]],
                    "fixedHeader": true,
                    "lengthMenu" : [10],
                    "pagingType": 'simple'
                });
                
                compTable.on('draw.dt', function() {
                    initCompTable();
                });
                initCompTable();
                
                exportTable = $('#exportTable').DataTable({
                    "dom": 'frtip',
                    "columnDefs": [
                        { "type": "date-euro", "targets": 6 }
                    ],
                    "lengthChange": true,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "ext.errMode": "none",
                    "order": [[6, "desc"]],
                    "fixedHeader": true,
                    "lengthMenu" : [10],
                    "pagingType": 'simple'
                });
                
                exportTable.on('draw.dt', function() {
                    initExportTable();
                });
                initExportTable();
            });
            
            function initCompTable() {
                $('#competitionViewsTable tbody tr td.dt-control').click(function () {
                    var tr = $(this).parents('tr');
                    var row = compTable.row(tr);
                    var idx = $.inArray(tr.attr('id'), detailRows);

                    if (row.child.isShown()) {
                        tr.removeClass('details');
                        row.child.hide();

                        // Remove from the 'open' array
                        detailRows.splice(idx, 1);
                    } else {
                        tr.addClass('details');
                        row.child(format(row.data())).show();

                        // Add to the 'open' array
                        if (idx === -1) {
                            detailRows.push(tr.attr('id'));
                        }
                    }
                });
            }
            
            function initExportTable() {
                $('#exportTable tbody tr td.dt-control').click(function () {
                    var tr = $(this).parents('tr');
                    var row = exportTable.row(tr);
                    var idx = $.inArray(tr.attr('id'), detailRows);

                    if (row.child.isShown()) {
                        tr.removeClass('details');
                        row.child.hide();

                        // Remove from the 'open' array
                        detailRows.splice(idx, 1);
                    } else {
                        tr.addClass('details');
                        row.child(formatExport(row.data())).show();

                        // Add to the 'open' array
                        if (idx === -1) {
                            detailRows.push(tr.attr('id'));
                        }
                    }
                });
            }
            
            function format(d) {
                var rows = d[d.length - 1];
                var result = "<table><thead><tr><th style='min-width: 350px'><spring:message code="menu.admin.partita"/></th><th><spring:message code="menu.admin.numero.visualizzazioni"/></th></tr></thead><tbody>";
                var splitted = rows.split("||");
                splitted.forEach(function (item, index) {
                    item = item.trim();
                    if (item.includes(";")) {
                        var game = item.split(";")[0];
                        var views = item.split(";")[1];
                        
                        result += "<tr>";
                        result += "<td>" + game + "</td>";
                        result += "<td>" + views + "</td>";
                        result += "<tr>";
                    }
                });
                result += "</tbody></table>";
                return result;
            }
            
            function formatExport(d) {
                var rows = d[d.length - 1];
                var result = "<table><thead><tr><th style='min-width: 350px'><spring:message code="menu.user.export.tipologia"/></th><th><spring:message code="menu.user.export.stato"/></th><th><spring:message code="menu.user.export.inizio.clip"/></th><th><spring:message code="menu.user.export.durata.clip"/></th><th><spring:message code="menu.user.export.tempo.esecuzione"/></th></tr></thead><tbody>";
                var splitted = rows.split("||");
                splitted.forEach(function (item, index) {
                    item = item.trim();
                    if (item.includes(";")) {
                        var type = item.split(";")[0];
                        var status = item.split(";")[1];
                        var start = item.split(";")[2];
                        var duration = item.split(";")[3];
                        var execution = item.split(";")[4];
                        
                        result += "<tr>";
                        result += "<td>" + type + "</td>";
                        result += "<td>" + status + "</td>";
                        result += "<td>" + start + "</td>";
                        result += "<td>" + duration + "</td>";
                        result += "<td>" + execution + "</td>";
                        result += "<tr>";
                    }
                });
                result += "</tbody></table>";
                return result;
            }
            
            function showUserLog() {
                $("#modalUserLog").addClass("uk-open");
                $("#modalUserLog").removeClass("uk-hidden");
                
                var date;
                if ($("#userLogData").length > 0) {
                    var dateVal = $("#userLogData").val();
                    var splitted = dateVal.split("/");
                    date = new Date(splitted[1] + "/" + splitted[0] + "/" + splitted[2]).getTime();
                }
                
                var data = "userId=${mUserSearched.id}";
                if (typeof date !== 'undefined') {
                    data += "&date=" + date;
                }
                
                $.ajax({
                    type: "GET",
                    url: "/sicstv/admin/userLog.htm",
                    cache: false,
                    data: encodeURI(data),
                    success: function (content) {
                        $("#modalUserLogContainer > div").remove();
                        $("#modalUserLogContainer").append(content);
                    },
                    error: function () {
                        UIkit.notify("<spring:message code="team.stats.update.error"/>", {status: 'danger', timeout: 1000});
                    }
                });
            }
            
            function closeUserLog() {
                $("#modalUserLog").removeClass("uk-open");
                $("#modalUserLog").addClass("uk-hidden");
            }
        </script>
        
    </head>
    <body>
        <%@ include file="header.jsp" %>
        <div style="margin: 10px 10px 0px 10px">
            <div class="block">
                <!-- User Data -->
                <center><p class="title" style="text-transform: uppercase"><spring:message code="menu.admin.dati.utente"/></p></center>
                <div class="orangeBorder center-vertically" style="display: flex">
                    <div class="box3-header">
                        <center>
                            <p>
                                <strong><spring:message code="menu.admin.nome.cognome"/></strong>
                                <br/>
                                <c:if test="${mUser.groupsetId == 2}">
                                    <a onclick="showUserLog();">${mUserSearched.firstName} ${mUserSearched.lastName}</a>
                                </c:if>
                                <c:if test="${mUser.groupsetId != 2}">
                                    ${mUserSearched.firstName} ${mUserSearched.lastName}
                                </c:if>
                            </p>
                        </center>
                    </div>
                    <div class="box3-header">
                        <center>
                            <p>
                                <strong>Email</strong>
                                <br/>
                                ${mUserSearched.email}
                            </p>
                        </center>
                    </div>
                    <div class="box3-header">
                        <center>
                            <p>
                                <strong><spring:message code="menu.admin.gruppo.ruolo"/></strong>
                                <br/>
                                ${mUserSearched.groupsetName} (${mUserSearched.groupsetId})
                                <br/>
                                <span style="font-size: 10px">${mUserSearched.role}</span>
                            </p>
                        </center>
                    </div>
                    <div class="box3-header">
                        <center>
                            <p>
                                <strong><spring:message code="menu.admin.data.scadenza.sicstv"/></strong>
                                <br/>
                                ${mUserSearched.getDatascadenzaString()}
                            </p>
                        </center>
                    </div>
                </div>
                <div>
                    <center><p class="title"><spring:message code="menu.admin.ultime.100.azioni"/></p></center>
                    <table class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1">
                        <thead>
                            <tr>
                                <th><spring:message code="menu.admin.data"/></th>
                                <th><spring:message code="menu.admin.azione"/></th>
                                <th><spring:message code="menu.admin.descrizione"/></th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="data" items="${mLast100Data}">
                                <tr>
                                    <td style="vertical-align: middle;">
                                        ${data.getDateStringWithTime()}
                                    </td>
                                    <td style="vertical-align: middle;">
                                        <c:if test="${data.action.equals('SESSION_DESTROYED')}">
                                            SESSION_CLOSED
                                        </c:if>
                                        <c:if test="${!data.action.equals('SESSION_DESTROYED')}">
                                            ${data.action}
                                        </c:if>
                                    </td>
                                    <td style="vertical-align: middle;">
                                        ${data.videoId}
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>
                        
            <div class="block">
                <div style="margin-left: 2%">
                    <center><p class="title" style="text-transform: uppercase"><spring:message code="menu.admin.esportazioni"/> (${totExportUsed} / ${totExportMinutes} min.)</p></center>
                    <table class="uk-table uk-table-export uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1" id="exportTable">
                        <thead>
                            <tr>
                                <th style="width: 20px;"></th>
                                <th><spring:message code="menu.user.export.nome"/></th>
                                <th><spring:message code="menu.user.export.tipologia"/></th>
                                <th><spring:message code="menu.user.export.stato"/></th>
                                <th><spring:message code="menu.user.export.numero.clip"/></th>
                                <th><spring:message code="menu.user.export.durata.totale"/></th>
                                <th><spring:message code="menu.user.export.data.richiesta"/></th>
                                <th class="uk-hidden">Detail</th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="row" items="${mExports.keySet()}">
                                <tr>
                                    <td class="dt-control"></td>
                                    <td>${row.name}</td>
                                    <td>${row.type == 1 ? 'Video' : 'ZIP'}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${row.statusCode == 200}">
                                                <spring:message code="menu.user.export.completata"/>
                                            </c:when>
                                            <c:otherwise>
                                                <spring:message code="menu.user.export.in.errore"/>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>${row.clipAmount}</td>
                                    <td>${row.getTotalVideoDurationFormatted()}</td>
                                    <td>${row.getStartTimeString()}</td>
                                    <td class="uk-hidden">
                                        <c:forEach var="detail" items="${mExports.get(row)}">
                                            <c:choose>
                                                <c:when test="${detail.type == 2}">
                                                    <c:set var="type" value="Zip"/>
                                                </c:when>
                                                <c:when test="${detail.type == 3}">
                                                    <c:set var="type" value="Video"/>
                                                </c:when>
                                                <c:otherwise>
                                                    <c:set var="type" value="Clip"/>
                                                </c:otherwise>
                                            </c:choose>
                                            ${type};${detail.statusCode == 200 ? mCompletataLabel : mErroreLabel};${detail.getClipStartString()};${detail.getClipDurationString()};${detail.getExecutionTimeString()}||
                                        </c:forEach>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>

            <div style="margin-top: 5px; float: left; width: 100%">
                <div class="block">
                    <div>
                        <center><p class="title" style="text-transform: uppercase">VIDEO STREAM - <spring:message code="menu.admin.dettaglio"/></p></center>
                        <div style="width: 100%; float: right;">
                            <table class="uk-table uk-table-comp uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1" id="competitionViewsTable">
                                <thead>
                                    <tr>
                                        <th style="width: 20px;"></th>
                                        <th><spring:message code="menu.admin.competizione"/></th>
                                        <th><spring:message code="menu.admin.numero.visualizzazioni"/></th>
                                        <th class="uk-hidden">Detail</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <c:forEach var="row" items="${mCompetitionRows}">
                                        <tr>
                                            <td class="dt-control"></td>
                                            <td>${row.key}</td>
                                            <td>${row.value}</td>
                                            <td class="uk-hidden">
                                                <c:forEach var="game" items="${mCompetitionDetails.get(row.key)}">
                                                    ${game.homeTeam} - ${game.awayTeam} (${game.idFixture});${game.counter}||
                                                </c:forEach>
                                            </td>
                                        </tr>
                                    </c:forEach>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="block">
                    <div class="box2-header" style="margin-left: 2%">
                        <center><p class="title">LOGIN / LOGOUT</p></center>
                        <table class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1">
                            <thead>
                                <tr>
                                    <th><spring:message code="menu.admin.data"/></th>
                                    <th><spring:message code="menu.admin.azione"/></th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="data" items="${mLoginLogoutData}">
                                    <tr>
                                        <td style="vertical-align: middle;">
                                            ${data.getDateStringWithTime()}
                                        </td>
                                        <td style="vertical-align: middle;">
                                            ${data.action}
                                        </td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                    <div class="box2-header" style="margin-left: 2%">
                        <center><p class="title" style="text-transform: uppercase">DOWNLOAD <spring:message code="menu.admin.partite"/></p></center>
                        <table class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1">
                            <thead>
                                <tr>
                                    <th><spring:message code="menu.admin.data"/></th>
                                    <th><spring:message code="menu.admin.partita"/></th>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="data" items="${mVideoDownloadData}">
                                    <tr>
                                        <td style="vertical-align: middle;">
                                            ${data.getDateStringWithTime()}
                                        </td>
                                        <td style="vertical-align: middle;">
                                            ${data.videoId}
                                        </td>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div id="modalUserLog" class="uk-modal uk-hidden" aria-hidden="false" style="display: block; overflow-y: auto;">
            <div id="modalUserLogContainer" class="uk-modal-dialog" style="min-height: 0; min-width: 0; max-width: 50%; width: auto; height: 75%;">
                
            </div>
        </div>
    </body>
</html>