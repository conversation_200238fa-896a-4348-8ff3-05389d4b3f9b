<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1"/>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title><spring:message code="theme.title"/></title>
        <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
        <link rel="icon" href="/sicstv/images/favicon.ico"/>
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        
        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>
        
        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>
        
        <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
        <script src="/sicstv/uikit/js/components/tooltip.js"></script>
        
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >
        
        <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
        <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
        <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>

        <style>
            .chart {
                width: 50%;
                height: 400px;
                max-height: 500px;
                overflow-y: auto;
            }
            
            .minichart {
                height: 400px;
                max-height: 400px;
                overflow-y: auto;
                overflow-x: hidden;
            }
        </style>
        
        <script>
            var userTable;
            $(document).ready(function () {
                $("#linkDashboard").addClass("uk-active");
                $.unblockUI();
                
                userTable = $('#userTable').DataTable({
                    "dom": 'frtip',
                    "columnDefs": [
                        {
                            "type": "date-euro",
                            "targets": [3, 6],
                            render: function (data, type, row) {
                                // Se il dato è vuoto, restituisci una data molto grande per i valori "più alti"
                                if (data === '') {
                                    return type === 'sort' ? '99/99/9999' : data;
                                }
                                return data;
                            }
                        },
                        { "width": "10%", "targets": [4, 5] }
                    ],
                    "lengthChange": true,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "ext.errMode": "none",
                    "order": [[6, "desc"]],
                    "fixedHeader": true,
                    "lengthMenu" : [10],
                    "pagingType": 'simple'
                });
            });

            am4core.ready(function() {                
                // Themes begin
                am4core.useTheme(am4themes_animated);
                // Themes end

            /*
             *      SESSIONI ATTIVE - BEGIN
             */
//            $.ajax({
//                type: "GET",
//                url: "/sicstv/admin/homeActiveSession.htm",
//                cache: false,
//                success: function (data) {
//                    var chartActiveSession = am4core.create("activeSessionDiv", am4charts.XYChart);
//                    chartActiveSession.data = JSON.parse(data);
//                    chartActiveSession.padding(40, 40, 40, 40);
//                    
//                    var chartTitleActiveSession = chartActiveSession.titles.create();
//                    chartTitleActiveSession.text = "Sessioni Attive";
//                    chartTitleActiveSession.fontSize = 25;
//
//                    var categoryAxisActiveSession = chartActiveSession.xAxes.push(new am4charts.CategoryAxis());
//                    categoryAxisActiveSession.renderer.grid.template.location = 0;
//                    categoryAxisActiveSession.dataFields.category = "user";
//                    categoryAxisActiveSession.renderer.minGridDistance = 60;
//                    categoryAxisActiveSession.renderer.inversed = true;
//                    categoryAxisActiveSession.renderer.grid.template.disabled = true;
//                    categoryAxisActiveSession.tooltip.disabled = true;
//
//                    var valueAxisActiveSession = chartActiveSession.yAxes.push(new am4charts.ValueAxis());
//                    valueAxisActiveSession.min = 0;
//                    valueAxisActiveSession.extraMax = 0.1;
//                    valueAxisActiveSession.tooltip.disabled = true;
//                    //valueAxis.rangeChangeEasing = am4core.ease.linear;
//                    //valueAxis.rangeChangeDuration = 1500;
//
//                    var seriesActiveSession = chartActiveSession.series.push(new am4charts.ColumnSeries());
//                    seriesActiveSession.dataFields.categoryX = "user";
//                    seriesActiveSession.dataFields.valueY = "value";
//                    seriesActiveSession.columns.template.strokeOpacity = 0;
//                    seriesActiveSession.columns.template.column.cornerRadiusTopRight = 10;
//                    seriesActiveSession.columns.template.column.cornerRadiusTopLeft = 10;
//                    //series.interpolationDuration = 1500;
//                    //series.interpolationEasing = am4core.ease.linear;
//                    var labelBulletActiveSession = seriesActiveSession.bullets.push(new am4charts.LabelBullet());
//                    labelBulletActiveSession.label.verticalCenter = "bottom";
//                    labelBulletActiveSession.label.dy = -10;
//                    labelBulletActiveSession.label.text = "{values.valueY.workingValue.formatNumber('#.')}";
//
//                    // senza questo il tooltip non va (non so perchè)
//                    chartActiveSession.cursor = new am4charts.XYCursor();
//                    chartActiveSession.cursor.lineY.disabled = true;
//                    chartActiveSession.cursor.lineX.disabled = true;
//
//                    // as by default columns of the same series are of the same color, we add adapter which takes colors from chart.colors color set
//                    seriesActiveSession.columns.template.adapter.add("fill", function (fill, target) {
//                        return chartActiveSession.colors.getIndex(target.dataItem.index);
//                    });
//
//                    setInterval(function () {
//                        $.ajax({
//                            type: "GET",
//                            url: "/sicstv/admin/homeActiveSession.htm",
//                            cache: false,
//                            success: function (data) {
//                                chartActiveSession.data = JSON.parse(data);
//                                chartActiveSession.invalidateRawData();
//                                
//                                $.ajax({
//                                    type: "GET",
//                                    url: "/sicstv/admin/homeActiveSessionPage.htm",
//                                    cache: false,
//                                    success: function (page) {
//                                        $("#activeSessionPageDiv").html(page);
//                                    },
//                                    error: function () {
//                                        UIkit.notify("Errore nella ri-lettura dei dati. Rotta 'homeActiveSessionPage.htm'", {status: 'danger', timeout: 2500});
//                                    }
//                                });
//                            },
//                            error: function () {
//                                UIkit.notify("Errore nella ri-lettura dei dati. Rotta 'homeStartExport.htm'", {status: 'danger', timeout: 2500});
//                            }
//                        });
//                    }, 10000);
//
//                    categoryAxisActiveSession.sortBySeries = seriesActiveSession;
//                },
//                error: function () {
//                    UIkit.notify("Errore nella lettura dei dati. Rotta 'homeStartExport.htm'", {status: 'danger', timeout: 2500});
//                }
//            });
//            
                $.ajax({
                    type: "GET",
                    url: "/sicstv/admin/homeActiveSessionPage.htm",
                    cache: false,
                    success: function (page) {
                        $("#activeSessionPageDiv").html(page);

                        setInterval(function () {
                            $.ajax({
                                type: "GET",
                                url: "/sicstv/admin/homeActiveSessionPage.htm",
                                cache: false,
                                success: function (page) {
                                    $("#activeSessionPageDiv").html(page);
                                },
                                error: function () {
                                    UIkit.notify("Errore nella ri-lettura dei dati. Rotta 'homeActiveSessionPage.htm'", {status: 'danger', timeout: 2500});
                                }
                            });
                        }, 5000);
                    },
                    error: function () {
                        UIkit.notify("Errore nella lettura dei dati. Rotta 'homeActiveSessionPage.htm'", {status: 'danger', timeout: 2500});
                    }
                });

                /*
                 *      SESSIONI ATTIVE - END
                 */

                /*
                 *      START_EXPORT - BEGIN
                 */
                $.ajax({
                    type: "GET",
                    url: "/sicstv/admin/homeStartExport.htm",
                    data: encodeURI("timeFilter=" + $("#timeFilter").val() + "&agentId=" + $("#agentFilter").val()),
                    cache: false,
                    success: function (data) {
                        var chartStartExport = am4core.create("startExportDiv", am4charts.XYChart);
                        chartStartExport.data = JSON.parse(data);
                        chartStartExport.padding(40, 40, 40, 40);

                        var chartTitleStartExport = chartStartExport.titles.create();
                        chartTitleStartExport.text = "<spring:message code="menu.admin.richieste.esportazioni"/>";
                        chartTitleStartExport.fontSize = 25;

                        var categoryAxisStartExport = chartStartExport.xAxes.push(new am4charts.CategoryAxis());
                        categoryAxisStartExport.renderer.grid.template.location = 0;
                        categoryAxisStartExport.dataFields.category = "user";
                        categoryAxisStartExport.renderer.minGridDistance = 60;
                        categoryAxisStartExport.renderer.inversed = true;
                        categoryAxisStartExport.renderer.grid.template.disabled = true;
                        categoryAxisStartExport.tooltip.disabled = true;

                        var valueAxisStartExport = chartStartExport.yAxes.push(new am4charts.ValueAxis());
                        valueAxisStartExport.min = 0;
                        valueAxisStartExport.extraMax = 0.1;
                        valueAxisStartExport.tooltip.disabled = true;
                        //valueAxis.rangeChangeEasing = am4core.ease.linear;
                        //valueAxis.rangeChangeDuration = 1500;

                        var seriesStartExport = chartStartExport.series.push(new am4charts.ColumnSeries());
                        seriesStartExport.dataFields.categoryX = "user";
                        seriesStartExport.dataFields.valueY = "value";
                        seriesStartExport.tooltipText = "{tooltip}";
                        // seriesStartExport.tooltip.dy = +25;
                        seriesStartExport.columns.template.strokeOpacity = 0;
                        seriesStartExport.columns.template.column.cornerRadiusTopRight = 10;
                        seriesStartExport.columns.template.column.cornerRadiusTopLeft = 10;
                        //series.interpolationDuration = 1500;
                        //series.interpolationEasing = am4core.ease.linear;
                        var labelBulletStartExport = seriesStartExport.bullets.push(new am4charts.LabelBullet());
                        labelBulletStartExport.label.verticalCenter = "bottom";
                        labelBulletStartExport.label.dy = -10;
                        labelBulletStartExport.label.text = "{values.valueY.workingValue.formatNumber('#.')}";

                        // senza questo il tooltip non va (non so perchè)
                        chartStartExport.cursor = new am4charts.XYCursor();
                        chartStartExport.cursor.lineY.disabled = true;
                        chartStartExport.cursor.lineX.disabled = true;

                        // as by default columns of the same series are of the same color, we add adapter which takes colors from chart.colors color set
                        seriesStartExport.columns.template.adapter.add("fill", function (fill, target) {
                            return chartStartExport.colors.getIndex(target.dataItem.index);
                        });

                        categoryAxisStartExport.renderer.labels.template.events.on("over", function(ev) {
                            var point = categoryAxisStartExport.categoryToPoint(ev.target.dataItem.category);
                            chartStartExport.cursor.triggerMove(point, "soft");
                        });

                        categoryAxisStartExport.renderer.labels.template.events.on("out", function(ev) {
                            var point = categoryAxisStartExport.categoryToPoint(ev.target.dataItem.category);
                            chartStartExport.cursor.triggerMove(point, "none");
                        });

                        setInterval(function () {
                            $.ajax({
                                type: "GET",
                                url: "/sicstv/admin/homeStartExport.htm",
                                data: encodeURI("timeFilter=" + $("#timeFilter").val() + "&agentId=" + $("#agentFilter").val()),
                                cache: false,
                                success: function (data) {
                                    chartStartExport.data = JSON.parse(data);
                                    chartStartExport.invalidateRawData();
                                },
                                error: function () {
                                    UIkit.notify("Errore nella ri-lettura dei dati. Rotta 'homeStartExport.htm'", {status: 'danger', timeout: 2500});
                                }
                            });
                        }, 60000); // ogni minuto

                        categoryAxisStartExport.sortBySeries = seriesStartExport;
                    },
                    error: function () {
                        UIkit.notify("Errore nella lettura dei dati. Rotta 'homeStartExport.htm'", {status: 'danger', timeout: 2500});
                    }
                });
                /*
                 *      START_EXPORT - END
                 */

                /*
                 *      LICENSE_EXPIRED - BEGIN
                 */
                $.ajax({
                    type: "GET",
                    url: "/sicstv/admin/homeLicenseExpired.htm",
                    data: encodeURI("timeFilter=" + $("#timeFilter").val() + "&agentId=" + $("#agentFilter").val()),
                    cache: false,
                    success: function (data) {
                        var chartLicenseExpired = am4core.create("licenseExpiredDiv", am4charts.XYChart);
                        chartLicenseExpired.data = JSON.parse(data);
                        chartLicenseExpired.padding(40, 40, 40, 40);

                        var chartTitleLicenseExpired = chartLicenseExpired.titles.create();
                        chartTitleLicenseExpired.text = "<spring:message code="menu.admin.login.licenza.scaduta"/>";
                        chartTitleLicenseExpired.fontSize = 25;

                        var categoryAxisLicenseExpired = chartLicenseExpired.xAxes.push(new am4charts.CategoryAxis());
                        categoryAxisLicenseExpired.renderer.grid.template.location = 0;
                        categoryAxisLicenseExpired.dataFields.category = "user";
                        categoryAxisLicenseExpired.renderer.minGridDistance = 60;
                        categoryAxisLicenseExpired.renderer.inversed = true;
                        categoryAxisLicenseExpired.renderer.grid.template.disabled = true;
                        categoryAxisLicenseExpired.tooltip.disabled = true;

                        var valueAxisLicenseExpired = chartLicenseExpired.yAxes.push(new am4charts.ValueAxis());
                        valueAxisLicenseExpired.min = 0;
                        valueAxisLicenseExpired.extraMax = 0.1;
                        valueAxisLicenseExpired.tooltip.disabled = true;
                        //valueAxis.rangeChangeEasing = am4core.ease.linear;
                        //valueAxis.rangeChangeDuration = 1500;

                        var seriesLicenseExpired = chartLicenseExpired.series.push(new am4charts.ColumnSeries());
                        seriesLicenseExpired.dataFields.categoryX = "user";
                        seriesLicenseExpired.dataFields.valueY = "value";
                        seriesLicenseExpired.tooltipText = "{tooltip}";
                        seriesLicenseExpired.columns.template.strokeOpacity = 0;
                        seriesLicenseExpired.columns.template.column.cornerRadiusTopRight = 10;
                        seriesLicenseExpired.columns.template.column.cornerRadiusTopLeft = 10;
                        //series.interpolationDuration = 1500;
                        //series.interpolationEasing = am4core.ease.linear;
                        var labelBulletLicenseExpired = seriesLicenseExpired.bullets.push(new am4charts.LabelBullet());
                        labelBulletLicenseExpired.label.verticalCenter = "bottom";
                        labelBulletLicenseExpired.label.dy = -10;
                        labelBulletLicenseExpired.label.text = "{values.valueY.workingValue.formatNumber('#.')}";

                        // senza questo il tooltip non va (non so perchè)
                        chartLicenseExpired.cursor = new am4charts.XYCursor();
                        chartLicenseExpired.cursor.lineY.disabled = true;
                        chartLicenseExpired.cursor.lineX.disabled = true;

                        // as by default columns of the same series are of the same color, we add adapter which takes colors from chart.colors color set
                        seriesLicenseExpired.columns.template.adapter.add("fill", function (fill, target) {
                            return chartLicenseExpired.colors.getIndex(target.dataItem.index);
                        });

                        categoryAxisLicenseExpired.renderer.labels.template.events.on("over", function(ev) {
                            var point = categoryAxisLicenseExpired.categoryToPoint(ev.target.dataItem.category);
                            chartLicenseExpired.cursor.triggerMove(point, "soft");
                        });

                        categoryAxisLicenseExpired.renderer.labels.template.events.on("out", function(ev) {
                            var point = categoryAxisLicenseExpired.categoryToPoint(ev.target.dataItem.category);
                            chartLicenseExpired.cursor.triggerMove(point, "none");
                        });

                        setInterval(function () {
                            $.ajax({
                                type: "GET",
                                url: "/sicstv/admin/homeLicenseExpired.htm",
                                data: encodeURI("timeFilter=" + $("#timeFilter").val() + "&agentId=" + $("#agentFilter").val()),
                                cache: false,
                                success: function (data) {
                                    chartLicenseExpired.data = JSON.parse(data);
                                    chartLicenseExpired.invalidateRawData();
                                },
                                error: function () {
                                    UIkit.notify("Errore nella ri-lettura dei dati. Rotta 'homeLicenseExpired.htm'", {status: 'danger', timeout: 2500});
                                }
                            });
                        }, 300000); // ogni 5 minuti

                        categoryAxisLicenseExpired.sortBySeries = seriesLicenseExpired;
                    },
                    error: function () {
                        UIkit.notify("Errore nella lettura dei dati. Rotta 'homeLicenseExpired.htm'", {status: 'danger', timeout: 2500});
                    }
                });
                /*
                 *      LICENSE_EXPIRED - END
                 */

                /*
                 *      VIDEO_DOWNLOAD - BEGIN
                 */
                $.ajax({
                    type: "GET",
                    url: "/sicstv/admin/homeVideoDownload.htm",
                    data: encodeURI("timeFilter=" + $("#timeFilter").val() + "&agentId=" + $("#agentFilter").val()),
                    cache: false,
                    success: function (data) {
                        var chartVideoDownload = am4core.create("videoDownloadDiv", am4charts.XYChart);
                        chartVideoDownload.data = JSON.parse(data);
                        chartVideoDownload.padding(40, 40, 40, 40);

                        var chartTitleVideoDownload = chartVideoDownload.titles.create();
                        chartTitleVideoDownload.text = "<spring:message code="menu.admin.richieste.download.partita"/>";
                        chartTitleVideoDownload.fontSize = 25;

                        var categoryAxisVideoDownload = chartVideoDownload.xAxes.push(new am4charts.CategoryAxis());
                        categoryAxisVideoDownload.renderer.grid.template.location = 0;
                        categoryAxisVideoDownload.dataFields.category = "user";
                        categoryAxisVideoDownload.renderer.minGridDistance = 60;
                        categoryAxisVideoDownload.renderer.inversed = true;
                        categoryAxisVideoDownload.renderer.grid.template.disabled = true;
                        categoryAxisVideoDownload.tooltip.disabled = true;

                        var valueAxisVideoDownload = chartVideoDownload.yAxes.push(new am4charts.ValueAxis());
                        valueAxisVideoDownload.min = 0;
                        valueAxisVideoDownload.extraMax = 0.1;
                        valueAxisVideoDownload.tooltip.disabled = true;
                        //valueAxis.rangeChangeEasing = am4core.ease.linear;
                        //valueAxis.rangeChangeDuration = 1500;

                        var seriesVideoDownload = chartVideoDownload.series.push(new am4charts.ColumnSeries());
                        seriesVideoDownload.dataFields.categoryX = "user";
                        seriesVideoDownload.dataFields.valueY = "value";
                        seriesVideoDownload.tooltipText = "{tooltip}";
                        seriesVideoDownload.columns.template.strokeOpacity = 0;
                        seriesVideoDownload.columns.template.column.cornerRadiusTopRight = 10;
                        seriesVideoDownload.columns.template.column.cornerRadiusTopLeft = 10;
                        //series.interpolationDuration = 1500;
                        //series.interpolationEasing = am4core.ease.linear;
                        var labelBulletVideoDownload = seriesVideoDownload.bullets.push(new am4charts.LabelBullet());
                        labelBulletVideoDownload.label.verticalCenter = "bottom";
                        labelBulletVideoDownload.label.dy = -10;
                        labelBulletVideoDownload.label.text = "{values.valueY.workingValue.formatNumber('#.')}";

                        // senza questo il tooltip non va (non so perchè)
                        chartVideoDownload.cursor = new am4charts.XYCursor();
                        chartVideoDownload.cursor.lineY.disabled = true;
                        chartVideoDownload.cursor.lineX.disabled = true;

                        // as by default columns of the same series are of the same color, we add adapter which takes colors from chart.colors color set
                        seriesVideoDownload.columns.template.adapter.add("fill", function (fill, target) {
                            return chartVideoDownload.colors.getIndex(target.dataItem.index);
                        });

                        categoryAxisVideoDownload.renderer.labels.template.events.on("over", function(ev) {
                            var point = categoryAxisVideoDownload.categoryToPoint(ev.target.dataItem.category);
                            chartVideoDownload.cursor.triggerMove(point, "soft");
                        });

                        categoryAxisVideoDownload.renderer.labels.template.events.on("out", function(ev) {
                            var point = categoryAxisVideoDownload.categoryToPoint(ev.target.dataItem.category);
                            chartVideoDownload.cursor.triggerMove(point, "none");
                        });

                        setInterval(function () {
                            $.ajax({
                                type: "GET",
                                url: "/sicstv/admin/homeVideoDownload.htm",
                                data: encodeURI("timeFilter=" + $("#timeFilter").val() + "&agentId=" + $("#agentFilter").val()),
                                cache: false,
                                success: function (data) {
                                    chartVideoDownload.data = JSON.parse(data);
                                    chartVideoDownload.invalidateRawData();
                                },
                                error: function () {
                                    UIkit.notify("Errore nella ri-lettura dei dati. Rotta 'homeVideoDownload.htm'", {status: 'danger', timeout: 2500});
                                }
                            });
                        }, 60000); // ogni minuto

                        categoryAxisVideoDownload.sortBySeries = seriesVideoDownload;
                    },
                    error: function () {
                        UIkit.notify("Errore nella lettura dei dati. Rotta 'homeVideoDownload.htm'", {status: 'danger', timeout: 2500});
                    }
                });
                /*
                 *      VIDEO_DOWNLOAD - END
                 */

            }); // end am4core.ready()

            function changeTimeFilter() {
                var currentTimeFiler = new URL(document.URL).searchParams.get("timeFilter");
                if (typeof currentTimeFiler === 'undefined' || currentTimeFiler === null) {
                    window.location.replace(document.URL + "?timeFilter=" + $("#timeFilter").val());
                } else {
                    window.location.replace(document.URL.replace("?timeFilter=" + currentTimeFiler, "?timeFilter=" + $("#timeFilter").val()));
                }
            }
            
            function changeAgentFilter() {
                var currentAgentFilter = new URL(document.URL).searchParams.get("agentFilter");
                if (typeof currentAgentFilter === 'undefined' || currentAgentFilter === null) {
                    window.location.replace(document.URL + "&agentFilter=" + $("#agentFilter").val());
                } else {
                    window.location.replace(document.URL.replace("&agentFilter=" + currentAgentFilter, "&agentFilter=" + $("#agentFilter").val()));
                }
            }
            
            function openUserDetail(userId) {
                window.location.href = "/sicstv/admin/userDetail.htm?userId=" + userId + "&from=${mFromDate}&to=${mToDate}";
            }
        </script>
    </head>
    <body>
        <%@ include file="header.jsp" %>
        <c:if test="${mUser.groupsetId == 412 && !mUser.isUserAdmin()}">
            <input type="hidden" value="${mAgentFilter}" id="agentFilter"/>
        </c:if>
        
        <div style="padding-left: 2px; padding-right: 2px;">
            <div style="width: 100%; height: 30px;">
                <span class="uk-form" style="float: right; padding-right: 2%">
                    <spring:message code="menu.admin.filtro.temporale"/>: 
                    <select id="timeFilter" onchange="changeTimeFilter();">
                        <option value="1" <c:if test="${mTimeFilter != null && mTimeFilter == 1}">selected</c:if>><spring:message code="menu.admin.filtro.temporale.1"/></option>
                        <option value="2" <c:if test="${mTimeFilter != null && mTimeFilter == 2}">selected</c:if>><spring:message code="menu.admin.filtro.temporale.2"/></option>
                        <option value="3" <c:if test="${mTimeFilter != null && mTimeFilter == 3}">selected</c:if>><spring:message code="menu.admin.filtro.temporale.3"/></option>
                        <option value="4" <c:if test="${mTimeFilter != null && mTimeFilter == 4}">selected</c:if>><spring:message code="menu.admin.filtro.temporale.4"/></option>
                    </select>
                </span>
                <c:if test="${mUser.groupsetId != 412 || mUser.isUserAdmin()}">
                    <span class="uk-form" style="float: right; padding-right: 2%">
                        <spring:message code="menu.admin.agente"/>: 
                        <select id="agentFilter" onchange="changeAgentFilter();">
                            <c:forEach var="agent" items="${mVtigerAgents}">
                                <option value="${agent.id}" <c:if test="${mAgentFilter != null && mAgentFilter == agent.id}">selected</c:if>>${agent.firstName} ${agent.lastName}</option>
                            </c:forEach>
                        </select>
                    </span>
                </c:if>
            </div>
            <div style="height: 400px">
                <div id="activeSessionDiv" class="minichart" style="float: left; width: 50%">
                    <table id="userTable" class="uk-table uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1">
                        <thead>
                            <tr>
                                <th><spring:message code="menu.admin.utente"/></th>
                                <th><spring:message code="menu.admin.gruppo"/></th>
                                <th><spring:message code="menu.admin.organizzazione"/></th>
                                <th><spring:message code="menu.admin.scadenza"/></th>
                                <th><spring:message code="menu.admin.login.fatti"/></th>
                                <th><spring:message code="menu.admin.azioni.fatte"/></th>
                                <th><spring:message code="menu.admin.data.ultimo.login"/></th>
                            </tr>
                        </thead>
                        <tbody>
                            <c:forEach var="user" items="${mAllUser}">
                                <tr <c:if test="${user.getAccessoScaduto()}">style="background-color: #ff9696 !important"</c:if>>
                                    <td style="vertical-align: middle;">
                                        <a onclick="openUserDetail(${user.id})">${user.firstName} ${user.lastName}</a>
                                    </td>
                                    <td style="vertical-align: middle;">
                                        ${user.groupsetName}
                                    </td>
                                    <td style="vertical-align: middle;">
                                        ${user.vtigerOrganization}
                                    </td>
                                    <td style="vertical-align: middle;">
                                        <c:if test="${user.getAccessoScaduto()}">
                                            <strong>${user.getDatascadenzaString()}</strong>
                                        </c:if>
                                        <c:if test="${!user.getAccessoScaduto()}">
                                            ${user.getDatascadenzaString()}
                                        </c:if>
                                    </td>
                                    <td style="vertical-align: middle;">
                                        ${user.totalLogin}
                                    </td>
                                    <td style="vertical-align: middle;">
                                        ${user.totalAction}
                                    </td>
                                    <td style="vertical-align: middle;">
                                        ${user.getLastLoginString()}
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                    <c:if test="${mUser.groupsetId != 412 || mUser.isUserAdmin()}">
                        <hr/>
                        <div id="activeSessionPageDiv" class="minichart" style="float: left; width: 100%"></div>
                    </c:if>
                </div>
                <div id="startExportDiv" class="chart" style="float: left"><center><div class="loader"></div><p><spring:message code="menu.admin.caricamento"/></p></center></div>
            </div>
            <div style="height: 400px">
                <div id="licenseExpiredDiv" class="chart" style="float: left"><center><div class="loader"></div><p><spring:message code="menu.admin.caricamento"/></p></center></div>
                <div id="videoDownloadDiv" class="chart" style="float: left"><center><div class="loader"></div><p><spring:message code="menu.admin.caricamento"/></p></center></div>
            </div>
        </div>
    </body>
</html>