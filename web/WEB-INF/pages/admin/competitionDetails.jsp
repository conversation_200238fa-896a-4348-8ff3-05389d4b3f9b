<%@page contentType="text/html" pageEncoding="UTF-8"%>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1"/>
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
            <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
            <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
            <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
            <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
            <title><spring:message code="theme.title"/></title>
            <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
            <link rel="icon" href="/sicstv/images/favicon.ico"/>

            <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
            <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

            <script src="/sicstv/js/searchFilter.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
            <script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
            <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
            <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
            <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>

            <script src="/sicstv/uikit/js/components/notify.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/accordion.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/autocomplete.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/datepicker.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/form-select.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/pagination.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/core/dropdown.min.js" type="text/javascript"></script>
            <script src="/sicstv/uikit/js/components/tooltip.js"></script>

            <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
            <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
            <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.css?<%=System.currentTimeMillis()%>" />
            <link href="/sicstv/uikit/css/components/notify.almost-flat.min.css" rel="stylesheet" >

                <script src="https://cdn.amcharts.com/lib/4/core.js"></script>
                <script src="https://cdn.amcharts.com/lib/4/charts.js"></script>
                <script src="https://cdn.amcharts.com/lib/4/themes/animated.js"></script>

                <style>
                    .block {
                        width: 50%;
                        float: left;
                    }

                    .container {
                        display: flex;
                        width: 100%;
                    }

                    .box3-header {
                        width: 33%;
                        height: 50%;
                        float: left;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }

                    .box2-header {
                        width: 48%;
                        height: 50%;
                        float: left;
                        justify-content: center;
                        align-items: center;
                    }

                    .title {
                        background: #f27e46;
                        color: #ffffff;
                        text-transform: uppercase;
                        font-size: 12px;
                        height: 20px;
                        vertical-align: middle;
                        padding: 8px 12px 0px 12px;
                        margin: 0;
                    }

                    .dataTables_wrapper, .orangeBorder {
                        border-color: #f27e46;
                        border-width: 0px 2px 2px 2px;
                        border-style: solid solid solid solid;
                        padding: 5px 5px 5px 5px;
                        overflow-x: auto;
                    }
                </style>

                <script>
                    var tables;
                    var compTable;
                    // Array to track the ids of the details displayed rows
                    var detailRows = [];
                    var exportTable;

                    $(document).ready(function () {
                        $("#linkCompetitionData").addClass("uk-active");
                        
                        compTable = $('.uk-table-comp').DataTable({
                            "dom": 'frtip',
                            "columnDefs": [
                                {"type": "date-euro", "targets": 0}
                            ],
                            "lengthChange": true,
                            "searching": true,
                            "ordering": true,
                            "info": true,
                            "autoWidth": false,
                            "ext.errMode": "none",
                            "order": [[2, "desc"]],
                            "fixedHeader": true,
                            "lengthMenu": [10],
                            "pagingType": 'simple'
                        });

                        compTable.on('draw.dt', function () {
                            initCompTable();
                        });
                        initCompTable();
                    });

                    function initCompTable() {
                        $('#competitionViewsTable tbody tr td.dt-control').click(function () {
                            var tr = $(this).parents('tr');
                            var row = compTable.row(tr);
                            var idx = $.inArray(tr.attr('id'), detailRows);

                            if (row.child.isShown()) {
                                tr.removeClass('details');
                                row.child.hide();

                                // Remove from the 'open' array
                                detailRows.splice(idx, 1);
                            } else {
                                tr.addClass('details');
                                row.child(format(row.data())).show();

                                // Add to the 'open' array
                                if (idx === -1) {
                                    detailRows.push(tr.attr('id'));
                                }
                            }
                        });
                    }

                    function format(d) {
                        var rows = d[d.length - 1];
                        var result = "<table><thead><tr><th style='min-width: 350px'><spring:message code="menu.admin.partita"/></th><th><spring:message code="menu.admin.numero.visualizzazioni"/></th></tr></thead><tbody>";
                        var splitted = rows.split("||");
                        splitted.forEach(function (item, index) {
                            item = item.trim();
                            if (item.includes(";")) {
                                var game = item.split(";")[0];
                                var views = item.split(";")[1];

                                result += "<tr>";
                                result += "<td>" + game + "</td>";
                                result += "<td>" + views + "</td>";
                                result += "<tr>";
                            }
                        });
                        result += "</tbody></table>";
                        return result;
                    }

                    function formatExport(d) {
                        var rows = d[d.length - 1];
                        var result = "<table><thead><tr><th style='min-width: 350px'><spring:message code="menu.user.export.tipologia"/></th><th><spring:message code="menu.user.export.stato"/></th><th><spring:message code="menu.user.export.inizio.clip"/></th><th><spring:message code="menu.user.export.durata.clip"/></th><th><spring:message code="menu.user.export.tempo.esecuzione"/></th></tr></thead><tbody>";
                        var splitted = rows.split("||");
                        splitted.forEach(function (item, index) {
                            item = item.trim();
                            if (item.includes(";")) {
                                var type = item.split(";")[0];
                                var status = item.split(";")[1];
                                var start = item.split(";")[2];
                                var duration = item.split(";")[3];
                                var execution = item.split(";")[4];

                                result += "<tr>";
                                result += "<td>" + type + "</td>";
                                result += "<td>" + status + "</td>";
                                result += "<td>" + start + "</td>";
                                result += "<td>" + duration + "</td>";
                                result += "<td>" + execution + "</td>";
                                result += "<tr>";
                            }
                        });
                        result += "</tbody></table>";
                        return result;
                    }

                    function changeTimeFilter() {
                        var currentTimeFiler = new URL(document.URL).searchParams.get("timeFilter");
                        if (typeof currentTimeFiler === 'undefined' || currentTimeFiler === null) {
                            window.location.replace(document.URL + "?timeFilter=" + $("#timeFilter").val());
                        } else {
                            window.location.replace(document.URL.replace("?timeFilter=" + currentTimeFiler, "?timeFilter=" + $("#timeFilter").val()));
                        }
                    }
                </script>

                </head>
                <body>
                    <%@ include file="header.jsp" %>
                    <div style="padding-left: 2px; padding-right: 2px;">
                        <div style="width: 100%; height: 30px;">
                            <span class="uk-form" style="float: right; padding-right: 2%">
                                <spring:message code="menu.admin.filtro.temporale"/>: 
                                <select id="timeFilter" onchange="changeTimeFilter();">
                                    <option value="1" <c:if test="${mTimeFilter != null && mTimeFilter == 1}">selected</c:if>><spring:message code="menu.admin.filtro.temporale.1"/></option>
                                    <option value="2" <c:if test="${mTimeFilter != null && mTimeFilter == 2}">selected</c:if>><spring:message code="menu.admin.filtro.temporale.2"/></option>
                                    <option value="3" <c:if test="${mTimeFilter != null && mTimeFilter == 3}">selected</c:if>><spring:message code="menu.admin.filtro.temporale.3"/></option>
                                    <option value="4" <c:if test="${mTimeFilter != null && mTimeFilter == 4}">selected</c:if>><spring:message code="menu.admin.filtro.temporale.4"/></option>
                                    </select>
                                </span>
                            </div>
                        </div>

                        <div style="margin: 10px 10px 0px 10px">
                            <div style="margin-top: 5px; float: left; width: 100%">
                                <div class="block">
                                    <div>
                                        <center><p class="title" style="text-transform: uppercase">VIDEO STREAM - <spring:message code="menu.admin.dettaglio"/></p></center>
                                    <div style="width: 100%; float: right;">
                                        <table class="uk-table uk-table-comp uk-table-bordered uk-table-hover uk-table-striped uk-margin-all-small uk-width-1-1" id="competitionViewsTable">
                                            <thead>
                                                <tr>
                                                    <th style="width: 20px;"></th>
                                                    <th><spring:message code="menu.admin.competizione"/></th>
                                                    <th><spring:message code="menu.admin.numero.visualizzazioni"/></th>
                                                    <th class="uk-hidden">Detail</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <c:forEach var="row" items="${mCompetitionRows}">
                                                    <tr>
                                                        <td class="dt-control"></td>
                                                        <td>${row.key}</td>
                                                        <td>${row.value}</td>
                                                        <td class="uk-hidden">
                                                            <c:forEach var="entry" items="${mCompetitionDetails.get(row.key)}">
                                                                ${mUsers.get(entry.key).firstName} ${mUsers.get(entry.key).lastName} (${mUsers.get(entry.key).groupsetName});${entry.value}||
                                                            </c:forEach>
                                                        </td>
                                                    </tr>
                                                </c:forEach>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                </body>
                </html>