<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <title><spring:message code="theme.title"/></title>
        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <link href="/sicstv/<spring:message code='theme.css'/>" rel="stylesheet" type="text/css"/>
        <script type="text/javascript" src="/sicstv/js/utils.js" ></script>
        <link rel="icon" href="/sicstv/images/favicon.ico"/>
    </head>
    <script type="text/javascript"></script>

    <script>

        jQuery(document).ready(function () {
            /*Causa rallentamenti alla chiusura della dialog
             * dopo la chisura tiene in memoria il numero di pagine 
             * */
            $('a.mcPagerLink, th.mcPagerLink a').click(function () {
                var strPageD = $(this).attr('href').split(/\?/)[0];
                var strDataD = $(this).attr('href').split(/\?/)[1];
                $.ajax({
                    url: strPageD,
                    data: encodeURI(strDataD),
                    success: function (msg) {
                        $("#streaming").html(msg);
                    }
                });
                return false; // to stop link
            });
        });
    </script>

    <body>

        <div id="streaming" title="Dettagli Streaming">

            <display:table cellspacing="1" cellpadding="0" id="itemz" pagesize="10"
                           name="mStreaming" sort="list" requestURI="/sicstv/admin/userStreaming.htm"
                           class="displayTable" style="width: 100%;text-align: center;">

                <display:column sortable="true" title="Id_video" style="text-align: center; width: 120px;">
                    ${itemz.videoId}
                </display:column>

                <display:column titleKey="itemz.date" property="date" format="{0,date,dd-MM-yyyy}"  sortable="true" 
                                title="Streaming_date" style="text-align: center; width: 120px;">
                    ${itemz.date}
                </display:column>

                <display:column sortable="true" title="Home_team" style="text-align: center; width: 120px;">
                    ${itemz.homeTeam}
                </display:column>

                <display:column sortable="true" title="Away_team" style="text-align: center; width: 120px;">
                    ${itemz.awayTeam}
                </display:column>

                <display:column titleKey="itemz.gameDate" property="gameDate" format="{0,date,dd-MM-yyyy}"  sortable="true" 
                                title="Match_date" style="text-align: center; width: 120px;">
                    ${itemz.gameDate}
                </display:column>

            </display:table>
        </div>
        <%@ include file="footer.jsp" %>
    </body>
</html>


