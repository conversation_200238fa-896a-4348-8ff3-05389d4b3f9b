<%@ include file="../global/main.jsp" %>

<script type="text/javascript">
    function clickLogin() {
        $.ajax({
            type: "GET",
            url: "/sicstv/user/getIn.htm",
            cache: false,
            success: function () {
                window.location.href = "/sicstv/user/home.htm";
            }
        });
    }

    function clickLogout() {
        $.ajax({
            type: "GET",
            url: "/sicstv/user/getOut.htm",
            cache: false,
            success: function () {
                window.location.href = "/sicstv/auth/login.htm";
            }
        });

    }
</script>

<body class="bg-dark">
    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content d-flex justify-content-center align-items-center">

                    <div class="card">
                        <div class="card-header d-flex align-items-center">
                            <h5 class="mb-0"><spring:message code='sessione.messaggio'/></h5>
                        </div>

                        <div class="card-body pb-0">
                            <p class="mb-3"><spring:message code='sessione.messaggio.secondario'/></p>
                        </div>

                        <div class="card-footer d-sm-flex justify-content-sm-between align-items-sm-center py-sm-2">
                            <div class="hstack gap-2 mt-3 mt-sm-0">
                                <button type="button" class="btn btn-indigo w-100 w-sm-auto" onclick="clickLogin();">
                                    <i class="ph-check me-2"></i>
                                    <spring:message code='sessione.entra'/>
                                </button>
                                <button type="button" class="btn btn-light w-100 w-sm-auto" onclick="clickLogout();">
                                    <i class="ph-x me-2"></i>
                                    <spring:message code='sessione.esci'/>
                                </button>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- /content area -->

            </div>
            <!-- /inner content -->

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
</body>
