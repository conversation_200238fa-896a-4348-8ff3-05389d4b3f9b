<html>
<head>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>	
</head>
<body>
<c:choose>
	<c:when test="${mUser!=null}">
            <div class="uk-animation-fade uk-alert uk-alert-success uk-text-center"><i class="uk-icon-check-circle"></i>&nbsp;<spring:message code="email.datiaccesso"/></div>
	</c:when>
	<c:otherwise>
		<div class="uk-animation-shake uk-alert uk-alert-danger uk-text-center"><i class="uk-icon-exclamation-circle"></i>&nbsp;<spring:message code="email.accessoscaduto"/></div>
	</c:otherwise>
</c:choose>
</body>
</html>
