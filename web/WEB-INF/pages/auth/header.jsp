<link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
<script type="text/javascript" src="/sicstv/js/jquery.timer.js"></script>

<script src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
<script src="/sicstv/js/blockUI.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
<script type="text/javascript">
    window.addEventListener('beforeunload', function (event) {
        // questo evento viene richiamato ogni volta prima che si esce dalla pagina
        // quindi sia per navigazione avanti e indietro, che per reload della pagina
        jsShowBlockUI();
    });
</script>

<nav id="sicsHeader" class="uk-margin-small-top uk-margin-small-bottom ">

    <div class="uk-container">

        <div class="uk-hidden-small">
            <img style='width:150px;' src="/sicstv/<spring:message code='theme.logo'/>" title="SICS.tv" alt="SICS.tv">
        </div>

        <div class="uk-visible-small">
            <div class="uk-text-center" style="margin-left: 20px;">
                <a href="home.htm"><img style='width:150px;' src="/sicstv/<spring:message code='theme.logo'/>" title="SICS.tv" alt="SICS.tv"></a>
            </div>

        </div>

    </div>
</nav>
<div id="subline" class="uk-width-1-1"></div>