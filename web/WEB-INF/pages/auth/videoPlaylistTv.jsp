<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />

        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <link rel="icon" href="/sicstv/images/favicon.ico">
        <title><spring:message code="theme.title"/></title>
        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

        <script type="text/javascript" src="/sicstv/js/jquery.timer.js"></script>
        <script type="text/javascript" src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" ></script>
        
        <link rel="stylesheet" href="/sicstv/datatables/jquery.dataTables.min.css"/>
        <script src="/sicstv/datatables/jquery.dataTables.min.js"></script>
        <script src="/sicstv/datatables/dataTables.bootstrap.min.js"></script>
        <script src="/sicstv/datatables/plugins/sorting/date-euro.js"></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/components/notify.almost-flat.min.css"  >
        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />

        <script type="text/javascript">
            var playerHTML;
            var hdActive, tactActive;
            var isPlaylistTv = true;
            var curVideoPath, curVideoHDPath, curVideoTACTPath;
            var actionTable, actionTableMobile;
            var gamesSelected = [];
            var selectedActions;
            var tacticalEventIds = [];
            
            var indexActionToPlay = -1; // messo a -1 per fixare problema se fai screenshot senza entrare almeno in una clip
            var isClipStopped = true;
            // imposato da timerClockHTML.set({time: 500, autostart: true});
            var timerClockHTML = $.timer(function () {

                if (playerHTML && !playerHTML.paused && !isClipStopped) {

                    nowTime = playerHTML.currentTime;
                    var timeToEnd = parseFloat(fineAzione - nowTime);
                    var stringTimeToEnd = "00";
                    var sec = timeToEnd;
                    if (timeToEnd > 59) {
                        var min = Math.floor(timeToEnd / 60);
                        if (min > 9) {
                            stringTimeToEnd = min;
                        } else {
                            stringTimeToEnd = "0" + min;
                        }
                        sec = timeToEnd % 60;
                    }
                    if (parseInt(sec) > 9) {
                        stringTimeToEnd += ":" + parseInt(sec);
                    } else {
                        stringTimeToEnd += ":0" + parseInt(sec);
                    }
                    $("#timeToEnd").html(" -" + stringTimeToEnd);
                    var seekSlider = document.getElementById('seek-slider');
                    seekSlider.value = parseInt(seekSlider.max - parseInt(timeToEnd * 2));
                    if (nowTime > fineAzione) {
                        playerHTML.pause();
                        this.stop();
                        
                        var actionsArray = selectedActions;
                        $('.currentClip').removeClass("currentClip");
                        if (indexActionToPlay < actionsArray.length - 1) {
                            indexActionToPlay++;
                            enableSwitchAction();
                            checkIfNeededToChangePage();

                            $('#rowEventId' + actionsArray[indexActionToPlay] +' td').addClass("currentClip");
                            var params = $("#startData" + actionsArray[indexActionToPlay]).val().split("||");
                            if (params.length > 14) {
                                goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]), $.trim(params[14]));
                            } else {
                                goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]));
                            }
                            updateTooltipSelectedActions("");
                        } else {
                            // DAFARE tentativo di cambiare tab, selezionare tutto e andare in play
                            if ($("#checkboxSelectAllEvents").prop("checked")) {
                                if ($(".paginate_button.next.disabled").length === 0) {
                                    if (isMobile) {
                                        $("#azioni_mobile_next a").trigger('click');
                                    } else {

                                        $("#azioni_next a").trigger('click');
                                    }
                                    $("#checkboxSelectAllEvents").attr("checked", "checked");
                                    $("#checkboxSelectAllEventsSmall").attr("checked", "checked");
                                    jsSelectAllEvents('azioni', true);
                                    startAction('', 0);
                                }
                            }
                        }
                    }
                }
            });
            
            function initPageVideo() {
                singleMatch = ${fn:length(mGame)==1};
                var primo = true;
                if (${mGame.size() > 0}) {
                <c:forEach items="${mGame}" var="game" varStatus="status" >
                    gamesSelected.push('${game.value.idFixture}');
                    if (singleMatch) {
                        $('body').prepend("<input type='hidden' id='mCompetitionId' value='${game.value.competitionId}' /><input type = 'hidden' id = 'mDay' value = '${game.value.matchday}' />");
                    }
                    if (primo) {
                        primo = false;
                        curVideo = "${game.value.videoName}";
                        curVideoPath = "${game.value.getVideoPathS3()}";
                        if (${game.value.hd && !personalSource}) {
                            curVideoHDPath = "${game.value.getVideoPathHDS3()}";
                            $("#btnHD").removeClass("uk-hidden");
                        }
                        if (${game.value.tacticalVideo && !personalSource}) {
                            curVideoTACTPath = "${game.value.getVideoPathTACTS3()}";
                            $("#btnTACT").removeClass("uk-hidden");
                        }
                        loadVideo(curVideoPath, false, false, true);
                        $("<div id='playerType' hidden>${game.value.providerId}</div>").insertAfter("#mGame");
                    }
                </c:forEach>
                }

                $("#numMatchSelected").html(gamesSelected.length);
            }
            
            function loadVideo(videoname, playHD, playTACT, force) {
                if (typeof playerHTML === 'undefined') {
                    var hdEnable = !$("#btnHD").hasClass("uk-hidden");
                    if (hdEnable) {
                        playHD = true;
                        if (!isMobile && !iOS()) {
                            if ($("#btnHD").hasClass("HDon")) {
                                // se è in hd allora passo allo standard
                                hdActive = false;
                                $("#btnHD img").attr("src", "/sicstv/images/hd-dark.png");
                                $("#btnHD").removeClass("HDon");
                            } else {
                                // se è sd passo all'HD
                                hdActive = true;
                                $("#btnHD img").attr("src", "/sicstv/images/hd-selected.png");
                                $("#btnHD").addClass("HDon");
                            }
                            tactActive = false;
                            $("#btnTACT img").attr("src", "/sicstv/images/tact.png");
                            $("#btnTACT").removeClass("HDon");
                        }
                    }
                    var videoHtmlToAdd = '<source src="' + videoname + '" type="video/mp4">';
                    $('#media_playerHTML').html(videoHtmlToAdd);
                    playerHTML = $('#media_playerHTML').get(0);
                    if (isPlaylistTv) {
                        playerHTML.className += ' hideSeekBar';
                    }
                }
                if (videoname !== curVideoPath || force) {
                    var hd = !$("#btnHD").hasClass("uk-hidden");
                    var tact = !$("#btnTACT").hasClass("uk-hidden");
                    $("#btnHD").addClass("uk-hidden");
                    $("#btnTACT").addClass("uk-hidden");
                    $("#btnCinema").addClass("uk-hidden");
                    curVideoPath = videoname;
                    var splitVideName = curVideo.split(".");
                    updateBreadcrumb(splitVideName[0]);
                    var tactV = false;
                    var hdV = false;
                    var minVideoQuality = -1;
                    <c:forEach items="${mGame}" var="game" varStatus="status" >
                    tactV = ${game.value.tacticalVideo && !personalSource};
                    hdV = ${game.value.hd && !personalSource};
                    minVideoQuality = ${game.value.minVideoQuality};
                    if ("${game.value.videoName}" === curVideo) {
                        if (hdV) {
                            curVideoHDPath = "${game.value.getVideoPathHDS3()}";
                        }
                        if (tactV) {
                            curVideoTACTPath = "${game.value.getVideoPathTACTS3()}";
                        }
                    }
                    </c:forEach>
                    if (playTACT && curVideoTACTPath.length > 0) {
                        $("#media_playerHTML source").attr("src", curVideoTACTPath);
                        playerHTML.load(curVideoTACTPath);
                    } else if (playHD && curVideoHDPath.length > 0) {
                        $("#media_playerHTML source").attr("src", curVideoHDPath);
                        playerHTML.load(curVideoHDPath);
                    } else {
                        $("#media_playerHTML source").attr("src", curVideoPath);
                        playerHTML.load(curVideoPath);
                    }

                    if (hd && minVideoQuality == 0) {
                        $("#btnHD").removeClass("uk-hidden");
                    }
                    if (tact) {
                        $("#btnTACT").removeClass("uk-hidden");
                    }
                    //$("#btnHD").addClass("uk-hidden");
                    $("#btnCinema").removeClass("uk-hidden");
                }
            }

            function initVideoHTML(videoname) {
                var videoHtmlToAdd = '<source src="' + videoname + '" type="video/mp4">';
                $('#media_playerHTML').html(videoHtmlToAdd);
                playerHTML = $('#media_playerHTML').get(0);
                
                if (isPlaylistTv) {
                    playerHTML.className += ' hideSeekBar';
                }
                curVideoPath = videoname;
            }
            
            function startAction(id, index) {
                // ha la classe nel momento in cui clicco la X di fianco alla barra delle clip
                if (indexActionToPlay === -1) {
                    indexActionToPlay = 0;
                }
                
                var actionsArray = selectedActions;
                
                // controllo se ho cliccato su una riga che ha il checked
                if (typeof id !== 'undefined' && id !== "") {                    
                    // controllo se devo mettere il tattico
                    checkIfNeededToEnableTactical(id);
                } else {
                    var tmpId = actionsArray[indexActionToPlay];
                    // controllo se devo mettere il tattico
                    checkIfNeededToEnableTactical(tmpId);
                }
                
                $("#spanNameAction").removeClass("uk-hidden");
                $('.currentClip').removeClass("currentClip");
                if (id === "") {
                    checkIfNeededToChangePage();
                    if (actionsArray.length > 0) {
                        $("input[id^=infoSearch] + i").addClass("uk-hidden");
                        indexActionToPlay = index;
                        $('#rowEventId' + actionsArray[indexActionToPlay] +' td').addClass("currentClip");
                        updateTooltipSelectedActions("");
                        if ($("#startData" + actionsArray[indexActionToPlay]).val().length > 0) {
                            var params = $("#startData" + actionsArray[indexActionToPlay]).val().split("||");
                            if (params.length > 14) {
                                goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]), $.trim(params[14]));
                            } else {
                                goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]));
                            }
                        }
                        enableSwitchAction();
                    }
                } else {
                    indexActionToPlay = actionsArray.indexOf(id);
                    checkIfNeededToChangePage();
                    
                    $('#rowEventId' + actionsArray[indexActionToPlay] +' td').addClass("currentClip");
                    var descr = $("#descrToShow" + id).val();
                    var splitDesc = descr.split("#");
                    var description = "<strong>" + splitDesc[0] + "</strong>" + (splitDesc[1] !== "" ? ": " : "") + "<span class='tag'>" + splitDesc[1] + "</span> " + splitDesc[2];
                    updateTooltipSelectedActions(description);
                    var stringParam = $("#startData" + id).val();
                    var params = stringParam.split("||");
                    if (params.length > 14) {
                        goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]), $.trim(params[14]));
                    } else {
                        goToAction($.trim(params[0]), $.trim(params[1]), $.trim(params[2]), $.trim(params[3]), $.trim(params[4]), $.trim(params[5]), $.trim(params[6]), $.trim(params[7]), $.trim(params[8]), $.trim(params[9]), $.trim(params[10]), $.trim(params[11]), $.trim(params[12]), $.trim(params[13]));
                    }
                    
                    enableSwitchAction();
                }

            }
            
            //funzione che fa andare l'azione al secondo esatto del match e mette in pausa dopo il tempo di durata dell'azione.			
            function goToActionHTML(start, end, tempo, minutoInizio, durataAzione, nomeSquadra, playerTrim, descAzione, idevent) {
                $(".actionFocus").removeClass("actionFocus");

                playerHTML.currentTime = start;
                if (iOS() || isSafari()) {
                    playerHTML.addEventListener('canplay', function () {
                        playerHTML.currentTime = start;
                        if (playerHTML.currentTime === start) {
                            playerHTML.play();
                        }
                    });
                } else {
                    playerHTML.play();
                }
                fineAzione = end;
                timerClockHTML.set({time: 500, autostart: true});
                var player = '';
                if (playerTrim !== '') {
                    player = " - " + playerTrim;
                }
                var tagFocus = "";
                if (!isMobile) {
                    tagFocus = $("#azioni li").children("#azione").children("#tagAzione-" + idevent).text();
                } else {
                    tagFocus = $("#azioni_mobile li").children("#azione").children("#tagAzione-" + idevent).text();
                }
                desc = tempo + ' - min ' + minutoInizio + ' - ' + durataAzione + 's' + '<br>' + nomeSquadra + player + '<br>' + descAzione + ' ' + tagFocus;
                $('#nome_azione').html(desc);
            }
            
            function goToAction(start, end, tempo, minutoInizio, durataAzione, nomeSquadra, playerTrim, descAzione, idevent, idVideo, videoName, videoPath, provider, idFixture, tactStart) {
                var tact = false;
                var hd = false;
                var minVideoQuality = -1;
                <c:forEach items="${mGame}" var="game" varStatus="status" >
                if ("${game.value.idFixture}" == idFixture) {
                    tact = ${game.value.tacticalVideo && !personalSource};
                    hd = ${game.value.hd && !personalSource};
                    minVideoQuality = ${game.value.minVideoQuality};
                }
                </c:forEach>
                var clipDuration = parseInt(end - start);
                // imposto lo slider delle azioni ( valore massimo + valore iniziale )
                var seekSlider = document.getElementById('seek-slider');
                seekSlider.max = clipDuration * 2;
                seekSlider.value = 0;
                seekSlider.dataset.initVideo = start;
                
                if (tact) {
                    $("#btnTACT").removeClass("uk-hidden");
                    if (tactActive && tactStart !== 'undefined' && tactStart !== '' && tactStart !== "-1") {
                        start = tactStart;
                        end = parseInt(start) + clipDuration;
                    }
                } else {
                    if (!$("#btnTACT").hasClass("uk-hidden")) {
                        $("#btnTACT").addClass("uk-hidden");
                    }
                }
                if (hd) {
                    $("#btnHD").removeClass("uk-hidden");
                } else {
                    if (!$("#btnHD").hasClass("uk-hidden")) {
                        $("#btnHD").addClass("uk-hidden");
                    }
                }
                //$("#btnHD").addClass("uk-hidden");
                $('#playerType').html(provider);
                //initVideo(provider, videoName, idVideo); per multipartita
                curVideo = videoName;
                loadVideo(videoPath, hd, tact && tactActive, false);

                var splitDesc = descAzione.split("#");
                var totActionToPlay = selectedActions.length;
                if (totActionToPlay == 0) {
                    totActionToPlay = 1;
                }
                var intestazione = $("#clipXdiY").html().replace("X", (indexActionToPlay + 1)).replace("Y", totActionToPlay);
                //"Clip "+ (indexActionToPlay + 1) +" di "+ totActionToPlay;
                $("#spanNameAction").html(intestazione + "<strong>" + splitDesc[0] + "</strong>" + (splitDesc[1] !== "" ? ": " : "") + "<span class='tag'>" + splitDesc[1] + "</span>, " + splitDesc[2] + "<span id='timeToEnd'></span>");
                var tactStartOk = false;
                if (${mEventAll.size()} > 0 && tactActive && tact) {
                <c:forEach begin="0" end="${mEventAll.size()}" var="item" items="${mEventAll}">
                    if ("${item.mIdEvent}" == idevent) {
                        tactStartOk = "${item.getSecTactStart()}" != "undefined" && "${item.getSecTactStart()}" != "" && "${item.getSecTactStart()}" != "-1";
                    <c:set var="list" value="${mEventAll.size()}"/>
                    <c:set var="item" value="${mEventAll.size()}"/>
                    }
                </c:forEach>
                }
                if ((tact && tactActive && tactStartOk) || (!tact && tactActive) || !tactActive) {
                    goToActionHTML(start, end, tempo, minutoInizio, durataAzione, nomeSquadra, playerTrim, descAzione, idevent);
                }
            }
            
            var isMobile = false; //initiate as false
            // device detection
            if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent)
                    || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substr(0, 4))) {
                isMobile = true;
            }
            
            $(document).ready(function () {
                <c:forEach var="eventId" items="${mTacticalEventIdList}">
                    tacticalEventIds.push('${eventId}');
                </c:forEach>
                actionTable = $('#azioni').DataTable({
                    "columnDefs": [
                        {type: 'date-euro', targets: 0, orderable: false}
                    ],
                    "paging": true,
                    "lengthChange": true,
                    "pageLength": 15,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "ext.errMode": "none",
                    "order": [[0, "desc"]],
                    "lengthMenu" : [10, 15, 25, 50, 100]
                });
//                actionTable.column(actionTable.columns(':visible').nodes().length).visible(false); // hide last column
//                actionTable.column(actionTable.columns(':visible').nodes().length - 1).visible(false); // hide column
                actionTable.on('preDraw.dt', function() {
                    //$("#infoSearch" + selectedActions[indexActionToPlay] + " + i").addClass("uk-hidden");
                    var actionsArray = selectedActions;
                    //$('#rowEventId' + actionsArray[indexActionToPlay] +' td').removeClass("currentClip");
                    $('.currentClip').removeClass("currentClip");
                });
                actionTable.on('draw.dt', function() {
                    //$("#infoSearch" + selectedActions[indexActionToPlay] + " + i").removeClass("uk-hidden");
                    var actionsArray = selectedActions;
                    if (indexActionToPlay >= 0) {
                        //checkIfNeededToChangePage();
                        $('#rowEventId' + actionsArray[indexActionToPlay] + ' td').addClass("currentClip");
                    }
                    
//                    actionTable.rows({filter: 'applied'}).every( function ( rowIdx, tableLoop, rowLoop ) {
//                        if (this.nodes().to$().is(':visible')) {
//                            var data = this.data();
//                            var id = data[actionTable.columns().nodes().length - 1];
//                            if (checkedActions.includes(id, checkedActions) == true) {
//                                $("#checkbox" + id).attr("checked", "checked");
//                            } else { // per reset
//                                $("#checkbox" + id).removeAttr("checked");
//                                $("#checkboxSelectAllEvents").removeAttr("checked");
//                                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
//                            }
//                        }
//                    });
                });

                actionTableMobile = $('#azioni_mobile').DataTable({
                    "columnDefs": [
                        {type: 'date-euro', targets: 0, orderable: false}
                    ],
                    "paging": true,
                    "lengthChange": true,
                    "pageLength": 10,
                    "searching": true,
                    "ordering": true,
                    "info": true,
                    "autoWidth": false,
                    "ext.errMode": "none",
                    "order": [[0, "desc"]]
                });
//                actionTableMobile.column(actionTableMobile.columns(':visible').nodes().length - 1).visible(false); // hide last column
//                actionTableMobile.column(actionTableMobile.columns(':visible').nodes().length - 2).visible(false); // hide column
                actionTableMobile.on('preDraw.dt', function() {
                    //$("#infoSearch" + selectedActions[indexActionToPlay] + " + i").addClass("uk-hidden");
                    var actionsArray = selectedActions;
                    //$('#rowEventId' + actionsArray[indexActionToPlay] +' td').removeClass("currentClip");
                    $('.currentClip').removeClass("currentClip");
                });
                actionTableMobile.on('draw.dt', function() {
                    //$("#infoSearch" + selectedActions[indexActionToPlay] + " + i").removeClass("uk-hidden");
                    var actionsArray = selectedActions;
                    if (indexActionToPlay >= 0) {
                        checkIfNeededToChangePage();
                        $('#rowEventId' + actionsArray[indexActionToPlay] + ' td').addClass("currentClip");
                    }
                    
//                    actionTableMobile.rows({filter: 'applied'}).every( function ( rowIdx, tableLoop, rowLoop ) {
//                        if (this.nodes().to$().is(':visible')) {
//                            var data = this.data();
//                            var id = data[actionTableMobile.columns().nodes().length - 1];
//                            if (checkedActions.includes(id, checkedActions) == true) {
//                                $("#checkbox" + id).attr("checked", "checked");
//                            } else { // per reset
//                                $("#checkbox" + id).removeAttr("checked");
//                                $("#checkboxSelectAllEvents").removeAttr("checked");
//                                $("#checkboxSelectAllEventsSmall").removeAttr("checked");
//                            }
//                        }
//                    });
                });
                
                initPageVideo();
                updateSelectedActionsArray(true);
                
                playerHTML.addEventListener("click", (event) => {
                    if (event.detail === 1) { // 1 click
                        resumeStopVideo();
                    } else if (event.detail === 2) {
                        if (document.fullscreenElement !== null) { // sono in fullscreen
                            playerHTML.webkitExitFullScreen();
                        } else {
                            playerHTML.requestFullscreen();
                        }
                        if (playerHTML.paused) {
                            playerHTML.play();
                        }
                        checkPlayButton();
                    }
                    
                });
                
                startAction('', 0);
            });
            
            function updateSelectedActionsArray(resetTablePage) {
                selectedActions = [];
                if (isMobile || iOS()) {
                    actionTableMobile.rows({ filter: 'applied' }).every(function() {
                        var data = this.data();
                        // actionTableMobile.columns().nodes().length - 1 return the last column index
                        selectedActions.push(data[actionTableMobile.columns().nodes().length - 1]);
                    });
                    
                    // per risoluzione bug riporto a pagina iniziale della datatable
                    // inoltre aggiornando la pagina verrebbe fuori la spunta del play nella prima riga, quindi meglio tenere
                    if (actionTableMobile.page() !== 0 && resetTablePage) {
                        actionTableMobile.page(0).draw(false);
                    }
                } else {
                    actionTable.rows({ filter: 'applied' }).every(function() {
                        var data = this.data();
                        // actionTable.columns().nodes().length - 1 return the last column index
                        selectedActions.push(data[actionTable.columns().nodes().length - 1]);
                    });
                    
                    // per risoluzione bug riporto a pagina iniziale della datatable
                    // inoltre aggiornando la pagina verrebbe fuori la spunta del play nella prima riga, quindi meglio tenere
                    if (actionTable.page() !== 0 && resetTablePage) {
                        actionTable.page(0).draw(false);
                    }
                }
            }
            
            function checkIfNeededToEnableTactical(id) {
                if (tacticalEventIds.length > 0) {
                    if (tacticalEventIds.includes(id)) {
                        if (!$("#btnTACT").hasClass("HDon")) {
                            switchTACT();
                        }
                    } else { // per tutte le clip che non sono tattico allora disattivo
                        if ($("#btnTACT").hasClass("HDon")) {
                            switchHD();
                        }
                    }
                }
            }
            
            function switchTACT() {
                if ($("#btnTACT").hasClass("HDon")) {
                    // se è in hd allora passo allo standard
                    tactActive = false;
                    hdActive = true;
                    $("#btnTACT img").attr("src", "/sicstv/images/tact.png");
                    $("#btnTACT").removeClass("HDon");
                } else {
                    // se è sd passo a TACT
                    tactActive = true;
                    hdActive = false;
                    $("#btnTACT img").attr("src", "/sicstv/images/tact-selected.png");
                    $("#btnTACT").addClass("HDon");
                }
                $("#btnHD img").attr("src", "/sicstv/images/hd-dark.png");
                $("#btnHD").removeClass("HDon");
                var lastValue = 0;
                if (playerHTML) {
                    lastValue = playerHTML.currentTime;
                    if (tactActive) {
                        lastValue = resyncTime(lastValue, true);
                    } else {
                        lastValue = resyncTime(lastValue, false);
                    }
                }
                loadVideo(curVideoPath, hdActive, tactActive, true);
                if (playerHTML) {
                    playerHTML.currentTime = lastValue;
                }
            }
            
            function switchHD() {
                var tactBefore = tactActive;
                if ($("#btnHD").hasClass("HDon")) {
                    // se è in hd allora passo allo standard
                    hdActive = false;
                    $("#btnHD img").attr("src", "/sicstv/images/hd-dark.png");
                    $("#btnHD").removeClass("HDon");
                } else {
                    // se è sd passo all'HD
                    hdActive = true;
                    $("#btnHD img").attr("src", "/sicstv/images/hd-selected.png");
                    $("#btnHD").addClass("HDon");
                }
                tactActive = false;
                $("#btnTACT img").attr("src", "/sicstv/images/tact.png");
                $("#btnTACT").removeClass("HDon");
                var lastValue = 0;
                if (playerHTML) {
                    lastValue = playerHTML.currentTime;
                    if (tactBefore) {
                        lastValue = resyncTime(lastValue, false);
                    }
                }
                loadVideo(curVideoPath, hdActive, tactActive, true);
                if (lastValue !== 0 && !tactBefore) {
                    resumeVideoFromLastValue(lastValue);
                }
            }
            
            function resyncTime(startToSync, toTact) {
                var result = startToSync;
                //DISABILITATO
                //TODO --> PRIMA AGGIUNGERE CAMPI SU DB
                if (false) {
                    var tactStart = 0;
                    var tvStart = 0;
                    /*
                     
                     */
                    if (toTact) {

                    } else {

                    }
                    if (result < 0) {
                        result = 0;
                    }
                }
                return result;
            }
            
            function checkIfNeededToChangePage() {
                var currentTable;
                if (isMobile || iOS()) {
                    currentTable = actionTableMobile;
                } else {
                    currentTable = actionTable;
                }
            
                var currentPageNumber = currentTable.page();
                var minIndexValue = currentPageNumber * currentTable.page.len();
                var maxIndexValue = currentPageNumber * currentTable.page.len() + currentTable.page.len();
                
                var tmpIndexActionToPlay = indexActionToPlay;
                
                var indexPageToSelect = parseInt(tmpIndexActionToPlay / currentTable.page.len());
                
                if(indexPageToSelect!==currentPageNumber){
                    currentTable.page(indexPageToSelect).draw(false);
                }
            }
            
            function changeAction(next) {
                var newIndex = indexActionToPlay;
                var actionsArray = selectedActions;
                
                $('.currentClip').removeClass("currentClip");
                if (next) {
                    if (indexActionToPlay + 1 < actionsArray.length) {
                        indexActionToPlay++;
                        startAction('', indexActionToPlay);
                    }
                } else {
                    if (indexActionToPlay > 0) {
                        indexActionToPlay--;
                        startAction('', indexActionToPlay);
                    }
                }
                enableSwitchAction();

            }
            
            function updateTooltipSelectedActions(textTooltip) {
                if (textTooltip === "") {
//                    $.each(descrSelectedActions, function (key, value) {
//                        if (key >= indexActionToPlay) {
//                            textTooltip += (key + 1) + ")" + value + "<br>";
//                        }
//                    });
                }
                $("#spanNameAction").attr("title", textTooltip);
                $("#spanNameAction").attr("data-cached-title", textTooltip);
            }
            
            function enableSwitchAction() {
                verifyNextAndPreviousButton();
                $("#seek-slider").removeProp("disabled");
                $("#closeClipViewer").removeProp("disabled");
                isClipStopped = false;
            }
            
            function verifyNextAndPreviousButton() {
                var actionsArray = selectedActions;
            
                if (indexActionToPlay < actionsArray.length - 1) {
                    $("#nextActionSwitch").removeProp("disabled");
                } else {
                    $("#nextActionSwitch").prop("disabled", "disabled");
                }
                if (indexActionToPlay == 0) {
                    $("#prevActionSwitch").prop("disabled", "disabled");
                } else {
                    $("#prevActionSwitch").removeProp("disabled");
                }
            }
            
            function updatePlayerFromSeekSlider() {
                var seekSlider = document.getElementById('seek-slider');
                var initialClipSeconds = seekSlider.dataset.initVideo;
                var currentSliderValue = seekSlider.value;
                
                var timeToSet = (parseInt(initialClipSeconds) + parseFloat(currentSliderValue / 2));
                if (playerHTML.currentTime !== timeToSet) {
                    playerHTML.pause();
                    playerHTML.currentTime = timeToSet;
                    timerClockHTML.set({time: 500, autostart: true});
                }
            }
            
            function stopVideo() {
                if (!playerHTML.paused) {
                    playerHTML.pause();
                }
            }
            
            function resumeVideo() {
                if (playerHTML.paused) {
                    // uso un delay altrimenti se tengo premuto e mi muovo molto piano nella barra poi non esegue correttamente
                    setTimeout( function() {
                        playerHTML.play();
                    }, 250);
                }
            }
            
            function iOS() {

                var iDevices = [
                    'iPad Simulator',
                    'iPhone Simulator',
                    'iPod Simulator',
                    'iPad',
                    'iPhone',
                    'iPod'
                            //,
                            //'Mac68K',
                            //'MacPPC',
                            //'MacIntel'
                ];

                if (!!navigator.platform) {
                    while (iDevices.length) {
                        if (navigator.platform === iDevices.pop()) {
                            return true;
                        }
                    }
                }

                return false;
            }
            
            function isSafari() {
                return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            }
            
            function resumeStopVideo() {
                if (!playerHTML.paused) {
                    playerHTML.pause();
                } else {
                    // verifico se devo tornare alla prima clip
                    if (indexActionToPlay === (selectedActions.length - 1)) {
                        var params = $("#startData" + selectedActions[indexActionToPlay]).val().split("||");
                        if (playerHTML.currentTime > parseInt(params[1])) { // se il tempo attuale è maggiore della fine dell'ultima clip
                            startAction('', 0);
                        }
                    }
                    playerHTML.play();
                }
                checkPlayButton();
            }
            
            function checkPlayButton() {
                if (playerHTML.paused) {
                    $("#resumeStopButton").attr("title", "<spring:message code='video.play'/>");
                    $("#resumeStopButton i").attr("class", "uk-icon-play");
                } else {
                    $("#resumeStopButton").attr("title", "<spring:message code='video.stop'/>");
                    $("#resumeStopButton i").attr("class", "uk-icon-stop");
                }
            }
        </script>

    </head>
    <body>
        <%@ include file="header.jsp" %>
        <div id='breadcrumb' class='uk-width-1-1'><span class="data">Playlist "${mPlaylist.name}" <spring:message code='playlist.shared.by'/> ${mSharingUser.lastName} ${mSharingUser.firstName}</span></div>

        <div class="uk-grid " id="centralGrid">

            <!--Video player-->
            <div id="video" class="uk-width-5-10 uk-responsive-width" >
                <div id="videoTools" >
                    <span id="clipXdiY" class="uk-hidden"><spring:message code='video.clipavanzamento'/></span>
                    <span id="spanNameAction" class="topBarVideo" title="  "></span>
                    <button id='btnHD' class="uk-hidden uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small" onclick="switchHD();" title="<spring:message code='tooltip.HD'/> ${mGame[0].hd}">
                        <img src='/sicstv/images/hd-dark.png'/>
                    </button>
                    <button id='btnTACT' class="uk-hidden uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small" onclick="switchTACT();" title="<spring:message code='tooltip.TACT'/>">
                        <img src='/sicstv/images/tact.png'/>
                    </button>
                    <button id='btnCinema' class="uk-button uk-button-small uk-float-right uk-hidden-medium uk-hidden-small" onclick="toggleTheatreMode();" title="<spring:message code='tooltip.cinema'/>">
                        <i class="uk-icon-desktop"></i>
                    </button>
                    <button id='btnCinema' class="uk-hidden uk-button uk-button-small uk-float-right" onclick="getScreenshot();" title="Cattura schermata video">
                        <i class="uk-icon-camera"></i>
                    </button>
                </div>
                
                <div id="videoContainer">
                    <!-- il video viene caricato qui da jQuery-->
                    <video id='media_playerHTML' class='uk-width-1-1' data-setup='{}' preload muted autoplay playsinline controls controlsList="nodownload" style='background: #000;'>
                    </video>
                </div>
                <div class="tagEvento video-player-tools" id="videoPlayerTools">
                    <button onclick="changeAction(false);" id="prevActionSwitch" class="uk-button uk-button-small uk-margin-small-left uk-margin-small-top uk-margin-small-bottom uk-hidden-medium uk-hidden-small" disabled title="<spring:message code='calendar.precedente'/>"><i class="uk-icon-angle-double-left"></i> </button>
                    <button onclick="resumeStopVideo();" id="resumeStopButton" class="uk-button uk-button-small uk-margin-small uk-hidden-small" title="<spring:message code='video.stop'/>" style="min-width: 33px"><i class="uk-icon-stop"></i></button>
                    <button onclick="changeAction(true);" id="nextActionSwitch" class="uk-button uk-button-small uk-margin-small uk-hidden-medium uk-hidden-small" disabled title="<spring:message code='calendar.successiva'/>"><i class="uk-icon-angle-double-right"></i> </button>
                    <input class="video-player-tools-child" data-initVideo="0" type="range" id="seek-slider" min="0" step="1" value="0" onmousedown="stopVideo()" onmouseup="resumeVideo()" oninput="updatePlayerFromSeekSlider()" onchange="updatePlayerFromSeekSlider()" ontouchend="updatePlayerFromSeekSlider()" disabled>
                </div>
            </div>
            
            <div id="bottomNav" class="uk-width-1-1 uk-hidden" id="filter">
                <ul id="bottomNavContent" class="uk-switcher uk-margin-small-left uk-margin-small-right uk-clear-both uk-text-center">
                    <li id="liEventiSmallContent" class="uk-visible-small uk-width-1-1">
                        <table id="azioni_mobile" class="uk-overflow-container uk-visible-small table table-bordered table-hover uk-margin-all-small uk-table">
                            <thead>
                                <tr>
                                    <th class="uk-hidden-small uk-text-center">
                                        <span class="squaredCheckboxEvent uk-hidden-small"><input type="checkbox" id="checkboxSelectAllEvents" onclick="jsSelectAllEvents('azioni', true);"/>
                                            <label for="checkboxSelectAllEvents"></label>
                                        </span>
                                    </th>
                                    <th><spring:message code="video.descrizione"/></th>
                                    <th class="uk-hidden-small"><spring:message code="video.half"/></th>
                                    <th class="uk-hidden-small"><spring:message code="video.durata"/></th>
                                    <th class="uk-visible-small uk-text-center"><spring:message code="video.half"/><br/><spring:message code="video.inizio"/><br/><spring:message code="video.durata"/></th>
                                    <th><spring:message code="video.squadra"/></th>
                                        <c:if test="${fn:length(mEventFilter)>0 || mGame.size() > 1}">
                                        <th><spring:message code="video.partita"/></th>
                                        </c:if>
                                        <!--c:if test="${matchPlayerId == 0}"-->
                                    <th class="uk-hidden-small"><spring:message code="video.giocatore"/></th>
                                    <!--/c:if-->
                                    <c:if test="${mPlaylistTvId != null}"><th></th></c:if>
                                    <th class="uk-hidden-small">checked</th>
                                    <th class="uk-hidden-small">infoSearch</th>
                                    <th class="uk-hidden-small">ID</th>
                                </tr>
                            </thead>
                            <c:if test="${not empty mEvent}">
                                <tbody>
                                    <c:set var="countEvents" value="0"/>
                                    <c:forEach var="list" items="${mEvent}">
                                        <c:forEach var="item" items="${list.value}" >
                                            <c:set var="countEvents" value="${countEvents + 1}"/>

                                            <tr class="uk-table-middle uk-margin-small-bottom uk-margin-small-top eventToShow" id="rowEventId${item.id}">
                                                <td class="clickRow uk-text-center uk-hidden-small" style="width: 50px;">
                                                    <span class="squaredCheckboxEvent uk-hidden-small"><input type="checkbox" id="checkbox${item.id}small" onclick="jsSelectAction('${item.id}');" value="${item.id}"/>
                                                        <label for="checkbox${item.id}small"></label>
                                                        <!--																0							1						2						3								4                               5					6                           7							8				9				10					11                  12-->
                                                        <input type="hidden" id="startData${item.id}" value="${item.getSecStartAzione()}||${item.getSecEndAzione()}||${item.halfTrim(mLanguage)}||${item.getPeriod_start_minute()}||${item.getDurataAzioneMinSec(false)}||${item.getmTeam()}||${item.playerTrim()}||${item.getDescrAzioneToShow(mLanguage)}||${item.mIdEvent}||${item.videoId}||${item.videoName}||${item.videoPathS3}||${item.provider}||${item.idFixture}||${item.getSecTactStart()}" />
                                                        <input type="hidden" id="descrToShow${item.id}" value="${item.getDescrAzioneToShow(mLanguage)}" />
                                                    </span>
                                                </td>
                                                <td class="clickRow" style="width: 250px;" onclick="startAction('${item.id}', ${countEvents});">
                                                    <div class="descrBox">
                                                        <span class="uk-float-left">${item.button.descTrim(mLanguage)}<br>
                                                            <c:set var="tagval" value=""/>
                                                            <c:forEach var="tag" items="${item.mTags}" >
                                                                <c:if test="${not empty tag}">
                                                                    <c:if test="${not empty tagval}"><c:set var="tagval" value="${tagval}, "/></c:if>
                                                                    <c:set var="tagval" value="${tagval}${tag.descLan(mLanguage)}"/>
                                                                </c:if>
                                                            </c:forEach>
                                                            <span class="tag">${tagval}</span>
                                                            <c:if test="${not empty item.mNote}">
                                                                <c:if test="${not empty tagval}">
                                                                <br>
                                                                </c:if>
                                                                <span class="tag">${item.mNote}</span>
                                                            </c:if>
                                                        </span>

                                                        <input type="hidden" id="infoSearch${item.id}" class="infoSearch" value="${item.getInfoSearch()}#${list.key}||${item.mIdEvent}#${item.getmResultReal()}" />
                                                        <!--<i class="uk-icon-play-circle uk-hidden uk-float-right uk-icon-small" style="color: green;"></i>-->
                                                        <span id="timeToMatch" style="display:none">${item.mStart}</span> <span id="timeToMatchEnd" style="display:none">${item.durataAzione}</span>
                                                    </div>
                                                </td>
                                                <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents});">${item.halfTrim(mLanguage)}</td>
                                                <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents});">${item.durataAzione} </td>
                                                <td class="clickRow uk-text-center uk-visible-small uk-text-center" onclick="startAction('${item.id}', ${countEvents});">${item.halfTrim(mLanguage)}<br/>${item.getStartAzione()}<br/>${item.durataAzione}</td>
                                                <td class="clickRow" onclick="startAction('${item.id}', ${countEvents});">${item.mTeam} </td>
                                                <c:if test="${fn:length(mEventFilter)>0  || mGame.size() > 1}">
                                                    <td class="clickRow" style="width: 200px;" onclick="startAction('${item.id}', ${countEvents});">${item.getMatch()} </td>
                                                </c:if>
                                                <td class="clickRow uk-hidden-small" onclick="startAction('${item.id}', ${countEvents});">${item.playerCamel()}</td>
                                                <c:if test="${mPlaylistTvId != null}">
                                                    <td>
                                                        <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}">
                                                            <button class="uk-icon-hover uk-button uk-button-mini" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.options"/>"><i class="uk-icon-hover uk-icon-caret-down"></i></button>
                                                            <div class="uk-dropdown uk-dropdown-small uk-text-left">
                                                                <ul class="uk-nav uk-nav-dropdown">
                                                                    <li><a <c:choose><c:when test="${mPlaylistTVShare != null && mPlaylistTVShare.editable == false}">class="disabled"</c:when><c:otherwise> onclick="openModifyClip('${item.id}')"</c:otherwise></c:choose>><i class="uk-icon-pencil uk-margin-right"></i><spring:message code='playlist.edit.single.title'/></a></li>
                                                                    <li><a <c:choose><c:when test="${mPlaylistTVShare != null && mPlaylistTVShare.editable == false}">class="disabled"</c:when><c:otherwise> onclick="removeClipsFromPlaylist('${item.id}')"</c:otherwise></c:choose>><i class="uk-icon-trash uk-margin-right"></i><spring:message code='playlist.remove.single.title'/></a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </c:if>
                                                <td class="uk-hidden-small">false</td>
                                                <td class="uk-hidden-small">${item.getInfoSearch()}#${list.key}||${item.mIdEvent}#${item.getmResultReal()}</td>
                                                <td class="uk-hidden-small">${item.id}</td>
                                            </tr>
                                        </c:forEach>
                                    </c:forEach>
                                </tbody>
                            </c:if>
                        </table>
                    </li>
                </ul>
            </div>

            <div id="sideNav" class="uk-width-5-10 uk-hidden-small">
                <ul class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left uk-margin-bottom" data-uk-tab="{connect:'#sideNavContent'}">
                    <li id="liActions" class="uk-active"><a href="#" onclick="changeButtonsVisibility(true);"><spring:message code="video.eventi"/><span id="numEvents" class="uk-badge uk-hidden uk-margin-small-left">0</span></a></li>
                </ul>
                
                <ul id="sideNavContent" class="uk-switcher uk-margin-all ">

                    <li class="uk-active uk-hidden-small" id="eventi">


                        <table id="azioni" class="uk-hidden-small table table-bordered table-hover uk-margin-all-small uk-table">
                            <thead>
                                <tr>
                                    <th><spring:message code="video.descrizione"/></th>
                                    <c:if test="${mPlaylistTvId == null}">
                                    <th class="uk-hidden-small"><spring:message code="video.half"/></th>
                                    </c:if>
                                    <th class="uk-hidden-small"><spring:message code="video.durata"/></th>
                                    <th class="uk-visible-small uk-text-center"><spring:message code="video.half"/><br/><spring:message code="video.inizio"/><br/><spring:message code="video.durata"/></th>

                                    <th><spring:message code="video.squadra"/></th>
                                        <c:if test="${fn:length(mEventFilter)>0 || mGame.size() > 1}">
                                        <th><spring:message code="video.partita"/></th>
                                        </c:if>
                                        <!--c:if test="${matchPlayerId == 0}"-->
                                    <th><spring:message code="video.giocatore"/></th>
                                    <!--/c:if-->
                                    <c:if test="${mPlaylistTvId != null}"><th></th></c:if>
                                    <th class="uk-hidden">checked</th>
                                    <th class="uk-hidden">infoSearch</th>
                                    <th class="uk-hidden">ID</th>
                                </tr>
                            </thead>
                            <c:if test="${not empty mEvent}">
                                <tbody>
                                    <c:set var="countEvents" value="0"/>
                                    <c:forEach var="list" items="${mEvent}">
                                        <c:forEach var="item" items="${list.value}" >
                                            <c:set var="countEvents" value="${countEvents + 1}"/>

                                            <tr class="uk-table-middle uk-margin-small-bottom uk-margin-small-top eventToShow" id="rowEventId${item.id}">
                                                <td class="clickRow" style="width: 250px;" onclick="startAction('${item.id}', ${countEvents});">
                                                    <div class="descrBox">
                                                        <span class="uk-float-left">${item.button.descTrim(mLanguage)}<br>
                                                            <c:set var="tagval" value=""/>
                                                            <c:forEach var="tag" items="${item.mTags}" >
                                                                <c:if test="${not empty tag}">
                                                                    <c:if test="${not empty tagval}"><c:set var="tagval" value="${tagval}, "/></c:if>
                                                                    <c:set var="tagval" value="${tagval}${tag.descLan(mLanguage)}"/>
                                                                </c:if>
                                                            </c:forEach>
                                                            <span class="tag">${tagval}</span>
                                                            <c:if test="${not empty item.mNote}">
                                                                <c:if test="${not empty tagval}">
                                                                <br>
                                                                </c:if>
                                                                <span class="tag">${item.mNote}</span>
                                                            </c:if>
                                                        </span>

                                                        <input type="hidden" id="infoSearch${item.id}" class="infoSearch" value="${item.getInfoSearch()}#${list.key}||${item.mIdEvent}#${item.getmResultReal()}" />
                                                        <!--<i class="uk-icon-play-circle uk-hidden uk-float-right uk-icon-small" style="color: green;"></i>-->
                                                        <span id="timeToMatch" style="display:none">${item.mStart}</span> <span id="timeToMatchEnd" style="display:none">${item.durataAzione}</span>
                                                    </div>
                                                </td>
                                                <c:if test="${mPlaylistTvId == null}">
                                                <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents});">${item.halfTrim(mLanguage)}</td>
                                                </c:if>
                                                <td class="clickRow uk-text-center uk-hidden-small" onclick="startAction('${item.id}', ${countEvents});">${item.durataAzione} </td>
                                                <td class="clickRow uk-text-center uk-visible-small uk-text-center" onclick="startAction('${item.id}', ${countEvents});">${item.halfTrim(mLanguage)}<br/>${item.getStartAzione()}<br/>${item.durataAzione}</td>
                                                <td class="clickRow" onclick="startAction('${item.id}', ${countEvents});">${item.mTeam} </td>
                                                <c:if test="${fn:length(mEventFilter)>0  || mGame.size() > 1}">
                                                    <td class="clickRow" style="width: 200px;" onclick="startAction('${item.id}', ${countEvents});">${item.getMatch()} </td>
                                                </c:if>
                                                <td class="clickRow" onclick="startAction('${item.id}', ${countEvents});">${item.playerCamel()}</td>
                                                <c:if test="${mPlaylistTvId != null}">
                                                    <td>
                                                        <div class="uk-button-dropdown" data-uk-dropdown="{mode:'click'}">
                                                            <button class="uk-icon-hover uk-button uk-button-mini" data-uk-tooltip="{pos:'bottom'}" title="<spring:message code="menu.user.options"/>"><i class="uk-icon-hover uk-icon-caret-down"></i></button>
                                                            <div class="uk-dropdown uk-dropdown-small uk-text-left">
                                                                <ul class="uk-nav uk-nav-dropdown">
                                                                    <li><a <c:choose><c:when test="${mPlaylistTVShare != null && mPlaylistTVShare.editable == false}">class="disabled"</c:when><c:otherwise> onclick="openModifyClip('${item.id}')"</c:otherwise></c:choose>><i class="uk-icon-pencil uk-margin-right"></i><spring:message code='playlist.edit.single.title'/></a></li>
                                                                        <li><a <c:choose><c:when test="${mPlaylistTVShare != null && mPlaylistTVShare.editable == false}">class="disabled"</c:when><c:otherwise> onclick="removeClipsFromPlaylist('${item.id}')"</c:otherwise></c:choose>><i class="uk-icon-trash uk-margin-right"></i><spring:message code='playlist.remove.single.title'/></a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </c:if>
                                                <td class="uk-hidden">false</td>
                                                <td class="uk-hidden">${item.getInfoSearch()}#${list.key}||${item.mIdEvent}#${item.getmResultReal()}</td>
                                                <td class="uk-hidden">${item.id}</td>
                                            </tr>
                                        </c:forEach>
                                    </c:forEach>
                                </tbody>
                            </c:if>
                        </table>
                    </li>
                </ul>
            </div>
        </div>

    </body>
</html>
