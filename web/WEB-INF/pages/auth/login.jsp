<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />

        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <link rel="icon" href="/sicstv/images/favicon.ico">
        <title><spring:message code="theme.title"/></title>

        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <link href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" rel="stylesheet" />
        <link rel="stylesheet" href="/sicstv/css/login.css" />
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>
        <script type="text/javascript" src="/sicstv/js/utils.js" ></script>
        <script src="/sicstv/js/blockUI.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
        <!--link href='http://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700' rel='stylesheet' type='text/css'-->
        <script type="text/javascript">

            function jsLostPwdDialog() {
                $('#lostPwdAlert').html("");
                $("#divLostPwd").toggle();
            }

            $(document).ready(function () {
                if (${not empty usr} && ${not empty psw}) {
                    $("#j_username").attr("value", "${usr}");
                    $("#j_password").attr("value", "${psw}");
                    $("#submit").click();
                }
            });

            sessionStorage['exportIdentifier'] = "";

            function jsSubmitPassword() {
                jsLoadingAlpha("#lostPwdAlert");
                $.ajax({
                    type: "GET",
                    url: "/sicstv/auth/lostpwd.htm",
                    data: encodeURI("formUsername=" + $('#userEmail').val()),
                    cache: false,
                    success: function (msg) {
                        jsLoadingOff("#lostPwdAlert");
                        $('#lostPwdAlert').html(msg);
                    }
                });
            }

            function jsSubmitReturn(idLogin, ev) {
                if ((ev.which && ev.which == 13) || (ev.keyCode && ev.keyCode == 13)) {
                    $("#" + idLogin).submit();
                    return false;
                } else
                    return true;
            }

            function showLoadingMessage() {
                $("#loadingDivNotifier").removeClass("uk-hidden");
                jsLoadingAlpha("#loadingNotifier");
            }

            window.addEventListener('beforeunload', function (event) {
                // questo evento viene richiamato ogni volta prima che si esce dalla pagina
                // quindi sia per navigazione avanti e indietro, che per reload della pagina
                jsShowBlockUI();
            });
        </script>
    </head>

    <body>
        <c:if test="${not empty usr && not empty psw}">
            <div class="uk-grid" style="visibility: hidden;">
            </c:if>
            <c:if test="${empty usr || empty psw}">

                <div class="uk-grid">
                </c:if>
                <nav id="sicsHeader" class="uk-width-1-1">
                    <div class="uk-width-1-2 uk-container-center"><img style='width:150px;' src="/sicstv/<spring:message code='theme.logo'/>" title="SICS.tv" alt="SICS.tv"></div>
                </nav>
                <div id="subline" class="uk-width-1-1"></div>

                <div id="loginContent" class="uk-width-1-1">

                    <!--Form login web -->
                    <div class="uk-width-1-2 uk-container-center uk-margin-large-top uk-margin-large-bottom uk-hidden-small">
                        <form id="login_form" class="uk-form" action='/sicstv/j_spring_security_check' method='POST'>
                            <fieldset>                                
                                <div class="uk-form-row"><label class="columnSpacer"><spring:message code="menu.username"/></label><input id="j_username" onkeydown="return jsSubmitReturn('login_form', event);" type="text" class="uk-width-1-2" name="j_username" value=""></div>
                                <div class="uk-form-row"><label class="columnSpacer"><spring:message code="menu.password"/></label><input id="j_password" onkeydown="return jsSubmitReturn('login_form', event);" type="password" class="uk-width-1-2" name="j_password" value=""></div>
                                <div class="uk-form-row"><label class="columnSpacer">&nbsp;</label><button type="submit" id="submit" class="uk-button uk-button-large" value="entra"><spring:message code="menu.area.accedi"/></button></div>
                                <div class="uk-form-row"><a onclick="jsLostPwdDialog()" id="pswLost" class="uk-link"  ><spring:message code="email.pwd.smarrita.subject"/></a></div>
                            </fieldset>
                        </form>
                    </div>

                    <!--Form login small -->
                    <div class="uk-width-1-1 uk-container-center uk-margin-large-top uk-margin-large-bottom uk-margin-left uk-margin-right uk-visible-small">
                        <form id="login_form" class="uk-form" action='/sicstv/j_spring_security_check' method='POST'>
                            <fieldset>
                                <div class="uk-form-row"><label class="columnSpacer"><spring:message code="menu.username"/></label><input id="j_username" type="text" class="uk-width-1-2" name="j_username" value=""></div>
                                <div class="uk-form-row"><label class="columnSpacer"><spring:message code="menu.password"/></label><input id="j_password" type="password" class="uk-width-1-2" name="j_password" value=""></div>
                                <div class="uk-form-row"><label class="columnSpacer">&nbsp;</label><button type="submit" id="submit" class="uk-button uk-button-large" value="entra"><spring:message code="menu.area.accedi"/></button></div>
                                <div class="uk-form-row"><a onclick="jsLostPwdDialog()" id="pswLost" class="uk-link"  ><spring:message code="email.pwd.smarrita.subject"/></a></div>
                            </fieldset>
                        </form>
                    </div>

                    <c:choose>
                        <c:when test="${not empty param.expired}">
                            <div class="uk-width-1-2 uk-container-center uk-alert uk-alert-danger uk-text-center">
                                <i class="uk-icon-exclamation-circle"></i>&nbsp;<spring:message code="menu.login.expired"/>
                            </div>
                        </c:when>
                        <c:when test="${sessionScope.expiredAccess != null}">
                            <div class="uk-width-1-2 uk-container-center uk-alert uk-alert-danger uk-text-center">
                                <i class="uk-icon-exclamation-circle"></i>&nbsp;<spring:message code="menu.accesso.scaduto"/> ${sessionScope.expiredAccess}
                            </div>
                        </c:when>
                        <c:when test="${sessionScope.expiredAccess != null}">
                            <div class="uk-width-1-2 uk-container-center uk-alert uk-alert-danger uk-text-center"><i class="uk-icon-exclamation-circle"></i>&nbsp;<spring:message code="menu.accesso.scaduto"/> ${sessionScope.expiredAccess}</div>
                        </c:when>
                        <c:when test="${sessionScope.errorInfo != null}">
                            <div class="uk-width-1-2 uk-container-center uk-alert uk-alert-danger uk-text-center"><i class="uk-icon-exclamation-circle"></i>&nbsp;${sessionScope.errorInfo}</div>
                        </c:when>
                        <c:when test="${not empty param.login_error}">
                            <div class="uk-width-1-2 uk-container-center uk-alert uk-alert-danger uk-text-center"><i class="uk-icon-exclamation-circle"></i>&nbsp;<spring:message code="menu.login.bad"/></div>
                        </c:when>
                        <c:when test="${not empty param.session}">
                            <div class="uk-width-1-2 uk-container-center uk-alert uk-alert-danger uk-text-center">
                                <i class="uk-icon-exclamation-circle"></i>&nbsp;<spring:message code="menu.login.altroDispositivo"/>
                            </div>
                        </c:when>
                        <c:otherwise>

                        </c:otherwise>
                    </c:choose>

<!--                    <div class="uk-width-1-2 uk-container-center uk-alert uk-alert-warning uk-text-center">
                        <i class="uk-icon-exclamation-circle"></i>&nbsp;<spring:message code="menu.warning.maintenance"/>
                    </div>-->

                    <div id="divLostPwd" class="uk-width-1-2 uk-container-center">
                        <hr class="uk-grid-divider">
                        <form class="uk-form">
                            <div class="uk-form-row"><label class="uk-form-label"><spring:message code="menu.password.retrivepwd"/></label></div>
                            <div class="uk-form-row"><input type="text" id="userEmail">&nbsp;<button class="uk-button uk-button-medium" onclick="event.preventDefault();
                                    jsSubmitPassword()"><spring:message code="menu.conferma"/></button></div>
                            <div id="lostPwdAlert" class="uk-form-row uk-margin-top uk-text-center"><i class="uk-icon-times-circle"></i>&nbsp;</div>
                        </form>
                    </div>
                </div>

                <div id="loadingDivNotifier" class="spinner uk-hidden" >
                    <div class="loader"></div> 
                </div>
            </div>
    </body>
</html>
