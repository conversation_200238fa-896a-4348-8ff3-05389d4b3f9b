<%@page contentType="text/html" pageEncoding="UTF-8"%>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />

        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <link rel="icon" href="/sicstv/images/favicon.ico">
        <title><spring:message code="theme.title"/></title>
        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

        <script type="text/javascript" src="/sicstv/js/jquery.timer.js"></script>
        <script type="text/javascript" src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" ></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />

        <script src="https://d3js.org/d3.v5.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.1/d3-tip.min.js"></script>
        <script src="/sicstv/js/sics-canvas.js?<%=System.currentTimeMillis()%>"></script>
        <script src="/sicstv/js/d3-soccer.js?<%=System.currentTimeMillis()%>"></script>

    </head>
    <body>
        <div id="chart"></div>

        <script>
            // Funzione per generare punti casuali all'interno di un rettangolo
//            function generaPuntiCasuali(numeroPunti, larghezzaRettangolo, altezzaRettangolo) {
//                const punti = [];
//                for (let i = 0; i < numeroPunti; i++) {
//                    const x = Math.round(Math.random() * larghezzaRettangolo);
//                    const y = Math.round(Math.random() * altezzaRettangolo);
//                    punti.push({x: x, y: y});
//                }
//                return punti;
//            }
//
//            // Dimensioni del rettangolo
//            const larghezzaRettangolo = 400;
//            const altezzaRettangolo = 250;
//
//            // Numero di punti casuali da generare
//            const numeroPunti = 500;
//
//            // Genera punti casuali all'interno del rettangolo
//            const datiCasuali = generaPuntiCasuali(numeroPunti, larghezzaRettangolo, altezzaRettangolo);
//
//            // Funzione di interpolazione lineare tra due punti
//            function interpolateLinear(p1, p2, t) {
//                var x = p1.x + (p2.x - p1.x) * t;
//                var y = p1.y + (p2.y - p1.y) * t;
//                var value = p1.value + (p2.value - p1.value) * t;
//                return {x: x, y: y, value: value};
//            }
//
//            // Genera nuovi punti intermedi tramite interpolazione lineare
//            function generateInterpolatedData(data, numPoints) {
//                var interpolatedData = [];
//
//                for (var i = 0; i < data.length - 1; i++) {
//                    for (var j = 0; j < numPoints; j++) {
//                        var t = j / numPoints;
//                        var interpolatedPoint = interpolateLinear(data[i], data[i + 1], t);
//
//                        // Controlla se il punto generato rientra nei limiti dell'area SVG
//                        if (interpolatedPoint.x >= 10 && interpolatedPoint.x <= 400 &&
//                                interpolatedPoint.y >= 10 && interpolatedPoint.y <= 250) {
//                            interpolatedData.push(interpolatedPoint);
//                        }
//                    }
//                }
//
//                return interpolatedData;
//            }
//
//            // Usa la funzione per generare dati intermedi
//            var newData = generateInterpolatedData(datiCasuali, 10);
//
//            // Usa d3.nest per raggruppare i dati per i valori di i e j
//            var raggruppati = d3.nest()
//                    .key(function (d) {
//                        return Math.round(d.x) + "_" + Math.round(d.y);
//                    })
//                    .entries(newData);
//
//            // Converte i dati raggruppati in un array di oggetti per la heatmap
//            var heatmapData = raggruppati.map(function (gruppo) {
//                const [i, j] = gruppo.key.split('_').map(Number);
//                return {
//                    height: 0.99,
//                    width: 0.99,
//                    x: i,
//                    y: j,
//                    i: i,
//                    j: j,
//                    value: gruppo.values.length // Utilizza la lunghezza del gruppo come valore
//                };
//            });

//            function generaPunti(datiOriginali) {
//                // Inizializza un array per contenere tutti i punti generati
//                const puntiGenerati = [];
//
//                // Aggiungi tutti i punti forniti in datiOriginali
//                datiOriginali.forEach(puntoOriginale => {
//                    puntiGenerati.push({
//                        height: 5,
//                        width: 5,
//                        x: puntoOriginale.x,
//                        y: puntoOriginale.y,
//                        i: Math.floor(puntoOriginale.x / 5),
//                        j: Math.floor(puntoOriginale.y / 5),
//                        value: 1
//                    });
//                });
//
//                // Calcola il numero di celle lungo l'asse x e y
//                const numCelleX = Math.ceil(105 / 5);
//                const numCelleY = Math.ceil(68 / 5);
//
//                // Inizializza una matrice per tenere traccia del numero di punti nelle vicinanze di ogni cella
//                const puntiVicini = Array(numCelleY).fill().map(() => Array(numCelleX).fill(0));
//
//                // Conta i punti nelle vicinanze di ogni cella
//                puntiGenerati.forEach(punto => {
//                    puntiVicini[punto.j][punto.i]++;
//                });
//
//                // Per ogni cella
//                for (let j = 0; j < numCelleY; j++) {
//                    for (let i = 0; i < numCelleX; i++) {
//                        // Verifica se c'è già un punto per questa cella
//                        const puntoEsistente = puntiGenerati.find(punto => punto.i === i && punto.j === j);
//
//                        // Se non c'è un punto esistente, aggiungine uno con valore pari al numero di punti nelle vicinanze
//                        if (!puntoEsistente) {
//                            const x = i * 5;
//                            const y = j * 5;
//
//                            puntiGenerati.push({
//                                height: 5,
//                                width: 5,
//                                x: x,
//                                y: y,
//                                i: i,
//                                j: j,
//                                value: puntiVicini[j][i]
//                            });
//                        }
//                    }
//                }
//
//                // Ritorna l'array dei punti generati
//                return puntiGenerati;
//            }

            function generateCircleCoordinates(point) {
                var circleCoordinates = [];

                // Aggiungi e sottrai 5 punti sia alle coordinate x che alle coordinate y
                for (var i = -5; i <= 5; i++) {
                    for (var j = -5; j <= 5; j++) {
                        if (i !== 0 || j !== 0) { // Evita di aggiungere il punto stesso
                            var newPointValue = calculateValue(point, {x: point.x + i, y: point.y + j});
                            newPointValue = newPointValue * point.value;
                            var newPoint = {
                                x: point.x + i,
                                y: point.y + j,
                                value: newPointValue
                            };
                            // Verifica se le nuove coordinate cadono all'interno dei limiti
                            if (newPoint.x >= 0 && newPoint.x <= 104 && newPoint.y >= 0 && newPoint.y <= 67) {
                                circleCoordinates.push(newPoint);
                            }
                        }
                    }
                }

                return circleCoordinates;
            }

            function calculateValue(basePoint, currentPoint) {
                // Calcola la distanza euclidea tra il punto base e il punto corrente
                var distance = Math.sqrt(Math.pow(basePoint.x - currentPoint.x, 2) + Math.pow(basePoint.y - currentPoint.y, 2));
                // Calcola il valore basato sulla distanza, limitato tra 0 e 1
                var value = Math.max(0, 1 - (distance / 6));
                return value;
            }

            function removeDuplicates(points) {
                var uniquePoints = [];
                var visited = {}; // Memorizza il valore massimo per ogni coppia di coordinate

                points.forEach(function (point) {
                    var key = point.x + ',' + point.y;
                    if (!(key in visited)) {
                        visited[key] = point.value;
                        uniquePoints.push(point);
                    } else if (visited[key] < point.value) {
                        // rimuovo punto precedente
                        uniquePoints = uniquePoints.filter(function(element) { return element.x !== point.x || element.y !== point.y; });
                        
                        visited[key] = point.value;
                        uniquePoints.push(point);
                    }
                });

                return uniquePoints;
            }

            // Dati originali dei punti
            var datiOriginali = [
                {x: 0, y: 0, value: 0.01},
                {x: 15, y: 20, value: 1},
                {x: 17, y: 20, value: 1},
                {x: 18, y: 20, value: 1},
                {x: 18, y: 23, value: 1},
                {x: 104, y: 67, value: 0.01}
            ];
            datiOriginali.forEach(puntoOriginale => {
                var datiGenerati = generateCircleCoordinates(puntoOriginale);
                datiGenerati.forEach(puntoGenerato => {
                    datiOriginali.push(puntoGenerato);
                });
            });
            datiOriginali = removeDuplicates(datiOriginali);

            // Converti i punti originali nel formato richiesto per la heatmap
            var heatmapData = datiOriginali.map(function (punto, indice) {
                return {
                    height: 1, // Altezza della cella
                    width: 1, // Larghezza della cella
                    x: punto.x, // Posizione x del punto
                    y: punto.y, // Posizione y del punto
                    i: punto.x, // Indice x del punto
                    j: punto.y, // Indice y del punto
                    value: punto.value    // Valore del punto
                };
            });

            //var heatmapData = generaPunti(datiOriginali);
            var maxDataValue = d3.max(heatmapData, function (d) {
                return d.value;
            });

            var pitch = d3.pitch()
                    .rotate(false);
            var heatmap = d3.heatmap(pitch)
                    .colorScale(d3.scaleSequential(d3.interpolateViridis).domain([0, maxDataValue]))
                    .enableInteraction(true)
                    .interpolate(true);

            d3.select("#chart")
                    .datum(heatmapData)
                    .call(heatmap);
        </script>
    </body>
</html>
