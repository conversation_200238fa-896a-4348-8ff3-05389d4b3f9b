<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />

        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <link rel="icon" href="/sicstv/images/favicon.ico">
        <title><spring:message code="theme.title"/></title>
        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

        <script type="text/javascript" src="/sicstv/js/jquery.timer.js"></script>
        <script type="text/javascript" src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" ></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />


    </head>
    <body>
        <%@ include file="header.jsp" %>
        <div id='breadcrumb' class='uk-width-1-1'><span class="data"><b>${title}</b></span></div>

        <div class="videoContainer uk-text-center" style="max-width: 1280px; margin:auto; ">
            <video id="media_player1" class="uk-width-1-1" controls autoplay muted>
                <source src="${videoname}" type='video/mp4'>
            </video>
        </div>
        <div class="videoContainer uk-text-center" style="max-width: 1280px; margin:auto; ">
            <div id="playlistInfo" class="uk-margin-bottom">
                <ul id="matchDataTab" class="uk-tab uk-tab-grid uk-tab-bottom uk-margin-small-left uk-float-left" data-uk-tab="{connect:'#bottomNavContent'}">
                    <li><a href="#"  class="uk-active"><spring:message code="playlist.informazioni"/></a></li>
                </ul>
                <ul id="bottomNavContent" class="uk-switcher uk-margin-left uk-clear-both" style="text-transform: uppercase;">
                    <li>
                        <table id="infoTable">
                            <tr class="uk-margin-top">
                                <td class="uk-text-bold"><c:if test="${mScaduto=='false'}"><spring:message code="video.descrizione" /></c:if></td>
                                <td><c:if test="${mScaduto=='false'}">${title}</c:if></td>
                                </tr>
                                <tr class="uk-margin-top">
                                    <td class="uk-text-bold uk-text-danger uk-text-large">
                                    <c:if test="${mScaduto=='true'}"><spring:message code="link.expired" /></c:if>
                                    <c:if test="${mScaduto!='false' && mScaduto!='true'}"><spring:message code="link.error" /></c:if>
                                    </td>
                                </tr>
                            <c:if test="${scadenza!=''}">
                                <tr class="uk-margin-top">
                                    <td class="uk-text-bold"><spring:message code="link.expirationDate" /></td>
                                    <td>${scadenza}</td>
                                </tr>
                            </c:if>
                        </table>
                    </li>
                </ul>
            </div>
        </div>

    </body>
</html>
