<%@page contentType="text/html" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />

        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
        <%@ taglib prefix="display" uri="http://displaytag.sf.net" %>
        <link rel="icon" href="/sicstv/images/favicon.ico">
        <title><spring:message code="theme.title"/></title>
        <script type="text/javascript" src="/sicstv/js/jquery-1.8.0.min.js"></script>
        <script type="text/javascript" src="/sicstv/uikit/js/uikit.min.js"></script>

        <script type="text/javascript" src="/sicstv/js/jquery.timer.js"></script>
        <script type="text/javascript" src="/sicstv/js/utils.js?<%=System.currentTimeMillis()%>" ></script>

        <link rel="stylesheet" href="/sicstv/uikit/css/default.css?<%=System.currentTimeMillis()%>" />
        <link rel="stylesheet" href="/sicstv/css/sics.css?<%=System.currentTimeMillis()%>" />
        <script>
            function backToHome() {
                window.location.replace("/sicstv/user/home.htm");
            }
        </script>

    </head>
    <body>
        <%@ include file="../user/header.jsp" %>
        <div id="breadcrumb">
            <ul class="uk-breadcrumb uk-margin-left">
                <li style="text-transform:uppercase;" id="liBreadcrumb"><spring:message code="404.title"/></li>
            </ul>
        </div>
        
        <div class="uk-container uk-text-center uk-width-1-1 uk-vertical-align uk-height-viewport">
            <div class="uk-vertical-align-middle">
                <div class="uk-text-bold uk-text-large uk-margin-bottom">
                    <img src="/sicstv/<spring:message code='theme.logo'/>"/>
                </div>
                <div class="uk-text-bold uk-text-large uk-margin-top">
                    <p><spring:message code="404.title"/></p>
                </div>
                <div class="uk-text-large">
                    <p><spring:message code="404.description"/></p>
                </div>
                <div class="uk-text-bold uk-text-large" onclick="backToHome();">
                    <a href="/sicstv/user/home.htm"><spring:message code="error.go.home"/></a>
                </div>
            </div>
        </div>

    </body>
</html>
