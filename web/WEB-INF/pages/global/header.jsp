<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!-- Main navbar -->
<div class="navbar navbar-dark navbar-expand-lg navbar-static">
    <div class="container-fluid">
        <div class="navbar-brand flex-1 flex-lg-0">
            <a href="/sicsmanager/user/home.htm" class="d-inline-flex align-items-center">
                <img src="/sicsmanager/images/logo_white.png" class="d-none d-sm-inline-block h-16px ms-3" alt="">
            </a>
        </div>

        <ul class="nav flex-row justify-content-end order-1 order-lg-2">
            <li class="nav-item nav-item-dropdown-lg dropdown ms-lg-2">
                <a href="#" class="navbar-nav-link align-items-center rounded-pill p-1" data-bs-toggle="dropdown">
                    <i class="ph-user-circle me-2"></i>
                    <span class="d-none d-lg-inline-block mx-lg-2">${mUser.firstName}</span>
                </a>

                <div class="dropdown-menu dropdown-menu-end">
                    <a href="/sicsmanager/auth/logout.htm" class="dropdown-item">
                        <i class="ph-sign-out me-2"></i>
                        Logout
                    </a>
                </div>
            </li>
        </ul>
    </div>
</div>
<!-- /main navbar -->

<!-- Navigation -->
<div class="navbar navbar-sm shadow">
    <div class="container-fluid">
        <div class="flex-fill overflow-auto overflow-lg-visible scrollbar-hidden">
            <ul class="nav gap-1 flex-nowrap flex-lg-wrap align-items-center">
                <li class="nav-item">
                    <a id="headerHomeButton" href="/sicsmanager/user/home.htm" class="navbar-nav-link rounded">
                        <i class="ph-house me-2"></i>
                        Home
                    </a>
                </li>
                <c:if test="${mUser.hasAccess(1)}">
                    <li class="nav-item">
                        <a id="headerAssignButton" href="/sicsmanager/user/assignGame.htm" class="navbar-nav-link rounded">
                            <i class="ph-book me-2"></i>
                            Assign Game
                        </a>
                    </li>
                </c:if>
                <c:if test="${mUser.hasAccess(2)}">
                    <li class="nav-item">
                        <a id="trialRequestsButton" href="/sicsmanager/user/trialTv.htm" class="navbar-nav-link rounded">
                            <i class="ph-note-pencil me-2"></i>
                            Trial Requests
                        </a>
                    </li>
                </c:if>
                <c:if test="${mUser.hasAccess(3)}">
                    <li class="nav-item">
                        <a id="assignTeamRequestsButton" href="/sicsmanager/user/assignTeamTv.htm" class="navbar-nav-link rounded">
                            <i class="ph-user-plus me-2"></i>
                            Match Report
                        </a>
                    </li>
                </c:if>
                <c:if test="${mUser.hasAccess(4)}">
                    <li class="nav-item">
                        <a id="ordersButton" href="/sicsmanager/user/orders.htm" class="navbar-nav-link rounded">
                            <i class="ph-receipt me-2"></i>
                            Orders
                        </a>
                    </li>
                </c:if>
                <c:if test="${mUser.hasAccess(5)}">
                    <li class="nav-item">
                        <a id="vtigerErrorsButton" href="/sicsmanager/user/vtigerErrors.htm" class="navbar-nav-link rounded">
                            <i class="ph-warning me-2"></i>
                            CRM Problems
                        </a>
                    </li>
                </c:if>
                <c:if test="${mUser.hasAccess(6)}">
                    <li class="nav-item">
                        <a id="groupsetsButton" href="/sicsmanager/user/groupset.htm" class="navbar-nav-link rounded">
                            <i class="ph-address-book me-2"></i>
                            Groupsets
                        </a>
                    </li>
                </c:if>
                <c:if test="${mUser.hasAccess(7)}">
                    <li class="nav-item">
                        <a id="logCheckerButton" href="/sicsmanager/user/logChecker.htm" class="navbar-nav-link rounded">
                            <i class="ph-identification-card me-2"></i>
                            Tv Report
                        </a>
                    </li>
                </c:if>
                <c:if test="${mUser.hasAccess(8)}">
                    <li class="nav-item">
                        <a id="translationsButton" href="/sicsmanager/user/translations.htm" class="navbar-nav-link rounded">
                            <i class="ph-translate me-2"></i>
                            Translations
                        </a>
                    </li>
                </c:if>
            </ul>
        </div>
    </div>
</div>
<!-- /navigation -->