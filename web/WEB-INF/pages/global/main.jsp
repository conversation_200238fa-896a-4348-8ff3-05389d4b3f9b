<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
%>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="/sicstv/images/favicon.ico">
    <title><spring:message code="theme.title"/></title>

    <!-- Global stylesheets -->
    <link href="https://server.sics.it/static/theme/b/1/4.0/assets/fonts/inter/inter.css?<%=System.currentTimeMillis()%>" rel="stylesheet" type="text/css">
    <link href="https://server.sics.it/static/theme/b/1/4.0/assets/icons/phosphor/styles.min.css?<%=System.currentTimeMillis()%>" rel="stylesheet" type="text/css">
    <link href="https://server.sics.it/static/theme/b/1/4.0/assets/icons/icomoon/styles.min.css?<%=System.currentTimeMillis()%>" rel="stylesheet" type="text/css">
    <link href="https://server.sics.it/static/theme/b/1/4.0/html/layout_1/full/assets/css/ltr/all.min.css?<%=System.currentTimeMillis()%>" id="stylesheet" rel="stylesheet" type="text/css">
    <!-- /global stylesheets -->

    <!-- Mandano in 404: bootstrap.bundle.min.js, moment.min.js, noty.min.js -->
    
    <!-- Core JS files -->
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/bootstrap/bootstrap.bundle.min.js?<%=System.currentTimeMillis()%>"></script>
    <!-- /core JS files -->

    <!-- Theme JS files -->
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/jquery/jquery.min.js?<%=System.currentTimeMillis()%>"></script>
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/forms/validation/validate.min.js?<%=System.currentTimeMillis()%>"></script>
    <!--<script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/forms/validation/localization/messages_it.min.js?<%=System.currentTimeMillis()%>"></script>-->
    <script src="https://server.sics.it/static/theme/b/1/4.0/html/layout_1/full/assets/js/app.js?<%=System.currentTimeMillis()%>"></script>

    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/forms/selects/select2.min.js?<%=System.currentTimeMillis()%>"></script>
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/forms/selects/bootstrap_multiselect.js?<%=System.currentTimeMillis()%>"></script>
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/visualization/d3/d3.min.js?<%=System.currentTimeMillis()%>"></script>
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/visualization/d3/d3_tooltip.js?<%=System.currentTimeMillis()%>"></script>
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/visualization/echarts/echarts.min.js?<%=System.currentTimeMillis()%>"></script>

    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/ui/moment/moment.min.js?<%=System.currentTimeMillis()%>"></script>
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/pickers/daterangepicker.js?<%=System.currentTimeMillis()%>"></script>
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/pickers/datepicker.min.js?<%=System.currentTimeMillis()%>"></script>

    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/forms/wizards/steps.min.js?<%=System.currentTimeMillis()%>"></script>
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/demo/pages/components_offcanvas.js?<%=System.currentTimeMillis()%>"></script>
    <!-- /theme JS files -->

    <!-- BlockUI JS files -->
    <script src="/sicstv/js/blockUI.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
    <!-- /blockui JS files -->

    <!-- Datatable JS files -->
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/tables/datatables/datatables.min.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/tables/datatables/extensions/responsive.min.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>
    <script src="/sicstv/js/datatable-date-euro.js"></script>
    <!-- /datatable JS files -->

    <!-- Noty JS files -->
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/notifications/noty.min.js?<%=System.currentTimeMillis()%>"></script>
    <!-- /noty js files -->

    <!-- Sweet Alert Js files -->
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/js/vendor/notifications/sweet_alert.min.js?<%=System.currentTimeMillis()%>"></script>
    <!-- /sweet alert js files -->

    <!-- JQuery Timer JS files -->
    <script type="text/javascript" src="/sicstv/js/jquery.timer.js?<%=System.currentTimeMillis()%>"></script>
    <!-- /jquery timer JS files -->

    <!-- Datepicker JS files -->
    <script src="https://server.sics.it/static/theme/b/1/4.0/assets/demo/pages/picker_date.js?<%=System.currentTimeMillis()%>"></script>
    <!-- /datepicker JS files -->

    <!-- Handsontable files -->
    <!--    <link href="/sicstv/css/handsontable.css" rel="stylesheet" type="text/css">
        <script src="/sicstv/js/handsontable.min.js?<%=System.currentTimeMillis()%>" type="text/javascript"></script>-->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/handsontable/dist/handsontable.full.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/handsontable/dist/handsontable.full.min.css"/>
    <!-- /handsontable files -->

    <!-- Dropzone files -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/dropzone.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.2/min/dropzone.min.js"></script>
    <!-- /dropzone files -->

    <script type="text/javascript">
        var sweetAlert;

        function checkSession() {
            $.ajax({
                type: "GET",
                url: "/sicstv/user/isValidSession.htm",
                cache: false,
                success: function (msg) {
                    if (msg === "askClearSession") {
                        window.location.href = "/sicstv/auth/multipleSession.htm";
                    } else if (msg === "expired") {
                        window.location.href = "/sicstv/auth/login.htm?expired=true";
                    } else if (msg !== "true") {
                        window.location.href = "/sicstv/auth/login.htm?session=disposed";
                    }

                    setTimeout(function () {
                        checkSession();
                    }, 5000);
                }
            });
        }

        $(document).ready(function () {
            if (!document.URL.includes("/auth/")) {
                checkSession();
            }

            // Default initialization
            $('.select').select2();
            $(document).on('select2:open', () => {
                document.querySelector('.select2-search__field').focus();
            });

            $('.multiselect').multiselect({
                onDropdownShown: function () {
                    this.$filter.find('.multiselect-search').focus();
                }
            });
            // fix css per search
            $('.multiselect-search').css('padding-left', 'calc(0.875rem * 2 + 1.25rem)');

            // Override Noty defaults
            Noty.overrideDefaults({
                theme: 'limitless',
                layout: 'topRight',
                type: 'alert',
                timeout: 2500
            });

            // Defaults
            sweetAlert = swal.mixin({
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-primary',
                    cancelButton: 'btn btn-light',
                    denyButton: 'btn btn-light',
                    input: 'form-control'
                }
            });

            // Default initialization datepicker
            // Single picker
            $('.daterange-single').daterangepicker({
                parentEl: '.content-inner',
                singleDatePicker: true
            });
        });

        window.addEventListener('beforeunload', function (event) {
            // questo evento viene richiamato ogni volta prima che si esce dalla pagina
            // quindi sia per navigazione avanti e indietro, che per reload della pagina
            showBlockUI();
        });

        function showBlockUI() {
            $.blockUI({
                message: '<i class="icon-spinner4 spinner"></i>',
                overlayCSS: {
                    backgroundColor: '#1b2024',
                    opacity: 0.8,
                    cursor: 'wait'
                },
                css: {
                    border: 0,
                    color: '#fff',
                    padding: 0,
                    backgroundColor: 'transparent'
                }
            });
        }

        function closeBlockUI() {
            $.unblockUI();
        }

        function removeParamFromURL(url, paramName, paramValue) {
            url = url.replace("?" + paramName + "=" + paramValue, "");
            url = url.replace("&" + paramName + "=" + paramValue, "");
            return url;
        }

        function replaceParamFromURL(url, paramName, oldValue, newValue) {
            return url = url.replace(paramName + "=" + oldValue, paramName + "=" + newValue);
        }

        function addParamToURL(url, paramName, paramValue) {
            if (url.includes("?")) {
                url += "&" + paramName + "=" + paramValue;
            } else {
                url += "?" + paramName + "=" + paramValue;
            }
            return url;
        }

        function blockElement(element) {
            $(element).block({
                message: '<i class="icon-spinner4 spinner"></i>',
                overlayCSS: {
                    backgroundColor: '#ffffff',
                    opacity: 0.8,
                    cursor: 'wait'
                },
                css: {
                    width: 'auto',
                    border: 0,
                    padding: 0
                }
            });
        }

        function reloadTooltips() {
            // https://getbootstrap.com/docs/5.0/components/tooltips/
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-popup="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
    </script>
</head>

<!-- Header Body content -->

<!-- /header body content -->