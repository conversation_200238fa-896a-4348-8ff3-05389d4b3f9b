<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>

    <!--settings>
            <setting name="lazyLoadingEnabled=" value="true"/>
    </settings-->

    <!--settings>
            <setting name="cacheEnabled" value="true"/>
            <setting name="lazyLoadingEnabled" value="false"/>
            <setting name="multipleResultSetsEnabled" value="true"/>
            <setting name="useColumnLabel" value="true"/>
            <setting name="useGeneratedKeys" value="false"/>
            <setting name="defaultExecutorType" value="SIMPLE"/>
            <setting name="defaultStatementTimeout" value="25000"/>
    </settings-->

    <typeAliases>
        <typeAlias type="sics.domain.User" alias="User" />
        <typeAlias type="sics.domain.FixturePlaylistSize" alias="FixturePlaylistSize" />
        <typeAlias type="sics.domain.Country" alias="Country" />
        <typeAlias type="sics.domain.InternationalCompetition" alias="InternationalCompetition" />
        <typeAlias type="sics.domain.Competition" alias="Competition" />
        <typeAlias type="sics.domain.Season" alias="Season" />
        <typeAlias type="sics.domain.Playlist" alias="Playlist" />
        <typeAlias type="sics.domain.PlaylistData" alias="PlaylistData" />
        <typeAlias type="sics.domain.Game" alias="Game" />
        <typeAlias type="sics.domain.GameData" alias="GameData" />
        <typeAlias type="sics.domain.LogData" alias="LogData" />
        <typeAlias type="sics.domain.Team" alias="Team" />
        <typeAlias type="sics.domain.Atleta" alias="Atleta" />
        <typeAlias type="sics.domain.Fascia" alias="Fascia" />
        <typeAlias type="sics.domain.Intervallo" alias="Intervallo" />
        <typeAlias type="sics.domain.Tags" alias="Tags" />
        <typeAlias type="sics.domain.Button" alias="Button" />
        <typeAlias type="sics.model.Azione" alias="Azione" />
        <typeAlias type="sics.domain.Modulo" alias="Modulo" />
        <typeAlias type="sics.domain.CalendarRange" alias="CalendarRange" />
        <typeAlias type="sics.domain.PlaylistTV" alias="PlaylistTV" />
        <typeAlias type="sics.domain.PlaylistTVEvent" alias="PlaylistTVEvent" />
        <typeAlias type="sics.domain.Groupset" alias="Groupset" />
        <typeAlias type="sics.domain.PlaylistTVShare" alias="PlaylistTVShare" />
        <typeAlias type="sics.domain.PlaylistTVSharePublic" alias="PlaylistTVSharePublic" />
        <typeAlias type="sics.domain.TeamStats" alias="TeamStats" />
        <typeAlias type="sics.domain.TeamStatsFilter" alias="TeamStatsFilter" />
        <typeAlias type="sics.domain.TeamStatsFilterDetail" alias="TeamStatsFilterDetail" />
        <typeAlias type="sics.domain.StatsType" alias="StatsType" />
        <typeAlias type="sics.domain.TeamReportRanking" alias="TeamReportRanking" />
        <typeAlias type="sics.domain.Position" alias="Position" />
        <typeAlias type="sics.domain.ChartData" alias="ChartData" />
        <typeAlias type="sics.domain.Watchlist" alias="Watchlist" />
        <typeAlias type="sics.domain.WatchlistDetail" alias="WatchlistDetail" />
        <typeAlias type="sics.domain.WatchlistShare" alias="WatchlistShare" />
        <typeAlias type="sics.domain.Favorites" alias="Favorites" />
        <typeAlias type="sics.domain.Shortcut" alias="Shortcut" />
        <typeAlias type="sics.domain.Settings" alias="Settings" />
        <typeAlias type="sics.domain.VtigerAgent" alias="VtigerAgent" />
        <typeAlias type="sics.domain.Export" alias="Export" />
        <typeAlias type="sics.domain.ExportDetail" alias="ExportDetail" />
        <typeAlias type="sics.domain.FixtureDetails" alias="FixtureDetails" />
        <typeAlias type="sics.domain.Cache" alias="Cache" />
        <typeAlias type="sics.domain.CacheLanguageItem" alias="CacheLanguageItem" />
        <typeAlias type="sics.domain.UserCompetitionException" alias="UserCompetitionException" />
        <typeAlias type="sics.domain.ShadowTeam" alias="ShadowTeam" />
        <typeAlias type="sics.domain.ShadowTeamDetail" alias="ShadowTeamDetail" />
        <typeAlias type="sics.domain.PlayerAgent" alias="PlayerAgent" />
    </typeAliases>

    <mappers>
        <mapper resource="sics/dao/mapper/User.xml" />
        <mapper resource="sics/dao/mapper/Country.xml" />
        <mapper resource="sics/dao/mapper/InternationalCompetition.xml" />
        <mapper resource="sics/dao/mapper/Competition.xml" />
        <mapper resource="sics/dao/mapper/Season.xml" />
        <mapper resource="sics/dao/mapper/Playlist.xml" />
        <mapper resource="sics/dao/mapper/PlaylistData.xml" />
        <mapper resource="sics/dao/mapper/Game.xml" />
        <mapper resource="sics/dao/mapper/GameData.xml" />
        <mapper resource="sics/dao/mapper/LogData.xml" />
        <mapper resource="sics/dao/mapper/Team.xml" />
        <mapper resource="sics/dao/mapper/Atleta.xml" />
        <mapper resource="sics/dao/mapper/Fascia.xml" />
        <mapper resource="sics/dao/mapper/Intervallo.xml" />
        <mapper resource="sics/dao/mapper/Azione.xml" />
        <mapper resource="sics/dao/mapper/Button.xml" />
        <mapper resource="sics/dao/mapper/Modulo.xml" />
        <mapper resource="sics/dao/mapper/PlaylistTV.xml" />
        <mapper resource="sics/dao/mapper/Groupset.xml" />
        <mapper resource="sics/dao/mapper/TeamStats.xml" />
        <mapper resource="sics/dao/mapper/ChartData.xml" />
        <mapper resource="sics/dao/mapper/Watchlist.xml" />
        <mapper resource="sics/dao/mapper/Favorites.xml" />
        <mapper resource="sics/dao/mapper/Shortcut.xml" />
        <mapper resource="sics/dao/mapper/Settings.xml" />
        <mapper resource="sics/dao/mapper/Vtiger.xml" />
        <mapper resource="sics/dao/mapper/Export.xml" />
        <mapper resource="sics/dao/mapper/Cache.xml" />
        <mapper resource="sics/dao/mapper/ShadowTeam.xml" />
        <mapper resource="sics/dao/mapper/PlayerAgent.xml" />
    </mappers>

</configuration>
