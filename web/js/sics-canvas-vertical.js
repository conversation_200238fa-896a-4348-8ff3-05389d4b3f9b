var colors = {
    green: "#58e043",
    yellow: "#e0e043",
    red: "#e04343",
    black: "#242424",
    orange: "#e0bb43",
    white: "#ffffff"
};

var textColors = {
    white: "#ffffff",
    black: "#000000"
};

var lineColors = {
    black: "#2a2d2a",
    blue: "#3672ff",
    red: "#ff4545",
    yellow: "#eded4a"
};

var basePitchVerticalWidth = 68, basePitchVerticalLength = 105;

var pitchVertical = {
    width: basePitchVerticalWidth / 2.5,
    length: basePitchVerticalLength / 2.5,
    padding: {
        top: 0.5,
        right: 0.5,
        bottom: 0.5,
        left: 0.5
    },
    paintColor: "#FFF",
    grassColor: "#40b36d",
    isDefault: true,
    id: "basePitchVertical"
};

pitchVerticalTwo = {...pitchVertical};
pitchVerticalTwo.id = "basePitchVerticalTwo";

pitchVerticalThree = {...pitchVertical};
pitchVerticalThree.id = "basePitchVerticalThree";

pitchVerticalFour = {...pitchVertical};
pitchVerticalFour.id = "basePitchVerticalFour";
pitchVerticalFour.isTocchi = true;

var multiplier, scale, minDistance;
var scaleSvg, shotsScaleSvg, shotsRaggio = 10.0, raggio = 3;
var realShotsLength = 105;
var tip;

function getPitch(which) {
    if (which === 1) {
        return pitchVertical;
    } else if (which === 2) {
        return pitchVerticalTwo;
    } else if (which === 3) {
        return pitchVerticalThree;
    } else if (which === 4) {
        return pitchVerticalFour;
    }
}

function drawVerticalField(containerId, which) {
    var pitchElement = getPitch(which);

    var tmpMultiplier = pitchElement.length / realShotsLength;
    var tmpScale = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500 * tmpMultiplier]);

    multiplier = tmpMultiplier + 0.6; // 0.4 di base, aggiungo 0.6 per arrivare a 1 di base
    scale = tmpScale;

    minDistance = scale(2.3); // distanza dipendente dalla dimensione del field

    scaleSvg = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500]);

    var baseBezier = 50 * tmpMultiplier;

    // rimuovo precedente se esistente
    d3.select("#" + pitchElement.id).remove();
    var svg = d3.select("#" + containerId).append("svg")
            .attr("width", scaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right))
            .attr("height", scaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom))
            .attr("style", "background:" + pitchElement.grassColor + "!important")
            .attr("id", pitchElement.id);

    var baseLayer = svg.append("g")
            .attr("data-index", "0")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

    // fasce alternate verde chiaro e verde scuro
    var stripeHeight = pitchElement.width / 8; // Altezza delle fasce
    var numStripes = Math.floor(scaleSvg(pitchElement.width) / scaleSvg(stripeHeight));

    if (pitchElement.isDefault) {
        for (var i = 0; i < numStripes; i++) {
            var stripeColor = i % 2 === 0 ? "#46ac6d" : "#40b36d"; // Alterna tra verde chiaro e scuro

            baseLayer.append("rect")
                    .attr("x", 0)
                    .attr("y", scaleSvg(i * stripeHeight))
                    .attr("width", scaleSvg(pitchElement.width))
                    .attr("height", scaleSvg(stripeHeight))
                    .attr("fill", stripeColor);
        }
    }

    // linee dei campi
    baseLayer.append("rect")
            .attr("x", 0)
            .attr("y", 0)
            .attr("width", scaleSvg(pitchElement.width))
            .attr("height", scaleSvg(pitchElement.length))
            .attr("stroke", pitchElement.paintColor)
            .attr("fill", "none");
    if (typeof pitchElement.isTocchi === "undefined" || !pitchElement.isTocchi) {
        baseLayer.append("circle")
                .attr("cx", scaleSvg(pitchElement.width / 2))
                .attr("cy", scaleSvg(pitchElement.length / 2))
                .attr("r", 2)
                .attr("fill", pitchElement.paintColor);

        baseLayer.append("circle")
                .attr("cx", scaleSvg(pitchElement.width / 2))
                .attr("cy", scaleSvg(pitchElement.length / 2))
                .attr("r", baseBezier)
                .attr("fill", 'none')
                .attr("stroke", pitchElement.paintColor);

        baseLayer.append("line")
                .attr("y1", scaleSvg(pitchElement.length / 2))
                .attr("y2", scaleSvg(pitchElement.length / 2))
                .attr("x1", scaleSvg(pitchElement.width))
                .attr("x2", 0)
                .attr("stroke", pitchElement.paintColor);
    } else {
        // Terzo Offensivo
        baseLayer.append("line")
                .attr("y1", scaleSvg(pitchElement.length / 3))
                .attr("y2", scaleSvg(pitchElement.length / 3))
                .attr("x1", scaleSvg(pitchElement.width))
                .attr("x2", 0)
                .attr("stroke-dasharray", "2,2")
                .attr("stroke", pitchElement.paintColor);

        // Terzo Difensivo
        baseLayer.append("line")
                .attr("y1", scaleSvg(pitchElement.length / 3 * 2))
                .attr("y2", scaleSvg(pitchElement.length / 3 * 2))
                .attr("x1", scaleSvg(pitchElement.width))
                .attr("x2", 0)
                .attr("stroke-dasharray", "2,2")
                .attr("stroke", pitchElement.paintColor);

        for (var i = 1; i < 5; i++) {
            baseLayer.append("line")
                    .attr("y1", scaleSvg(pitchElement.length))
                    .attr("y2", 0)
                    .attr("x1", scaleSvg(pitchElement.width / 5 * i))
                    .attr("x2", scaleSvg(pitchElement.width / 5 * i))
                    .attr("stroke-dasharray", "2,2")
                    .attr("stroke", pitchElement.paintColor);

            baseLayer.append("text")
                    .attr("x", function () {
                        return scaleSvg(pitchElement.width / 5 * i) - scaleSvg(pitchElement.width / 5 / 2);
                    })
                    .attr("y", function () {
                        return scaleSvg(1.75);
                    })  // Regola la posizione del testo verticalmente
                    .attr("text-anchor", "middle")  // Centra il testo rispetto al cerchio
                    .attr("fill", colors.white)  // Colore del testo
                    .attr("font-size", calculateTextSize(10))
                    .text(function () {
                        return "C" + i;
                    });
        }

        // testo C5
        baseLayer.append("text")
                .attr("x", function () {
                    return scaleSvg(pitchElement.width / 5 * 5) - scaleSvg(pitchElement.width / 5 / 2);
                })
                .attr("y", function () {
                    return scaleSvg(1.75);
                })  // Regola la posizione del testo verticalmente
                .attr("text-anchor", "middle")  // Centra il testo rispetto al cerchio
                .attr("fill", colors.white)  // Colore del testo
                .attr("font-size", calculateTextSize(10))
                .text(function () {
                    return "C5";
                });
    }

    // corners
    function addPath(pathData, parentElement, size) {
        var path = parentElement.append("path")
                .attr("d", pathData)
                .attr("stroke", pitchElement.paintColor)
                .attr("fill", "none");

        if (typeof size !== 'undefined') {
            path.attr("stroke-width", size);
        }
    }

    // top left
    var pathData = "M0," + scaleSvg(1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(1) + ",0";
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + ",0 A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(pitchElement.width) + "," + scaleSvg(1);
    addPath(pathData, baseLayer);

    // bottom left
    pathData = "M0," + scaleSvg(pitchElement.length - 1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(1) + "," + scaleSvg(pitchElement.length);
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + "," + scaleSvg(pitchElement.length) + " A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(pitchElement.width) + "," + scaleSvg(pitchElement.length - 1);
    addPath(pathData, baseLayer);

    // Top Penalty Area
    var penaltyAreaTop = baseLayer.append("g");
//    pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54);
    pathData = "M" + tmpScale(14) + ",0L" + tmpScale(14) + "," + tmpScale(16.5) + "L" + tmpScale(54) + "," + tmpScale(16.5) + "L" + tmpScale(54) + ",0";
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Area
    pathData = "M" + tmpScale(24.84) + ",0L" + tmpScale(24.84) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + ",0";
    addPath(pathData, penaltyAreaTop);

    // Top D
    pathData = "M" + tmpScale(42) + "," + tmpScale(16.5) + "A " + baseBezier + " " + baseBezier + " 5 0 1 " + tmpScale(26) + "," + tmpScale(16.5);
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Spot
    penaltyAreaTop.append("circle")
            .attr("cx", tmpScale(34))
            .attr("cy", tmpScale(11))
            .attr("r", 1)
            .attr("fill", pitchElement.paintColor)
            .attr("stroke", pitchElement.paintColor);

    var penaltyAreaBottom = baseLayer.append("g");
    penaltyAreaBottom.html(penaltyAreaTop.html());
    penaltyAreaBottom.attr("transform", "rotate(180) translate(-" + scaleSvg(pitchElement.width) + ",-" + scaleSvg(pitchElement.length) + ")");

    pitchElement.baseLayer = baseLayer;

    var pointLayer = svg.append("g")
            .attr("data-index", "1")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

    pitchElement.pointLayer = pointLayer;

    if (typeof tip === "undefined") {
        tip = d3.tip()
                .attr('class', 'd3-tip')
                .offset([-10, 0])
                .html(function (d) {
                    return d;
                });
    }
}

function drawVerticalEvent(which, event, normalized, viewModality) {
    var startPos, endPos;
    var pitchElement = getPitch(which);

    if (typeof normalized === 'undefined' || normalized) {
        startPos = event.mActionPosNormalized;
        endPos = event.mActionPosEndNormalized;
    } else {
        startPos = event.mActionPos;
        endPos = event.mActionPosEnd;
    }

    startPos = getVerticalCoord(startPos);
    endPos = getVerticalCoord(endPos);

    var player = event._player;
    var playerTo = event._playerTo;
    var scaledRaggio = raggio * multiplier;

    if (startPos && !startPos.isDefault) {
        var firstPoint = {
            cx: scale(startPos.x),
            cy: scale(startPos.y),
            r: scaledRaggio
        };
        var originalFirstPoint = {...firstPoint};
        var secondPoint, originalSecondPoint;

        if (player) {
            var color = colors.orange;
//            if (event.mTeamColor) {
//                color = event.mTeamColor;
//            }

            pitchElement.pointLayer.append("circle")
                    .attr("cx", firstPoint.cx - scaledRaggio / 2)
                    .attr("cy", firstPoint.cy - scaledRaggio / 2)
                    .attr("r", scaledRaggio / (viewModality === 1 ? 1.2 : 1))
                    .attr("fill", color)
                    .attr("data-event", "event-" + event._id);

            originalFirstPoint.cx = firstPoint.cx - scaledRaggio / 2;
            originalFirstPoint.cy = firstPoint.cy - scaledRaggio / 2;
            firstPoint.cx = firstPoint.cx - scaledRaggio / 2;
            firstPoint.cy = firstPoint.cy - scaledRaggio / 4;

            if (Object.keys(player).length === 1) {
                // solo se singolo giocatore scrivo il numero
                var playerObject = player[Object.keys(player)[0]];
                var number = playerObject.matchNumber;
                if (number) {
                    var color = colors.black;
//                    if (event.mTeamTextColor) {
//                        color = event.mTeamTextColor;
//                    }

                    if (viewModality === 2) {
                        pitchElement.pointLayer.append("text")
                                .attr("x", function () {
                                    return parseFloat(firstPoint.cx);
                                })
                                .attr("y", function () {
                                    return parseFloat(firstPoint.cy);
                                })  // Regola la posizione del testo verticalmente
                                .attr("text-anchor", "middle") // Centro orizzontalmente
                                .attr("alignment-baseline", "middle") // Centro verticalmente
                                .attr("fill", color)  // Colore del testo
                                .attr("font-size", calculateTextNumberSize(number))
                                .text(function () {
                                    return number;
                                })
                                .attr("data-event", "event-" + event._id)
                                .attr("data-tooltip", playerObject.known_name)
                                .call(tip)  // Chiamata per inizializzare d3-tip sul testo
                                .on("mouseover", function (d) {
                                    tip.show(d3.select(this).attr("data-tooltip"), this);
                                })
                                .on("mouseout", function (d) {
                                    tip.hide();
                                });
                    }
                } else {
                    console.warn("drawVerticalEvent()", "Number not found for player " + playerObject.known_name + ". Event id is " + event._id);
                }
            }
        }

        if (endPos && !endPos.isDefault && viewModality !== 4) {
            secondPoint = {
                cx: scale(endPos.x),
                cy: scale(endPos.y),
                r: scaledRaggio
            };
            originalSecondPoint = {...secondPoint};

            if (playerTo) {
                var color = colors.orange;
//                if (event.mTeamColor) {
//                    color = event.mTeamColor;
//                }

                pitchElement.pointLayer.append("circle")
                        .attr("cx", secondPoint.cx - scaledRaggio / 2)
                        .attr("cy", secondPoint.cy - scaledRaggio / 2)
                        .attr("r", scaledRaggio / (viewModality === 2 ? 1.2 : 1))
                        .attr("fill", color)
                        .attr("data-event", "event-" + event._id);

                originalSecondPoint.cx = secondPoint.cx - scaledRaggio / 2;
                originalSecondPoint.cy = secondPoint.cy - scaledRaggio / 2;
                secondPoint.cx = secondPoint.cx - scaledRaggio / 2;
                secondPoint.cy = secondPoint.cy + scaledRaggio / 4;

                var number = playerTo.matchNumber;
                if (number) {
                    var color = colors.black;
                    if (event.mTeamTextColor) {
                        color = event.mTeamTextColor;
                    }

                    if (viewModality === 1) {
                        pitchElement.pointLayer.append("text")
                                .attr("x", function () {
                                    return parseFloat(secondPoint.cx);
                                })
                                .attr("y", function () {
                                    return parseFloat(secondPoint.cy);
                                })  // Regola la posizione del testo verticalmente
                                .attr("text-anchor", "middle")  // Centra il testo rispetto al cerchio
                                .attr("fill", color)  // Colore del testo
                                .attr("font-size", calculateTextNumberSize(number))
                                .text(function () {
                                    return number;
                                })
                                .attr("data-event", "event-" + event._id)
                                .attr("data-tooltip", playerTo.known_name)
                                .call(tip)  // Chiamata per inizializzare d3-tip sul testo
                                .on("mouseover", function (d) {
                                    tip.show(d3.select(this).attr("data-tooltip"), this);
                                })
                                .on("mouseout", function (d) {
                                    tip.hide();
                                });
                    }
                } else {
                    console.warn("drawVerticalEvent()", "Number not found for player " + playerTo.known_name + ". Event id is " + event._id);
                }
            }
        }

        if (firstPoint && secondPoint && viewModality !== 4) {
            var drawArrow = !(scale(event.mActionDistance) <= scale(4));
            connectVerticalPoints(which, originalFirstPoint, originalSecondPoint, drawArrow, event);
        }
    } else {
        if (!event.mType === 'SUB' && !event.mType === 'SOS') {
            console.warn("drawVerticalEvent()", "can't draw event id " + event._id + ". Coords are empty");
        }
    }
}

function connectVerticalPoints(which, firstPoint, secondPoint, drawArrow, event, isConduzione) {
    // console.log(pointLayer, firstPoint, secondPoint, drawArrow);
    var pitchElement = getPitch(which);
    var distance = getPointsDistance(firstPoint, secondPoint);

    if (distance > minDistance) {
        // colore freccia
        var lineColor = getEventColor(event);
        if (typeof isConduzione !== 'undefined' && isConduzione) {
            // conduzione sempre nera
            lineColor = lineColors.black;
        }

        // controllo se c'è bisogno di creare la freccia
        if (typeof drawArrow !== 'undefined' && drawArrow) {
            var isArrowheadPresent = !pitchElement.pointLayer.select("#arrowheadvertical" + lineColor.replace("#", "")).empty();
            if (!isArrowheadPresent) {
                pitchElement.pointLayer.append("defs").append("marker")
                        .attr("id", "arrowheadvertical" + lineColor.replace("#", ""))
                        .attr("refX", 2)
                        .attr("refY", 2)
                        .attr("markerWidth", 3)
                        .attr("markerHeight", 4)
                        .attr("orient", "auto")
                        .attr("isShot", true)
                        .append("path")
                        .attr("d", "M0,0 L3,2 L0,4")
                        .style("fill", lineColor);
            }

            isArrowheadPresent = !pitchElement.pointLayer.select("#arrowheadverticalHighlighted").empty();
            if (!isArrowheadPresent) {
                pitchElement.pointLayer.append("defs").append("marker")
                        .attr("id", "arrowheadverticalHighlighted")
                        .attr("refX", 2)
                        .attr("refY", 2)
                        .attr("markerWidth", 3)
                        .attr("markerHeight", 4)
                        .attr("orient", "auto")
                        .attr("isShot", true)
                        .append("path")
                        .attr("d", "M0,0 L3,2 L0,4")
                        .style("fill", "lime");
            }
        }

        // dati primo punto
        var x1 = parseFloat(firstPoint.cx);
        var y1 = parseFloat(firstPoint.cy);
        var r1 = parseFloat(firstPoint.r);
        // dati secondo punto
        var x2 = parseFloat(secondPoint.cx);
        var y2 = parseFloat(secondPoint.cy);
        var r2 = parseFloat(secondPoint.r);

        var angle = Math.atan2(y2 - y1, x2 - x1);
        var startX = x1 + r1 * Math.cos(angle);
        var startY = y1 + r1 * Math.sin(angle);
        var endX = x2 - (r2) * Math.cos(angle);
        var endY = y2 - (r2) * Math.sin(angle);
//        if (typeof event._playerTo === 'undefined' || !event._playerTo) {
//            if (typeof event.mActionAngle !== 'undefined' && event.mActionAngle) {
//                if ((event.mActionAngle > 90 && event.mActionAngle < 270) || (event.mActionAngle < -90 && event.mActionAngle > -270)) {
//                    endX = x2 + (r2) * Math.cos(angle);
//                    endY = y2 + (r2) * Math.sin(angle);
//                }
//            }
//        }
        if (typeof event._playerTo !== 'undefined' && event._playerTo) {
            endX = x2 - (r2 * 1.5) * Math.cos(angle);
            endY = y2 - (r2 * 1.5) * Math.sin(angle);
        }

        // creo ora la linea
        var line = pitchElement.pointLayer.append("line")
                .attr("x1", startX)
                .attr("y1", startY)
                .attr("x2", endX) // 6 è la lunghezza della freccia
                .attr("y2", endY) // 6 è la lunghezza della freccia
                .attr("stroke", lineColor)
                .attr("isShot", true)
                .attr("stroke-width", 1);
        if (typeof drawArrow !== 'undefined' && drawArrow) {
            line.attr("marker-end", "url(#arrowheadvertical" + lineColor.replace("#", "") + ")");
        }
        if (typeof event !== 'undefined' && event) {
            line.attr("data-event", "event-" + event._id);
        }
        if (typeof isConduzione !== 'undefined' && isConduzione) {
            line.attr("stroke-dasharray", "2,2"); // Imposta un modello di tratteggio (2 pixel di tratto, 2 pixel di spazio)
        }
    }
}

function showTooltip() {
    var tooltipText = d3.select(this).attr("data-tooltip");
    var bbox = this.getBBox(); // Ottieni il bounding box dell'elemento <text>

    tooltip.transition()
            .duration(200)
            .style("opacity", .9)
            .style("left", (bbox.x + bbox.width / 2) + "px")
            .style("top", (bbox.y - 10) + "px");

    tooltip.html(tooltipText)
            .style("left", (bbox.x + bbox.width / 2) + "px")
            .style("top", (bbox.y - 10) + "px");
}

function hideTooltip() {
    tooltip.transition()
            .duration(500)
            .style("opacity", 0);
}

function getVerticalCoord(pos) {
    var newPos = {...pos};
//    if (newPos.x < 52.5) {
//        newPos.y = 68 - newPos.y;
//    } else if (newPos.x > 52.5) {
//        newPos.x = 105 - newPos.x;
//    }
    newPos.x = 105 - newPos.x;
    var tmp = newPos.x;
    newPos.x = newPos.y;
    newPos.y = tmp;

    return newPos;
}

function getPointsDistance(firstPoint, secondPoint) {
    if (firstPoint && !firstPoint.isDefault && secondPoint && !secondPoint.isDefault) {
        var x = firstPoint.cx - secondPoint.cx;
        var y = firstPoint.cy - secondPoint.cy;

        return Math.sqrt(x * x + y * y);
    } else {
        return 0;
    }
}

function getEventColor(event) {
    var lineColor = lineColors.yellow;
//    if (typeof event !== 'undefined' && event) {
//        if (event.mType === 'TIF') { // tiri rossi
//            lineColor = lineColors.red;
//        } else if (event.mType === 'PASS') {
//            event.mTags.forEach((tag) => {
//                if (tag.code && tag.code === 'PASS-0') {
//                    lineColor = lineColors.blue;
//                    return false;
//                }
//            });
//        }
//    }

    return lineColor;
}

/*
 * UTILS
 */
function calculateTextNumberSize(number) {
    var baseSize = 5;

    if (number > 9) {
        baseSize = 5;
    }

    return baseSize * multiplier;
}

function calculateTextSize(number) {
    return number * multiplier;
}

function handleShotsResize() {
    var baseWidth = 1920;

    // Aggiorna le dimensioni del tuo SVG in base alle nuove dimensioni della finestra
    var newWidth = window.innerWidth;

    var newPitchVerticalWidth = basePitchVerticalWidth / baseWidth * newWidth;
    var newPitchVerticalLength = basePitchVerticalLength / basePitchVerticalWidth * newPitchVerticalWidth;
    console.log("handleShotsResize()", "old pitch:", pitchVertical.width, "x", pitchVertical.length, "new pitch:", newPitchVerticalWidth, "x", newPitchVerticalLength);

    pitchVertical.width = newPitchVerticalWidth;
    pitchVertical.length = newPitchVerticalLength;

    drawVerticalField();
    if (typeof zoneShotEventAmount !== "undefined") {
        updateZoneShotCounters();
    }
}