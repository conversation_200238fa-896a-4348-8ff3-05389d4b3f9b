function getVals() {
    // Get slider values
    var parent = this.parentNode;
    var slides = parent.getElementsByTagName("input");
    var slide1 = parseFloat(slides[0].value);
    var slide2 = parseFloat(slides[1].value);
    // Neither slider will clip the other, so make sure we determine which is larger
    if (slide1 > slide2) {
        var tmp = slide2;
        slide2 = slide1;
        slide1 = tmp;
    }

    var displayElement = parent.getElementsByClassName("range-values")[0];
    displayElement.innerHTML = slide1 + " - " + slide2;
}

function updateTrack() {
    const minSlider = document.getElementById('min');
    const maxSlider = document.getElementById('max');
    const track = document.querySelector('.slider-track');
    const trackMin = document.querySelector('.slider-track-min');
    const trackMax = document.querySelector('.slider-track-max');
    const minValue = parseInt(minSlider.value);
    const maxValue = parseInt(maxSlider.value);
    const max = parseInt(minSlider.max);

    const percentMin = (minValue / max) * 100;
    const percentMax = (maxValue / max) * 100;
    const percentMissed = (max - maxValue) / max * 100;

    trackMin.style.width = percentMin + '%';
    trackMax.style.left = percentMax + '%';
    trackMax.style.width = percentMissed + '%';

    track.style.left = percentMin + '%';
    track.style.width = percentMax - percentMin + '%';
}

window.onload = function () {
    // Initialize Sliders
    var sliderSections = document.getElementsByClassName("range-slider");
    for (var x = 0; x < sliderSections.length; x++) {
        var sliders = sliderSections[x].getElementsByTagName("input");
        for (var y = 0; y < sliders.length; y++) {
            if (sliders[y].type === "range") {
                sliders[y].oninput = getVals;
                // Manually trigger event first time to display values
                sliders[y].oninput();
            }
        }
    }

//    document.getElementById('min').addEventListener('input', updateTrack);
//    document.getElementById('max').addEventListener('input', updateTrack);

    // Inizializza la posizione del track
//    updateTrack();
};