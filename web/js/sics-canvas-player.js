var svgFasce, pitch, scale, scaleSvg, multiplier;
var tip;
var currentContainerId;
var raggio = 15.0;
var basePitchWidth = 27.2, basePitchLength = 42;
var realWidth = 68, realLength = 105;
var minDistance;

var posizionaleOptions = {
    startPos: "in", // in = le azioni INIZIANO nel quadrante selezionato, out = le azioni FINISCONO nel quadrante selezionato
    orientation: "vertical"     // vertical, horizontal, grid
};

var lineColors = {
    black: "#2a2d2a",
    blue: "#3672ff",
    red: "#ff4545"
};

var clickColors = {
    base: "#adc274",
    from: "#0040ff44",
    to: "#00ddff44",
    fromTo: "#ea00ff44",

    fromAngle: "#5e6fa2",
    toAngle: "#5e99a2",
    fromToAngle: "#9c5ea2"
};

var colors = {
    orange: "#f27e46cc"
};

pitch = {
    width: basePitchWidth,
    length: basePitchLength,
    padding: {
        top: 0.5,
        right: 0.5,
        bottom: 0.5,
        left: 0.5
    },
    paintColor: "#FFF",
    grassColor: "#40b36d",
    isDefault: true,
    id: "basePitch",
    pointsDrawed: 0,
    heatmapPoints: []
};

pitchHeatMap = {...pitch};
pitchHeatMap.id = "heatMapPitch";
pitchHeatMap.marginRight = "5px";
pitchHeatMap.isDefault = false;
pitchHeatMap.grassColor = "gray";

function drawField(containerId, pitchElement) {
    currentContainerId = containerId;

    if (typeof pitchElement === 'undefined' || !pitchElement) {
        pitchElement = pitch;
    }

    var tmpMultiplier = pitchElement.length / realLength;
    var tmpScale = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500 * tmpMultiplier]);

    if (pitchElement.isDefault) {
        multiplier = tmpMultiplier;
        scale = tmpScale;

        minDistance = scale(2.3); // distanza dipendente dalla dimensione del field
    }

    scaleSvg = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500]);

    var baseBezier = 50 * tmpMultiplier;

    // rimuovo precedente se esistente
    d3.select("#" + pitchElement.id).remove();
    var svg = d3.select("#" + currentContainerId).append("svg")
            .attr("width", scaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right))
            .attr("height", scaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom))
            .attr("style", "background:" + pitchElement.grassColor)
            .attr("id", pitchElement.id);

    if (typeof pitchElement.marginRight !== "undefined") {
        svg.attr("style", svg.attr("style") + ";margin-right: " + pitchElement.marginRight);
    }

    var baseLayer = svg.append("g")
            .attr("data-index", "0")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

    // fasce alternate verde chiaro e verde scuro
    var stripeHeight = pitchElement.length / 8; // disegno sempre 8 fasce, 4 per mezzo campo
    var numStripes = Math.floor(scaleSvg(pitchElement.length) / scaleSvg(stripeHeight));

    if (pitchElement.isDefault) {
        for (var i = 0; i < numStripes; i++) {
            var stripeColor = i % 2 === 0 ? "#46ac6d" : "#40b36d"; // Alterna tra verde chiaro e scuro

            baseLayer.append("rect")
                    .attr("x", 0)
                    .attr("y", scaleSvg(i * stripeHeight))
                    .attr("width", scaleSvg(pitchElement.width))
                    .attr("height", scaleSvg(stripeHeight))
                    .attr("fill", stripeColor);
        }
    }

    // linee dei campi
    var pitchElementOutline = baseLayer.append("rect")
            .attr("x", 0)
            .attr("y", 0)
            .attr("width", scaleSvg(pitchElement.width))
            .attr("height", scaleSvg(pitchElement.length))
            .attr("stroke", pitchElement.paintColor)
            .attr("fill", "none");

    var centerSpot = baseLayer.append("circle")
            .attr("cx", scaleSvg(pitchElement.width / 2))
            .attr("cy", scaleSvg(pitchElement.length / 2))
            .attr("r", 2)
            .attr("fill", pitchElement.paintColor);

    var centerCircle = baseLayer.append("circle")
            .attr("cx", scaleSvg(pitchElement.width / 2))
            .attr("cy", scaleSvg(pitchElement.length / 2))
            .attr("r", baseBezier)
            .attr("fill", 'none')
            .attr("stroke", pitchElement.paintColor);

    var halfwayLine = baseLayer.append("line")
            .attr("y1", scaleSvg(pitchElement.length / 2))
            .attr("y2", scaleSvg(pitchElement.length / 2))
            .attr("x1", 0)
            .attr("x2", scaleSvg(pitchElement.width))
            .attr("stroke", pitchElement.paintColor);

    // corners
    function addPath(pathData, parentElement, size) {
        var path = parentElement.append("path")
                .attr("d", pathData)
                .attr("stroke", pitchElement.paintColor)
                .attr("fill", "none");

        if (typeof size !== 'undefined') {
            path.attr("stroke-width", size);
        }
    }

    // top left
    var pathData = "M0," + scaleSvg(1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(1) + ",0";
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + ",0 A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(pitchElement.width) + "," + scaleSvg(1);
    addPath(pathData, baseLayer);

    // bottom left
    pathData = "M0," + scaleSvg(pitchElement.length - 1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(1) + "," + scaleSvg(pitchElement.length);
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + "," + scaleSvg(pitchElement.length) + " A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(pitchElement.width) + "," + scaleSvg(pitchElement.length - 1);
    addPath(pathData, baseLayer);

    // Top Penalty Area
    var penaltyAreaTop = baseLayer.append("g");
//    pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54);
    pathData = "M" + tmpScale(14) + ",0L" + tmpScale(14) + "," + tmpScale(16.5) + "L" + tmpScale(54) + "," + tmpScale(16.5) + "L" + tmpScale(54) + ",0";
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Area
//    pathData = "M0," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(43.16) + "L0," + tmpScale(43.16);
    pathData = "M" + tmpScale(24.84) + ",0L" + tmpScale(24.84) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + ",0";
    addPath(pathData, penaltyAreaTop);

    // Top D
//    pathData = "M" + tmpScale(16.5) + "," + tmpScale(42) + "A " + baseBezier + " " + baseBezier + " 5 0 0 " + tmpScale(16.5) + "," + tmpScale(26);
    pathData = "M" + tmpScale(42) + "," + tmpScale(16.5) + "A " + baseBezier + " " + baseBezier + " 5 0 1 " + tmpScale(26) + "," + tmpScale(16.5);
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Spot
    var penaltySpotTop = penaltyAreaTop.append("circle")
            .attr("cx", tmpScale(34))
            .attr("cy", tmpScale(11))
            .attr("r", 1)
            .attr("fill", pitchElement.paintColor)
            .attr("stroke", pitchElement.paintColor);

    var penaltyAreaBottom = baseLayer.append("g");
    penaltyAreaBottom.html(penaltyAreaTop.html());
    penaltyAreaBottom.attr("transform", "rotate(180) translate(-" + scaleSvg(pitchElement.width) + ",-" + scaleSvg(pitchElement.length) + ")");

    pitchElement.baseLayer = baseLayer;
    var pointLayer = svg.append("g")
            .attr("data-index", "1")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");
    pitchElement.pointLayer = pointLayer;

    tip = d3.tip()
            .attr('class', 'd3-tip')
            .offset([-10, 0])
            .html(function (d) {
                return d;
            });
}

function drawPoint(x, y, amount, totalAmount, index) {
    if (pitch.pointsDrawed < 3) {
        // sistemo i punti
        var firstPoint = getCorrectPoint(x, y);
        var scaledRaggio = raggio * multiplier;
        scaledRaggio += (2 - (pitch.pointsDrawed));

        var color = "gray";
        if (pitch.pointsDrawed === 0) {
            color = "white";
        }

        var percentage = Math.floor(amount / totalAmount * 100);
        var pointCircle = pitch.pointLayer.append("circle")
                .attr("cx", firstPoint.cx + scaledRaggio / 4)
                .attr("cy", firstPoint.cy + scaledRaggio / 4)
                .attr("r", scaledRaggio)
                .attr("percentage", percentage)
                .attr("fill", color)
                .call(tip)  // Chiamata per inizializzare d3-tip sul testo
                .on("mouseover", function (d) {
                    tip.show(d3.select(this).attr("percentage") + " %", this);
                })
                .on("mouseout", function (d) {
                    tip.hide();
                });

        if (color === "gray") {
            pointCircle.style("stroke", "white")
                    .style("stroke-width", 1);
        }

        if (typeof index !== "undefined") {
            pitch.pointLayer.append("text")
                    .attr("x", function () {
                        return firstPoint.cx + scaledRaggio / 4;
                    })
                    .attr("y", function () {
                        return firstPoint.cy + scaledRaggio;
                    })
                    .attr("text-anchor", "middle")  // Centra il testo rispetto al cerchio
                    .attr("fill", "#ffffff")  // Colore del testo
                    .attr("font-size", 13)
                    .text(function () {
                        return index;
                    });
        }

        pitch.pointsDrawed++;
    }
}

function drawPointTeam(x, y, color) {
    // sistemo i punti
    var firstPoint = getCorrectPoint(x, y);
    var scaledRaggio = raggio * multiplier;

    pitch.pointLayer.append("circle")
            .attr("cx", firstPoint.cx + scaledRaggio / 4)
            .attr("cy", firstPoint.cy + scaledRaggio / 4)
            .attr("r", scaledRaggio)
            .attr("fill", color);

    pitch.pointsDrawed++;
}

function drawPlayerRole(role) {
    if (role === "P") {
        // portiere
        var tmpMultiplier = pitch.length / realLength;
        var tmpScale = d3.scaleLinear()
                .domain([0, 100])
                .range([0, 500 * tmpMultiplier]);

        var pathData = "M" + tmpScale(25.75) + "," + tmpScale(0.75) + "L" + tmpScale(25.75) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + "," + tmpScale(0.75);

        var translate = "rotate(180) translate(-" + (scaleSvg(pitch.width) + scaleSvg(pitch.padding.left)) + ",-" + (scaleSvg(pitch.length) + scaleSvg(pitch.padding.left)) + ")";
        var g = pitch.pointLayer.append("g")
                .attr("transform", translate);

        g.append("path")
                .attr("d", pathData)
                .attr("fill", colors.orange);
    } else if (role === "D") {
        // difensore
        var width = scaleSvg(pitch.width);
        var height = scaleSvg(pitch.length);

        pitch.pointLayer.append("rect")
                .attr("x", 0)
                .attr("y", height / 3 * 2)
                .attr("width", width)
                .attr("height", height / 3)
                .attr("fill", colors.orange);
    } else if (role === "C") {
        // centrocampista
        var width = scaleSvg(pitch.width);
        var height = scaleSvg(pitch.length);

        pitch.pointLayer.append("rect")
                .attr("x", 0)
                .attr("y", height / 3)
                .attr("width", width)
                .attr("height", height / 3)
                .attr("fill", colors.orange);
    } else if (role === "A") {
        // attaccante
        var width = scaleSvg(pitch.width);
        var height = scaleSvg(pitch.length);

        pitch.pointLayer.append("rect")
                .attr("x", 0)
                .attr("y", 0)
                .attr("width", width)
                .attr("height", height / 3)
                .attr("fill", colors.orange);
    }
}

/*
 * UTILS
 */
function getPointsRectangle(punti) {
    let minX = punti[0].x;
    let maxX = punti[0].x;
    let minY = punti[0].y;
    let maxY = punti[0].y;

    // Trova i valori min e max per x e y
    punti.forEach(punto => {
        if (punto.x < minX)
            minX = punto.x;
        if (punto.x > maxX)
            maxX = punto.x;
        if (punto.y < minY)
            minY = punto.y;
        if (punto.y > maxY)
            maxY = punto.y;
    });

    var raggio = 30;
    // Calcola larghezza e altezza aggiungendo un margine di 20 unità (10 per ogni lato)
    let larghezza = maxX - minX + raggio * 2; // Aggiunge un "raggio" di 10 per lato alla larghezza
    let altezza = maxY - minY + raggio * 2; // Aggiunge un "raggio" di 10 per lato all'altezza

    // Crea il rettangolo con il margine
    if (minX < raggio) {
        minX = raggio;
    }
    if (minY < raggio) {
        minY = raggio;
    }
    let rettangolo = {
        x: minX - 30, // Sposta a sinistra di 10 unità per il margine
        y: minY - 30, // Sposta in alto di 10 unità per il margine
        larghezza: larghezza,
        altezza: altezza
    };

    return rettangolo;
}

function getCorrectPoint(x, y) {
    x = ((8.5 - x) / 8.5 * pitch.length);
    y = ((y) / 8 * pitch.width);

    var scaledRaggio = raggio * multiplier;
    scaledRaggio += (2 - (pitch.pointsDrawed));

    var firstPoint = {
        cx: scaleSvg(y),
        cy: scaleSvg(x),
        r: scaledRaggio
    };

    return firstPoint;
}

function handleResize() {
    var baseWidth = 1920;

    // Aggiorna le dimensioni del tuo SVG in base alle nuove dimensioni della finestra
    var newWidth = window.innerWidth;
    var newPitchWidth = basePitchWidth / baseWidth * newWidth;
    var newPitchLength = basePitchLength / basePitchWidth * newPitchWidth;

    console.log("handleResize()", "old pitch:", pitch.width, "x", pitch.length, "new pitch:", newPitchWidth, "x", newPitchLength);
    pitch.width = newPitchWidth;
    pitch.length = newPitchLength;

    drawField(currentContainerId, pitch);
}

// Aggiungi un ascoltatore per l'evento di ridimensionamento della finestra
// window.addEventListener("resize", handleResize);