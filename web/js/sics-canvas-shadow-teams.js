var colors = {
    green: "#58e043",
    yellow: "#e0e043",
    red: "#e04343",
    black: "#242424",
    orange: "#e0bb43",
    white: "#ffffff"
};

var textColors = {
    white: "#ffffff",
    black: "#000000"
};

var lineColors = {
    black: "#2a2d2a",
    blue: "#3672ff",
    red: "#ff4545",
    yellow: "#eded4a"
};

var basePitchVerticalWidth = 272, basePitchVerticalLength = 420;

var pitchVertical = {
    width: basePitchVerticalWidth / 2.5,
    length: basePitchVerticalLength / 2.5,
    padding: {
        top: 1,
        right: 1,
        bottom: 1,
        left: 1
    },
    paintColor: "#FFF",
    grassColor: "#40b36d",
    isDefault: true,
    id: "basePitchVertical"
};

var multiplier, scale, minDistance;
var scaleSvg, shotsScaleSvg, shotsRaggio = 10.0, raggio = 3;
var realShotsLength = 105;
var tip;
var drag, dragCircle, dropzoneElements;
var currentIndex;

var form3412 = [{x: 0.5, y: 4}, {x: 2.2, y: 6}, {x: 1.9, y: 4}, {x: 2.2, y: 2}, {x: 4.3, y: 7}, {x: 3.9, y: 5}, {x: 3.9, y: 3}, {x: 4.3, y: 1}, {x: 5.7, y: 4}, {x: 6.3, y: 6}, {x: 6.3, y: 2}];
var form3421 = [{x: 0.5, y: 4}, {x: 2.2, y: 6}, {x: 1.9, y: 4}, {x: 2.2, y: 2}, {x: 4.3, y: 7}, {x: 3.9, y: 5}, {x: 3.9, y: 3}, {x: 4.3, y: 1}, {x: 5.7, y: 5.5}, {x: 5.7, y: 2.5}, {x: 6.7, y: 4}];
var form343 = [{x: 0.5, y: 4}, {x: 2.2, y: 6}, {x: 1.9, y: 4}, {x: 2.2, y: 2}, {x: 4.3, y: 7}, {x: 3.9, y: 5}, {x: 3.9, y: 3}, {x: 4.3, y: 1}, {x: 6, y: 6}, {x: 6, y: 4}, {x: 6, y: 2}];
var form3511 = [{x: 0.5, y: 4}, {x: 2.2, y: 6}, {x: 1.9, y: 4}, {x: 2.2, y: 2}, {x: 4.8, y: 7.2}, {x: 4, y: 5.8}, {x: 3.8, y: 4}, {x: 4, y: 2.2}, {x: 4.8, y: 0.8}, {x: 6, y: 4}, {x: 7.5, y: 4}];
var form352 = [{x: 0.5, y: 4}, {x: 2.2, y: 6}, {x: 1.9, y: 4}, {x: 2.2, y: 2}, {x: 4.8, y: 7.2}, {x: 4, y: 5.8}, {x: 3.8, y: 4}, {x: 4, y: 2.2}, {x: 4.8, y: 0.8}, {x: 6.5, y: 3}, {x: 6.5, y: 5}];
var form4141 = [{x: 0.5, y: 4}, {x: 2.3, y: 7}, {x: 1.9, y: 5}, {x: 1.9, y: 3}, {x: 2.3, y: 1}, {x: 3, y: 4}, {x: 4.8, y: 7}, {x: 4.4, y: 5}, {x: 4.4, y: 3}, {x: 4.8, y: 1}, {x: 6, y: 4}];
var form4222 = [{x: 0.5, y: 4}, {x: 2.3, y: 7}, {x: 1.9, y: 5}, {x: 1.9, y: 3}, {x: 2.3, y: 1}, {x: 3.7, y: 5}, {x: 3.7, y: 3}, {x: 4.8, y: 5.3}, {x: 4.8, y: 2.7}, {x: 6.5, y: 5.5}, {x: 6.5, y: 2.5}];
var form4231 = [{x: 0.5, y: 4}, {x: 2.3, y: 7}, {x: 1.9, y: 5}, {x: 1.9, y: 3}, {x: 2.3, y: 1}, {x: 3.9, y: 3}, {x: 3.9, y: 5}, {x: 5.7, y: 6.5}, {x: 5.7, y: 4}, {x: 5.7, y: 1.5}, {x: 7.5, y: 4}];
var form442 = [{x: 0.5, y: 4}, {x: 2.3, y: 7}, {x: 1.9, y: 5}, {x: 1.9, y: 3}, {x: 2.3, y: 1}, {x: 4.2, y: 7}, {x: 3.9, y: 5}, {x: 3.9, y: 3}, {x: 4.2, y: 1}, {x: 6, y: 5}, {x: 6, y: 3}];
var form4411 = [{x: 0.5, y: 4}, {x: 2.3, y: 7}, {x: 1.9, y: 5}, {x: 1.9, y: 3}, {x: 2.3, y: 1}, {x: 4.2, y: 7}, {x: 3.9, y: 5}, {x: 3.9, y: 3}, {x: 4.2, y: 1}, {x: 5.7, y: 4}, {x: 7, y: 4}];
var form451 = [{x: 0.5, y: 4}, {x: 2.3, y: 7}, {x: 1.9, y: 5}, {x: 1.9, y: 3}, {x: 2.3, y: 1}, {x: 4.8, y: 7.2}, {x: 4, y: 5.8}, {x: 3.8, y: 4}, {x: 4, y: 2.2}, {x: 4.8, y: 0.8}, {x: 6, y: 4}];
var form433 = [{x: 0.5, y: 4}, {x: 2.3, y: 7}, {x: 1.9, y: 5}, {x: 1.9, y: 3}, {x: 2.3, y: 1}, {x: 4.2, y: 6}, {x: 3.9, y: 4}, {x: 4.2, y: 2}, {x: 6, y: 6}, {x: 6, y: 4}, {x: 6, y: 2}];
var form4312 = [{x: 0.5, y: 4}, {x: 2.3, y: 7}, {x: 1.9, y: 5}, {x: 1.9, y: 3}, {x: 2.3, y: 1}, {x: 4.2, y: 6}, {x: 3.9, y: 4}, {x: 4.2, y: 2}, {x: 5.7, y: 4}, {x: 6.3, y: 5.4}, {x: 6.3, y: 2.5}];
var form4321 = [{x: 0.5, y: 4}, {x: 2.3, y: 7}, {x: 1.9, y: 5}, {x: 1.9, y: 3}, {x: 2.3, y: 1}, {x: 4.2, y: 6}, {x: 3.9, y: 4}, {x: 4.2, y: 2}, {x: 5.7, y: 5.4}, {x: 5.7, y: 2.5}, {x: 6.3, y: 4}];
var form532 = [{x: 0.5, y: 4}, {x: 2.8, y: 7.2}, {x: 2.1, y: 5.8}, {x: 1.9, y: 4}, {x: 2.1, y: 2.2}, {x: 2.8, y: 0.8}, {x: 4.2, y: 6}, {x: 3.9, y: 4}, {x: 4.2, y: 2}, {x: 6, y: 5}, {x: 6, y: 3}];
var form541 = [{x: 0.5, y: 4}, {x: 2.8, y: 7.2}, {x: 2.1, y: 5.8}, {x: 1.9, y: 4}, {x: 2.1, y: 2.2}, {x: 2.8, y: 0.8}, {x: 4.2, y: 7}, {x: 3.9, y: 5}, {x: 3.9, y: 3}, {x: 4.2, y: 1}, {x: 6, y: 4}];

function drawVerticalField(containerId) {
    var pitchElement = pitchVertical;

    var tmpMultiplier = pitchElement.length / realShotsLength;
    var tmpScale = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500 * tmpMultiplier]);

    multiplier = tmpMultiplier + 0.6; // 0.4 di base, aggiungo 0.6 per arrivare a 1 di base
    scale = tmpScale;

    minDistance = scale(2.3); // distanza dipendente dalla dimensione del field

    scaleSvg = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500]);

    var baseBezier = 50 * tmpMultiplier;

    // rimuovo precedente se esistente
    d3.select("#" + pitchElement.id).remove();
    var svg = d3.select("#" + containerId).append("svg")
            .attr("width", scaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right + (pitchElement.width / 4)))
            .attr("height", scaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom))
            .attr("style", "background:" + pitchElement.grassColor + "!important")
            .attr("id", pitchElement.id);

    var baseLayer = svg.append("g")
            .attr("data-index", "0")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

    // fasce alternate verde chiaro e verde scuro
    var stripeHeight = pitchElement.width / 8; // Altezza delle fasce
    var numStripes = Math.floor(scaleSvg(pitchElement.length) / scaleSvg(stripeHeight));

    if (pitchElement.isDefault) {
        for (var i = 0; i < numStripes; i++) {
            var stripeColor = i % 2 === 0 ? "#46ac6d" : "#40b36d"; // Alterna tra verde chiaro e scuro

            baseLayer.append("rect")
                    .attr("x", 0)
                    .attr("y", scaleSvg(i * stripeHeight))
                    .attr("width", scaleSvg(pitchElement.width))
                    .attr("height", scaleSvg(stripeHeight))
                    .attr("fill", stripeColor);
        }
    }

    // linee dei campi
    baseLayer.append("rect")
            .attr("x", 0)
            .attr("y", 0)
            .attr("width", scaleSvg(pitchElement.width))
            .attr("height", scaleSvg(pitchElement.length))
            .attr("stroke", pitchElement.paintColor)
            .attr("stroke-width", 2)
            .attr("fill", "none");
    if (typeof pitchElement.isTocchi === "undefined" || !pitchElement.isTocchi) {
        baseLayer.append("circle")
                .attr("cx", scaleSvg(pitchElement.width / 2))
                .attr("cy", scaleSvg(pitchElement.length / 2))
                .attr("r", 4)
                .attr("fill", pitchElement.paintColor);

        baseLayer.append("circle")
                .attr("cx", scaleSvg(pitchElement.width / 2))
                .attr("cy", scaleSvg(pitchElement.length / 2))
                .attr("r", baseBezier)
                .attr("fill", 'none')
                .attr("stroke", pitchElement.paintColor)
                .attr("stroke-width", 2);

        baseLayer.append("line")
                .attr("y1", scaleSvg(pitchElement.length / 2))
                .attr("y2", scaleSvg(pitchElement.length / 2))
                .attr("x1", scaleSvg(pitchElement.width))
                .attr("x2", 0)
                .attr("stroke", pitchElement.paintColor)
                .attr("stroke-width", 2);
    } else {
        // Terzo Offensivo
        baseLayer.append("line")
                .attr("y1", scaleSvg(pitchElement.length / 3))
                .attr("y2", scaleSvg(pitchElement.length / 3))
                .attr("x1", scaleSvg(pitchElement.width))
                .attr("x2", 0)
                .attr("stroke-dasharray", "2,2")
                .attr("stroke", pitchElement.paintColor);

        // Terzo Difensivo
        baseLayer.append("line")
                .attr("y1", scaleSvg(pitchElement.length / 3 * 2))
                .attr("y2", scaleSvg(pitchElement.length / 3 * 2))
                .attr("x1", scaleSvg(pitchElement.width))
                .attr("x2", 0)
                .attr("stroke-dasharray", "2,2")
                .attr("stroke", pitchElement.paintColor);

        for (var i = 1; i < 5; i++) {
            baseLayer.append("line")
                    .attr("y1", scaleSvg(pitchElement.length))
                    .attr("y2", 0)
                    .attr("x1", scaleSvg(pitchElement.width / 5 * i))
                    .attr("x2", scaleSvg(pitchElement.width / 5 * i))
                    .attr("stroke-dasharray", "2,2")
                    .attr("stroke", pitchElement.paintColor);

            baseLayer.append("text")
                    .attr("x", function () {
                        return scaleSvg(pitchElement.width / 5 * i) - scaleSvg(pitchElement.width / 5 / 2);
                    })
                    .attr("y", function () {
                        return scaleSvg(1.75);
                    })  // Regola la posizione del testo verticalmente
                    .attr("text-anchor", "middle")  // Centra il testo rispetto al cerchio
                    .attr("fill", colors.white)  // Colore del testo
                    .attr("font-size", calculateTextSize(10))
                    .text(function () {
                        return "C" + i;
                    });
        }

        // testo C5
        baseLayer.append("text")
                .attr("x", function () {
                    return scaleSvg(pitchElement.width / 5 * 5) - scaleSvg(pitchElement.width / 5 / 2);
                })
                .attr("y", function () {
                    return scaleSvg(1.75);
                })  // Regola la posizione del testo verticalmente
                .attr("text-anchor", "middle")  // Centra il testo rispetto al cerchio
                .attr("fill", colors.white)  // Colore del testo
                .attr("font-size", calculateTextSize(10))
                .text(function () {
                    return "C5";
                });
    }

    // corners
    function addPath(pathData, parentElement, size) {
        var path = parentElement.append("path")
                .attr("d", pathData)
                .attr("stroke", pitchElement.paintColor)
                .attr("fill", "none");

        if (typeof size !== 'undefined') {
            path.attr("stroke-width", size);
        }
    }

    // top left
    var pathData = "M0," + scaleSvg(1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(1) + ",0";
    addPath(pathData, baseLayer, 2);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + ",0 A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(pitchElement.width) + "," + scaleSvg(1);
    addPath(pathData, baseLayer, 2);

    // bottom left
    pathData = "M0," + scaleSvg(pitchElement.length - 1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(1) + "," + scaleSvg(pitchElement.length);
    addPath(pathData, baseLayer, 2);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + "," + scaleSvg(pitchElement.length) + " A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(pitchElement.width) + "," + scaleSvg(pitchElement.length - 1);
    addPath(pathData, baseLayer, 2);

    // Top Penalty Area
    var penaltyAreaTop = baseLayer.append("g");
//    pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54);
    pathData = "M" + tmpScale(14) + ",0L" + tmpScale(14) + "," + tmpScale(16.5) + "L" + tmpScale(54) + "," + tmpScale(16.5) + "L" + tmpScale(54) + ",0";
    addPath(pathData, penaltyAreaTop, 2);

    // Top Penalty Area
    pathData = "M" + tmpScale(24.84) + ",0L" + tmpScale(24.84) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + ",0";
    addPath(pathData, penaltyAreaTop, 2);

    // Top D
    pathData = "M" + tmpScale(42) + "," + tmpScale(16.5) + "A " + baseBezier + " " + baseBezier + " 5 0 1 " + tmpScale(26) + "," + tmpScale(16.5);
    addPath(pathData, penaltyAreaTop, 2);

    // Top Penalty Spot
    penaltyAreaTop.append("circle")
            .attr("cx", tmpScale(34))
            .attr("cy", tmpScale(11))
            .attr("r", 2)
            .attr("fill", pitchElement.paintColor)
            .attr("stroke", pitchElement.paintColor)
            .attr("stroke-width", 2);

    var penaltyAreaBottom = baseLayer.append("g");
    penaltyAreaBottom.html(penaltyAreaTop.html());
    penaltyAreaBottom.attr("transform", "rotate(180) translate(-" + scaleSvg(pitchElement.width) + ",-" + scaleSvg(pitchElement.length) + ")");

    pitchElement.baseLayer = baseLayer;

    var pointLayer = svg.append("g")
            .attr("data-index", "1")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

    pitchElement.pointLayer = pointLayer;

    if (typeof tip === "undefined") {
        tip = d3.tip()
                .attr('class', 'd3-tip')
                .offset([-10, 0])
                .html(function (d) {
                    return d;
                });
    }

    drag = d3.drag()
            .on("start", dragstarted)
            .on("drag", dragged)
            .on("end", dragended);

    dragCircle = d3.drag()
            .on("drag", draggedCircle)
            .on("end", dragendedCircle);
}

function changeFormazione(index) {
    pitchVertical.pointLayer.selectAll("*").remove();
    if (typeof index === "undefined") {
        index = parseInt($("#formazioneSelect").val());
    }

    var array = [];
    if (index === 0) {
        array = [...form3412];
    } else if (index === 1) {
        array = [...form3421];
    } else if (index === 2) {
        array = [...form343];
    } else if (index === 3) {
        array = [...form3511];
    } else if (index === 4) {
        array = [...form352];
    } else if (index === 5) {
        array = [...form4141];
    } else if (index === 6) {
        array = [...form4222];
    } else if (index === 7) {
        array = [...form4231];
    } else if (index === 8) {
        array = [...form442];
    } else if (index === 9) {
        array = [...form4411];
    } else if (index === 10) {
        array = [...form451];
    } else if (index === 11) {
        array = [...form433];
    } else if (index === 12) {
        array = [...form4312];
    } else if (index === 13) {
        array = [...form4321];
    } else if (index === 14) {
        array = [...form532];
    } else if (index === 15) {
        array = [...form541];
    }

    // panchina per gestione aggiunta generica
    array.push({x: 0.5, y: 9});

    // sistemo i punti
    var tmpIndex = 0;
    array.forEach(function (coords) {
        var firstPoint = getCorrectPoint(coords.x, coords.y);
        var scaledRaggio = raggio * multiplier * 2;

        const circle = pitchVertical.pointLayer.append("circle")
                .attr("class", "cursor-pointer drop-here circle-draggable")
                .attr("cx", roundNumber(firstPoint.cx + scaledRaggio / 4, 4))
                .attr("cy", roundNumber(firstPoint.cy + scaledRaggio / 4, 4))
                .attr("r", scaledRaggio * 1.2)
                .attr("fill", colors.white)
                .attr("index", tmpIndex)
                .attr("stroke", "#ffffff")
                .attr("stroke-width", 2)
                .on("click", handlePlusClick);

//        pitchVertical.pointLayer.append("text")
//                .attr("class", "plus-icon")
//                .attr("x", firstPoint.cx + scaledRaggio / 4)
//                .attr("y", firstPoint.cy + scaledRaggio / 1.75)
//                .attr("text-anchor", "middle")
//                .attr("text-plus-index", tmpIndex)
//                .text("+")
//                .on("mouseover", showPlusIcon)
//                .on("click", handlePlusClick);

        tmpIndex++;
    });

    dropzoneElements = d3.selectAll(".drop-here");
    d3.selectAll(".circle-draggable").call(dragCircle);
    repaintPlayers();
}

// Funzioni per mostrare/nascondere l'icona "+"
function showPlusIcon(event, d) {
    if (d3.select(this).attr("text-plus-index") !== null) {
        d3.select(this).style("display", "block");
    } else {
        var indexToShow = d3.select(this).attr("index");
        d3.selectAll("text[text-plus-index='" + indexToShow + "']")
                .style("display", "block");
    }
}

function hidePlusIcon(event, d) {
    var indexToShow = d3.select(this).attr("index");
    d3.selectAll("text[text-plus-index='" + indexToShow + "']")
            .style("display", "none");
}

// Funzione di gestione del click sull'icona "+"
function handlePlusClick(event, d) {
    if (isCopyMode) {
        if (!copyModeColor) {
            copyModeColor = d3.select(this).attr("fill");
            $("#paint-brush-color").attr("style", "background-color: " + copyModeColor);
        } else {
            d3.select(this).attr("fill", copyModeColor);
            repaintPlayers();
        }
    } else {
        var index = d3.select(this).attr("text-plus-index") !== null ? d3.select(this).attr("text-plus-index") : d3.select(this).attr("index");
        currentIndex = index;
        openShadowTeamPlayerManager(d3.select(this).attr("fill"));
    }
//    insertPlayer({id: 12345, name: 'N. Barella', age: '92', height: '186cm', team: 'Milan', photo: 'https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/MUSSO J._16872.png'}, index);
}

function repaintPlayers() {
    // prima di tutto un bel clear
    d3.selectAll(".draggable").remove();

    shadowTeamIndexPlayers.forEach(function (value, key) {
        value.forEach(function (playerId) {
            insertPlayer(false, playerId, key);
        });
    });

    shadowTeamIndexPlayersPersonal.forEach(function (value, key) {
        value.forEach(function (playerId) {
            insertPlayer(true, playerId, key);
        });
    });

    temporaryPlayerMap = new Map();
    temporaryPlayerPersonalMap = new Map();
}

var playerBoxHeight = 40, playerBoxWidth = 125, playerPhotoSize = 30;
var shadowTeamPlayerMap = new Map(), shadowTeamIndexPlayers = new Map(), shadowTeamPlayerPersonalMap = new Map(), shadowTeamIndexPlayersPersonal = new Map();
var temporaryPlayerMap = new Map(), temporaryPlayerPersonalMap = new Map();
function insertPlayer(isPersonal, playerId, index) {
    if (typeof skipChecks === "undefined") {
        skipChecks = false;
    }
    var player = resultPlayers.get(playerId);
    if (typeof player === "undefined" && !isPersonal) {
        player = shadowTeamPlayerMap.get(playerId);
    } else if (typeof player === "undefined" && isPersonal) {
        player = shadowTeamPlayerPersonalMap.get(playerId);
    }
    if (typeof index === "undefined") {
        if (typeof currentIndex === "undefined") {
            index = player.currentIndex;
        } else {
            index = currentIndex;
        }
    }

    var alreadyAdded = false;
    if (isPersonal) {
        alreadyAdded = !d3.select("g[playerPersonalId='" + playerId + "']").empty();
    } else {
        alreadyAdded = !d3.select("g[playerId='" + playerId + "']").empty();
    }

    if (!alreadyAdded) {
        player.currentIndex = parseInt(index);
        if ((typeof player.teamName === "undefined" || player.teamName === null) && typeof player.id !== "undefined" && player.id !== null) {
            // carico ultimo team da ajax
            $.ajax({
                type: "GET",
                url: "/sicstv/user/getPlayerLastTeam.htm",
                dataType: "json",
                data: "playerId=" + player.id,
                success: function (result) {
                    if (isPersonal) {
                        shadowTeamPlayerPersonalMap.get(playerId).teamName = result.teamName;
                        shadowTeamPlayerPersonalMap.get(playerId).footId = result.footId;
                        shadowTeamPlayerPersonalMap.get(playerId).idPosition = result.positionId;
                    } else {
                        resultPlayers.get(playerId).teamName = result.teamName;
                        resultPlayers.get(playerId).footId = result.footId;
                        resultPlayers.get(playerId).idPosition = result.positionId;
                    }
                    insertPlayer(isPersonal, playerId, index);
                }
            });

            return;
        }

        if (isPersonal) {
            temporaryPlayerPersonalMap.set(player.personalId, player);
            shadowTeamPlayerPersonalMap.set(player.personalId, player);
            if (!shadowTeamIndexPlayersPersonal.has(player.currentIndex)) {
                shadowTeamIndexPlayersPersonal.set(player.currentIndex, []);
            }
            if (!shadowTeamIndexPlayersPersonal.get(player.currentIndex).includes(player.personalId)) {
                shadowTeamIndexPlayersPersonal.get(player.currentIndex).push(player.personalId);
            }
        } else {
            temporaryPlayerMap.set(player.id, player);
            shadowTeamPlayerMap.set(player.id, player);
            if (!shadowTeamIndexPlayers.has(player.currentIndex)) {
                shadowTeamIndexPlayers.set(player.currentIndex, []);
            }
            if (!shadowTeamIndexPlayers.get(player.currentIndex).includes(player.id)) {
                shadowTeamIndexPlayers.get(player.currentIndex).push(player.id);
            }
        }
        var baseElement = d3.select("circle[index='" + index + "']");
        var baseX = baseElement.attr("cx");
        var baseY = baseElement.attr("cy");
        var baseRadius = baseElement.attr("r");

        var playerAmount = getPlayerAmount(index);

        var translateX = (parseFloat(baseX) + -playerBoxWidth / 2);
        var translateY = (parseFloat(baseY) + -playerBoxHeight / 2 - (baseRadius * 2.5) - (playerAmount * playerBoxHeight));
        var playerBox = pitchVertical.pointLayer.append("g")
                .attr("class", "draggable")
                .attr("index", index + "-" + playerAmount)
                .attr("baseIndex", index)
                .attr("playerId", player.id)
                .attr("playerPersonalId", player.personalId)
                .attr("transform", "translate(" + translateX + ", " + translateY + ")")
                .attr("translateX", translateX)
                .attr("translateY", translateY);

        playerBox.append("rect")
                .attr("width", playerBoxWidth)
                .attr("height", playerBoxHeight)
                .attr("fill", "#fff")
                .attr("rx", 3) // Angoli arrotondati
                .attr("x", 0) // Centra il rettangolo orizzontalmente
                .attr("y", 0); // Centra il rettangolo verticalmente

        var circleColor = baseElement.attr("fill");
        if (circleColor !== null && circleColor && playerAmount === 0) {
            if (circleColor !== "#ffffff") {
                playerBox.append("rect")
                        .attr("width", playerBoxWidth)
                        .attr("height", 3)
                        .attr("fill", circleColor)
                        .attr("rx", 1) // Angoli arrotondati
                        .attr("x", 0) // Centra il rettangolo orizzontalmente
                        .attr("y", playerBoxHeight); // Centra il rettangolo verticalmente
            }
        }

        var tmpPhotoSize = playerPhotoSize;
        if ($("#photoCheck").is(":checked")) {
            playerBox.append("image")
                    .attr("xlink:href", "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/" + player.photo + ".png")
                    .attr("width", tmpPhotoSize)
                    .attr("height", tmpPhotoSize)
                    .attr("x", 0) // Posiziona l'immagine a sinistra
                    .attr("y", 5) // Centra verticalmente
                    .on("error", function (d) {
                        this.setAttribute("href", "/sicstv/images/user_gray.png");
                    });
        } else {
            tmpPhotoSize = 0;
        }

        var playerName = player.known_name;
        if (playerName.length === 0) {
            playerName = player.first_name + " " + player.last_name;
        }
        playerBox.append("text")
                .attr("x", tmpPhotoSize + 5) // Dopo l'immagine
                .attr("y", 10)
                .attr("alignment-baseline", "middle")
                .style("font-size", "12px")
                .style("font-weight", "bold")
                .text(truncateIfNeeded(playerName))
                .attr("data-tooltip", playerName)
                .call(tip)  // Chiamata per inizializzare d3-tip sul testo
                .on("mouseover", function (d) {
                    tip.show(d3.select(this).attr("data-tooltip"), this);
                })
                .on("mouseout", function (d) {
                    tip.hide();
                });

        var ageHeightContent = "";
        if ($("#ageCheck").is(":checked")) {
            ageHeightContent = player.bornDateYearLast2Digit;
        }
        if ($("#heightCheck").is(":checked")) {
            if (ageHeightContent.length === 0) {
                ageHeightContent = player.height;
            } else {
                ageHeightContent += " | " + player.height;
            }
        }

        if (ageHeightContent.length > 0) {
            playerBox.append("text")
                    .attr("x", tmpPhotoSize + 5) // Dopo l'immagine
                    .attr("y", 20)
                    .attr("alignment-baseline", "middle")
                    .style("font-size", "10px")
                    .text(ageHeightContent);
        }

        if ($("#teamCheck").is(":checked")) {
            playerBox.append("text")
                    .attr("x", tmpPhotoSize + 5) // Dopo l'immagine
                    .attr("y", 30)
                    .attr("alignment-baseline", "middle")
                    .style("font-size", "10px")
                    .text(player.teamName);
        }

        playerBox.append('svg:foreignObject')
                .attr("x", playerBoxWidth - 15)
                .attr("y", 25)
                .attr("width", 15)
                .attr("height", 15)
                .append("xhtml:body")
                .html('<i class="uk-icon-trash cursor-pointer" onclick="removeElement(' + isPersonal + ', ' + (isPersonal ? player.personalId : player.id) + ', ' + player.currentIndex + ');"></i>');

        d3.selectAll(".draggable").call(drag);
    }
}

function cancelInsert() {
    temporaryPlayerMap.forEach(function (value, index) {
        removeElement(false, value.id, value.currentIndex, true);
    });

    temporaryPlayerPersonalMap.forEach(function (value, index) {
        removeElement(true, value.personalId, value.currentIndex, true);
    });

    repaintPlayers();
//    temporaryPlayerMap = new Map();
//    temporaryPlayerPersonalMap = new Map();
}

function removeElement(isPersonal, playerId, index, skipRepaint) {
    if (isPersonal) {
        var element = d3.select("g[baseIndex='" + index + "'][playerPersonalId='" + playerId + "']");
        element.remove();
        var playerIndex = shadowTeamIndexPlayersPersonal.get(index).indexOf(playerId);
        if (playerIndex > -1) { // only splice array when item is found
            shadowTeamIndexPlayersPersonal.get(index).splice(playerIndex, 1); // 2nd parameter means remove one item only
        }
        shadowTeamPlayerPersonalMap.delete(playerId);
    } else {
        var element = d3.select("g[baseIndex='" + index + "'][playerId='" + playerId + "']");
        element.remove();
        var playerIndex = shadowTeamIndexPlayers.get(index).indexOf(playerId);
        if (playerIndex > -1) { // only splice array when item is found
            shadowTeamIndexPlayers.get(index).splice(playerIndex, 1); // 2nd parameter means remove one item only
        }
        shadowTeamPlayerMap.delete(playerId);
    }
    if (typeof skipRepaint === "undefined" || !skipRepaint) {
        repaintPlayers();
    }
}

function removePlayer(playerId) {
    d3.selectAll("g[playerId='" + playerId + "']").remove();
}

function getPlayerAmount(index) {
    var goNext = true;
    var amount = 0;
    while (goNext) {
        if (!d3.select("g[index='" + index + "-" + amount + "']").empty()) {
            amount++;
        } else {
            goNext = false;
        }
    }

    return amount;
}

function showTooltip() {
    var tooltipText = d3.select(this).attr("data-tooltip");
    var bbox = this.getBBox(); // Ottieni il bounding box dell'elemento <text>

    tooltip.transition()
            .duration(200)
            .style("opacity", .9)
            .style("left", (bbox.x + bbox.width / 2) + "px")
            .style("top", (bbox.y - 10) + "px");

    tooltip.html(tooltipText)
            .style("left", (bbox.x + bbox.width / 2) + "px")
            .style("top", (bbox.y - 10) + "px");
}

function hideTooltip() {
    tooltip.transition()
            .duration(500)
            .style("opacity", 0);
}

// Funzione per calcolare la distanza tra due punti
function distance(point1, point2) {
    const dx = point1.x - point2.x;
    const dy = point1.y - point2.y;
    return Math.sqrt(dx * dx + dy * dy);
}

// Funzioni di drag
function dragstarted(event, d) {
//    d3.select(this).raise().attr("stroke", "black");
}

function dragged(d, i, nodes) {
    if (!isCopyMode) {
        var x = d3.event.x;
        var y = d3.event.y;

        if (d3.select(this).attr("baseX") === null) {
            d3.select(this).attr("baseX", x);
            d3.select(this).attr("baseY", y);
        } else {
            var transformX = parseFloat(d3.select(this).attr("translateX"));
            var transformY = parseFloat(d3.select(this).attr("translateY"));

            var baseX = parseFloat(d3.select(this).attr("baseX"));
            var baseY = parseFloat(d3.select(this).attr("baseY"));

            transformX = transformX - (baseX - x) + (playerBoxWidth / 4);
            transformY = transformY - (baseY - y) - 20;
            d3.select(this).attr("transform", "translate(" + transformX + ", " + transformY + ")");
        }

        d3.select(this)
                .attr("cx", x)
                .attr("cy", y);
    }
}

function dragended(event, d) {
    const element = d3.select(this);
    const cx = parseFloat(element.attr("cx"));
    const cy = parseFloat(element.attr("cy"));
    const playerId = parseFloat(element.attr("playerId"));
    const playerPersonalId = parseFloat(element.attr("playerPersonalId"));
    const previousIndex = parseInt(element.attr("index").split("-")[0]);

    const isPersonal = !isNaN(playerPersonalId);
    if (playerId !== null) {
        let droppedInZone = false;
        dropzoneElements.each(function () {
            const dropzone = d3.select(this);
            const dropCx = parseFloat(dropzone.attr("cx"));
            const dropCy = parseFloat(dropzone.attr("cy"));
            const dropR = parseFloat(dropzone.attr("r"));
            const dropzoneIndex = parseInt(dropzone.attr("index"));

            // Controlla se il cerchio trascinato è droppato sopra un cerchio con la classe "drop-here"
//        console.log(cx, cy, dropCx, dropCy, dropR, distance({x: cx, y: cy}, {x: dropCx, y: dropCy}), dropR);
            if (distance({x: cx, y: cy}, {x: dropCx, y: dropCy}) <= dropR * 5) {
                droppedInZone = true;
                element.remove();
                if (isPersonal) {
                    const index = shadowTeamIndexPlayersPersonal.get(previousIndex).indexOf(playerPersonalId);
                    if (index > -1) { // only splice array when item is found
                        shadowTeamIndexPlayersPersonal.get(previousIndex).splice(index, 1); // 2nd parameter means remove one item only
                    }
                    insertPlayer(isPersonal, playerPersonalId, dropzoneIndex);
                } else {
                    const index = shadowTeamIndexPlayers.get(previousIndex).indexOf(playerId);
                    if (index > -1) { // only splice array when item is found
                        shadowTeamIndexPlayers.get(previousIndex).splice(index, 1); // 2nd parameter means remove one item only
                    }
                    insertPlayer(isPersonal, playerId, dropzoneIndex);
                }
                repaintPlayers();
            }
        });

        if (!droppedInZone) {
            element.attr("transform", "translate(" + parseFloat(element.attr("translateX")) + ", " + parseFloat(element.attr("translateY")) + ")");
        }
    } else {
        element.attr("transform", "translate(" + parseFloat(element.attr("translateX")) + ", " + parseFloat(element.attr("translateY")) + ")");
    }
}

function draggedCircle(d, i, nodes) {
    if (!isCopyMode) {
        var x = d3.event.x;
        var y = d3.event.y;

        d3.select(this)
                .attr("cx", x)
                .attr("cy", y);
    }
}

function dragendedCircle(event, d) {
    repaintPlayers();
}

/*
 * UTILS
 */
function truncateIfNeeded(text) {
    if (typeof text !== "undefined" && text !== null) {
        var maxLenght = 10;
        if (!$("#photoCheck").is(":checked")) {
            maxLenght = 15;
        }
        if (text.length > maxLenght) {
            text = text.substr(0, maxLenght) + "...";
        }
    }
    return text;
}

function getCorrectPoint(x, y) {
    x = ((8.5 - x) / 8.5 * pitchVertical.length);
    y = ((y) / 8 * pitchVertical.width);

    var scaledRaggio = raggio * multiplier;
    scaledRaggio += (2 - (pitchVertical.pointsDrawed));

    var firstPoint = {
        cx: scaleSvg(y),
        cy: scaleSvg(x),
        r: scaledRaggio
    };

    return firstPoint;
}

function calculateTextNumberSize(number) {
    var baseSize = 5;

    if (number > 9) {
        baseSize = 5;
    }

    return baseSize * multiplier;
}

function calculateTextSize(number) {
    return number * multiplier;
}

function roundNumber(number, decimals) {
    return Number(Math.round(number + 'e' + decimals) + 'e-' + decimals);
}