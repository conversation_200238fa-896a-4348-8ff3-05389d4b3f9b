var shotColors = {
    green: "#58e043",
    yellow: "#e0e043",
    red: "#e04343"
};

var shotTextColors = {
    white: "#ffffff",
    black: "#000000"
};

var basePitchShotsWidth = 170, basePitchShotsLength = 70;
var basePitchShotsFilterWidth = 85, basePitchShotsFilterLength = 35;
var basePitchVerticalWidth = 68, basePitchVerticalLength = 52.5;

pitchShots = {
    width: basePitchShotsWidth,
    length: basePitchShotsLength,
    padding: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
    },
    paintColor: "#FFF",
    grassColor: "#40b36d",
    isDefault: true,
    percentageView: 0,
    id: "basePitchShots"
};
pitchShotsFilter = {
    width: basePitchShotsFilterWidth,
    length: basePitchShotsFilterLength,
    padding: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0
    },
    paintColor: "#FFF",
    grassColor: "#40b36d",
    isDefault: false,
    id: "basePitchShotsFilter"
};
pitchVertical = {
    width: basePitchVerticalWidth,
    length: basePitchVerticalLength,
    padding: {
        top: 2,
        right: 2,
        bottom: 2,
        left: 2
    },
    paintColor: "#FFF",
    grassColor: "#40b36d",
    isDefault: true,
    id: "basePitchVertical"
};

var currentShotsContainerId;
var shotsMultiplier, shotsScale, shotsMinDistance;
var shotsScaleSvg, shotsRaggio = 10.0;
var realShotsLength = 52.5;

function drawShotField(containerId, pitchElement) {
    currentShotsContainerId = containerId;

    if (typeof pitchElement === 'undefined' || !pitchElement) {
        pitchElement = pitchShots;
    }

    var tmpMultiplier = pitchVertical.length / realShotsLength;
    var tmpScale = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500 * tmpMultiplier]);

    if (pitchElement.isDefault) {
        shotsMultiplier = tmpMultiplier;
        shotsScale = tmpScale;

        shotsMinDistance = shotsScale(2.3); // distanza dipendente dalla dimensione del field
    }

    shotsScaleSvg = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500]);

    // rimuovo precedente se esistente
    d3.select("#" + pitchElement.id).remove();
    var svg = d3.select("#" + currentShotsContainerId).append("svg")
            .attr("width", shotsScaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right))
            .attr("height", shotsScaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom))
            .attr("id", pitchElement.id);

    var baseLayer = svg.append("g")
            .attr("data-index", "0");

    tmpGlobalLayer = baseLayer;

    baseLayer.append('image')
            .attr('xlink:href', '/sicstv/images/porta2023.png')
            .attr('width', shotsScaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right))
            .attr('height', shotsScaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom));

    var pointLayer = svg.append("g")
            .attr("data-index", "1");

    pitchElement.pointLayer = pointLayer;

    // legenda
    var legendaWidth = scaleSvg(pitchVertical.width) * 1.5;
    d3.select("#pitchShotsLegend").remove();
    var svgLegenda = d3.select("#" + currentShotsContainerId).append("svg")
            .attr("width", legendaWidth)
            .attr("height", scaleSvg(10))
            .attr("id", "pitchShotsLegend");

    var baseLayerLegenda = svgLegenda.append("g")
            .attr("data-index", "0");

    var scaledRaggio = shotsRaggio * shotsMultiplier;
    baseLayerLegenda.append("circle")
            .attr("cx", scaledRaggio)
            .attr("cy", scaleSvg(10) / 2)
            .attr("r", scaledRaggio)
            .attr("fill", shotColors.red);

    baseLayerLegenda.append("text")
            .attr("x", function () {
                return scaledRaggio * 3;
            })
            .attr("y", function () {
                return scaleSvg(10) / 2;
            })  // Regola la posizione del testo verticalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("fill", "black")
            .attr("font-size", calculateTextNumberSize(10))
            .text(function () {
                return pageTranslation.get("redCircle");
            });

    baseLayerLegenda.append("circle")
            .attr("cx", legendaWidth / 5 + scaledRaggio)
            .attr("cy", scaleSvg(10) / 2)
            .attr("r", scaledRaggio)
            .attr("fill", shotColors.yellow);

    baseLayerLegenda.append("text")
            .attr("x", function () {
                return (legendaWidth / 5) + scaledRaggio * 3;
            })
            .attr("y", function () {
                return scaleSvg(10) / 2;
            })  // Regola la posizione del testo verticalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("fill", "black")
            .attr("font-size", calculateTextNumberSize(10))
            .text(function () {
                return pageTranslation.get("yellowCircle");
            });

    baseLayerLegenda.append("circle")
            .attr("cx", (legendaWidth / 3 * 2) + scaledRaggio)
            .attr("cy", scaleSvg(10) / 2)
            .attr("r", scaledRaggio)
            .attr("fill", shotColors.green);

    baseLayerLegenda.append("text")
            .attr("x", function () {
                return (legendaWidth / 3 * 2) + scaledRaggio * 3;
            })
            .attr("y", function () {
                return scaleSvg(10) / 2;
            })  // Regola la posizione del testo verticalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("fill", "black")
            .attr("font-size", calculateTextNumberSize(10))
            .text(function () {
                return pageTranslation.get("greenCircle");
            });

    drawShotFieldFilter();
    drawVerticalField();
}

function drawShotFieldFilter(pitchElement) {
    if (typeof pitchElement === 'undefined' || !pitchElement) {
        pitchElement = pitchShotsFilter;
    }

    var tmpMultiplier = pitchElement.length / realShotsLength;
    var tmpScale = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500]);

    if (pitchElement.isDefault) {
        shotsMultiplier = tmpMultiplier;
        shotsScale = tmpScale;

        shotsMinDistance = shotsScale(2.3); // distanza dipendente dalla dimensione del field
    }

    shotsScaleSvg = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500]);

    // rimuovo precedente se esistente
    d3.select("#" + pitchElement.id).remove();
    var svg = d3.select("#" + currentShotsContainerId).append("svg")
            .attr("width", shotsScaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right))
            .attr("height", shotsScaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom))
            .attr("id", pitchElement.id);

    var baseLayer = svg.append("g")
            .attr("data-index", "0");

    baseLayer.append('image')
            .attr('xlink:href', '/sicstv/images/porta2023.png')
            .attr('width', shotsScaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right))
            .attr('height', shotsScaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom));

    var pointLayer = svg.append("g")
            .attr("data-index", "1");

    pitchElement.pointLayer = pointLayer;

    var rows = 3, columns = 3;
    var widthPart, heightPart;

    widthPart = (shotsScaleSvg(pitchElement.width) - ((25) * shotsMultiplier * 2)) / rows;
    heightPart = (shotsScaleSvg(pitchElement.length) - ((25) * shotsMultiplier)) / columns;
    var additionalX, additionalY;
    additionalX = 25 * shotsMultiplier;
    additionalY = 25 * shotsMultiplier;
    for (var row = 0; row <= rows; row++) {
        pitchElement.pointLayer.append("line")
                .attr("x1", additionalX + widthPart * row)
                .attr("y1", additionalY)
                .attr("x2", additionalX + widthPart * row)
                .attr("y2", shotsScaleSvg(pitchElement.length))
                .attr("stroke", "black")
                .attr("isShot", true)
                .attr("stroke-width", 2);
    }
    for (var column = 0; column < columns; column++) {
        pitchElement.pointLayer.append("line")
                .attr("x1", additionalX)
                .attr("y1", additionalY + heightPart * column)
                .attr("x2", shotsScaleSvg(pitchElement.width) - additionalX)
                .attr("y2", additionalY + heightPart * column)
                .attr("stroke", "black")
                .attr("stroke-width", 2);
    }
    // linee per identificare zone "fuori porta"
    // linea sinistra
    pitchElement.pointLayer.append("line")
            .attr("x1", 1)
            .attr("y1", 1)
            .attr("x2", 1)
            .attr("y2", shotsScaleSvg(pitchElement.length))
            .attr("stroke", "gray")
            .attr("stroke-width", 2)
            .attr('stroke-dasharray', '10,5')
            .attr('stroke-linecap', 'butt');
    // linea sopra
    pitchElement.pointLayer.append("line")
            .attr("x1", 1)
            .attr("y1", 1)
            .attr("x2", shotsScaleSvg(pitchElement.width))
            .attr("y2", 1)
            .attr("stroke", "gray")
            .attr("stroke-width", 2)
            .attr('stroke-dasharray', '10,5')
            .attr('stroke-linecap', 'butt');
    // linea piccola sinistra
    pitchElement.pointLayer.append("line")
            .attr("x1", additionalX)
            .attr("y1", 1)
            .attr("x2", additionalX)
            .attr("y2", additionalY)
            .attr("stroke", "gray")
            .attr("stroke-width", 2)
            .attr('stroke-dasharray', '10,5')
            .attr('stroke-linecap', 'butt');
    // linea piccola destra
    pitchElement.pointLayer.append("line")
            .attr("x1", shotsScaleSvg(pitchElement.width) - additionalX)
            .attr("y1", 1)
            .attr("x2", shotsScaleSvg(pitchElement.width) - additionalX)
            .attr("y2", additionalY)
            .attr("stroke", "gray")
            .attr("stroke-width", 2)
            .attr('stroke-dasharray', '10,5')
            .attr('stroke-linecap', 'butt');
    // linea destra
    pitchElement.pointLayer.append("line")
            .attr("x1", shotsScaleSvg(pitchElement.width) - 1)
            .attr("y1", 1)
            .attr("x2", shotsScaleSvg(pitchElement.width) - 1)
            .attr("y2", shotsScaleSvg(pitchElement.length))
            .attr("stroke", "gray")
            .attr("stroke-width", 2)
            .attr('stroke-dasharray', '10,5')
            .attr('stroke-linecap', 'butt');

    var counter = 1;
    for (var row = 0; row < rows; row++) {
        for (var column = 0; column < columns; column++) {
            pitchElement.pointLayer.append("rect")
                    .attr("x", additionalX + widthPart * row)
                    .attr("y", additionalY + heightPart * column)
                    .attr("width", widthPart)
                    .attr("height", heightPart)
                    .attr("style", "cursor: pointer")
                    .attr("fill", "transparent")
                    .attr("data-event", counter)
                    .attr("data-index", column)
                    .attr("data-index-j", row)
                    .on("click", function () {
                        clickVerticalEvent(this, 1, 1);
                    });

            counter++;
        }
    }
    // zone fuori porta
    pitchElement.pointLayer.append("rect")
            .attr("x", 0)
            .attr("y", 0)
            .attr("width", additionalX)
            .attr("height", shotsScaleSvg(pitchElement.length))
            .attr("style", "cursor: pointer")
            .attr("fill", "transparent")
            .attr("data-event", counter)
            .attr("data-index", columns)
            .attr("data-index-j", rows)
            .on("click", function () {
                clickVerticalEvent(this, 2, 1);
            });
    counter++;
    pitchElement.pointLayer.append("rect")
            .attr("x", additionalX)
            .attr("y", 0)
            .attr("width", shotsScaleSvg(pitchElement.width) - (additionalX * 2))
            .attr("height", additionalY)
            .attr("style", "cursor: pointer")
            .attr("fill", "transparent")
            .attr("data-event", counter)
            .attr("data-index", columns + 1)
            .attr("data-index-j", rows + 1)
            .on("click", function () {
                clickVerticalEvent(this, 3, 1);
            });
    counter++;
    pitchElement.pointLayer.append("rect")
            .attr("x", shotsScaleSvg(pitchElement.width) - additionalX)
            .attr("y", 0)
            .attr("width", additionalX)
            .attr("height", shotsScaleSvg(pitchElement.length))
            .attr("style", "cursor: pointer")
            .attr("fill", "transparent")
            .attr("data-event", counter)
            .attr("data-index", columns + 2)
            .attr("data-index-j", rows + 2)
            .on("click", function () {
                clickVerticalEvent(this, 4, 1);
            });
    counter++;
}

function drawVerticalField() {
    var tmpMultiplier = pitchVertical.length / realShotsLength;
    var tmpScale = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500 * tmpMultiplier]);

    var baseBezier = 50 * tmpMultiplier;

    // rimuovo precedente se esistente
    d3.select("#" + pitchVertical.id).remove();
    var svg = d3.select("#" + currentShotsContainerId).append("svg")
            .attr("width", scaleSvg(pitchVertical.width + pitchVertical.padding.left + pitchVertical.padding.right))
            .attr("height", scaleSvg(pitchVertical.length + pitchVertical.padding.top + pitchVertical.padding.bottom))
            .attr("style", "background:" + pitchVertical.grassColor)
            .attr("id", pitchVertical.id);

    var baseLayer = svg.append("g")
            .attr("data-index", "0")
            .attr("transform", "translate(" + scaleSvg(pitchVertical.padding.left) + "," + scaleSvg(pitchVertical.padding.top) + ")");

    // fasce alternate verde chiaro e verde scuro
    var stripeHeight = pitchVertical.width / 8; // Altezza delle fasce
    var numStripes = Math.floor(scaleSvg(pitchVertical.width) / scaleSvg(stripeHeight));

    if (pitchVertical.isDefault) {
        for (var i = 0; i < numStripes; i++) {
            var stripeColor = i % 2 === 0 ? "#46ac6d" : "#40b36d"; // Alterna tra verde chiaro e scuro

            baseLayer.append("rect")
                    .attr("x", 0)
                    .attr("y", scaleSvg(i * stripeHeight))
                    .attr("width", scaleSvg(pitchVertical.width))
                    .attr("height", scaleSvg(stripeHeight))
                    .attr("fill", stripeColor);
        }
    }

    // linee dei campi
    baseLayer.append("rect")
            .attr("x", 0)
            .attr("y", 0)
            .attr("width", scaleSvg(pitchVertical.width))
            .attr("height", scaleSvg(pitchVertical.length))
            .attr("stroke", pitchVertical.paintColor)
            .attr("fill", "none");

    // corners
    function addPath(pathData, parentElement, size) {
        var path = parentElement.append("path")
                .attr("d", pathData)
                .attr("stroke", pitchVertical.paintColor)
                .attr("fill", "none");

        if (typeof size !== 'undefined') {
            path.attr("stroke-width", size);
        }
    }

    // top left
    var pathData = "M0," + scaleSvg(1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(1) + ",0";
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchVertical.width - 1) + ",0 A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(pitchVertical.width) + "," + scaleSvg(1);
    addPath(pathData, baseLayer);

    // bottom left
//    pathData = "M0," + scaleSvg(pitchVertical.length - 1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(1) + "," + scaleSvg(pitchVertical.length);
//    addPath(pathData, baseLayer);

    // top right
//    pathData = "M" + scaleSvg(pitchVertical.width - 1) + "," + scaleSvg(pitchVertical.length) + " A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(pitchVertical.width) + "," + scaleSvg(pitchVertical.length - 1);
//    addPath(pathData, baseLayer);

    // Top Penalty Area
    var penaltyAreaTop = baseLayer.append("g");
//    pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54);
    pathData = "M" + tmpScale(14) + ",0L" + tmpScale(14) + "," + tmpScale(16.5) + "L" + tmpScale(54) + "," + tmpScale(16.5) + "L" + tmpScale(54) + ",0";
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Area
    pathData = "M" + tmpScale(24.84) + ",0L" + tmpScale(24.84) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + ",0";
    addPath(pathData, penaltyAreaTop);

    // Top D
    pathData = "M" + tmpScale(42) + "," + tmpScale(16.5) + "A " + baseBezier + " " + baseBezier + " 5 0 1 " + tmpScale(26) + "," + tmpScale(16.5);
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Spot
    penaltyAreaTop.append("circle")
            .attr("cx", tmpScale(34))
            .attr("cy", tmpScale(11))
            .attr("r", 1)
            .attr("fill", pitchVertical.paintColor)
            .attr("stroke", pitchVertical.paintColor);

    pitchVertical.baseLayer = baseLayer;

    var pointLayer = svg.append("g")
            .attr("data-index", "1")
            .attr("transform", "translate(" + scaleSvg(pitchVertical.padding.left) + "," + scaleSvg(pitchVertical.padding.top) + ")");

    pitchVertical.pointLayer = pointLayer;

    // legenda
    var legendaWidth = scaleSvg(pitchVertical.width) * 1.5;
    d3.select("#pitchShotsLegendVertical").remove();
    var svgLegenda = d3.select("#" + currentShotsContainerId).append("svg")
            .attr("width", legendaWidth)
            .attr("height", scaleSvg(10))
            .attr("id", "pitchShotsLegendVertical");

    var baseLayerLegenda = svgLegenda.append("g")
            .attr("data-index", "0");

    baseLayerLegenda.append("text")
            .attr("x", function () {
                return (legendaWidth / 2);
            })
            .attr("y", function () {
                return scaleSvg(10) / 2;
            })  // Regola la posizione del testo verticalmente
            .attr("text-anchor", "middle") // Centro orizzontalmente
            .attr("alignment-baseline", "middle") // Centro verticalmente
            .attr("fill", "black")
            .attr("font-size", calculateTextNumberSize(10))
            .text(function () {
                return "* " + pageTranslation.get("blockedShots");
            });
}

function drawShotEvent(event, normalized) {
    var pos;

    if (typeof normalized === 'undefined' || normalized) {
        pos = event.mShot3DNormalized;
    } else {
        pos = event.mShot3D;
    }

    var player = event._player;
    var scaledRaggio = shotsRaggio * shotsMultiplier;

    if (pos && !pos.isDefault) {
        pos = getShotCoord(pos, event);
        var firstPoint = {
            cx: pos.x,
            cy: pos.y,
            r: scaledRaggio
        };

        if (player) {
            var isBlocked = false;
            event.mTags.forEach((tag) => {
                if (tag.code && (tag.code === 'TIF-1' || tag.code === 'TIS-1')) {
                    isBlocked = true;
                }
            });
            if (!isBlocked) {
                var type;
                // rete
                if (event.mType === "RTF" || event.mType === "RTS") {
                    type = 1;
                }
                if (typeof type === "undefined") {
                    event.mTags.forEach((tag) => {
                        if (tag.code && (tag.code === 'TIF-2' || tag.code === 'TIS-2')) {
                            type = 1;
                        }
                    });
                }
                if (typeof type === "undefined") {
                    // parato
                    event.mTags.forEach((tag) => {
                        if (tag.code && (tag.code === 'TIF-0' || tag.code === 'TIS-0')) {
                            type = 3;
                        }
                    });
                }
                if (typeof type === "undefined") {
                    // deviato, fuori o palo
                    event.mTags.forEach((tag) => {
                        if (tag.code && (tag.code === 'TIF-19' || tag.code === 'TIF-12' || tag.code === 'TIS-19' || tag.code === 'TIS-12' || tag.code === 'TIF-11' || tag.code === 'TIS-11')) {
                            type = 2;
                        }
                    });
                }

                var color, textColor;
                if (type === 1) {
                    color = shotColors.green;
                    textColor = shotTextColors.white;
                } else if (type === 2) {
                    color = shotColors.yellow;
                    textColor = shotTextColors.black;
                } else if (type === 3) {
                    color = shotColors.red;
                    textColor = shotTextColors.white;
                } else {
                    color = shotColors.yellow;
                    textColor = shotTextColors.black;
                }

                pitchShots.pointLayer.append("circle")
                        .attr("cx", firstPoint.cx)
                        .attr("cy", firstPoint.cy)
                        .attr("r", scaledRaggio)
                        .attr("fill", color)
                        .attr("data-event", "event-" + event._id)
                        .attr("isShot", true)
                        .on("click", highlightElements);

                firstPoint.cy = firstPoint.cy + scaledRaggio / 4;

                if (Object.keys(player).length === 1) {
                    // solo se singolo giocatore scrivo il numero
                    var playerObject = player[Object.keys(player)[0]];
                    var number = playerObject.matchNumber;
                    if (number) {
                        pitchShots.pointLayer.append("text")
                                .attr("x", function () {
                                    return parseFloat(firstPoint.cx);
                                })
                                .attr("y", function () {
                                    return parseFloat(firstPoint.cy);
                                })  // Regola la posizione del testo verticalmente
                                .attr("text-anchor", "middle") // Centro orizzontalmente
                                .attr("alignment-baseline", "middle") // Centro verticalmente
                                .attr("isShot", true)
                                .attr("fill", textColor)  // Colore del testo
                                .attr("font-size", calculateTextNumberSize(number))
                                .text(function () {
                                    return number;
                                })
                                .attr("data-event", "event-" + event._id)
                                .on("click", highlightElements)
                                .attr("data-tooltip", playerObject.known_name)
                                .call(tip)  // Chiamata per inizializzare d3-tip sul testo
                                .on("mouseover", function (d) {
                                    tip.show(d3.select(this).attr("data-tooltip"), this);
                                })
                                .on("mouseout", function (d) {
                                    tip.hide();
                                });
                    } else {
                        console.warn("drawShotEvent()", "Number not found for player " + playerObject.known_name + ". Event id is " + event._id);
                    }
                }

                drawVerticalEvent(event, normalized);
            }
        }
    } else {
        if (!event.mType === 'SUB' && !event.mType === 'SOS') {
            console.warn("drawShotEvent()", "can't draw event id " + event._id + ". Coords are empty");
        }
    }
}

function drawVerticalEvent(event, normalized) {
    var startPos, endPos;

    if (typeof normalized === 'undefined' || normalized) {
        startPos = event.mActionPosNormalized;
        endPos = event.mActionPosEndNormalized;
    } else {
        startPos = event.mActionPos;
        endPos = event.mActionPosEnd;
    }

    startPos = getVerticalCoord(startPos);
    endPos = getVerticalCoord(endPos);

    var player = event._player;
    var playerTo = event._playerTo;
    var scaledRaggio = raggio * multiplier;

    if (startPos && !startPos.isDefault) {
        var firstPoint = {
            cx: shotsScale(startPos.x),
            cy: shotsScale(startPos.y),
            r: scaledRaggio
        };
        var originalFirstPoint = {...firstPoint};
        var secondPoint, originalSecondPoint;

        if (player) {
            var color = teamColors.get("" + event._idTeam);
            if (event.mTeamColor) {
                color = event.mTeamColor;
            }

            if (event.mType === 'DLL' || event.mType === 'DLS' ||
                    event.mType === 'DRF' || event.mType === 'DRS' ||
                    event.mType === 'INT') {
                pitchVertical.pointLayer.append("rect")
                        .attr("x", firstPoint.cx - scaledRaggio - (scaledRaggio / 2))
                        .attr("y", firstPoint.cy - scaledRaggio - (scaledRaggio / 2))
                        .attr("width", scaledRaggio * 2)
                        .attr("height", scaledRaggio * 2)
                        .attr("fill", color)
                        .attr("data-event", "event-" + event._id)
                        .on("click", highlightElements);

                firstPoint.cx = firstPoint.cx - scaledRaggio / 2;
                firstPoint.cy = firstPoint.cy - scaledRaggio / 4;
            } else if (event.mType === 'FAF' || event.mType === 'AMM' || event.mType === 'ESP') {
                var firstCoord = (firstPoint.cx - scaledRaggio * 2 - scaledRaggio / 2) + "," + (firstPoint.cy + scaledRaggio);
                var secondCoord = firstPoint.cx - scaledRaggio / 2 + "," + (firstPoint.cy - scaledRaggio * 2);
                var thirdCoord = (firstPoint.cx + scaledRaggio * 2 - scaledRaggio / 2) + "," + (firstPoint.cy + scaledRaggio);

                pitchVertical.pointLayer.append("polygon")
                        .attr("points", firstCoord + " " + secondCoord + " " + thirdCoord)
                        .attr("fill", color)
                        .attr("data-event", "event-" + event._id)
                        .on("click", highlightElements);

                firstPoint.cx = firstPoint.cx - scaledRaggio / 2;
                firstPoint.cy = firstPoint.cy + scaledRaggio / 4;
            } else {
                pitchVertical.pointLayer.append("circle")
                        .attr("cx", firstPoint.cx - scaledRaggio / 2)
                        .attr("cy", firstPoint.cy - scaledRaggio / 2)
                        .attr("r", scaledRaggio)
                        .attr("fill", color)
                        .attr("data-event", "event-" + event._id)
                        .on("click", highlightElements);

                originalFirstPoint.cx = firstPoint.cx - scaledRaggio / 2;
                originalFirstPoint.cy = firstPoint.cy - scaledRaggio / 2;
                firstPoint.cx = firstPoint.cx - scaledRaggio / 2;
                firstPoint.cy = firstPoint.cy - scaledRaggio / 4;
            }

            if (Object.keys(player).length === 1) {
                // solo se singolo giocatore scrivo il numero
                var playerObject = player[Object.keys(player)[0]];
                var number = playerObject.matchNumber;
                if (number) {
                    var color = teamTextColors.get("" + event._idTeam);
                    if (event.mTeamTextColor) {
                        color = event.mTeamTextColor;
                    }

                    pitchVertical.pointLayer.append("text")
                            .attr("x", function () {
                                return parseFloat(firstPoint.cx);
                            })
                            .attr("y", function () {
                                return parseFloat(firstPoint.cy);
                            })  // Regola la posizione del testo verticalmente
                            .attr("text-anchor", "middle") // Centro orizzontalmente
                            .attr("alignment-baseline", "middle") // Centro verticalmente
                            .attr("fill", color)  // Colore del testo
                            .attr("font-size", calculateTextNumberSize(number))
                            .text(function () {
                                return number;
                            })
                            .attr("data-event", "event-" + event._id)
                            .on("click", highlightElements)
                            .attr("data-tooltip", playerObject.known_name)
                            .call(tip)  // Chiamata per inizializzare d3-tip sul testo
                            .on("mouseover", function (d) {
                                tip.show(d3.select(this).attr("data-tooltip"), this);
                            })
                            .on("mouseout", function (d) {
                                tip.hide();
                            });
                } else {
                    console.warn("drawVerticalEvent()", "Number not found for player " + playerObject.known_name + ". Event id is " + event._id);
                }
            }
        }

        if (endPos && !endPos.isDefault) {
            secondPoint = {
                cx: shotsScale(endPos.x),
                cy: shotsScale(endPos.y),
                r: scaledRaggio
            };
            originalSecondPoint = {...secondPoint};

            if (playerTo) {
                var color = teamColors.get("" + event._idTeam);
                if (event.mTeamColor) {
                    color = event.mTeamColor;
                }

                pitchVertical.pointLayer.append("circle")
                        .attr("cx", secondPoint.cx - scaledRaggio / 2)
                        .attr("cy", secondPoint.cy - scaledRaggio / 2)
                        .attr("r", scaledRaggio)
                        .attr("fill", color)
                        .attr("data-event", "event-" + event._id)
                        .on("click", highlightElements);

                originalSecondPoint.cx = secondPoint.cx - scaledRaggio / 2;
                originalSecondPoint.cy = secondPoint.cy - scaledRaggio / 2;
                secondPoint.cx = secondPoint.cx - scaledRaggio / 2;
                secondPoint.cy = secondPoint.cy + scaledRaggio / 4;

                var number = playerTo.matchNumber;
                if (number) {
                    var color = teamTextColors.get("" + event._idTeam);
                    if (event.mTeamTextColor) {
                        color = event.mTeamTextColor;
                    }

                    pitchVertical.pointLayer.append("text")
                            .attr("x", function () {
                                return parseFloat(secondPoint.cx);
                            })
                            .attr("y", function () {
                                return parseFloat(secondPoint.cy);
                            })  // Regola la posizione del testo verticalmente
                            .attr("text-anchor", "middle")  // Centra il testo rispetto al cerchio
                            .attr("fill", color)  // Colore del testo
                            .attr("font-size", calculateTextNumberSize(number))
                            .text(function () {
                                return number;
                            })
                            .attr("data-event", "event-" + event._id)
                            .on("click", highlightElements)
                            .attr("data-tooltip", playerTo.known_name)
                            .call(tip)  // Chiamata per inizializzare d3-tip sul testo
                            .on("mouseover", function (d) {
                                tip.show(d3.select(this).attr("data-tooltip"), this);
                            })
                            .on("mouseout", function (d) {
                                tip.hide();
                            });
                } else {
                    console.warn("drawVerticalEvent()", "Number not found for player " + playerTo.known_name + ". Event id is " + event._id);
                }
            }
        }

        if (firstPoint && secondPoint) {
            var drawArrow = !(shotsScale(event.mActionDistance) <= shotsScale(4));
            connectVerticalPoints(originalFirstPoint, originalSecondPoint, drawArrow, event);
        }
    } else {
        if (!event.mType === 'SUB' && !event.mType === 'SOS') {
            console.warn("drawVerticalEvent()", "can't draw event id " + event._id + ". Coords are empty");
        }
    }
}

function drawShotEvents() {
    d3.select("#infoText" + pitchShots.id).remove(); // tolgo testi se ci sono
    d3.select("#infoEventText" + pitchShots.id).remove();

    if (typeof actionTable !== 'undefined' && typeof eventMap !== 'undefined') {
        pitchShots.pointLayer.selectAll("*").remove();
        pitchVertical.pointLayer.selectAll("*").remove();
        tip.hide();

        var index = 0;
        var currentTableRows = actionTable.rows({filter: 'applied'})[0].length;
        var tableRows = actionTable.rows()[0].length;
        var atLeastOneFilter = $("#listAzioneRicerca").find("input:checked").length > 0
                || ($("#listAzioneRicerca").find("button").length === 1 && $("#listAzioneRicerca").find("button").attr("id").includes("TIS"))
                || ($("#listAzioneRicerca").find("button").length === 1 && $("#listAzioneRicerca").find("button").attr("id").includes("RTS"));
        if (pitchShots.percentageView !== 0) {
            pitchShots.percentageView = pitchShots.percentageView - 2;
            managePercentageView();

            // disegno comunque gli eventi nel campo verticale
            //if (currentTableRows !== tableRows || filteredRequest) {
            actionTable.rows({filter: 'applied'}).every(function () {
                var id = $(this.node()).attr("id").replace("rowEventId", "");

                var event = eventMap.get(id);
                if (atLeastOneFilter || (event.mType !== "TIS" && event.mType !== "RTS")) {
                    var canDraw = true;
                    if (!filteredRequest && !atLeastOneFilter && event.mType === "TIF") {
                        event.mTags.forEach((tag) => {
                            if (tag.code && tag.code === 'TIF-2') {
                                canDraw = false;
                                return;
                            }
                        });
                    }
                    // solo eventi che hanno posizione 3D
                    if (canDraw) {
                        if (event.mShot3DNormalized && !event.mShot3DNormalized.isDefault) {
                            if (id) {
                                drawVerticalEvent(event);
                            } else {
                                console.warn("drawShotEvents()", "Event id not found at index " + index);
                            }
                        }
                    }
                }
                index++;
            });
            //}
        } else {
            //if (currentTableRows !== tableRows || filteredRequest) {
            actionTable.rows({filter: 'applied'}).every(function () {
                var id = $(this.node()).attr("id").replace("rowEventId", "");

                var event = eventMap.get(id);
                if (atLeastOneFilter || (event.mType !== "TIS" && event.mType !== "RTS")) {
                    var canDraw = true;
                    if (!filteredRequest && !atLeastOneFilter && event.mType === "TIF") {
                        event.mTags.forEach((tag) => {
                            if (tag.code && tag.code === 'TIF-2') {
                                canDraw = false;
                                return;
                            }
                        });
                    }
                    if (canDraw) {
                        if (id) {
                            drawShotEvent(event);
                        } else {
                            console.warn("drawShotEvents()", "Event id not found at index " + index);
                        }
                    }
                }
                index++;
            });
            //}
        }
    } else {
        console.warn("drawShotEvents()", "actionTable or eventMap are undefined");
    }
}

function drawShotEventsIfNeeded() {
    if (typeof actionTable !== 'undefined' && typeof eventMap !== 'undefined') {
        if (pitchShots.pointLayer) {
            var elements = pitchShots.pointLayer.selectAll("*")._groups[0].length;
            if (elements === 0) {
                drawShotEvents();
            }
        } else {
            console.warn("drawShotEventsIfNeeded()", "pitch.pointLayer is not initialized");
        }
    } else {
        console.warn("drawShotEventsIfNeeded()", "actionTable or eventMap are undefined");
    }
}

function clickVerticalEvent(element, type, eventType) {
    if (eventType === 2) {  // tasto destro
        d3.event.preventDefault();
    }

    var d3Element = d3.select(element);
    var text = "";
    if (d3Element.attr("fill") === 'transparent') {
        d3Element.attr("fill", clickColors.to);
    } else {
        d3Element.attr("fill", "transparent");
    }

    var zoneId = parseInt(d3Element.attr("data-event"));
    var index = parseInt(d3Element.attr("data-index"));
    var indexJ = parseInt(d3Element.attr("data-index-j"));
    var coords;
    // LE COORDINATE PARTONO DAL PUNTO IN ALTO A SINISTRA DEL RETTANGOLO
    if (type === 1) {
        coords = eventType + "|" + (7.32 / 3 * indexJ + 30.34) + ";" + (2.44 - 2.44 / 3 * index) + ";" + (7.32 / 3) + ";" + (2.44 / 3);
    } else if (type === 2) {
        // fuori porta sinistra
        coords = eventType + "|" + (0) + ";" + (15) + ";" + (30.339) + ";" + (15);
    } else if (type === 3) {
        // fuori porta sopra
        coords = eventType + "|" + (30.34) + ";" + (15) + ";" + (7.319) + ";" + (12.559);
    } else if (type === 4) {
        // fuori porta destra
        coords = eventType + "|" + (37.661) + ";" + (15) + ";" + (30.341) + ";" + (15);
    }

    if (typeof jsSetButtonSelected === 'function') {
        jsShowBlockUI();
        setTimeout(function () {
            jsSetButtonSelected("" + 0, 9, 0, zoneId + "-_-" + coords); // uso campo comment
            checkModality();
            $.unblockUI();
        }, 150);
    }

    d3.select("#textShot" + index + indexJ).remove();
    if (typeof text !== 'undefined') {
        // devo sempre mettere le 2 linee altrimenti quando vado ad aggiornare
        // filtrando per giocatore o evento e la linea non è presente non aggiorna
        // il contatore
        if (zoneShotEventAmount.has(zoneId)) {
            text += zoneShotEventAmount.get(zoneId).length;
        } else {
            text += -1;
        }
        var lines = text.split("\n");
        var dy = (lines.length > 1 ? ((lines.length - 1) * scale(2)) : 0);

        pitchShotsFilter.pointLayer.append("text")
                .attr("x", parseFloat(d3Element.attr("width")) / 2 + parseFloat(d3Element.attr("x")))
                .attr("y", parseFloat(d3Element.attr("height")) / 2 + parseFloat(d3Element.attr("y")) + (calculateTextSize(12) / 2) - dy)
                .attr("text-anchor", "middle")
                .attr("fill", "gray")
                .attr("font-size", calculateTextSize(12))
                .attr("id", "textShot" + index + indexJ)
                .style("font-weight", "bold")
                .text(null)
                .each(function () {
                    var lines = text.split("\n");
                    for (var i = 0; i < lines.length; i++) {
                        var dy = i * 1.2;
                        var textContent = lines[i];
                        if (textContent === '-1') {
                            textContent = null;
                        }
                        var tspan = d3.select(this).append("tspan")
                                .attr("x", parseFloat(d3Element.attr("width")) / 2 + parseFloat(d3Element.attr("x")))
                                .attr("dy", dy + "em")
                                .text(textContent);

                        if (i === 0) { // nella seconda linea metto l'id
                            tspan.attr("id", "textShotAmount" + zoneId);
                            tspan.attr("font-size", calculateTextSize(10));
                            tspan.attr("eventAmountShotSpan", "1");
                        }
                    }
                });

        d3Element.raise();
    }
}

function resetShotFilters() {
    posizioniPortaSelected = [];
    applyFilterEvents(false, true);
    drawShotField("posizionaleShotsContainer");
    drawShotEvents();
    startAction('', 0, false);  // faccio ripartire la prima clip
}

function connectVerticalPoints(firstPoint, secondPoint, drawArrow, event, isConduzione) {
    // console.log(pointLayer, firstPoint, secondPoint, drawArrow);
    var distance = getPointsDistance(firstPoint, secondPoint);

    if (distance > minDistance) {
        // colore freccia
        var lineColor = getEventColor(event);
        if (typeof isConduzione !== 'undefined' && isConduzione) {
            // conduzione sempre nera
            lineColor = lineColors.black;
        }

        // controllo se c'è bisogno di creare la freccia
        if (typeof drawArrow !== 'undefined' && drawArrow) {
            var isArrowheadPresent = !pitchVertical.pointLayer.select("#arrowheadvertical" + lineColor.replace("#", "")).empty();
            if (!isArrowheadPresent) {
                pitchVertical.pointLayer.append("defs").append("marker")
                        .attr("id", "arrowheadvertical" + lineColor.replace("#", ""))
                        .attr("refX", 2)
                        .attr("refY", 2)
                        .attr("markerWidth", 6)
                        .attr("markerHeight", 4)
                        .attr("orient", "auto")
                        .attr("isShot", true)
                        .append("path")
                        .attr("d", "M0,0 L6,2 L0,4")
                        .style("fill", lineColor);
            }

            isArrowheadPresent = !pitchVertical.pointLayer.select("#arrowheadverticalHighlighted").empty();
            if (!isArrowheadPresent) {
                pitchVertical.pointLayer.append("defs").append("marker")
                        .attr("id", "arrowheadverticalHighlighted")
                        .attr("refX", 2)
                        .attr("refY", 2)
                        .attr("markerWidth", 6)
                        .attr("markerHeight", 4)
                        .attr("orient", "auto")
                        .attr("isShot", true)
                        .append("path")
                        .attr("d", "M0,0 L6,2 L0,4")
                        .style("fill", "lime");
            }
        }

        // dati primo punto
        var x1 = parseFloat(firstPoint.cx);
        var y1 = parseFloat(firstPoint.cy);
        var r1 = parseFloat(firstPoint.r);
        // dati secondo punto
        var x2 = parseFloat(secondPoint.cx);
        var y2 = parseFloat(secondPoint.cy);
        var r2 = parseFloat(secondPoint.r);

        var angle = Math.atan2(y2 - y1, x2 - x1);
        var startX = x1 + r1 * Math.cos(angle);
        var startY = y1 + r1 * Math.sin(angle);
        var endX = x2 - (r2) * Math.cos(angle);
        var endY = y2 - (r2) * Math.sin(angle);
//        if (typeof event._playerTo === 'undefined' || !event._playerTo) {
//            if (typeof event.mActionAngle !== 'undefined' && event.mActionAngle) {
//                if ((event.mActionAngle > 90 && event.mActionAngle < 270) || (event.mActionAngle < -90 && event.mActionAngle > -270)) {
//                    endX = x2 + (r2) * Math.cos(angle);
//                    endY = y2 + (r2) * Math.sin(angle);
//                }
//            }
//        }
        if (typeof event._playerTo !== 'undefined' && event._playerTo) {
            endX = x2 - (r2 * 2) * Math.cos(angle);
            endY = y2 - (r2 * 2) * Math.sin(angle);
        }

        // creo ora la linea
        var line = pitchVertical.pointLayer.append("line")
                .attr("x1", startX)
                .attr("y1", startY)
                .attr("x2", endX) // 6 è la lunghezza della freccia
                .attr("y2", endY) // 6 è la lunghezza della freccia
                .attr("stroke", lineColor)
                .attr("isShot", true)
                .attr("stroke-width", 2);
        if (typeof drawArrow !== 'undefined' && drawArrow) {
            line.attr("marker-end", "url(#arrowheadvertical" + lineColor.replace("#", "") + ")");
        }
        if (typeof event !== 'undefined' && event) {
            line.attr("data-event", "event-" + event._id);
            line.on("click", highlightElements);
        }
        if (typeof isConduzione !== 'undefined' && isConduzione) {
            line.attr("stroke-dasharray", "2,2"); // Imposta un modello di tratteggio (2 pixel di tratto, 2 pixel di spazio)
        }
    }
}

function managePercentageView() {
    // shotsScaleSvg(pitchShots.width) - ((62) * shotsMultiplier * 2)
    // shotsScaleSvg(pitchShots.length) - ((60) * shotsMultiplier)

    var current = pitchShots.percentageView;
    pitchShots.percentageView = pitchShots.percentageView + 2;
    if (current === 0 || current === 1) {
        $("#posizionaleShotsPercentage").addClass("uk-button-active");
        // quadrati tutta la porta
        var squareWidth = 7.32;
        var squareHeight = 2.44;
//        if (current === 0) {
//            // porta interna
//            squareWidth = shotsScaleSvg(pitchShots.width) - ((50) * shotsMultiplier * 2);
//            squareHeight = shotsScaleSvg(pitchShots.length) - ((50) * shotsMultiplier);
//        }
        const columns = 3, rows = 3;
        var pointWidth = squareWidth / columns;
        var pointHeight = squareHeight / rows;

        // Funzione per mappare le coordinate X e Y ai punti della mappa
        function mapCoordinatesToPoints(x, y) {
//            console.log(x, y, pointWidth, pointHeight);
//            if (current === 0) {
//                x = x / shotsScaleSvg(pitchShots.width) * squareWidth;
//                y = y / shotsScaleSvg(pitchShots.length) * squareHeight;
//            }
//            console.log(x, y, pointWidth, pointHeight);
            const pointX = Math.floor((x - 30.34) / pointWidth);
            const pointY = Math.floor(y / pointHeight);
            return {x: pointX, y: pointY};
        }

        // Struttura dati per tenere traccia della densità dei punti
        const heatmapData = [];
        var totalEvents = 0;

        for (var row = 0; row < rows; row++) {
            heatmapData[row] = [];
        }
        // Funzione per incrementare la densità dei punti in una specifica posizione
        function incrementHeatmapPoint(x, y) {
            const {x: pointX, y: pointY} = mapCoordinatesToPoints(x, y);
            if (!heatmapData[pointY]) {
                heatmapData[pointY] = [];
            }
            heatmapData[pointY][pointX] = (heatmapData[pointY][pointX] || 0) + 1;
        }

        var atLeastOneFilter = $("#listAzioneRicerca").find("input:checked").length > 0
                || ($("#listAzioneRicerca").find("button").length === 1 && $("#listAzioneRicerca").find("button").attr("id").includes("TIS"))
                || ($("#listAzioneRicerca").find("button").length === 1 && $("#listAzioneRicerca").find("button").attr("id").includes("RTS"));
        actionTable.rows({filter: 'applied'}).every(function () {
            var id = $(this.node()).attr("id").replace("rowEventId", "");

            var event = eventMap.get(id);
            var pos = {...event.mShot3DNormalized};
            if (pos && !pos.isDefault) {
                if (((event.mType === 'TIF' || event.mType === 'RTF') && pos.x < 52.5) ||
                        ((event.mType === 'TIS' || event.mType === 'RTS') && pos.x < 52.5)) {
                    pos.y = 68 - pos.y;
                }
                if (atLeastOneFilter || (event.mType !== "TIS" && event.mType !== "RTS")) {
                    var canContinue = false;
                    if (current === 0 && event.mType !== "RTF" && event.mType !== "RTS") {
                        // solo in porta
                        event.mTags.forEach((tag) => {
                            if (tag.code && (tag.code === 'TIF-15' || tag.code === 'TIS-15')) {
                                canContinue = true;
                            }
                        });
                    } else {
                        canContinue = true;
                    }
                    if (canContinue) {
                        var isBlocked = false;
                        event.mTags.forEach((tag) => {
                            if (tag.code && (tag.code === 'TIF-1' || tag.code === 'TIS-1')) {
                                isBlocked = true;
                            }
                        });
                        if (!isBlocked) {
//                            pos = getShotCoord(pos, event);
                            incrementHeatmapPoint(pos.y, pos.z);
                            totalEvents++;
                        }
                    }
                }
            }
        });

        var minValue, maxValue;
        for (var row = 0; row < rows; row++) {
            for (var column = 0; column < columns; column++) {
                const value = heatmapData[row][column];
                if (typeof value !== "undefined") {
                    if (typeof minValue === "undefined" || value < minValue) {
                        minValue = value;
                    }
                    if (typeof maxValue === "undefined" || value > maxValue) {
                        maxValue = value;
                    }
                }
            }
        }

        var colorScale = chroma.scale(['#1ea1e888', '#251ee888']).domain([minValue, maxValue]);
        pitchShots.pointLayer.selectAll("*").remove();
        var additionalX = 0, additionalY = 0;
        if (current === 0) {
            // porta interna
            additionalX = 50 * shotsMultiplier;
            additionalY = 50 * shotsMultiplier;
        }

        // imposto la grandezza dei rettangoli in base alla dimensione della porta
        squareWidth = shotsScaleSvg(pitchShots.width) - ((50) * shotsMultiplier * 2);
        squareHeight = shotsScaleSvg(pitchShots.length) - ((50) * shotsMultiplier);
        pointWidth = squareWidth / columns;
        pointHeight = squareHeight / rows;

        for (var row = 0; row < rows; row++) {
            for (var column = 0; column < columns; column++) {
                const value = heatmapData[row][column];
                if (typeof value !== "undefined") {
                    var rect = pitchShots.pointLayer.append("rect")
                            .attr("x", additionalX + pointWidth * column)
                            .attr("y", squareHeight - (additionalY + pointHeight * row))
                            .attr("width", pointWidth)
                            .attr("height", pointHeight)
                            // .attr("style", "outline: 1px solid " + colorScale(maxValue).hex())
                            .attr("style", "outline: 1px solid #ffffff60")
                            .attr("fill", colorScale(value).hex());

                    pitchShots.pointLayer.append("text")
                            .attr("x", function () {
                                return parseFloat(parseFloat(rect.attr("x")) + pointWidth / 2);
                            })
                            .attr("y", function () {
                                return parseFloat(parseFloat(rect.attr("y")) + pointHeight / 2);
                            })  // Regola la posizione del testo verticalmente
                            .attr("text-anchor", "middle") // Centro orizzontalmente
                            .attr("alignment-baseline", "middle") // Centro verticalmente
                            .attr("fill", "white")  // Colore del testo
                            .attr("font-size", calculateTextNumberSize(value))
                            .text(function () {
                                return value + " (" + (Math.round(value / totalEvents * 100 * 10) / 10) + "%)";
                            });
                }
            }
        }
    } else {
        // disattivo
        pitchShots.percentageView = 0;
        $("#posizionaleShotsPercentage").removeClass("uk-button-active");
        drawShotEvents();
    }
}

function getShotCoord(pos, event) {
    var newPos = {x: pos.y, y: pos.z, isDefault: false};
    var baseX = newPos.x;
    if (((event.mType === 'TIF' || event.mType === 'RTF') && pos.x < 52.5)
            || ((event.mType === 'TIS' || event.mType === 'RTS') && pos.x < 52.5)) {
        newPos.x = 68 - newPos.x;
    }

    newPos.x = newPos.x * shotsMultiplier;
    newPos.y = newPos.y * shotsMultiplier;

    var puntoXPartendoDaZero = newPos.x - (30.34 * shotsMultiplier);
    var scalaX = true, scalaY = true;

    var tmpCalc = shotsScaleSvg(pitchShots.width) / 16;

    if (newPos.y === 2.44) {
        // traversa
        newPos.y = tmpCalc;
        scalaY = false;
    }
    if (baseX === 37.66) {
        // palo destro
        newPos.x = (tmpCalc * 15);
        scalaX = false;
    } else if (baseX === 30.34) {
        // palo sinistro
        newPos.x = tmpCalc;
        scalaX = false;
    }

    if (scalaX && puntoXPartendoDaZero < 0) {
//        console.log("punto fuori porta a DESTRA");
        newPos.x = tmpCalc / 2;
        scalaX = false;
    } else if (scalaX && puntoXPartendoDaZero > (7.32 * shotsMultiplier)) {
//        console.log("punto fuori porta a SINISTRA");
        newPos.x = (tmpCalc * 16) - (tmpCalc / 2);
        scalaX = false;
    }
    if (scalaY && newPos.y > (2.44 * shotsMultiplier)) {
//        console.log("punto fuori porta SOPRA");
        newPos.y = tmpCalc / 2;
        scalaY = false;
    }
    // questo serve per sistemare il fatto che se la Z è troppo bassa viene mostrato
    // solo mezzo pallino. il valore 0.075 lo ho messo facendo un paio di prove, con il
    // raggio a 7.5 sembra andare bene (prob se cambia il raggio bisogna aggiornare sto valore)
    // da capire se conviene parametrizzare...
    if (scalaY && newPos.y < 0.075) {
        newPos.y = 0.075;
    }

    if (scalaX) {
        var larghezza = shotsScaleSvg(pitchShots.width) - (tmpCalc * 2);
        newPos.x = (puntoXPartendoDaZero / (7.32 * shotsMultiplier) * larghezza) + tmpCalc;
//        newPos.x = ((tmpCalc * 16) - puntoXPartendoDaZero / (7.32 * shotsMultiplier) * tmpCalc * 15);
    }
    if (scalaY) {
        var altezza = shotsScaleSvg(pitchShots.length) - tmpCalc;
        newPos.y = altezza - (newPos.y / (2.44 * shotsMultiplier) * altezza) + tmpCalc;
    }

    return newPos;
}

function getVerticalCoord(pos) {
    var newPos = {...pos};
    if (newPos.x < 52.5) {
        newPos.y = 68 - newPos.y;
    } else if (newPos.x > 52.5) {
        newPos.x = 105 - newPos.x;
    }
    var tmp = newPos.x;
    newPos.x = newPos.y;
    newPos.y = tmp;

    return newPos;
}

function updateZoneShotCounters(excludedZoneId) {
    // prima di tutto tolgo tutti i contatori così nel caso in cui passo da avere un contatore
    // a non averlo e non è la stessa zona che ho cliccato, devo togliere il suo valore
    d3.selectAll("tspan[eventAmountShotSpan]").text(null);

    // aggiorno tutti gli altri contatori
    zoneShotEventAmount.forEach(function (value, key) {
        if (typeof excludedZoneId === 'undefined' || !excludedZoneId || key !== excludedZoneId) {
            var textAmountElement = d3.select("#textShotAmount" + key);
            if (typeof textAmountElement !== 'undefined' && textAmountElement) {
                textAmountElement.text(value.length);
            } else {
                console.warn("Can't find element by id", ("#textShotAmount" + key));
            }
        }
    });
}

function takeBasePitchShotsScreen(svgElement, name) {
    // Serializza l'SVG
    var serializer = new XMLSerializer();
    var svgString = serializer.serializeToString(svgElement);
    svgString = svgString.replace('"/sicstv/images/porta2023.png"', '""');

    // Crea un oggetto Image per l'immagine esterna
    var imgExternal = new Image();
    imgExternal.crossOrigin = "Anonymous"; // Per consentire il caricamento dell'immagine esterna da un dominio diverso
    imgExternal.src = "/sicstv/images/porta2023.png"; // Modifica con il tuo URL esterno

    // Quando l'immagine esterna è caricata
    imgExternal.onload = function () {
        // Crea un oggetto Image per l'immagine SVG
        var imgSvg = new Image();
        imgSvg.src = 'data:image/svg+xml;base64,' + window.btoa(unescape(encodeURIComponent(svgString)));

        // Quando l'immagine SVG è caricata
        imgSvg.onload = function () {
            var canvas = document.createElement('canvas');
            canvas.width = svgElement.clientWidth;
            canvas.height = svgElement.clientHeight;
            var ctx = canvas.getContext('2d');

            // Disegna l'immagine esterna
            ctx.drawImage(imgExternal, 0, 0, shotsScale(pitchShots.width), shotsScale(pitchShots.length)); // Modifica x, y, width, height secondo le tue esigenze

            // Disegna l'immagine SVG
            ctx.drawImage(imgSvg, 0, 0);

            // Salva il canvas come PNG
            var a = document.createElement('a');
            a.href = canvas.toDataURL('image/png');
            a.download = name || 'image.png';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        };
    };
}

function handleShotsResize() {
    var baseWidth = 1920;

    // Aggiorna le dimensioni del tuo SVG in base alle nuove dimensioni della finestra
    var newWidth = window.innerWidth;
    var newPitchWidth = basePitchShotsWidth / baseWidth * newWidth;
    var newPitchLength = basePitchShotsLength / basePitchShotsWidth * newPitchWidth;

    var newPitchFilterWidth = basePitchShotsFilterWidth / baseWidth * newWidth;
    var newPitchFilterLength = basePitchShotsFilterLength / basePitchShotsFilterWidth * newPitchFilterWidth;

    var newPitchVerticalWidth = basePitchVerticalWidth / baseWidth * newWidth;
    var newPitchVerticalLength = basePitchVerticalLength / basePitchVerticalWidth * newPitchVerticalWidth;

    console.log("handleShotsResize()", "old pitch:", pitchShots.width, "x", pitchShots.length, "new pitch:", newPitchWidth, "x", newPitchLength);
    pitchShots.width = newPitchWidth;
    pitchShots.length = newPitchLength;

    pitchShotsFilter.width = newPitchFilterWidth;
    pitchShotsFilter.length = newPitchFilterLength;

    pitchVertical.width = newPitchVerticalWidth;
    pitchVertical.length = newPitchVerticalLength;

    drawShotField(currentShotsContainerId);
    drawShotEvents();
    if (typeof zoneShotEventAmount !== "undefined") {
        updateZoneShotCounters();
    }
}