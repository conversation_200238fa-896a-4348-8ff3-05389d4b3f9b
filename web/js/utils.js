//**********************************************************************
// function waitfor - Wait until a condition is met
//        
// Needed parameters:
//    test: function that returns a value
//    expectedValue: the value of the test function we are waiting for
//    msec: delay between the calls to test
//    callback: function to execute when the condition is met
// Parameters for debugging:
//    count: used to count the loops
//    source: a string to specify an ID, a message, etc
//**********************************************************************
function waitfor(test, expectedValue, msec, count, source, callback) {
    // Check if condition met. If not, re-check later (msec).
    while (test() !== expectedValue) {
        count++;
        setTimeout(function () {
            waitfor(test, expectedValue, msec, count, source, callback);
        }, msec);
        return;
    }
    // Condition finally met. callback() can be executed.
    // console.log(source + ': ' + test() + ', expected: ' + expectedValue + ', ' + count + ' loops.');
    callback();
}

function updateBreadcrumb(v) {
    $("#liBreadcrumb").html(v);
}

function jsFormatTime(totalSec) {
    if (isNaN(totalSec))
        return "00:00";
    var hours = parseInt(totalSec / 3600) % 24;
    var minutes = parseInt(totalSec / 60) % 60;
    var seconds = parseInt(totalSec % 60, 10);
    return (hours > 0 ? (hours < 10 ? "0" + hours : hours) + ":" : "") + (minutes < 10 ? "0" + minutes : minutes) + ":" + (seconds < 10 ? "0" + seconds : seconds);
}

function jsMatchTime(totalSec) {
    if (isNaN(totalSec))
        return "00:00";
    var minutes = parseInt(totalSec / 60000) % 60;
    var seconds = parseInt(totalSec % 60000, 10);
    return (hours > 0 ? (hours < 10 ? "0" + hours : hours) + ":" : "") + (minutes < 10 ? "0" + minutes : minutes) + ":" + (seconds < 10 ? "0" + seconds : seconds);
}

function jsFormatString(num, length) {
    var ret = '';
    if (isNaN(num))
        return "";
    var str = num + '';
    for (i = 1; i <= length - str.length; i++)
        ret += '0';
    return ret + num;
}

function jsLoadingAlpha(divDisplay) {
//$(divDisplay).html('<img src="/' + context + '/images/loadinfo-alpha.gif" border="0"/>');
    $(divDisplay).html("");
    $(divDisplay).addClass("uk-icon-circle-o-notch uk-icon-spin uk-icon-small orangeiconcolor");
    $(divDisplay).attr("style", "");
}
function jsLoadingOff(divDisplay) {
//$(divDisplay).html('');
    $(divDisplay).removeClass("uk-icon-circle-o-notch uk-icon-spin uk-icon-small orangeiconcolor");
}
function jsIsNullString(s)
{
    if (s == null || s == "null" || s == "" || s == undefined || s == 'undefined' || typeof (s) == 'undefined')
        return true;
    else
        return false;
}
function jsIsMobile()
{
    var ua = navigator.userAgent.toLowerCase();
    var isAndroid = ua.indexOf("android") > -1; //&& ua.indexOf("mobile");
    //var isiPad = /ipad/i.test(ua);
    var isiPhone = /iphone/i.test(ua);
    //alert(ua + " ipad=" + isiPad + " android=" + isAndroid);

    return (isAndroid || /*isiPad ||*/ isiPhone) ? true : false;
}

function ajaxSessionTimeout() {
    window.location.replace("/sicstv/auth/login.htm");
}

var selectedMatches = [];
function jsSelectMatch(id) {
    if ($.inArray(id, selectedMatches) === -1) {
        selectedMatches.push(id);
    } else {
        selectedMatches.splice($.inArray(id, selectedMatches), 1);
    }
    $("#selectedMatches").html("<i class='uk-icon-bars uk-icon-mini'></i> " + selectedMatches.length);
    if (selectedMatches.length > 0) {
        $("#selectedMatches").addClass("uk-button-active");
    } else {
        $("#selectedMatches").removeClass("uk-button-active");
    }
}



//funzione che carica il calendario
function jsRefreshMatchesCalendar(idC) {
    jsLoadingAlpha("#loaderSpinnerCalendar");
    if (typeof idC !== 'undefined' && idC) {
        var day = $("#mDay").val();
        var countryId = new URL(document.URL).searchParams.get("countryId");
        if (typeof countryId === 'undefined' || countryId === null) {
            countryId = "";
        }
        var groupId = new URL(document.URL).searchParams.get("groupId");
        if (typeof groupId === 'undefined' || groupId === null) {
            groupId = "";
        }

        var isMyUploadPage = typeof adjustCalendarTable === "function";
        if (typeof day === 'undefined') {
            $.ajax({
                type: "GET",
                url: "/sicstv/user/searchCalendarByCompetition.htm",
                cache: false,
                data: "&formCompetitionId=" + idC + "&formDay=&countryId=" + countryId + "&groupId=" + groupId + "&bypassGroupset=" + isMyUploadPage,
                success: function (msg) {
                    jsLoadingOff("#loaderSpinnerCalendar");
                    $("#calendario").html(msg);
                    if (isMyUploadPage) {
                        adjustCalendarTable();
                    }
//                    return false;
                }
            });
        } else {
            $.ajax({
                type: "GET",
                url: "/sicstv/user/searchCalendarByCompetition.htm",
                cache: false,
                data: "&formCompetitionId=" + idC + "&formDay=" + day + "&countryId=" + countryId + "&groupId=" + groupId + "&bypassGroupset=" + isMyUploadPage,
                success: function (msg) {
                    jsLoadingOff("#loaderSpinnerCalendar");
                    $("#calendario").html(msg);
                    if (isMyUploadPage) {
                        adjustCalendarTable();
                    }
//                    return false;
                }
            });
        }
    }
}

// funzione che carica le playlist
function jsLoadPlaylistTv(isMySicsTvPage) {
    var currentPlaylistId = new URL(document.URL).searchParams.get("playlistTvId");

    $.ajax({
        type: "GET",
        url: "/sicstv/user/playlistTv.htm",
        cache: false,
        data: "&isMySicsTvPage=" + isMySicsTvPage + (currentPlaylistId !== null ? ("&currentPlaylistId=" + currentPlaylistId) : ""),
        success: function (msg) {
            $("#playlistTv").html(msg);
            return false;
        }
    });
}

// funzione che carica la tabella delle statistiche
function jsLoadTeamStats(contentId, playerStats, competitionPlayersStats, filterIdToLoad, watchlistId, forceLoad, isGrouped) {
    if ($("#" + contentId).find("div").find("div").find("div").length <= 0 || (typeof forceLoad !== 'undefined' && forceLoad)) {
        var message = "Please wait while loading data... (It might take a while)";
        if (typeof teamStatsMessage !== 'undefined') {
            message = teamStatsMessage;
        }
        $("#" + contentId).empty();
        $("#" + contentId).html('<center><div class="loader"></div><p>' + message + '</p></center>');

        var data = "";
        new URL(document.URL).searchParams.forEach(function (value, key) {
            // non accetto tutti i parametri altrimenti se si fanno modifiche ai parametri
            // non funzionerebbe più la pagina
            if (key === 'formCompetitionId' || key === 'gameIds' || key === 'formTeamId' || key === 'countryId' || key === 'groupId') {
                data += "&" + key + "=" + value;
            }
        });
        if (typeof playerStats !== 'undefined' && playerStats) {
            data += "&playerStats=" + playerStats;
        }
        if (typeof filterIdToLoad !== 'undefined') {
            data += "&filterId=" + filterIdToLoad;
            sessionStorage["statsTypeLastFilterId"] = filterIdToLoad;
        } else {
            if (typeof sessionStorage["statsTypeLastFilterId"] !== "undefined") {
                data += "&filterId=" + sessionStorage["statsTypeLastFilterId"];
            }
        }
        if (typeof competitionPlayersStats === 'undefined' || !competitionPlayersStats) {
            data += "&competitionPlayers=true";
        }
        if (typeof watchlistId !== 'undefined') {
            data += "&watchlistId=" + watchlistId;
            if (typeof isGrouped !== 'undefined' && isGrouped !== null) {
                data += "&isGrouped=" + isGrouped;
            }
        }
        var fromDate = new URL(document.URL).searchParams.get("statsFrom");
        var toDate = new URL(document.URL).searchParams.get("statsTo");
        if (typeof fromDate !== 'undefined' && fromDate !== null) {
            data += "&from=" + fromDate;
            data += "&to=" + toDate;
        }

        // controllo se posso caricare tramite cache
//        var load = true;
//        var content = "";
//        var seasonId = $("#pageSeasonId").val();
//        var competitionId = new URL(document.URL).searchParams.get("formCompetitionId");
//        if (typeof seasonId !== "undefined" && seasonId !== null) {
//            var storedSeasonId = localStorage["seasonId"];
//            var storedCompetitionId = localStorage["competitionId"];
//            var storedContent = localStorage["content"];
//            
//            if (typeof storedSeasonId !== "undefined" && storedSeasonId !== null) {
//                if (parseInt(seasonId) === parseInt(storedSeasonId)) {
//                    // 1. Stagione giusta
//                    if (parseInt(competitionId) === parseInt(storedCompetitionId)) {
//                        // 2. Competizione giusta
//                        if (typeof storedContent !== "undefined" && storedContent) {
//                            content = storedContent;
//                            load = false;
//                        }
//                    }
//                }
//            }
//        }

//        if (load) {
        $.ajax({
            type: "GET",
            url: "/sicstv/user/teamStats.htm",
            cache: false,
            data: data,
            success: function (msg) {
                $("#" + contentId).html(msg);

//                    if (!data.includes("competitionPlayers=true")) { // salvo cache
//                        localStorage["seasonId"] = seasonId;
//                        localStorage["competitionId"] = competitionId;
//                        localStorage["content"] = msg.replaceAll(/\/\*[\s\S]*?\*\/|(?<=[^:])\/\/.*|^\/\/.*/g, ' ').replaceAll(/\s+/g, ' ');
//                    }
                return false;
            }
        });
//        } else {
//            $("#" + contentId).html(content);
//        }
    }
}

function jsLoadTeamStatsPlayers(contentId, filterIdToLoad, forceLoad) {
    jsLoadTeamStats(contentId, true, false, filterIdToLoad, undefined, forceLoad);
}

function jsLoadTeamStatsCompetitionPlayers(contentId, filterIdToLoad, forceLoad) {
    jsLoadTeamStats(contentId, true, true, filterIdToLoad, undefined, forceLoad);
}

function jsLoadTeamStatsFromWatchlist(contentId, filterIdToLoad, watchlistId, forceLoad, isGrouped) {
    jsLoadTeamStats(contentId, true, true, filterIdToLoad, watchlistId, forceLoad, isGrouped);
}

// funzione che carica il modale per modificare una clip
function jsLoadEditClipPage(playlistId, eventId) {
    $('#editClipPage').html("");
    $.ajax({
        type: "GET",
        url: "/sicstv/user/playlistTvEditEvent.htm",
        cache: false,
        data: encodeURI("&playlistId=" + playlistId + "&eventId=" + eventId),
        success: function (msg) {
            $("#editClipPage").html(msg);
            return false;
        }
    });
}

function closeModifyPlaylist() {
    if (typeof selectedPlaylist !== 'undefined') {
        if (selectedPlaylist.size + selectedGame.size > 0) {
            $("#menuDelete").removeClass("uk-hidden");
        } else {
            $("#menuDelete").addClass("uk-hidden");
        }
    }
    $("#saveModifyPlaylist").html("");
    $("#modalModifyPlaylist").removeClass("uk-open");
    $("#modalModifyPlaylist").addClass("uk-hidden");
    $("#namePlaylistModify").val("");
}

function closeSharePlaylist() {
    if (typeof selectedPlaylist !== 'undefined') {
        if (selectedPlaylist.size + selectedGame.size > 0) {
            $("#menuDelete").removeClass("uk-hidden");
        } else {
            $("#menuDelete").addClass("uk-hidden");
        }
    }
    $("#modalSharePlaylist").removeClass("uk-open");
    $("#modalSharePlaylist").addClass("uk-hidden");
}

// funzione che apre la pagina multipartita
function jsGoToMultipartita(personal, idTeam) {
    var ids = "";
    $.each(selectedMatches, function (index, value) {
        ids = ids + value + "-";
    });
    var pl = $("#idPlayer").val();
    if (typeof pl === 'undefined') {
        pl = '';
        console.log("SERPENTE" + pl);
    }
    //Multipartita giocatore disabilitato
    pl = '';

    if (typeof idTeam === 'undefined') {
        idTeam = "";
    }
    if (ids !== "") {
        location.href = '/sicstv/user/video.htm?personal=' + personal + '&id=&fixtureId=' + ids + '&idComp=-1&idTeam=' + idTeam + '&goals=true&idPlayer=' + pl + '&event=&filter=&limit=50';
    }
}

function jsSelectAll(val, element) {
    $("#" + element + " li.uk-active").find("input:checkbox").each(function () {
        if (val !== $(this).prop("checked")) {
            $(this).prop("checked", val);
            jsSelectMatch($(this).val());
        }
    });
}

function jsDownload(id, type) {
    var strUrl = "/sicstv/user/download.htm?id=" + id + "&type=" + type;
    $.ajax({
        type: "GET",
        url: strUrl,
        cache: false,
        success: function (msg) {
            if (msg.substr(0, 4) === "true") {
                UIkit.notify(msg.substr(5), {status: 'danger'});
            } else {
                jsReloadUserLimits(); // ricarico i limiti
                UIkit.notify("Download...", {status: 'success'});
                window.location.replace(msg.substr(5));
                $.unblockUI();
            }
        }
    });
}

var pageStatus = [];
function refreshMatches(loadingElement, elementToFill, page, comp, team, country, intComp, playlistTv) {
    var idComp = comp;
    var loadCalendar = false;
    var loadPlaylistTv = false;
    var lastSeason = false;
    if (typeof comp === 'undefined') {
        idComp = "";
    } else {
        loadCalendar = true;
    }

    if (typeof playlistTv !== 'undefined') {
        loadPlaylistTv = true;
    }

    var idTeam = team;
    if (typeof team === 'undefined') {
        idTeam = "";
    }

    var idCountry = country;
    if (typeof idCountry === 'undefined' || idCountry === null) {
        // controllo se ho un filtro su countryId in pagina
        idCountry = new URL(document.URL).searchParams.get("countryId");

        if (typeof idCountry === 'undefined' || idCountry === null) {
            idCountry = "";
        }
    }

    var idIntComp = intComp;
    if (typeof intComp === 'undefined') {
        idIntComp = "";
    }

    var groupId = new URL(document.URL).searchParams.get("groupId");
    if (typeof groupId === 'undefined' || groupId === null) {
        groupId = "";
    }

    var playerId = new URL(document.URL).searchParams.get("playerId");
    if (typeof playerId === 'undefined' || playerId === null) {
        playerId = "";
    }

    // si mette la variabile in pagina per attivare l'utilizzo dell'ultima stagione
    if (typeof useLastSeason !== "undefined") {
        lastSeason = true;
    }

    jsLoadingAlpha("#loaderSpinner");
    $.ajax({
        url: "/sicstv/user/refreshMatches.htm",
        cache: false,
        data: "page=" + page + "&parent=" + elementToFill + "&idComp=" + idComp + "&idTeam=" + idTeam + "&idCountry=" + idCountry + "&idIntComp=" + idIntComp + "&groupId=" + groupId + "&playerId=" + playerId + "&lastSeason=" + lastSeason,
        success: function (msg) {
            jsLoadingOff("#" + loadingElement);
            $("#" + elementToFill).html(msg);
            $("#numMatches").addClass("uk-hidden");
            pageStatus[elementToFill] = $("#mCurrentPage").val();
            if (loadCalendar) {
                jsRefreshMatchesCalendar(idComp);
            }
            if (loadPlaylistTv) {
                jsLoadPlaylistTv(false);
            }

            // se devo mettere il checked su alcuni game
            var idsToCheck = $("#gameIds").val();
            if (typeof idsToCheck !== 'undefined' && idsToCheck !== null) {
                if (idsToCheck !== '') {
                    idsToCheck.split(",").forEach(function (id) {
                        $("#checkbox_" + id).attr("checked", "checked"); // pagina team
                        $("#checkbox_all_" + id).attr("checked", "checked"); // pagina player
                    });
                }
            }

            // per la pagina team, ritorno che sono stati caricati
            if (typeof matchesLoaded !== 'undefined') {
                matchesLoaded = true;
            }
            return false;
        }, async: true
    });
}

var currentFilterMulti = [];

function readFilterMulti(excludeComp) {

    var arrayCurFilter = [];
    var arrayFilter = [];
    var arrayTeams = [];

    var split = $('#teamSelAutocomplete').val().split("@");
    var team = "";
    if (split.length > 1) {
        arrayFilter.push("<b>Team:</b> " + split[1]);
        arrayTeams.push(split[0]);
        team = arrayTeams.join('-');
    }

    var arrayPlayer = [];
    var split = $('#playerSelAutocomplete').val().split("@");
    var player = "";
    if (split.length > 1) {
        arrayFilter.push("<b>Player:</b> " + split[1]);
        arrayPlayer.push(split[0]);
        player = arrayPlayer.join('-');
    } else {
        player = $("#idPlayer").val();
    }

    var dateF = $('#inputDateFromMulti').val();
    var dateT = $('#inputDateToMulti').val();
    var module = $("#selectModuleMulti option:selected").val();
    var moduleVs = $("#selectModuleVsMulti option:selected").val();

    var score = $("#selectScoreMulti option:selected").val();

    var homeAway = $("#selectHomeAwayMulti option:selected").val();

    if (typeof player === 'undefined') {
        player = "";
    }

    if (typeof moduleVs === 'undefined') {
        moduleVs = "";
    }

    if (typeof score === 'undefined') {
        score = "";
    }

    if (typeof homeAway === 'undefined') {
        homeAway = "";
    }

    if (dateF !== "") {
        arrayFilter.push(strings['menu.user.daData'] + ": " + dateF);
    }
    arrayCurFilter.push(dateF);
    if (dateT !== "") {
        arrayFilter.push(strings['menu.user.aData'] + ": " + dateT);

    }
    arrayCurFilter.push(dateT);
    if (module !== "") {
        arrayFilter.push(module);
    }
    arrayCurFilter.push(module);
    if (moduleVs !== "") {
        arrayFilter.push("<b>Vs:</b> " + moduleVs);
    }
    arrayCurFilter.push(moduleVs);
    if (score !== "") {
        if (score === "V") {
            arrayFilter.push(strings['menu.user.vittorie']);
        } else if (score === "N") {
            arrayFilter.push(strings['menu.user.pareggi']);
        } else {
            arrayFilter.push(strings['menu.user.sconfitte']);
        }

    }
    arrayCurFilter.push(score);
    if (homeAway !== "") {
        if (homeAway === "home") {
            arrayFilter.push(strings['menu.user.incasa']);
        } else {
            arrayFilter.push(strings['menu.user.intrasferta']);
        }
    }
    arrayCurFilter.push(homeAway);

    var stringFilter = "";
    if (arrayFilter.length > 0) {
        stringFilter = " - " + arrayFilter.join(" , ");
    }
    // bisogna lasciare l'END alla fine perché altrimenti lo split sbarella nel caso imposto soltanto la data di FROM
    var curFilterValue = arrayCurFilter.join(",") + ",END";
    var idComp = "";

    // se nella pagina competizione
    if (typeof $("#idComp").val() !== 'undefined' && !excludeComp) {
        idComp = $("#idComp").val();
    }

    // se nella pagina team
    if (typeof $("#idTeam").val() !== 'undefined') {
        team = $("#idTeam").val();
    }

    currentFilterMulti['team'] = team;
    currentFilterMulti['player'] = player;
    currentFilterMulti['dateF'] = dateF;
    currentFilterMulti['dateT'] = dateT;
    currentFilterMulti['module'] = module;
    currentFilterMulti['moduleVs'] = moduleVs;
    currentFilterMulti['score'] = score;
    currentFilterMulti['homeAway'] = homeAway;
    currentFilterMulti['stringFilter'] = stringFilter;
    currentFilterMulti['curFilterValue'] = curFilterValue;
    currentFilterMulti['idComp'] = idComp;
}


function filterMatches(targetElement, page) {
    jsLoadingAlpha("#" + targetElement);
    var team = currentFilterMulti['team'];
    var player = currentFilterMulti['player'];
    var dateF = currentFilterMulti['dateF'];
    var dateT = currentFilterMulti['dateT'];
    var module = currentFilterMulti['module'];
    var moduleVs = currentFilterMulti['moduleVs'];
    var score = currentFilterMulti['score'];
    var homeAway = currentFilterMulti['homeAway'];
    var stringFilter = currentFilterMulti['stringFilter'];
    var curFilterValue = currentFilterMulti['curFilterValue'];
    var idComp = currentFilterMulti['idComp'];

    var param = "page=" + page + "&parent=" + targetElement + "&idTeam=" + team + "&idPlayer=" + player + "&idComp=" + idComp + "&idModule=" + module + "&idModuleVs=" + moduleVs + "&dateFrom=" + dateF + "&dateTo=" + dateT + "&score=" + score + "&homeaway=" + homeAway;

    $.ajax({
        type: "GET",
        url: "/sicstv/user/filterMatches.htm",
        data: param,
        cache: false,
        success: function (msg) {
            jsLoadingOff("#" + targetElement);

            $("#" + targetElement).html(msg);
            var numMatches = $("#mNumMatches").val();
            $("#numMatches").html(numMatches);
            $("#numMatches").removeClass("uk-hidden");
            UIkit.offcanvas.hide([force = false]);
            //resetFilterForm();
            var filterValue = "<input type='hidden' id='gameFilter' value='" + curFilterValue + "'/>";
            $("#showCurrentFilter").html(stringFilter + filterValue);
            return false;
        }
    });
}

// logout ajax
function ajaxSessionTimeoutExpired() {
    window.location.href = "/sicstv/auth/login.htm?expired=true";
}
// logout ajax
function ajaxSessionTimeout() {
    window.location.href = "/sicstv/auth/login.htm";
}
// logout ajax
function ajaxSessionTimeoutDisposed() {
    window.location.href = "/sicstv/auth/login.htm?session=disposed";
}

function clickLogin() {
    $.ajax({
        type: "GET",
        url: "/sicstv/user/getIn.htm",
        data: encodeURI("identifier=1"),
        cache: false,
        success: function (msg) {
            $("#myModal").removeClass("uk-open");
            $("#myModal").addClass("uk-hidden");
            checkSession();
        }
    });
}

function clickLogout() {
    $.ajax({
        type: "GET",
        url: "/sicstv/user/getOut.htm",
        data: encodeURI("identifier=1"),
        cache: false,
        success: function (msg) {
            ajaxSessionTimeout();
        }
    });

}

function saveTab(elementId, identifier) {
    var path = window.location.pathname;
    var page = path.split("/").pop();

    $.ajax({
        type: "GET",
        url: "/sicstv/user/saveTab.htm",
        cache: false,
        data: encodeURI("&pageName=" + page + "&elementId=" + elementId + "&identifier=" + identifier)
    });
}

function setUserSeasonId(seasonId) {
    var adjustedSeasonId = seasonId;
    if (seasonId.includes("-")) { // calcistico
        adjustedSeasonId = seasonId.split("-")[0];
    } else { // solare
        adjustedSeasonId = seasonId.substring(2, 4);
    }

    $.ajax({
        type: "GET",
        url: "/sicstv/user/changeSeason.htm",
        cache: false,
        data: encodeURI("newSeason=" + adjustedSeasonId)
    });
}

function jsShowBlockUI() {
    $.blockUI({
        message: '<div class="loader"></div>',
        css: {
            border: 'none',
            padding: '0',
            backgroundColor: 'transparent',
            textAlign: 'center',
            fontFamily: 'Arial, sans-serif',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
        },
        overlayCSS: {
            backgroundColor: '#757575'
        }
    });
}

function jsBlockElement(element) {
    $(element).block({
        message: '<div class="loader"></div>',
        css: {
            border: 'none',
            padding: '0',
            backgroundColor: 'transparent',
            textAlign: 'center',
            fontFamily: 'Arial, sans-serif',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
        },
        overlayCSS: {
            backgroundColor: '#757575'
        }
    });
}

function hideElement(id) {
    setTimeout(function () {
        $("#" + id).addClass("uk-hidden");
    }, 100);
}

function jsManageActive(element) {
    if ($(element).hasClass("uk-button-active")) {
        $(element).removeClass("uk-button-active");
    } else {
        $(element).addClass("uk-button-active");
    }
}

function loadPlayerOverview(contentId, filterId, roleId) {
    $("#" + contentId).html('<center><div class="loader"></div></center>');

    var data = "";
    new URL(document.URL).searchParams.forEach(function (value, key) {
        // non accetto tutti i parametri altrimenti se si fanno modifiche ai parametri
        // non funzionerebbe più la pagina
        if (key === 'playerId' || key === 'competitionId' || key === 'teamId' || key === 'groupId' || key === 'gameIds') {
            data += "&" + key + "=" + value;
        }
    });

    if (!data.includes("competitionId")) {
        data += "&competitionId=-1";
    }
    if (!data.includes("teamId")) {
        data += "&teamId=-1";
    }
    if (typeof filterId !== 'undefined') {
        data += "&filterId=" + filterId;
    }
    if (typeof roleId !== 'undefined') {
        data += "&roleId=" + roleId;
    }
    $.ajax({
        type: "GET",
        url: "/sicstv/user/playerOverview.htm",
        cache: false,
        data: data,
        success: function (msg) {
            $("#" + contentId).html(msg);
            return false;
        }
    });
}

function loadTeamOverview(contentId) {
    $("#" + contentId).html('<center><div class="loader"></div></center>');

    var data = "";
    new URL(document.URL).searchParams.forEach(function (value, key) {
        // non accetto tutti i parametri altrimenti se si fanno modifiche ai parametri
        // non funzionerebbe più la pagina
        if (key === 'formTeamId' || key === 'formCompetitionId' || key === 'groupId' || key === 'gameIds') {
            data += "&" + key + "=" + value;
        }
    });

    if (!data.includes("formCompetitionId") || data.includes("formCompetitionId=-1")) {
        data = data.replace("&formCompetitionId=-1", "");

        // le prendo dalla pagina
        var competitions = "";
        $("#competitions > ul > li > a > div.competitionBox").each(function () {
            var attr = $(this).attr("id");
            if (attr && !attr.includes("-")) { // niente gironi oppure competizione -1 (tutte)
                if (competitions !== "")
                    competitions += ",";
                competitions += $(this).attr("id");
            }
        });
        data += "&formCompetitionId=" + competitions;
    }
    if (data.includes("formTeamId")) {
        $.ajax({
            type: "GET",
            url: "/sicstv/user/teamOverview.htm",
            cache: false,
            data: data,
            success: function (msg) {
                $("#" + contentId).html(msg);
                return false;
            }
        });
    } else {
        alert("Can't find any team to load.");
    }
}

function jsReloadFavorites() {
    $.ajax({
        type: "GET",
        url: "/sicstv/user/getFavorites.htm",
        cache: false,
        success: function (msg) {
            $("#favoritesContent").html(msg);
            return false;
        }
    });
}

function jsReloadShortcuts() {
    $.ajax({
        type: "POST",
        url: "/sicstv/user/getShortcuts.htm",
        data: encodeURI("link=" + encodeURIComponent(window.location.href.replace(window.location.origin, ""))),
        cache: false,
        success: function (msg) {
            $("#shortcutsContent").html(msg);
            return false;
        }
    });
}

var userLimitsTyppy;
function jsReloadUserLimits() {
    $.ajax({
        type: "POST",
        url: "/sicstv/user/getUserLimits.htm",
        cache: false,
        success: function (msg) {
            $("#userLimitsContent").html(msg);

            var tippyContent = $("#userLimitsTooltipContent").html();
            if (typeof userLimitsTyppy === 'undefined') {
                userLimitsTyppy = tippy("#userLimitsButton", {
                    theme: 'light-border',
                    content: tippyContent,
                    interactive: true,
                    maxWidth: 600,
                    allowHTML: true,
                    placement: 'bottom',
                    trigger: 'click mouseenter focus focusin manual'
                });
            } else {
                userLimitsTyppy.forEach(function (element) {
                    element.setContent(tippyContent);
                });
            }
            return false;
        }
    });
}

function jsGetGameData(gameId, type) {
    UIkit.notify("Downloading " + type + "...", {status: 'success', timeout: 1000});

    $.ajax({
        type: "GET",
        url: "/sicstv/user/getGameXML.htm",
        data: encodeURI("gameId=" + gameId + "&type=" + type),
        contentType: "application/x-www-form-urlencoded",
        cache: false,
        success: function (content) {
            if (content === 'limitReached') {
                UIkit.notify("Export Failed: Limit Reached", {status: 'warning', timeout: 1000});
            } else {
                var contentToDownload, fileName;
                if (content.includes("--.--")) {
                    contentToDownload = content.split("--.--")[0];
                    fileName = content.split("--.--")[1];
                } else {
                    contentToDownload = content;
                    fileName = "game" + type;
                }
                if (type === 'xml') {
                    downloadXML(contentToDownload, fileName);
                } else {
                    downloadJSON(contentToDownload, fileName);
                }
                jsReloadUserLimits(); // ricarico i limiti
            }
            $.unblockUI();
            return false;
        }
    });
}

function downloadXML(xmlContent, fileName) {
    // Converti la stringa XML in un oggetto XML
    var parser = new DOMParser();
    var xmlDoc = parser.parseFromString(xmlContent, "text/xml");

    // Crea un oggetto Blob contenente il tuo XML
    var blob = new Blob([xmlContent], {type: "text/xml"});

    // Crea un URL temporaneo per il Blob
    var url = URL.createObjectURL(blob);

    // Crea un link ancorato per il download
    var a = document.createElement("a");
    a.href = url;
    a.download = fileName + ".xml";

    // Aggiungi il link al documento e scatena il click
    document.body.appendChild(a);
    a.click();

    // Rimuovi il link dal documento
    document.body.removeChild(a);

    // Rilascia l'URL creato per il Blob
    URL.revokeObjectURL(url);
}

function downloadJSON(jsonString, fileName) {
    // Converti la stringa JSON in un oggetto JSON
    var jsonObj = JSON.parse(jsonString);

    // Crea un oggetto Blob contenente il tuo JSON
    var blob = new Blob([jsonString], {type: "application/json"});

    // Crea un URL temporaneo per il Blob
    var url = URL.createObjectURL(blob);

    // Crea un link ancorato per il download
    var a = document.createElement("a");
    a.href = url;
    a.download = fileName + ".json";

    // Aggiungi il link al documento e scatena il click
    document.body.appendChild(a);
    a.click();

    // Rimuovi il link dal documento
    document.body.removeChild(a);

    // Rilascia l'URL creato per il Blob
    URL.revokeObjectURL(url);
}

function isValidExportRequest(actions) {
    var result = "";
    var removeDuplicates = new URL(document.URL).searchParams.get("removeDuplicates");
    var gameId = new URL(document.URL).searchParams.get("id");

    // 1. MAX 250 CLIP
    // se sono in esportazione tempo effettivo, non limito per numero di clip
    if (actions.length > 250) {
        if (typeof removeDuplicates === 'undefined' || removeDuplicates === null || gameId === '') {
            result += "AMOUNT";
        } else {
            result += "TEMPO_EFFETTIVO";
        }
    }

    // 2. MAX 120 MINUTI TOTALI
    var totalTime = 0, minTime = -1, maxTime = 0;
    actions.forEach(function (element) {
        if (typeof clipDurationMap !== 'undefined' && clipDurationMap !== null) {
            var formattedTime = clipDurationMap.get("" + element);

            var minutes = parseInt(formattedTime.split(":")[0]);
            var seconds = parseInt(formattedTime.split(":")[1]);

            var clipTime = (minutes * 60) + seconds;
            totalTime += clipTime;

            // 3. MAX 5 MIN SINGOLA CLIP
            if (clipTime > maxTime) {
                maxTime = clipTime;
            }

            // 4. MIN 1 SEC SINGOLA CLIP
            if (clipTime < minTime || minTime === -1) {
                minTime = clipTime;
            }

            // console.log("Clip " + element + ": " + clipTime + ". Min: " + minTime + " ; Max: " + maxTime + " ; Total: " + totalTime);
        }
    });

    if (totalTime > 7200) { // 7200 secondi = 120 min
        result += "TOTAL_TIME";
    }
    if (maxTime >= 300) {
        result += "MAX_CLIP_TIME";
    }
    if (minTime < 1) {
        result += "MIN_CLIP_TIME";
    }

    return result;
}

// color deve arrivare dalla libreria chroma.js
function getColoreInContrasto(color) {
    var result = "white";
    if (color[1] > 190) { // GREEN
        result = "black";
    } else if (color[0] > 190) { // RED
        if ((color[1] > 190 || color[2] > 190) || (color[1] > 127 || color[2] > 127)) {
            result = "black";
        }
    } else if (color[2] > 190) {
        if ((color[1] > 190 || color[0] > 190) || (color[1] > 128 || color[0] > 128)) {
            result = "black";
        }
    }
    return result;
}

// funzione usata per stampare il tabellino
function printElement(elem, append, delimiter) {
    var domClone = elem.cloneNode(true);
    var printSection = document.getElementById("printSection");
    if (!printSection) {
        printSection = document.createElement("div");
        printSection.id = "printSection";
        document.body.appendChild(printSection);
    }

    if (append !== true) {
        printSection.innerHTML = "";
    } else if (append === true) {
        if (typeof (delimiter) === "string") {
            printSection.innerHTML += delimiter;
        } else if (typeof (delimiter) === "object") {
            printSection.appendChild(delimiter);
        }
    }

    printSection.appendChild(domClone);
}

function addPlayerNewButton(data) {
    var myPlayer = data.player,
            controlBar,
            newElement = document.createElement('div'),
            newLink = document.createElement('a');

    newElement.id = data.id;
    newElement.className = 'downloadStyle vjs-control center center-vertically';

    newLink.innerHTML = data.content;
    newElement.appendChild(newLink);
    controlBar = document.getElementsByClassName('vjs-control-bar')[0];
    insertBeforeNode = document.getElementsByClassName(data.insertBeforeClassName)[0]; // vjs-fullscreen-control
    controlBar.insertBefore(newElement, insertBeforeNode);

    return newElement;
}

function addPlayerNewImage(data) {
    var myPlayer = data.player,
            controlBar,
            newElement = document.createElement('div');

    newElement.id = data.id;
    newElement.className = 'column downloadStyle vjs-control center center-vertically uk-hidden';

    newElement.innerHTML = data.content;
    controlBar = document.getElementsByClassName('vjs-control-bar')[0];
    insertBeforeNode = document.getElementsByClassName(data.insertBeforeClassName)[0]; // vjs-fullscreen-control
    controlBar.insertBefore(newElement, insertBeforeNode);

    return newElement;
}

function initializeLogoDropzone(elementId) {
    return new Dropzone("#" + elementId, {
        acceptedFiles: ".png", // Solo file PNG
        init: function () {
            this.on("success", function (file, response) {
                location.reload();
            });
            this.on("error", function (file, errorMessage) {
                console.error("Errore durante il caricamento del file:", errorMessage);
            });
        },
        url: "/sicstv/user/uploadLogo.htm?", // Indirizzo URL per il caricamento dei file
        baseUrl: "/sicstv/user/uploadLogo.htm?",
        method: "post", // Metodo HTTP per il caricamento
        paramName: "file", // Nome del parametro che contiene il file
        autoProcessQueue: true
    });
}

function removeUnwantedCharacter(text) {
    return text.replace(/ /g, "_").replace(/[^a-zA-Z0-9_]/g, "");
}

function changeLanguage(language, error) {
    $.ajax({
        type: "GET",
        url: "/sicstv/user/changeLanguage.htm",
        cache: false,
        data: encodeURI("language=" + language),
        success: function (result) {
            if (result === "ok") {
                location.reload();
            } else {
                UIkit.notify(error, {status: 'danger', timeout: 1000});
            }
            return false;
        }
    });
}

function removeDuplicates(events) {
    // mStart, mEnd
    // se mStart >= mStartBase e <= mEndBase allora controlla
    // se mType o playerId
    var eventsToRemove = new Set();
    var eventList = [];

    events.every(function () {
        var eventId = $(this.node()).attr("id").replace("rowEventId", "");
        eventList.push(eventMap.get("" + eventId));
    });

    // Ordina gli eventi per mStart
    eventList.sort(function (a, b) {
        var x = a.mStart;
        var y = b.mStart;
        return ((x < y) ? -1 : ((x > y) ? 1 : 0));
    });

    // console.log(eventList);

    for (let i = 0; i < eventList.length; i++) {
        var eventId = eventList[i]._id;
        if (!eventsToRemove.has(eventId)) { // se già escluso skippo, per velocizzare
            var eventData = eventList[i];
            var basemStart = eventData.mStart, basemEnd = eventData.mEnd;

            for (let j = i + 1; j < eventList.length; j++) {
                var tmpEventId = eventList[j]._id;
                if (!eventsToRemove.has(tmpEventId)) { // se già escluso skippo, per velocizzare
                    var tmpEventData = eventList[j];
                    var mStart = tmpEventData.mStart, mEnd = tmpEventData.mEnd;

                    if (mStart <= basemEnd) { // solo se c'è una sovrapposizione potenziale
                        if (1 === 1) {
                            // console.log("to check", eventData, tmpEventData);
                            // clip da verificare se togliere
                            if (eventData.mType !== tmpEventData.mType || (eventData.playerId !== 0 && eventData.playerId === tmpEventData.playerId)) {
                                // playerId è 0 nel caso in cui non c'è il giocatore
                                // se evento diverso allora escludo oppure se trovo lo stesso giocatore
                                eventsToRemove.add(tmpEventId);
                                $("#rowEventId" + tmpEventId).removeClass("eventToShow");
                                // console.log("removed", tmpEventData);
                            }
                        }
                    } else {
                        // Uscire dal ciclo interno se gli eventi successivi non possono sovrapporsi
                        break;
                    }
                }
            }
        }
    }

    console.log("Events to remove: ", Array.from(eventsToRemove));
    events.every(function () {
        var eventId = $(this.node()).attr("id").replace("rowEventId", "");
        if (eventsToRemove.has(parseInt(eventId))) {
            $(this.node()).removeClass("eventToShow");
        }
    });
}

var removeDuplicatesEventPriority = ["ATT", "DIF", "PIF", "PIC"];
var removeDuplicatesEventsUnwanted = ["PASS", "PER", "REC", "INT", "LINO"];
function removeDuplicatesV2(events) {
    // mStart, mEnd
    // se mStart >= mStartBase e <= mEndBase allora controlla
    // se mType o playerId
    var eventsToRemove = new Set();
    var eventList = [];

    events.every(function () {
        var eventId = $(this.node()).attr("id").replace("rowEventId", "");
        eventList.push(eventMap.get("" + eventId));
    });

    // Ordina gli eventi per mStart
    eventList.sort(function (a, b) {
        var x = a.mStart;
        var y = b.mStart;
        return ((x < y) ? -1 : ((x > y) ? 1 : 0));
    });

    // se il giocatore è impostato (parametro playerId) togliere di base sempre ATT e DIF
    var idPlayer = new URL(document.URL).searchParams.get("idPlayer");
    if (idPlayer !== null && idPlayer !== "") {
        // console.log("STEP 1: remove duplicates for player " + idPlayer);
        // controllo se non ci sono filtri sugli eventi selezionati
        var atLeastOneFilter = $("#listAzioneRicerca").find("input:checked").length > 0
            || $("#liFiltriTacticalContent").find("input:checked").length > 0
            || ($("#listAzioneRicerca").find("button").length === 1 && $("#listAzioneRicerca").find("button").attr("id").includes("TIS"))
            || ($("#listAzioneRicerca").find("button").length === 1 && $("#listAzioneRicerca").find("button").attr("id").includes("RTS"));

        if (!atLeastOneFilter) {
            // console.log("STEP 2: remove duplicates for player " + idPlayer + " (no filters)");
            // dagli eventi tolgo ATT e DIF
            for (let i = 0; i < eventList.length; i++) {
                var eventData = eventList[i];

                if (eventData.mType === "ATT" || eventData.mType === "DIF") {
                    if (eventData.mType === "ATT" || eventData.mType === "DIF") {
                        eventsToRemove.add(eventData._id);
                    }
                }
            }
        }
    }

    for (let k = 0; k < removeDuplicatesEventPriority.length; k++) {
        for (let i = 0; i < eventList.length; i++) {
            var eventId = eventList[i]._id;
            if (!eventsToRemove.has(eventId)) { // se già escluso skippo, per velocizzare
                var eventData = eventList[i];

                if (eventData.mType === removeDuplicatesEventPriority[k]) {
                    for (let j = 0; j < eventList.length; j++) {
                        var tmpEventData = eventList[j];
                        if (!eventsToRemove.has(tmpEventData._id)) {
                            if (eventData._id !== tmpEventData._id) {
                                // escludo me stesso
                                if (tmpEventData.mStart <= eventData.mEnd) {
                                    if (tmpEventData.mStart >= (eventData.mStart - 1000)) { // aggiungo 1 secondo di scarto
                                        if (tmpEventData.mType !== eventData.mType || (eventData.playerId !== 0 && eventData.playerId === tmpEventData.playerId)) {
                                            eventsToRemove.add(tmpEventData._id);
                                        }
                                    }
                                } else {
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    for (let i = 0; i < eventList.length; i++) {
        var eventId = eventList[i]._id;
        if (!eventsToRemove.has(eventId)) { // se già escluso skippo, per velocizzare
            var eventData = eventList[i];

            if (removeDuplicatesEventsUnwanted.includes(eventData.mType)) {
                eventsToRemove.add(eventData._id);
            } else {
                var basemStart = eventData.mStart, basemEnd = eventData.mEnd;
                for (let j = i + 1; j < eventList.length; j++) {
                    var tmpEventId = eventList[j]._id;
                    if (!eventsToRemove.has(tmpEventId)) { // se già escluso skippo, per velocizzare
                        var tmpEventData = eventList[j];
                        var mStart = tmpEventData.mStart, mEnd = tmpEventData.mEnd;

                        if (mStart <= basemEnd) { // solo se c'è una sovrapposizione potenziale
                            if (mStart >= (basemStart - 1000)) {
                                // console.log("to check", eventData, tmpEventData);
                                // clip da verificare se togliere
                                if (eventData.mType !== tmpEventData.mType || (eventData.playerId !== 0 && eventData.playerId === tmpEventData.playerId)) {
                                    // playerId è 0 nel caso in cui non c'è il giocatore
                                    // se evento diverso allora escludo oppure se trovo lo stesso giocatore
                                    eventsToRemove.add(tmpEventId);
                                    // $("#rowEventId" + tmpEventId).removeClass("eventToShow");
                                    // console.log("removed", tmpEventData);
                                }
                            }
                        } else {
                            // Uscire dal ciclo interno se gli eventi successivi non possono sovrapporsi
                            break;
                        }
                    }
                }
            }
        }
    }

    console.log("Events to remove: ", Array.from(eventsToRemove));

    // start sistemo i numeri del posizionale
    var eventsToRemoveArray = Array.from(eventsToRemove);
    adjustPositionalAmount(eventsToRemoveArray);
    // end sistemo i numeri del posizionale

    events.every(function () {
        var eventId = $(this.node()).attr("id").replace("rowEventId", "");
        if (eventsToRemove.has(parseInt(eventId))) {
            $(this.node()).removeClass("eventToShow");
        }
    });
}

var removeDuplicatesSmartPriority = ["TIF", "ASS", "PCF", "CRF", "DLL", "DLS", "DRF", "DRS"];
function removeDuplicatesSmart(events) {
    // questa funzione rimuove solo gli eventi che sono ESATTAMENTE uguali
    // quindi stesso mStart e mEnd ma evento diverso

    // mStart, mEnd
    // se mStart >= mStartBase e <= mEndBase allora controlla
    // se mType o playerId
    var eventsToRemove = new Set();
    var eventList = [];

    events.every(function () {
        var eventId = $(this.node()).attr("id").replace("rowEventId", "");
        eventList.push(eventMap.get("" + eventId));
    });

    // Ordina gli eventi per mStart
    eventList.sort(function (a, b) {
        var x = a.mStart;
        var y = b.mStart;
        return ((x < y) ? -1 : ((x > y) ? 1 : 0));
    });

    // se il giocatore è impostato (parametro playerId) togliere di base sempre ATT e DIF
    var idPlayer = new URL(document.URL).searchParams.get("idPlayer");
    if (idPlayer !== null && idPlayer !== "") {
        // console.log("STEP 1: remove duplicates for player " + idPlayer);
        // controllo se non ci sono filtri sugli eventi selezionati
        var eventParam = new URL(document.URL).searchParams.get("event");
        var atLeastOneFilter = $("#listAzioneRicerca").find("input:checked").length > 0
            || $("#liFiltriTacticalContent").find("input:checked").length > 0
            || ($("#listAzioneRicerca").find("button").length === 1 && $("#listAzioneRicerca").find("button").attr("id").includes("TIS"))
            || ($("#listAzioneRicerca").find("button").length === 1 && $("#listAzioneRicerca").find("button").attr("id").includes("RTS"))
            || eventParam !== null;

        if (!atLeastOneFilter) {
            // console.log("STEP 2: remove duplicates for player " + idPlayer + " (no filters)");
            // dagli eventi tolgo ATT e DIF
            for (let i = 0; i < eventList.length; i++) {
                var eventData = eventList[i];

                if (eventData.mType === "ATT" || eventData.mType === "DIF") {
                    eventsToRemove.add(eventData._id);
                }
            }
        }
    }

    for (let k = 0; k < removeDuplicatesSmartPriority.length; k++) {
        for (let i = 0; i < eventList.length; i++) {
            var eventId = eventList[i]._id;
            if (!eventsToRemove.has(eventId)) { // se già escluso skippo, per velocizzare
                var eventData = eventList[i];

                if (eventData.mType === removeDuplicatesSmartPriority[k]) {
                    for (let j = 0; j < eventList.length; j++) {
                        var tmpEventData = eventList[j];
                        if (!eventsToRemove.has(tmpEventData._id)) {
                            if (eventData._id !== tmpEventData._id) {
                                // escludo me stesso
                                var baseLength = [eventData.mStart, eventData.mEnd],
                                    tmpLength = [tmpEventData.mStart, tmpEventData.mEnd];
                                var result = calculateOverlap(baseLength, tmpLength);
//                            console.log(result, baseLength, tmpLength, eventData, tmpEventData);

                                if (result.overlapPercentage1 >= 80 || result.overlapPercentage2 >= 80) {
                                    // se uno dei 2 eventi è contenuto per l'80% nell'altro allora rimuovo
                                    eventsToRemove.add(tmpEventData._id);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

//    console.log(eventsToRemove);

    for (let i = 0; i < eventList.length; i++) {
        var eventId = eventList[i]._id;
        if (!eventsToRemove.has(eventId)) { // se già escluso skippo, per velocizzare
            var eventData = eventList[i];
            var basemStart = eventData.mStart, basemEnd = eventData.mEnd;
            for (let j = i + 1; j < eventList.length; j++) {
                var tmpEventId = eventList[j]._id;
                if (!eventsToRemove.has(tmpEventId)) { // se già escluso skippo, per velocizzare
                    var tmpEventData = eventList[j];
                    var mStart = tmpEventData.mStart, mEnd = tmpEventData.mEnd;

                    if (mStart <= basemEnd) { // solo se c'è una sovrapposizione potenziale
                        if (mStart >= (basemStart - 1000)) {
                            // console.log("to check", eventData, tmpEventData);
                            // clip da verificare se togliere
                            if (eventData.mType !== tmpEventData.mType || (eventData.playerId !== 0 && eventData.playerId !== tmpEventData.playerId)) {
                                // playerId è 0 nel caso in cui non c'è il giocatore
                                // se evento diverso allora escludo oppure se trovo lo stesso giocatore
                                eventsToRemove.add(tmpEventId);
                                // $("#rowEventId" + tmpEventId).removeClass("eventToShow");
                                // console.log("removed", tmpEventData);
                            }
                        }
                    } else {
                        // Uscire dal ciclo interno se gli eventi successivi non possono sovrapporsi
                        break;
                    }
                }
            }
        }
    }

    console.log("Events to remove: ", Array.from(eventsToRemove));

    // start sistemo i numeri del posizionale
    var eventsToRemoveArray = Array.from(eventsToRemove);
    adjustPositionalAmount(eventsToRemoveArray);
    // end sistemo i numeri del posizionale

    events.every(function () {
        var eventId = $(this.node()).attr("id").replace("rowEventId", "");
        if (eventsToRemove.has(parseInt(eventId))) {
            $(this.node()).removeClass("eventToShow");
        }
    });
}

function calculateOverlap(pair1, pair2) {
    const start1 = pair1[0];
    const end1 = pair1[1];
    const start2 = pair2[0];
    const end2 = pair2[1];

    const overlapStart = Math.max(start1, start2);
    const overlapEnd = Math.min(end1, end2);

    if (overlapStart >= overlapEnd) {
        return 0; // No overlap
    }

    const overlapLength = overlapEnd - overlapStart;
    const length1 = end1 - start1;
    const length2 = end2 - start2;

    const overlapPercentage1 = (overlapLength / length1) * 100;
    const overlapPercentage2 = (overlapLength / length2) * 100;

    return {
        overlapLength,
        overlapPercentage1,
        overlapPercentage2
    };
}

function adjustPositionalAmount(eventsToRemove) {
    var tmpZoneEventAmount = new Map(), tmpAngleEventAmount = new Map(), tmpZoneShotEventAmount = new Map();

    zoneEventAmount.forEach(function (value, key) {
        value = value.filter(function (el) {
            return !eventsToRemove.includes(el);
        });

        tmpZoneEventAmount.set(key, value);
    });
    zoneEventAmount = tmpZoneEventAmount;

    angleEventAmount.forEach(function (value, key) {
        value = value.filter(function (el) {
            return !eventsToRemove.includes(el);
        });

        tmpAngleEventAmount.set(key, value);
    });
    angleEventAmount = tmpAngleEventAmount;

    zoneShotEventAmount.forEach(function (value, key) {
        value = value.filter(function (el) {
            return !eventsToRemove.includes(el);
        });

        tmpZoneShotEventAmount.set(key, value);
    });
    zoneShotEventAmount = tmpZoneShotEventAmount;
    // end sistemo i numeri del posizionale
}

function notEmpty(variable) {
    return (typeof variable !== "undefined" && variable);
}

function captureVideoSnapshot(video) {
    let canvas = document.createElement('canvas');

    canvas.width = 1920;
    canvas.height = 1080;

    let ctx = canvas.getContext('2d');
    ctx.drawImage(video.children_[0], 0, 0, canvas.width, canvas.height);

    let image = canvas.toDataURL('image/jpeg');
    console.log(image);
}