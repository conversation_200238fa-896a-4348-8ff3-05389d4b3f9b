var lock = false;
var functionsDone;
function initSearchFilter() {
    // Seleziona l'elemento da monitorare
    var targetElement = document.getElementById('autocompleteTeamPlayer');
    if (targetElement !== null) {
        // carico storico delle ricerche (ultime 10)
        loadSearchHistory(false);

        // Crea una nuova istanza di MutationObserver
        var observer = new MutationObserver(function (mutationsList, observer) {
            for (var mutation of mutationsList) {
                if (mutation.type === 'attributes') {
                    if (!$("#autocompleteTeamPlayer").hasClass("uk-open")) {
                        $("#autoCompleteSeasons").addClass("uk-hidden");
                    }
                }
            }
        });
        // Opzioni per il MutationObserver
        var observerConfig = {attributes: true, childList: true, subtree: true};
        // Inizia a osservare l'elemento target con le opzioni specificate
        observer.observe(targetElement, observerConfig);

        //    $("#teamPlayerAuto").on("input", function() {
        //        if ($(this).val().length >= 3) {
        //            $("#searchSpinner").removeClass("uk-hidden");
        //        }
        //    });

        $.UIkit.autocomplete($('#autocompleteTeamPlayer'), {
            'minLength': 3,
            'delay': 750,
            'hoverClass': 'uk-hover',
            'source': function (release) {
                $("#searchSpinner").removeClass("uk-hidden");
                setTimeout(function () {
                    if (!lock) {
                        var searchHistory = localStorage['searchHistory'];
                        if (typeof searchHistory === "undefined") {
                            searchHistory = [];
                        } else {
                            searchHistory = searchHistory.split(",");
                        }
                        if (searchHistory.indexOf($("#teamPlayerAuto").val()) === -1) {
                            searchHistory.unshift($("#teamPlayerAuto").val()); // aggiunge all'inizio dell'array
                            if (searchHistory.length > 10) { // tengo solo le ultime 10
                                searchHistory = searchHistory.splice(0, 10);
                            }
                            localStorage['searchHistory'] = searchHistory;
                        }

                        var arrayTeam = [], arrayPlayer = [], arrayCompetition = [], arrayCountry = [];
                        var array = [];
                        lock = true;
                        isTeam = false;
                        isPlayer = false;
                        isCompetition = false;
                        isCountry = false;
                        functionsDone = 0;
                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/searchTeam.htm",
                            dataType: "json",
                            data: "pattern=" + $("#teamPlayerAuto").val(),
                            success: function (data) {
                                var strf = $("#teamPlayerAuto").val();
                                var re = new RegExp(strf, "ig");
                                $.each(data, function (arrKey, arrValue) {
                                    isTeam = true;
                                    var str = arrValue.name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                    if (strf.includes(" ")) {
                                        str = arrValue.name;
                                        strf.split(" ").forEach(function (word) {
                                            str = str.replace(word, "<b>" + word + "</b>");
                                        });
                                        str = str.toUpperCase();
                                    }
                                    var obj = {id: arrValue.id, value: arrValue.name, seasons: arrValue.seasonList, lastSeason: arrValue.seasonName, name: str, team: true, player: false, competition: false, country: false, countryName: arrValue.countryName};
                                    arrayTeam.push(obj);
                                });
                                functionsDone++;
                            }
                        });

                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/searchPlayer.htm",
                            dataType: "json",
                            data: "pattern=" + $("#teamPlayerAuto").val(),
                            success: function (data) {
                                var strf = $("#teamPlayerAuto").val();
                                var re = new RegExp(strf, "ig");
                                $.each(data, function (arrKey, arrValue) {
                                    isPlayer = true;
                                    var str = arrValue.last_name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                    var knownName = "";
                                    if (arrValue.known_name.indexOf(arrValue.last_name) === -1) {
                                        knownName = " (" + arrValue.known_name.replace(re, "<b>" + strf + "</b>").toUpperCase() + ")";
                                    }
                                    if (strf.includes(" ")) {
                                        strf.split(" ").forEach(function (word) {
                                            word = word.toUpperCase();
                                            arrValue.last_name = arrValue.last_name.replace(word, "<b>" + word + "</b>");
                                            arrValue.first_name = arrValue.first_name.replace(word, "<b>" + word + "</b>");
                                            knownName = knownName.replace(word, "<b>" + word + "</b>");
                                        });
                                    } else {
                                        arrValue.last_name = arrValue.last_name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                        arrValue.first_name = arrValue.first_name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                    }
                                    var bornDate = arrValue.bornDate.split("-")[0];
                                    var obj = {id: arrValue.id, value: arrValue.known_name, seasons: arrValue.seasonList, lastSeason: arrValue.seasonName, name: str, namel: arrValue.last_name, namef: arrValue.first_name, namek: knownName, borny: bornDate, team: false, player: true, competition: false, country: false};
                                    arrayPlayer.push(obj);
                                });
                                functionsDone++;
                            }
                        });

                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/searchCompetition.htm",
                            dataType: "json",
                            data: "pattern=" + $("#teamPlayerAuto").val(),
                            success: function (data) {
                                var strf = $("#teamPlayerAuto").val();
                                var re = new RegExp(strf, "ig");
                                $.each(data, function (arrKey, arrValue) {
                                    isCompetition = true;
                                    var str = arrValue.name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                    if (strf.includes(" ")) {
                                        str = arrValue.name;
                                        strf.split(" ").forEach(function (word) {
                                            str = str.replace(word, "<b>" + word + "</b>");
                                        });
                                        str = str.toUpperCase();
                                    }
                                    var obj = {id: arrValue.id, value: arrValue.name, seasons: arrValue.seasonList, lastSeason: arrValue.seasonName, name: str, team: false, player: false, competition: true, country: false};
                                    arrayCompetition.push(obj);
                                });
                                functionsDone++;
                            }
                        });

                        $.ajax({
                            type: "GET",
                            url: "/sicstv/user/searchCountry.htm",
                            dataType: "json",
                            data: "pattern=" + $("#teamPlayerAuto").val(),
                            success: function (data) {
                                var strf = $("#teamPlayerAuto").val();
                                var re = new RegExp(strf, "ig");
                                $.each(data, function (arrKey, arrValue) {
                                    isCountry = true;
                                    var str = arrValue.name.replace(re, "<b>" + strf + "</b>").toUpperCase();
                                    if (strf.includes(" ")) {
                                        str = arrValue.name;
                                        strf.split(" ").forEach(function (word) {
                                            str = str.replace(word, "<b>" + word + "</b>");
                                        });
                                        str = str.toUpperCase();
                                    }
                                    var obj = {id: arrValue.id, value: arrValue.name, seasons: arrValue.seasonList, lastSeason: arrValue.seasonName, name: str, team: false, player: false, competition: false, country: true};
                                    arrayCountry.push(obj);
                                });
                                functionsDone++;
                            }
                        });

                        waitfor(isSearchDone, 4, 250, 0, 'search', function () {
                            loadSearchHistory(true);
                            array = array.concat(arrayTeam);
                            array = array.concat(arrayPlayer);
                            array = array.concat(arrayCompetition);
                            array = array.concat(arrayCountry);
                            release(array);
                            lock = false;
                            initializeHover();

                            $("#searchSpinner").addClass("uk-hidden");
                        });
                    }
                }, 100);
            }
        });
    }
}

function isSearchDone() {
    return functionsDone;
}

function initializeHover() {
    $('li[data-value]').hover(function () {
        var position = $(this).position();
        var leftOffset = position.left + $(this).outerWidth() + 70;

        var seasons = $(this).attr("data-seasons");
        var onclick = $(this).attr("onclick");
        if (onclick.includes("; ")) {
            var onclickSplitted = onclick.split("; ");
            onclick = onclickSplitted[2];
        }
        if (typeof seasons !== 'undefined') {
            var splitted = seasons.split(",");
            var content = "<ul class='uk-nav uk-nav-autocomplete uk-autocomplete-results margin-small-left'>";
            content += "<li class''><i class='uk-icon-shield'></i>&nbsp;<b>" + $("#seasonTitle").val() + "</b><hr></li>";

            splitted.forEach(function (key) {
                content += '<li onclick="jsShowBlockUI(); setUserSeasonId(\'' + key + '\'); ' + onclick + '" class>';
                content += "<a>" + key + "</a>";
                content += "</li>";
            });

            content += "</ul>";

            $('#autoCompleteSeasons')
                    .html(content)
                    .css({
                        display: 'block',
                        position: 'absolute',
                        left: leftOffset + 'px',
                        top: position.top + 'px'
                    });
            $("#autoCompleteSeasons").removeClass("uk-hidden");

            // bind eventi
            $("#autoCompleteSeasons > ul > li > a").hover(
                    function () {
                        $(this).addClass("selected");
                        $("#autoCompleteSeasons > ul > li:nth-child(2) > a").removeClass("selected");
                    }, function () {
                $("#autoCompleteSeasons > ul > li > a").removeClass("selected");
            }
            );
            $("#autoCompleteSeasons > ul > li:nth-child(2) > a").addClass("selected");
        }
    });
}

function loadSearchHistory(hide) {
    $('#search-history option').remove();

    var sessionSearchHistory = localStorage['searchHistory'];
    if (typeof sessionSearchHistory !== "undefined" && sessionSearchHistory) {
        sessionSearchHistory = sessionSearchHistory.split(",");
        sessionSearchHistory.forEach(function (element) {
            $("#search-history").append($('<option>', {
                value: element
            }));
        });
    }

    if (typeof hide !== "undefined" && hide) {
        $(".search-history").attr("id", "hidden-search-history");
    }
}

function showSearchHistory() {
    $(".search-history").attr("id", "search-history");
}

function doPlayerTeamSearch(id, div) {
    $('#' + div).val(id);
    filterData();
}

function openTeamPage(id, personal) {
    window.location.href = "/sicstv/user/team.htm?personal=" + personal + "&formCompetitionId=-1&formTeamId=" + id;
}

function openPlayerPage(id, personal) {
    window.location.href = "/sicstv/user/player.htm?personal=" + personal + "&playerId=" + id;
}

function openCompetitionPage(id, personal) {
    window.location.href = "/sicstv/user/competition.htm?personal=" + personal + "&formCompetitionId=" + id;
}

function openCountryPage(id, personal) {
    window.location.href = "/sicstv/user/country.htm?personal=" + personal + "&countryId=" + id;
}

function resetFilterForm() {
    $('#teamSelAutocomplete').val("");
    $('#playerSelAutocomplete').val("");
    $('#inputDateFromMulti').val("");
    $('#inputDateToMulti').val("");
    $("#selectModuleMulti option[value='']").prop('selected', true);
    $("#spanSelectModule").html("");
//	$('#moduleSel').html("");
//	$('#moduleAuto').val("");
}

function showTabellino(id) {

    var modal = UIkit.modal("#divShowTabellino");
    modal.show();
    jsLoadingAlpha("#divOverflow");
    $.ajax({
        type: "GET",
        url: "/sicstv/user/generateTabellino.htm",
        data: "idGame=" + id,
        cache: false,
        success: function (msg) {
            jsLoadingOff("#divOverflow");
            $("#divOverflow").html(msg);
            modal.show();
        }
    });
}

function showTabellinoCalendar(id) {

    var modal = UIkit.modal("#divShowTabellinoCalendar");
    modal.show();
    jsLoadingAlpha("#divOverflowCalendar");
    $.ajax({
        type: "GET",
        url: "/sicstv/user/generateTabellino.htm",
        data: "idGame=" + id,
        cache: false,
        success: function (msg) {
            jsLoadingOff("#divOverflowCalendar");
            $("#divOverflowCalendar").html(msg);
            modal.show();
        }
    });
}

function jsIncludeTabellino(id, element) {
    $.ajax({
        type: "GET",
        url: "/sicstv/user/generateTabellino.htm",
        data: "idGame=" + id,
        cache: false,
        success: function (msg) {
            $("#" + element).html(msg);
        }
    });
}