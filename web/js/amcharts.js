function createOverviewChart(data) {
    am5.ready(function () {

        // Create root element
        // https://www.amcharts.com/docs/v5/getting-started/#Root_element
        var root = am5.Root.new("chartdiv");

        // Set themes
        // https://www.amcharts.com/docs/v5/concepts/themes/
        root.setThemes([
            am5themes_Animated.new(root)
        ]);

        // Create chart
        // https://www.amcharts.com/docs/v5/charts/radar-chart/
        var chart = root.container.children.push(am5radar.RadarChart.new(root, {
            panX: false,
            panY: false,
            innerRadius: am5.percent(15) // Set inner radius to 0 or a negative value for more space
        }));

        // Create axes and their renderers
        // https://www.amcharts.com/docs/v5/charts/radar-chart/#Adding_axes
        var xRenderer = am5radar.AxisRendererCircular.new(root, {
            minGridDistance: 10,
            // Adjust these properties to fill space
            cellStartLocation: 0, // Start at the beginning of the cell
            cellEndLocation: 1    // End at the end of the cell
        });
        xRenderer.labels.template.setAll({
            radius: 10,
            textType: 'adjusted',
            fontSize: 12
        });
        var xAxis = chart.xAxes.push(am5xy.CategoryAxis.new(root, {
            maxDeviation: 0,
            categoryField: "category",
            renderer: xRenderer,
            tooltip: am5.Tooltip.new(root, {})
        }));
        var yAxis = chart.yAxes.push(am5xy.ValueAxis.new(root, {
            min: 0,
            max: 100,
            strictMinMax: true,
            renderer: am5radar.AxisRendererRadial.new(root, {})
        }));

        // Disable labels for both axes
        xAxis.get("renderer").labels.template.set("disabled", true); // Disable category axis labels
        yAxis.get("renderer").labels.template.set("disabled", true); // Disable value axis labels

        // Create series
        // https://www.amcharts.com/docs/v5/charts/radar-chart/#Adding_series
        var series = chart.series.push(am5radar.RadarColumnSeries.new(root, {
            stacked: true,
            name: "Serie",
            xAxis: xAxis,
            yAxis: yAxis,
            valueYField: "value",
            categoryXField: "category"
        }));
        series.columns.template.setAll({
            tooltipHTML: "{tooltip}"
        });
        // Use adapter to set fill color based on data's color property
        series.columns.template.adapters.add("fill", (fill, target) => {
            const dataItem = target.dataItem;
            return am5.color(dataItem.dataContext.color); // Use the color from data context
        });
        // set stroke color
        series.columns.template.adapters.add("stroke", (stroke, target) => {
            const dataItem = target.dataItem;
            return am5.color(dataItem.dataContext.color); // Optionally use same color for stroke
        });
        series.data.setAll(data);
        series.appear(1500);

        xAxis.data.setAll(data);

        // Animate chart
        // https://www.amcharts.com/docs/v5/concepts/animations/#Initial_animation
        chart.appear(1000, 100);
    });
}