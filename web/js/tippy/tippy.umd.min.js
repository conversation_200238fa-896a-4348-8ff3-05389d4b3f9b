!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@popperjs/core")):"function"==typeof define&&define.amd?define(["@popperjs/core"],t):(e=e||self).tippy=t(e.<PERSON><PERSON>)}(this,(function(e){"use strict";var t={passive:!0,capture:!0};function n(e,t,n){if(Array.isArray(e)){var r=e[t];return null==r?Array.isArray(n)?n[t]:n:r}return e}function r(e,t){var n={}.toString.call(e);return 0===n.indexOf("[object")&&n.indexOf(t+"]")>-1}function i(e,t){return"function"==typeof e?e.apply(void 0,t):e}function o(e,t){return 0===t?e:function(r){clearTimeout(n),n=setTimeout((function(){e(r)}),t)};var n}function a(e,t){var n=Object.assign({},e);return t.forEach((function(e){delete n[e]})),n}function s(e){return[].concat(e)}function u(e,t){-1===e.indexOf(t)&&e.push(t)}function c(e){return e.split("-")[0]}function p(e){return[].slice.call(e)}function f(){return document.createElement("div")}function l(e){return["Element","Fragment"].some((function(t){return r(e,t)}))}function d(e){return r(e,"MouseEvent")}function v(e){return!(!e||!e._tippy||e._tippy.reference!==e)}function m(e){return l(e)?[e]:function(e){return r(e,"NodeList")}(e)?p(e):Array.isArray(e)?e:p(document.querySelectorAll(e))}function g(e,t){e.forEach((function(e){e&&(e.style.transitionDuration=t+"ms")}))}function h(e,t){e.forEach((function(e){e&&e.setAttribute("data-state",t)}))}function b(e){var t,n=s(e)[0];return(null==n||null==(t=n.ownerDocument)?void 0:t.body)?n.ownerDocument:document}function y(e,t,n){var r=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(t){e[r](t,n)}))}var w={isTouch:!1},E=0;function O(){w.isTouch||(w.isTouch=!0,window.performance&&document.addEventListener("mousemove",T))}function T(){var e=performance.now();e-E<20&&(w.isTouch=!1,document.removeEventListener("mousemove",T)),E=e}function C(){var e=document.activeElement;if(v(e)){var t=e._tippy;e.blur&&!t.state.isVisible&&e.blur()}}var x="undefined"!=typeof window&&"undefined"!=typeof document?navigator.userAgent:"",A=/MSIE |Trident\//.test(x),L=Object.assign({appendTo:function(){return document.body},aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),D=Object.keys(L);function k(e){var t=(e.plugins||[]).reduce((function(t,n){var r=n.name,i=n.defaultValue;return r&&(t[r]=void 0!==e[r]?e[r]:i),t}),{});return Object.assign({},e,{},t)}function R(e,t){var n=Object.assign({},t,{content:i(t.content,[e])},t.ignoreAttributes?{}:function(e,t){return(t?Object.keys(k(Object.assign({},L,{plugins:t}))):D).reduce((function(t,n){var r=(e.getAttribute("data-tippy-"+n)||"").trim();if(!r)return t;if("content"===n)t[n]=r;else try{t[n]=JSON.parse(r)}catch(e){t[n]=r}return t}),{})}(e,t.plugins));return n.aria=Object.assign({},L.aria,{},n.aria),n.aria={expanded:"auto"===n.aria.expanded?t.interactive:n.aria.expanded,content:"auto"===n.aria.content?t.interactive?null:"describedby":n.aria.content},n}function j(e,t){e.innerHTML=t}function M(e){var t=f();return!0===e?t.className="tippy-arrow":(t.className="tippy-svg-arrow",l(e)?t.appendChild(e):j(t,e)),t}function P(e,t){l(t.content)?(j(e,""),e.appendChild(t.content)):"function"!=typeof t.content&&(t.allowHTML?j(e,t.content):e.textContent=t.content)}function V(e){var t=e.firstElementChild,n=p(t.children);return{box:t,content:n.find((function(e){return e.classList.contains("tippy-content")})),arrow:n.find((function(e){return e.classList.contains("tippy-arrow")||e.classList.contains("tippy-svg-arrow")})),backdrop:n.find((function(e){return e.classList.contains("tippy-backdrop")}))}}function I(e){var t=f(),n=f();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var r=f();function i(n,r){var i=V(t),o=i.box,a=i.content,s=i.arrow;r.theme?o.setAttribute("data-theme",r.theme):o.removeAttribute("data-theme"),"string"==typeof r.animation?o.setAttribute("data-animation",r.animation):o.removeAttribute("data-animation"),r.inertia?o.setAttribute("data-inertia",""):o.removeAttribute("data-inertia"),o.style.maxWidth="number"==typeof r.maxWidth?r.maxWidth+"px":r.maxWidth,r.role?o.setAttribute("role",r.role):o.removeAttribute("role"),n.content===r.content&&n.allowHTML===r.allowHTML||P(a,e.props),r.arrow?s?n.arrow!==r.arrow&&(o.removeChild(s),o.appendChild(M(r.arrow))):o.appendChild(M(r.arrow)):s&&o.removeChild(s)}return r.className="tippy-content",r.setAttribute("data-state","hidden"),P(r,e.props),t.appendChild(n),n.appendChild(r),i(e.props,e.props),{popper:t,onUpdate:i}}I.$$tippy=!0;var S=1,B=[],H=[];function N(r,a){var l,v,m,E,O,T,C,x,D,j=R(r,Object.assign({},L,{},k((l=a,Object.keys(l).reduce((function(e,t){return void 0!==l[t]&&(e[t]=l[t]),e}),{}))))),M=!1,P=!1,I=!1,N=!1,U=[],_=o(be,j.interactiveDebounce),F=S++,W=(D=j.plugins).filter((function(e,t){return D.indexOf(e)===t})),X={id:F,reference:r,popper:f(),popperInstance:null,props:j,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:W,clearDelayTimeouts:function(){clearTimeout(v),clearTimeout(m),cancelAnimationFrame(E)},setProps:function(e){if(X.state.isDestroyed)return;ie("onBeforeUpdate",[X,e]),ge();var t=X.props,n=R(r,Object.assign({},X.props,{},e,{ignoreAttributes:!0}));X.props=n,me(),t.interactiveDebounce!==n.interactiveDebounce&&(se(),_=o(be,n.interactiveDebounce));t.triggerTarget&&!n.triggerTarget?s(t.triggerTarget).forEach((function(e){e.removeAttribute("aria-expanded")})):n.triggerTarget&&r.removeAttribute("aria-expanded");ae(),re(),q&&q(t,n);X.popperInstance&&(Oe(),Ce().forEach((function(e){requestAnimationFrame(e._tippy.popperInstance.forceUpdate)})));ie("onAfterUpdate",[X,e])},setContent:function(e){X.setProps({content:e})},show:function(){var e=X.state.isVisible,t=X.state.isDestroyed,r=!X.state.isEnabled,o=w.isTouch&&!X.props.touch,a=n(X.props.duration,0,L.duration);if(e||t||r||o)return;if(Z().hasAttribute("disabled"))return;if(ie("onShow",[X],!1),!1===X.props.onShow(X))return;X.state.isVisible=!0,Q()&&($.style.visibility="visible");re(),fe(),X.state.isMounted||($.style.transition="none");if(Q()){var s=te(),c=s.box,p=s.content;g([c,p],0)}C=function(){var e;if(X.state.isVisible&&!N){if(N=!0,$.offsetHeight,$.style.transition=X.props.moveTransition,Q()&&X.props.animation){var t=te(),n=t.box,r=t.content;g([n,r],a),h([n,r],"visible")}oe(),ae(),u(H,X),null==(e=X.popperInstance)||e.forceUpdate(),X.state.isMounted=!0,ie("onMount",[X]),X.props.animation&&Q()&&function(e,t){de(e,t)}(a,(function(){X.state.isShown=!0,ie("onShown",[X])}))}},function(){var e,t=X.props.appendTo,n=Z();e=X.props.interactive&&t===L.appendTo||"parent"===t?n.parentNode:i(t,[n]);e.contains($)||e.appendChild($);Oe()}()},hide:function(){var e=!X.state.isVisible,t=X.state.isDestroyed,r=!X.state.isEnabled,i=n(X.props.duration,1,L.duration);if(e||t||r)return;if(ie("onHide",[X],!1),!1===X.props.onHide(X))return;X.state.isVisible=!1,X.state.isShown=!1,N=!1,M=!1,Q()&&($.style.visibility="hidden");if(se(),le(),re(),Q()){var o=te(),a=o.box,s=o.content;X.props.animation&&(g([a,s],i),h([a,s],"hidden"))}oe(),ae(),X.props.animation?Q()&&function(e,t){de(e,(function(){!X.state.isVisible&&$.parentNode&&$.parentNode.contains($)&&t()}))}(i,X.unmount):X.unmount()},hideWithInteractivity:function(e){ee().addEventListener("mousemove",_),u(B,_),_(e)},enable:function(){X.state.isEnabled=!0},disable:function(){X.hide(),X.state.isEnabled=!1},unmount:function(){X.state.isVisible&&X.hide();if(!X.state.isMounted)return;Te(),Ce().forEach((function(e){e._tippy.unmount()})),$.parentNode&&$.parentNode.removeChild($);H=H.filter((function(e){return e!==X})),X.state.isMounted=!1,ie("onHidden",[X])},destroy:function(){if(X.state.isDestroyed)return;X.clearDelayTimeouts(),X.unmount(),ge(),delete r._tippy,X.state.isDestroyed=!0,ie("onDestroy",[X])}};if(!j.render)return X;var Y=j.render(X),$=Y.popper,q=Y.onUpdate;$.setAttribute("data-tippy-root",""),$.id="tippy-"+X.id,X.popper=$,r._tippy=X,$._tippy=X;var z=W.map((function(e){return e.fn(X)})),J=r.hasAttribute("aria-expanded");return me(),ae(),re(),ie("onCreate",[X]),j.showOnCreate&&xe(),$.addEventListener("mouseenter",(function(){X.props.interactive&&X.state.isVisible&&X.clearDelayTimeouts()})),$.addEventListener("mouseleave",(function(e){X.props.interactive&&X.props.trigger.indexOf("mouseenter")>=0&&(ee().addEventListener("mousemove",_),_(e))})),X;function G(){var e=X.props.touch;return Array.isArray(e)?e:[e,0]}function K(){return"hold"===G()[0]}function Q(){var e;return!!(null==(e=X.props.render)?void 0:e.$$tippy)}function Z(){return x||r}function ee(){var e=Z().parentNode;return e?b(e):document}function te(){return V($)}function ne(e){return X.state.isMounted&&!X.state.isVisible||w.isTouch||O&&"focus"===O.type?0:n(X.props.delay,e?0:1,L.delay)}function re(){$.style.pointerEvents=X.props.interactive&&X.state.isVisible?"":"none",$.style.zIndex=""+X.props.zIndex}function ie(e,t,n){var r;(void 0===n&&(n=!0),z.forEach((function(n){n[e]&&n[e].apply(void 0,t)})),n)&&(r=X.props)[e].apply(r,t)}function oe(){var e=X.props.aria;if(e.content){var t="aria-"+e.content,n=$.id;s(X.props.triggerTarget||r).forEach((function(e){var r=e.getAttribute(t);if(X.state.isVisible)e.setAttribute(t,r?r+" "+n:n);else{var i=r&&r.replace(n,"").trim();i?e.setAttribute(t,i):e.removeAttribute(t)}}))}}function ae(){!J&&X.props.aria.expanded&&s(X.props.triggerTarget||r).forEach((function(e){X.props.interactive?e.setAttribute("aria-expanded",X.state.isVisible&&e===Z()?"true":"false"):e.removeAttribute("aria-expanded")}))}function se(){ee().removeEventListener("mousemove",_),B=B.filter((function(e){return e!==_}))}function ue(e){if(!(w.isTouch&&(I||"mousedown"===e.type)||X.props.interactive&&$.contains(e.target))){if(Z().contains(e.target)){if(w.isTouch)return;if(X.state.isVisible&&X.props.trigger.indexOf("click")>=0)return}else ie("onClickOutside",[X,e]);!0===X.props.hideOnClick&&(X.clearDelayTimeouts(),X.hide(),P=!0,setTimeout((function(){P=!1})),X.state.isMounted||le())}}function ce(){I=!0}function pe(){I=!1}function fe(){var e=ee();e.addEventListener("mousedown",ue,!0),e.addEventListener("touchend",ue,t),e.addEventListener("touchstart",pe,t),e.addEventListener("touchmove",ce,t)}function le(){var e=ee();e.removeEventListener("mousedown",ue,!0),e.removeEventListener("touchend",ue,t),e.removeEventListener("touchstart",pe,t),e.removeEventListener("touchmove",ce,t)}function de(e,t){var n=te().box;function r(e){e.target===n&&(y(n,"remove",r),t())}if(0===e)return t();y(n,"remove",T),y(n,"add",r),T=r}function ve(e,t,n){void 0===n&&(n=!1),s(X.props.triggerTarget||r).forEach((function(r){r.addEventListener(e,t,n),U.push({node:r,eventType:e,handler:t,options:n})}))}function me(){var e;K()&&(ve("touchstart",he,{passive:!0}),ve("touchend",ye,{passive:!0})),(e=X.props.trigger,e.split(/\s+/).filter(Boolean)).forEach((function(e){if("manual"!==e)switch(ve(e,he),e){case"mouseenter":ve("mouseleave",ye);break;case"focus":ve(A?"focusout":"blur",we);break;case"focusin":ve("focusout",we)}}))}function ge(){U.forEach((function(e){var t=e.node,n=e.eventType,r=e.handler,i=e.options;t.removeEventListener(n,r,i)})),U=[]}function he(e){var t,n=!1;if(X.state.isEnabled&&!Ee(e)&&!P){var r="focus"===(null==(t=O)?void 0:t.type);O=e,x=e.currentTarget,ae(),!X.state.isVisible&&d(e)&&B.forEach((function(t){return t(e)})),"click"===e.type&&(X.props.trigger.indexOf("mouseenter")<0||M)&&!1!==X.props.hideOnClick&&X.state.isVisible?n=!0:xe(e),"click"===e.type&&(M=!n),n&&!r&&Ae(e)}}function be(e){var t=e.target,n=Z().contains(t)||$.contains(t);"mousemove"===e.type&&n||function(e,t){var n=t.clientX,r=t.clientY;return e.every((function(e){var t=e.popperRect,i=e.popperState,o=e.props.interactiveBorder,a=c(i.placement),s=i.modifiersData.offset;if(!s)return!0;var u="bottom"===a?s.top.y:0,p="top"===a?s.bottom.y:0,f="right"===a?s.left.x:0,l="left"===a?s.right.x:0,d=t.top-r+u>o,v=r-t.bottom-p>o,m=t.left-n+f>o,g=n-t.right-l>o;return d||v||m||g}))}(Ce().concat($).map((function(e){var t,n=null==(t=e._tippy.popperInstance)?void 0:t.state;return n?{popperRect:e.getBoundingClientRect(),popperState:n,props:j}:null})).filter(Boolean),e)&&(se(),Ae(e))}function ye(e){Ee(e)||X.props.trigger.indexOf("click")>=0&&M||(X.props.interactive?X.hideWithInteractivity(e):Ae(e))}function we(e){X.props.trigger.indexOf("focusin")<0&&e.target!==Z()||X.props.interactive&&e.relatedTarget&&$.contains(e.relatedTarget)||Ae(e)}function Ee(e){return!!w.isTouch&&K()!==e.type.indexOf("touch")>=0}function Oe(){Te();var t=X.props,n=t.popperOptions,i=t.placement,o=t.offset,a=t.getReferenceClientRect,s=t.moveTransition,u=Q()?V($).arrow:null,c=a?{getBoundingClientRect:a,contextElement:a.contextElement||Z()}:r,p=[{name:"offset",options:{offset:o}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!s}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(e){var t=e.state;if(Q()){var n=te().box;["placement","reference-hidden","escaped"].forEach((function(e){"placement"===e?n.setAttribute("data-placement",t.placement):t.attributes.popper["data-popper-"+e]?n.setAttribute("data-"+e,""):n.removeAttribute("data-"+e)})),t.attributes.popper={}}}}];Q()&&u&&p.push({name:"arrow",options:{element:u,padding:3}}),p.push.apply(p,(null==n?void 0:n.modifiers)||[]),X.popperInstance=e.createPopper(c,$,Object.assign({},n,{placement:i,onFirstUpdate:C,modifiers:p}))}function Te(){X.popperInstance&&(X.popperInstance.destroy(),X.popperInstance=null)}function Ce(){return p($.querySelectorAll("[data-tippy-root]"))}function xe(e){X.clearDelayTimeouts(),e&&ie("onTrigger",[X,e]),fe();var t=ne(!0),n=G(),r=n[0],i=n[1];w.isTouch&&"hold"===r&&i&&(t=i),t?v=setTimeout((function(){X.show()}),t):X.show()}function Ae(e){if(X.clearDelayTimeouts(),ie("onUntrigger",[X,e]),X.state.isVisible){if(!(X.props.trigger.indexOf("mouseenter")>=0&&X.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(e.type)>=0&&M)){var t=ne(!1);t?m=setTimeout((function(){X.state.isVisible&&X.hide()}),t):E=requestAnimationFrame((function(){X.hide()}))}}else le()}}function U(e,n){void 0===n&&(n={});var r=L.plugins.concat(n.plugins||[]);document.addEventListener("touchstart",O,t),window.addEventListener("blur",C);var i=Object.assign({},n,{plugins:r}),o=m(e).reduce((function(e,t){var n=t&&N(t,i);return n&&e.push(n),e}),[]);return l(e)?o[0]:o}U.defaultProps=L,U.setDefaultProps=function(e){Object.keys(e).forEach((function(t){L[t]=e[t]}))},U.currentInput=w;var _=Object.assign({},e.applyStyles,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}}),F={mouseover:"mouseenter",focusin:"focus",click:"click"};var W={name:"animateFill",defaultValue:!1,fn:function(e){var t;if(!(null==(t=e.props.render)?void 0:t.$$tippy))return{};var n=V(e.popper),r=n.box,i=n.content,o=e.props.animateFill?function(){var e=f();return e.className="tippy-backdrop",h([e],"hidden"),e}():null;return{onCreate:function(){o&&(r.insertBefore(o,r.firstElementChild),r.setAttribute("data-animatefill",""),r.style.overflow="hidden",e.setProps({arrow:!1,animation:"shift-away"}))},onMount:function(){if(o){var e=r.style.transitionDuration,t=Number(e.replace("ms",""));i.style.transitionDelay=Math.round(t/10)+"ms",o.style.transitionDuration=e,h([o],"visible")}},onShow:function(){o&&(o.style.transitionDuration="0ms")},onHide:function(){o&&h([o],"hidden")}}}};var X={clientX:0,clientY:0},Y=[];function $(e){var t=e.clientX,n=e.clientY;X={clientX:t,clientY:n}}var q={name:"followCursor",defaultValue:!1,fn:function(e){var t=e.reference,n=b(e.props.triggerTarget||t),r=!1,i=!1,o=!0,a=e.props;function s(){return"initial"===e.props.followCursor&&e.state.isVisible}function u(){n.addEventListener("mousemove",f)}function c(){n.removeEventListener("mousemove",f)}function p(){r=!0,e.setProps({getReferenceClientRect:null}),r=!1}function f(n){var r=!n.target||t.contains(n.target),i=e.props.followCursor,o=n.clientX,a=n.clientY,s=t.getBoundingClientRect(),u=o-s.left,c=a-s.top;!r&&e.props.interactive||e.setProps({getReferenceClientRect:function(){var e=t.getBoundingClientRect(),n=o,r=a;"initial"===i&&(n=e.left+u,r=e.top+c);var s="horizontal"===i?e.top:r,p="vertical"===i?e.right:n,f="horizontal"===i?e.bottom:r,l="vertical"===i?e.left:n;return{width:p-l,height:f-s,top:s,right:p,bottom:f,left:l}}})}function l(){e.props.followCursor&&(Y.push({instance:e,doc:n}),function(e){e.addEventListener("mousemove",$)}(n))}function v(){0===(Y=Y.filter((function(t){return t.instance!==e}))).filter((function(e){return e.doc===n})).length&&function(e){e.removeEventListener("mousemove",$)}(n)}return{onCreate:l,onDestroy:v,onBeforeUpdate:function(){a=e.props},onAfterUpdate:function(t,n){var o=n.followCursor;r||void 0!==o&&a.followCursor!==o&&(v(),o?(l(),!e.state.isMounted||i||s()||u()):(c(),p()))},onMount:function(){e.props.followCursor&&!i&&(o&&(f(X),o=!1),s()||u())},onTrigger:function(e,t){d(t)&&(X={clientX:t.clientX,clientY:t.clientY}),i="focus"===t.type},onHidden:function(){e.props.followCursor&&(p(),c(),o=!0)}}}};var z={name:"inlinePositioning",defaultValue:!1,fn:function(e){var t,n=e.reference;var r=-1,i=!1,o={name:"tippyInlinePositioning",enabled:!0,phase:"afterWrite",fn:function(i){var o=i.state;e.props.inlinePositioning&&(t!==o.placement&&e.setProps({getReferenceClientRect:function(){return function(e){return function(e,t,n,r){if(n.length<2||null===e)return t;if(2===n.length&&r>=0&&n[0].left>n[1].right)return n[r]||t;switch(e){case"top":case"bottom":var i=n[0],o=n[n.length-1],a="top"===e,s=i.top,u=o.bottom,c=a?i.left:o.left,p=a?i.right:o.right;return{top:s,bottom:u,left:c,right:p,width:p-c,height:u-s};case"left":case"right":var f=Math.min.apply(Math,n.map((function(e){return e.left}))),l=Math.max.apply(Math,n.map((function(e){return e.right}))),d=n.filter((function(t){return"left"===e?t.left===f:t.right===l})),v=d[0].top,m=d[d.length-1].bottom;return{top:v,bottom:m,left:f,right:l,width:l-f,height:m-v};default:return t}}(c(e),n.getBoundingClientRect(),p(n.getClientRects()),r)}(o.placement)}}),t=o.placement)}};function a(){var t;i||(t=function(e,t){var n;return{popperOptions:Object.assign({},e.popperOptions,{modifiers:[].concat(((null==(n=e.popperOptions)?void 0:n.modifiers)||[]).filter((function(e){return e.name!==t.name})),[t])})}}(e.props,o),i=!0,e.setProps(t),i=!1)}return{onCreate:a,onAfterUpdate:a,onTrigger:function(t,n){if(d(n)){var i=p(e.reference.getClientRects()),o=i.find((function(e){return e.left-2<=n.clientX&&e.right+2>=n.clientX&&e.top-2<=n.clientY&&e.bottom+2>=n.clientY}));r=i.indexOf(o)}},onUntrigger:function(){r=-1}}}};var J={name:"sticky",defaultValue:!1,fn:function(e){var t=e.reference,n=e.popper;function r(t){return!0===e.props.sticky||e.props.sticky===t}var i=null,o=null;function a(){var s=r("reference")?(e.popperInstance?e.popperInstance.state.elements.reference:t).getBoundingClientRect():null,u=r("popper")?n.getBoundingClientRect():null;(s&&G(i,s)||u&&G(o,u))&&e.popperInstance&&e.popperInstance.update(),i=s,o=u,e.state.isMounted&&requestAnimationFrame(a)}return{onMount:function(){e.props.sticky&&a()}}}};function G(e,t){return!e||!t||(e.top!==t.top||e.right!==t.right||e.bottom!==t.bottom||e.left!==t.left)}return U.setDefaultProps({plugins:[W,q,z,J],render:I}),U.createSingleton=function(e,t){var n;void 0===t&&(t={});var r,i=e,o=[],s=t.overrides,u=[],c=!1;function p(){o=i.map((function(e){return e.reference}))}function l(e){i.forEach((function(t){e?t.enable():t.disable()}))}function d(e){return i.map((function(t){var n=t.setProps;return t.setProps=function(i){n(i),t.reference===r&&e.setProps(i)},function(){t.setProps=n}}))}function v(e,t){var n=o.indexOf(t);if(t!==r){r=t;var a=(s||[]).concat("content").reduce((function(e,t){return e[t]=i[n].props[t],e}),{});e.setProps(Object.assign({},a,{getReferenceClientRect:"function"==typeof a.getReferenceClientRect?a.getReferenceClientRect:function(){return t.getBoundingClientRect()}}))}}l(!1),p();var m={fn:function(){return{onDestroy:function(){l(!0)},onHidden:function(){r=null},onClickOutside:function(e){e.props.showOnCreate&&!c&&(c=!0,r=null)},onShow:function(e){e.props.showOnCreate&&!c&&(c=!0,v(e,o[0]))},onTrigger:function(e,t){v(e,t.currentTarget)}}}},g=U(f(),Object.assign({},a(t,["overrides"]),{plugins:[m].concat(t.plugins||[]),triggerTarget:o,popperOptions:Object.assign({},t.popperOptions,{modifiers:[].concat((null==(n=t.popperOptions)?void 0:n.modifiers)||[],[_])})})),h=g.show;g.show=function(e){if(h(),!r&&null==e)return v(g,o[0]);if(!r||null!=e){if("number"==typeof e)return o[e]&&v(g,o[e]);if(i.includes(e)){var t=e.reference;return v(g,t)}return o.includes(e)?v(g,e):void 0}},g.showNext=function(){var e=o[0];if(!r)return g.show(0);var t=o.indexOf(r);g.show(o[t+1]||e)},g.showPrevious=function(){var e=o[o.length-1];if(!r)return g.show(e);var t=o.indexOf(r),n=o[t-1]||e;g.show(n)};var b=g.setProps;return g.setProps=function(e){s=e.overrides||s,b(e)},g.setInstances=function(e){l(!0),u.forEach((function(e){return e()})),i=e,l(!1),p(),d(g),g.setProps({triggerTarget:o})},u=d(g),g},U.delegate=function(e,n){var r=[],i=[],o=!1,u=n.target,c=a(n,["target"]),p=Object.assign({},c,{trigger:"manual",touch:!1}),f=Object.assign({},c,{showOnCreate:!0}),l=U(e,p);function d(e){if(e.target&&!o){var t=e.target.closest(u);if(t){var r=t.getAttribute("data-tippy-trigger")||n.trigger||L.trigger;if(!t._tippy&&!("touchstart"===e.type&&"boolean"==typeof f.touch||"touchstart"!==e.type&&r.indexOf(F[e.type])<0)){var a=U(t,f);a&&(i=i.concat(a))}}}}function v(e,t,n,i){void 0===i&&(i=!1),e.addEventListener(t,n,i),r.push({node:e,eventType:t,handler:n,options:i})}return s(l).forEach((function(e){var n=e.destroy,a=e.enable,s=e.disable;e.destroy=function(e){void 0===e&&(e=!0),e&&i.forEach((function(e){e.destroy()})),i=[],r.forEach((function(e){var t=e.node,n=e.eventType,r=e.handler,i=e.options;t.removeEventListener(n,r,i)})),r=[],n()},e.enable=function(){a(),i.forEach((function(e){return e.enable()})),o=!1},e.disable=function(){s(),i.forEach((function(e){return e.disable()})),o=!0},function(e){var n=e.reference;v(n,"touchstart",d,t),v(n,"mouseover",d),v(n,"focusin",d),v(n,"click",d)}(e)})),l},U.hideAll=function(e){var t=void 0===e?{}:e,n=t.exclude,r=t.duration;H.forEach((function(e){var t=!1;if(n&&(t=v(n)?e.reference===n:e.popper===n.popper),!t){var i=e.props.duration;e.setProps({duration:r}),e.hide(),e.state.isDestroyed||e.setProps({duration:i})}}))},U.roundArrow='<svg width="16" height="6" xmlns="http://www.w3.org/2000/svg"><path d="M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z"></svg>',U}));
//# sourceMappingURL=tippy.umd.min.js.map