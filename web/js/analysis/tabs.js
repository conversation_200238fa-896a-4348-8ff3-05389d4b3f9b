$(document).ready(function () {
//	
//	$(function () {
//		$("#buttons").tabs();
//	});

//	$(function () {
	var tabTitle = $("#tab_title"),
			tabContent = $("#tab_content"),
			tabTemplate = "<li><a href='#{href}'>#{label}</a> <span class='ui-icon ui-icon-close' role='presentation'>Remove Tab</span></li>",
			tabCounter = 4;
	var tabs = $("#tabs").tabs();
	tabs.find(".ui-tabs-nav").sortable({
		axis: "x",
		stop: function () {
			tabs.tabs("refresh");
		}
	});
	// modal dialog init: custom buttons and a "close" callback resetting the form inside
	var dialogWindow = $("#dialog").dialog({
		autoOpen: false,
		modal: true,
		buttons: {
			Add: function () {
				addTab();
				$(this).dialog("close");
			},
			Cancel: function () {
				$(this).dialog("close");
			}
		},
		close: function () {
			form[ 0 ].reset();
		}
	});
	// addTab form: calls addTab function on submit and closes the dialog
	var form = dialogWindow.find("form").submit(function (event) {
		addTab();
		dialogWindow.dialog("close");
		event.preventDefault();
	});
	// actual addTab function: adds new tab using the input from the form above
	function addTab() {
		var label = tabTitle.val() || "Tab " + tabCounter,
				id = "tabs-" + tabCounter,
				li = $(tabTemplate.replace(/#\{href\}/g, "#" + id).replace(/#\{label\}/g, label));
		tabs.find(".ui-tabs-nav").append(li);
		tabs.append("<div id='" + id + "' class='tabsContent'></div>");
		var index = $("#tabs >ul >li").size() - 1;
		tabs.tabs("refresh");
		tabs.tabs('option', 'active', index);

		tabCounter++;
	}

	// addTab button: just opens the dialog
	$("#add_tab").button().click(function () {
		dialogWindow.dialog("open");
	});
	// close icon: removing the tab on click 
	tabs.delegate("span.ui-icon-close", "click", function () {
		alert($(this).closest("li").text());
		var tabName = $(this).closest("li").text();
		var panelId = $(this).closest("li").remove().attr("aria-controls");
		$("#" + panelId).remove();
		tabs.tabs("refresh");
		return $.ajax({
			type: "GET",
			url: "/" + contextPath + "/user/saveconfig.htm",
			cache: false,
			data: "tabName=" + tabName + "&code=&textButton=&delete=true",
			contentType: "json",
			success: function (data) {
			}
		});
	});
	tabs.bind("keyup", function (event) {
		if (event.altKey && event.keyCode === $.ui.keyCode.BACKSPACE) {
			var panelId = tabs.find(".ui-tabs-active").remove().attr("aria-controls");
			$("#" + panelId).remove();
			tabs.tabs("refresh");
		}
	});
//	});
});


