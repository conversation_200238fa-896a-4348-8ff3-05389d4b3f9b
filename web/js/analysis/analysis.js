// array dei colori per la rappresentazione
var colors = ["#2b908f", "#90ee7e", "#f45b5b", "#7798BF", "#aaeeee", "#ff0066", "#eeaaee",
	"#55BF3B", "#DF5353", "#7798BF", "#aaeeee"];

// array con la serie di oggetti da rappresentare
var series = [];
var yaxis = [];
var xaxis = [];
var titleGraphic = "";
var unitaMisura = "m";

var interval5 = ["0-5", "5-10", "10-15", "15-20", "20-25", "25-30", "30-35", "35-40", "40-45", "REC 1°T", "45-50", "50-55", "55-60", "60-65", "65-70", "70-75", "75-80", "80-85", "85-90", "REC 2°T"];
var interval15 = ["0-15", "15-30", "30-45", "REC 1°T", "45-60", "60-75", "75-90", "REC 2°T"];
var intervalHalf = ["1°T", "2°T"];

var container;

var contextPath = $("#contextPath").val();

/*function jsListGames() {
 // mostra il cerchietto di caricamento
 jsLoadingAlpha(container);
 $.ajax({
 type: "GET",
 url: "/${mContextPath}/user/players.htm",
 cache: false,
 data: "fixtureId=2888",
 //data: "formSeasonId=" + $("#formSeasonId option:selected").val() + "&formCompetitionId=" + $("#formCompetitionId option:selected").val() + "&formDateFrom=" + $("#formDateFrom").val() + "&formDateTo=" + $("#formDateTo").val() + "&formReferee=" + $("#formReferee").val() + "&formMatchdayId=" + matId,
 success: function (msg) {
 $("#divListPlayer").html(msg);
 return false;
 }
 });
 } */
var colorCount = -1;

$(document).ready(function () {
	container = $('#containerGrafico');

	$.expr[':'].textEquals = $.expr.createPseudo(function (arg) {
		return function (elem) {
			return $(elem).text().match("^" + arg + "$");
		};
	});

//	var listMatch = $.parseJSON($("#listPartite").val());
//	$.each(listMatch, function (arrKey, arrValue) {
//		$("#selectPartita").append("<option value='" + arrValue.idFixture + "'>" + arrValue.homeTeam + "-" + arrValue.awayTeam + "</option>");
//	});


	$("#selectPartita").multiselect();

	$(".removeButton").click(function () {
		$(this).closest("div").remove();
		deleteButtonConfig($(this).closest("div").attr("id"));
	});


	$(".removeGiocatoreIcon img").click(function () {
		alert($(this).closest("span.giocatoreScelto").text());
		$(this).closest("span.giocatoreScelto").remove();
	});


	$("#expandGraphic").button();
	
	$("#graphicDiv").hide("slide");
	
	
//	$( ".button" ).sortable({
//      revert: true
//    });
//	$( ".button" ).draggable({
//      connectToSortable: "#sortable",
//      helper: "clone",
//      revert: "invalid"
//    });
//    $( ".button" ).disableSelection();

//	addButtonStatus();

});

// rimuove una categoria dal grafico a barre
function removeFromSeries(name) {
	var array = [];
	var axisY = [];
	var axisX = [];
	if (titleGraphic !== name) {
		for (var i = 0; i < series.length; i++) {
			if (series[i].name !== name) {
				series[i].yAxis = array.length;
				array.push(series[i]);
//			axisY.push(yaxis[i]);
				axisX.push(xaxis[i]);
			}
		}
	}
//	yaxis = axisY;
	xaxis = axisX;
	return array;
}

// disegna il grafico
function fillGraphics() {
//	alert(xaxis);
//	alert(JSON.stringify(series));
	container.highcharts({
		colors: colors,
		chart: {
			type: 'column',
			backgroundColor: '#262626'/*{
			 linearGradient: { x1: 0, y1: 0, x2: 1, y2: 1 },
			 stops: [
			 [0, '#2a2a2b'],
			 [1, '#3e3e40']
			 ]
			 }*/,
			style: {
				fontFamily: "'Unica One', sans-serif"
			},
			plotBorderColor: '#606063'
		},
		title: {
			text: titleGraphic,
			style: {
				color: '#E0E0E3',
				textTransform: 'uppercase',
				fontSize: '20px'
			}
		},
		subtitle: {
			text: '',
			style: {
				color: '#E0E0E3',
				textTransform: 'uppercase'
			}
		},
		xAxis: {
			categories: xaxis,
//			categories: [
//				'Team Average',
//				'Arbeloa',
//				'Bale',
//				'Benzema',
//				'Carvajal',
//				'Chicharito',
//				'Coentrao',
//				'De Torres',
//				'Ilara',
//				'Isco',
//				'Jese',
//				'Khedira',
//				'Kroos',
//				'Jese'
//			],
			gridLineColor: '#707073',
			labels: {
				style: {
					color: '#E0E0E3'
				}
			},
			lineColor: '#707073',
			minorGridLineColor: '#505053',
			tickColor: '#707073',
			title: {
				style: {
					color: '#A0A0A3'

				}
			}
		},
//		yAxis: yaxis,
		yAxis: {
			min: 0,
			gridLineColor: '#707073',
			labels: {
				style: {
					color: '#E0E0E3'
				}
			},
			lineColor: '#707073',
			minorGridLineColor: '#505053',
			tickColor: '#707073',
			tickWidth: 1,
			title: {
				style: {
					color: '#A0A0A3'
				}
			}
		},
		tooltip: {
			headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
			pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
					'<td style="padding:0"><b>{point.y:.1f} ' + unitaMisura + '</b></td></tr>',
			footerFormat: '</table>',
			shared: true,
			useHTML: true,
			backgroundColor: 'rgba(0, 0, 0, 0.85)',
			style: {
				color: '#F0F0F0'
			}
		},
		plotOptions: {
			column: {
				pointPadding: 0, //spazio fra le due colonne
				borderWidth: 0
			},
			row: {
				pointPadding: 0.2,
				borderWidth: 0
			}
		},
		series: series,
		legend: {
			itemStyle: {
				color: '#E0E0E3'
			},
			itemHoverStyle: {
				color: '#FFF'
			},
			itemHiddenStyle: {
				color: '#606063'
			}
		},
		labels: {
			style: {
				color: '#707073'
			}
		}
	});
}
;

function togglePlayer(idCheck) {
	var contextPath = $("#contextPath").val();
	var idTeam = idCheck;
//	var idTextString = idCheck ;
	if (idCheck === "aggSqu" || idCheck === "aggPor" || idCheck === "aggDif" || idCheck === "aggCen"
			|| idCheck === "aggAtt") {
		idTeam = idTeam + $('#selectSquadra option:selected').val();
//		idTextString = idCheck +$('#selectSquadra option:selected').val();
	}
	var teamSelected = $('#selectSquadra option:selected').text();

	var objPlayer = getPlayer(idCheck);
	if (!objPlayer) {
		objPlayer = {nome: $("#" + idCheck).val(), teamName: teamSelected};
	}
	if ($("#" + idCheck).is(':checked')) {
		$("#filtriGiocatoriSelezionati").append("<span class='giocatoreScelto' id='span" + idTeam + "'><input type='hidden' id='" + idTeam + "' name='" + idTeam + "' value='" + idTeam + "'/>" + objPlayer.nome + "<br />" + objPlayer.teamName + "<span id='iconRemove" + idTeam + "' class='removeGiocatoreIcon'><img src='/" + contextPath + "/images/delete.png' alt='Remove'/></span></span>");
//		setDraggable('span'+idCheck);
		$(".removeGiocatoreIcon img").click(function () {
			$(this).closest("span.giocatoreScelto").remove();
		});

	} else {
		$("#span" + idTeam + "").remove();
		$("#iconRemove" + idTeam + "").remove();
	}
}

function removeSelected(idCheck) {
	$("#span" + idCheck).remove();

	$("#" + idCheck).attr('checked', false);
}

function addPartita() {
	$("#selectSquadra").empty();
	var teams = [];
	$("#partiteSelezionate").empty();
	$("#selectPartita option").each(function () {
		var match = $(this).val();
		$("#hidden" + match).remove();
		if ($(this).is(":checked")) {
			var arr = $(this).text().split('-');
			$("#partiteSelezionate").append("<input type='hidden' id='hidden" + match + "' name='" + match + "' value='" + match + "'/>");
//			$("#partiteSelezionate").append("<span class='giocatoreScelto' id='span" + match + "'><input type='hidden' id='" + match + "' name='" + match + "' value='" + match + "'/>" + arr[0] + " - <br />" + arr[1] + "<span id='iconRemove" + match + "' class='removePartitaIcon'><img src='/" + contextPath + "/images/delete.png' alt='Remove'/></span></span>");
//			$(".removePartitaIcon img").click(function () {
//				$('#selectPartita option[value="' + match + '"]').removeAttr("selected");
////				alert($("#selectPartita").html());
//				$(this).closest("span.giocatoreScelto").remove();
//				$("#selectPartita").multiselect("deselect", match);
//			});
			var objTeam = getTeam(match);

			if ($.inArray(objTeam.homeTeam, teams) === -1) {
				teams.push(objTeam.homeTeam);
				$("#selectSquadra").append("<option value='" + objTeam.homeTeamId + "'>" + objTeam.homeTeam + "</option>");
			}
			if ($.inArray(objTeam.awayTeam, teams) === -1) {
				teams.push(objTeam.awayTeam);
				$("#selectSquadra").append("<option value='" + objTeam.awayTeamId + "'>" + objTeam.awayTeam + "</option>");
			}

//			$("#selectSquadra").append();
		}
	});
	fillPlayers();
}



function setDraggable(element) {
	$("#" + element).draggable();
}

function fillFasce() {

	var val = $('#parametriFiltro option:selected').val();
	var htmlText = "<option value=''></option>";
	var stringJson = $("#mapFasce").val();
	var mapFasce = $.parseJSON(stringJson);
	$.each(mapFasce, function (key, value) {
		if (key === val) {
			$.each(value, function (arrKey, arrValue) {
				htmlText += "<option value=" + arrValue.mCode + ">" + arrValue.etichetta + "</option>";
			});
		}
	});
	$("#fasceFiltro").empty();
	$("#fasceFiltro").html(htmlText);
}

function setSpinner() {
	var parameter = $('#parametriFiltro option:selected').val();
	var val = $('#fasceFiltro option:selected').val();
	var stringJson = $("#mapFasce").val();
	var mapFasce = $.parseJSON(stringJson);

	$.each(mapFasce, function (key, value) {
		if (key === parameter) {
			$.each(value, function (arrKey, arrValue) {
				if (arrValue.mCode == val) {
					$('#startFascia').val(arrValue.valMin);
					$('#endFascia').val(arrValue.valMax);
				}
			});
		}
	});
}

function fillIntervalli() {

	var val = $('#tipoIntervalloFiltro option:selected').val();
	var htmlText = "<option value=''></option>";
	var stringJson = $("#mapIntervalli").val();
	var mapIntervalli = $.parseJSON(stringJson);
	$.each(mapIntervalli, function (key, value) {
		if (key === val) {
			$.each(value, function (arrKey, arrValue) {
				htmlText += "<option value=" + arrValue.code + ">" + arrValue.desc + "</option>";
			});
		}
	});
	$("#intervalloFiltro").empty();
	$("#intervalloFiltro").html(htmlText);
}

function fillPlayers() {

	var val = $('#selectSquadra option:selected').val();
//	<span class='checkboxPlayer'><input type='checkbox' id='team' name='checkTeam' value='team' onclick='togglePlayer('team');'/> " + $('#selectSquadra option:selected').text() + "</span>
	var htmlText = "";
	var stringJson = $("#mapPlayers").val();
	var mapPlayers = $.parseJSON(stringJson);

	$.each(mapPlayers, function (key, value) {
		if (key === val) {
			$.each(value, function (arrKey, arrValue) {
				htmlText += "<span class='checkboxPlayer'><input type='checkbox' id='" + arrValue.id + "' name='check" + arrValue.id + "' value='" + arrValue.nome + "' onclick=\"togglePlayer('" + arrValue.id + "');\"> " + arrValue.nome + " - " + arrValue.ruolo + "</span>";
			});
		}
	});
	$("#sceltaGiocatori").empty();
	$("#sceltaGiocatori").html(htmlText);
}

function createButton() {
	var parametro = $('#parametriFiltro option:selected').text();
	var fascia = $('#fasceFiltro option:selected').text();
	var grandezza = $('#grandezzeFiltro option:selected').text();
	var intervallo = $('#tipoIntervalloFiltro option:selected').text();
	var operazione = $('#operazioniFiltro option:selected').text();
	var startFascia = $('#startFascia').val();
	var endFascia = $('#endFascia').val()
	var code = "rangeId=" + $('#parametriFiltro option:selected').val() + "&rangeCode=" +
			$('#fasceFiltro option:selected').val() + "&outputType=1"
			+ "&outputCode=" + $('#grandezzeFiltro option:selected').val()
//			+ "&intervalId=" + $('#tipoIntervalloFiltro option:selected').val()
			+ "&startFascia=" + startFascia
			+ "&endFascia=" + endFascia;
//			+ "&intervalCode=" + $('#intervalloFiltro option:selected').val();

	var saveCode = "rangeId=" + $('#parametriFiltro option:selected').val() + "|rangeCode=" +
			$('#fasceFiltro option:selected').val() + "|outputType=1"
			+ "|outputCode=" + $('#grandezzeFiltro option:selected').val()
//			+ "&intervalId=" + $('#tipoIntervalloFiltro option:selected').val()
			+ "|startFascia=" + startFascia
			+ "|endFascia=" + endFascia;

	var textButton = parametro + ": " + fascia + "(" + startFascia + "," + endFascia + ")" + " | " + grandezza;
//	if (!intervallo.trim()) {
//	} else {
//		textButton += " | " + intervallo;
//	}
	if (!operazione.trim()) {
	} else {
		textButton += " | " + operazione;
	}


	var panel = $("#tabs .ui-tabs-active a").attr("href");

//	$("#buttons").append("<div class='button' onclick='changeButtonStatus(event);'><p class='textbutton'><input type='hidden' id='" + code + "' name='" + code + "' value='" + code + "'/>" + textButton + "</p></div>");
	$(panel).append("<div class='button' onclick='changeButtonStatus(event);'><p class='textbutton'><input type='hidden' id='" + code + "' name='" + code + "' value='" + code + "'/>" + textButton + "<span class='removeButton'><img src='/" + contextPath + "/images/deleteWhite.png' alt='Remove'/></span></p></div>");
	var tabName = $("#tabs .ui-tabs-active a").text();
	if (!tabName.trim()) {
		alert("Select tab");
	} else {
		saveAnalysisConfig(saveCode, tabName, textButton);
	}

}
;


function changeButtonStatus(event) {
	var btnText = $(event.target).text();
	if (btnText.trim() !== '') {
		if ($('div').filter(function () {
			return $(this).text() === btnText;
		}).hasClass("selected")) {
			$('div').filter(function () {
				return $(this).text() === btnText;
			}).removeClass("selected");
			series = removeFromSeries(btnText);
			fillGraphics();
		} else {
			$('div').filter(function () {
				return $(this).text() === btnText;
			}).addClass("selected");

			jsLoadData($('div').filter(function () {
				return $(this).text() === btnText;
			}), btnText);

			colorCount = colorCount + 1;
//			yaxis = $.merge(yaxis, [{
//					min: 0,
//					gridLineColor: colors[series.length % colors.length],
//					labels: {
//						style: {
//							color: colors[colorCount % colors.length]
//						}
//					},
//					lineColor: colors[series.length % colors.length],
//					minorGridLineColor: colors[series.length % colors.length],
//					tickColor: colors[series.length % colors.length],
//					tickWidth: 1,
//					title: {
//						text: "",
//						style: {
//							color: colors[colorCount % colors.length]
//						}
//					}
//				}]);
//			series = $.merge(series, [{
//					name: '' + btnText + '',
//					data: [48.9 * tot, 38.8 * tot, 39.3 * tot, 41.4 * tot, 47.0 * tot, 48.3 * tot, 59.0 * tot, 59.6 * tot, 52.4 * tot, 65.2 * tot, 59.3 * tot, 51.2 * tot],
//					color: colors[colorCount % colors.length],
//					yAxis: series.length
//				}]);

		}
	}
}
;


function jsLoadData(button, text) {
	var contextPath = $("#contextPath").val();
	$("#graphicDiv").show("slide",100);
	;
	var average = $("#checkMedia").is(':checked');

	var code = $(button).children("p").children("input").val() + "&intervalId=" + $('#tipoIntervalloFiltro option:selected').val() + "&average=" + average;
	var typeOutput = $('#grandezzeFiltro option:selected').val();
	if (typeOutput == "dist") {
		unitaMisura = "m";
	} else if (typeOutput == "time") {
		unitaMisura = "sec";
	} else {
		unitaMisura = "";
	}

	var players = "";
	$("#filtriGiocatoriSelezionati").children("span.giocatoreScelto").each(function () {
		var context = $(this);
		if (players === '') {
			players = context.children("input").val();
		} else {
			players = players + "-" + context.children("input").val();
		}
	});

	var fixtures = "";
	$("#partiteSelezionate").children("input").each(function () {
		var context = $(this);

		if (fixtures === '') {
			fixtures = context.val();
		} else {
			fixtures = fixtures + "-" + context.val();
		}
	});

	$.ajax({
		type: "GET",
		url: "/" + contextPath + "/user/loadData.htm",
		cache: false,
		contentType: "application/json",
		data: code + "&players=" + players + "&fixtures=" + fixtures,
		success: function (data) {
			var names = $.parseJSON(data);
			var namePlayers = names.asseX;
			$.each(names, function (key, value) {
				if (key == "map") {
					$.each(value, function (arrKey, arrValue) {
						var array = [];
						$.each(arrValue, function (listKey, listValue) {
							array.push(listValue);
						});
						var objPlayer = getPlayer(arrKey);
						colorCount = colorCount + 1;
						if (objPlayer === "") {
							if (average) {
								series = $.merge(series, [{
										type: 'spline',
										name: arrKey,
										data: array,
										marker: {
											lineWidth: 2,
											lineColor: "#000000",
											fillColor: 'black'
										}

									}]);
							} else {
								series = $.merge(series, [{
										name: '' + arrKey + '',
										data: array,
										color: colors[colorCount % colors.length],
										yAxis: 0
									}]);
							}
						} else {
							series = $.merge(series, [{
									name: '' + objPlayer.nome + '',
									data: array,
									color: colors[colorCount % colors.length],
									yAxis: 0
								}]);
						}


					});

				}
			});
//			$.each(names, function (key, value) {
//				var obj = $.parseJSON(value);
//				var array = [];
////				namePlayers = [];
//				$.each(obj, function (arrKey, arrValue) {
//					if ($.inArray(arrValue.key, namePlayers) === -1) {
//						namePlayers.push(arrValue.key);
//						
//						
//					}
//					array.push(arrValue.val);
//					count += 1;
//				});
//
//				var objPlayer = getPlayer(key);
//				colorCount = colorCount + 1;
//
//				series = $.merge(series, [{
//						name: '' + objPlayer.nome + '',
//						data: array,
//						color: colors[colorCount % colors.length],
////					yAxis: series.length
//						yAxis: 0
//					}]);
//				alert(JSON.stringify(namePlayers));
//			});
			titleGraphic = text;
			xaxis = namePlayers;
//			series = $.merge(series, [{
//					name: '' + text + '',
//					data: array,
//					color: colors[colorCount % colors.length],
////					yAxis: series.length
//					yAxis: 0
//				}]);
//			var intervalValue = $('#tipoIntervalloFiltro option:selected').val();
//			if(intervalValue == '5'){
//				xaxis = interval5;
//			}else if(intervalValue == '15'){
//				xaxis = interval15;
//			}else if(intervalValue == 'half'){
//				xaxis = intervalHalf;
//			}else if(!intervalValue.trim()){
//				
//			}
			fillGraphics();
		}

		//data: "formSeasonId=" + $("#formSeasonId option:selected").val() + "&formCompetitionId=" + $("#formCompetitionId option:selected").val() + "&formDateFrom=" + $("#formDateFrom").val() + "&formDateTo=" + $("#formDateTo").val() + "&formReferee=" + $("#formReferee").val() + "&formMatchdayId=" + matId,
	});

//			$("#containerGrafico").html(msg);
//			console.log(msg);
//			var textFasce = $("#mapAtletiFasce").val();
//			alert(textFasce);
//			container = $('#containerGrafico');
//			fillGraphics();
//			result = msg;
//			return false;

}

// passo l'id del giocatore: se lo trovo restituisco un oggetto che lo descrive altrimenti una stringa vuota
function getPlayer(id) {
	var jsonAtleta = "";
	var mapPlayers = $.parseJSON($("#mapPlayers").val());
	$.each(mapPlayers, function (key, value) {
		$.each(value, function (arrKey, arrValue) {
			if (arrValue.id == id) {
				jsonAtleta = JSON.stringify(arrValue);
			}
		});

	});
	var objPlayer;
	if (!jsonAtleta.trim()) {
		objPlayer = "";
	} else {
		objPlayer = $.parseJSON(jsonAtleta);
	}
	return objPlayer;
}

function getTeam(id) {
	var listMatch = $.parseJSON($("#listPartite").val());
	var objTeam = "";
	$.each(listMatch, function (arrKey, arrValue) {
		if (arrValue.idFixture == id) {
			objTeam = arrValue;
		}
	});
	return objTeam;
}

function saveAnalysisConfig(saveCode, tabName, textButton) {
	return $.ajax({
		type: "GET",
		url: "/" + contextPath + "/user/saveconfig.htm",
		cache: false,
		data: "tabName=" + tabName + "&code=" + saveCode + "&textButton=" + textButton + "&delete=false",
		contentType: "json",
		success: function (data) {
			$(".removeButton").click(function () {
				$(this).closest("div").remove();
				deleteButtonConfig(data);
			});
		}
	});
}

function deleteButtonConfig(id) {
	$.ajax({
		type: "GET",
		url: "/" + contextPath + "/user/deletebuttonconfig.htm",
		cache: false,
		contentType: "application/json",
		data: "id=" + id,
		success: function () {
		}
	});
}

function zoomGraphic() {
	if ($("#expandGraphic").text() === "Expand") {
		$("#tabsContainer").hide("slide", {direction: "left"}, 100);
		$("#tabsContainer").css("width", "0");
		$("#graphicDiv").css("width", "99%");
		$("#containerGrafico").css("height", "600px");
		fillGraphics();
		$("#expandGraphic").text("Reduce");
	} else {
		$("#tabsContainer").css("width", "49%");
		$("#graphicDiv").css("width", "49%");
		$("#containerGrafico").css("height", "300px");
		$("#tabsContainer").show("slide", {direction: "left"}, 100);
		$("#containerGrafico").show("slide", {direction: "left"}, 100);
		fillGraphics();
		$("#expandGraphic").text("Expand");
	}
	$("#expandGraphic").button();
}

///////////////////



	