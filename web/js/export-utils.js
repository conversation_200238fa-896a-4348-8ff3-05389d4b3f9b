function exportEvents() {
    var index = 0;
    if (notEmpty(eventMap) || notEmpty(checkedActions)) {
        var rows = [];

        var eventIds = [];
        if (checkedActions.length > 0) {
            eventIds = checkedActions;
        } else {
            actionTable.rows({filter: 'applied'}).every(function () {
                var id = $(this.node()).attr("id").replace("rowEventId", "");
                eventIds.push(id);
            });
        }

        eventIds.forEach(function (eventId) {
            if (notEmpty(eventId)) {
                const eventData = eventMap.get(eventId);
                if (notEmpty(eventData)) {
//                    console.log(eventData.descrAzione, eventData.durataAzione, eventData.mActionAngle, eventData.mActionPos, eventData.mActionPosNormalized,
//                            eventData.mActionEnd, eventData.mActionEndNormalized, eventData.mShot3D, eventData.mShot3DNormalized, eventData.mTeam,
//                            eventData._player, eventData._playerTo);

                    var descrParts = eventData.descrAzione.split("#");

                    var row = new Object();
                    row.event = descrParts[0];
                    row.tags = descrParts[1];
                    row.period = descrParts[2].split(",")[0];
                    row.duration = eventData.durataAzione;
                    if (notEmpty(eventData.mActionAngle)) {
                        row.angle = eventData.mActionAngle;
                    } else {
                        row.angle = "-";
                    }
                    if (notEmpty(eventData.mTeam)) {
                        row.team = eventData.mTeam;
                    } else {
                        row.team = "-";
                    }
                    if (notEmpty(eventData._player) && Object.keys(eventData._player).length > 0) {
                        var players = [];
                        for (let key in eventData._player) {
                            if (eventData._player.hasOwnProperty(key) && eventData._player[key].hasOwnProperty('known_name')) {
                                players.push(eventData._player[key]['known_name']);
                            }
                        }
                        row.playerFrom = players.join(", ");
                    } else {
                        row.playerFrom = "-";
                    }
                    if (notEmpty(eventData._playerTo) && Object.keys(eventData._player).length > 0) {
                        row.playerTo = eventData._playerTo.known_name;
                    } else {
                        row.playerTo = "-";
                    }
                    if (notEmpty(eventData.mActionPos) && !eventData.mActionPos.isDefault) {
                        row.startPoint = eventData.mActionPos.x + ";" + eventData.mActionPos.y;
                    } else {
                        row.startPoint = "-";
                    }
                    if (notEmpty(eventData.mActionPosNormalized) && !eventData.mActionPosNormalized.isDefault) {
                        row.startPointNorm = eventData.mActionPosNormalized.x + ";" + eventData.mActionPosNormalized.y;
                    } else {
                        row.startPointNorm = "-";
                    }
                    if (notEmpty(eventData.mActionPosEnd) && !eventData.mActionPosEnd.isDefault) {
                        row.endPoint = eventData.mActionPosEnd.x + ";" + eventData.mActionPosEnd.y;
                    } else {
                        row.endPoint = "-";
                    }
                    if (notEmpty(eventData.mActionPosEndNormalized) && !eventData.mActionPosEndNormalized.isDefault) {
                        row.endPointNorm = eventData.mActionPosEndNormalized.x + ";" + eventData.mActionPosEndNormalized.y;
                    } else {
                        row.endPointNorm = "-";
                    }
                    if (notEmpty(eventData.mShot3D) && !eventData.mShot3D.isDefault) {
                        row.intersectionPoint = eventData.mShot3D.x + ";" + eventData.mShot3D.y + ";" + eventData.mShot3D.z;
                    } else {
                        row.intersectionPoint = "-";
                    }
                    if (notEmpty(eventData.mShot3DNormalized) && !eventData.mShot3DNormalized.isDefault) {
                        row.intersectionPointNorm = eventData.mShot3DNormalized.x + ";" + eventData.mShot3DNormalized.y + ";" + eventData.mShot3DNormalized.z;
                    } else {
                        row.intersectionPointNorm = "-";
                    }

                    rows.push(row);
                } else {
                    console.warn("exportEvents()", "Empty eventData at index " + index);
                }
            } else {
                console.warn("exportEvents()", "Event id not found at index " + index);
            }
            index++;
        });

        if (rows.length > 0) {
            // Ottieni tutti i campi dagli oggetti
            const fields = Object.keys(rows[0]);
            const csvContent = [];
            // Aggiungi intestazioni al CSV
            csvContent.push(fields.join(','));
            // Aggiungi righe al CSV
            rows.forEach(item => {
                const row = fields.map(field => JSON.stringify(item[field], (key, value) => value === null ? '' : value)).join(',');
                csvContent.push(row);
            });
            // Crea il contenuto del file CSV
            const csvString = csvContent.join('\n');
            // Crea un blob con il contenuto CSV
            const blob = new Blob([csvString], {type: 'text/csv'});
            // Crea un link e fai partire il download
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = "SICS-Events-" + (new Date().toLocaleDateString("en-GB").replace("/", "_")) + ".csv";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    } else {
        console.warn("exportEvents()", "eventMap is empty and checkedActions is empty");
    }
}