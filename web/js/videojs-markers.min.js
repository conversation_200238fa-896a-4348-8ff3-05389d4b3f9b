/*! videojs-markers - v0.6.1 - 2016-10-24
* Copyright (c) 2016 ; Licensed  */
"use strict";!function(a,b,c){function d(){var a=(new Date).getTime(),b="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(b){var c=(a+16*Math.random())%16|0;return a=Math.floor(a/16),("x"==b?c:3&c|8).toString(16)});return b}function e(b){function c(){u.sort(function(a,b){return s.markerTip.time(a)-s.markerTip.time(b)})}function e(a){a.forEach(function(a){a.key=d(),v.find(".vjs-progress-holder").append(i(a)),t[a.key]=a,u.push(a)}),c()}function h(a){return s.markerTip.time(a)/x.duration()*100}function i(b){var c=a("<div class='vjs-marker'></div>");return c.css(s.markerStyle).css({"margin-left":-parseFloat(c.css("width"))/2+"px",left:h(b)+"%"}).attr("data-marker-key",b.key).attr("data-marker-time",s.markerTip.time(b)),b.class&&c.addClass(b.class),c.on("click",function(c){var d=!1;if("function"==typeof s.onMarkerClick&&(d=s.onMarkerClick(b)===!1),!d){var e=a(this).data("marker-key");x.currentTime(s.markerTip.time(t[e]))}}),s.markerTip.display&&l(c),c}function j(){u.forEach(function(a){var b=v.find(".vjs-marker[data-marker-key='"+a.key+"']"),c=s.markerTip.time(a);b.data("marker-time")!==c&&b.css({left:h(a)+"%"}).attr("data-marker-time",c)}),c()}function k(a){z&&(A=g,z.css("visibility","hidden")),w=g;var b=[];a.forEach(function(a){var c=u[a];c&&(delete t[c.key],b.push(a),v.find(".vjs-marker[data-marker-key='"+c.key+"']").remove())}),b.reverse(),b.forEach(function(a){u.splice(a,1)}),c()}function l(b){b.on("mouseover",function(){var c=t[a(b).data("marker-key")];y&&(y.find(".vjs-tip-inner").html(s.markerTip.text(c)),y.css({left:h(c)+"%","margin-left":-parseFloat(y.width())/2-5+"px",visibility:"visible"}))}),b.on("mouseout",function(){!!y&&y.css("visibility","hidden")})}function m(){y=a("<div class='vjs-tip'><div class='vjs-tip-arrow'></div><div class='vjs-tip-inner'></div></div>"),v.find(".vjs-progress-holder").append(y)}function n(){if(s.breakOverlay.display&&!(w<0)){var a=x.currentTime(),b=u[w],c=s.markerTip.time(b);a>=c&&a<=c+s.breakOverlay.displayTime?(A!==w&&(A=w,z&&z.find(".vjs-break-overlay-text").html(s.breakOverlay.text(b))),z&&z.css("visibility","visible")):(A=g,z&&z.css("visibility","hidden"))}}function o(){z=a("<div class='vjs-break-overlay'><div class='vjs-break-overlay-text'></div></div>").css(s.breakOverlay.style),v.append(z),A=g}function p(){q(),n(),b.onTimeUpdateAfterMarkerUpdate&&b.onTimeUpdateAfterMarkerUpdate()}function q(){if(u.length){var a=function(a){return a<u.length-1?s.markerTip.time(u[a+1]):x.duration()},c=x.currentTime(),d=g;if(w!==g){var e=a(w);if(c>=s.markerTip.time(u[w])&&c<e)return;if(w===u.length-1&&c===x.duration())return}if(c<s.markerTip.time(u[0]))d=g;else for(var f=0;f<u.length;f++)if(e=a(f),c>=s.markerTip.time(u[f])&&c<e){d=f;break}d!==w&&(d!==g&&b.onMarkerReached&&b.onMarkerReached(u[d],d),w=d)}}function r(){s.markerTip.display&&m(),x.markers.removeAll(),e(b.markers),s.breakOverlay.display&&o(),p(),x.on("timeupdate",p)}var s=a.extend(!0,{},f,b),t={},u=[],v=a(this.el()),w=g,x=this,y=null,z=null,A=g;x.on("loadedmetadata",function(){r()}),x.markers={getMarkers:function(){return u},next:function(){for(var a=x.currentTime(),b=0;b<u.length;b++){var c=s.markerTip.time(u[b]);if(c>a){x.currentTime(c);break}}},prev:function(){for(var a=x.currentTime(),b=u.length-1;b>=0;b--){var c=s.markerTip.time(u[b]);if(c+.5<a)return void x.currentTime(c)}},add:function(a){e(a)},remove:function(a){k(a)},removeAll:function(){for(var a=[],b=0;b<u.length;b++)a.push(b);k(a)},updateTime:function(){j()},reset:function(a){x.markers.removeAll(),e(a)},destroy:function(){x.markers.removeAll(),z&&z.remove(),y&&y.remove(),x.off("timeupdate",n),delete x.markers}}}var f={markerStyle:{width:"7px","border-radius":"30%","background-color":"red"},markerTip:{display:!0,text:function(a){return"Break: "+a.text},time:function(a){return a.time}},breakOverlay:{display:!1,displayTime:3,text:function(a){return"Break overlay: "+a.overlayText},style:{width:"100%",height:"20%","background-color":"rgba(0,0,0,0.7)",color:"white","font-size":"17px"}},onMarkerClick:function(a){},onMarkerReached:function(a,b){},markers:[]},g=-1;b.registerPlugin("markers",e)}(jQuery,window.videojs);