/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
/* ========================================================================
   Component: Form password
 ========================================================================== */
/*
 * 1. Container width fits its content
 * 2. Create position context
 * 3. Prevent `inline-block` consequences
 */
.uk-form-password {
  /* 1 */
  display: inline-block;
  /* 2 */
  position: relative;
  /* 3 */
  max-width: 100%;
}
.uk-form-password-toggle {
  display: block;
  position: absolute;
  top: 50%;
  right: 10px;
  margin-top: -6px;
  font-size: 13px;
  line-height: 13px;
  color: #999999;
}
.uk-form-password-toggle:hover {
  color: #999999;
  text-decoration: none;
}
.uk-form-password > input {
  padding-right: 50px !important;
}
