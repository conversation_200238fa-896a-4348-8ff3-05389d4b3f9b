/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
.uk-autocomplete{display:inline-block;position:relative;max-width:100%;vertical-align:middle}.uk-nav-autocomplete>li>a{color:#444}.uk-nav-autocomplete>li.uk-active>a{background:#009dd8;color:#fff;outline:0;box-shadow:inset 0 2px 4px rgba(0,0,0,.2);text-shadow:0 -1px 0 rgba(0,0,0,.2)}.uk-nav-autocomplete .uk-nav-header{color:#999}.uk-nav-autocomplete .uk-nav-divider{border-top:1px solid #ddd}