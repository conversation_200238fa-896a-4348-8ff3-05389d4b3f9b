/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
.uk-autocomplete{display:inline-block;position:relative;max-width:100%;vertical-align:middle}.uk-nav-autocomplete>li>a{color:#444}.uk-nav-autocomplete>li.uk-active>a{background:#00a8e6;color:#fff;outline:0;box-shadow:inset 0 0 5px rgba(0,0,0,.05);text-shadow:0 -1px 0 rgba(0,0,0,.1)}.uk-nav-autocomplete .uk-nav-header{color:#999}.uk-nav-autocomplete .uk-nav-divider{border-top:1px solid #ddd}