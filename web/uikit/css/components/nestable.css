/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
/* ========================================================================
   Component: Nestable
 ========================================================================== */
.uk-nestable {
  padding: 0;
  list-style: none;
}
/* Sub-object `uk-nestable-list`
 ========================================================================== */
.uk-nestable-list {
  margin: 0;
  padding-left: 40px;
  list-style: none;
}
/* Sub-modifier `uk-nestable-list-dragged`
 ========================================================================== */
.uk-nestable-list-dragged {
  position: absolute;
  z-index: 1050;
  padding-left: 0;
  pointer-events: none;
}
/* Sub-object `uk-nestable-item`
 ========================================================================== */
.uk-nestable-item {
  margin-bottom: 10px;
  padding: 5px;
  background: #f5f5f5;
}
/* Sub-object `uk-nestable-placeholder`
 * The placeholder which marks the drop area
 ========================================================================== */
.uk-nestable-placeholder {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  margin-bottom: 10px;
  border: 1px dashed #dddddd;
}
/* Sub-object `uk-nestable-empty`
 * The style of an empty list
 ========================================================================== */
.uk-nestable-empty {
  min-height: 40px;
}
/* Sub-object `uk-nestable-handle`
 ========================================================================== */
/*
 * 1. Deactivate browser touch actions in IE11
 */
.uk-nestable-handle {
  display: inline-block;
  font-size: 18px;
  color: #dddddd;
  /* 1 */
  touch-action: none;
}
/* Hover */
.uk-nestable-handle:hover {
  cursor: move;
}
/* Icon */
.uk-nestable-handle:after {
  content: "\f0c9";
  font-family: FontAwesome;
}
/* Sub-object `uk-nestable-moving`
 ========================================================================== */
.uk-nestable-moving,
.uk-nestable-moving * {
  cursor: move;
}
/* Sub-object `[data-action='toggle']`
 ========================================================================== */
/* Hidden by default */
[data-nestable-action='toggle'] {
  display: inline-block;
  color: #999999;
  visibility: hidden;
}
/* Hover */
[data-nestable-action='toggle']:hover {
  color: #444444;
  cursor: pointer;
}
/* Icon */
[data-nestable-action='toggle']:after {
  content: "\f147";
  font-family: FontAwesome;
}
/*
 * Show if nested
 */
.uk-parent > .uk-nestable-item [data-nestable-action='toggle'] {
  visibility: visible;
}
/*
 * Collapsed
 */
.uk-collapsed > .uk-nestable-item [data-nestable-action='toggle']:after {
  content: "\f196";
}
.uk-collapsed .uk-nestable-list {
  display: none;
}
