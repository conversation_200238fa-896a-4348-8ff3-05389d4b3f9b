/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){"use strict";t.component("buttonRadio",{defaults:{target:".uk-button"},boot:function(){t.$html.on("click.buttonradio.uikit","[data-uk-button-radio]",function(i){var e=t.$(this);if(!e.data("buttonRadio")){var a=t.buttonRadio(e,t.Utils.options(e.attr("data-uk-button-radio"))),n=t.$(i.target);n.is(a.options.target)&&n.trigger("click")}})},init:function(){var i=this;this.find(i.options.target).attr("aria-checked","false").filter(".uk-active").attr("aria-checked","true"),this.on("click",this.options.target,function(e){var a=t.$(this);a.is('a[href="#"]')&&e.preventDefault(),i.find(i.options.target).not(a).removeClass("uk-active").blur(),a.addClass("uk-active"),i.find(i.options.target).not(a).attr("aria-checked","false"),a.attr("aria-checked","true"),i.trigger("change.uk.button",[a])})},getSelected:function(){return this.find(".uk-active")}}),t.component("buttonCheckbox",{defaults:{target:".uk-button"},boot:function(){t.$html.on("click.buttoncheckbox.uikit","[data-uk-button-checkbox]",function(i){var e=t.$(this);if(!e.data("buttonCheckbox")){var a=t.buttonCheckbox(e,t.Utils.options(e.attr("data-uk-button-checkbox"))),n=t.$(i.target);n.is(a.options.target)&&n.trigger("click")}})},init:function(){var i=this;this.find(i.options.target).attr("aria-checked","false").filter(".uk-active").attr("aria-checked","true"),this.on("click",this.options.target,function(e){var a=t.$(this);a.is('a[href="#"]')&&e.preventDefault(),a.toggleClass("uk-active").blur(),a.attr("aria-checked",a.hasClass("uk-active")),i.trigger("change.uk.button",[a])})},getSelected:function(){return this.find(".uk-active")}}),t.component("button",{defaults:{},boot:function(){t.$html.on("click.button.uikit","[data-uk-button]",function(){var i=t.$(this);if(!i.data("button")){{t.button(i,t.Utils.options(i.attr("data-uk-button")))}i.trigger("click")}})},init:function(){var t=this;this.element.attr("aria-pressed",this.element.hasClass("uk-active")),this.on("click",function(i){t.element.is('a[href="#"]')&&i.preventDefault(),t.toggle(),t.trigger("change.uk.button",[t.element.blur().hasClass("uk-active")])})},toggle:function(){this.element.toggleClass("uk-active"),this.element.attr("aria-pressed",this.element.hasClass("uk-active"))}})}(UIkit);