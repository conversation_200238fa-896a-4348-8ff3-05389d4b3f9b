/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){"use strict";var e,i=!1;t.component("dropdown",{defaults:{mode:"hover",remaintime:800,justify:!1,boundary:t.$win,delay:0},remainIdle:!1,boot:function(){var e=t.support.touch?"click":"mouseenter";t.$html.on(e+".dropdown.uikit","[data-uk-dropdown]",function(i){var o=t.$(this);if(!o.data("dropdown")){var s=t.dropdown(o,t.Utils.options(o.attr("data-uk-dropdown")));("click"==e||"mouseenter"==e&&"hover"==s.options.mode)&&s.element.trigger(e),s.element.find(".uk-dropdown").length&&i.preventDefault()}})},init:function(){var i=this;this.dropdown=this.find(".uk-dropdown"),this.centered=this.dropdown.hasClass("uk-dropdown-center"),this.justified=this.options.justify?t.$(this.options.justify):!1,this.boundary=t.$(this.options.boundary),this.flipped=this.dropdown.hasClass("uk-dropdown-flip"),this.boundary.length||(this.boundary=t.$win),this.element.attr("aria-haspopup","true"),this.element.attr("aria-expanded",this.element.hasClass("uk-open")),"click"==this.options.mode||t.support.touch?this.on("click.uikit.dropdown",function(e){var o=t.$(e.target);o.parents(".uk-dropdown").length||((o.is("a[href='#']")||o.parent().is("a[href='#']")||i.dropdown.length&&!i.dropdown.is(":visible"))&&e.preventDefault(),o.blur()),i.element.hasClass("uk-open")?(o.is("a:not(.js-uk-prevent)")||o.is(".uk-dropdown-close")||!i.dropdown.find(e.target).length)&&i.hide():i.show()}):this.on("mouseenter",function(){i.remainIdle&&clearTimeout(i.remainIdle),e&&clearTimeout(e),e=setTimeout(i.show.bind(i),i.options.delay)}).on("mouseleave",function(){e&&clearTimeout(e),i.remainIdle=setTimeout(function(){i.hide()},i.options.remaintime)}).on("click",function(e){var o=t.$(e.target);i.remainIdle&&clearTimeout(i.remainIdle),(o.is("a[href='#']")||o.parent().is("a[href='#']"))&&e.preventDefault(),i.show()})},show:function(){t.$html.off("click.outer.dropdown"),i&&i[0]!=this.element[0]&&(i.removeClass("uk-open"),i.attr("aria-expanded","false")),e&&clearTimeout(e),this.checkDimensions(),this.element.addClass("uk-open"),this.element.attr("aria-expanded","true"),this.trigger("show.uk.dropdown",[this]),t.Utils.checkDisplay(this.dropdown,!0),i=this.element,this.registerOuterClick()},hide:function(){this.element.removeClass("uk-open"),this.remainIdle=!1,this.element.attr("aria-expanded","false"),i&&i[0]==this.element[0]&&(i=!1)},registerOuterClick:function(){var o=this;t.$html.off("click.outer.dropdown"),setTimeout(function(){t.$html.on("click.outer.dropdown",function(s){e&&clearTimeout(e);var n=t.$(s.target);i&&i[0]==o.element[0]&&(n.is("a:not(.js-uk-prevent)")||n.is(".uk-dropdown-close")||!o.dropdown.find(s.target).length)&&(o.hide(),t.$html.off("click.outer.dropdown"))})},10)},checkDimensions:function(){if(this.dropdown.length){this.justified&&this.justified.length&&this.dropdown.css("min-width","");var e=this,i=this.dropdown.css("margin-"+t.langdirection,""),o=i.show().offset(),s=i.outerWidth(),n=this.boundary.width(),r=this.boundary.offset()?this.boundary.offset().left:0;if(this.centered&&(i.css("margin-"+t.langdirection,-1*(parseFloat(s)/2-i.parent().width()/2)),o=i.offset(),(s+o.left>n||o.left<0)&&(i.css("margin-"+t.langdirection,""),o=i.offset())),this.justified&&this.justified.length){var d=this.justified.outerWidth();if(i.css("min-width",d),"right"==t.langdirection){var a=n-(this.justified.offset().left+d),l=n-(i.offset().left+i.outerWidth());i.css("margin-right",a-l)}else i.css("margin-left",this.justified.offset().left-o.left);o=i.offset()}s+(o.left-r)>n&&(i.addClass("uk-dropdown-flip"),o=i.offset()),o.left-r<0&&(i.addClass("uk-dropdown-stack"),i.hasClass("uk-dropdown-flip")&&(this.flipped||(i.removeClass("uk-dropdown-flip"),o=i.offset(),i.addClass("uk-dropdown-flip")),setTimeout(function(){(i.offset().left-r<0||!e.flipped&&i.outerWidth()+(o.left-r)<n)&&i.removeClass("uk-dropdown-flip")},0)),this.trigger("stack.uk.dropdown",[this])),i.css("display","")}}})}(UIkit);