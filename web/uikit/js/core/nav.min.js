/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){"use strict";function i(i){var a=t.$(i),n="auto";if(a.is(":visible"))n=a.outerHeight();else{var s={position:a.css("position"),visibility:a.css("visibility"),display:a.css("display")};n=a.css({position:"absolute",visibility:"hidden",display:"block"}).outerHeight(),a.css(s)}return n}t.component("nav",{defaults:{toggle:">li.uk-parent > a[href='#']",lists:">li.uk-parent > ul",multiple:!1},boot:function(){t.ready(function(i){t.$("[data-uk-nav]",i).each(function(){var i=t.$(this);if(!i.data("nav")){t.nav(i,t.Utils.options(i.attr("data-uk-nav")))}})})},init:function(){var i=this;this.on("click.uikit.nav",this.options.toggle,function(a){a.preventDefault();var n=t.$(this);i.open(n.parent()[0]==i.element[0]?n:n.parent("li"))}),this.find(this.options.lists).each(function(){var a=t.$(this),n=a.parent(),s=n.hasClass("uk-active");a.wrap('<div style="overflow:hidden;height:0;position:relative;"></div>'),n.data("list-container",a.parent()),n.attr("aria-expanded",n.hasClass("uk-open")),s&&i.open(n,!0)})},open:function(a,n){var s=this,e=this.element,o=t.$(a);this.options.multiple||e.children(".uk-open").not(a).each(function(){var i=t.$(this);i.data("list-container")&&i.data("list-container").stop().animate({height:0},function(){t.$(this).parent().removeClass("uk-open")})}),o.toggleClass("uk-open"),o.attr("aria-expanded",o.hasClass("uk-open")),o.data("list-container")&&(n?(o.data("list-container").stop().height(o.hasClass("uk-open")?"auto":0),this.trigger("display.uk.check")):o.data("list-container").stop().animate({height:o.hasClass("uk-open")?i(o.data("list-container").find("ul:first")):0},function(){s.trigger("display.uk.check")}))}})}(UIkit);