/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){if("function"==typeof define&&define.amd&&define("uikit",function(){var n=window.UIkit||t(window,window.jQuery,window.document);return n.load=function(t,e,o,i){var r,s=t.split(","),a=[],u=(i.config&&i.config.uikit&&i.config.uikit.base?i.config.uikit.base:"").replace(/\/+$/g,"");if(!u)throw new Error("Please define base path to UIkit in the requirejs config.");for(r=0;r<s.length;r+=1){var c=s[r].replace(/\./g,"/");a.push(u+"/components/"+c)}e(a,function(){o(n)})},n}),!window.jQuery)throw new Error("UIkit requires jQuery");window&&window.jQuery&&t(window,window.jQuery,window.document)}(function(t,n,e){"use strict";var o={},i=window.UIkit;if(o.version="2.18.0",o.noConflict=function(){return i&&(window.UIkit=i,n.UIkit=i,n.fn.uk=i.fn),o},o.prefix=function(t){return t},o.$=n,o.$doc=o.$(document),o.$win=o.$(window),o.$html=o.$("html"),o.fn=function(t,e){var i=arguments,r=t.match(/^([a-z\-]+)(?:\.([a-z]+))?/i),s=r[1],a=r[2];return o[s]?this.each(function(){var t=n(this),r=t.data(s);r||t.data(s,r=o[s](this,a?void 0:e)),a&&r[a].apply(r,Array.prototype.slice.call(i,1))}):(n.error("UIkit component ["+s+"] does not exist."),this)},o.support={},o.support.transition=function(){var t=function(){var t,n=e.body||e.documentElement,o={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(t in o)if(void 0!==n.style[t])return o[t]}();return t&&{end:t}}(),o.support.animation=function(){var t=function(){var t,n=e.body||e.documentElement,o={WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd oanimationend",animation:"animationend"};for(t in o)if(void 0!==n.style[t])return o[t]}();return t&&{end:t}}(),o.support.requestAnimationFrame=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||function(t){setTimeout(t,1e3/60)},o.support.touch="ontouchstart"in window&&navigator.userAgent.toLowerCase().match(/mobile|tablet/)||t.DocumentTouch&&document instanceof t.DocumentTouch||t.navigator.msPointerEnabled&&t.navigator.msMaxTouchPoints>0||t.navigator.pointerEnabled&&t.navigator.maxTouchPoints>0||!1,o.support.mutationobserver=t.MutationObserver||t.WebKitMutationObserver||null,o.Utils={},o.Utils.str2json=function(t,n){try{return n?JSON.parse(t.replace(/([\$\w]+)\s*:/g,function(t,n){return'"'+n+'":'}).replace(/'([^']+)'/g,function(t,n){return'"'+n+'"'})):new Function("","var json = "+t+"; return JSON.parse(JSON.stringify(json));")()}catch(e){return!1}},o.Utils.debounce=function(t,n,e){var o;return function(){var i=this,r=arguments,s=function(){o=null,e||t.apply(i,r)},a=e&&!o;clearTimeout(o),o=setTimeout(s,n),a&&t.apply(i,r)}},o.Utils.removeCssRules=function(t){var n,e,o,i,r,s,a,u,c,d;t&&setTimeout(function(){try{for(d=document.styleSheets,i=0,a=d.length;a>i;i++){for(o=d[i],e=[],o.cssRules=o.cssRules,n=r=0,u=o.cssRules.length;u>r;n=++r)o.cssRules[n].type===CSSRule.STYLE_RULE&&t.test(o.cssRules[n].selectorText)&&e.unshift(n);for(s=0,c=e.length;c>s;s++)o.deleteRule(e[s])}}catch(f){}},0)},o.Utils.isInView=function(t,e){var i=n(t);if(!i.is(":visible"))return!1;var r=o.$win.scrollLeft(),s=o.$win.scrollTop(),a=i.offset(),u=a.left,c=a.top;return e=n.extend({topoffset:0,leftoffset:0},e),c+i.height()>=s&&c-e.topoffset<=s+o.$win.height()&&u+i.width()>=r&&u-e.leftoffset<=r+o.$win.width()?!0:!1},o.Utils.checkDisplay=function(t,e){var i=o.$("[data-uk-margin], [data-uk-grid-match], [data-uk-grid-margin], [data-uk-check-display]",t||document);return t&&!i.length&&(i=n(t)),i.trigger("display.uk.check"),e&&("string"!=typeof e&&(e='[class*="uk-animation-"]'),i.find(e).each(function(){var t=o.$(this),n=t.attr("class"),e=n.match(/uk\-animation\-(.+)/);t.removeClass(e[0]).width(),t.addClass(e[0])})),i},o.Utils.options=function(t){if(n.isPlainObject(t))return t;var e=t?t.indexOf("{"):-1,i={};if(-1!=e)try{i=o.Utils.str2json(t.substr(e))}catch(r){}return i},o.Utils.animate=function(t,e){var i=n.Deferred();return t=o.$(t),e=e,t.css("display","none").addClass(e).one(o.support.animation.end,function(){t.removeClass(e),i.resolve()}).width(),t.css("display",""),i.promise()},o.Utils.uid=function(t){return(t||"id")+(new Date).getTime()+"RAND"+Math.ceil(1e5*Math.random())},o.Utils.template=function(t,n){for(var e,o,i,r,s=t.replace(/\n/g,"\\n").replace(/\{\{\{\s*(.+?)\s*\}\}\}/g,"{{!$1}}").split(/(\{\{\s*(.+?)\s*\}\})/g),a=0,u=[],c=0;a<s.length;){if(e=s[a],e.match(/\{\{\s*(.+?)\s*\}\}/))switch(a+=1,e=s[a],o=e[0],i=e.substring(e.match(/^(\^|\#|\!|\~|\:)/)?1:0),o){case"~":u.push("for(var $i=0;$i<"+i+".length;$i++) { var $item = "+i+"[$i];"),c++;break;case":":u.push("for(var $key in "+i+") { var $val = "+i+"[$key];"),c++;break;case"#":u.push("if("+i+") {"),c++;break;case"^":u.push("if(!"+i+") {"),c++;break;case"/":u.push("}"),c--;break;case"!":u.push("__ret.push("+i+");");break;default:u.push("__ret.push(escape("+i+"));")}else u.push("__ret.push('"+e.replace(/\'/g,"\\'")+"');");a+=1}return r=new Function("$data",["var __ret = [];","try {","with($data){",c?'__ret = ["Not all blocks are closed correctly."]':u.join(""),"};","}catch(e){__ret = [e.message];}",'return __ret.join("").replace(/\\n\\n/g, "\\n");',"function escape(html) { return String(html).replace(/&/g, '&amp;').replace(/\"/g, '&quot;').replace(/</g, '&lt;').replace(/>/g, '&gt;');}"].join("\n")),n?r(n):r},o.Utils.events={},o.Utils.events.click=o.support.touch?"tap":"click",window.UIkit=o,n.UIkit=o,n.fn.uk=o.fn,o.langdirection="rtl"==o.$html.attr("dir")?"right":"left",o.components={},o.component=function(t,e){var i=function(e,r){var s=this;return this.UIkit=o,this.element=e?o.$(e):null,this.options=n.extend(!0,{},this.defaults,r),this.plugins={},this.element&&this.element.data(t,this),this.init(),(this.options.plugins.length?this.options.plugins:Object.keys(i.plugins)).forEach(function(t){i.plugins[t].init&&(i.plugins[t].init(s),s.plugins[t]=!0)}),this.trigger("init.uk.component",[t,this]),this};return i.plugins={},n.extend(!0,i.prototype,{defaults:{plugins:[]},boot:function(){},init:function(){},on:function(t,n,e){return o.$(this.element||this).on(t,n,e)},one:function(t,n,e){return o.$(this.element||this).one(t,n,e)},off:function(t){return o.$(this.element||this).off(t)},trigger:function(t,n){return o.$(this.element||this).trigger(t,n)},find:function(t){return o.$(this.element?this.element:[]).find(t)},proxy:function(t,n){var e=this;n.split(" ").forEach(function(n){e[n]||(e[n]=function(){return t[n].apply(t,arguments)})})},mixin:function(t,n){var e=this;n.split(" ").forEach(function(n){e[n]||(e[n]=t[n].bind(e))})},option:function(){return 1==arguments.length?this.options[arguments[0]]||void 0:(2==arguments.length&&(this.options[arguments[0]]=arguments[1]),void 0)}},e),this.components[t]=i,this[t]=function(){var e,i;if(arguments.length)switch(arguments.length){case 1:"string"==typeof arguments[0]||arguments[0].nodeType||arguments[0]instanceof jQuery?e=n(arguments[0]):i=arguments[0];break;case 2:e=n(arguments[0]),i=arguments[1]}return e&&e.data(t)?e.data(t):new o.components[t](e,i)},o.domready&&o.component.boot(t),i},o.plugin=function(t,n,e){this.components[t].plugins[n]=e},o.component.boot=function(t){o.components[t].prototype&&o.components[t].prototype.boot&&!o.components[t].booted&&(o.components[t].prototype.boot.apply(o,[]),o.components[t].booted=!0)},o.component.bootComponents=function(){for(var t in o.components)o.component.boot(t)},o.domObservers=[],o.domready=!1,o.ready=function(t){o.domObservers.push(t),o.domready&&t(document)},o.on=function(t,n,e){return t&&t.indexOf("ready.uk.dom")>-1&&o.domready&&n.apply(o.$doc),o.$doc.on(t,n,e)},o.one=function(t,n,e){return t&&t.indexOf("ready.uk.dom")>-1&&o.domready?(n.apply(o.$doc),o.$doc):o.$doc.one(t,n,e)},o.trigger=function(t,n){return o.$doc.trigger(t,n)},o.domObserve=function(t,n){o.support.mutationobserver&&(n=n||function(){},o.$(t).each(function(){var t=this,e=o.$(t);if(!e.data("observer"))try{var i=new o.support.mutationobserver(o.Utils.debounce(function(){n.apply(t,[]),e.trigger("changed.uk.dom")},50));i.observe(t,{childList:!0,subtree:!0}),e.data("observer",i)}catch(r){}}))},o.on("domready.uk.dom",function(){o.domObservers.forEach(function(t){t(document)}),o.domready&&o.Utils.checkDisplay(document)}),n(function(){o.$body=o.$("body"),o.ready(function(){o.domObserve("[data-uk-observe]")}),o.on("changed.uk.dom",function(t){var n=t.target;o.domObservers.forEach(function(t){t(n)}),o.Utils.checkDisplay(n)}),o.trigger("beforeready.uk.dom"),o.component.bootComponents(),setInterval(function(){var t,n={x:window.pageXOffset,y:window.pageYOffset},e=function(){(n.x!=window.pageXOffset||n.y!=window.pageYOffset)&&(t={x:0,y:0},window.pageXOffset!=n.x&&(t.x=window.pageXOffset>n.x?1:-1),window.pageYOffset!=n.y&&(t.y=window.pageYOffset>n.y?1:-1),n={dir:t,x:window.pageXOffset,y:window.pageYOffset},o.$doc.trigger("scrolling.uk.document",[n]))};return o.support.touch&&o.$html.on("touchmove touchend MSPointerMove MSPointerUp pointermove pointerup",e),(n.x||n.y)&&e(),e}(),15),o.trigger("domready.uk.dom"),o.support.touch&&navigator.userAgent.match(/(iPad|iPhone|iPod)/g)&&o.$win.on("load orientationchange resize",o.Utils.debounce(function(){var t=function(){return n(".uk-height-viewport").css("height",window.innerHeight),t};return t()}(),100)),o.trigger("afterready.uk.dom"),o.domready=!0}),o.$html.addClass(o.support.touch?"uk-touch":"uk-notouch"),o.support.touch){var r,s=!1,a="uk-hover",u=".uk-overlay, .uk-overlay-hover, .uk-overlay-toggle, .uk-animation-hover, .uk-has-hover";o.$html.on("touchstart MSPointerDown pointerdown",u,function(){s&&n("."+a).removeClass(a),s=n(this).addClass(a)}).on("touchend MSPointerUp pointerup",function(t){r=n(t.target).parents(u),s&&s.not(r).removeClass(a)})}return o});