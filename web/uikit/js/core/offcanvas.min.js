/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(a){"use strict";var n={x:window.scrollX,y:window.scrollY},t=(a.$win,a.$doc),i=a.$html,o={show:function(o){if(o=a.$(o),o.length){var s=a.$("body"),e=o.find(".uk-offcanvas-bar:first"),f="right"==a.langdirection,r=e.hasClass("uk-offcanvas-bar-flip")?-1:1,c=r*(f?-1:1);n={x:window.pageXOffset,y:window.pageYOffset},o.addClass("uk-active"),s.css({width:window.innerWidth,height:window.innerHeight}).addClass("uk-offcanvas-page"),s.css(f?"margin-right":"margin-left",(f?-1:1)*e.outerWidth()*c).width(),i.css("margin-top",-1*n.y),e.addClass("uk-offcanvas-bar-show"),this._initElement(o),t.trigger("show.uk.offcanvas",[o,e]),o.attr("aria-hidden","false")}},hide:function(t){var o=a.$("body"),s=a.$(".uk-offcanvas.uk-active"),e="right"==a.langdirection,f=s.find(".uk-offcanvas-bar:first"),r=function(){o.removeClass("uk-offcanvas-page").css({width:"",height:"","margin-left":"","margin-right":""}),s.removeClass("uk-active"),f.removeClass("uk-offcanvas-bar-show"),i.css("margin-top",""),window.scrollTo(n.x,n.y),a.$doc.trigger("hide.uk.offcanvas",[s,f]),s.attr("aria-hidden","true")};s.length&&(a.support.transition&&!t?(o.one(a.support.transition.end,function(){r()}).css(e?"margin-right":"margin-left",""),setTimeout(function(){f.removeClass("uk-offcanvas-bar-show")},0)):r())},_initElement:function(n){n.data("OffcanvasInit")||(n.on("click.uk.offcanvas swipeRight.uk.offcanvas swipeLeft.uk.offcanvas",function(n){var t=a.$(n.target);if(!n.type.match(/swipe/)&&!t.hasClass("uk-offcanvas-close")){if(t.hasClass("uk-offcanvas-bar"))return;if(t.parents(".uk-offcanvas-bar:first").length)return}n.stopImmediatePropagation(),o.hide()}),n.on("click","a[href^='#']",function(){var n=a.$(this),t=n.attr("href");"#"!=t&&(a.$doc.one("hide.uk.offcanvas",function(){var n=a.$(t);n.length||(n=a.$('[name="'+t.replace("#","")+'"]')),a.Utils.scrollToElement&&n.length?a.Utils.scrollToElement(n):window.location.href=t}),o.hide())}),n.data("OffcanvasInit",!0))}};a.component("offcanvasTrigger",{boot:function(){i.on("click.offcanvas.uikit","[data-uk-offcanvas]",function(n){n.preventDefault();var t=a.$(this);if(!t.data("offcanvasTrigger")){{a.offcanvasTrigger(t,a.Utils.options(t.attr("data-uk-offcanvas")))}t.trigger("click")}}),i.on("keydown.uk.offcanvas",function(a){27===a.keyCode&&o.hide()})},init:function(){var n=this;this.options=a.$.extend({target:n.element.is("a")?n.element.attr("href"):!1},this.options),this.on("click",function(a){a.preventDefault(),o.show(n.options.target)})}}),a.offcanvas=o}(UIkit);