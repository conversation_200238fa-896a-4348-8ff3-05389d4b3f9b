/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(e){function t(e,t,n,o){return Math.abs(e-t)>=Math.abs(n-o)?e-t>0?"Left":"Right":n-o>0?"Up":"Down"}function n(){p=null,g.last&&(g.el.trigger("longTap"),g={})}function o(){p&&clearTimeout(p),p=null}function i(){a&&clearTimeout(a),l&&clearTimeout(l),u&&clearTimeout(u),p&&clearTimeout(p),a=l=u=p=null,g={}}function r(e){return e.pointerType==e.MSPOINTER_TYPE_TOUCH&&e.isPrimary}if(!e.fn.swipeLeft){var a,l,u,p,c,g={},s=750;e(function(){var y,w,v,f=0,M=0;"MSGesture"in window&&(c=new MSGesture,c.target=document.body),e(document).on("MSGestureEnd gestureend",function(e){var t=e.originalEvent.velocityX>1?"Right":e.originalEvent.velocityX<-1?"Left":e.originalEvent.velocityY>1?"Down":e.originalEvent.velocityY<-1?"Up":null;t&&(g.el.trigger("swipe"),g.el.trigger("swipe"+t))}).on("touchstart MSPointerDown pointerdown",function(t){("MSPointerDown"!=t.type||r(t.originalEvent))&&(v="MSPointerDown"==t.type||"pointerdown"==t.type?t:t.originalEvent.touches[0],y=Date.now(),w=y-(g.last||y),g.el=e("tagName"in v.target?v.target:v.target.parentNode),a&&clearTimeout(a),g.x1=v.pageX,g.y1=v.pageY,w>0&&250>=w&&(g.isDoubleTap=!0),g.last=y,p=setTimeout(n,s),!c||"MSPointerDown"!=t.type&&"pointerdown"!=t.type&&"touchstart"!=t.type||c.addPointer(t.originalEvent.pointerId))}).on("touchmove MSPointerMove pointermove",function(e){("MSPointerMove"!=e.type||r(e.originalEvent))&&(v="MSPointerMove"==e.type||"pointermove"==e.type?e:e.originalEvent.touches[0],o(),g.x2=v.pageX,g.y2=v.pageY,f+=Math.abs(g.x1-g.x2),M+=Math.abs(g.y1-g.y2))}).on("touchend MSPointerUp pointerup",function(n){("MSPointerUp"!=n.type||r(n.originalEvent))&&(o(),g.x2&&Math.abs(g.x1-g.x2)>30||g.y2&&Math.abs(g.y1-g.y2)>30?u=setTimeout(function(){g.el.trigger("swipe"),g.el.trigger("swipe"+t(g.x1,g.x2,g.y1,g.y2)),g={}},0):"last"in g&&(isNaN(f)||30>f&&30>M?l=setTimeout(function(){var t=e.Event("tap");t.cancelTouch=i,g.el.trigger(t),g.isDoubleTap?(g.el.trigger("doubleTap"),g={}):a=setTimeout(function(){a=null,g.el.trigger("singleTap"),g={}},250)},0):g={},f=M=0))}).on("touchcancel MSPointerCancel",i),e(window).on("scroll",i)}),["swipe","swipeLeft","swipeRight","swipeUp","swipeDown","doubleTap","tap","singleTap","longTap"].forEach(function(t){e.fn[t]=function(n){return e(this).on(t,n)}})}}(jQuery);