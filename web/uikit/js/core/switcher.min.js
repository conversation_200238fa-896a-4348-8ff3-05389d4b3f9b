/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){"use strict";function i(i,n,e){var a,s=t.$.Deferred(),o=i,r=i;return e[0]===n[0]?(s.resolve(),s.promise()):("object"==typeof i&&(o=i[0],r=i[1]||i[0]),a=function(){n&&n.hide().removeClass("uk-active "+r+" uk-animation-reverse"),e.addClass(o).one(t.support.animation.end,function(){e.removeClass(""+o).css({opacity:"",display:""}),s.resolve(),n&&n.css({opacity:"",display:""})}.bind(this)).show()},e.css("animation-duration",this.options.duration+"ms"),n&&n.length?(n.css("animation-duration",this.options.duration+"ms"),n.css("display","none").addClass(r+" uk-animation-reverse").one(t.support.animation.end,function(){a()}.bind(this)).css("display","")):(e.addClass("uk-active"),a()),s.promise())}var n;t.component("switcher",{defaults:{connect:!1,toggle:">*",active:0,animation:!1,duration:200},animating:!1,boot:function(){t.ready(function(i){t.$("[data-uk-switcher]",i).each(function(){var i=t.$(this);if(!i.data("switcher")){t.switcher(i,t.Utils.options(i.attr("data-uk-switcher")))}})})},init:function(){var i=this;if(this.on("click.uikit.switcher",this.options.toggle,function(t){t.preventDefault(),i.show(this)}),this.options.connect){this.connect=t.$(this.options.connect),this.connect.find(".uk-active").removeClass(".uk-active"),this.connect.length&&(this.connect.children().attr("aria-hidden","true"),this.connect.on("click","[data-uk-switcher-item]",function(n){n.preventDefault();var e=t.$(this).attr("data-uk-switcher-item");if(i.index!=e)switch(e){case"next":case"previous":i.show(i.index+("next"==e?1:-1));break;default:i.show(parseInt(e,10))}}).on("swipeRight swipeLeft",function(t){t.preventDefault(),i.show(i.index+("swipeLeft"==t.type?1:-1))}));var n=this.find(this.options.toggle),e=n.filter(".uk-active");if(e.length)this.show(e,!1);else{if(this.options.active===!1)return;e=n.eq(this.options.active),this.show(e.length?e:n.eq(0),!1)}n.not(e).attr("aria-expanded","false"),e.attr("aria-expanded","true"),this.on("changed.uk.dom",function(){i.connect=t.$(i.options.connect)})}},show:function(e,a){if(!this.animating){if(isNaN(e))e=t.$(e);else{var s=this.find(this.options.toggle);e=0>e?s.length-1:e,e=s.eq(s[e]?e:0)}var o=this,s=this.find(this.options.toggle),r=t.$(e),c=n[this.options.animation]||function(t,e){if(!o.options.animation)return n.none.apply(o);var a=o.options.animation.split(",");return 1==a.length&&(a[1]=a[0]),a[0]=a[0].trim(),a[1]=a[1].trim(),i.apply(o,[a,t,e])};a!==!1&&t.support.animation||(c=n.none),r.hasClass("uk-disabled")||(s.attr("aria-expanded","false"),r.attr("aria-expanded","true"),s.filter(".uk-active").removeClass("uk-active"),r.addClass("uk-active"),this.options.connect&&this.connect.length&&(this.index=this.find(this.options.toggle).index(r),-1==this.index&&(this.index=0),this.connect.each(function(){var i=t.$(this),n=t.$(i.children()),e=t.$(n.filter(".uk-active")),a=t.$(n.eq(o.index));o.animating=!0,c.apply(o,[e,a]).then(function(){e.removeClass("uk-active"),a.addClass("uk-active"),e.attr("aria-hidden","true"),a.attr("aria-hidden","false"),t.Utils.checkDisplay(a,!0),o.animating=!1})})),this.trigger("show.uk.switcher",[r]))}}}),n={none:function(){var i=t.$.Deferred();return i.resolve(),i.promise()},fade:function(t,n){return i.apply(this,["uk-animation-fade",t,n])},"slide-bottom":function(t,n){return i.apply(this,["uk-animation-slide-bottom",t,n])},"slide-top":function(t,n){return i.apply(this,["uk-animation-slide-top",t,n])},"slide-vertical":function(t,n){var e=["uk-animation-slide-top","uk-animation-slide-bottom"];return t&&t.index()>n.index()&&e.reverse(),i.apply(this,[e,t,n])},"slide-left":function(t,n){return i.apply(this,["uk-animation-slide-left",t,n])},"slide-right":function(t,n){return i.apply(this,["uk-animation-slide-right",t,n])},"slide-horizontal":function(t,n){var e=["uk-animation-slide-right","uk-animation-slide-left"];return t&&t.index()>n.index()&&e.reverse(),i.apply(this,[e,t,n])},scale:function(t,n){return i.apply(this,["uk-animation-scale-up",t,n])}},t.switcher.animations=n}(UIkit);