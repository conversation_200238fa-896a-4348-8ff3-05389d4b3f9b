/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){"use strict";var i=[];t.component("gridMatchHeight",{defaults:{target:!1,row:!0},boot:function(){t.ready(function(i){t.$("[data-uk-grid-match]",i).each(function(){var i,n=t.$(this);n.data("gridMatchHeight")||(i=t.gridMatchHeight(n,t.Utils.options(n.attr("data-uk-grid-match"))))})})},init:function(){var n=this;this.columns=this.element.children(),this.elements=this.options.target?this.find(this.options.target):this.columns,this.columns.length&&(t.$win.on("load resize orientationchange",function(){var i=function(){n.match()};return t.$(function(){i()}),t.Utils.debounce(i,50)}()),t.$html.on("changed.uk.dom",function(){n.columns=n.element.children(),n.elements=n.options.target?n.find(n.options.target):n.columns,n.match()}),this.on("display.uk.check",function(){this.element.is(":visible")&&this.match()}.bind(this)),i.push(this))},match:function(){var i=this.columns.filter(":visible:first");if(i.length){var n=Math.ceil(100*parseFloat(i.css("width"))/parseFloat(i.parent().css("width")))>=100;return n?this.revert():t.Utils.matchHeights(this.elements,this.options),this}},revert:function(){return this.elements.css("min-height",""),this}}),t.component("gridMargin",{defaults:{cls:"uk-grid-margin"},boot:function(){t.ready(function(i){t.$("[data-uk-grid-margin]",i).each(function(){var i,n=t.$(this);n.data("gridMargin")||(i=t.gridMargin(n,t.Utils.options(n.attr("data-uk-grid-margin"))))})})},init:function(){t.stackMargin(this.element,this.options)}}),t.Utils.matchHeights=function(i,n){i=t.$(i).css("min-height",""),n=t.$.extend({row:!0},n);var e=function(i){if(!(i.length<2)){var n=0;i.each(function(){n=Math.max(n,t.$(this).outerHeight())}).each(function(){var i=t.$(this),e=n-(i.outerHeight()-i.height());i.css("min-height",e+"px")})}};n.row?(i.first().width(),setTimeout(function(){var n=!1,s=[];i.each(function(){var i=t.$(this),h=i.offset().top;h!=n&&s.length&&(e(t.$(s)),s=[],h=i.offset().top),s.push(i),n=h}),s.length&&e(t.$(s))},0)):e(i)}}(UIkit);