/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){"use strict";var i=[];t.component("stackMargin",{defaults:{cls:"uk-margin-small-top"},boot:function(){t.ready(function(i){t.$("[data-uk-margin]",i).each(function(){var i,n=t.$(this);n.data("stackMargin")||(i=t.stackMargin(n,t.Utils.options(n.attr("data-uk-margin"))))})})},init:function(){var n=this;this.columns=this.element.children(),this.columns.length&&(t.$win.on("resize orientationchange",function(){var i=function(){n.process()};return t.$(function(){i(),t.$win.on("load",i)}),t.Utils.debounce(i,20)}()),t.$html.on("changed.uk.dom",function(){n.columns=n.element.children(),n.process()}),this.on("display.uk.check",function(){n.columns=n.element.children(),this.element.is(":visible")&&this.process()}.bind(this)),i.push(this))},process:function(){return t.Utils.stackMargin(this.columns,this.options),this},revert:function(){return this.columns.removeClass(this.options.cls),this}}),t.ready(function(){var i=[],n=function(){i.forEach(function(t){if(t.is(":visible")){var i=t.parent().width(),n=t.data("width"),s=i/n,e=Math.floor(s*t.data("height"));t.css({height:n>i?e:t.data("height")})}})};return t.$win.on("resize",t.Utils.debounce(n,15)),function(s){t.$("iframe.uk-responsive-width",s).each(function(){var n=t.$(this);!n.data("responsive")&&n.attr("width")&&n.attr("height")&&(n.data("width",n.attr("width")),n.data("height",n.attr("height")),n.data("responsive",!0),i.push(n))}),n()}}()),t.Utils.stackMargin=function(i,n){n=t.$.extend({cls:"uk-margin-small-top"},n),n.cls=n.cls,i=t.$(i).removeClass(n.cls);var s=!1,e=i.filter(":visible:first"),a=e.length?e.position().top+e.outerHeight()-1:!1;a!==!1&&i.each(function(){var i=t.$(this);i.is(":visible")&&(s?i.addClass(n.cls):i.position().top>=a&&(s=i.addClass(n.cls)))})}}(UIkit);