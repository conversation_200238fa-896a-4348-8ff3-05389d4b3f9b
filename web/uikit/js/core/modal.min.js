/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){"use strict";function i(i,e){return e?("object"==typeof i?(i=i instanceof jQuery?i:t.$(i),i.parent().length&&(e.persist=i,e.persist.data("modalPersistParent",i.parent()))):i="string"==typeof i||"number"==typeof i?t.$("<div></div>").html(i):t.$("<div></div>").html("UIkit.modal Error: Unsupported data type: "+typeof i),i.appendTo(e.element.find(".uk-modal-dialog")),e):void 0}var e,o=!1,n=t.$html;t.component("modal",{defaults:{keyboard:!0,bgclose:!0,minScrollHeight:150,center:!1},scrollable:!1,transition:!1,init:function(){e||(e=t.$("body"));var i=this;this.transition=t.support.transition,this.paddingdir="padding-"+("left"==t.langdirection?"right":"left"),this.dialog=this.find(".uk-modal-dialog"),this.element.attr("aria-hidden",this.element.hasClass("uk-open")),this.on("click",".uk-modal-close",function(t){t.preventDefault(),i.hide()}).on("click",function(e){var o=t.$(e.target);o[0]==i.element[0]&&i.options.bgclose&&i.hide()})},toggle:function(){return this[this.isActive()?"hide":"show"]()},show:function(){if(!this.isActive())return o&&o.hide(!0),this.element.removeClass("uk-open").show(),this.resize(),o=this,n.addClass("uk-modal-page").height(),this.element.addClass("uk-open"),this.element.attr("aria-hidden","false"),this.element.trigger("show.uk.modal"),t.Utils.checkDisplay(this.dialog,!0),this},hide:function(i){if(this.isActive()){if(!i&&t.support.transition){var e=this;this.one(t.support.transition.end,function(){e._hide()}).removeClass("uk-open")}else this._hide();return this}},resize:function(){var t=e.width();if(this.scrollbarwidth=window.innerWidth-t,e.css(this.paddingdir,this.scrollbarwidth),this.element.css("overflow-y",this.scrollbarwidth?"scroll":"auto"),!this.updateScrollable()&&this.options.center){var i=this.dialog.outerHeight(),o=parseInt(this.dialog.css("margin-top"),10)+parseInt(this.dialog.css("margin-bottom"),10);i+o<window.innerHeight?this.dialog.css({top:window.innerHeight/2-i/2-o}):this.dialog.css({top:""})}},updateScrollable:function(){var t=this.dialog.find(".uk-overflow-container:visible:first");if(t.length){t.css("height",0);var i=Math.abs(parseInt(this.dialog.css("margin-top"),10)),e=this.dialog.outerHeight(),o=window.innerHeight,n=o-2*(20>i?20:i)-e;return t.css("height",n<this.options.minScrollHeight?"":n),!0}return!1},_hide:function(){this.element.hide().removeClass("uk-open"),this.element.attr("aria-hidden","true"),n.removeClass("uk-modal-page"),e.css(this.paddingdir,""),o===this&&(o=!1),this.trigger("hide.uk.modal")},isActive:function(){return o==this}}),t.component("modalTrigger",{boot:function(){t.$html.on("click.modal.uikit","[data-uk-modal]",function(i){var e=t.$(this);if(e.is("a")&&i.preventDefault(),!e.data("modalTrigger")){var o=t.modalTrigger(e,t.Utils.options(e.attr("data-uk-modal")));o.show()}}),t.$html.on("keydown.modal.uikit",function(t){o&&27===t.keyCode&&o.options.keyboard&&(t.preventDefault(),o.hide())}),t.$win.on("resize orientationchange",t.Utils.debounce(function(){o&&o.resize()},150))},init:function(){var i=this;this.options=t.$.extend({target:i.element.is("a")?i.element.attr("href"):!1},this.options),this.modal=t.modal(this.options.target,this.options),this.on("click",function(t){t.preventDefault(),i.show()}),this.proxy(this.modal,"show hide isActive")}}),t.modal.dialog=function(e,o){var n=t.modal(t.$(t.modal.dialog.template).appendTo("body"),o);return n.on("hide.uk.modal",function(){n.persist&&(n.persist.appendTo(n.persist.data("modalPersistParent")),n.persist=!1),n.element.remove()}),i(e,n),n},t.modal.dialog.template='<div class="uk-modal"><div class="uk-modal-dialog" style="min-height:0;"></div></div>',t.modal.alert=function(i,e){t.modal.dialog(['<div class="uk-margin uk-modal-content">'+String(i)+"</div>",'<div class="uk-modal-footer uk-text-right"><button class="uk-button uk-button-primary uk-modal-close">Ok</button></div>'].join(""),t.$.extend({bgclose:!1,keyboard:!1},e)).show()},t.modal.confirm=function(i,e,o){e=t.$.isFunction(e)?e:function(){};var n=t.modal.dialog(['<div class="uk-margin uk-modal-content">'+String(i)+"</div>",'<div class="uk-modal-footer uk-text-right"><button class="uk-button uk-button-primary js-modal-confirm">Ok</button> <button class="uk-button uk-modal-close">Cancel</button></div>'].join(""),t.$.extend({bgclose:!1,keyboard:!1},o));n.element.find(".js-modal-confirm").on("click",function(){e(),n.hide()}),n.show()}}(UIkit);