/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){var i;window.UIkit&&(i=t(UIkit)),"function"==typeof define&&define.amd&&define("uikit-sticky",["uikit"],function(){return i||t(UIkit)})}(function(t){"use strict";function i(){var i=arguments.length?arguments:o;if(i.length&&!(e.scrollTop()<0))for(var s,a,r,p,h=e.scrollTop(),c=n.height(),l=e.height(),u=c-l,d=h>u?u-h:0,m=0;m<i.length;m++)if(p=i[m],p.element.is(":visible")&&!p.animate){if(p.check()){if(p.options.top<0?s=0:(r=p.element.outerHeight(),s=c-r-p.options.top-p.options.bottom-h-d,s=0>s?s+p.options.top:p.options.top),p.boundary&&p.boundary.length){var f=p.boundary.position().top;a=p.boundtoparent?c-(f+p.boundary.outerHeight())+parseInt(p.boundary.css("padding-bottom")):c-f-parseInt(p.boundary.css("margin-top")),s=h+r>c-a-(p.options.top<0?0:p.options.top)?c-a-(h+r):s}if(p.currentTop!=s){if(p.element.css({position:"fixed",top:s,width:"undefined"!=typeof p.getWidthFrom?t.$(p.getWidthFrom).width():p.element.width(),left:p.wrapper.offset().left}),!p.init&&(p.element.addClass(p.options.clsinit),location.hash&&h>0&&p.options.target)){var g=t.$(location.hash);g.length&&setTimeout(function(t,i){return function(){i.element.width();var e=t.offset(),n=e.top+t.outerHeight(),o=i.element.offset(),s=i.element.outerHeight(),a=o.top+s;o.top<n&&e.top<a&&(h=e.top-s-i.options.target,window.scrollTo(0,h))}}(g,p),0)}p.element.addClass(p.options.clsactive),p.element.css("margin",""),p.options.animation&&p.init&&p.element.addClass(p.options.animation),p.currentTop=s}}else null!==p.currentTop&&p.reset();p.init=!0}}var e=t.$win,n=t.$doc,o=[];return t.component("sticky",{defaults:{top:0,bottom:0,animation:"",clsinit:"uk-sticky-init",clsactive:"uk-active",getWidthFrom:"",boundary:!1,media:!1,target:!1,disabled:!1},boot:function(){t.$doc.on("scrolling.uk.document",function(){i()}),t.$win.on("resize orientationchange",t.Utils.debounce(function(){if(o.length){for(var t=0;t<o.length;t++)o[t].reset(!0),o[t].self.computeWrapper();i()}},100)),t.ready(function(e){setTimeout(function(){t.$("[data-uk-sticky]",e).each(function(){var i=t.$(this);i.data("sticky")||t.sticky(i,t.Utils.options(i.attr("data-uk-sticky")))}),i()},0)})},init:function(){var i,s=t.$('<div class="uk-sticky-placeholder"></div>'),a=this.options.boundary;this.wrapper=this.element.css("margin",0).wrap(s).parent(),this.computeWrapper(),a&&(a===!0?(a=this.wrapper.parent(),i=!0):"string"==typeof a&&(a=t.$(a))),this.sticky={self:this,options:this.options,element:this.element,currentTop:null,wrapper:this.wrapper,init:!1,getWidthFrom:this.options.getWidthFrom||this.wrapper,boundary:a,boundtoparent:i,reset:function(i){var e=function(){this.element.css({position:"",top:"",width:"",left:"",margin:"0"}),this.element.removeClass([this.options.animation,"uk-animation-reverse",this.options.clsactive].join(" ")),this.currentTop=null,this.animate=!1}.bind(this);!i&&this.options.animation&&t.support.animation?(this.animate=!0,this.element.removeClass(this.options.animation).one(t.support.animation.end,function(){e()}).width(),this.element.addClass(this.options.animation+" uk-animation-reverse")):e()},check:function(){if(this.options.disabled)return!1;if(this.options.media)switch(typeof this.options.media){case"number":if(window.innerWidth<this.options.media)return!1;break;case"string":if(window.matchMedia&&!window.matchMedia(this.options.media).matches)return!1}var t=e.scrollTop(),i=n.height(),o=i-window.innerHeight,s=t>o?o-t:0,a=this.wrapper.offset().top,r=a-this.options.top-s;return t>=r}},o.push(this.sticky)},update:function(){i(this.sticky)},enable:function(){this.options.disabled=!1,this.update()},disable:function(t){this.options.disabled=!0,this.sticky.reset(t)},computeWrapper:function(){this.wrapper.css({height:"absolute"!=this.element.css("position")?this.element.outerHeight():"","float":"none"!=this.element.css("float")?this.element.css("float"):"",margin:this.element.css("margin")})}}),t.sticky});