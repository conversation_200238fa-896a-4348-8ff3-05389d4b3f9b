/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(i){var t;window.UIkit&&(t=i(UIkit)),"function"==typeof define&&define.amd&&define(["uikit-lightbox"],function(){return t||i(UIkit)})}(function(i){"use strict";function t(t){return o?(o.lightbox=t,o):(o=i.$(['<div class="uk-modal">','<div class="uk-modal-dialog uk-modal-dialog-lightbox uk-slidenav-position" style="margin-left:auto;margin-right:auto;width:200px;height:200px;top:'+Math.abs(window.innerHeight/2-200)+'px;">','<a href="#" class="uk-modal-close uk-close uk-close-alt"></a>','<div class="uk-lightbox-content"></div>','<div class="uk-modal-spinner uk-hidden"></div>',"</div>","</div>"].join("")).appendTo("body"),o.dialog=o.find(".uk-modal-dialog:first"),o.content=o.find(".uk-lightbox-content:first"),o.loader=o.find(".uk-modal-spinner:first"),o.closer=o.find(".uk-close.uk-close-alt"),o.modal=i.modal(o),o.on("swipeRight swipeLeft",function(i){o.lightbox["swipeLeft"==i.type?"next":"previous"]()}).on("click","[data-lightbox-previous], [data-lightbox-next]",function(t){t.preventDefault(),o.lightbox[i.$(this).is("[data-lightbox-next]")?"next":"previous"]()}),o.on("hide.uk.modal",function(){o.content.html("")}),i.$win.on("load resize orientationchange",i.Utils.debounce(function(){o.is(":visible")&&o.lightbox.fitSize()}.bind(this),100)),o.lightbox=t,o)}var o,e={};return i.component("lightbox",{defaults:{group:!1,duration:400,keyboard:!0},index:0,items:!1,boot:function(){i.$html.on("click","[data-uk-lightbox]",function(t){t.preventDefault();var o=i.$(this);o.data("lightbox")||i.lightbox(o,i.Utils.options(o.attr("data-uk-lightbox"))),o.data("lightbox").show(o)}),i.$doc.on("keyup",function(i){if(o&&o.is(":visible")&&o.lightbox.options.keyboard)switch(i.preventDefault(),i.keyCode){case 37:o.lightbox.previous();break;case 39:o.lightbox.next()}})},init:function(){var t=[];if(this.index=0,this.siblings=[],this.element&&this.element.length){var o=this.options.group?i.$(['[data-uk-lightbox*="'+this.options.group+'"]',"[data-uk-lightbox*='"+this.options.group+"']"].join(",")):this.element;o.each(function(){var o=i.$(this);t.push({source:o.attr("href"),title:o.attr("title"),type:o.attr("data-lightbox-type")||"auto",link:o})}),this.index=o.index(this.element),this.siblings=t}else this.options.group&&this.options.group.length&&(this.siblings=this.options.group);this.trigger("lightbox-init",[this])},show:function(o){this.modal=t(this),this.modal.dialog.stop(),this.modal.content.stop();var e,n,s=this,h=i.$.Deferred();o=o||0,"object"==typeof o&&this.siblings.forEach(function(i,t){o[0]===i.link[0]&&(o=t)}),0>o?o=this.siblings.length-o:this.siblings[o]||(o=0),n=this.siblings[o],e={lightbox:s,source:n.source,type:n.type,index:o,promise:h,title:n.title,item:n,meta:{content:"",width:null,height:null}},this.index=o,this.modal.content.empty(),this.modal.is(":visible")||(this.modal.content.css({width:"",height:""}).empty(),this.modal.modal.show()),this.modal.loader.removeClass("uk-hidden"),h.promise().done(function(){s.data=e,s.fitSize(e)}).fail(function(){alert("Loading resource failed!")}),s.trigger("showitem.uk.lightbox",[e])},fitSize:function(){var t=this,o=this.data,e=this.modal.dialog.outerWidth()-this.modal.dialog.width(),n=parseInt(this.modal.dialog.css("margin-top"),10),s=parseInt(this.modal.dialog.css("margin-bottom"),10),h=n+s,a=o.meta.content,d=t.options.duration;this.siblings.length>1&&(a=[a,'<a href="#" class="uk-slidenav uk-slidenav-contrast uk-slidenav-previous uk-hidden-touch" data-lightbox-previous></a>','<a href="#" class="uk-slidenav uk-slidenav-contrast uk-slidenav-next uk-hidden-touch" data-lightbox-next></a>'].join(""));var l,r,u=i.$("<div>&nbsp;</div>").css({opacity:0,position:"absolute",top:0,left:0,width:"100%","max-width":t.modal.dialog.css("max-width"),padding:t.modal.dialog.css("padding"),margin:t.modal.dialog.css("margin")}),c=o.meta.width,g=o.meta.height;u.appendTo("body").width(),l=u.width(),r=window.innerHeight-h,u.remove(),this.modal.dialog.find(".uk-modal-caption").remove(),o.title&&(this.modal.dialog.append('<div class="uk-modal-caption">'+o.title+"</div>"),r-=this.modal.dialog.find(".uk-modal-caption").outerHeight()),l<o.meta.width&&(g=Math.floor(g*(l/c)),c=l),g>r&&(g=Math.floor(r),c=Math.ceil(o.meta.width*(r/o.meta.height))),this.modal.content.css("opacity",0).width(c).html(a),"iframe"==o.type&&this.modal.content.find("iframe:first").height(g);var m=g+e,p=Math.floor(window.innerHeight/2-m/2)-h;0>p&&(p=0),this.modal.closer.addClass("uk-hidden"),t.modal.data("mwidth")==c&&t.modal.data("mheight")==g&&(d=0),this.modal.dialog.animate({width:c+e,height:g+e,top:p},d,"swing",function(){t.modal.loader.addClass("uk-hidden"),t.modal.content.css({width:""}).animate({opacity:1},function(){t.modal.closer.removeClass("uk-hidden")}),t.modal.data({mwidth:c,mheight:g})})},next:function(){this.show(this.siblings[this.index+1]?this.index+1:0)},previous:function(){this.show(this.siblings[this.index-1]?this.index-1:this.siblings.length-1)}}),i.plugin("lightbox","image",{init:function(i){i.on("showitem.uk.lightbox",function(i,t){if("image"==t.type||t.source&&t.source.match(/\.(jpg|jpeg|png|gif|svg)$/)){var o=function(i,o,e){t.meta={content:'<img class="uk-responsive-width" width="'+o+'" height="'+e+'" src ="'+i+'">',width:o,height:e},t.type="image",t.promise.resolve()};if(e[t.source])o(t.source,e[t.source].width,e[t.source].height);else{var n=new Image;n.onerror=function(){t.promise.reject("Loading image failed")},n.onload=function(){e[t.source]={width:n.width,height:n.height},o(t.source,e[t.source].width,e[t.source].height)},n.src=t.source}}})}}),i.plugin("lightbox","youtube",{init:function(i){var t=/(\/\/.*?youtube\.[a-z]+)\/watch\?v=([^&]+)&?(.*)/,o=/youtu\.be\/(.*)/;i.on("showitem.uk.lightbox",function(i,n){var s,h,a=function(i,t,o){n.meta={content:'<iframe src="//www.youtube.com/embed/'+i+'" width="'+t+'" height="'+o+'" style="max-width:100%;"></iframe>',width:t,height:o},n.type="iframe",n.promise.resolve()};if((h=n.source.match(t))&&(s=h[2]),(h=n.source.match(o))&&(s=h[1]),s){if(e[s])a(s,e[s].width,e[s].height);else{var d=new Image;d.onerror=function(){e[s]={width:640,height:320},a(s,e[s].width,e[s].height)},d.onload=function(){e[s]={width:d.width,height:d.height},a(s,d.width,d.height)},d.src="//img.youtube.com/vi/"+s+"/0.jpg"}i.stopImmediatePropagation()}})}}),i.plugin("lightbox","vimeo",{init:function(t){var o,n=/(\/\/.*?)vimeo\.[a-z]+\/([0-9]+).*?/;t.on("showitem.uk.lightbox",function(t,s){var h,a=function(i,t,o){s.meta={content:'<iframe src="//player.vimeo.com/video/'+i+'" width="'+t+'" height="'+o+'" style="width:100%;box-sizing:border-box;"></iframe>',width:t,height:o},s.type="iframe",s.promise.resolve()};(o=s.source.match(n))&&(h=o[2],e[h]?a(h,e[h].width,e[h].height):i.$.ajax({type:"GET",url:"http://vimeo.com/api/oembed.json?url="+encodeURI(s.source),jsonp:"callback",dataType:"jsonp",success:function(i){e[h]={width:i.width,height:i.height},a(h,e[h].width,e[h].height)}}),t.stopImmediatePropagation())})}}),i.plugin("lightbox","video",{init:function(t){t.on("showitem.uk.lightbox",function(t,o){var n=function(i,t,e){o.meta={content:'<video class="uk-responsive-width" src="'+i+'" width="'+t+'" height="'+e+'" controls></video>',width:t,height:e},o.type="video",o.promise.resolve()};if("video"==o.type||o.source.match(/\.(mp4|webm|ogv)$/))if(e[o.source])n(o.source,e[o.source].width,e[o.source].height);else var s=i.$('<video style="position:fixed;visibility:hidden;top:-10000px;"></video>').attr("src",o.source).appendTo("body"),h=setInterval(function(){s[0].videoWidth&&(clearInterval(h),e[o.source]={width:s[0].videoWidth,height:s[0].videoHeight},n(o.source,e[o.source].width,e[o.source].height),s.remove())},20)})}}),i.lightbox.create=function(t,o){if(t){var e,n=[];return t.forEach(function(t){n.push(i.$.extend({source:"",title:"",type:"auto",link:!1},"string"==typeof t?{source:t}:t))}),e=i.lightbox(i.$.extend({},o,{group:n}))}},i.lightbox});