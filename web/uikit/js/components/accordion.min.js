/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){var i;window.UIkit&&(i=t(UIkit)),"function"==typeof define&&define.amd&&define("uikit-accordion",["uikit"],function(){return i||t(UIkit)})}(function(t){"use strict";function i(i){var a=t.$(i),o="auto";if(a.is(":visible"))o=a.outerHeight();else{var e={position:a.css("position"),visibility:a.css("visibility"),display:a.css("display")};o=a.css({position:"absolute",visibility:"hidden",display:"block"}).outerHeight(),a.css(e)}return o}return t.component("accordion",{defaults:{showfirst:!0,collapse:!0,animate:!0,easing:"swing",duration:300,toggle:".uk-accordion-title",containers:".uk-accordion-content",clsactive:"uk-active"},boot:function(){t.ready(function(i){setTimeout(function(){t.$("[data-uk-accordion]",i).each(function(){var i=t.$(this);i.data("accordion")||t.accordion(i,t.Utils.options(i.attr("data-uk-accordion")))})},0)})},init:function(){var i=this;this.element.on("click.uikit.accordion",this.options.toggle,function(a){a.preventDefault(),i.toggleItem(t.$(this).data("wrapper"),i.options.animate,i.options.collapse)}),this.update(),this.options.showfirst&&this.toggleItem(this.toggle.eq(0).data("wrapper"),!1,!1)},toggleItem:function(a,o,e){var n=this;a.data("toggle").toggleClass(this.options.clsactive);var s=a.data("toggle").hasClass(this.options.clsactive);e&&(this.toggle.not(a.data("toggle")).removeClass(this.options.clsactive),this.content.not(a.data("content")).parent().stop().animate({height:0},{easing:this.options.easing,duration:o?this.options.duration:0}).attr("aria-expanded","false")),o?a.stop().animate({height:s?i(a.data("content")):0},{easing:this.options.easing,duration:this.options.duration,complete:function(){s&&(t.Utils.checkDisplay(a.data("content")),a.height("auto")),n.trigger("display.uk.check")}}):(a.stop().height(s?"auto":0),s&&t.Utils.checkDisplay(a.data("content")),this.trigger("display.uk.check")),a.attr("aria-expanded",s),this.element.trigger("toggle.uk.accordion",[s,a.data("toggle"),a.data("content")])},update:function(){var i,a,o,e=this;this.toggle=this.find(this.options.toggle),this.content=this.find(this.options.containers),this.content.each(function(n){i=t.$(this),i.parent().data("wrapper")?a=i.parent():(a=t.$(this).wrap('<div data-wrapper="true" style="overflow:hidden;height:0;position:relative;"></div>').parent(),a.attr("aria-expanded","false")),o=e.toggle.eq(n),a.data("toggle",o),a.data("content",i),o.data("wrapper",a),i.data("wrapper",a)}),this.element.trigger("update.uk.accordion",[this])}}),t.accordion});