/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(e){var t;window.UIkit&&(t=e(UIkit)),"function"==typeof define&&define.amd&&define("uikit-datepicker",["uikit"],function(){return t||e(UIkit)})}(function(e){"use strict";var t,n,a=!1;return e.component("datepicker",{defaults:{mobile:!1,weekstart:1,i18n:{months:["January","February","March","April","May","June","July","August","September","October","November","December"],weekdays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},format:"DD.MM.YYYY",offsettop:5,maxDate:!1,minDate:!1,template:function(t,a){var s,i,r,o="";if(a.maxDate!==!1&&(s=isNaN(a.maxDate)?n(a.maxDate,a.format):n().add(a.maxDate,"days")),a.minDate!==!1&&(i=isNaN(a.minDate)?n(a.minDate,a.format):n().add(a.minDate-1,"days")),o+='<div class="uk-datepicker-nav">',o+='<a href="" class="uk-datepicker-previous"></a>',o+='<a href="" class="uk-datepicker-next"></a>',e.formSelect){var u,c,d,l,h=(new Date).getFullYear(),f=[];for(r=0;r<a.i18n.months.length;r++)r==t.month?f.push('<option value="'+r+'" selected>'+a.i18n.months[r]+"</option>"):f.push('<option value="'+r+'">'+a.i18n.months[r]+"</option>");for(u='<span class="uk-form-select">'+a.i18n.months[t.month]+'<select class="update-picker-month">'+f.join("")+"</select></span>",f=[],d=i?i.year():h-50,l=s?s.year():h+20,r=d;l>=r;r++)r==t.year?f.push('<option value="'+r+'" selected>'+r+"</option>"):f.push('<option value="'+r+'">'+r+"</option>");c='<span class="uk-form-select">'+t.year+'<select class="update-picker-year">'+f.join("")+"</select></span>",o+='<div class="uk-datepicker-heading">'+u+" "+c+"</div>"}else o+='<div class="uk-datepicker-heading">'+a.i18n.months[t.month]+" "+t.year+"</div>";for(o+="</div>",o+='<table class="uk-datepicker-table">',o+="<thead>",r=0;r<t.weekdays.length;r++)t.weekdays[r]&&(o+="<th>"+t.weekdays[r]+"</th>");for(o+="</thead>",o+="<tbody>",r=0;r<t.days.length;r++)if(t.days[r]&&t.days[r].length){o+="<tr>";for(var m=0;m<t.days[r].length;m++)if(t.days[r][m]){var _=t.days[r][m],p=[];_.inmonth||p.push("uk-datepicker-table-muted"),_.selected&&p.push("uk-active"),s&&_.day>s&&p.push("uk-datepicker-date-disabled uk-datepicker-table-muted"),i&&i>_.day&&p.push("uk-datepicker-date-disabled uk-datepicker-table-muted"),o+='<td><a href="" class="'+p.join(" ")+'" data-date="'+_.day.format()+'">'+_.day.format("D")+"</a></td>"}o+="</tr>"}return o+="</tbody>",o+="</table>"}},boot:function(){e.$win.on("resize orientationchange",function(){a&&a.hide()}),e.$html.on("focus.datepicker.uikit","[data-uk-datepicker]",function(t){var n=e.$(this);if(!n.data("datepicker")){t.preventDefault();{e.datepicker(n,e.Utils.options(n.attr("data-uk-datepicker")))}n.trigger("focus")}}),e.$html.on("click.datepicker.uikit",function(n){var s=e.$(n.target);!a||s[0]==t[0]||s.data("datepicker")||s.parents(".uk-datepicker:first").length||a.hide()})},init:function(){if(!e.support.touch||"date"!=this.element.attr("type")||this.options.mobile){var s=this;this.current=this.element.val()?n(this.element.val(),this.options.format):n(),this.on("click",function(){a!==s&&s.pick(this.value)}).on("change",function(){s.element.val()&&!n(s.element.val(),s.options.format).isValid()&&s.element.val(n().format(s.options.format))}),t||(t=e.$('<div class="uk-dropdown uk-datepicker"></div>'),t.on("click",".uk-datepicker-next, .uk-datepicker-previous, [data-date]",function(s){s.stopPropagation(),s.preventDefault();var i=e.$(this);return i.hasClass("uk-datepicker-date-disabled")?!1:(i.is("[data-date]")?(a.element.val(n(i.data("date")).format(a.options.format)).trigger("change"),t.hide(),a=!1):a.add(1*(i.hasClass("uk-datepicker-next")?1:-1),"months"),void 0)}),t.on("change",".update-picker-month, .update-picker-year",function(){var t=e.$(this);a[t.is(".update-picker-year")?"setYear":"setMonth"](Number(t.val()))}),t.appendTo("body"))}},pick:function(s){var i=this.element.offset(),r={top:i.top+this.element.outerHeight()+this.options.offsettop,left:i.left,right:""};this.current=s?n(s,this.options.format):n(),this.initdate=this.current.format("YYYY-MM-DD"),this.update(),"right"==e.langdirection&&(r.right=window.innerWidth-(r.left+this.element.outerWidth()),r.left=""),t.css(r).show(),this.trigger("show.uk.datepicker"),a=this},add:function(e,t){this.current.add(e,t),this.update()},setMonth:function(e){this.current.month(e),this.update()},setYear:function(e){this.current.year(e),this.update()},update:function(){var e=this.getRows(this.current.year(),this.current.month()),n=this.options.template(e,this.options);t.html(n),this.trigger("update.uk.datepicker")},getRows:function(e,t){var a=this.options,s=n().format("YYYY-MM-DD"),i=[31,e%4===0&&e%100!==0||e%400===0?29:28,31,30,31,30,31,31,30,31,30,31][t],r=new Date(e,t,1).getDay(),o={month:t,year:e,weekdays:[],days:[]},u=[];o.weekdays=function(){for(var e=0,t=[];7>e;e++){for(var n=e+(a.weekstart||0);n>=7;)n-=7;t.push(a.i18n.weekdays[n])}return t}(),a.weekstart&&a.weekstart>0&&(r-=a.weekstart,0>r&&(r+=7));for(var c=i+r,d=c;d>7;)d-=7;c+=7-d;for(var l,h,f,m,_,p=0,y=0;c>p;p++)l=new Date(e,t,1+(p-r)),h=a.mindate&&l<a.mindate||a.maxdate&&l>a.maxdate,_=!(r>p||p>=i+r),l=n(l),f=this.initdate==l.format("YYYY-MM-DD"),m=s==l.format("YYYY-MM-DD"),u.push({selected:f,today:m,disabled:h,day:l,inmonth:_}),7===++y&&(o.days.push(u),u=[],y=0);return o},hide:function(){a&&a===this&&(t.hide(),a=!1,this.trigger("hide.uk.datepicker"))}}),n=function(e){function t(e,t,n){switch(arguments.length){case 2:return null!=e?e:t;case 3:return null!=e?e:null!=t?t:n;default:throw new Error("Implement me")}}function n(e,t){return Yt.call(e,t)}function a(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1}}function s(e){kt.suppressDeprecationWarnings===!1&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function i(e,t){var n=!0;return h(function(){return n&&(s(e),n=!1),t.apply(this,arguments)},t)}function r(e,t){mn[e]||(s(t),mn[e]=!0)}function o(e,t){return function(n){return _(e.call(this,n),t)}}function u(e,t){return function(n){return this.localeData().ordinal(e.call(this,n),t)}}function c(){}function d(e,t){t!==!1&&F(e),f(this,e),this._d=new Date(+e._d)}function l(e){var t=v(e),n=t.year||0,a=t.quarter||0,s=t.month||0,i=t.week||0,r=t.day||0,o=t.hour||0,u=t.minute||0,c=t.second||0,d=t.millisecond||0;this._milliseconds=+d+1e3*c+6e4*u+36e5*o,this._days=+r+7*i,this._months=+s+3*a+12*n,this._data={},this._locale=kt.localeData(),this._bubble()}function h(e,t){for(var a in t)n(t,a)&&(e[a]=t[a]);return n(t,"toString")&&(e.toString=t.toString),n(t,"valueOf")&&(e.valueOf=t.valueOf),e}function f(e,t){var n,a,s;if("undefined"!=typeof t._isAMomentObject&&(e._isAMomentObject=t._isAMomentObject),"undefined"!=typeof t._i&&(e._i=t._i),"undefined"!=typeof t._f&&(e._f=t._f),"undefined"!=typeof t._l&&(e._l=t._l),"undefined"!=typeof t._strict&&(e._strict=t._strict),"undefined"!=typeof t._tzm&&(e._tzm=t._tzm),"undefined"!=typeof t._isUTC&&(e._isUTC=t._isUTC),"undefined"!=typeof t._offset&&(e._offset=t._offset),"undefined"!=typeof t._pf&&(e._pf=t._pf),"undefined"!=typeof t._locale&&(e._locale=t._locale),Ft.length>0)for(n in Ft)a=Ft[n],s=t[a],"undefined"!=typeof s&&(e[a]=s);return e}function m(e){return 0>e?Math.ceil(e):Math.floor(e)}function _(e,t,n){for(var a=""+Math.abs(e),s=e>=0;a.length<t;)a="0"+a;return(s?n?"+":"":"-")+a}function p(e,t){var n={milliseconds:0,months:0};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function y(e,t){var n;return t=x(t,e),e.isBefore(t)?n=p(e,t):(n=p(t,e),n.milliseconds=-n.milliseconds,n.months=-n.months),n}function k(e,t){return function(n,a){var s,i;return null===a||isNaN(+a)||(r(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period)."),i=n,n=a,a=i),n="string"==typeof n?+n:n,s=kt.duration(n,a),D(this,s,e),this}}function D(e,t,n,a){var s=t._milliseconds,i=t._days,r=t._months;a=null==a?!0:a,s&&e._d.setTime(+e._d+s*n),i&&ft(e,"Date",ht(e,"Date")+i*n),r&&lt(e,ht(e,"Month")+r*n),a&&kt.updateOffset(e,i||r)}function g(e){return"[object Array]"===Object.prototype.toString.call(e)}function M(e){return"[object Date]"===Object.prototype.toString.call(e)||e instanceof Date}function Y(e,t,n){var a,s=Math.min(e.length,t.length),i=Math.abs(e.length-t.length),r=0;for(a=0;s>a;a++)(n&&e[a]!==t[a]||!n&&S(e[a])!==S(t[a]))&&r++;return r+i}function w(e){if(e){var t=e.toLowerCase().replace(/(.)s$/,"$1");e=on[e]||un[t]||t}return e}function v(e){var t,a,s={};for(a in e)n(e,a)&&(t=w(a),t&&(s[t]=e[a]));return s}function b(t){var n,a;if(0===t.indexOf("week"))n=7,a="day";else{if(0!==t.indexOf("month"))return;n=12,a="month"}kt[t]=function(s,i){var r,o,u=kt._locale[t],c=[];if("number"==typeof s&&(i=s,s=e),o=function(e){var t=kt().utc().set(a,e);return u.call(kt._locale,t,s||"")},null!=i)return o(i);for(r=0;n>r;r++)c.push(o(r));return c}}function S(e){var t=+e,n=0;return 0!==t&&isFinite(t)&&(n=t>=0?Math.floor(t):Math.ceil(t)),n}function T(e,t){return new Date(Date.UTC(e,t+1,0)).getUTCDate()}function O(e,t,n){return ot(kt([e,11,31+t-n]),t,n).week}function W(e){return U(e)?366:365}function U(e){return e%4===0&&e%100!==0||e%400===0}function F(e){var t;e._a&&-2===e._pf.overflow&&(t=e._a[vt]<0||e._a[vt]>11?vt:e._a[bt]<1||e._a[bt]>T(e._a[wt],e._a[vt])?bt:e._a[St]<0||e._a[St]>23?St:e._a[Tt]<0||e._a[Tt]>59?Tt:e._a[Ot]<0||e._a[Ot]>59?Ot:e._a[Wt]<0||e._a[Wt]>999?Wt:-1,e._pf._overflowDayOfYear&&(wt>t||t>bt)&&(t=bt),e._pf.overflow=t)}function G(e){return null==e._isValid&&(e._isValid=!isNaN(e._d.getTime())&&e._pf.overflow<0&&!e._pf.empty&&!e._pf.invalidMonth&&!e._pf.nullInput&&!e._pf.invalidFormat&&!e._pf.userInvalidated,e._strict&&(e._isValid=e._isValid&&0===e._pf.charsLeftOver&&0===e._pf.unusedTokens.length)),e._isValid}function C(e){return e?e.toLowerCase().replace("_","-"):e}function z(e){for(var t,n,a,s,i=0;i<e.length;){for(s=C(e[i]).split("-"),t=s.length,n=C(e[i+1]),n=n?n.split("-"):null;t>0;){if(a=I(s.slice(0,t).join("-")))return a;if(n&&n.length>=t&&Y(s,n,!0)>=t-1)break;t--}i++}return null}function I(e){var t=null;if(!Ut[e]&&Gt)try{t=kt.locale(),require("./locale/"+e),kt.locale(t)}catch(n){}return Ut[e]}function x(e,t){return t._isUTC?kt(e).zone(t._offset||0):kt(e).local()}function L(e){return e.match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"")}function P(e){var t,n,a=e.match(xt);for(t=0,n=a.length;n>t;t++)a[t]=fn[a[t]]?fn[a[t]]:L(a[t]);return function(s){var i="";for(t=0;n>t;t++)i+=a[t]instanceof Function?a[t].call(s,e):a[t];return i}}function H(e,t){return e.isValid()?(t=A(t,e.localeData()),cn[t]||(cn[t]=P(t)),cn[t](e)):e.localeData().invalidDate()}function A(e,t){function n(e){return t.longDateFormat(e)||e}var a=5;for(Lt.lastIndex=0;a>=0&&Lt.test(e);)e=e.replace(Lt,n),Lt.lastIndex=0,a-=1;return e}function Z(e,t){var n,a=t._strict;switch(e){case"Q":return qt;case"DDDD":return Rt;case"YYYY":case"GGGG":case"gggg":return a?Xt:At;case"Y":case"G":case"g":return Kt;case"YYYYYY":case"YYYYY":case"GGGGG":case"ggggg":return a?Bt:Zt;case"S":if(a)return qt;case"SS":if(a)return Qt;case"SSS":if(a)return Rt;case"DDD":return Ht;case"MMM":case"MMMM":case"dd":case"ddd":case"dddd":return jt;case"a":case"A":return t._locale._meridiemParse;case"X":return Vt;case"Z":case"ZZ":return Et;case"T":return $t;case"SSSS":return Nt;case"MM":case"DD":case"YY":case"GG":case"gg":case"HH":case"hh":case"mm":case"ss":case"ww":case"WW":return a?Qt:Pt;case"M":case"D":case"d":case"H":case"h":case"m":case"s":case"w":case"W":case"e":case"E":return Pt;case"Do":return Jt;default:return n=new RegExp(R(Q(e.replace("\\","")),"i"))}}function N(e){e=e||"";var t=e.match(Et)||[],n=t[t.length-1]||[],a=(n+"").match(sn)||["-",0,0],s=+(60*a[1])+S(a[2]);return"+"===a[0]?-s:s}function j(e,t,n){var a,s=n._a;switch(e){case"Q":null!=t&&(s[vt]=3*(S(t)-1));break;case"M":case"MM":null!=t&&(s[vt]=S(t)-1);break;case"MMM":case"MMMM":a=n._locale.monthsParse(t),null!=a?s[vt]=a:n._pf.invalidMonth=t;break;case"D":case"DD":null!=t&&(s[bt]=S(t));break;case"Do":null!=t&&(s[bt]=S(parseInt(t,10)));break;case"DDD":case"DDDD":null!=t&&(n._dayOfYear=S(t));break;case"YY":s[wt]=kt.parseTwoDigitYear(t);break;case"YYYY":case"YYYYY":case"YYYYYY":s[wt]=S(t);break;case"a":case"A":n._isPm=n._locale.isPM(t);break;case"H":case"HH":case"h":case"hh":s[St]=S(t);break;case"m":case"mm":s[Tt]=S(t);break;case"s":case"ss":s[Ot]=S(t);break;case"S":case"SS":case"SSS":case"SSSS":s[Wt]=S(1e3*("0."+t));break;case"X":n._d=new Date(1e3*parseFloat(t));break;case"Z":case"ZZ":n._useUTC=!0,n._tzm=N(t);break;case"dd":case"ddd":case"dddd":a=n._locale.weekdaysParse(t),null!=a?(n._w=n._w||{},n._w.d=a):n._pf.invalidWeekday=t;break;case"w":case"ww":case"W":case"WW":case"d":case"e":case"E":e=e.substr(0,1);case"gggg":case"GGGG":case"GGGGG":e=e.substr(0,2),t&&(n._w=n._w||{},n._w[e]=S(t));break;case"gg":case"GG":n._w=n._w||{},n._w[e]=kt.parseTwoDigitYear(t)}}function E(e){var n,a,s,i,r,o,u;n=e._w,null!=n.GG||null!=n.W||null!=n.E?(r=1,o=4,a=t(n.GG,e._a[wt],ot(kt(),1,4).year),s=t(n.W,1),i=t(n.E,1)):(r=e._locale._week.dow,o=e._locale._week.doy,a=t(n.gg,e._a[wt],ot(kt(),r,o).year),s=t(n.w,1),null!=n.d?(i=n.d,r>i&&++s):i=null!=n.e?n.e+r:r),u=ut(a,s,i,o,r),e._a[wt]=u.year,e._dayOfYear=u.dayOfYear}function $(e){var n,a,s,i,r=[];if(!e._d){for(s=J(e),e._w&&null==e._a[bt]&&null==e._a[vt]&&E(e),e._dayOfYear&&(i=t(e._a[wt],s[wt]),e._dayOfYear>W(i)&&(e._pf._overflowDayOfYear=!0),a=at(i,0,e._dayOfYear),e._a[vt]=a.getUTCMonth(),e._a[bt]=a.getUTCDate()),n=0;3>n&&null==e._a[n];++n)e._a[n]=r[n]=s[n];for(;7>n;n++)e._a[n]=r[n]=null==e._a[n]?2===n?1:0:e._a[n];e._d=(e._useUTC?at:nt).apply(null,r),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()+e._tzm)}}function V(e){var t;e._d||(t=v(e._i),e._a=[t.year,t.month,t.day,t.hour,t.minute,t.second,t.millisecond],$(e))}function J(e){var t=new Date;return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}function q(e){if(e._f===kt.ISO_8601)return B(e),void 0;e._a=[],e._pf.empty=!0;var t,n,a,s,i,r=""+e._i,o=r.length,u=0;for(a=A(e._f,e._locale).match(xt)||[],t=0;t<a.length;t++)s=a[t],n=(r.match(Z(s,e))||[])[0],n&&(i=r.substr(0,r.indexOf(n)),i.length>0&&e._pf.unusedInput.push(i),r=r.slice(r.indexOf(n)+n.length),u+=n.length),fn[s]?(n?e._pf.empty=!1:e._pf.unusedTokens.push(s),j(s,n,e)):e._strict&&!n&&e._pf.unusedTokens.push(s);e._pf.charsLeftOver=o-u,r.length>0&&e._pf.unusedInput.push(r),e._isPm&&e._a[St]<12&&(e._a[St]+=12),e._isPm===!1&&12===e._a[St]&&(e._a[St]=0),$(e),F(e)}function Q(e){return e.replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,n,a,s){return t||n||a||s})}function R(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function X(e){var t,n,s,i,r;if(0===e._f.length)return e._pf.invalidFormat=!0,e._d=new Date(0/0),void 0;for(i=0;i<e._f.length;i++)r=0,t=f({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._pf=a(),t._f=e._f[i],q(t),G(t)&&(r+=t._pf.charsLeftOver,r+=10*t._pf.unusedTokens.length,t._pf.score=r,(null==s||s>r)&&(s=r,n=t));h(e,n||t)}function B(e){var t,n,a=e._i,s=en.exec(a);if(s){for(e._pf.iso=!0,t=0,n=nn.length;n>t;t++)if(nn[t][1].exec(a)){e._f=nn[t][0]+(s[6]||" ");break}for(t=0,n=an.length;n>t;t++)if(an[t][1].exec(a)){e._f+=an[t][0];break}a.match(Et)&&(e._f+="Z"),q(e)}else e._isValid=!1}function K(e){B(e),e._isValid===!1&&(delete e._isValid,kt.createFromInputFallback(e))}function et(e,t){var n,a=[];for(n=0;n<e.length;++n)a.push(t(e[n],n));return a}function tt(t){var n,a=t._i;a===e?t._d=new Date:M(a)?t._d=new Date(+a):null!==(n=Ct.exec(a))?t._d=new Date(+n[1]):"string"==typeof a?K(t):g(a)?(t._a=et(a.slice(0),function(e){return parseInt(e,10)}),$(t)):"object"==typeof a?V(t):"number"==typeof a?t._d=new Date(a):kt.createFromInputFallback(t)}function nt(e,t,n,a,s,i,r){var o=new Date(e,t,n,a,s,i,r);return 1970>e&&o.setFullYear(e),o}function at(e){var t=new Date(Date.UTC.apply(null,arguments));return 1970>e&&t.setUTCFullYear(e),t}function st(e,t){if("string"==typeof e)if(isNaN(e)){if(e=t.weekdaysParse(e),"number"!=typeof e)return null}else e=parseInt(e,10);return e}function it(e,t,n,a,s){return s.relativeTime(t||1,!!n,e,a)}function rt(e,t,n){var a=kt.duration(e).abs(),s=Mt(a.as("s")),i=Mt(a.as("m")),r=Mt(a.as("h")),o=Mt(a.as("d")),u=Mt(a.as("M")),c=Mt(a.as("y")),d=s<dn.s&&["s",s]||1===i&&["m"]||i<dn.m&&["mm",i]||1===r&&["h"]||r<dn.h&&["hh",r]||1===o&&["d"]||o<dn.d&&["dd",o]||1===u&&["M"]||u<dn.M&&["MM",u]||1===c&&["y"]||["yy",c];return d[2]=t,d[3]=+e>0,d[4]=n,it.apply({},d)}function ot(e,t,n){var a,s=n-t,i=n-e.day();return i>s&&(i-=7),s-7>i&&(i+=7),a=kt(e).add(i,"d"),{week:Math.ceil(a.dayOfYear()/7),year:a.year()}}function ut(e,t,n,a,s){var i,r,o=at(e,0,1).getUTCDay();return o=0===o?7:o,n=null!=n?n:s,i=s-o+(o>a?7:0)-(s>o?7:0),r=7*(t-1)+(n-s)+i+1,{year:r>0?e:e-1,dayOfYear:r>0?r:W(e-1)+r}}function ct(t){var n=t._i,a=t._f;return t._locale=t._locale||kt.localeData(t._l),null===n||a===e&&""===n?kt.invalid({nullInput:!0}):("string"==typeof n&&(t._i=n=t._locale.preparse(n)),kt.isMoment(n)?new d(n,!0):(a?g(a)?X(t):q(t):tt(t),new d(t)))}function dt(e,t){var n,a;if(1===t.length&&g(t[0])&&(t=t[0]),!t.length)return kt();for(n=t[0],a=1;a<t.length;++a)t[a][e](n)&&(n=t[a]);return n}function lt(e,t){var n;return"string"==typeof t&&(t=e.localeData().monthsParse(t),"number"!=typeof t)?e:(n=Math.min(e.date(),T(e.year(),t)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,n),e)}function ht(e,t){return e._d["get"+(e._isUTC?"UTC":"")+t]()}function ft(e,t,n){return"Month"===t?lt(e,n):e._d["set"+(e._isUTC?"UTC":"")+t](n)}function mt(e,t){return function(n){return null!=n?(ft(this,e,n),kt.updateOffset(this,t),this):ht(this,e)}}function _t(e){return 400*e/146097}function pt(e){return 146097*e/400}function yt(e){kt.duration.fn[e]=function(){return this._data[e]}}for(var kt,Dt,gt="2.8.3",Mt=Math.round,Yt=Object.prototype.hasOwnProperty,wt=0,vt=1,bt=2,St=3,Tt=4,Ot=5,Wt=6,Ut={},Ft=[],Gt="undefined"!=typeof module&&module.exports,Ct=/^\/?Date\((\-?\d+)/i,zt=/(\-)?(?:(\d*)\.)?(\d+)\:(\d+)(?:\:(\d+)\.?(\d{3})?)?/,It=/^(-)?P(?:(?:([0-9,.]*)Y)?(?:([0-9,.]*)M)?(?:([0-9,.]*)D)?(?:T(?:([0-9,.]*)H)?(?:([0-9,.]*)M)?(?:([0-9,.]*)S)?)?|([0-9,.]*)W)$/,xt=/(\[[^\[]*\])|(\\)?(Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Q|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|mm?|ss?|S{1,4}|X|zz?|ZZ?|.)/g,Lt=/(\[[^\[]*\])|(\\)?(LT|LL?L?L?|l{1,4})/g,Pt=/\d\d?/,Ht=/\d{1,3}/,At=/\d{1,4}/,Zt=/[+\-]?\d{1,6}/,Nt=/\d+/,jt=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,Et=/Z|[\+\-]\d\d:?\d\d/gi,$t=/T/i,Vt=/[\+\-]?\d+(\.\d{1,3})?/,Jt=/\d{1,2}/,qt=/\d/,Qt=/\d\d/,Rt=/\d{3}/,Xt=/\d{4}/,Bt=/[+-]?\d{6}/,Kt=/[+-]?\d+/,en=/^\s*(?:[+-]\d{6}|\d{4})-(?:(\d\d-\d\d)|(W\d\d$)|(W\d\d-\d)|(\d\d\d))((T| )(\d\d(:\d\d(:\d\d(\.\d+)?)?)?)?([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,tn="YYYY-MM-DDTHH:mm:ssZ",nn=[["YYYYYY-MM-DD",/[+-]\d{6}-\d{2}-\d{2}/],["YYYY-MM-DD",/\d{4}-\d{2}-\d{2}/],["GGGG-[W]WW-E",/\d{4}-W\d{2}-\d/],["GGGG-[W]WW",/\d{4}-W\d{2}/],["YYYY-DDD",/\d{4}-\d{3}/]],an=[["HH:mm:ss.SSSS",/(T| )\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss",/(T| )\d\d:\d\d:\d\d/],["HH:mm",/(T| )\d\d:\d\d/],["HH",/(T| )\d\d/]],sn=/([\+\-]|\d\d)/gi,rn=("Date|Hours|Minutes|Seconds|Milliseconds".split("|"),{Milliseconds:1,Seconds:1e3,Minutes:6e4,Hours:36e5,Days:864e5,Months:2592e6,Years:31536e6}),on={ms:"millisecond",s:"second",m:"minute",h:"hour",d:"day",D:"date",w:"week",W:"isoWeek",M:"month",Q:"quarter",y:"year",DDD:"dayOfYear",e:"weekday",E:"isoWeekday",gg:"weekYear",GG:"isoWeekYear"},un={dayofyear:"dayOfYear",isoweekday:"isoWeekday",isoweek:"isoWeek",weekyear:"weekYear",isoweekyear:"isoWeekYear"},cn={},dn={s:45,m:45,h:22,d:26,M:11},ln="DDD w W M D d".split(" "),hn="M D H h m s w W".split(" "),fn={M:function(){return this.month()+1},MMM:function(e){return this.localeData().monthsShort(this,e)},MMMM:function(e){return this.localeData().months(this,e)},D:function(){return this.date()},DDD:function(){return this.dayOfYear()},d:function(){return this.day()},dd:function(e){return this.localeData().weekdaysMin(this,e)},ddd:function(e){return this.localeData().weekdaysShort(this,e)},dddd:function(e){return this.localeData().weekdays(this,e)},w:function(){return this.week()},W:function(){return this.isoWeek()},YY:function(){return _(this.year()%100,2)},YYYY:function(){return _(this.year(),4)},YYYYY:function(){return _(this.year(),5)},YYYYYY:function(){var e=this.year(),t=e>=0?"+":"-";return t+_(Math.abs(e),6)},gg:function(){return _(this.weekYear()%100,2)},gggg:function(){return _(this.weekYear(),4)},ggggg:function(){return _(this.weekYear(),5)},GG:function(){return _(this.isoWeekYear()%100,2)},GGGG:function(){return _(this.isoWeekYear(),4)},GGGGG:function(){return _(this.isoWeekYear(),5)},e:function(){return this.weekday()},E:function(){return this.isoWeekday()},a:function(){return this.localeData().meridiem(this.hours(),this.minutes(),!0)},A:function(){return this.localeData().meridiem(this.hours(),this.minutes(),!1)},H:function(){return this.hours()},h:function(){return this.hours()%12||12},m:function(){return this.minutes()},s:function(){return this.seconds()},S:function(){return S(this.milliseconds()/100)},SS:function(){return _(S(this.milliseconds()/10),2)},SSS:function(){return _(this.milliseconds(),3)},SSSS:function(){return _(this.milliseconds(),3)},Z:function(){var e=-this.zone(),t="+";return 0>e&&(e=-e,t="-"),t+_(S(e/60),2)+":"+_(S(e)%60,2)},ZZ:function(){var e=-this.zone(),t="+";return 0>e&&(e=-e,t="-"),t+_(S(e/60),2)+_(S(e)%60,2)},z:function(){return this.zoneAbbr()},zz:function(){return this.zoneName()},X:function(){return this.unix()},Q:function(){return this.quarter()}},mn={},_n=["months","monthsShort","weekdays","weekdaysShort","weekdaysMin"];ln.length;)Dt=ln.pop(),fn[Dt+"o"]=u(fn[Dt],Dt);for(;hn.length;)Dt=hn.pop(),fn[Dt+Dt]=o(fn[Dt],2);fn.DDDD=o(fn.DDD,3),h(c.prototype,{set:function(e){var t,n;for(n in e)t=e[n],"function"==typeof t?this[n]=t:this["_"+n]=t},_months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),months:function(e){return this._months[e.month()]},_monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),monthsShort:function(e){return this._monthsShort[e.month()]},monthsParse:function(e){var t,n,a;for(this._monthsParse||(this._monthsParse=[]),t=0;12>t;t++)if(this._monthsParse[t]||(n=kt.utc([2e3,t]),a="^"+this.months(n,"")+"|^"+this.monthsShort(n,""),this._monthsParse[t]=new RegExp(a.replace(".",""),"i")),this._monthsParse[t].test(e))return t},_weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdays:function(e){return this._weekdays[e.day()]},_weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysShort:function(e){return this._weekdaysShort[e.day()]},_weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),weekdaysMin:function(e){return this._weekdaysMin[e.day()]},weekdaysParse:function(e){var t,n,a;for(this._weekdaysParse||(this._weekdaysParse=[]),t=0;7>t;t++)if(this._weekdaysParse[t]||(n=kt([2e3,1]).day(t),a="^"+this.weekdays(n,"")+"|^"+this.weekdaysShort(n,"")+"|^"+this.weekdaysMin(n,""),this._weekdaysParse[t]=new RegExp(a.replace(".",""),"i")),this._weekdaysParse[t].test(e))return t},_longDateFormat:{LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY LT",LLLL:"dddd, MMMM D, YYYY LT"},longDateFormat:function(e){var t=this._longDateFormat[e];return!t&&this._longDateFormat[e.toUpperCase()]&&(t=this._longDateFormat[e.toUpperCase()].replace(/MMMM|MM|DD|dddd/g,function(e){return e.slice(1)}),this._longDateFormat[e]=t),t},isPM:function(e){return"p"===(e+"").toLowerCase().charAt(0)},_meridiemParse:/[ap]\.?m?\.?/i,meridiem:function(e,t,n){return e>11?n?"pm":"PM":n?"am":"AM"},_calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},calendar:function(e,t){var n=this._calendar[e];return"function"==typeof n?n.apply(t):n},_relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},relativeTime:function(e,t,n,a){var s=this._relativeTime[n];return"function"==typeof s?s(e,t,n,a):s.replace(/%d/i,e)},pastFuture:function(e,t){var n=this._relativeTime[e>0?"future":"past"];return"function"==typeof n?n(t):n.replace(/%s/i,t)},ordinal:function(e){return this._ordinal.replace("%d",e)},_ordinal:"%d",preparse:function(e){return e},postformat:function(e){return e},week:function(e){return ot(e,this._week.dow,this._week.doy).week},_week:{dow:0,doy:6},_invalidDate:"Invalid date",invalidDate:function(){return this._invalidDate}}),kt=function(t,n,s,i){var r;return"boolean"==typeof s&&(i=s,s=e),r={},r._isAMomentObject=!0,r._i=t,r._f=n,r._l=s,r._strict=i,r._isUTC=!1,r._pf=a(),ct(r)},kt.suppressDeprecationWarnings=!1,kt.createFromInputFallback=i("moment construction falls back to js Date. This is discouraged and will be removed in upcoming major release. Please refer to https://github.com/moment/moment/issues/1407 for more info.",function(e){e._d=new Date(e._i)}),kt.min=function(){var e=[].slice.call(arguments,0);return dt("isBefore",e)},kt.max=function(){var e=[].slice.call(arguments,0);return dt("isAfter",e)},kt.utc=function(t,n,s,i){var r;return"boolean"==typeof s&&(i=s,s=e),r={},r._isAMomentObject=!0,r._useUTC=!0,r._isUTC=!0,r._l=s,r._i=t,r._f=n,r._strict=i,r._pf=a(),ct(r).utc()},kt.unix=function(e){return kt(1e3*e)},kt.duration=function(e,t){var a,s,i,r,o=e,u=null;return kt.isDuration(e)?o={ms:e._milliseconds,d:e._days,M:e._months}:"number"==typeof e?(o={},t?o[t]=e:o.milliseconds=e):(u=zt.exec(e))?(a="-"===u[1]?-1:1,o={y:0,d:S(u[bt])*a,h:S(u[St])*a,m:S(u[Tt])*a,s:S(u[Ot])*a,ms:S(u[Wt])*a}):(u=It.exec(e))?(a="-"===u[1]?-1:1,i=function(e){var t=e&&parseFloat(e.replace(",","."));return(isNaN(t)?0:t)*a},o={y:i(u[2]),M:i(u[3]),d:i(u[4]),h:i(u[5]),m:i(u[6]),s:i(u[7]),w:i(u[8])}):"object"==typeof o&&("from"in o||"to"in o)&&(r=y(kt(o.from),kt(o.to)),o={},o.ms=r.milliseconds,o.M=r.months),s=new l(o),kt.isDuration(e)&&n(e,"_locale")&&(s._locale=e._locale),s},kt.version=gt,kt.defaultFormat=tn,kt.ISO_8601=function(){},kt.momentProperties=Ft,kt.updateOffset=function(){},kt.relativeTimeThreshold=function(t,n){return dn[t]===e?!1:n===e?dn[t]:(dn[t]=n,!0)},kt.lang=i("moment.lang is deprecated. Use moment.locale instead.",function(e,t){return kt.locale(e,t)}),kt.locale=function(e,t){var n;return e&&(n="undefined"!=typeof t?kt.defineLocale(e,t):kt.localeData(e),n&&(kt.duration._locale=kt._locale=n)),kt._locale._abbr},kt.defineLocale=function(e,t){return null!==t?(t.abbr=e,Ut[e]||(Ut[e]=new c),Ut[e].set(t),kt.locale(e),Ut[e]):(delete Ut[e],null)},kt.langData=i("moment.langData is deprecated. Use moment.localeData instead.",function(e){return kt.localeData(e)}),kt.localeData=function(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return kt._locale;if(!g(e)){if(t=I(e))return t;e=[e]}return z(e)},kt.isMoment=function(e){return e instanceof d||null!=e&&n(e,"_isAMomentObject")},kt.isDuration=function(e){return e instanceof l};for(Dt=_n.length-1;Dt>=0;--Dt)b(_n[Dt]);kt.normalizeUnits=function(e){return w(e)},kt.invalid=function(e){var t=kt.utc(0/0);return null!=e?h(t._pf,e):t._pf.userInvalidated=!0,t},kt.parseZone=function(){return kt.apply(null,arguments).parseZone()},kt.parseTwoDigitYear=function(e){return S(e)+(S(e)>68?1900:2e3)},h(kt.fn=d.prototype,{clone:function(){return kt(this)},valueOf:function(){return+this._d+6e4*(this._offset||0)},unix:function(){return Math.floor(+this/1e3)},toString:function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},toDate:function(){return this._offset?new Date(+this):this._d},toISOString:function(){var e=kt(this).utc();return 0<e.year()&&e.year()<=9999?H(e,"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]"):H(e,"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]")},toArray:function(){var e=this;return[e.year(),e.month(),e.date(),e.hours(),e.minutes(),e.seconds(),e.milliseconds()]},isValid:function(){return G(this)},isDSTShifted:function(){return this._a?this.isValid()&&Y(this._a,(this._isUTC?kt.utc(this._a):kt(this._a)).toArray())>0:!1},parsingFlags:function(){return h({},this._pf)},invalidAt:function(){return this._pf.overflow},utc:function(e){return this.zone(0,e)},local:function(e){return this._isUTC&&(this.zone(0,e),this._isUTC=!1,e&&this.add(this._dateTzOffset(),"m")),this},format:function(e){var t=H(this,e||kt.defaultFormat);return this.localeData().postformat(t)},add:k(1,"add"),subtract:k(-1,"subtract"),diff:function(e,t,n){var a,s,i,r=x(e,this),o=6e4*(this.zone()-r.zone());return t=w(t),"year"===t||"month"===t?(a=432e5*(this.daysInMonth()+r.daysInMonth()),s=12*(this.year()-r.year())+(this.month()-r.month()),i=this-kt(this).startOf("month")-(r-kt(r).startOf("month")),i-=6e4*(this.zone()-kt(this).startOf("month").zone()-(r.zone()-kt(r).startOf("month").zone())),s+=i/a,"year"===t&&(s/=12)):(a=this-r,s="second"===t?a/1e3:"minute"===t?a/6e4:"hour"===t?a/36e5:"day"===t?(a-o)/864e5:"week"===t?(a-o)/6048e5:a),n?s:m(s)},from:function(e,t){return kt.duration({to:this,from:e}).locale(this.locale()).humanize(!t)},fromNow:function(e){return this.from(kt(),e)},calendar:function(e){var t=e||kt(),n=x(t,this).startOf("day"),a=this.diff(n,"days",!0),s=-6>a?"sameElse":-1>a?"lastWeek":0>a?"lastDay":1>a?"sameDay":2>a?"nextDay":7>a?"nextWeek":"sameElse";return this.format(this.localeData().calendar(s,this))},isLeapYear:function(){return U(this.year())},isDST:function(){return this.zone()<this.clone().month(0).zone()||this.zone()<this.clone().month(5).zone()},day:function(e){var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(e=st(e,this.localeData()),this.add(e-t,"d")):t},month:mt("Month",!0),startOf:function(e){switch(e=w(e)){case"year":this.month(0);case"quarter":case"month":this.date(1);case"week":case"isoWeek":case"day":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return"week"===e?this.weekday(0):"isoWeek"===e&&this.isoWeekday(1),"quarter"===e&&this.month(3*Math.floor(this.month()/3)),this},endOf:function(e){return e=w(e),this.startOf(e).add(1,"isoWeek"===e?"week":e).subtract(1,"ms")},isAfter:function(e,t){return t=w("undefined"!=typeof t?t:"millisecond"),"millisecond"===t?(e=kt.isMoment(e)?e:kt(e),+this>+e):+this.clone().startOf(t)>+kt(e).startOf(t)},isBefore:function(e,t){return t=w("undefined"!=typeof t?t:"millisecond"),"millisecond"===t?(e=kt.isMoment(e)?e:kt(e),+e>+this):+this.clone().startOf(t)<+kt(e).startOf(t)},isSame:function(e,t){return t=w(t||"millisecond"),"millisecond"===t?(e=kt.isMoment(e)?e:kt(e),+this===+e):+this.clone().startOf(t)===+x(e,this).startOf(t)},min:i("moment().min is deprecated, use moment.min instead. https://github.com/moment/moment/issues/1548",function(e){return e=kt.apply(null,arguments),this>e?this:e}),max:i("moment().max is deprecated, use moment.max instead. https://github.com/moment/moment/issues/1548",function(e){return e=kt.apply(null,arguments),e>this?this:e}),zone:function(e,t){var n,a=this._offset||0;return null==e?this._isUTC?a:this._dateTzOffset():("string"==typeof e&&(e=N(e)),Math.abs(e)<16&&(e=60*e),!this._isUTC&&t&&(n=this._dateTzOffset()),this._offset=e,this._isUTC=!0,null!=n&&this.subtract(n,"m"),a!==e&&(!t||this._changeInProgress?D(this,kt.duration(a-e,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,kt.updateOffset(this,!0),this._changeInProgress=null)),this)},zoneAbbr:function(){return this._isUTC?"UTC":""},zoneName:function(){return this._isUTC?"Coordinated Universal Time":""},parseZone:function(){return this._tzm?this.zone(this._tzm):"string"==typeof this._i&&this.zone(this._i),this},hasAlignedHourOffset:function(e){return e=e?kt(e).zone():0,(this.zone()-e)%60===0},daysInMonth:function(){return T(this.year(),this.month())},dayOfYear:function(e){var t=Mt((kt(this).startOf("day")-kt(this).startOf("year"))/864e5)+1;
return null==e?t:this.add(e-t,"d")},quarter:function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},weekYear:function(e){var t=ot(this,this.localeData()._week.dow,this.localeData()._week.doy).year;return null==e?t:this.add(e-t,"y")},isoWeekYear:function(e){var t=ot(this,1,4).year;return null==e?t:this.add(e-t,"y")},week:function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},isoWeek:function(e){var t=ot(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},weekday:function(e){var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")},isoWeekday:function(e){return null==e?this.day()||7:this.day(this.day()%7?e:e-7)},isoWeeksInYear:function(){return O(this.year(),1,4)},weeksInYear:function(){var e=this.localeData()._week;return O(this.year(),e.dow,e.doy)},get:function(e){return e=w(e),this[e]()},set:function(e,t){return e=w(e),"function"==typeof this[e]&&this[e](t),this},locale:function(t){var n;return t===e?this._locale._abbr:(n=kt.localeData(t),null!=n&&(this._locale=n),this)},lang:i("moment().lang() is deprecated. Use moment().localeData() instead.",function(t){return t===e?this.localeData():this.locale(t)}),localeData:function(){return this._locale},_dateTzOffset:function(){return 15*Math.round(this._d.getTimezoneOffset()/15)}}),kt.fn.millisecond=kt.fn.milliseconds=mt("Milliseconds",!1),kt.fn.second=kt.fn.seconds=mt("Seconds",!1),kt.fn.minute=kt.fn.minutes=mt("Minutes",!1),kt.fn.hour=kt.fn.hours=mt("Hours",!0),kt.fn.date=mt("Date",!0),kt.fn.dates=i("dates accessor is deprecated. Use date instead.",mt("Date",!0)),kt.fn.year=mt("FullYear",!0),kt.fn.years=i("years accessor is deprecated. Use year instead.",mt("FullYear",!0)),kt.fn.days=kt.fn.day,kt.fn.months=kt.fn.month,kt.fn.weeks=kt.fn.week,kt.fn.isoWeeks=kt.fn.isoWeek,kt.fn.quarters=kt.fn.quarter,kt.fn.toJSON=kt.fn.toISOString,h(kt.duration.fn=l.prototype,{_bubble:function(){var e,t,n,a=this._milliseconds,s=this._days,i=this._months,r=this._data,o=0;r.milliseconds=a%1e3,e=m(a/1e3),r.seconds=e%60,t=m(e/60),r.minutes=t%60,n=m(t/60),r.hours=n%24,s+=m(n/24),o=m(_t(s)),s-=m(pt(o)),i+=m(s/30),s%=30,o+=m(i/12),i%=12,r.days=s,r.months=i,r.years=o},abs:function(){return this._milliseconds=Math.abs(this._milliseconds),this._days=Math.abs(this._days),this._months=Math.abs(this._months),this._data.milliseconds=Math.abs(this._data.milliseconds),this._data.seconds=Math.abs(this._data.seconds),this._data.minutes=Math.abs(this._data.minutes),this._data.hours=Math.abs(this._data.hours),this._data.months=Math.abs(this._data.months),this._data.years=Math.abs(this._data.years),this},weeks:function(){return m(this.days()/7)},valueOf:function(){return this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*S(this._months/12)},humanize:function(e){var t=rt(this,!e,this.localeData());return e&&(t=this.localeData().pastFuture(+this,t)),this.localeData().postformat(t)},add:function(e,t){var n=kt.duration(e,t);return this._milliseconds+=n._milliseconds,this._days+=n._days,this._months+=n._months,this._bubble(),this},subtract:function(e,t){var n=kt.duration(e,t);return this._milliseconds-=n._milliseconds,this._days-=n._days,this._months-=n._months,this._bubble(),this},get:function(e){return e=w(e),this[e.toLowerCase()+"s"]()},as:function(e){var t,n;if(e=w(e),"month"===e||"year"===e)return t=this._days+this._milliseconds/864e5,n=this._months+12*_t(t),"month"===e?n:n/12;switch(t=this._days+pt(this._months/12),e){case"week":return t/7+this._milliseconds/6048e5;case"day":return t+this._milliseconds/864e5;case"hour":return 24*t+this._milliseconds/36e5;case"minute":return 24*t*60+this._milliseconds/6e4;case"second":return 24*t*60*60+this._milliseconds/1e3;case"millisecond":return Math.floor(24*t*60*60*1e3)+this._milliseconds;default:throw new Error("Unknown unit "+e)}},lang:kt.fn.lang,locale:kt.fn.locale,toIsoString:i("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",function(){return this.toISOString()}),toISOString:function(){var e=Math.abs(this.years()),t=Math.abs(this.months()),n=Math.abs(this.days()),a=Math.abs(this.hours()),s=Math.abs(this.minutes()),i=Math.abs(this.seconds()+this.milliseconds()/1e3);return this.asSeconds()?(this.asSeconds()<0?"-":"")+"P"+(e?e+"Y":"")+(t?t+"M":"")+(n?n+"D":"")+(a||s||i?"T":"")+(a?a+"H":"")+(s?s+"M":"")+(i?i+"S":""):"P0D"},localeData:function(){return this._locale}}),kt.duration.fn.toString=kt.duration.fn.toISOString;for(Dt in rn)n(rn,Dt)&&yt(Dt.toLowerCase());return kt.duration.fn.asMilliseconds=function(){return this.as("ms")},kt.duration.fn.asSeconds=function(){return this.as("s")},kt.duration.fn.asMinutes=function(){return this.as("m")},kt.duration.fn.asHours=function(){return this.as("h")},kt.duration.fn.asDays=function(){return this.as("d")},kt.duration.fn.asWeeks=function(){return this.as("weeks")},kt.duration.fn.asMonths=function(){return this.as("M")},kt.duration.fn.asYears=function(){return this.as("y")},kt.locale("en",{ordinal:function(e){var t=e%10,n=1===S(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th";return e+n}}),kt}.call(this),e.Utils.moment=n,e.datepicker});