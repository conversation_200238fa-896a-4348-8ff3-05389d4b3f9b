/*! UIkit 2.18.0 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */
!function(t){var e;window.UIkit&&(e=t(UIkit)),"function"==typeof define&&define.amd&&define("uikit-search",["uikit"],function(){return e||t(UIkit)})}(function(t){"use strict";for(var e={"12h":[],"24h":[]},i=0,o="";24>i;i++)o=""+i,10>i&&(o="0"+o),e["24h"].push({value:o+":00"}),e["24h"].push({value:o+":30"}),i>0&&13>i&&(e["12h"].push({value:o+":00 AM"}),e["12h"].push({value:o+":30 AM"})),i>12&&(o-=12,10>o&&(o="0"+String(o)),e["12h"].push({value:o+":00 PM"}),e["12h"].push({value:o+":30 PM"}));t.component("timepicker",{defaults:{format:"24h",delay:0},boot:function(){t.$html.on("focus.timepicker.uikit","[data-uk-timepicker]",function(){var e=t.$(this);if(!e.data("timepicker")){var i=t.timepicker(e,t.Utils.options(e.attr("data-uk-timepicker")));setTimeout(function(){i.autocomplete.input.focus()},40)}})},init:function(){var i=this;this.options.minLength=0,this.options.template='<ul class="uk-nav uk-nav-autocomplete uk-autocomplete-results">{{~items}}<li data-value="{{$item.value}}"><a>{{$item.value}}</a></li>{{/items}}</ul>',this.options.source=function(t){t(e[i.options.format]||e["12h"])},this.element.wrap('<div class="uk-autocomplete"></div>'),this.autocomplete=t.autocomplete(this.element.parent(),this.options),this.autocomplete.dropdown.addClass("uk-dropdown-small uk-dropdown-scrollable"),this.autocomplete.on("show.uk.autocomplete",function(){var t=i.autocomplete.dropdown.find('[data-value="'+i.autocomplete.input.val()+'"]');setTimeout(function(){i.autocomplete.pick(t,!0)},10)}),this.autocomplete.input.on("focus",function(){i.autocomplete.value=Math.random(),i.autocomplete.triggercomplete()}).on("blur",function(){i.checkTime()}),this.element.data("timepicker",this)},checkTime:function(){var t,e,i,o,a="AM",u=this.autocomplete.input.val();"12h"==this.options.format?(t=u.split(" "),e=t[0].split(":"),a=t[1]):e=u.split(":"),i=parseInt(e[0],10),o=parseInt(e[1],10),isNaN(i)&&(i=0),isNaN(o)&&(o=0),"12h"==this.options.format?(i>12?i=12:0>i&&(i=12),"am"===a||"a"===a?a="AM":("pm"===a||"p"===a)&&(a="PM"),"AM"!==a&&"PM"!==a&&(a="AM")):i>=24?i=23:0>i&&(i=0),0>o?o=0:o>=60&&(o=0),this.autocomplete.input.val(this.formatTime(i,o,a)).trigger("change")},formatTime:function(t,e,i){return t=10>t?"0"+t:t,e=10>e?"0"+e:e,t+":"+e+("12h"==this.options.format?" "+i:"")}})});