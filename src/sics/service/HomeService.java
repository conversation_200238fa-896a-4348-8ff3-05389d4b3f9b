/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.service;

import java.sql.SQLException;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import sics.domain.Atleta;
import sics.domain.Game;
import sics.domain.Playlist;
import sics.domain.User;

/**
 *
 * <AUTHOR>
 */
public class HomeService extends BaseService {

    public User getUserByMail(String val) {
        try {
            return dao.getUserByMail(val);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Playlist getPlaylistByVideoName(String val, Long userId) {
        try {
            return dao.getPlaylistByVideoName(val, userId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Game getGameByVideoName(String videoname, Long ownerUserId, String language) {
        try {
            return dao.getGameByVideoName(videoname, ownerUserId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List getSeasonAll() {
        try {
            return dao.getSeasonAll();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public Atleta getAtletaById(Long seasonId, Long playerId, Long teamId, Long groupsetId, boolean personalSource, String language) {
        try {
            return dao.getAtletaById(seasonId.toString(), playerId, teamId, groupsetId, personalSource, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
}
