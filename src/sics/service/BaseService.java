package sics.service;

import sics.dao.AppDAO;
import sics.dao.service.ManagerService;
import sics.helper.GlobalHelper;
import sics.helper.SpringApplicationContextHelper;

public class BaseService {

    protected ManagerService mManager;
    protected AppDAO dao, daoProtezione;

    //Injection del bean a costruttore
    public BaseService() {
        try {
            mManager = (ManagerService) SpringApplicationContextHelper.getBean(GlobalHelper.kBeanAppManager);
            dao = (AppDAO) SpringApplicationContextHelper.getBean(GlobalHelper.kBeanAppDao);
            daoProtezione = (AppDAO) SpringApplicationContextHelper.getBean(GlobalHelper.kBeanAppDaoProtezione);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    
    public void setAppDAO(AppDAO dao) {
        this.dao = dao;
    }

    public void setAppDAOProtezione(AppDAO dao) {
        this.daoProtezione = dao;
    }
}
