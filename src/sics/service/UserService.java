package sics.service;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import sics.domain.Atleta;
import sics.domain.Cache;
import sics.domain.CacheLanguageItem;
import sics.domain.CalendarRange;
import sics.domain.Game;
import sics.domain.Playlist;
import sics.domain.User;
import sics.domain.Season;
import sics.domain.Competition;
import sics.domain.Country;
import sics.domain.Export;
import sics.domain.ExportDetail;
import sics.domain.Favorites;
import sics.domain.FilterGameWrapper;
import sics.domain.FixtureDetails;
import sics.domain.FixturePlaylistSize;
import sics.domain.Groupset;
import sics.domain.LogData;
import sics.domain.PlayerAgent;
import sics.domain.PlaylistTV;
import sics.domain.PlaylistTVEvent;
import sics.domain.PlaylistTVShare;
import sics.domain.PlaylistTVSharePublic;
import sics.domain.Position;
import sics.domain.Settings;
import sics.domain.ShadowTeam;
import sics.domain.ShadowTeamDetail;
import sics.domain.Shortcut;
import sics.domain.StatsType;
import sics.domain.Tags;
import sics.domain.Team;
import sics.domain.TeamReportRanking;
import sics.domain.TeamStats;
import sics.domain.TeamStatsFilter;
import sics.domain.TeamStatsFilterDetail;
import sics.domain.UserCompetitionException;
import sics.domain.Watchlist;
import sics.domain.WatchlistDetail;
import sics.domain.WatchlistShare;
import sics.model.Azione;

public class UserService extends BaseService {

    /*
	 public List getUserAll(Long idRes, String name, String surname, String role) {
	 try {
	 return dao.getUserAll(name, surname, role);
	 } catch (SQLException ex) {
	 Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
	 return null;
	 }
	 }
     */
    public User getUser(Long id) {
        try {
            return dao.getUser(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public User getUser(String id) {
        try {
            return dao.getUser(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public User getUserBySer(String ser) {
        try {
            return dao.getUserBySer(ser);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<User> getUsers(List<Long> ids) {
        try {
            return dao.getUsers(ids);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public void deleteGame(Long idFixture, Long idUser) {
        try {
            dao.deleteGame(idFixture, idUser);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void deletePlaylist(Long idPlaylist, Long idUser) {
        try {
            dao.deletePlaylist(idPlaylist, idUser);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void updatePlaylist(Long idPlaylist, Long idUser, String name, String note) {
        try {
            dao.updatePlaylist(idPlaylist, idUser, name, note);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void saveUser(User item) {
        try {
            dao.saveUser(item);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    //Aggiunta in più
    public void saveUser2(User item) {
        try {
            dao.saveUser2(item);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void delUser(Long id) {
        try {
            dao.delUser(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List getPlaylistUser(Long idGro, Long idUse) {
        try {
            return dao.getPlaylistUser(idGro, idUse);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getCompetitionTactAllowed(Long idGro) {
        try {
            return dao.getCompetitionTactAllowed(idGro);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Playlist getPlaylist(Long id, Long idUse) {
        try {
            return dao.getPlaylist(id, idUse);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public void setPlaylistSeen(Long id, Long idUse) {
        try {
            dao.setPlaylistSeen(id, idUse);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public Long getPlaylistUnseen(Long idUse, Long idGro) {
        try {
            return dao.getPlaylistUnseen(idUse, idGro);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return -1l;
        }
    }

    public void addPlaylist(Playlist playlist) {
        try {
            dao.addPlaylist(playlist);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void saveLogData(LogData item) {
        try {
            dao.saveLogData(item);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List getPlaylistDataAll(Long id) {
        try {
            return dao.getPlaylistDataAll(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getGameDataAll(Long id) {
        try {
            return dao.getGameDataAll(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getGameAll(Long groupId, String seasonIds, Long compId, Long teamId, Date from, Date to, String referee, Long matchdayId, String language) {
        try {
            return dao.getGameAll(groupId, seasonIds, compId, teamId, from, to, referee, matchdayId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getGameAllDigital(Long groupId, String seasonIds, Long compId, Long teamId, Date from, Date to, String referee, Long matchdayId) {
        try {
            return dao.getGameAllDigital(groupId, seasonIds, compId, teamId, from, to, referee, matchdayId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Game getGame(Long id, String language) {
        try {
            return dao.getGame(id, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<Game> getGamesByFixtureIds(List<Long> ids, Long groupsetId, String language) {
        try {
            return dao.getGamesByFixtureIds(ids, groupsetId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Game getGameByFixtureId(Long idFixture, String language) {
        try {
            return dao.getGameByFixtureId(idFixture, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getGameByCompetitionId(Long compId, String seasonIds, Long groupsetId, Long startFrom, Boolean source, Long countryId, Long groupId, String language) {
        try {
            return dao.getGameByCompetitionId(compId, seasonIds, groupsetId, startFrom, source, countryId, groupId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getGameByTeamId(Long compId, String seasonIds, Long groupsetId, Long teamId, Long startFrom, Boolean source, String language, Integer limit, Long groupId) {
        try {
            return dao.getGameByTeamId(compId, seasonIds, groupsetId, teamId, startFrom, source, language, limit, groupId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getGameMulti(Long compId, String seasonIds, Long groupId, Long teamId, Long dayId, Boolean source, String language) {
        try {
            return dao.getGameMulti(compId, seasonIds, groupId, teamId, dayId, source, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getGameByMultiFilter(Map<String, String> mapFilters, Long startFrom, Boolean source, String language) {
        try {
            return dao.getGameByMultiFilter(mapFilters, startFrom, source, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getGameBySeason(String seasonIds, Long groupId, Long startFrom, Boolean source, String language) {
        try {
            return dao.getGameBySeason(seasonIds, groupId, startFrom, source, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public CalendarRange getMinAndMaxMatchdays() {
        try {
            return dao.getMinAndMaxMatchdays();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public CalendarRange getMinAndMaxMatchdaysByCompetitionAndSeason(String seasonIds, Long idComp, Long countryId, Long groupId, Boolean personalSource) {
        try {
            return dao.getMinAndMaxMatchdaysByCompetitionAndSeason(seasonIds, idComp, countryId, groupId, personalSource);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getSeasonAll() {
        try {
            return dao.getSeasonAll();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public Season getSeasonById(Long seasonId) {
        try {
            return dao.getSeasonById(seasonId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getSeasonAllAvailable(Long userId, Long groupset_id, List<Competition> allowedCompetitions, Long groupId, List<UserCompetitionException> competitionExceptions, Boolean personalSource) {
        try {
            return dao.getSeasonAllAvailable(userId, groupset_id, allowedCompetitions, groupId, competitionExceptions, personalSource);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List getSeasonAllAvailableByParams(Long userId, Long groupsetId, Long countryId, Long intCompId, Long competitionId, Long teamId, List<Competition> allowedCompetitions, Long groupId, List<UserCompetitionException> competitionExceptions, Boolean personalSource) {
        try {
            return dao.getSeasonAllAvailableByParams(userId, groupsetId, countryId, intCompId, competitionId, teamId, allowedCompetitions, groupId, competitionExceptions, personalSource);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List getSeasonAllAvailableByPlayerId(Long playerId, Long groupsetId, List<UserCompetitionException> competitionExceptions, Boolean personalSource) {
        try {
            return dao.getSeasonAllAvailableByPlayerId(playerId, groupsetId, competitionExceptions, personalSource);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Season getLastSeason(Long userId, Long groupsetId) {
        try {
            return dao.getLastSeason(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Season getLastSeasonByCompetitionId(Long competitionId) {
        try {
            return dao.getLastSeasonByCompetitionId(competitionId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<Country> getInternationalCompetitionAll(String seasonIds, Long idSport, String language) {
        try {
            return dao.getInternationalCompetitionAll(seasonIds, idSport, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<Country> getInternationalCompetitionVisible(Long idGroupset, String seasonIds, Long idSport, String language) {
        try {
            return dao.getInternationalCompetitionVisible(idGroupset, seasonIds, idSport, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Country getInternationalCompetition(Long id, String language) {
        try {
            return dao.getInternationalCompetition(id, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List<Country> getCountryAll(String seasonIds, Long idSport, Long idGroupset, String allowedCompetitionIds, String language) {
        try {
            return dao.getCountryAll(seasonIds, idSport, idGroupset, allowedCompetitionIds, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<Country> getBaseCountry(String seasonIds, Long idSport, String language) {
        try {
            return dao.getBaseCountry(seasonIds, idSport, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<Country> getCountryVisible(Long idGroupset, String seasonIds, Long idSport, String language) {
        try {
            return dao.getCountryVisible(idGroupset, seasonIds, idSport, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Country getCountry(Long id, String language) {
        try {
            return dao.getCountry(id, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getCompetitionAll(Long idGroupset, String seasonIds, Boolean personalSource, Long idSport, List<Competition> allowedCompetitions, String language) {
        try {
            return dao.getCompetitionAll(idGroupset, seasonIds, personalSource, idSport, allowedCompetitions, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getCompetitionByCountry(Long idGroupset, String seasonIds, Boolean personal, Long idSport, String language, Long idCountry, Long idInternationalCompetition, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) {
        try {
            return dao.getCompetitionByCountry(idGroupset, seasonIds, personal, idSport, language, idCountry, idInternationalCompetition, allowedCompetitions, competitionExceptions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<Competition> getInternationalCompetitionByCountry(Long idGroupset, String seasonIds, Long idSport, String language, Long idCountry, List<UserCompetitionException> competitionExceptions) {
        try {
            return dao.getInternationalCompetitionByCountry(idGroupset, seasonIds, idSport, language, idCountry, competitionExceptions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getCompetitionByGroupsetId(Long idGroupset, String language) {
        try {
            return dao.getCompetitionByGroupsetId(idGroupset, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getCompetitionBySeasons(Long idGroupset, String seasonIds, String language) {
        try {
            return dao.getCompetitionBySeasons(idGroupset, seasonIds, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getCompetitionByTeam(Long idGroupset, String seasonIds, Long teamId, Boolean source, List<Competition> allowedCompetitions, String language) {
        try {
            return dao.getCompetitionByTeam(idGroupset, seasonIds, teamId, source, allowedCompetitions, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getCompetitionByTeamPlayer(String seasonIds, Long playerId, Long teamId, Long groupsetId, Long groupId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException {
        try {
            return dao.getCompetitionByTeamPlayer(seasonIds, playerId, teamId, groupsetId, groupId, personalSource, allowedCompetitions, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getCompetitionByPlayer(String seasonIds, Long playerId, Long groupsetId, Long groupId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException {
        try {
            return dao.getCompetitionByPlayer(seasonIds, playerId, groupsetId, groupId, personalSource, allowedCompetitions, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Competition getCompetition(Long id, String language) {
        try {
            return dao.getCompetition(id, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public List<Competition> getCompetitions(String ids, String language) {
        try {
            return dao.getCompetitions(ids, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public FixturePlaylistSize getFixturePlaylistSize(boolean fromUser, Long userId, Long groupsetId, String idSeasons, Long minSeason) {
        try {
            return dao.getFixturePlaylistSize(fromUser, userId, groupsetId, idSeasons, minSeason);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getTeamAll(String language) {
        try {
            return dao.getTeamAll(language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public Team getTeam(Long id, String language) {
        try {
            return dao.getTeam(id, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public List<Team> getTeams(String ids, String language) {
        try {
            return dao.getTeams(ids, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getTeamByGroupsetId(Long idGroupset, String language, List<Competition> allowedCompetitions) {
        try {
            return dao.getTeamByGroupsetId(idGroupset, language, allowedCompetitions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List getNationalTeam(Long idSport, String seasonIds, Long idCountry, String language) {
        try {
            return dao.getNationalTeam(idSport, seasonIds, idCountry, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List getNationalTeamByGroupsetId(Long idGroupset, String seasonIds, String language, Long idCountry, List<Competition> allowedCompetitions) {
        try {
            return dao.getNationalTeamByGroupsetId(idGroupset, seasonIds, language, idCountry, allowedCompetitions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getTeamByCompetition(String seasonIds, Long compId, Long idGro, Long sportId, Boolean personalSource, String gameIds, Long countryId, List<Competition> allowedCompetitions, Long groupId, String language) {
        try {
            return dao.getTeamByCompetition(seasonIds, compId, idGro, sportId, personalSource, gameIds, countryId, allowedCompetitions, groupId, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getTeamByMultiFilter(String seasonIds, String compIds, String language) {
        try {
            return dao.getTeamByMultiFilter(seasonIds, compIds, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getTeamByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language, List<UserCompetitionException> competitionExceptions) {
        try {
            return dao.getTeamByPattern(pattern, sportId, seasonIds, groupsetId, personalSource, allowedCompetitions, language, competitionExceptions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public List getCompetitionByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language, List<UserCompetitionException> competitionExceptions) {
        try {
            return dao.getCompetitionByPattern(pattern, sportId, seasonIds, groupsetId, personalSource, allowedCompetitions, language, competitionExceptions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public List<Country> getCountryByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language, List<UserCompetitionException> competitionExceptions) {
        try {
            return dao.getCountryByPattern(pattern, sportId, seasonIds, groupsetId, personalSource, allowedCompetitions, language, competitionExceptions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public Long getUserDownload(Long idUser) {
        try {
            return dao.getUserDownload(idUser);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public Long getUserXmlJsonExport(Long userId) {
        try {
            return dao.getUserXmlJsonExport(userId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Atleta getAtletaById(String seasonIds, Long playerId, Long teamId, Long groupsetId, boolean personalSource, String language) {
        try {
            return dao.getAtletaById(seasonIds, playerId, teamId, groupsetId, personalSource, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getAtletiByGame(Long idFixture, Long idTeamH, Long idTeamA, Long groupsetId, String language) {
        try {
            return dao.getAtletiByGame(idFixture, idTeamH, idTeamA, groupsetId, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getAtletiByTeam(String seasonIds, Long idTeam, Long idGroupset, Boolean personalSource, Boolean checkPlaytime, String language) {
        try {
            return dao.getAtletiByTeam(seasonIds, idTeam, idGroupset, personalSource, checkPlaytime, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getAtletiByTeamAndCompetition(String seasonIds, Long idTeam, Long idCompetition, Long idGroupset, Long groupId, Boolean personalSource, Boolean checkPlaytime, String language) {
        try {
            return dao.getAtletiByTeamAndCompetition(seasonIds, idTeam, idCompetition, idGroupset, groupId, personalSource, checkPlaytime, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getAtletiSelezionati(Long idFixture, ArrayList<Long> players, Long idTeamH, Long idTeamA, String language) {
        try {
            return dao.getAtletiSelezionati(idFixture, players, idTeamH, idTeamA, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public Atleta getAtleta(Long playerId, String language) {
        try {
            return dao.getAtleta(playerId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List<Atleta> getAtletiByIds(String ids, String language) {
        try {
            return dao.getAtletiByIds(ids, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getAtletiByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<UserCompetitionException> competitionExceptions) {
        try {
            return dao.getAtletiByPattern(pattern, sportId, seasonIds, groupsetId, personalSource, competitionExceptions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getAtletiPersonalByPattern(String pattern, Long userId, Long groupsetId) {
        try {
            return dao.getAtletiPersonalByPattern(pattern, userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getAtletiByRole(Long idFixture, Long roleId, Long teamId, String language) {
        try {
            return dao.getAtletiByRole(idFixture, roleId, teamId, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public Map getFasce(Long idConfiguration) {
        try {
            return dao.getFasce(idConfiguration);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public Map getIntervalli() {
        try {
            return dao.getIntervalli();

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public FilterGameWrapper readPlayerEventMatches(Long idPlayer, String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions/*, String eventTypeFilter*/, Long userId, Long groupId, User curUser, String language) {
        try {
            return dao.readPlayerEventMatches(idPlayer, idFixtures, games, sportId, groupsetId, allowedCompetitions/*,eventTypeFilter*/, userId, groupId, curUser, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public FilterGameWrapper readTeamEventMatches(Long teamId, String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, User curUser, String language) {
        try {
            return dao.readTeamEventMatches(teamId, idFixtures, games, sportId, groupsetId, allowedCompetitions, userId, groupId, curUser, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public FilterGameWrapper readPlayerBestEvents(Long playerId, Long teamId, Long competitionId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, String seasonIds, String language) {
        try {
            return dao.readPlayerBestEvents(playerId, teamId, competitionId, groupsetId, allowedCompetitions, userId, groupId, seasonIds, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public FilterGameWrapper readPlayerHighlights(Long playerId, Long teamId, Long competitionId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, String seasonIds, String language) {
        try {
            return dao.readPlayerHighlights(playerId, teamId, competitionId, groupsetId, allowedCompetitions, userId, groupId, seasonIds, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public FilterGameWrapper readEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions/*, String eventTypeFilter*/, Long userId, Long groupId, User curUser, String language) {
        try {
            return dao.readEventMatches(idFixtures, games, sportId, groupsetId, allowedCompetitions/*,eventTypeFilter*/, userId, groupId, curUser, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public FilterGameWrapper readEventMatchesByEventIds(String eventIds, Map<Long, Game> games, Long sportId, Long groupsetId, Long playlistTvId, List<Competition> allowedCompetitions, User curUser, String language) {
        try {
            return dao.readEventMatchesByEventIds(eventIds, games, sportId, groupsetId, playlistTvId, allowedCompetitions, curUser, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public FilterGameWrapper readHighlightsEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions/*, String eventTypeFilter*/, Long userId, Long groupId, String seasonIds, String language) {
        try {
            return dao.readHighlightsEventMatches(idFixtures, games, sportId, groupsetId, allowedCompetitions/*,eventTypeFilter*/, userId, groupId, seasonIds, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public FilterGameWrapper readIPOEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, Long teamId, List<Competition> allowedCompetitions/*, String eventTypeFilter*/, Long userId, Long groupId, String seasonIds, String language) {
        try {
            return dao.readIPOEventMatches(idFixtures, games, sportId, groupsetId, teamId, allowedCompetitions/*,eventTypeFilter*/, userId, groupId, seasonIds, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public LinkedHashMap<String, Azione> getActionsByIdEvent(Long idFixture, ArrayList<String> idEvents, Long sportId, List<Competition> allowedCompetitions, Long userId, Long groupsetId, String language) {
        try {
            return dao.getActionsByIdEvent(idFixture, idEvents, sportId, allowedCompetitions, userId, groupsetId, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public List<Azione> getActionsByEventIds(String eventIds, Long sportId, List<Competition> allowedCompetitions, String language) {
        try {
            return dao.getActionsByEventIds(eventIds, sportId, allowedCompetitions, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getActionsAvailable(boolean conTag, boolean soloSenzaTag, Long teamId, Long playerId, String seasonIds, Long competitionId, String fixtureId, Long groupsetId, Boolean personalSource, String gameIds, String event, List<Competition> allowedCompetitions, Long groupId, User curUser, String language) {
        try {
            return dao.getActionsAvailable(conTag, soloSenzaTag, teamId, playerId, seasonIds, competitionId, fixtureId, groupsetId, personalSource, gameIds, event, allowedCompetitions, groupId, curUser, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public List<Azione> getActionsAvailableForCompetition(boolean conTag, String seasonIds, Long competitionId, Long teamId, Long playerId, Long groupsetId, String event, String from, String to) {
        try {
            return dao.getActionsAvailableForCompetition(conTag, seasonIds, competitionId, teamId, playerId, groupsetId, event, from, to);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public FilterGameWrapper getActionsByType(String idGame, String event, String filter, Long idComp, Long teamId, Long playerId, String seasonIds, Long limit, Long groupsetId, Long sportId, List<Competition> allowedCompetitions , String gameIds, Long userId, Long groupId, String from, String to, Boolean allSeasons, User curUser, String language) {
        try {
            return dao.getActionsByType(idGame, event, filter, idComp, teamId, playerId, seasonIds, limit, groupsetId, sportId, allowedCompetitions, gameIds, userId, groupId, from, to, allSeasons, curUser, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public FilterGameWrapper getActionsSingleMatchByType(String idGame, String event, String filter, Long idComp, Long teamId, Long playerId, String seasonIds, Long limit, Long groupsetId, Long sportId, List<Competition> allowedCompetitions, Long userId, Long groupId, String language) {
        try {
            return dao.getActionsSingleMatchByType(idGame, event, filter, idComp, teamId, playerId, seasonIds, limit, groupsetId, sportId, allowedCompetitions, userId, groupId, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public Map<String, List> getPanel(String idPanels, Long idGroupset, String lang) {
        try {
            return dao.getPanel(idPanels, idGroupset, lang);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class
                    .getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public Map<String, List> getPanelByName(String name, Long idGroupset, String language) {
        try {
            return dao.getPanelByName(name, idGroupset, language);

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }
    
    public Map<String, List> getPanelById(Long id, String language) {
        try {
            return dao.getPanelById(id, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public Map<String, List> getPanelsByGroupset(Long idGroupset, String language) {
        try {
            return dao.getPanelsByGroupset(idGroupset, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);

            return null;
        }
    }

    public List getModuli() {
        try {
            return dao.getModuli();

        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Long addPlaylistTv(PlaylistTV playlistTV) {
        try {
            return dao.addPlaylistTv(playlistTV);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public void addPlaylistTvEvents(String values) {
        try {
            dao.addPlaylistTvEvents(values);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<PlaylistTV> getPlaylistTvByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getPlaylistTvByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<PlaylistTV> getPlaylistTvWithCounterByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getPlaylistTvWithCounterByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void deletePlaylistTvById(Long playlistId) {
        try {
            dao.deletePlaylistTvById(playlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<PlaylistTVEvent> getPlaylistTvEventsById(Long playlistId) {
        try {
            return dao.getPlaylistTvEventsById(playlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<PlaylistTVEvent> getPlaylistTvEventsDeletedById(Long playlistId) {
        try {
            return dao.getPlaylistTvEventsDeletedById(playlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public PlaylistTV getPlaylistTvById(Long playlistId) {
        try {
            return dao.getPlaylistTvById(playlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void updatePlaylistTv(PlaylistTV playlistTV) {
        try {
            dao.updatePlaylistTv(playlistTV);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void updatePlaylistShare(PlaylistTVShare playlistTVShare) {
        try {
            dao.updatePlaylistShare(playlistTVShare);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void updatePlaylistTvEvent(PlaylistTVEvent event) {
        try {
            dao.updatePlaylistTvEvent(event);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void deletePlaylistTvEvents(Long playlistId, String eventIds, Long updateBy, Timestamp updateDate, String updateDescription) {
        try {
            dao.deletePlaylistTvEvents(playlistId, eventIds, updateBy, updateDate, updateDescription);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void permanentlyDeletePlaylistTvEvents(Long playlistId, String eventIds) {
        try {
            dao.permanentlyDeletePlaylistTvEvents(playlistId, eventIds);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
//	public List getScadenzaFromSerialNumber(String serialnumber) throws SQLException {
//		try {
//			return dao.getScadenzaFromSerialNumber(serialnumber);
//		} catch (SQLException ex) {
//			Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
//			return null;
//		}
//	}
    
    public Groupset getGroupsetById(Long groupsetId) {
        try {
            return dao.getGroupsetById(groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<User> getUserListByGroupsetId(Long groupsetId) {
        try {
            return dao.getUserListByGroupsetId(groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<PlaylistTVShare> getAllPlaylistTVShareByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getAllPlaylistTVShareByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void addPlaylistTVShare(Long userId, Long playlistId, boolean editable) {
        try {
            dao.addPlaylistTVShare(userId, playlistId, editable);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void deletePlaylistTVShare(Long playlistId) {
        try {
            dao.deletePlaylistTVShare(playlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<PlaylistTV> getSharedPlaylistTvByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getSharedPlaylistTvByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<PlaylistTV> getSharedPlaylistTvWithCounterByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getSharedPlaylistTvWithCounterByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<PlaylistTV> getSharingPlaylistTvByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getSharingPlaylistTvByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public PlaylistTVShare getSharedPlaylistTvByUserIdAndPlaylistId(Long userId, Long groupsetId, Long playlistId) {
        try {
            return dao.getSharedPlaylistTvByUserIdAndPlaylistId(userId, groupsetId, playlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<PlaylistTVShare> getAllSharedPlaylistTvByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getAllSharedPlaylistTvByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public PlaylistTVSharePublic getPlaylistTvSharePublicByUrl(String url) {
        try {
            return dao.getPlaylistTvSharePublicByUrl(url);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public PlaylistTVSharePublic getPlaylistTvSharePublicByUserIdAndPlaylistId(Long userId, Long playlistId) {
        try {
            return dao.getPlaylistTvSharePublicByUserIdAndPlaylistId(userId, playlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void updatePlaylistTvSharePublicClick(Long id, Long click) {
        try {
            dao.updatePlaylistTvSharePublicClick(id, click);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<PlaylistTVSharePublic> getAllPlaylistTvPublicShareByUserId(Long userId) {
        try {
            return dao.getAllPlaylistTvPublicShareByUserId(userId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void addPlaylistTvSharePublic(PlaylistTVSharePublic share) {
        try {
            dao.addPlaylistTvSharePublic(share);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void removePlaylistTvSharePublic(Long id) {
        try {
            dao.removePlaylistTvSharePublic(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public Season getLastSeasonForCountryId(Long userId, Long groupsetId, Long countryId, String allowedCompetitionIds, List<UserCompetitionException> competitionExceptions) {
        try {
            return dao.getLastSeasonForCountryId(userId, groupsetId, countryId, allowedCompetitionIds, competitionExceptions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public Season getLastSeasonForCountryIdWithNationalTeams(Long userId, Long groupsetId, Long countryId, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) {
        try {
            return dao.getLastSeasonForCountryIdWithNationalTeams(userId, groupsetId, countryId, allowedCompetitions, competitionExceptions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public Season getLastSeasonForInternationalCompetitionId(Long userId, Long groupsetId, Long intCompId, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) {
        try {
            return dao.getLastSeasonForInternationalCompetitionId(userId, groupsetId, intCompId, allowedCompetitions, competitionExceptions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public PlaylistTVEvent getPlaylistTvLastUpdateEventById(Long playlistId) {
        try {
            return dao.getPlaylistTvLastUpdateEventById(playlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Game> getGameIdsListByFilters(String from, String where) {
        try {
            return dao.getGameIdsListByFilters(from, where);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<StatsType> getAllStatsType(String language, Integer availability, String specificLanguage) {
        try {
            return dao.getAllStatsType(language, availability, specificLanguage);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getTeamStatsByCompetitionId(Long competitionId, Long countryId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) {
        try {
            return dao.getTeamStatsByCompetitionId(competitionId, countryId, gameIds, tableType, filterId, seasonIds, groupId, from, to, language, specificLanguage, isTabellino);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getPlayerStatsByCompetitionId(Long competitionId, Long countryId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) {
        try {
            return dao.getPlayerStatsByCompetitionId(competitionId, countryId, gameIds, tableType, filterId, seasonIds, groupId, from, to, language, specificLanguage, isTabellino);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getTeamStatsByCompetitionIdAndTeamId(Long competitionId, Long countryId, Long teamId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) {
        try {
            return dao.getTeamStatsByCompetitionIdAndTeamId(competitionId, countryId, teamId, gameIds, tableType, filterId, seasonIds, groupId, from, to, language, specificLanguage, isTabellino);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getTeamStatsByCompetitionIdAndTeamIdForPlayers(Long competitionId, Long countryId, Long teamId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) {
        try {
            return dao.getTeamStatsByCompetitionIdAndTeamIdForPlayers(competitionId, countryId, teamId, gameIds, tableType, filterId, seasonIds, groupId, from, to, language, specificLanguage, isTabellino);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getPlayerStatsByPlayerIds(String playerIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, Boolean isGrouped, String language, String specificLanguage) {
        try {
            return dao.getPlayerStatsByPlayerIds(playerIds, tableType, filterId, seasonIds, groupId, from, to, isGrouped, language, specificLanguage);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Long addTeamStatsFilter(TeamStatsFilter filter) {
        try {
            return dao.addTeamStatsFilter(filter);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void addTeamStatsFilterDetails(List<TeamStatsFilterDetail> filterDetailList) {
        try {
            dao.addTeamStatsFilterDetails(filterDetailList);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void deleteTeamStatsFilterDetails(Long statsTypeFilterId) {
        try {
            dao.deleteTeamStatsFilterDetails(statsTypeFilterId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<TeamStatsFilter> getTeamStatsFilterByUserId(Long userId, Long groupsetId, String tableType, String language) {
        try {
            return dao.getTeamStatsFilterByUserId(userId, groupsetId, tableType, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStatsFilterDetail> getTeamStatsFilterDetailsByFilterId(Long filterId) {
        try {
            return dao.getTeamStatsFilterDetailsByFilterId(filterId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public TeamStatsFilter getTeamStatsFilterById(Long filterId, String language) {
        try {
            return dao.getTeamStatsFilterById(filterId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void updateTeamStatsFilter(TeamStatsFilter filter) {
        try {
            dao.updateTeamStatsFilter(filter);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void updateTeamStatsFilterPreferred(Long isPreferred, String where) {
        try {
            dao.updateTeamStatsFilterPreferred(isPreferred, where);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void deleteTeamStatsFilter(TeamStatsFilter filter) {
        try {
            dao.deleteTeamStatsFilter(filter);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<TeamStats> getPlayerStatsByPlayerId(Long playerId, String competitions, String teams, String seasonIds, Long positionId, String statsTypeIds, Boolean averageCompetition, Long groupId, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) {
        try {
            return dao.getPlayerStatsByPlayerId(playerId, competitions, teams, seasonIds, positionId, statsTypeIds, averageCompetition, groupId, gameIds, language, specificLanguage, from, to, isTabellino);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getPlayerStatsByPlayerIdGroupByFixture(Long playerId, String competitions, String teams, String seasonIds, Long positionId, String statsTypeIds, Long groupId, String language, String specificLanguage, String from, String to, boolean isTabellino) {
        try {
            return dao.getPlayerStatsByPlayerIdGroupByFixture(playerId, competitions, teams, seasonIds, positionId, statsTypeIds, groupId, language, specificLanguage, from, to, isTabellino);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getPlayerStatsByFilters(String competitions, String seasonIds, Long positionId, String statsTypeIds, String language) {
        try {
            return dao.getPlayerStatsByFilters(competitions, seasonIds, positionId, statsTypeIds, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getPlayerStatsByFiltersExcludedPlayer(Long playerId, String competitions, String seasonIds, Long positionId, String statsTypeIds, Boolean averageCompetition, Long groupId, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) {
        try {
            return dao.getPlayerStatsByFiltersExcludedPlayer(playerId, competitions, seasonIds, positionId, statsTypeIds, averageCompetition, groupId, gameIds, language, specificLanguage, from, to, isTabellino);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getPlayerStatsResumeCareer(Long playerId, String language, String specificLanguage) {
        try {
            return dao.getPlayerStatsResumeCareer(playerId, language, specificLanguage);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Game> getModuleAmountByTeam(Long teamId, Long compId, String seasonIds, Long groupId, String from, String to) {
        try {
            return dao.getModuleAmountByTeam(teamId, compId, seasonIds, groupId, from, to);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamReportRanking> getTeamRankingBySeasonAndCompetition(String seasonIds, String competitions, Long groupId, String language, String from, String to) {
        try {
            return dao.getTeamRankingBySeasonAndCompetition(seasonIds, competitions, groupId, language, from, to);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getPlayerStatsByTeamAndCompetition(Long teamId, String seasonIds, String competitions, Long groupId, String language, String specificLanguage, String from, String to) {
        try {
           return dao.getPlayerStatsByTeamAndCompetition(teamId, seasonIds, competitions, groupId, language, specificLanguage, from, to);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getTeamStatsByCompetition(String seasonIds, String competitions, String statsTypeIdsForTables, Long groupId, Boolean groupedByCompetition, String language, String specificLanguage, String from, String to) {
        try {
            return dao.getTeamStatsByCompetition(seasonIds, competitions, statsTypeIdsForTables, groupId, groupedByCompetition, language, specificLanguage, from, to);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getDefaultTeamStatsByCompetition(String seasonIds, String competitions, Long groupId, Boolean groupedByCompetition, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) {
        try {
            return dao.getDefaultTeamStatsByCompetition(seasonIds, competitions, groupId, groupedByCompetition, gameIds, language, specificLanguage, from, to, isTabellino);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getTeamStatsByCompetitionAndPosition(String seasonIds, String competitions, Long positionId, String statsTypeIds, Long groupId, Boolean groupedByCompetition, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) {
        try {
            return dao.getTeamStatsByCompetitionAndPosition(seasonIds, competitions, positionId, statsTypeIds, groupId, groupedByCompetition, gameIds, language, specificLanguage, from, to, isTabellino);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Atleta> getAtletiJerseyAndRoleByFilters(String seasonIds, String teamId, String playerId, String language) {
        try {
            return dao.getAtletiJerseyAndRoleByFilters(seasonIds, teamId, playerId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getTeamPlayerAmountByCompetition(String seasonIds, String competitions, String language) {
        try {
            return dao.getTeamPlayerAmountByCompetition(seasonIds, competitions, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public TeamStats getCompetitionMinutesPlayed(String seasonIds, String competitions) {
        try {
            return dao.getCompetitionMinutesPlayed(seasonIds, competitions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Position getPositionById(Long positionId, String language) {
        try {
            return dao.getPositionById(positionId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Position> getPositionByIds(String positionIds, String language) {
        try {
            return dao.getPositionByIds(positionIds, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Long getGameAmountByCompetitions(String competitionIds, Long groupId, String seasonIds, String from, String to, boolean isTabellino) {
        try {
            return dao.getGameAmountByCompetitions(competitionIds, groupId, seasonIds, from, to, isTabellino);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Team getLastTeamForPlayer(Long playerId, String language) {
        try {
            return dao.getLastTeamForPlayer(playerId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Team> getLastTeamForPlayers(String playerIds, String language) {
        try {
            return dao.getLastTeamForPlayers(playerIds, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Long getFixtureByGameId(Long gameId) {
        try {
            return dao.getFixtureByGameId(gameId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Long> getFixturesByGameIds(String gameIds) {
        try {
            return dao.getFixturesByGameIds(gameIds);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Watchlist> getWatchlistByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getWatchlistByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Watchlist getWatchlistById(Long watchlistId) {
        try {
            return dao.getWatchlistById(watchlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<WatchlistDetail> getWatchlistDetailByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getWatchlistDetailByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<WatchlistDetail> getWatchlistDetailById(Long detailId) {
        try {
            return dao.getWatchlistDetailById(detailId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Long addWatchlist(Watchlist watchlist) {
        try {
            return dao.addWatchlist(watchlist);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public void addWatchlistDetail(WatchlistDetail detail) {
        try {
            dao.addWatchlistDetail(detail);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void removePlayerFromWatchlist(Long playerId, Long watchlistId) {
        try {
            dao.removePlayerFromWatchlist(playerId, watchlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void updateWatchlist(Watchlist watchlist) {
        try {
            dao.updateWatchlist(watchlist);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void deleteWatchlist(Watchlist watchlist) {
        try {
            dao.deleteWatchlist(watchlist);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<WatchlistShare> getAllWatchlistShareByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getAllWatchlistShareByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void deleteWatchlistShare(Long watchlistId) {
        try {
            dao.deleteWatchlistShare(watchlistId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void addWatchlistShare(Long userId, Long watchlistId, boolean editable) {
        try {
            dao.addWatchlistShare(userId, watchlistId, editable);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<Watchlist> getWatchlistSharedWithUserId(Long userId, Long groupsetId) {
        try {
            return dao.getWatchlistSharedWithUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<WatchlistDetail> getWatchlistDetailSharedWithUserId(Long userId, Long groupsetId) {
        try {
            return dao.getWatchlistDetailSharedWithUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Favorites> getFavoritesByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getFavoritesByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void addFavorites(Favorites favorites) {
        try {
            dao.addFavorites(favorites);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void deleteFavorites(Long id) {
        try {
            dao.deleteFavorites(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<Shortcut> getShortcutsByUserId(Long userId, Long groupsetId) {
        try {
            return dao.getShortcutsByUserId(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void addShortcut(Shortcut shortcut) {
        try {
            dao.addShortcut(shortcut);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void deleteShortcut(Long id) {
        try {
            dao.deleteShortcut(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void deleteShortcuts(String ids) {
        try {
            dao.deleteShortcuts(ids);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public Settings getSettingsByUserId(Long userId) {
        try {
            return dao.getSettingsByUserId(userId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void addSettings(Settings settings) {
        try {
            dao.addSettings(settings);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void updateSettings(Settings settings) {
        try {
            dao.updateSettings(settings);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<Long> getUserIdFromVtigerAgentId(Long agentId) {
        try {
            return dao.getUserIdFromVtigerAgentId(agentId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Long addExport(Export export) {
        try {
            return dao.addExport(export);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void addExportDetail(String values) {
        try {
            dao.addExportDetail(values);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void updateExport(Export export) {
        try {
            dao.updateExport(export);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<Export> getExportByUser(Long userId, Long groupsetId) {
        try {
            return dao.getExportByUser(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<ExportDetail> getExportDetailByUser(Long userId, Long groupsetId) {
        try {
            return dao.getExportDetailByUser(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Long getPlayerInLineupAmount(Long playerId, Long competitionId, Long teamId, String seasonIds, Long groupsetId, List<Competition> allowedCompetitions) {
        try {
            return dao.getPlayerInLineupAmount(playerId, competitionId, teamId, seasonIds, groupsetId, allowedCompetitions);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Long getUserExportLimit(Long userId) {
        try {
            return dao.getUserExportLimit(userId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Long getUserExportTimeUsed(Long userId) {
        try {
            return dao.getUserExportTimeUsed(userId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Team> getCompetitionTeamGironi(Long competitionId, String seasonIds, Long groupId, String language) {
        try {
            return dao.getCompetitionTeamGironi(competitionId, seasonIds, groupId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Competition> getTeamGironi(Long competitionId, Long teamId, String seasonIds, List<Competition> allowedCompetitions, String language, Locale userLanguage) {
        try {
            return dao.getTeamGironi(competitionId, teamId, seasonIds, allowedCompetitions, language, userLanguage);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Competition> getPlayerGironi(Long playerId, Long competitionId, Long teamId, String seasonIds, List<Competition> allowedCompetitions, String language, Locale userLanguage) {
        try {
            return dao.getPlayerGironi(playerId, competitionId, teamId, seasonIds, allowedCompetitions, language, userLanguage);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Team getGironeById(Long id, String language, Locale userLanguage) {
        try {
            return dao.getGironeById(id, language, userLanguage);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getTeamGameAmountByCompetitionId(Long competitionId, Long countryId, String gameIds, String seasonIds, Long groupId, Date from, Date to, String language) {
        try {
            return dao.getTeamGameAmountByCompetitionId(competitionId, countryId, gameIds, seasonIds, groupId, from, to, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<FixtureDetails> getFixtureDetails(List<Long> fixtureIds) {
        try {
            return dao.getFixtureDetails(fixtureIds);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Azione> checkEventTactical(Long groupsetId, List<String> eventIds) {
        try {
            return dao.checkEventTactical(groupsetId, eventIds);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Atleta> getPlayerModulePositionAmount(Long playerId, Long teamId, Long competitionId, List<Competition> allowedCompetitions, Long groupId, String seasonIds, String from, String to) {
        try {
            return dao.getPlayerModulePositionAmount(playerId, teamId, competitionId, allowedCompetitions, groupId, seasonIds, from, to);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public void addPlayerStatsSummary(List<TeamStats> playerSummary) {
        try {
            dao.addPlayerStatsSummary(playerSummary);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void addPlayerStatsSummary(String values) {
        try {
            dao.addPlayerStatsSummary(values);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void deletePlayerStatsSummary(Long playerId) {
        try {
            dao.deletePlayerStatsSummary(playerId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public List<TeamStats> getPlayerStatsForSummary(Long playerId) {
        try {
            return dao.getPlayerStatsForSummary(playerId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Long> getValidPlayersForSummary() {
        try {
            return dao.getValidPlayersForSummary();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<TeamStats> getSmartSearchPlayerData(Map<String, String> filters) {
        try {
            return dao.getSmartSearchPlayerData(filters);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Cache> getSmartSearchPlayerBaseData(Long playerId, String seasonId) {
        try {
            return dao.getSmartSearchPlayerBaseData(playerId, seasonId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Cache> getSmartSearchTeamBaseData(Long teamId, String seasonId) {
        try {
            return dao.getSmartSearchTeamBaseData(teamId, seasonId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Cache> getStatisticsData(Long playerId, String seasonId, String language) {
        try {
            return dao.getStatisticsData(playerId, seasonId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Cache getSmartSearchPlayerLastTeamData(Long playerId, String seasonId) {
        try {
            return dao.getSmartSearchPlayerLastTeamData(playerId, seasonId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Cache> getSmartSearchPlayerMatchData(Long playerId, String seasonId) {
        try {
            return dao.getSmartSearchPlayerMatchData(playerId, seasonId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Long> getSeasonAvailableForPlayer(Long playerId) {
        try {
            return dao.getSeasonAvailableForPlayer(playerId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Long> getSeasonAvailableForTeam(Long teamId) {
        try {
            return dao.getSeasonAvailableForTeam(teamId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Long> getAllValidPlayers() {
        try {
            return dao.getAllValidPlayers();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Long> getAllValidTeams() {
        try {
            return dao.getAllValidTeams();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<StatsType> getStatsTypeFilters() {
        try {
            return dao.getStatsTypeFilters();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<CacheLanguageItem> getCacheCountryLanguages() {
        try {
            return dao.getCacheCountryLanguages();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<CacheLanguageItem> getCacheFootLanguages() {
        try {
            return dao.getCacheFootLanguages();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<CacheLanguageItem> getCacheStatsTypeLanguages() {
        try {
            return dao.getCacheStatsTypeLanguages();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<CacheLanguageItem> getCacheCompetitionLanguages() {
        try {
            return dao.getCacheCompetitionLanguages();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<CacheLanguageItem> getCacheTeamLanguages() {
        try {
            return dao.getCacheTeamLanguages();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<CacheLanguageItem> getCachePositionLanguages() {
        try {
            return dao.getCachePositionLanguages();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Competition> getTop5EuropeanCompetitions(String language) {
        try {
            return dao.getTop5EuropeanCompetitions(language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<Competition> getTop5SouthAmericaCompetitions(String language) {
        try {
            return dao.getTop5SouthAmericaCompetitions(language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public List<Country> getCountriesForUpload(String seasonIds, String language) {
        try {
            return dao.getCountriesForUpload(seasonIds, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<Competition> getCompetitionsForUpload(String seasonIds, String language) {
        try {
            return dao.getCompetitionsForUpload(seasonIds, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List<Team> getTeamsForUpload(String seasonIds, String language) {
        try {
            return dao.getTeamsForUpload(seasonIds, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List<Game> getFixturesUploadableByCompetition(Long competitionId, String language) {
        try {
            return dao.getFixturesUploadableByCompetition(competitionId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public void insertFixtureVideo(Map<String, Object> parameters) {
        try {
            dao.insertFixtureVideo(parameters);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public Long getPlayerIdBySerialNumber(String serialNumber) {
        try {
            return dao.getPlayerIdBySerialNumber(serialNumber);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public void updateFixtureScore(Long fixtureId, Long homeScore, Long awayScore) {
        try {
            dao.updateFixtureScore(fixtureId, homeScore, awayScore);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }
    
    public void updateCompetitionLogo(Long competitionId, String logo) {
        try {
            dao.updateCompetitionLogo(competitionId, logo);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void updateTeamLogo(Long teamId, String logo) {
        try {
            dao.updateTeamLogo(teamId, logo);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void updatePlayerPhoto(Long playerId, String photo) {
        try {
            dao.updatePlayerPhoto(playerId, photo);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void updatePlayerAgentPhoto(Long agentId, String photo) {
        try {
            dao.updatePlayerAgentPhoto(agentId, photo);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public String getFixtureFileproject(Long fixtureId) {
        try {
            return dao.getFixtureFileproject(fixtureId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public Long getGameIdByFixtureId(Long fixtureId) {
        try {
            return dao.getGameIdByFixtureId(fixtureId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public Long insertPersonalPlayer(Atleta player, Long userId, Long groupsetId) {
        try {
            return dao.insertPersonalPlayer(player, userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public void updatePersonalPlayer(Atleta player) {
        try {
            dao.updatePersonalPlayer(player);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void deletePersonalPlayer(Long id) {
        try {
            dao.deletePersonalPlayer(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List<Atleta> getPlayerPersonalAll(Long userId, Long groupsetId, Boolean all) {
        try {
            return dao.getPlayerPersonalAll(userId, groupsetId, all);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public Atleta getPlayerPersonal(Long playerId, Long userId, Long groupsetId) {
        try {
            return dao.getPlayerPersonal(playerId, userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public List<Atleta> getPlayerPersonalByIds(String ids, Long userId, Long groupsetId) {
        try {
            return dao.getPlayerPersonalByIds(ids, userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public Atleta getPlayerPersonalByPlayerId(Long playerId, Long userId, Long groupsetId) {
        try {
            return dao.getPlayerPersonalByPlayerId(playerId, userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public ShadowTeam getShadowTeam(Long id) {
        try {
            return dao.getShadowTeam(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public List<ShadowTeam> getShadowTeams(Long userId, Long groupsetId) {
        try {
            return dao.getShadowTeams(userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public List<ShadowTeamDetail> getShadowTeamDetails(Long shadowTeamId) {
        try {
            return dao.getShadowTeamDetails(shadowTeamId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public Long insertShadowTeam(ShadowTeam shadowTeam, Long userId, Long groupsetId) {
        try {
            return dao.insertShadowTeam(shadowTeam, userId, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }

    public void updateShadowTeam(ShadowTeam shadowTeam) {
        try {
            dao.updateShadowTeam(shadowTeam);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void insertShadowTeamDetails(List<ShadowTeamDetail> shadowTeamDetails) {
        try {
            dao.insertShadowTeamDetails(shadowTeamDetails);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void deleteShadowTeam(Long shadowTeamId) {
        try {
            dao.deleteShadowTeam(shadowTeamId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void deleteShadowTeamDetails(Long shadowTeamId) {
        try {
            dao.deleteShadowTeamDetails(shadowTeamId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public Long insertPersonalEvent(Map<String, String> parameters) {
        try {
            return dao.insertPersonalEvent(parameters);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public void insertEventPlayer(Long eventId, Long playerId) {
        try {
            dao.insertEventPlayer(eventId, playerId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void updatePersonalEvent(Map<String, String> parameters) {
        try {
            dao.updatePersonalEvent(parameters);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void deletePersonalEvent(Long eventId) {
        try {
            dao.deletePersonalEvent(eventId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public void deletePersonalEventPlayers(Long eventId) {
        try {
            dao.deletePersonalEventPlayers(eventId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List<Azione> getPersonalEvents(Long groupsetId, String seasonIds, List<Competition> allowedCompetitions, User curUser, String language) {
        try {
            return dao.getPersonalEvents(groupsetId, seasonIds, allowedCompetitions, curUser, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public Integer getFixturePlayerJerseyNumber(Long fixtureId, Long playerId) {
        try {
            return dao.getFixturePlayerJerseyNumber(fixtureId, playerId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    // competitionId, countryId, gameIds, model.get("mType").toString(), filterId, curUser.getSavedSeasonForQuery(), groupId, fromDate, toDate
    public Integer getFullFixtureAmount(String competitionIds, String seasonIds, Long groupId, Date from, Date to) {
        try {
            return dao.getFullFixtureAmount(competitionIds, seasonIds, groupId, from, to);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public void updateTeamColor(Long teamId, String color) {
        try {
            dao.updateTeamColor(teamId, color);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List<Atleta> getAtletiByAgentId(Long agentId, String language) {
        try {
            return dao.getAtletiByAgentId(agentId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List<PlayerAgent> getPlayerAgents(Integer sort, String language) {
        try {
            return dao.getPlayerAgents(sort, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public PlayerAgent getPlayerAgent(Long agentId, String language) {
        try {
            return dao.getPlayerAgent(agentId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public void updatePlayerAgentDetails(Long agentId, String name, String street, String location, String phone, String email, String website, Long countryId) {
        try {
            dao.updatePlayerAgentDetails(agentId, name, street, location, phone, email, website, countryId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    public List<Tags> getGroupsetEventType(String panelName, Long groupsetId) {
        try {
            return dao.getGroupsetEventType(panelName, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List<Tags> getGroupsetTagType(String panelName, Long groupsetId) {
        try {
            return dao.getGroupsetTagType(panelName, groupsetId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List<String> getLastSeasonPair() {
        try {
            return dao.getLastSeasonPair();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
}
