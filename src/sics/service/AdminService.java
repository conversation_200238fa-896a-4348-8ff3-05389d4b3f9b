package sics.service;

import java.sql.SQLException;
import java.util.Date;

import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import sics.domain.ChartData;
import sics.domain.Export;
import sics.domain.ExportDetail;
import sics.domain.Game;
import sics.domain.User;
import sics.domain.VtigerAgent;

public class AdminService extends BaseService {

    public User getUser(Long id) {
        try {
            return dao.getUser(id);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    //metodo che ritorna lista di utenti
    public List getUserAll(String name, String surname, String dateFrom, String dateTo, String application) {
        try {
            return dao.getUserAll(name, surname, dateFrom, dateTo, application);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<User> getSicsTvUsers(Long timeFilterId, Long agentId) {
        try {
            return dao.getSicsTvUsers(timeFilterId, agentId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<User> getUsers(List<Long> ids) {
        try {
            return dao.getUsers(ids);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    //Ritorna i dati di Log di tutti gli utenti
    public List getLogDataAll(Long idRes) {
        try {
            return dao.getLogDataAll(idRes);
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List getLogDataFilter(Date dateFrom, Date dateTo, Long idVideo, String userName, String action, String application) {
        try {
            return dao.getLogDataFilter(dateFrom, dateTo, idVideo, userName, action, application);
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    //Dettagli streaming utente
    public List getStreamingDet(Long id) {
        try {
            return dao.getStreamingDet(id);
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    //Dettagli download utente
    public List getDownloadDet(Long id) {
        try {
            return dao.getDownloadDet(id);
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<ChartData> getStartExportData(Integer timeFilterId, Long agentId) {
        try {
            return dao.getStartExportData(timeFilterId, agentId);
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<ChartData> getLicenseExpiredData(Integer timeFilterId, Long agentId) {
        try {
            return dao.getLicenseExpiredData(timeFilterId, agentId);
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<ChartData> getVideoDownloadData(Integer timeFilterId, Long agentId) {
        try {
            return dao.getVideoDownloadData(timeFilterId, agentId);
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }

    public List<ChartData> getVideoStreamData(Integer timeFilterId, String userIdFilter) {
        try {
            return dao.getVideoStreamData(timeFilterId, userIdFilter);
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<ChartData> getUserLogData(Long userId, String from, String to) {
        try {
            return dao.getUserLogData(userId, from, to);
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<ChartData> getUserLast100LogData(Long userId) {
        try {
            return dao.getUserLast100LogData(userId);
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<VtigerAgent> getVtigerAgents() {
        try {
            return dao.getVtigerAgents();
        } catch (SQLException ex) {
            Logger.getLogger(AdminService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<Game> getGamesByFixtureIds(List<Long> ids, Long groupsetId, String language) {
        try {
            return dao.getGamesByFixtureIds(ids, groupsetId, language);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
            return null;
        }
    }
    
    public List<Export> getExportByUserAndData(Long userId, Long groupsetId, String from, String to) {
        try {
            return dao.getExportByUserAndData(userId, groupsetId, from, to);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<ExportDetail> getExportDetailByUserAndData(Long userId, Long groupsetId, String from, String to) {
        try {
            return dao.getExportDetailByUserAndData(userId, groupsetId, from, to);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Long getUserExportLimit(Long userId) {
        try {
            return dao.getUserExportLimit(userId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public Long getUserExportTimeUsed(Long userId) {
        try {
            return dao.getUserExportTimeUsed(userId);
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
    
    public List<VtigerAgent> getUserVtigerAgent() {
        try {
            return dao.getUserVtigerAgent();
        } catch (SQLException ex) {
            Logger.getLogger(UserService.class.getName()).log(Level.SEVERE, null, ex);
        }
        return null;
    }
}
