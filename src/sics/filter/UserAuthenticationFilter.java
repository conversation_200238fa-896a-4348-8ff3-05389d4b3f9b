package sics.filter;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;
import sics.helper.GlobalHelper;
import sics.dao.service.ManagerService;
import sics.domain.Atleta;
import sics.domain.Settings;
import sics.domain.User;
import sics.listener.SessionListener;

public class UserAuthenticationFilter extends UsernamePasswordAuthenticationFilter {

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        if (!request.getMethod().equals("POST")) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        }

        String username = obtainUsername(request);
        if (username == null) {
            username = "";
        }
        String password = obtainPassword(request);
        if (password == null) {
            password = "";
        }

        username = username.trim();
        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(username, password);
        setDetails(request, authRequest);

        HttpServletRequest req = (HttpServletRequest) request;
        HttpSession session = req.getSession(true);
        ServletContext servletContext = session.getServletContext();
        WebApplicationContext ctx = WebApplicationContextUtils.getWebApplicationContext(servletContext);
        ManagerService manager = (ManagerService) ctx.getBean(GlobalHelper.kBeanAppManager);
        try {
            sics.domain.User usr = manager.getUser(username);
            if (usr != null && usr.isExpired()) {
                throw new AuthenticationServiceException(GlobalHelper.kActionLicenseExpired + ": " + usr.getDatascadenzaString());
            } else if (usr != null) {
                if (usr.getGroupsetId() != null && Long.compare(usr.getGroupsetId(), 457L) == 0) {
                    if (usr.getPlayerId() == null || Long.compare(usr.getPlayerId(), 0L) == 0) {
                        // se non ho trovato il playerId allora non permetto il login
                        throw new AuthenticationServiceException(GlobalHelper.kActionLoginError + ":Empty Player");
                    } else {
                        Atleta player = manager.getAtleta(usr.getPlayerId(), "_en");
                        if (player == null) {
                            // potremmo aver cancellato il playerId
                            throw new AuthenticationServiceException(GlobalHelper.kActionLoginError + ":Invalid Player");
                        }
                    }
                }
                initializeSession(session, req, usr);
                //SessionListener.addSession(usr, session);
            } else {
                throw new AuthenticationServiceException(GlobalHelper.kActionLoginError);
            }
        } catch (SQLException ex) {
            Logger.getLogger(UserAuthenticationFilter.class.getName()).log(Level.SEVERE, null, ex);
        }

        return this.getAuthenticationManager().authenticate(authRequest);
        // DOPO QUESTA FUNZIONE SOLITAMENTE VENIVA FATTO IL "DISPOSE" DELLA SESSIONE ATTUALE
        // QUESTO ERA DOVUTO AL FATTO CHE VENIVA APPLICATO UNA DETERMINATA STRATEGIA DI AUTENTICAZIONE
        // CHE PER IMPEDIRE ATTACCHI DI TIPO "SESSION FIXATION" CREAVA UNA NUOVA SESSIONE
        // QUESTO ERA CONFIGURATO NEL FILE "applicationContext-Security.xml" CON LA SEGUENTE CLASSE
        // org.springframework.security.web.authentication.session.SessionFixationProtectionStrategy
        // IL CODICE E' ANCORA COMMENTATO (10/01/2024)
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request,
            HttpServletResponse response, Authentication authResult)
            throws IOException, ServletException {

        super.successfulAuthentication(request, response, authResult);

        HttpServletRequest req = (HttpServletRequest) request;
        HttpSession session = req.getSession(true);
        ServletContext servletContext = session.getServletContext();
        WebApplicationContext ctx = WebApplicationContextUtils.getWebApplicationContext(servletContext);
        if (session.getAttribute(GlobalHelper.kBeanUtente) != null) {
            sics.domain.User curUser = (sics.domain.User) session.getAttribute(GlobalHelper.kBeanUtente);
            SessionListener.addSession(curUser, session);
            GlobalHelper.writeLogData(session, GlobalHelper.kActionLoginCorrect, curUser.getUsername(), curUser.getPassword(), curUser.getId());

            if (session.getAttribute(GlobalHelper.kMaxSessionDuration) == null) {
                session.setAttribute(GlobalHelper.kMaxSessionDuration, session.getMaxInactiveInterval());
            }
        } else if (session.getAttribute(GlobalHelper.kBeanUtente) == null) {
            ManagerService manager = (ManagerService) ctx.getBean(GlobalHelper.kBeanAppManager);
            try {
                User userAuth = (User) authResult.getPrincipal();
                if (userAuth != null && userAuth.getUsername() != null) {

                    // todo: bisognerebbe leggerla da tvLanguage altrimenti mette le lingue sbagliate sull'utente
                    String language = req.getLocale().getLanguage().toLowerCase();
                    if (language.equalsIgnoreCase("it")) {
                        language = "";
                    } else {
                        language = "_en";
                    }

                    User user = manager.getUser(userAuth.getUsername());
                    user.setAvailableCompetition(manager.getCompetitionByGroupsetId(user.getGroupsetId(), language));
                    user.setAvailableTeam(manager.getTeamByGroupsetId(user.getGroupsetId(), language, user.getAvailableCompetition()));
                    user.setCompetitionException(manager.getCompetitionExceptionByGroupsetId(user.getGroupsetId()));

                    if (user.getSavedSeason() == null) {
                        user.setSavedSeason(manager.getLastSeason(user.getId(), user.getGroupsetId()).getId());
                        manager.saveUser(user);
                    }

                    GlobalHelper.writeLogData(session, GlobalHelper.kActionLoginCorrect, userAuth.getUsername(), userAuth.getPassword(), user.getId());
                    Logger.getLogger(UserAuthenticationFilter.class.getName()).log(Level.INFO, "INGRESSO OK {0}", userAuth.getUsername());
                    initializeSession(session, req, user);
                    SessionListener.addSession(user, session);

                    //http://localhost:8084/sicstv/auth/directlogin.htm?usr=tzuz&psw=tzuz92&id=1954925&idComp=15&idTeam=&idPlayer=7092&goals=false&event=&filter=&limit=1
                    //http://localhost:8084/sicstv/user/video.htm?id=1915913&idComp=2&idTeam=&idPlayer=7092&goals=false&event=&filter=&limit=1
                }

            } catch (SQLException se) {
                System.out.println("Exception during userLogin: " + se.getMessage());
                session.invalidate();
            }
        }

    }

    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException failed)
            throws IOException, ServletException {

        super.unsuccessfulAuthentication(request, response, failed);

        HttpServletRequest req = (HttpServletRequest) request;
        HttpSession session = req.getSession(true);

        try {
            String[] splitExceptionMsg = failed.getMessage().split(":");
            if (splitExceptionMsg[0].trim().equals(GlobalHelper.kActionLicenseExpired)) {
                GlobalHelper.writeLogData(session, GlobalHelper.kActionLicenseExpired, obtainUsername(request), obtainPassword(request), null);
            } else {
                GlobalHelper.writeLogData(session, GlobalHelper.kActionLoginError, obtainUsername(request), obtainPassword(request), null);
            }
        } catch (Exception e) {
            System.out.println("Exception during unsuccessfulAuthentication: " + e.getMessage());
            session.invalidate();
        }
    }

    public static void initializeSession(HttpSession session, HttpServletRequest request, User user) {
        try {
            ServletContext servletContext = session.getServletContext();
            WebApplicationContext ctx = WebApplicationContextUtils.getWebApplicationContext(servletContext);
            ManagerService manager = (ManagerService) ctx.getBean(GlobalHelper.kBeanAppManager);

            if (StringUtils.isBlank(user.getTvLanguage())) {
                // in teoria sempre
                Settings userSettings = manager.getSettingsByUserId(user.getId());
                if (userSettings != null) {
                    user.setTvLanguage(userSettings.getTvLanguage());
                } else {
                    user.setTvLanguage(StringUtils.defaultIfEmpty(user.getLanguage(), "en"));

                    userSettings = new Settings();
                    userSettings.setUserId(user.getId());
                    userSettings.setApplication(1);
                    userSettings.setEventAdditionalStart(0L);
                    userSettings.setEventAdditionalEnd(0L);
                    userSettings.setTvLanguage(StringUtils.defaultIfEmpty(user.getLanguage(), "en"));
                    manager.addSettings(userSettings);
                }
            }
            String language = "_en";
            if (StringUtils.equals(user.getTvLanguage(), "it")) {
                language = "";
            }

            user.setAvailableCompetition(manager.getCompetitionByGroupsetId(user.getGroupsetId(), language));
            user.setAvailableTeam(manager.getTeamByGroupsetId(user.getGroupsetId(), language, user.getAvailableCompetition()));
            user.setCompetitionException(manager.getCompetitionExceptionByGroupsetId(user.getGroupsetId()));
            if (user.getSavedSeason() == null) {
                //UserService mService = new UserService();
                user.setSavedSeason(manager.getLastSeason(user.getId(), user.getGroupsetId()).getId());
                manager.saveUser(user);
            }

            session.setAttribute(GlobalHelper.kBeanUtente, user);
            if (session.getAttribute(GlobalHelper.kBeanLanguage) == null) {
//                String strLocale = RequestContextUtils.getLocale(request).getLanguage().toLowerCase();
//                if (strLocale.equalsIgnoreCase("it")) {
//                    session.setAttribute(GlobalHelper.kBeanLanguage, "it");
//                } else {
//                    session.setAttribute(GlobalHelper.kBeanLanguage, "en");
//                }
                session.setAttribute(GlobalHelper.kBeanLanguage, StringUtils.defaultIfEmpty(user.getTvLanguage(), "en"));
            }

            if (GlobalHelper.kServletContextPathExport.isEmpty()) {
                GlobalHelper.kServletContextPathExport = session.getServletContext().getRealPath("/export/") + File.separator;
            }

            if (GlobalHelper.kServletContextPathBin.isEmpty()) {
                GlobalHelper.kServletContextPathBin = session.getServletContext().getRealPath("/bin/") + File.separator;
            }
        } catch (SQLException ex) {
            System.out.println("Exception during initializeSession: " + ex.getMessage());
            session.invalidate();
        }
    }
}
