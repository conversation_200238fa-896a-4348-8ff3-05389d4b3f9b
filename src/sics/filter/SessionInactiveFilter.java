package sics.filter;

import com.googlecode.htmlcompressor.compressor.HtmlCompressor;
import java.io.*;
import java.util.Date;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.*;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import sics.domain.User;
import sics.helper.CharResponseWrapper;
import sics.helper.GlobalHelper;

/**
 *
 */
public class SessionInactiveFilter implements Filter {

    protected transient final Log log = LogFactory.getLog(getClass());

    private static final boolean DEBUG = true;

    // The filter configuration object we are associated with.  If
    // this value is null, this filter instance is not currently
    // configured.
    private FilterConfig filterConfig = null;

    private final int customSessionExpiredErrorCode = 901;

    public SessionInactiveFilter() {
    }

    private void doBeforeProcessing(ServletRequest request, ServletResponse response) throws IOException, ServletException {
        HttpServletRequest hsreq = (HttpServletRequest) request;
        HttpServletResponse hsres = (HttpServletResponse) response;

        //controllo sessione scaduta e c'è stata una chiamata http
        if (hsreq != null && hsreq.getSession(false) == null) {
            String ajaxHeader = ((HttpServletRequest) request).getHeader("X-Requested-With");
            if (ajaxHeader != null && ajaxHeader.equalsIgnoreCase("XMLHttpRequest")) {
                hsres.sendError(this.customSessionExpiredErrorCode);
            }
        }
    }

    /**
     *
     * @param request The servlet request we are processing
     * @param response
     * @param chain The filter chain we are processing
     *
     * @exception IOException if an input/output error occurs
     * @exception ServletException if a servlet error occurs
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
            FilterChain chain)
            throws IOException, ServletException {

        Date execStart = new Date();

        doBeforeProcessing(request, response);

        Throwable problem = null;
        try {
            ServletResponse newResponse = response;

            if (request instanceof HttpServletRequest) {
                newResponse = new CharResponseWrapper((HttpServletResponse) response);
            }

            chain.doFilter(request, newResponse);

            if (newResponse instanceof CharResponseWrapper) {
                String text = newResponse.toString();
                if (text != null) {
                    HtmlCompressor htmlCompressor = new HtmlCompressor();
                    response.getWriter().write(htmlCompressor.compress(text));
                }
            }
        } catch (IOException | ServletException t) {
            //
            // If an exception is thrown somewhere down the filter chain,
            // we still want to execute our after processing, and then
            // rethrow the problem after that.
            //
            problem = t;
            t.printStackTrace();
            return;
        }

        //doAfterProcessing(request, response);
        if (DEBUG) {
            String user = null;
            HttpServletRequest servletRequest = (HttpServletRequest) request;
            if (servletRequest != null) {
                String url = servletRequest.getRequestURL().toString();
                if (url.endsWith(".htm")) {
                    HttpSession session = servletRequest.getSession(false);
                    if (session != null) {
                        try {
                            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
                            if (curUser != null) {
                                user = "{{" + curUser.getId() + ", " + curUser.getFirstName() + "}} ";
                            }
                        } catch (Exception ex) {
                            // suppressed
                        }
                    }

                    Logger.getLogger(SessionInactiveFilter.class.getName()).log(Level.INFO, (user != null ? user : "") + url + " " + (new Date().getTime() - execStart.getTime()) + "ms");
                }
            }
        }

        //
        // If there was a problem, we want to rethrow it if it is
        // a known type, otherwise log it.
        //
        if (problem != null) {
            if (problem instanceof ServletException) {
                throw (ServletException) problem;
            }
            if (problem instanceof IOException) {
                throw (IOException) problem;
            }
            sendProcessingError(problem, response);
        }
    }

    /**
     * Return the filter configuration object for this filter.
     *
     * @return
     */
    public FilterConfig getFilterConfig() {
        return (this.filterConfig);
    }

    /**
     * Set the filter configuration object for this filter.
     *
     * @param filterConfig The filter configuration object
     */
    public void setFilterConfig(FilterConfig filterConfig) {

        this.filterConfig = filterConfig;
    }

    /**
     * Destroy method for this filter
     *
     */
    public void destroy() {
    }

    /**
     * Init method for this filter
     *
     * @param filterConfig
     */
    public void init(FilterConfig filterConfig) {

        this.filterConfig = filterConfig;
        if (filterConfig != null) {
        }
    }

    /**
     * Return a String representation of this object.
     */
    public String toString() {
        if (filterConfig == null) {
            return ("SessionInactiveFilter()");
        }
        StringBuilder sb = new StringBuilder("SessionInactiveFilter(");
        sb.append(filterConfig);
        sb.append(")");
        return (sb.toString());

    }

    private void sendProcessingError(Throwable t, ServletResponse response) {

        String stackTrace = getStackTrace(t);

        if (stackTrace != null && !stackTrace.equals("")) {

            try {

                response.setContentType("text/html");
                try (PrintStream ps = new PrintStream(response.getOutputStream()); PrintWriter pw = new PrintWriter(ps)) {
                    pw.print("<html>\n<head>\n</head>\n<body>\n"); //NOI18N

                    // PENDING! Localize this for next official release
                    pw.print("<h1>The resource did not process correctly</h1>\n<pre>\n");
                    pw.print(stackTrace);
                    pw.print("</pre></body>\n</html>"); //NOI18N
                }
                response.getOutputStream().close();
            } catch (IOException ex) {
            }
        } else {
            try {
                try (PrintStream ps = new PrintStream(response.getOutputStream())) {
                    t.printStackTrace(ps);
                }
                response.getOutputStream().close();
            } catch (IOException ex) {
            }
        }
    }

    public static String getStackTrace(Throwable t) {

        String stackTrace = null;

        try {
            StringWriter sw = new StringWriter();
            PrintWriter pw = new PrintWriter(sw);
            t.printStackTrace(pw);
            pw.close();
            sw.close();
            stackTrace = sw.getBuffer().toString();
        } catch (IOException ex) {
        }
        return stackTrace;
    }

}
