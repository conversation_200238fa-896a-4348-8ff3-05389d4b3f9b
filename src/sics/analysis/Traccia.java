/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package sics.analysis;

import java.util.ArrayList;

/**
 *
 */
public class Traccia {
    
    private String id;
    private ArrayList<DatoDS> mPosizioni;
    private Float mLength;
    public String recupero;
    private Boolean mSelected = false;

    public Traccia() {
        this.mPosizioni = new ArrayList<DatoDS>();
    }

    public ArrayList<DatoDS> getmPosizioni() {
        return mPosizioni;
    }

    public void setmPosizioni(ArrayList<DatoDS> mPosizioni) {
        this.mPosizioni = mPosizioni;
    }

    public Float getmLength() {
        return mLength;
    }

    public void setmLength(Float mLength) {
        this.mLength = mLength;
    }

    public Boolean ismSelected() {
        return mSelected;
    }

    public void setmSelected(Boolean mSelected) {
        this.mSelected = mSelected;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
}
