/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.analysis;

/**
 *
 * <AUTHOR>
 */
public class Punto implements Comparable<Punto> {

    public float x;
    public float y;

    public Punto() {
        x = -1;
        y = -1;
    }

    public Punto(String[] posSplitted) {
        x = Float.parseFloat(posSplitted[0]);
        y = Float.parseFloat(posSplitted[1]);
    }

    public Punto(float x, float y) {
        this.x = x;
        this.y = y;
    }

    public boolean isDefault() {
        return (x == -1 && y == -1);
    }

    @Override
    public int compareTo(Punto p) {
        if (this.x > p.x - 0.5 && this.x < p.x + 0.5 && this.y > p.y - 0.5 && this.y < p.y + 0.5) {
            return 0;
        } else {
            return 1;
        }
    }

    public float getX() {
        return x;
    }

    public void setX(float mX) {
        this.x = mX;
    }

    public float getY() {
        return y;
    }

    public void setY(float mY) {
        this.y = mY;
    }

    public String toXmlString() {
        String stringValue = String.format("%.2f", x) + ";" + String.format("%.2f", y);
        stringValue = stringValue.replace(",", ".");
        return stringValue;
    }

}
