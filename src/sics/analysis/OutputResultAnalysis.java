/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.analysis;

import java.util.HashMap;
import java.util.ArrayList;
import java.util.Arrays;

/**
 *
 * <AUTHOR>
 */
public class OutputResultAnalysis {

	private final ArrayList<String> asseX = new ArrayList<String>();
	private int sizeArray = 0;
	private final HashMap<String, ArrayList<Integer>> map = new HashMap<String, ArrayList<Integer>>();

	public OutputResultAnalysis(int size, String[] values) {
		this.sizeArray = size;
		if (values != null) {
			asseX.addAll(Arrays.asList(values));
		}
	}

	public ArrayList<String> getAsseX() {
		return asseX;
	}

	public void addToAsseX(String val) {
		this.asseX.add(val);
	}

//	public void addValueToMap(String key, Integer val, String axis) {
//		ArrayList<Integer> values = map.get(key);
//		if (values == null) {
//			values = new ArrayList<Integer>();
//			for(int k = 0; k < sizeArray;k++){
//				values.add(0);
//			}
//			map.put(key, values);
//		}
//		int index = -1;
//		for (int j = 0; j < asseX.size() && index == -1; j++) {
//			if (asseX.get(j).equalsIgnoreCase(axis)) {
//				index = j;
//			}
//		}
//		values.set(index, val);
//	}
	public void incrementAggregateValueToMap(String key, Integer val, String axis) {
		ArrayList<Integer> values = map.get(key);
		if (values == null) {
			values = new ArrayList<Integer>();
			for (int k = 0; k < sizeArray; k++) {
				values.add(0);
			}
			map.put(key, values);
		}
		int index = -1;
		for (int j = 0; j < asseX.size() && index == -1; j++) {
			if (asseX.get(j).equalsIgnoreCase(axis)) {
				index = j;
			}
		}

		if (index > -1) {
			Integer valore = values.get(index) + val;
			values.set(index, valore);
		}
	}

	public HashMap<String, ArrayList<Integer>> getMap() {
		return map;
	}

	public int getSizeArray() {
		return sizeArray;
	}

}
