/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.analysis;


/**
 *
 */
public class DatoDS {

    public Integer mFrame;
    public Punto mPos;
    public Float mDis;
    public Float mVel;
    public Float mAcc;
    public Float mPow;

    public DatoDS(Integer mFrame, Punto mPos, Float mDis, Float mVel, Float mAcc, Float mPow) {
        this.mFrame = mFrame;
        this.mPos = mPos;
        this.mDis = mDis;
        this.mVel = mVel;
        this.mAcc = mAcc;
        this.mPow = mPow;
    }

    public Punto scala(Integer w, Integer h) {
        return new Punto(mPos.x * w / 105, mPos.y * h / 68);
    }

    public Integer getmFrame() {
        return mFrame;
    }

    public void setmFrame(Integer mFrame) {
        this.mFrame = mFrame;
    }

    public Punto getmPos() {
        return mPos;
    }

    public void setmPos(Punto mPos) {
        this.mPos = mPos;
    }

    public Float getmDis() {
        return mDis;
    }

    public void setmDis(Float mDis) {
        this.mDis = mDis;
    }

    public Float getmVel() {
        return mVel;
    }

    public void setmVel(Float mVel) {
        this.mVel = mVel;
    }

    public Float getmAcc() {
        return mAcc;
    }

    public void setmAcc(Float mAcc) {
        this.mAcc = mAcc;
    }

    public Float getmPow() {
        return mPow;
    }

    public void setmPow(Float mPow) {
        this.mPow = mPow;
    }

}
