/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package sics.analysis;

import sics.domain.Fascia;
import java.util.ArrayList;

/**
 *
 * <AUTHOR>
 */
public class FasciaResult {
    
    private Fascia mFascia;
    private ArrayList<Traccia> mTracce = new ArrayList<Traccia>();
    public Float mLength = 0f;
    public Float time = 0f;
    public Integer numOccorrenze = 0;


    public FasciaResult(Fascia mFascia) {
        this.mFascia = mFascia;
    }

    public Fascia getmFascia() {
        return mFascia;
    }

    public void setmFascia(Fascia mFascia) {
        this.mFascia = mFascia;
    }

    public ArrayList<Traccia> getmTracce() {
        return mTracce;
    }

    public void setmTracce(ArrayList<Traccia> mTracce) {
        this.mTracce = mTracce;
    }

    public Float getTime() {
        return time;
    }

    public void setTime(Float time) {
        this.time = time;
    }

}
