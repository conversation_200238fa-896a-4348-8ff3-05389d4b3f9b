package sics.export;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import sics.domain.Export;
import sics.domain.ExportDetail;
import sics.domain.Game;
import sics.domain.Playlist;
import sics.domain.User;
import sics.export.ExportLambdaResponse.STATUS;
import sics.helper.ArrayListSynchro;
import sics.helper.GlobalHelper;
import sics.helper.IntegerSynchro;
import sics.model.Azione;
import sics.service.UserService;

/**
 *
 * <AUTHOR>
 */
public class ExportLambdaThread extends Thread {

    private static final int MAX_CONCURRENT_THREADS = 20;

    private final IntegerSynchro clipDone;
    private final ArrayListSynchro<ExportLambdaResponse> listResponse;
    private final boolean removeOverlapping;

    private long totalTime;
    private Date startTimestamp;
    private ExportInfo expInfo;
    private int TOT_NUM_CLIP;
    private ExportLambdaResponse finalizationResponse;
    private int maxVideoQuality = -1;
    private boolean askedMaxQuality = false;

    public ExportLambdaThread(boolean removeOverlapping) {
        this.finalizationResponse = new ExportLambdaResponse(999);
        this.clipDone = new IntegerSynchro();
        this.listResponse = new ArrayListSynchro<>();
        this.TOT_NUM_CLIP = 0;
        this.removeOverlapping = removeOverlapping;
        this.startTimestamp = new Date();
        this.totalTime = 0;
    }

    @Override
    public void run() {
        Export export = new Export();
        try {
            if (!expInfo.getListActions().isEmpty()) {
                List<Long> eventIdsToExclude = new ArrayList<>();
                if (removeOverlapping && !expInfo.isMulti()) {
                    for (Azione azione : expInfo.getListActions()) {
                        Long idFixture = azione.getIdFixture();
                        if (idFixture != null && !eventIdsToExclude.contains(azione.getId())) {
                            // devo controllare se ci sono clip sovrapposte
                            for (Azione tmpAction : expInfo.getListActions()) {
                                if (Long.compare(azione.getId(), tmpAction.getId()) != 0 && ((tmpAction.getmStart() >= azione.getmStart() && tmpAction.getmStart() <= azione.getmEnd())
                                        || (tmpAction.getmEnd() >= azione.getmStart() && tmpAction.getmEnd() <= azione.getmEnd()))) {
                                    Long tmpIdFixture = tmpAction.getIdFixture();
                                    if (idFixture.equals(tmpIdFixture)) {
                                        //                                azione.setmStart(Math.min(tmpAction.getmStart(), azione.getmStart()));
                                        //                                azione.setmEnd(Math.max(tmpAction.getmEnd(), azione.getmEnd()));
                                        //                                azione.setmTactStart(Math.min(tmpAction.getmTactStart(), azione.getmTactStart()));
                                        eventIdsToExclude.add(tmpAction.getId());
                                    }
                                }
                            }
                        }
                    }
                }
                //ESCLUDO SOVRAPPOSTE
                if (!eventIdsToExclude.isEmpty()) {
                    for (Azione az : (ArrayList<Azione>) expInfo.getListActions().clone()) {
                        if (eventIdsToExclude.contains(az.getId())) {
                            expInfo.getListActions().remove(az);
                        }
                    }
                    //            expInfo.getListActions().removeAll(eventIdsToExclude);
                    TOT_NUM_CLIP = expInfo.getListActions().size();
                    listResponse.clear();
                    for (int i = 0; i < TOT_NUM_CLIP; i++) {
                        listResponse.add(new ExportLambdaResponse(i));
                    }
                }

                Map<Long, Game> fixures = new HashMap<>();
                long totalVideoDuration = 0;
                for (Azione azione : expInfo.getListActions()) {
                    totalVideoDuration += (azione.getmEnd() - azione.getmStart());

                    Game fixture = expInfo.getService().getGameByFixtureId(azione.getIdFixture(), "_en");
                    fixures.put(azione.getIdFixture(), fixture);
                    if (!expInfo.isMulti() && askedMaxQuality) {
                        // se video unico calcolo il massimo della qualità
                        Game tmpFixture = fixures.get(azione.getIdFixture());
                        if (tmpFixture != null) {
                            if (tmpFixture.getVideoQuality() != null && tmpFixture.getVideoQuality() > maxVideoQuality) {
                                maxVideoQuality = tmpFixture.getVideoQuality();
                            }
                        }

                        if ((expInfo.isTact() && azione.isTactAvailable() && azione.getmTactStart() >= 0) || (BooleanUtils.isTrue(azione.getIsTactical()) && azione.getmTactStart() >= 0)) {
                            maxVideoQuality = 2;
                        }
                    }
                }
                // nel caso in cui non legga i dati dal db oppure se non seleziono il massimo della qualità
                // imposto tutto in HD
                if (!expInfo.isMulti() && maxVideoQuality == -1) {
                    maxVideoQuality = 1;
                }
                expInfo.setFixtures(fixures);

                // INSERIMENTO TESTATA
                export.setApplication(1);
                export.setUserId(expInfo.getUser().getId());
                export.setGroupsetId(expInfo.getUser().getGroupsetId());
                export.setName(expInfo.getNameFile());
                export.setType(expInfo.isMulti() ? 2 : 1);
                export.setClipAmount(TOT_NUM_CLIP);
                export.setTotalVideoDuration(totalVideoDuration);
                Long exportId = expInfo.getService().addExport(export);
                export.setId(exportId);

                finalizationResponse.setStatus(STATUS.IN_PROGRESS);
//                GlobalHelper.writeLogData(expInfo.getService(), GlobalHelper.kActionStartExport, expInfo.getUser().getUsername(), expInfo.getUser().getPassword(), expInfo.getUser().getId());
                launchCompleteConcatZip(expInfo);
//                GlobalHelper.writeLogData(expInfo.getService(), GlobalHelper.kActionEndExport, expInfo.getUser().getUsername(), expInfo.getUser().getPassword(), expInfo.getUser().getId());
            }
            if (!finalizationResponse.getStatus().equals(STATUS.COMPLETED)) {
                finalizationResponse.setStatus(STATUS.ERROR);
            }
        } catch (Exception e) {
            System.err.println("ERROR! ExportLambdaThread.run");
        } finally {
            try {
                // INSERIMENTO DETTAGLIO + AGGIORNAMENTO TESTATA
                List<ExportDetail> exportDetails = new ArrayList<>();
                for (ExportLambdaResponse lambda : listResponse) {
                    Azione action = expInfo.getListActions().get(lambda.getIndex());

                    ExportDetail detail = new ExportDetail();
                    detail.setExportId(export.getId());
                    detail.setType(1);
                    detail.setStatusCode(lambda.getStatusCode());
                    detail.setMessage(lambda.getMessage());
                    detail.setClipIndex(lambda.getIndex());
                    long clipStartMs = expInfo.isTact() && action.isTactAvailable() && action.getmTactStart() >= 0 ? action.getmTactStart() : action.getmStart();
                    detail.setClipStartMs(clipStartMs);
                    detail.setClipDurationMs(action.getmEnd() - action.getmStart());
                    String pathVideo = getVideoPath(action);
                    detail.setVideoName(pathVideo);
                    detail.setStartTime(lambda.getStartTime());
                    detail.setExecutionTime(lambda.getTotalTime());
                    exportDetails.add(detail);
                }

                // Aggiungo alla fine l'ultima lambda che fa zip o concat
                ExportDetail detail = new ExportDetail();
                detail.setExportId(export.getId());
                detail.setType(expInfo.isMulti() ? 2 : 3);
                detail.setStatusCode(finalizationResponse.getStatusCode());
                detail.setMessage(finalizationResponse.getMessage());
                detail.setClipIndex(finalizationResponse.getIndex());
                detail.setStartTime(finalizationResponse.getStartTime());
                detail.setExecutionTime(finalizationResponse.getTotalTime());
                exportDetails.add(detail);

                List<String> insertDetailValues = new ArrayList<>();
                DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                for (ExportDetail exportDetail : exportDetails) {
                    String insertDetailValue = "(";
                    insertDetailValue += exportDetail.getExportId() + ",";
                    insertDetailValue += exportDetail.getType() + ",";
                    insertDetailValue += exportDetail.getStatusCode() + ",";
                    insertDetailValue += "'" + exportDetail.getMessage() + "',";
                    insertDetailValue += exportDetail.getClipIndex() + ",";
                    insertDetailValue += exportDetail.getClipStartMs() + ",";
                    insertDetailValue += exportDetail.getClipDurationMs() + ",";
                    insertDetailValue += (exportDetail.getVideoName() == null ? "NULL" : "'" + exportDetail.getVideoName() + "'") + ",";
                    insertDetailValue += "STR_TO_DATE('" + df.format(exportDetail.getStartTime()) + "', '%Y-%m-%d %H:%i:%s'),";
                    insertDetailValue += exportDetail.getExecutionTime() + "";
                    insertDetailValue += ")";

                    insertDetailValues.add(insertDetailValue);
                }
                expInfo.getService().addExportDetail(StringUtils.join(insertDetailValues, ","));

                // Aggiorno i dati della testata
                export.setStatusCode(finalizationResponse.getStatusCode());
                export.setTotalTime(totalTime);
                export.setTotalBilled(Math.round(getTotalBilled() * 1000000000L));
                Playlist playlist = expInfo.getPlaylist();
                if (playlist != null && playlist.getId() != -1L) {
                    export.setPlaylistId(playlist.getId());
                }
                expInfo.getService().updateExport(export);
            } catch (Exception ex) {
                System.out.println("ERRORE! Aggiornamento e/o Inserimento dati DB");
            }

            // UPDATE: lo rimuovo quando viene scaricato in automatico
            // lo scarica in automatico la pagina exportInfo.jsp
            //UserController.removeExport(this);
            System.out.println("BILLED: " + getTotalBilledString());
        }
    }

    private String getVideoPath(Azione action) {
        String pathVideo = action.getVideoName();
        Game fixture = (expInfo.getFixtures() != null && expInfo.getFixtures().containsKey(action.getIdFixture()) ? expInfo.getFixtures().get(action.getIdFixture()) : null);
        if (fixture != null && fixture.getProviderId() != 3) {
            if ((expInfo.isTact() && action.isTactAvailable() && action.getmTactStart() >= 0) || (BooleanUtils.isTrue(action.getIsTactical()) && action.getmTactStart() >= 0)) {
                pathVideo = action.getVideoName().replace(".mp4", "-TACT.mp4");
            } else {
                if (askedMaxQuality) {
                    // prendo la qualità del video
                    if (fixture.getVideoQuality() != null) {
                        if (fixture.getVideoQuality() == 1) {
                            pathVideo = action.getVideoName().replace(".mp4", "-HD.mp4");
                        } else if (fixture.getVideoQuality() == 2) {
                            pathVideo = action.getVideoName().replace(".mp4", "-FHD.mp4");
                        }
                    }
                } else if (expInfo.isHd() && action.isHdAvailable()) {
                    pathVideo = action.getVideoName().replace(".mp4", "-HD.mp4");
                }
            }
        }

        return pathVideo;
    }

    public Long getUserId() {
        return expInfo.getUser().getId();
    }

    private void launchCompleteConcatZip(final ExportInfo expInfo) {

        long startExport = System.currentTimeMillis();
        final boolean multi = expInfo.isMulti();
        System.out.println("LAUNCH COMPLETE " + (multi ? "ZIP" : "CONCAT"));
        final String multi_pack = String.valueOf(multi);
        final String timestamp = GlobalHelper.getTimestamp();
        final String GROUPSET_ID = expInfo.getUser().getGroupsetId().toString();
        final String USER_ID = expInfo.getUser().getId().toString();
        final String name_export = GlobalHelper.removeAccents(expInfo.getNameFile().replace(".mp4", "").replace(".zip", ""));
        final ArrayListSynchro<String> listClips = new ArrayListSynchro<>();

        final IntegerSynchro concurrentThreads = new IntegerSynchro();

        int totLenght = 0;
        for (int i = 0; i < TOT_NUM_CLIP; i++) {
            final int index = i;
            long startClip = System.currentTimeMillis();
            try {
                if (MAX_CONCURRENT_THREADS != 0) {
                    synchronized (concurrentThreads) {
                        while (concurrentThreads.value() >= MAX_CONCURRENT_THREADS) {
                            concurrentThreads.wait();
                        }
                        concurrentThreads.increment();
                    }
                }
                final Azione action = expInfo.getListActions().get(i);
                final String num_export = String.valueOf(index);
                String tmpVideoRes = "";
                String pathVideo = getVideoPath(action);
                // sistemazione VIDEO_RES
                if (maxVideoQuality > -1) {
                    // se maxVideoQuality impostato allora uso sicuramente quello
                    // se ho deciso di esportare file singoli non entra qua dentro
                    switch (maxVideoQuality) {
                        case 0:
                            tmpVideoRes = "720:408";
                            break;
                        case 1:
                            tmpVideoRes = "1280:720";
                            break;
                        case 2:
                            tmpVideoRes = "1920:1080";
                            break;
                        default:
                            break;
                    }
                }

//                if ((expInfo.isTact() && action.isTactAvailable() && action.getmTactStart() >= 0) || (BooleanUtils.isTrue(action.getIsTactical()) && action.getmTactStart() >= 0)) {
//                    pathVideo = action.getVideoName().replace(".mp4", "-TACT.mp4");
//                } else if (expInfo.isHd() && action.isHdAvailable()) {
//                    pathVideo = action.getVideoName().replace(".mp4", "-HD.mp4");
//                } else {
//                    pathVideo = action.getVideoName();
//                }
                final String VIDEO_RES = tmpVideoRes;
                final String VIDEO_KEY = pathVideo;
                final String START_ACTION = expInfo.isTact() && action.isTactAvailable() && action.getmTactStart() >= 0 ? GlobalHelper.getTimeString(action.getmTactStart()) : GlobalHelper.getTimeString(action.getmStart());
                final String DURATION_ACTION = GlobalHelper.getTimeString(action.getmEnd() - action.getmStart());
                totLenght += (action.getmEnd() - action.getmStart());
                new Thread() {
                    @Override
                    public void run() {
                        listResponse.get(index).setStartTime(new Date());
                        listResponse.get(index).setStatus(STATUS.IN_PROGRESS);
                        long startClipExport = System.currentTimeMillis();
                        System.out.println("LAUNCH LAMBDA CLIP #" + num_export);
                        try {
                            if (AWSLambdaInvokerController.invokeAWSLambdaClipSync(VIDEO_KEY, num_export, VIDEO_RES, multi_pack, START_ACTION, DURATION_ACTION, USER_ID, GROUPSET_ID, timestamp, name_export)) {
                                System.out.println("EXPORT CLIP OK.");
                                listResponse.get(index).setTotalTime(System.currentTimeMillis() - startClipExport);
                                listResponse.get(index).setStatus(STATUS.COMPLETED);
                                listResponse.get(index).setMessage("OK");
                                listResponse.get(index).setClipPath(GROUPSET_ID + "/" + USER_ID + "/" + name_export + "/" + name_export + "_" + num_export + ".mp4");
                            } else {
                                listResponse.get(index).setTotalTime(System.currentTimeMillis() - startClipExport);
                                listResponse.get(index).setStatus(STATUS.ERROR);
                                listResponse.get(index).setMessage("SBAAAAAM! ERROR LAMBDA CLIP #" + num_export);
                                System.err.println("SBAAAAAM! ERROR LAMBDA CLIP #" + num_export);
                            }
                            System.out.println("TIME LAMBDA CLIP #" + num_export + ": " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startClipExport), true, true, true));
                        } catch (Exception e) {
                            listResponse.get(index).setTotalTime(System.currentTimeMillis() - startClipExport);
                            listResponse.get(index).setStatus(STATUS.ERROR);
                            listResponse.get(index).setMessage("ERROR (unknown - maybe timeout)");
                            System.err.println("ERROR (unknown - maybe timeout) LAMBDA CLIP #" + num_export + ": " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startClipExport), true, true, true));
                        } finally {
                            clipDone.increment();
                            if (MAX_CONCURRENT_THREADS != 0) {
                                concurrentThreads.decrement();
                            }
                        }
                    }
                }.start();
            } catch (Exception e) {
                listResponse.get(index).setTotalTime(System.currentTimeMillis() - startClip);
                listResponse.get(index).setStatus(STATUS.ERROR);
                listResponse.get(index).setMessage("ERROR CONCURRENT THREADS");
                clipDone.increment();
                if (MAX_CONCURRENT_THREADS != 0) {
                    concurrentThreads.decrement();
                }
                System.err.println("ERROR CONCURRENT THREADS: " + e.getMessage());
            }
        }
        long startFinal = System.currentTimeMillis();
        try {
            synchronized (clipDone) {
                while (clipDone.value() != TOT_NUM_CLIP) {
                    clipDone.wait();
                }
                System.out.println("TIME TO EXPORT ALL SINGLE CLIPS: " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startExport), true, true, true));
            }
            startFinal = System.currentTimeMillis();
            // popolo qua la lista delle clip altrimenti potrebbero avere l'ordine sbagliato
            for (ExportLambdaResponse response : listResponse) {
                if (response.getStatus().equals(STATUS.COMPLETED) && response.getClipPath() != null) {
                    listClips.add(response.getClipPath());
                }
            }

            if (listClips.isEmpty()) {
                finalizationResponse.setStatus(STATUS.ERROR);
                finalizationResponse.setMessage("ERROR! EXPORTED CLIPS LIST IS EMPTY! TOTAL TIME: " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startExport), true, true, true));
                System.err.println("ERROR! EXPORTED CLIPS LIST IS EMPTY! TOTAL TIME: " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startExport), true, true, true));
            } else {
                finalizationResponse.setStatus(STATUS.FINALIZATION);
                finalizationResponse.setStartTime(new Date());

                boolean error = true;
                if (multi) {
                    //CALL LAMBDA ZIP
                    try {
                        startFinal = System.currentTimeMillis();
                        Long zipSize = AWSLambdaInvokerController.invokeAWSLambdaZipSync(listClips, GROUPSET_ID + "/" + USER_ID + "/" + name_export + "_" + timestamp + ".zip");
                        if (zipSize > 0) {
                            System.out.println("ZIP OK.");
                            try {
                                Playlist pl = new Playlist();
                                pl.setName(expInfo.getNameFile());
                                pl.setVideoName(GROUPSET_ID + "/" + USER_ID + "/" + name_export + "_" + timestamp + ".zip");
                                pl.setGroupsetId(expInfo.getUser().getGroupsetId());
                                pl.setUploadedBy(expInfo.getUser().getId().toString());
                                pl.setDate(new Date());
                                pl.setMedialength(expInfo.getDuration());
                                pl.setSizeVideoFromKb(zipSize);
                                pl.setType_file("zip");
                                expInfo.setPlaylist(pl);
                                if (expInfo.getPlaylist().getId() == -1) {
                                    GlobalHelper.addPlaylist(expInfo.getPlaylist());
                                    error = false;
                                } else {
                                    error = true;
                                }
                            } catch (Exception e) {
                                error = true;
                                finalizationResponse.setMessage("ERROR INSERT PLAYLIST ZIP: " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startExport), true, true, true));
                            }
                        } else {
                            error = true;
                            System.err.println("SBAAAAAM! ERROR LAMBDA ZIP");
                        }
                    } catch (Exception e) {
                        error = true;
                        System.err.println("ERROR (unknown - maybe timeout) LAMBDA ZIP");
                    } finally {
                        System.out.println("TIME ZIP (TOTAL NUM CLIPS:" + listClips.size() + "/" + TOT_NUM_CLIP + "): " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startFinal), true, true, true));
                    }
                } else {
                    //CALL LAMBDA UNISCI
                    try {
                        startFinal = System.currentTimeMillis();
                        Long videoSize = AWSLambdaInvokerController.invokeAWSLambdaConcatSync(listClips, GROUPSET_ID + "/" + USER_ID + "/" + name_export + "_" + timestamp + ".mp4");
                        if (videoSize > 0) {
                            System.out.println("CONCAT OK.");
                            try {
                                Playlist pl = new Playlist();
                                pl.setName(expInfo.getNameFile());
                                pl.setVideoName(GROUPSET_ID + "/" + USER_ID + "/" + name_export + "_" + timestamp + ".mp4");
                                pl.setGroupsetId(expInfo.getUser().getGroupsetId());
                                pl.setUploadedBy(expInfo.getUser().getId().toString());
                                pl.setDate(new Date());
                                pl.setMedialength(expInfo.getDuration());
                                pl.setSizeVideoFromKb(videoSize);
                                pl.setType_file("mp4");
                                expInfo.setPlaylist(pl);
                                //expInfo.updatePlaylistSizeVideo();
                                if (expInfo.getPlaylist().getId() == -1) {
                                    GlobalHelper.addPlaylist(expInfo.getPlaylist());
                                    error = false;
                                }
//                                generateThumbnails(GROUPSET_ID + "/" + USER_ID + "/" + name_export + ".mp4");
                            } catch (Exception e) {
                                error = true;
                                finalizationResponse.setTotalTime(System.currentTimeMillis() - startFinal);
                                finalizationResponse.setMessage("ERROR INSERT PLAYLIST MP4: " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startExport), true, true, true));
                            }
                        } else {
                            error = true;
                            finalizationResponse.setTotalTime(System.currentTimeMillis() - startFinal);
                            finalizationResponse.setMessage("SBAAAAAM! ERROR LAMBDA CONCAT");
                            System.err.println("SBAAAAAM! ERROR LAMBDA CONCAT");
                        }
                    } catch (Exception e) {
                        error = true;
                        finalizationResponse.setTotalTime(System.currentTimeMillis() - startFinal);
                        finalizationResponse.setMessage("ERROR (unknown - maybe timeout) LAMBDA CONCAT");
                        System.err.println("ERROR (unknown - maybe timeout) LAMBDA CONCAT");
                    } finally {
                        System.out.println("TIME CONCAT (TOTAL NUM CLIPS:" + listClips.size() + "/" + TOT_NUM_CLIP + "): " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startFinal), true, true, true));
                    }
                }
                if (!error) {
                    finalizationResponse.setTotalTime(System.currentTimeMillis() - startFinal);
                    finalizationResponse.setMessage("OK EXPORT: " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startExport), true, true, true));
                    finalizationResponse.setStatus(STATUS.COMPLETED);
                    System.out.println("TIME EXPORT (TOTAL LENGHT: " + GlobalHelper.msecToMinSec(totLenght, true, true, true) + " | TOTAL NUM CLIPS:" + listClips.size() + "/" + TOT_NUM_CLIP + "): " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startExport), true, true, true));
                } else {
                    finalizationResponse.setTotalTime(System.currentTimeMillis() - startFinal);
                    if (finalizationResponse.getMessage().isEmpty()) {
                        finalizationResponse.setMessage("ERROR EXPORT: " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startExport), true, true, true));
                    }
                    finalizationResponse.setStatus(STATUS.ERROR);
                    System.err.println("ERROR EXPORT (TOTAL LENGHT: " + GlobalHelper.msecToMinSec(totLenght, true, true, true) + " | TOTAL NUM CLIPS:" + listClips.size() + "/" + TOT_NUM_CLIP + "): " + GlobalHelper.msecToMinSec(Math.round(System.currentTimeMillis() - startExport), true, true, true));
                }
            }
        } catch (Exception e) {
            finalizationResponse.setTotalTime(System.currentTimeMillis() - startFinal);
            finalizationResponse.setStatus(STATUS.ERROR);
            finalizationResponse.setMessage("ERROR THREAD export");
            System.err.println("SBADABAAAAAAM! ERROR THREAD export!");
        } finally {
            setTotalTime(System.currentTimeMillis() - startExport);
            listClips.clear();
        }
    }

    public String addListActionsToExport(List<Azione> listActions, boolean hd, boolean tact, boolean multi, String name, User user, UserService service) {
        String id = "";
        try {
            ExportInfo expInfo = new ExportInfo(hd, tact, multi, name, user);
            expInfo.setService(service);
            expInfo.addActionToListActions(listActions);
            id = expInfo.getIdentifier();
            this.expInfo = expInfo;
            TOT_NUM_CLIP = expInfo.getListActions().size();
            for (int i = 0; i < TOT_NUM_CLIP; i++) {
                listResponse.add(new ExportLambdaResponse(i));
            }
        } catch (Exception ex) {
            System.err.println("ERROR! addListActionToExport Lambda");
            return "error";
        }
        return id;
    }

    public String getProgressPercentage() {
        if (!finalizationResponse.getStatus().equals(STATUS.ERROR)) {
            try {
                int value = (int) Math.round((((double) clipDone.value()) / ((double) (TOT_NUM_CLIP))) * 100);
                if (value >= 100) {
                    return "Finalizing...";
                } else if (value >= 0 && value < 100) {
                    return value + "%";
                }
            } catch (Exception e) {
                return "";
            }
        }
        return "";
    }

    public String getProgressPercentageBar() {
        String percentage = getProgressPercentage();
        if (percentage.equalsIgnoreCase("Finalizing...")) {
            return "100%";
        } else {
            return percentage;
        }
    }

    public String getExportName() {
        return expInfo.getNameFile();
    }

    public boolean isError() {
        return finalizationResponse.getStatus().equals(STATUS.ERROR);
    }

    public String getStatus() {
        String result = "";
        try {
            switch (finalizationResponse.getStatus()) {
                case READY:
                    result = "Queued";
                    break;
                case IN_PROGRESS:
                    result = "In Progress";
                    break;
                case FINALIZATION:
                    result = "Finalizing...";
                    break;
                case COMPLETED:
                    result = "Completed";
                    break;
                case ERROR:
                    result = "Error";
                    break;
            }
        } catch (Exception e) {
            result = "";
        }
        return result;
    }

    public String getDoneOnTotal() {
        return clipDone.value() + "/" + TOT_NUM_CLIP;
    }

    public String getElapsedTime() {
        try {
            return GlobalHelper.msecToMinSec(new Date().getTime() - startTimestamp.getTime(), false, true, false);
        } catch (Exception e) {
            return "";
        }
    }

    public String getEstimatedTime() {
        try {
            double totLenght = 0;
            for (Azione az : expInfo.getListActions()) {
                totLenght += (az.getmEnd() - az.getmStart());
            }
            double msToCompleteClips = ((double) TOT_NUM_CLIP / (double) MAX_CONCURRENT_THREADS) * ((totLenght / (double) TOT_NUM_CLIP) * (3d / 5d));
            double msToCompleteConcatZip = expInfo.isMulti() ? 0.03d * totLenght : 0.02 * totLenght;
            return GlobalHelper.msecToMinSec(Math.round(msToCompleteClips + msToCompleteConcatZip), false, true, false);
        } catch (Exception e) {
            return "";
        }
    }

    public long clipBilledTime() {
        long total = 0;
        for (ExportLambdaResponse elr : listResponse) {
            total += elr.getTotalTime();
        }
        return total;
    }

    public long finalizationBilledTime() {
        return finalizationResponse.getTotalTime();
    }

    public double getTotalBilled() {
        double billed = 0;
        try {
            long clipBilledTime = clipBilledTime();
            long finalizationBilledTime = finalizationBilledTime();
            double priceClips = (0.001d * clipBilledTime);
            priceClips = (((priceClips) * 8d) * 0.0000166667d);
            priceClips += (TOT_NUM_CLIP * 0.0000002d);
            priceClips += ((1.0d - 0.5d) * (0.001d * clipBilledTime) * 0.000000034d);

            double priceFinalization = (0.001d * finalizationBilledTime);
            priceFinalization = (((priceFinalization) * 8d) * 0.0000166667d);
            priceFinalization += (1 * 0.0000002d);
            priceFinalization += ((4.0d - 0.5d) * (0.001d * finalizationBilledTime) * 0.000000034d);
            billed = priceClips + priceFinalization;
        } catch (Exception e) {
            System.err.println("ERROR! totalBilled");
        }
        return billed;
    }

    public String getTotalBilledString() {
        return getTotalBilled() + " USD";
    }

    /**
     * @return the totalTime
     */
    public long getTotalTime() {
        return totalTime;
    }

    /**
     * @param totalTime the totalTime to set
     */
    public void setTotalTime(long totalTime) {
        this.totalTime = totalTime;
    }

    public ExportInfo getExpInfo() {
        return expInfo;
    }

    public boolean isAskedMaxQuality() {
        return askedMaxQuality;
    }

    public void setAskedMaxQuality(boolean askedMaxQuality) {
        this.askedMaxQuality = askedMaxQuality;
    }
}
