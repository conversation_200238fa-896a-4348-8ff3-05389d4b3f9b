package sics.export;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ExportLambdaResponse {

    public static enum STATUS {
        READY,
        IN_PROGRESS,
        FINALIZATION,
        COMPLETED,
        ERROR
    }

    private long totalTime;
    private int index;
    private STATUS status;
    private String message;
    private Date startTime;
    private String clipPath;

    public ExportLambdaResponse(int index) {
        this.startTime = new Date();
        this.index = index;
        this.status = STATUS.READY;
        this.message = "";
        this.totalTime = 0;
    }

    /**
     * @return the index
     */
    public int getIndex() {
        return index;
    }

    /**
     * @param index the index to set
     */
    public void setIndex(int index) {
        this.index = index;
    }

    /**
     * @return the status
     */
    public STATUS getStatus() {
        return status;
    }

    /**
     * @param status the status to set
     */
    public void setStatus(STATUS status) {
        this.status = status;
    }
    
    public int getStatusCode() {
        if (this.status.equals(STATUS.COMPLETED)) {
            return 200;
        } else {
            return 500;
        }
    }

    /**
     * @return the message
     */
    public String getMessage() {
        return message;
    }

    /**
     * @param message the message to set
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * @return the totalTime
     */
    public long getTotalTime() {
        return totalTime;
    }

    /**
     * @param totalTime the totalTime to set
     */
    public void setTotalTime(long totalTime) {
        this.totalTime = totalTime;
    }

    /**
     * @return the startTime
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * @param startTime the startTime to set
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public String getClipPath() {
        return clipPath;
    }

    public void setClipPath(String clipPath) {
        this.clipPath = clipPath;
    }
}
