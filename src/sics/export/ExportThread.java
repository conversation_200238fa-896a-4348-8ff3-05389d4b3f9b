package sics.export;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import sics.domain.Playlist;
import sics.domain.User;
import sics.helper.ArrayListSynchro;
import sics.helper.FileHelper;
import sics.helper.GlobalHelper;
import sics.helper.StringHelper;
import sics.helper.Uploader;

import sics.model.Azione;
import sics.service.UserService;

/**
 *
 * <AUTHOR> 30
 */
public class ExportThread extends Thread {

    protected transient final Log log = LogFactory.getLog(getClass());
    // lista con le informazioni per esportare
    protected ArrayListSynchro<ExportInfo> exportActionsQueue = new ArrayListSynchro<ExportInfo>();
    // identifica che il thread è attivo
    protected BooleanSynchro running = new BooleanSynchro();
    // mappa per tener traccia degli errori e dei tentativi di retry (imponiamo un limite di tentativi)
    protected HashMap<String, Integer> errorRetry = new HashMap<String, Integer>();
    // lista esportazioni completate
    protected ArrayListSynchro<ExportInfo> exportCompleted = new ArrayListSynchro<ExportInfo>();
    protected BooleanSynchro exportCompletedUpdate = new BooleanSynchro();

    public String addListActionsToExport(List<Azione> listActions, boolean hd, boolean tact, boolean multi, String name, User user, UserService service) {
        String id = "";
        try {
            ExportInfo expInfo = new ExportInfo(hd, tact, multi, name, user);
            expInfo.setService(service);
            expInfo.addActionToListActions(listActions);
            exportActionsQueue.add(expInfo);
            id = expInfo.getIdentifier();
        } catch (Exception ex) {

        }
        return id;
    }

    public ExportInfo isExportCompleted(String identifier) {
        ExportInfo result = null;
        for (ExportInfo expInfo : exportCompleted) {
            if (expInfo.getIdentifier().equals(identifier)) {
                if (expInfo.getPlaylist() != null) {
                    result = expInfo;
                    break;
                }
            }
        }
//		if (result != null) {
//			exportCompleted.remove(result);
//		}

        return result;
    }

    public ExportInfo getProgressInfo(String identifier) {
        for (ExportInfo expInfo : exportActionsQueue) {
            if (expInfo.getIdentifier().equals(identifier)) {
                return expInfo;
            }
        }

        return null;
    }

    public boolean isRunning() {
        return running.value();
    }

    public ArrayListSynchro<ExportInfo> getExportCompleted() {
        return exportCompleted;
    }

    public boolean isExportCompletedUpdate() {
        return exportCompletedUpdate.value();
    }

    public void setExportCompletedUpdate(boolean exportCompletedUpdate) {
        this.exportCompletedUpdate.setValue(exportCompletedUpdate);
    }

    public static String generateScreenshot(String videoPath, String keyName, Double milliseconds, boolean isTmp) {
        try {
            keyName = keyName.replace(".mp4", ".png");
            String fileName = "/tmp" + File.separator + keyName;
            File folder = new File(GlobalHelper.kServletContextPathExport + "tmp");
            if (!folder.exists()) {
                folder.mkdirs();
            }
            String command1 = GlobalHelper.getPathFFmpeg() + " -noaccurate_seek -ss " + milliseconds + " -i " + videoPath + " -frames:v 1 -y " + fileName + "";
            //System.out.println(command1);
            ArrayList<String> commandList = new ArrayList<String>();
            commandList.addAll(Arrays.asList(command1.split(" ")));
            ProcessBuilder pb = new ProcessBuilder(commandList.toArray(new String[commandList.size()]));
            pb.directory(new File(GlobalHelper.kServletContextPathExport));

            Process process = pb.start();
            InputStream inputStream = process.getErrorStream();
            InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

            String msg = "";
            while ((msg = bufferedReader.readLine()) != null) {
                //System.out.println(msg);
            }
            bufferedReader.close();
            inputStreamReader.close();
            inputStream.close();
            Uploader.uploadTmpThumbnail(fileName, keyName);
            File localFile = new File(fileName);
            if (localFile.exists()) {
                //localFile.delete();
            }

            return "https://s3.eu-west-1.amazonaws.com/it.sics.tmp/" + keyName;
        } catch (Exception e) {
            GlobalHelper.reportError(e);
        }

        return null;
    }

    public static boolean generateThumbnails(String videoPath, String keyName) {
        boolean done = false;
        try {

            File idmatchFolder = new File(videoPath).getParentFile();
            if (!idmatchFolder.exists()) {
                idmatchFolder.mkdirs();
            }

            String matchPath = idmatchFolder.getAbsolutePath() + File.separator;
            String msToSnap = "00:01";
            String snapName = matchPath + keyName.replace(".mp4", ".png");
            String pathInput = videoPath;

            if (!new File(snapName).exists()) {
                try {
                    String command1 = GlobalHelper.getPathFFmpeg() + " -noaccurate_seek -ss " + msToSnap + " -i " + pathInput + " -s 240x135 -frames:v 1 -y " + snapName;
                    ArrayList<String> commandList = new ArrayList<String>();
                    commandList.addAll(Arrays.asList(command1.split(" ")));
                    ProcessBuilder pb = new ProcessBuilder(commandList.toArray(new String[commandList.size()]));
                    pb.directory(new File(GlobalHelper.kServletContextPathExport));
                    Process process = pb.start();
                    InputStream inputStream = process.getErrorStream();
                    InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
                    BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

                    String msg = "";
                    while ((msg = bufferedReader.readLine()) != null) {
                        //System.out.println(msg);
                    }
                    bufferedReader.close();
                    inputStreamReader.close();
                    inputStream.close();
                    if (new File(snapName).exists()) {
                        Uploader.uploadThumbnail(snapName, keyName.replace(".mp4", ".png"));
                        done = true;
                    }
                } catch (Exception e) {
                    GlobalHelper.reportError(e);
                }
            }

        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        return done;
    }

    public static void unZipIt(String zipFileSource, String outputFolder) {
        try {
            ZipFile zipFile = new ZipFile(zipFileSource);
            zipFile.extractAll(outputFolder);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static boolean zipFiles(String pathFileToZip, String zipFolder) {
        boolean result = false;
        try {
            // Initiate ZipFile object
            ZipFile zipFile = new ZipFile(zipFolder);

            // Initiate Zip Parameters which define various properties such
            // as compression method, etc.
            ZipParameters parameters = new ZipParameters();

            // set compression method to store compression
            parameters.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);

            // Set the compression level
            parameters.setCompressionLevel(Zip4jConstants.DEFLATE_LEVEL_NORMAL);

            File fileToZip = new File(pathFileToZip);
            if (fileToZip.isDirectory()) {
                // Add folder to the zip file
                zipFile.addFolder(pathFileToZip, parameters);
            } else {
                // Add file to the zip file
                zipFile.addFile(fileToZip, parameters);
            }

            result = true;
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return result;
    }

    /* Per ogni oggetto ExportInfo nella lista exportActionsQueue vedo le azioni che devo andare ad esportare,
		per ciascuna di esse verifico se è già stata esportata precedentemente e quindi si trova nella cartella cache 
		comune a tutti gli utenti, altrimenti genero la clip corrispondente. Una volta ottenuta la clip proseguo con la concatenazione
		delle varie clip oppure alla creazione dello zip
	
     */
    @Override
    public void run() {

        while (!exportActionsQueue.isEmpty()) {
            running.setValue(true);
            ExportInfo expInfo = exportActionsQueue.get(0);
            // creo la cartella di riferimento per l'esportazione
            String pathCurrentExport = GlobalHelper.kServletContextPathExport + expInfo.getIdentifier() + File.separator;
            new File(pathCurrentExport).mkdirs();

            LinkedHashMap<String, Azione> listPathToMerge = new LinkedHashMap<String, Azione>();
            String pathVideo = "";
            Azione action = null;

            List<Long> eventIdsToExclude = new ArrayList<>();
            int totalClips = expInfo.getListActions().size();
            String logContent = totalClips + " Clip";
            GlobalHelper.writeLogData(null, expInfo.getService(), GlobalHelper.kActionStartExport, expInfo.getUser().getUsername(), expInfo.getUser().getPassword(), expInfo.getUser().getId(), logContent);
            Date startDate = new Date();
            while (!expInfo.getListActions().isEmpty()) {
                try {
                    action = expInfo.getListActions().get(0);
                    if (!eventIdsToExclude.contains(action.getId())) {

                        if (!expInfo.isMulti()) {
                            // devo controllare se ci sono clip sovrapposte
                            for (Azione azione : expInfo.getListActions()) {
                                Long idFixture = azione.getIdFixture();
                                if (idFixture != null && !eventIdsToExclude.contains(azione.getId())) {
                                    // devo controllare se ci sono clip sovrapposte
                                    for (Azione tmpAction : expInfo.getListActions()) {
                                        if (Long.compare(azione.getId(), tmpAction.getId()) != 0 && ((tmpAction.getmStart() >= azione.getmStart() && tmpAction.getmStart() <= azione.getmEnd())
                                                || (tmpAction.getmEnd() >= azione.getmStart() && tmpAction.getmEnd() <= azione.getmEnd()))) {
                                            Long tmpIdFixture = tmpAction.getIdFixture();
                                            if (idFixture.equals(tmpIdFixture)) {
//                                azione.setmStart(Math.min(tmpAction.getmStart(), azione.getmStart()));
//                                azione.setmEnd(Math.max(tmpAction.getmEnd(), azione.getmEnd()));
//                                azione.setmTactStart(Math.min(tmpAction.getmTactStart(), azione.getmTactStart()));
                                                eventIdsToExclude.add(tmpAction.getId());
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        if (!eventIdsToExclude.isEmpty()) {
                            for (Azione az : (ArrayList<Azione>) expInfo.getListActions().clone()) {
                                if (eventIdsToExclude.contains(az.getId())) {
                                    expInfo.getListActions().remove(az);
                                }
                            }
                        }

                        String pathToSearch = GlobalHelper.getPathExportCache() + File.separator + action.getIdFixture() + File.separator + (expInfo.isTact() && action.isTactAvailable() && action.getmTactStart() >= 0 ? "TACT" + File.separator : (expInfo.isHd() && action.isHdAvailable() ? "HD" + File.separator : "")) + action.getmIdEvent() + ".mp4";
                        pathToSearch = pathToSearch.replace(" ", "_");
                        File toSearch = new File(pathToSearch);
                        // se non esiste la cartella dove mettere i file, la creo
                        if (!toSearch.getParentFile().exists()) {
                            toSearch.getParentFile().mkdirs();
                        }
                        //expInfo.totalDuration += (action.getmEnd() - action.getmStart());
                        // vedo se in cache nel server ho già questa clip tagliata, se si la copio nella cartella dell'esportazione
                        if (toSearch.exists() && !(expInfo.isHd() && !action.isHdAvailable())) {
                            //						String actionFile = pathCut + action.getIdEvent() + ".mp4";
                            //						Files.copy(toSearch.toPath(), new File(actionFile).toPath(), StandardCopyOption.REPLACE_EXISTING);
                            listPathToMerge.put(toSearch.getAbsolutePath(), action);
                            expInfo.setTimeProcessed(expInfo.getTimeProcessed() + action.getmEnd() - action.getmStart());
                            expInfo.getListActions().remove(0);
                        } else {
                            // taglio la clip
                            //                        if (pathVideo.isEmpty()) {
                            // verifico se è stata scelta l'esportazione con video HD
                            // aggiunto caso se arrivo da playlistTv e scarico tattico
                            if ((expInfo.isTact() && action.isTactAvailable() && action.getmTactStart() >= 0) || (BooleanUtils.isTrue(action.getIsTactical()) && action.getmTactStart() >= 0)) {
                                pathVideo = GlobalHelper.pathS3(action.getVideoName().replace(".mp4", "-TACT.mp4"), "video", true);
                            } else if (expInfo.isHd() && action.isHdAvailable()) {
                                pathVideo = GlobalHelper.pathS3(action.getVideoName().replace(".mp4", "-HD.mp4"), "video", true);
                            } else {
                                pathVideo = GlobalHelper.pathS3(action.getVideoName(), "video", true);
                            }
                            //                        }
                            String startAction = expInfo.isTact() && action.isTactAvailable() && action.getmTactStart() >= 0 ? GlobalHelper.formatTime(action.getmTactStart()) : GlobalHelper.formatTime(action.getmStart());
                            String command1 = GlobalHelper.getPathFFmpeg() + " -ss " + startAction + " -i";

                            // se ho esportazione in hd ma l'azione non ha il video in hd e il tipo di esportazione è file unico allora scalo ad hd, altrimenti no
                            String command2 = "-t " + action.getmDuration() + (((expInfo.isHd() && !action.isHdAvailable() && !expInfo.isTact()) || ((!expInfo.isHd() || !action.isHdAvailable()) && expInfo.isTact() && (!action.isTactAvailable() || action.getmTactStart() < 0))) && !expInfo.isMulti() ? " -vf scale=1280:720" : "") + " -vcodec libx264 -q:v 0 -r 25 -acodec libmp3lame -ac 2 -ar 44100 -ab 128k -y";
                            //String command2 = "-t " + action.getDurataAzione() + " -vcodec libx264 -q:v 0 -r 25 -acodec libmp3lame -ac 2 -ar 44100 -ab 128k -y";
                            //						String command2 = "-t " + action.getDurataAzione() + " -c copy -y";
                            if (expInfo.isHd() && !action.isHdAvailable() && !expInfo.isMulti()) {
                                toSearch = new File(toSearch.getAbsolutePath().replace(".mp4", "-scaled.mp4"));
                            }

                            ArrayList<String> commandList = new ArrayList<String>();
                            commandList.addAll(Arrays.asList(command1.split(" ")));
                            commandList.add(pathVideo);
                            commandList.addAll(Arrays.asList(command2.split(" ")));
                            commandList.add(toSearch.getAbsolutePath());
                            //System.out.println(command1 + " " + pathVideo + " " + command2 + " " + toSearch.getAbsolutePath());
                            //commandList.add(output);

                            ProcessBuilder pb = new ProcessBuilder(commandList.toArray(new String[commandList.size()]));
                            pb.directory(new File(GlobalHelper.kServletContextPathExport));
                            System.out.println("");
                            System.out.println("");
                            System.out.println("START - " + pb.command().toString());
                            System.out.println("");
                            System.out.println("");
                            Process process = pb.start();

                            InputStream inputStream = process.getErrorStream();
                            InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
                            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

                            String msg = "";
                            int prevTime = 0;
                            while ((msg = bufferedReader.readLine()) != null) {
                                //System.out.println(msg);
                                if (msg.startsWith("frame")) {
                                    int index1 = msg.indexOf("time=");
                                    if (index1 > -1) {
                                        int startindex = index1 + 5;
                                        String timeString = msg.substring(startindex, startindex + 11);
                                        int timeMs = StringHelper.getMillisecondsFromTimeString(timeString);
                                        expInfo.setTimeProcessed(expInfo.getTimeProcessed() + timeMs - prevTime);
                                        prevTime = timeMs;
                                    }
                                }
                            }
                            bufferedReader.close();
                            inputStreamReader.close();
                            inputStream.close();
                            System.out.println("");
                            System.out.println("");
                            System.out.println("FINISH - " + pb.command().toString());
                            System.out.println("");
                            System.out.println("");
                            boolean error = false;
                            expInfo.getListActions().remove(0);

                            if (!toSearch.exists()) {
                                error = true;
                            } else {
                                listPathToMerge.put(toSearch.getAbsolutePath().replace("'", ""), action);
                            }
                            if (error) {
                                if (errorRetry.containsKey(action.getIdEvent()) && errorRetry.get(action.getIdEvent()) == 4) {
                                    //System.out.println("Azione con id: " + action.getIdEvent() + " non è stata esportata.");
                                    // dopo 5 tentativi la tolgo e rinuncio ad esportarla
                                } else {
                                    if (!errorRetry.containsKey(action.getIdEvent())) {
                                        errorRetry.put(action.getIdEvent(), 0);
                                    }
                                    int retryCount = errorRetry.get(action.getIdEvent()) + 1;
                                    errorRetry.put(action.getIdEvent(), retryCount);
                                    expInfo.getListActions().add(action);
                                }
                            }
                        }
                    } else { // se da skippare vado avanti e basta
                        expInfo.getListActions().remove(0);
                    }
                } catch (IOException ex) {
                    ex.printStackTrace();
                    if (action != null) {
                        if (errorRetry.containsKey(action.getIdEvent()) && errorRetry.get(action.getIdEvent()) == 4) {
                            //System.out.println("Azione con id: " + action.getIdEvent() + " non è stata esportata.");
                            expInfo.getListActions().remove(0);
                        } else {
                            if (!errorRetry.containsKey(action.getIdEvent())) {
                                errorRetry.put(action.getIdEvent(), 0);
                            }
                            int retryCount = errorRetry.get(action.getIdEvent()) + 1;
                            errorRetry.put(action.getIdEvent(), retryCount);
                            expInfo.getListActions().add(action);
                        }
                    } else {
                        // se action è null allora elimino l'azione corrente
                        expInfo.getListActions().remove(0);
                    }

                    GlobalHelper.reportError(ex);
                }
                pathVideo = "";
            }
            // se voglio file unico
            if (!expInfo.isMulti()) {

                try {
                    FileWriter fw = null;
                    BufferedWriter writer = null;

                    File fileMerge = new File(pathCurrentExport + "list.txt");
                    if (fileMerge.exists()) {
                        fileMerge.delete();
                    }
                    fileMerge.createNewFile();
                    //se il file esiste
                    if (fileMerge.exists()) {

                        fw = new FileWriter(fileMerge);
                        writer = new BufferedWriter(fw);
                        for (String path : listPathToMerge.keySet()) {
                            writer.write("file '" + path + "'");
                            writer.newLine();
                        }

                    }

                    writer.flush();
                    writer.close();
                    fw.close();

                    String command1 = GlobalHelper.getPathFFmpeg() + " -f concat -safe 0 -i";
                    String pathFile = pathCurrentExport + "list.txt";
                    String command2 = "-c copy -y";
                    String output = pathCurrentExport + (expInfo.getNameFile().isEmpty() ? "playlist" : expInfo.getNameFile()) + ".mp4";
                    output = output.replace(" ", "_");
                    ArrayList<String> commandList = new ArrayList<>();
                    commandList.addAll(Arrays.asList(command1.split(" ")));
                    commandList.add(pathFile);
                    commandList.addAll(Arrays.asList(command2.split(" ")));
                    commandList.add(output);
                    //System.out.println(command1 + " " + pathFile + " " + command2 + " " + output);

                    ProcessBuilder pb = new ProcessBuilder(commandList.toArray(new String[commandList.size()]));
                    pb.directory(new File(GlobalHelper.kServletContextPathExport));
                    Process process = pb.start();

                    InputStream inputStream = process.getErrorStream();
                    InputStreamReader inputStreamReader = new InputStreamReader(inputStream);
                    BufferedReader bufferedReader = new BufferedReader(inputStreamReader);

                    String msg = "";
                    while ((msg = bufferedReader.readLine()) != null) {
                        //System.out.println(msg);
                    }
                    bufferedReader.close();
                    inputStreamReader.close();
                    inputStream.close();

                    File outputFile = new File(output.replace("'", ""));
                    if (FileHelper.fileExist(outputFile.getAbsolutePath())) {
                        // 2020-10-19 eliminazione cache per riempimento disco server
                        for (String path : listPathToMerge.keySet()) {
                            File cacheMatch = new File(path);
                            if (cacheMatch.exists()) {
                                String nameFolder = cacheMatch.getParentFile().getName();
                                // se sta dentro a una cartella HD o TACT deve eliminare anche quella, torna su di 2
                                if (nameFolder.equals("HD") || nameFolder.equals("TACT")) {
                                    GlobalHelper.deleteDirectory(cacheMatch.getParentFile().getParentFile());
                                } else {
                                    GlobalHelper.deleteDirectory(cacheMatch.getParentFile());
                                }
                            }
                        }
                        Playlist pl = new Playlist();
                        pl.setName(expInfo.getNameFile());
                        pl.setVideoName(expInfo.getNameFile() + ".mp4");
                        pl.setGroupsetId(expInfo.getUser().getGroupsetId());
                        pl.setUploadedBy(expInfo.getUser().getId().toString());
                        pl.setDate(new Date());
                        pl.setMedialength(expInfo.getDuration());
                        pl.setType_file("mp4");
                        String keyName = Uploader.upload(expInfo, outputFile, pl);

                        if (!keyName.isEmpty()) {
                            expInfo.setPlaylist(pl);
                            expInfo.updatePlaylistSizeVideo();
                            if (expInfo.getPlaylist().getId() == -1) {
                                GlobalHelper.addPlaylist(expInfo.getPlaylist());
                            }
                            generateThumbnails(outputFile.getAbsolutePath(), keyName);
                            GlobalHelper.deleteDirectory(outputFile.getParentFile());
                        }

                    }

                } catch (IOException ex) {
                    GlobalHelper.reportError(ex);
                }
            } else {

                String pathToZip = pathCurrentExport + (expInfo.getNameFile().isEmpty() ? "playlist" : expInfo.getNameFile()) + File.separator;
                File folderToZip = new File(pathToZip);
                folderToZip.mkdirs();
                int index = 1;
                for (String p : listPathToMerge.keySet()) {
                    try {
                        String numProgr = String.valueOf(index);
                        if (listPathToMerge.size() > 99) {
                            if (index < 10) {
                                numProgr = "00" + numProgr;
                            } else {
                                numProgr = "0" + numProgr;
                            }

                        } else if (listPathToMerge.size() > 9) {
                            if (index < 10) {
                                numProgr = "0" + numProgr;
                            }
                        }
                        FileHelper.copyFile(p, pathToZip + File.separator + numProgr + "-" + listPathToMerge.get(p).getNameFileExport().replaceAll("\n", "_").replaceAll("\t", "_").replaceAll(" ", "_"));
                        index++;
                    } catch (Exception ex) {
                        GlobalHelper.reportError(ex);
                    }
                }
                String outputFileZip = pathToZip.substring(0, pathToZip.length() - 1) + ".zip";
                zipFiles(pathToZip, outputFileZip);
                if (FileHelper.fileExist(outputFileZip)) {
                    // 2020-10-19 eliminazione cache per riempimento disco server
                    for (String path : listPathToMerge.keySet()) {
                        File cacheMatch = new File(path);
                        if (cacheMatch.exists()) {
                            if (cacheMatch.getParentFile().getName().equals("HD")) {
                                GlobalHelper.deleteDirectory(cacheMatch.getParentFile().getParentFile());
                            } else {
                                GlobalHelper.deleteDirectory(cacheMatch.getParentFile());
                            }
                        }
                    }
                    GlobalHelper.deleteDirectory(folderToZip);
                    Playlist pl = new Playlist();
                    pl.setName(expInfo.getNameFile());
                    pl.setVideoName(expInfo.getNameFile() + ".zip");
                    pl.setGroupsetId(expInfo.getUser().getGroupsetId());
                    pl.setUploadedBy(expInfo.getUser().getId().toString());
                    pl.setDate(new Date());
                    pl.setMedialength(expInfo.getDuration());
                    pl.setType_file("zip");
                    String keyName = Uploader.upload(expInfo, new File(outputFileZip), pl);
                    if (!keyName.isEmpty()) {
                        GlobalHelper.deleteDirectory(new File(outputFileZip).getParentFile());
                        expInfo.setPlaylist(pl);
                        if (expInfo.getPlaylist().getId() == -1) {
                            GlobalHelper.addPlaylist(expInfo.getPlaylist());
                        }
                    }
                }

            }
            exportCompleted.add(exportActionsQueue.remove(0));
            exportCompletedUpdate.setValue(true);
            logContent = "Durata: " + (new Date().getTime() - startDate.getTime()) + " ms";
            if (expInfo.getPlaylist() != null && expInfo.getPlaylist().getId() != null) {
                logContent += ";Playlist ID: " + expInfo.getPlaylist().getId();
            }
            logContent += ";" + totalClips + " Clip";
            GlobalHelper.writeLogData(null, expInfo.getService(), GlobalHelper.kActionEndExport, expInfo.getUser().getUsername(), expInfo.getUser().getPassword(), expInfo.getUser().getId(), logContent);
        }
        running.setValue(false);
    }
}
