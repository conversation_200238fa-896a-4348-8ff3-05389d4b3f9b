package sics.export;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import sics.domain.Game;
import sics.domain.Playlist;
import sics.domain.User;
import sics.helper.DateHelper;
import sics.helper.GlobalHelper;
import sics.model.Azione;
import sics.service.UserService;

public class ExportInfo {

    private String identifier;
    private boolean hd = false;
    private boolean tact = false;
    private boolean multi = false;
    private String nameFile = "";
    private User user;
    private ArrayList<Azione> listActions = new ArrayList<>();
    private long totalDuration = 0l;
    private Playlist playlist;
    private double countAction = 0;
    private long uploaded = 0;
    private long fileSize = 0;
    private double progress = 0;
    private Date date;
    private UserService service;
    private double timeProcessed = 0l; // in millisecondi
    private Map<Long, Game> fixtures;

    public ExportInfo(boolean isHd, boolean isTact, boolean isMulti, String name, User u) {
        identifier = GlobalHelper.generateIdentifierFromTime();
        hd = isHd;
        tact = isTact;
        multi = isMulti;
        if (name.trim().isEmpty()) {
            nameFile = "playlist";
        } else {
            nameFile = GlobalHelper.removeUnwantedCharacter(name.trim(), true);
        }

        user = u;
        date = new Date();
    }

    public String getIdentifier() {
        return identifier;
    }

    public boolean isHd() {
        return hd;
    }

    public boolean isTact() {
        return tact;
    }

    public boolean isMulti() {
        return multi;
    }

    public String getNameFile() {
        return nameFile;
    }

    public void addActionToListActions(List<Azione> actions) {
        listActions.addAll(actions);
        countAction = listActions.size();
    }

    public ArrayList<Azione> getListActions() {
        return listActions;
    }

    public long getDuration() {
        if (totalDuration == 0) {
            for (Azione action : listActions) {
                totalDuration += (action.getmEnd() - action.getmStart());
            }
        }
        return totalDuration;
    }

    public User getUser() {
        return user;
    }

    public Playlist getPlaylist() {
        return playlist;
    }

    public void setPlaylist(Playlist playlist) {
        this.playlist = playlist;
    }

    public int getProgressExport() {
        progress = (getTimeProcessed() / getDuration()) + (fileSize > 0 ? (double) uploaded / fileSize : 0);
        progress = progress / 2;
//            progress = (1 - listActions.size() / countAction) + (fileSize > 0 ? (double) uploaded / fileSize : 0);
//            progress = progress / 2;
        return new Double(progress * 100).intValue();
    }

    public void increaseUploaded(long uploaded) {
        this.uploaded += uploaded;
        if (this.uploaded >= fileSize) {
            //System.out.println("Upload completato di " + fileSize + " byte");
        }
        //System.out.println("Trasferiti: " + this.uploaded + " su: " + fileSize);
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public void updatePlaylistSizeVideo() {
        if (getPlaylist() != null) {
            getPlaylist().setSizeVideo(fileSize);
        }
    }

    public String getDateDayString() {
        return DateHelper.toDayExt(date);
    }

    public String getDateMonthString() {
        return DateHelper.toMonthExt(date);
    }

    public UserService getService() {
        return service;
    }

    public void setService(UserService service) {
        this.service = service;
    }

    /**
     * @return the timeProcessed
     */
    public double getTimeProcessed() {
        return timeProcessed;
    }

    /**
     * @param timeProcessed the timeProcessed to set
     */
    public void setTimeProcessed(double timeProcessed) {
        this.timeProcessed = timeProcessed;
    }

    public Map<Long, Game> getFixtures() {
        return fixtures;
    }

    public void setFixtures(Map<Long, Game> fixtures) {
        this.fixtures = fixtures;
    }
}
