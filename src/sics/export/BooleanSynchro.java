package sics.export;

/**
 *
 * <AUTHOR> srl
 */
public class BooleanSynchro {

    private boolean value;

    public BooleanSynchro() {
        this.value = false;
    }

    public BooleanSynchro(boolean value) {
        this.value = value;
    }

    /**
     * @return the wait
     */
    public synchronized boolean value() {
        return value;
    }

    /**
     * @param wait the wait to set
     */
    public synchronized void setValue(boolean wait) {
        this.value = wait;
    }

    public synchronized void unlock() {
        this.notifyAll();
    }

}
