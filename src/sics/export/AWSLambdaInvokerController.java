package sics.export;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.lambda.AWSLambda;
import com.amazonaws.services.lambda.AWSLambdaClientBuilder;
import com.amazonaws.services.lambda.model.GetFunctionConfigurationRequest;
import com.amazonaws.services.lambda.model.GetFunctionConfigurationResult;
import com.amazonaws.services.lambda.model.InvokeRequest;
import com.amazonaws.services.lambda.model.InvokeResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.nio.charset.StandardCharsets;
import sics.helper.ArrayListSynchro;
import sics.helper.GlobalHelper;

/**
 *
 * <AUTHOR>
 */
public class AWSLambdaInvokerController {

    private static final String LAMBDA_FUNCTION_NAME = "sicstv-processing-export";
    private static final String LAMBDA_FUNCTION_CONCAT = "sicstv-processing-concat";
    private static final String LAMBDA_FUNCTION_ZIP = "sicstv-processing-zip";
    private static final String LAMBDA_USER_ACCESS_KEY = "********************";
    private static final String LAMBDA_USER_SECRET_KEY = "Y309prw/wgJhDYvXvunxyGw5GU8ipVpjEe1BN3tA";
    private static final int MAX_TIMEOUT_CONNECTION_CONCAT_ZIP = 365 * 1000; //6 min e 5 sec (ms) --> le lambda hanno 6 min e 3 sec
    private static final int MAX_TIMEOUT_CONNECTION_CLIP = 215 * 1000; //3 min e 35 sec (ms) --> le lambda hanno 3 min e 33 sec

    public static GetFunctionConfigurationResult getLambdaConfiguration() {
        AWSCredentials credentials = new BasicAWSCredentials(LAMBDA_USER_ACCESS_KEY, LAMBDA_USER_SECRET_KEY);
        ClientConfiguration config = new ClientConfiguration();
        config.setConnectionTimeout(MAX_TIMEOUT_CONNECTION_CLIP);
        config.setSocketTimeout(MAX_TIMEOUT_CONNECTION_CLIP);
        AWSLambda awsLambda = AWSLambdaClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(credentials))
                .withRegion(Regions.EU_WEST_1)
                .withClientConfiguration(config)
                .build();

        GetFunctionConfigurationRequest request = new GetFunctionConfigurationRequest().withFunctionName(LAMBDA_FUNCTION_NAME);
        GetFunctionConfigurationResult configResult = awsLambda.getFunctionConfiguration(request);
        return configResult;
    }
    
    public static boolean invokeAWSLambdaClipSync(String video_key, String num_export,
            String video_res, String multi_pack, String start_action, String dur_action, String user_id,
            String groupset_id, String timestamp, String name_export) {
        boolean result = false;
        String error = "";
        if (start_action.isEmpty()) {
            if (!error.isEmpty()) {
                error += "\n";
            }
            error += "ERROR start_action";
        }
        if (dur_action.isEmpty()) {
            if (!error.isEmpty()) {
                error += "\n";
            }
            error += "ERROR dur_action";
        }
        if (timestamp.isEmpty()) {
            if (!error.isEmpty()) {
                error += "\n";
            }
            error += "ERROR timestamp";
        }

        if (!error.isEmpty()) {
            return false;
        }

        try {
            String payload = GlobalHelper.generateClipPayload(video_key, num_export, video_res, multi_pack, start_action, dur_action, user_id, groupset_id, timestamp, name_export);
            InvokeRequest invokeRequest = new InvokeRequest()
                    .withFunctionName(LAMBDA_FUNCTION_NAME)
                    .withPayload(payload);
            InvokeResult invokeResult = null;

            AWSCredentials credentials = new BasicAWSCredentials(LAMBDA_USER_ACCESS_KEY, LAMBDA_USER_SECRET_KEY);
            ClientConfiguration config = new ClientConfiguration();
            config.setConnectionTimeout(MAX_TIMEOUT_CONNECTION_CLIP);
            config.setSocketTimeout(MAX_TIMEOUT_CONNECTION_CLIP);
            AWSLambda awsLambda = AWSLambdaClientBuilder.standard()
                    .withCredentials(new AWSStaticCredentialsProvider(credentials))
                    .withRegion(Regions.EU_WEST_1)
                    .withClientConfiguration(config)
                    .build();

            long startX = System.currentTimeMillis();
            invokeResult = awsLambda.invoke(invokeRequest);
            System.out.println("#" + num_export + " INVOKE CLIP TIME: " + GlobalHelper.getTimeString(System.currentTimeMillis() - startX));
            if (invokeResult != null) {
                String response = new String(invokeResult.getPayload().array(), StandardCharsets.UTF_8);
                if (invokeResult.getStatusCode() == 200) {
                    if (response.contains("\"statusCode\": 200")) {
                        System.out.println("Response Code:" + invokeResult.getStatusCode());
                        System.out.println(response);
                        result = true;
                    } else {
                        System.err.println("Response Code:" + invokeResult.getStatusCode());
                        System.err.println(response);
                    }
                } else {
                    System.err.println("Response Code:" + invokeResult.getStatusCode());
                    System.err.println(response);
                }
            } else {
                System.err.println("ERROR InvokeResult NULL - something went wrong");
            }
        } catch (Exception e) {
            System.err.println("Exception invokeLambda CLIP: " + e.getMessage());
            System.err.println(e);
        }
        return result;
    }

    public static long invokeAWSLambdaZipSync(ArrayListSynchro list, String name) {
        long result = 0;
        String error = "";
        if (list.isEmpty()) {
            if (!error.isEmpty()) {
                error += "\n";
            }
            error += "ERROR! CLIPS LIST EMPTY!";
        }

        if (!error.isEmpty()) {
            return 0;
        }

        String payload = GlobalHelper.generateConcatZipPayload(list, name);

        try {
            InvokeRequest invokeRequest = new InvokeRequest()
                    .withFunctionName(LAMBDA_FUNCTION_ZIP)
                    .withPayload(payload);
            InvokeResult invokeResult = null;

            AWSCredentials credentials = new BasicAWSCredentials(LAMBDA_USER_ACCESS_KEY, LAMBDA_USER_SECRET_KEY);
            ClientConfiguration config = new ClientConfiguration();
            config.setConnectionTimeout(MAX_TIMEOUT_CONNECTION_CONCAT_ZIP);
            config.setSocketTimeout(MAX_TIMEOUT_CONNECTION_CONCAT_ZIP);
            AWSLambda awsLambda = AWSLambdaClientBuilder.standard()
                    .withCredentials(new AWSStaticCredentialsProvider(credentials))
                    .withRegion(Regions.EU_WEST_1)
                    .withClientConfiguration(config)
                    .build();

            invokeResult = awsLambda.invoke(invokeRequest);

            if (invokeResult != null) {
                String response = new String(invokeResult.getPayload().array(), StandardCharsets.UTF_8);
                JsonNode jsonResult = getJsonResult(response);
                if (invokeResult.getStatusCode() == 200) {
                    if (jsonResult.get("statusCode").asInt(500) == 200) {
                        System.out.println("Response Code:" + jsonResult.get("statusCode").asInt(500));
                        System.out.println(response);
                        if (!jsonResult.get("size").asText("").isEmpty()) {
                            Float f = Float.valueOf(jsonResult.get("size").asText(""));
                            if (f > 0) {
                                result = Math.round(f);
                            }
                        }
                    } else {
                        System.err.println("Response Code:" + invokeResult.getStatusCode());
                        System.err.println(response);
                    }
                } else {
                    System.err.println("Response Code:" + invokeResult.getStatusCode());
                    System.err.println(response);
                }
            } else {
                System.err.println("ERROR InvokeResult NULL - something went wrong");
            }
        } catch (Exception e) {
            System.err.println("Exception invokeLambda CONCAT: " + e.getMessage());
            System.err.println(e);
        }
        return result;
    }

    public static long invokeAWSLambdaConcatSync(ArrayListSynchro list, String name) {
        long result = 0;
        String error = "";
        if (list.isEmpty()) {
            if (!error.isEmpty()) {
                error += "\n";
            }
            error += "ERROR! CLIPS LIST EMPTY!";
        }

        if (!error.isEmpty()) {
            return 0;
        }

        String payload = GlobalHelper.generateConcatZipPayload(list, name);

        try {
            InvokeRequest invokeRequest = new InvokeRequest()
                    .withFunctionName(LAMBDA_FUNCTION_CONCAT)
                    .withPayload(payload);
            InvokeResult invokeResult = null;

            AWSCredentials credentials = new BasicAWSCredentials(LAMBDA_USER_ACCESS_KEY, LAMBDA_USER_SECRET_KEY);
            ClientConfiguration config = new ClientConfiguration();
            config.setConnectionTimeout(MAX_TIMEOUT_CONNECTION_CONCAT_ZIP);
            config.setSocketTimeout(MAX_TIMEOUT_CONNECTION_CONCAT_ZIP);
            AWSLambda awsLambda = AWSLambdaClientBuilder.standard()
                    .withCredentials(new AWSStaticCredentialsProvider(credentials))
                    .withRegion(Regions.EU_WEST_1)
                    .withClientConfiguration(config)
                    .build();

            invokeResult = awsLambda.invoke(invokeRequest);

            if (invokeResult != null) {
                String response = new String(invokeResult.getPayload().array(), StandardCharsets.UTF_8);
                JsonNode jsonResult = getJsonResult(response);
                if (invokeResult.getStatusCode() == 200) {
                    if (jsonResult.get("statusCode").asInt(500) == 200) {
                        System.out.println("Response Code:" + jsonResult.get("statusCode").asInt(500));
                        System.out.println(response);
                        if (!jsonResult.get("size").asText("").isEmpty()) {
                            Float f = Float.valueOf(jsonResult.get("size").asText(""));
                            if (f > 0) {
                                result = Math.round(f);
                            }
                        }
                    } else {
                        System.err.println("Response Code:" + invokeResult.getStatusCode());
                        System.err.println(response);
                    }
                } else {
                    System.err.println("Response Code:" + invokeResult.getStatusCode());
                    System.err.println(response);
                }
            } else {
                System.err.println("ERROR InvokeResult NULL - something went wrong");
            }
        } catch (Exception e) {
            System.err.println("Exception invokeLambda CONCAT: " + e.getMessage());
            System.err.println(e);
        }
        return result;
    }
    
    private static JsonNode getJsonResult(String jsonString) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode actualObj = mapper.readTree(jsonString);
            return actualObj;
        } catch (Exception e) {
            System.err.println("Exception getJsonResult");
            return null;
        }
    }
}
