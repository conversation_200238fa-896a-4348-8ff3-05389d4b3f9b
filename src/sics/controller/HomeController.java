package sics.controller;

import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;  //Classe per il mapping delle richieste
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.dao.service.ManagerService;
import sics.domain.Atleta;
import sics.domain.FilterGameWrapper;
import sics.domain.Game;
import sics.domain.Playlist;
import sics.domain.PlaylistTV;
import sics.domain.PlaylistTVEvent;
import sics.domain.PlaylistTVSharePublic;
import sics.domain.Season;

import sics.domain.User;
import sics.helper.DynamicReloadableResourceBundleMessageSource;
import sics.helper.GlobalHelper;
import sics.helper.MailHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.listener.SessionListener;
import sics.model.Azione;
import sics.service.HomeService;
import sics.service.UserService;

@Controller
@RequestMapping("/auth")
public class HomeController extends BaseController {

    private final static String pageLogin = "auth/login.jsp";
    private final static String pageLostPwd = "auth/lostpwd.jsp";
    private final static String pageVideo = "auth/video.jsp";
    private final static String pageVideoPlaylistTv = "auth/videoPlaylistTv.jsp";
    private final static String page404 = "auth/404.jsp";
    private final static String page404Playlist = "auth/404Playlist.jsp";
    private final static String page403 = "auth/403.jsp";
    private final static String pagePlaylistLimit = "auth/playlistLimit.jsp";
    private final static String page204 = "auth/204.jsp";
    private final static String pageMultipleSession = "auth/multipleSession.jsp";
    
    private final static String pageTest = "auth/test.jsp";
    
    private final static String pageHome = "user/home.jsp";

    private HomeService mService;

    @Autowired
    public void setHomeService(HomeService homeService) {
        this.mService = homeService;
    }

    @RequestMapping("/404")
    public String error404(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            this.initModule(session, model, request, response);
        }
        return page404;
    }

    @RequestMapping("/403")
    public String error403(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            this.initModule(session, model, request, response);
        }
        return page403;
    }
    
    @RequestMapping("/404Playlist")
    public String error404Playlist(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            this.initModule(session, model, request, response);
        }
        return page404Playlist;
    }
    
    @RequestMapping("/playlistLimit")
    public String errorPlaylistLimit(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            this.initModule(session, model, request, response);
        }
        return pagePlaylistLimit;
    }
    
    @RequestMapping("/204")
    public String error204(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "parentLink", required = false) String parentLink, @RequestParam(value = "playerId", required = false) Long playerId,
            @RequestParam(value = "seasonId", required = false) Long seasonId) {
        if (!this.initModule(session, model, request, response)) {
            this.initModule(session, model, request, response);
        }

        String language = RequestContextUtils.getLocale(request).getLanguage().toLowerCase();
        if (language.equalsIgnoreCase("it")) {
            language = "";
        } else {
            language = "_en";
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        
        List<Season> seasonVisible = mService.getSeasonAll();
        List<Season> seasonWithData = new ArrayList<>();
        String playerName = null;
        if (seasonId != null) {
            for (Season season : seasonVisible) {
                if (Long.compare(season.getId(), seasonId) == 0) {
                    model.addAttribute("mSeasonSelected", season);
                }
                
                Atleta player = mService.getAtletaById(season.getId(), playerId, -1L, curUser.getGroupsetId(), false, language);
                if (player != null) {
                    if (playerName == null) {
                        playerName = player.getLast_name() + " " + player.getFirst_name();
                    }
                    
                    seasonWithData.add(season);
                }
            }
        }
        
        model.addAttribute("mSeasons", seasonWithData);
        if (parentLink != null) {
            model.addAttribute("mParentLink", GlobalHelper.getURLEconded(parentLink));
        }
        if (playerName != null) {
            model.addAttribute("mPlayer", playerName);
        }
       
        return page204;
    }

    @RequestMapping("/video")
    public String video(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "videoname", required = false) String videoname,
            @RequestParam(value = "game", required = false) String ownerUserId, @RequestParam(value = "playlist", required = false) String isPlaylist,
            ModelMap model, HttpSession session) {

        this.initModule(session, model, request, response);

        String video = "";
        String scaduto = "";
        String title = "";
        String scadenza = "";
        if (videoname != null) {
            try {
                byte[] decoded = Base64.decodeBase64(videoname);
                String pathDecoded = new String(decoded);
                if (ownerUserId != null && isPlaylist != null && !ownerUserId.equals("undefined") && !isPlaylist.equals("undefined")) {
//                String pathDecoded = new String(decoded);
                    Long ownerUserIdLong = Long.valueOf(ownerUserId);
                    Long isPlaylistLong = Long.valueOf(isPlaylist);
                    if (pathDecoded.contains("https://") || pathDecoded.contains("http://")) {
                        String pathRemoveFixedPart = pathDecoded.replace("https://s3-eu-west-1.amazonaws.com/it.sics.svs.playlist/", "").replace("https://s3.eu-west-1.amazonaws.com/it.sics.svs.playlist/", "").replace("http://s3.eu-west-1.amazonaws.com/it.sics.svs.playlist/", "").replace("http://s3-eu-west-1.amazonaws.com/it.sics.svs.playlist/", "").replace("http://s3-eu-west-1.amazonaws.com/it.sics.svs/", "").replace("https://s3-eu-west-1.amazonaws.com/it.sics.svs/", "");
                        video = pathRemoveFixedPart.split("\\?")[0];
                        String dataCreazione = pathRemoveFixedPart.replace(video + "?", "").split("Amz-Date=")[1].substring(0, pathRemoveFixedPart.replace(video + "?", "").split("Amz-Date=")[1].indexOf("&"));
                        String tempoScadenzaString = pathRemoveFixedPart.replace(video + "?", "").split("Amz-Expires=")[1];
                        if (StringUtils.contains(tempoScadenzaString, "&")) {
                            tempoScadenzaString = tempoScadenzaString.substring(0, tempoScadenzaString.indexOf("&"));
                        }
                        Integer tempoScadenza = Integer.valueOf(tempoScadenzaString);
                        Date dateCreazione = new SimpleDateFormat("yyyyMMddhhmmss", Locale.ITALY).parse(dataCreazione.replace("T", "").replace("Z", ""));
                        Calendar cal = Calendar.getInstance(Locale.ITALY);
                        cal.setTime(new Date());
                        Calendar calExp = Calendar.getInstance(Locale.ITALY);
                        calExp.setTime(dateCreazione);
                        calExp.add(Calendar.SECOND, tempoScadenza);
                        Date dateToExpire = calExp.getTime();
                        SimpleDateFormat kDateFormatYYYYMMDDhhmmss = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
                        scadenza = kDateFormatYYYYMMDDhhmmss.format(dateToExpire);
                        //1676281464
                        if (isPlaylistLong != 0 && ownerUserIdLong == 0) {
                            if (cal.before(calExp)) {
                                scaduto = "false";
                                Playlist pl = mService.getPlaylistByVideoName(video, isPlaylistLong);
                                if (pl != null) {
                                    title = pl.getName();
                                    video = GlobalHelper.createCloudFrontSignedURL(GlobalHelper.getUrlEncoded(video, true), dateToExpire);
//                                video = "https://s3-eu-west-1.amazonaws.com/it.sics.svs.playlist/" + video;
                                } else {
                                    scaduto = "";
                                    video = "";
                                    title = "";
                                }
                            } else {
                                scaduto = "true";
                                video = "";
                                title = "";
                            }
                        } else if (ownerUserIdLong != 0 && isPlaylistLong == 0) {
                            if (cal.before(calExp)) {
                                scaduto = "false";
                                String language = RequestContextUtils.getLocale(request).getLanguage().toLowerCase();
                                if (language.equalsIgnoreCase("it")) {
                                    language = "";
                                } else {
                                    language = "_en";
                                }
                                Game gm = mService.getGameByVideoName(video, ownerUserIdLong, language);
                                if (gm != null) {
                                    title = gm.getHomeTeam() + " - " + gm.getAwayTeam() + " | " + gm.getCompetitionName() + " | " + gm.getDateDayString() + " " + gm.getDateMonthString() + " " + gm.getDateYearString();
//                                video = "https://s3-eu-west-1.amazonaws.com/it.sics.svs/" + video;
                                    video = GlobalHelper.createCloudFrontSignedURL(GlobalHelper.getUrlEncoded(video, false), dateToExpire);

                                } else {
                                    scaduto = "";
                                    video = "";
                                    title = "";
                                }
                            } else {
                                scaduto = "true";
                                video = "";
                                title = "";
                            }
                        } else {
                            scaduto = "";
                            video = "";
                            title = "";
                        }
                    } else {
                        scaduto = "";
                        video = "";
                        title = "";
                    }
                } else if (!pathDecoded.contains("https://") && !pathDecoded.contains("http://")) {
                    video = pathDecoded;
                    Playlist pl = mService.getPlaylistByVideoName(video, null);
                    if (pl != null) {
                        title = pl.getName();
                        video = GlobalHelper.pathS3(video, "playlist", true);
//                    video = "https://s3-eu-west-1.amazonaws.com/it.sics.svs.playlist/" + video;
                        scaduto = "false";
                    }
                }
            } catch (ArrayIndexOutOfBoundsException | NumberFormatException | ParseException e) {
                scaduto = "";
                video = "";
                title = "";
                //e.printStackTrace();
            }
        }
        model.addAttribute("videoname", video);
        model.addAttribute("mScaduto", scaduto);
        model.addAttribute("title", title);
        model.addAttribute("scadenza", scadenza);

        return pageVideo;
    }
    
    @RequestMapping("/videoPlaylistTv")
    public String videoPlaylistTv(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "playlistId", required = true) Long playlistId,
            ModelMap model, HttpSession session) {

        this.initModule(session, model, request, response);
        String language = RequestContextUtils.getLocale(request).getLanguage().toLowerCase();
        if (language.equalsIgnoreCase("it")) {
            language = "";
        } else {
            language = "_en";
        }

        UserService userService = new UserService();
        PlaylistTV playlist = userService.getPlaylistTvById(playlistId);                
        if (playlist == null) { // playlist non trovata / cancellata
            return pageRedirect(page404Playlist);
        }
        
        PlaylistTVSharePublic sharedPlaylist = userService.getPlaylistTvSharePublicByUrl(request.getRequestURI() + "?" + request.getQueryString());
        if (sharedPlaylist == null) { // condivisione cancellata
            return pageRedirect(page404Playlist);
        }
        
        // controllo click
        if (sharedPlaylist.getClick() == null || sharedPlaylist.getClick() < sharedPlaylist.getMaxClick()) {
            userService.updatePlaylistTvSharePublicClick(sharedPlaylist.getId(), sharedPlaylist.getClick() == null ? 1 : (sharedPlaylist.getClick() + 1));
            
            model.addAttribute("mSharingUser", userService.getUser(playlist.getUserId()));
            model.addAttribute("mPlaylist", playlist);

            List<PlaylistTVEvent> eventsList = userService.getPlaylistTvEventsById(playlistId);
            List<String> eventIdList = new ArrayList<>();
            for (PlaylistTVEvent tmpEvent : eventsList) {
                eventIdList.add(tmpEvent.getEventId().toString());
            }

            ArrayList<Azione> allActions = new ArrayList<>();
            FilterGameWrapper filterGameWrapper = new FilterGameWrapper();
            filterGameWrapper = userService.readEventMatchesByEventIds(StringUtils.join(eventIdList, ","), filterGameWrapper.getGames(), 0L, 2L, playlistId, null, null, language);

            for (ArrayList<Azione> azList : filterGameWrapper.getResultActions().values()) {
                allActions.addAll((ArrayList<Azione>) azList.clone());
            }
            // carico ora una lista di azioni che dovranno essere visualizzate in modalità tattico
            List<Long> tacticalActionList = new ArrayList<>();
            for (Azione azione : allActions) {
                if (BooleanUtils.isTrue(azione.getIsTactical())) {
                    tacticalActionList.add(azione.getId());
                }
            }
            model.addAttribute("mTacticalEventIdList", tacticalActionList);
            model.addAttribute("mEventAll", allActions);
            model.addAttribute("mGame", filterGameWrapper.getGames());

            LinkedHashMap<Long, List> eventsOrdered = new LinkedHashMap<>();
            List<Long> listKeys = new ArrayList<>(filterGameWrapper.getResultActions().keySet());
            Collections.sort(listKeys, Collections.reverseOrder()); 
            for (Long k : listKeys) {
                eventsOrdered.put(k, filterGameWrapper.getResultActions().get(k));
            }
            LinkedHashMap<Long, List> eventsOrderedChoosed = eventsOrdered;
            model.addAttribute("mEvent", eventsOrderedChoosed);

            return pageVideoPlaylistTv;
        } else { // visualizzazioni finite
            return pageRedirect(pagePlaylistLimit);
        }
    }

    @RequestMapping("/login")
    public String login(ModelMap model, HttpSession session,
            @RequestParam(value = "expired", required = false) Boolean expired, @RequestParam(value = "login_error", required = false) Boolean isError,
            HttpServletRequest request, HttpServletResponse response) {

        this.initModule(session, model, request, response);

        String strLocale = RequestContextUtils.getLocale(request).getLanguage().toLowerCase();
        Locale loc;
        if (strLocale.equalsIgnoreCase("it")) {
            loc = new Locale(strLocale);
        } else {
            loc = new Locale("en");
        }
        
        // se sono già loggato faccio redirect alla pagina home, non ha senso rifare il login
//        try {
//            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
//            if (curUser != null) {
//                List<HttpSession> userSession = SessionListener.getUserSessions(curUser.getId());
//                if (userSession != null && userSession.contains(session)) {
//                    return pageRedirect(pageHome);
//                }
//            }
//        } catch (Exception ex) {
//            GlobalHelper.reportError(ex);
//        }
        
        SavedRequest savedRequest = new HttpSessionRequestCache().getRequest(request, response);
        
//        RequestContextUtils.getLocaleResolver(request).setLocale(request, response, loc);
//        if (savedRequest != null &&
//                !savedRequest.getParameterMap().isEmpty() &&
//                savedRequest.getParameterMap().get("ser") != null &&
//                !savedRequest.getParameterMap().get("ser")[0].isEmpty() &&
//                BooleanUtils.isNotTrue(isError)) {
//            String ser = savedRequest.getParameterMap().get("ser")[0];
//            UserService userService = new UserService();
//            User user = userService.getUserBySer(ser);
//            if (user != null) {
//                model.addAttribute("usr", user.getUsername());
//                model.addAttribute("psw", user.getPassword());
//            } else {
//                String username, password;
//                byte[] serDecode = Base64.decodeBase64(ser);
//                String[] splitSer = new String(serDecode).split("##");
//                if (splitSer.length == 2) {
//                    username = splitSer[0];
//                    password = splitSer[1];
//                    model.addAttribute("usr", username);
//                    model.addAttribute("psw", password);
//                }
//            }
//
//            // Per evitare login infiniti, in qualsiasi caso tolgo tutti i parametri così
//            // non torna più dentro a questo controllo e ritorna errore se necessario
//            savedRequest.getParameterMap().clear();
//        }
        return pageLogin;
    }

    @RequestMapping("/logout")
    public String logout(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {

        this.initModule(session, model, request, response);
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);

        if (curUser != null) {
            try {
                SecurityContextHolder.clearContext(); // Rimuove l'utente autenticato
                SessionListener.destroySession(session, curUser.getId());
            } catch (Exception ex) {
                GlobalHelper.sendExceptionMail(request, ex);
                Logger.getLogger(HomeController.class.getName()).log(Level.SEVERE, "/logout page. can't invalidate current session. Already invalidated ?");
            }
            
            GlobalHelper.writeLogData(session, GlobalHelper.kActionLogoutSession, curUser.getUsername(), curUser.getPassword(), curUser.getId());
//            if (SessionListener.getUserSessions(curUser.getId()) != null) {
//                List<HttpSession> currentUserSessionList = new ArrayList<>(SessionListener.getUserSessions(curUser.getId()));
//                for (HttpSession tmpSession : currentUserSessionList) {
//                    // il punto qua è che DEVE dare errore se ce ne sono altre
//                    // perchè significa che c'è una sessione che non viene tolta dal SessionListener
//                    try {
//                        if (!StringUtils.equals(tmpSession.getId(), session.getId())) {
//                            tmpSession.invalidate();
//                        }
//                    } catch (Exception ex) {
//                        GlobalHelper.sendExceptionMail(ex);
//                        Logger.getLogger(HomeController.class.getName()).log(Level.SEVERE, "/logout page. can't invalidate session. Already invalidated ?");
//                    }
//                }
//            }
        } else {
            Logger.getLogger(HomeController.class.getName()).log(Level.SEVERE, "/logout page. curUser is null");
        }

        return pageLogin;
    }

    @RequestMapping("/lostpwd")
    public String lostpwd(@RequestParam("formUsername") String formUsername, ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        this.initModule(session, model, request, response);
        ServletContext servletContext = session.getServletContext();
        WebApplicationContext ctx = WebApplicationContextUtils.getWebApplicationContext(servletContext);
        ManagerService manager = (ManagerService) ctx.getBean(GlobalHelper.kBeanAppManager);
        User curUser = null;
        if (!formUsername.isEmpty()) {
            curUser = mService.getUserByMail(formUsername);
        }
        if (!formUsername.isEmpty() && curUser == null) {
            try {
                curUser = manager.getUser(formUsername);
            } catch (SQLException e) {
                curUser = null;
            }
        }

        if (curUser != null && curUser.getDatascadenza().after(new Date())) {
            model.addAttribute("mUser", curUser);
            model.addAttribute("mMail", formUsername);
            String emailTo = curUser.getEmail();
            if (emailTo != null) {
                String emailFrom = SpringApplicationContextHelper.getMessage("email.from");
                String strHello = SpringApplicationContextHelper.getMessage("email.hello", curUser.getFirstName() + " " + curUser.getLastName());
                String strHeader = "SICS.tv";
                String pswB64 = "";
                for (char a : Base64.encodeBase64URLSafeString(curUser.getPassword().getBytes()).toCharArray()) {
                    pswB64 = a + pswB64;
                }
                String strSubject = strHeader + " - " + SpringApplicationContextHelper.getMessage("email.pwd.smarrita.subject");
                String strContent = SpringApplicationContextHelper.getMessage("email.pwd.smarrita.text", pswB64, curUser.getUsername());
                MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                mail.sendMail(emailFrom, emailTo, null, "<EMAIL>", strHeader, strHello, strSubject, strContent, null, pswB64);
            }
        } else {
            model.addAttribute("mUser", null);
            model.addAttribute("mMail", null);
        }

        return pageLostPwd;
    }

    @RequestMapping(value = "/updateTranslations")
    public @ResponseBody
    String updateTranslations(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request) {
        try {
            DynamicReloadableResourceBundleMessageSource.checkDatabase(true);
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return "ok";
    }

    @RequestMapping("/multipleSession")
    public String multipleSession(HttpServletRequest request, HttpServletResponse response, ModelMap model, HttpSession session) {

        this.initModule(session, model, request, response);
        return pageMultipleSession;
    }
}
