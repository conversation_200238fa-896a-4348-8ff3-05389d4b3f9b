package sics.controller;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.mobile.device.Device;
import org.springframework.ui.ModelMap;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.dao.service.ManagerService;
import sics.domain.Settings;
import sics.domain.User;
import sics.helper.GlobalHelper;

public class BaseController {

    protected transient final Log log = LogFactory.getLog(getClass());
    protected final static String pageRedirect = "redirect:";

    protected Device mCurrentDevice = null;
    protected static List<String> tacticalEventTypes = Arrays.asList("COS", "PRE", "ATA", "DIA", "TRO", "TRD");

    public BaseController() {

    }

    protected boolean initModule(HttpSession session, ModelMap model, HttpServletRequest request, HttpServletResponse response) {

        //ServletRequestAttributes sra = (ServletRequestAttributes)RequestContextHolder.getRequestAttributes();
        //mCurrentDevice = DeviceUtils.getCurrentDevice(sra.getRequest());
        //model.addAttribute("mCurrentDevice", mCurrentDevice);
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        Locale locale = null;
        if (curUser != null) {
            if (StringUtils.isBlank(curUser.getTvLanguage())) {
                // carico settings
                ServletContext servletContext = session.getServletContext();
                WebApplicationContext ctx = WebApplicationContextUtils.getWebApplicationContext(servletContext);
                ManagerService manager = (ManagerService) ctx.getBean(GlobalHelper.kBeanAppManager);
                if (manager != null) {
                    try {
                        Settings userSettings = manager.getSettingsByUserId(curUser.getId());
                        if (userSettings == null) {
                            userSettings = new Settings();
                            userSettings.setUserId(curUser.getId());
                            userSettings.setApplication(1);
                            userSettings.setEventAdditionalStart(0L);
                            userSettings.setEventAdditionalEnd(0L);
                            userSettings.setTvLanguage(StringUtils.defaultIfEmpty(curUser.getLanguage(), "en"));

                            manager.addSettings(userSettings);
                        }

                        // viene impostato basandosi sulla colonna "language" di user
                        curUser.setTvLanguage(userSettings.getTvLanguage());
                    } catch (SQLException ex) {
                        GlobalHelper.reportError(ex);
                    } finally {
                        if (StringUtils.isBlank(curUser.getTvLanguage())) {
                            // sicurezza
                            curUser.setTvLanguage("en");
                        }
                    }
                } else {
                    // sicurezza
                    curUser.setTvLanguage("en");
                }
            }

            if (StringUtils.equals(curUser.getTvLanguage(), "es")) {
                locale = new Locale("es", "ES");
            } else {
                locale = new Locale(StringUtils.defaultIfEmpty(curUser.getTvLanguage(), Locale.ENGLISH.getLanguage()));
            }
        } else {
            locale = new Locale(Locale.ENGLISH.getLanguage());
        }
        RequestContextUtils.getLocaleResolver(request).setLocale(request, response, locale);

        model.addAttribute("mUser", curUser);
        model.addAttribute("mLanguage", (String) session.getAttribute(GlobalHelper.kBeanLanguage));
        model.addAttribute("mExportUseLambda", GlobalHelper.USE_LAMBDA);

        return (curUser != null);
    }

    protected String pageRedirect(String page) {
        return pageRedirect + "/" + page.replace(".jsp", ".htm");
    }

}
