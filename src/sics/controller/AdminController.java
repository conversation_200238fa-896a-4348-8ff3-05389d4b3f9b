package sics.controller;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TreeMap;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import sics.service.AdminService;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.analysis.Punto;
import sics.domain.AgentPayment;
import sics.domain.ChartData;
import sics.domain.DatabaseEditor;
import sics.domain.Export;
import sics.domain.ExportDetail;
import sics.domain.Game;
import sics.domain.TomcatLog;
import sics.domain.User;
import sics.domain.VtigerAgent;
import sics.helper.AgentExcelReader;
import sics.helper.DateHelper;
import sics.helper.GlobalHelper;
import sics.helper.MailHelper;
import sics.helper.ModuleHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.listener.SessionListener;

@Controller
@RequestMapping("/admin")
public class AdminController extends BaseController {

    private final static String pageLibrary = "user/library.jsp";
    private final static String pageUserList = "admin/userlist.jsp";
    private final static String pageLogList = "admin/loglist.jsp";
    private final static String pageLogData = "admin/logdata.jsp";
    private final static String pageUserData = "admin/userdata.jsp";
    private final static String pageStats = "admin/stats.jsp";
    private final static String userStatsDetails = "admin/userStatsDetails.jsp";
    private final static String streamingStats = "admin/userStreaming.jsp";
    private final static String pageHome = "admin/home.jsp";
    private final static String pageHomeActiveSession = "admin/homeActiveSession.jsp";
    private final static String pageUserDetail = "admin/userDetail.jsp";
    private final static String pageCompetitionDetails = "admin/competitionDetails.jsp";
    private final static String pageUserLog = "admin/userLog.jsp";
    private final static String pageAgentPayment = "admin/agentPayment.jsp";
    private final static String pageModuleChecker = "admin/moduleChecker.jsp";

    private AdminService aService;

    @Autowired
    public void setHomeService(AdminService homeService) {
        this.aService = homeService;
    }

    @RequestMapping(value = "/home")
    public String home(
            @RequestParam(value = "timeFilter", required = false) Integer timeFilterId, @RequestParam(value = "agentFilter", required = false) Long agentId,
            ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {

        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(pageLibrary);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date toDate = new Date();
        Date fromDate = new Date();
        if (timeFilterId != null) {
            model.addAttribute("mTimeFilter", timeFilterId);

            switch (timeFilterId) {
                case 1:
                    fromDate = DateUtils.addDays(fromDate, -1);
                    break;
                case 2:
                    fromDate = DateUtils.addWeeks(fromDate, -1);
                    break;
                case 3:
                    fromDate = DateUtils.addMonths(fromDate, -1);
                    break;
                case 4:
                    fromDate = DateUtils.addYears(fromDate, -1);
                    break;
                default:
                    break;
            }
        } else {
            fromDate = DateUtils.addWeeks(fromDate, -1);
        }

        model.addAttribute("mFromDate", df.format(fromDate));
        model.addAttribute("mToDate", df.format(toDate));

        List<VtigerAgent> vtigerAgentList = aService.getVtigerAgents();
        Collections.sort(vtigerAgentList, new Comparator<VtigerAgent>() {
            @Override
            public int compare(VtigerAgent o1, VtigerAgent o2) {
                return o1.getFirstName().compareTo(o2.getFirstName());
            }
        });
        VtigerAgent everyone = new VtigerAgent();
        everyone.setId(0L);
        everyone.setFirstName("Tutti");
        vtigerAgentList.add(0, everyone);
        model.addAttribute("mVtigerAgents", vtigerAgentList);

        if (agentId != null) {
            model.addAttribute("mAgentFilter", agentId);
        } else {
            if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 1) != 0) {
                for (VtigerAgent agent : vtigerAgentList) {
                    if (agent.getFirstName().equals(curUser.getFirstName()) && agent.getLastName().equals(curUser.getLastName())) {
                        model.addAttribute("mAgentFilter", agent.getId());
                        break;
                    }
                }
            } else {
                model.addAttribute("mAgentFilter", 0L);
            }
        }

        Long modelAgentId = (Long) model.get("mAgentFilter");
        if (modelAgentId == null) {
            return pageRedirect("auth/403.jsp");
        }
        model.addAttribute("mAllUser", aService.getSicsTvUsers(timeFilterId.longValue(), modelAgentId));

        return pageHome;
    }

    @RequestMapping(value = "/userDetail")
    public String userDetail(@RequestParam("userId") Long userId, @RequestParam("from") String from, @RequestParam("to") String to, HttpServletRequest request, ModelMap model, HttpSession session, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(pageLibrary);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        User checkedUser = aService.getUser(userId);
        String language = RequestContextUtils.getLocale(request).getLanguage().toLowerCase();
        if (language.equalsIgnoreCase("it")) {
            language = "";
        } else {
            language = "_en";
        }

        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        // FORMATO DATE: yyyy-MM-dd
//        SimpleDateFormat df = new SimpleDateFormat("dd/MM/yyyy", Locale.ITALY);
//        Date fromDate = df.parse(from);
//        Date toDate = df.parse(to);
        List<ChartData> userDetailData = aService.getUserLogData(userId, from, to);
        // bisogna ora raggrupparle per azione
        List<ChartData> loginAndLogoutData = new ArrayList<>();
        List<ChartData> startExportData = new ArrayList<>();
        List<ChartData> videoDownloadData = new ArrayList<>();
        List<ChartData> videoStreamData = new ArrayList<>();
        for (ChartData data : userDetailData) {
            switch (data.getAction()) {
                case GlobalHelper.kActionLoginCorrect:
                case GlobalHelper.kActionLogoutSession:
                case GlobalHelper.kActionSessionUpdated:
                case GlobalHelper.kActionSessionMaintained:
                case GlobalHelper.kActionSessionExpired:
                case GlobalHelper.kActionLoginError:
                case GlobalHelper.kActionLogoutTimeout:
                    loginAndLogoutData.add(data);
                    break;
                case GlobalHelper.kActionStartExport:
                case GlobalHelper.kActionEndExport:
                    startExportData.add(data);
                    break;
                case GlobalHelper.kActionVideoDownload:
                    videoDownloadData.add(data);
                    break;
                case GlobalHelper.kActionVideoStreaming:
                    videoStreamData.add(data);
                    break;
                default:
                    break;
            }
        }
        model.addAttribute("mLoginLogoutData", loginAndLogoutData);
        model.addAttribute("mStartExportData", startExportData);
        model.addAttribute("mVideoDownloadData", videoDownloadData);
        model.addAttribute("mVideoStreamData", videoStreamData);

        if (!videoStreamData.isEmpty()) {
            Map<Long, Integer> fixtureCounterMap = new HashMap<>();
            for (ChartData data : videoStreamData) {
                String logDescription = data.getVideoId();
                if (logDescription != null && !logDescription.isEmpty()) {
                    logDescription = logDescription.replace("Multi:", "");
                    List<String> fixtureIds = Arrays.asList(StringUtils.split(logDescription, ","));
                    for (String fixtureId : fixtureIds) {
                        fixtureCounterMap.put(Long.valueOf(fixtureId), fixtureCounterMap.getOrDefault(Long.valueOf(fixtureId), 0) + 1);
                    }
                }
            }

            if (!fixtureCounterMap.isEmpty()) {
                List<Game> fixtureList = aService.getGamesByFixtureIds(new ArrayList<>(fixtureCounterMap.keySet()), checkedUser.getGroupsetId(), language);
                Map<String, Integer> competitionViewsMap = new HashMap<>();
                Map<String, List<Game>> competitionDetailsMap = new HashMap<>();

                for (Game game : fixtureList) {
                    game.setCounter(fixtureCounterMap.getOrDefault(game.getIdFixture(), 1));

                    competitionViewsMap.put(game.getCompetitionName(), competitionViewsMap.getOrDefault(game.getCompetitionName(), 0) + game.getCounter());
                    competitionDetailsMap.putIfAbsent(game.getCompetitionName(), new ArrayList<Game>());
                    competitionDetailsMap.get(game.getCompetitionName()).add(game);
                }

                //model.addAttribute("mFixtures", fixtureList);
                model.addAttribute("mCompetitionRows", competitionViewsMap);
                model.addAttribute("mCompetitionDetails", competitionDetailsMap);
            }
        }

        List<ChartData> userLast100Data = aService.getUserLast100LogData(userId);
        model.addAttribute("mLast100Data", userLast100Data);

        // gestione esportazioni
        List<Export> userExport = aService.getExportByUserAndData(userId, checkedUser.getGroupsetId(), from, to);
        List<ExportDetail> userExportDetail = aService.getExportDetailByUserAndData(userId, checkedUser.getGroupsetId(), from, to);
        Map<Export, List<ExportDetail>> userExports = new LinkedHashMap<>();
        long userTimeUsed = 0;
        for (Export export : userExport) {
            userTimeUsed += (export.getTotalVideoDuration() != null ? export.getTotalVideoDuration() : 0);
            userExports.putIfAbsent(export, new ArrayList<ExportDetail>());

            for (ExportDetail detail : userExportDetail) {
                if (Long.compare(detail.getExportId(), export.getId()) == 0) {
                    userExports.get(export).add(detail);
                }
            }
        }
        model.addAttribute("mExports", userExports);
        Long userExportLimit = aService.getUserExportLimit(userId);
        // Long userTimeUsed = aService.getUserExportTimeUsed(userId); calcolo dalla lista delle esportazioni
        model.addAttribute("totExportMinutes", userExportLimit);
        model.addAttribute("totExportUsed", Math.round(userTimeUsed / 60000D));
        model.addAttribute("mCompletataLabel", SpringApplicationContextHelper.getMessage("menu.user.export.completata", RequestContextUtils.getLocale(request)));
        model.addAttribute("mErroreLabel", SpringApplicationContextHelper.getMessage("menu.user.export.in.errore", RequestContextUtils.getLocale(request)));

        List<Long> tmpUserIdList = new ArrayList<>();
        tmpUserIdList.add(userId);
        List<User> user = aService.getUsers(tmpUserIdList);
        if (!user.isEmpty()) {
            model.addAttribute("mUserSearched", user.get(0));
        }

        model.addAttribute("mFromDate", from);
        model.addAttribute("mToDate", to);

        return pageUserDetail;
    }

    @RequestMapping(value = "/competitionDetails")
    public String competitionDetails(@RequestParam("timeFilter") Integer timeFilterId, HttpServletRequest request, ModelMap model, HttpSession session, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(pageLibrary);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        String language = RequestContextUtils.getLocale(request).getLanguage().toLowerCase();
        if (language.equalsIgnoreCase("it")) {
            language = "";
        } else {
            language = "_en";
        }

        List<ChartData> userDetailData = aService.getVideoStreamData(timeFilterId, null);
        Map<Long, Integer> fixtureCounterMap = new HashMap<>();
        Map<Long, List<Long>> fixtureUserCounterMap = new HashMap<>();
        for (ChartData data : userDetailData) {
            String logDescription = data.getVideoId();
            if (logDescription != null && !logDescription.isEmpty()) {
                logDescription = logDescription.replace("Multi:", "");
                List<String> fixtureIds = Arrays.asList(StringUtils.split(logDescription, ","));
                for (String fixtureId : fixtureIds) {
                    fixtureCounterMap.put(Long.valueOf(fixtureId), fixtureCounterMap.getOrDefault(Long.valueOf(fixtureId), 0) + 1);
                    fixtureUserCounterMap.putIfAbsent(Long.valueOf(fixtureId), new ArrayList<Long>());
                    if (!fixtureUserCounterMap.get(Long.valueOf(fixtureId)).contains(data.getUserId())) {
                        fixtureUserCounterMap.get(Long.valueOf(fixtureId)).add(data.getUserId());
                    }
                }
            }
        }

        if (!fixtureCounterMap.isEmpty()) {
            List<Game> fixtureList = aService.getGamesByFixtureIds(new ArrayList<>(fixtureCounterMap.keySet()), curUser.getGroupsetId(), language);
            Map<Long, Game> fixtureMap = new HashMap<>();

            for (Game game : fixtureList) {
                fixtureMap.put(game.getIdFixture(), game);
            }
            Map<String, Integer> competitionViewsMap = new HashMap<>();
            Map<String, Map<Long, Integer>> competitionDetailsMap = new HashMap<>();
            Map<String, Map<Long, Integer>> competitionDetailsMapSorted = new HashMap<>();
            Map<Long, User> userMap = new HashMap<>();

            for (ChartData data : userDetailData) {
                String logDescription = data.getVideoId();
                if (logDescription != null && !logDescription.isEmpty()) {
                    logDescription = logDescription.replace("Multi:", "");
                    List<String> fixtureIds = Arrays.asList(StringUtils.split(logDescription, ","));
                    for (String stringFixtureId : fixtureIds) {
                        Long fixtureId = Long.valueOf(stringFixtureId);
                        if (fixtureMap.containsKey(fixtureId)) {
                            Game game = fixtureMap.get(fixtureId);

                            competitionViewsMap.put(game.getCompetitionName(), competitionViewsMap.getOrDefault(game.getCompetitionName(), 0) + 1);
                            competitionDetailsMap.putIfAbsent(game.getCompetitionName(), new TreeMap<Long, Integer>());
                            competitionDetailsMap.get(game.getCompetitionName()).put(data.getUserId(), competitionDetailsMap.get(game.getCompetitionName()).getOrDefault(data.getUserId(), 0) + 1);

                            if (!userMap.containsKey(data.getUserId())) {
                                User user = new User();
                                user.setId(data.getUserId());
                                user.setFirstName(data.getFirstName());
                                user.setLastName(data.getLastName());
                                user.setGroupsetName(data.getGroupsetName());
                                userMap.put(data.getUserId(), user);
                            }
                        }
                    }
                }
            }

            for (String competitionName : competitionDetailsMap.keySet()) {
                ValueComparator bvc = new ValueComparator(competitionDetailsMap.get(competitionName));
                TreeMap<Long, Integer> sortedMap = new TreeMap<>(bvc);
                sortedMap.putAll(competitionDetailsMap.get(competitionName));
                competitionDetailsMapSorted.put(competitionName, sortedMap);
            }

//            for (Game game : fixtureList) {
//                game.setCounter(fixtureCounterMap.getOrDefault(game.getIdFixture(), 1));
//
//                competitionViewsMap.put(game.getCompetitionName(), competitionViewsMap.getOrDefault(game.getCompetitionName(), 0) + game.getCounter());
//                competitionDetailsMap.putIfAbsent(game.getCompetitionName(), new ArrayList<Game>());
//                competitionDetailsMap.get(game.getCompetitionName()).add(game);
//            }
            //model.addAttribute("mFixtures", fixtureList);
            model.addAttribute("mCompetitionRows", competitionViewsMap);
            model.addAttribute("mCompetitionDetails", competitionDetailsMapSorted);
            model.addAttribute("mUsers", userMap);
        }

//        List<ChartData> userDetailData = aService.getVideoStreamData(timeFilterId, null);
//        Map<Long, Integer> userCounterMap = new HashMap<>();
//        for (ChartData data : userDetailData) {
//            if (data.getUserId() != null) {
//                userCounterMap.put(data.getUserId(), userCounterMap.getOrDefault(data.getUserId(), 0) + 1);
//            }
//        }
//
//        if (!userCounterMap.isEmpty()) {
//            List<User> fixtureList = aService.getUsers(new ArrayList<>(userCounterMap.keySet()));
//            Map<String, Integer> competitionViewsMap = new HashMap<>();
//            Map<String, List<Game>> competitionDetailsMap = new HashMap<>();
//
//            for (Game game : fixtureList) {
//                game.setCounter(userCounterMap.getOrDefault(game.getIdFixture(), 1));
//
//                competitionViewsMap.put(game.getCompetitionName(), competitionViewsMap.getOrDefault(game.getCompetitionName(), 0) + game.getCounter());
//                competitionDetailsMap.putIfAbsent(game.getCompetitionName(), new ArrayList<Game>());
//                competitionDetailsMap.get(game.getCompetitionName()).add(game);
//            }
//
//            //model.addAttribute("mFixtures", fixtureList);
//            model.addAttribute("mCompetitionRows", competitionViewsMap);
//            model.addAttribute("mCompetitionDetails", competitionDetailsMap);
//        }
        model.addAttribute("mTimeFilter", timeFilterId);

        return pageCompetitionDetails;
    }

    @RequestMapping(value = "/userLog")
    public String userLog(@RequestParam("userId") Long userId, @RequestParam(value = "date", required = false) Long date,
            HttpServletRequest request, ModelMap model, HttpSession session) {

        List<TomcatLog> logs = new ArrayList<>();

        String filePath = System.getProperty("catalina.base") + "/logs/";
        // catalina.yyyy-MM-dd
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Date logData;
        if (date != null) {
            logData = new Date(date);
        } else {
            logData = new Date();
        }
        filePath += "catalina." + formatter.format(logData) + ".log";
        formatter = new SimpleDateFormat("dd/MM/YYYY");
        model.addAttribute("mLogData", formatter.format(logData));
        model.addAttribute("mLogFile", filePath);

        //filePath += "catalina.2023-11-30.log";
        File file = new File(filePath);
        try (BufferedReader br = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = br.readLine()) != null) {
                if (line.contains("{{")) {
                    SimpleDateFormat format = new SimpleDateFormat("dd-MMM-yyyy HH:mm:ss", Locale.ENGLISH);

                    List<String> splitted = Arrays.asList(StringUtils.split(line, " "));
                    if (splitted.size() >= 8) {
                        if (splitted.get(0).contains("-")) {
                            if (splitted.get(1).contains(":") && splitted.get(1).contains(".")) {
                                try {
                                    Long logUserId = Long.valueOf(splitted.get(5).replace("{{", "").replace(",", ""));   // {{14,
                                    if (Long.compare(logUserId, userId) == 0) {
                                        TomcatLog tomcatLog = new TomcatLog();
                                        tomcatLog.setDate(format.parse(splitted.get(0) + " " + StringUtils.substringBefore(splitted.get(1), ".")));
                                        tomcatLog.setLevel(splitted.get(2));
                                        tomcatLog.setThread(splitted.get(3));
                                        tomcatLog.setLoggedClass(splitted.get(4));
                                        tomcatLog.setUserId(logUserId);
                                        tomcatLog.setUserFirstName(splitted.get(6).replace("}}", ""));                  // Marco}}
                                        tomcatLog.setLink(splitted.get(7));
                                        tomcatLog.setDuration(splitted.get(splitted.size() - 1));
                                        if (!tomcatLog.getLink().contains("isValidSession") && !tomcatLog.getLink().contains("homeActiveSessionPage")) {
                                            logs.add(tomcatLog);
                                        }
                                    }
                                } catch (NumberFormatException ex) {
                                    System.out.println("Line skipped: invalid userId found on line: " + line);
                                }
                            }
                        } else {
                            System.out.println("Line skipped: " + line);
                        }
                    } else {
                        System.out.println("Line skipped, size not correct: " + line);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        Collections.reverse(logs);
        model.addAttribute("mLogs", logs);

        return pageUserLog;
    }

    @RequestMapping(value = "/homeStartExport")
    public @ResponseBody
    String homeStartExport(@RequestParam("timeFilter") Integer timeFilterId, @RequestParam("agentId") Long agentId, ModelMap model, HttpSession session) {

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        List<ChartData> chartData = aService.getStartExportData(timeFilterId, agentId);
        return getContentByChartData(chartData);
    }

    @RequestMapping(value = "/homeLicenseExpired")
    public @ResponseBody
    String homeLicenseExpired(@RequestParam("timeFilter") Integer timeFilterId, @RequestParam("agentId") Long agentId, ModelMap model, HttpSession session) {

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        List<ChartData> chartData = aService.getLicenseExpiredData(timeFilterId, agentId);
        return getContentByChartData(chartData);
    }

    @RequestMapping(value = "/homeVideoDownload")
    public @ResponseBody
    String homeVideoDownload(@RequestParam("timeFilter") Integer timeFilterId, @RequestParam("agentId") Long agentId, ModelMap model, HttpSession session) {

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        List<ChartData> chartData = aService.getVideoDownloadData(timeFilterId, agentId);
        return getContentByChartData(chartData);
    }

    public String getContentByChartData(List<ChartData> chartData) {
        List<VtigerAgent> userAgent = aService.getUserVtigerAgent();
        Map<Long, VtigerAgent> userAgentMap = new HashMap<>();
        for (VtigerAgent agent : userAgent) {
            userAgentMap.put(agent.getUserId(), agent);
        }
        for (ChartData data : chartData) {
            if (data.getUserId() != null) {
                VtigerAgent agent = userAgentMap.get(data.getUserId());
                if (agent != null) {
                    data.setAgentFirstName(agent.getFirstName());
                    data.setAgentLastName(agent.getLastName());
                }
            }
        }

        if (!chartData.isEmpty()) {
            // 1. Raggruppo per utente
            Map<Long, List<ChartData>> userGroupedData = new LinkedHashMap<>();
            Map<Long, Long> userGroupedTotalActions = new HashMap<>();
            for (ChartData data : chartData) {
                if (userGroupedData.get(data.getUserId()) == null) {
                    userGroupedData.put(data.getUserId(), new ArrayList<ChartData>());
                }
                userGroupedData.get(data.getUserId()).add(data);

                if (userGroupedTotalActions.get(data.getUserId()) == null) {
                    userGroupedTotalActions.put(data.getUserId(), 0L);
                }
                userGroupedTotalActions.put(data.getUserId(), userGroupedTotalActions.get(data.getUserId()) + data.getActions());
            }

            // 2. Prendo i primi 10 in ordine di totale di azioni
            List<Long> selectedUserId = new ArrayList<>();
            Map<Long, Long> userGroupedTotalActionsSorted = GlobalHelper.sortHashMapByValues(userGroupedTotalActions);
            for (Long userId : userGroupedTotalActionsSorted.keySet()) {
                if (selectedUserId.size() >= 10) {
                    break;
                }

                selectedUserId.add(userId);
            }

            String content = "";
            if (!selectedUserId.isEmpty()) {
                content = "[";
                for (Long userId : selectedUserId) {
                    List<ChartData> userData = userGroupedData.get(userId);
                    if (!userData.isEmpty()) {
                        String tooltip = "Utente: " + userData.get(0).getFullName() + "\\n";
                        tooltip += "Gruppo: " + userData.get(0).getGroupsetName() + " (" + userData.get(0).getGroupsetId() + ")\\n";
                        if (userData.get(0).getAgentFirstName() != null) {
                            tooltip += "Agente: " + userData.get(0).getAgentFirstName() + " " + userData.get(0).getAgentLastName() + "\\n\\n";
                        } else {
                            tooltip += "\\n";
                        }
                        for (ChartData data : userData) {
                            tooltip += data.getDay() + "/" + data.getMonth() + "/" + data.getYear() + ": " + data.getActions() + "\\n";
                        }

                        content += "{";
                        content += "\"user\": \"" + userData.get(0).getFullNameFormatted() + "\",";
                        content += "\"value\": \"" + userGroupedTotalActions.get(userId) + "\",";
                        content += "\"tooltip\": \"" + tooltip + "\"";
                        content += "},";
                    }
                }
                content = content.substring(0, content.length() - 1); // tolgo ultima virgola
                content += "]";
            }

            return content;
        } else {
            return "[]";
        }
    }

    @RequestMapping(value = "/homeActiveSessionPage")
    public String homeActiveSessionPage(ModelMap model, HttpSession session) {

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        if (SessionListener.getSessions() != null && !SessionListener.getSessions().isEmpty()) {
            Map<Long, User> userMap = new HashMap<>();
            boolean inError = false;
            synchronized (SessionListener.getSessions()) {
                for (Long userId : SessionListener.getSessions().keySet()) {
                    // devo fare try catch perchè ci sono ancora casi in cui la sessione non viene gestita correttamente
                    try {
                        User user = (User) SessionListener.getSessions().get(userId).get(0).getAttribute(GlobalHelper.kBeanUtente);
                        userMap.put(userId, user);
                    } catch (Exception ex) {
                        GlobalHelper.reportError(ex);
                        MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
                        mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), "<EMAIL>", null, null, null, null, "Errore sics.tv - sessioni", "Sessione già invalidata per userId " + userId, null, "");
                        inError = true;
                    }
                }
            }
            if (inError) {
                SessionListener.checkSessions();
            }
//            List<Long> userIdToLoad = new ArrayList<>(SessionListener.getSessions().keySet());
//            List<User> userList = aService.getUsers(userIdToLoad);
//            for (User user : userList) {
//                userMap.put(user.getId(), user);
//            }

            model.addAttribute("mUsers", userMap);
            model.addAttribute("mSessions", SessionListener.getSessions());
            int totalSessions = 0;
            for (List<HttpSession> userSession : SessionListener.getSessions().values()) {
                totalSessions += userSession.size();
            }
            model.addAttribute("mTotalSessions", totalSessions);
        }

        return pageHomeActiveSession;
    }

    @RequestMapping(value = "/killSession")
    public @ResponseBody
    String killSession(@RequestParam("userId") Long userId, ModelMap model, HttpSession session) {

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        if (userId != null && SessionListener.getSessions() != null && !SessionListener.getSessions().isEmpty()) {
            if (SessionListener.getSessions().get(userId) != null) {
                List<HttpSession> userSessionList = SessionListener.getSessions().get(userId);
                List<HttpSession> userSessionListCloned = new ArrayList<>(userSessionList);

                for (HttpSession userSession : userSessionListCloned) {
                    SessionListener.destroySession(userSession, userId, true);
                }

                return "true";
            }
        }

        return "false";
    }

    @RequestMapping(value = "/homeActiveSession")
    public @ResponseBody
    String homeActiveSession(ModelMap model, HttpSession session) {

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        int sessionSize = 0;
        if (SessionListener.getSessions() != null && !SessionListener.getSessions().isEmpty()) {
            sessionSize = SessionListener.getSessions().size();
        }

        String content = "";
        content += "[{";
        content += "\"user\": \"Sessioni\",";
        content += "\"value\": \"" + sessionSize + "\"";
        content += "}]";

        return content;
    }

    //Elenco utenti
    @RequestMapping(value = "/userlist")
    public String userList(@RequestParam("formDateFrom") String dateFrom, @RequestParam("formDateTo") String dateTo, @RequestParam("formNome") String name, @RequestParam("formCognome") String surname, @RequestParam("formApplication") String application,
            ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(pageLibrary);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        List userList = aService.getUserAll(name, surname, dateFrom, dateTo, application);
        model.addAttribute("mUserList", userList);
        return pageUserList;
    }

    @RequestMapping(value = "/userdata")
    public String userData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) throws SQLException {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(pageLibrary);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        model.addAttribute("mUser", curUser);
        model.addAttribute("formDateFrom", DateHelper.toStringInputDate(DateHelper.sumDates(new Date(), -30)));
        model.addAttribute("formDateTo", DateHelper.toStringInputDate(new Date()));
        return pageUserData;
    }

    //Filtra i dati di log rispetto ai parametri inseriti dall' utente
    @RequestMapping(value = "/loglist")
    public String logList(@RequestParam("formDateFrom") String dateFrom, @RequestParam("formDateTo") String dateTo, @RequestParam("formIdVideo") Long idVideo, @RequestParam("formUserName") String userName, @RequestParam("formIdAction") String action, @RequestParam("formApplication") String application,
            ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) throws SQLException {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(pageLibrary);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        String actionToSearch = "";
        if (!action.isEmpty()) {
            actionToSearch = action;
//            switch (action) {
//                case "LOGIN_CORRECT":
//                    actionToSearch = "'LOGIN_CORRECT','LOGIN'";
//                    break;
//                case "VIDEO_STREAM":
//                    break;
//                case "VIDEO_DOWNLOAD":
//                    actionToSearch = "'VIDEO_DOWNLOAD','VIDEO_DOWNLOAD_COMPLETE'";
//                    break;
//            }
        }
        List logData = aService.getLogDataFilter(DateHelper.toDate(dateFrom), DateHelper.toDate(dateTo), idVideo, userName, actionToSearch, application);
        model.addAttribute("mLogDataList", logData);
        return pageLogList;
    }

    @RequestMapping(value = "/logdata")
    public String logData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) throws SQLException {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(pageLibrary);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        model.addAttribute("mUser", curUser);
        model.addAttribute("formDateFrom", DateHelper.toString(DateHelper.sumDates(new Date(), -7)));
        model.addAttribute("formDateTo", DateHelper.toString(new Date()));
        return pageLogData;
    }

    //Dettaglio Streaming singolo utente
    @RequestMapping(value = "/userStreaming")
    public String streamingStats(@RequestParam("formId") Long id,
            ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(pageLibrary);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        List stats = aService.getStreamingDet(id);
        model.addAttribute("downloadDetails", "false");
        model.addAttribute("mStats", stats);
        return userStatsDetails;
    }

    //Dettaglio Download singolo utente
    @RequestMapping(value = "/userDownload")
    public String downloadStats(@RequestParam("formId") Long id, ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(pageLibrary);
        }
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        List stats = aService.getDownloadDet(id);
        model.addAttribute("downloadDetails", "true");
        model.addAttribute("mStats", stats);
        return userStatsDetails;
    }

    @RequestMapping(value = "/databaseEditor")
    public String databaseEditor(@RequestParam("table") String table, ModelMap model, HttpSession session) {

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin()) {
            return pageRedirect("auth/403.jsp");
        }

        if (table == null) {
            table = "user";
        }
        DatabaseEditor editor = new DatabaseEditor.Builder(table)
                .isAdminRequest(true)
                .build();

        String query = editor.toString();

        return "todo";
    }

    @RequestMapping(value = "/agentPayment")
    public String agentPayment(ModelMap model, HttpSession session) {

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin() && Long.compare(curUser.getGroupsetId(), 412) != 0 && Long.compare(curUser.getGroupsetId(), 1) != 0) {
            return pageRedirect("auth/403.jsp");
        }

        AgentExcelReader reader = new AgentExcelReader();
        String agentName;
        if (curUser.isUserAdmin()) {
            agentName = "All";
        } else {
            agentName = curUser.getFirstName();
        }
        AgentPayment tableContent = reader.read(agentName);
        model.addAttribute("mTableContent", tableContent);

        return pageAgentPayment;
    }

    @RequestMapping(value = "/moduleChecker")
    public String moduleChecker(HttpServletRequest request, ModelMap model, HttpSession session, HttpServletResponse response) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!curUser.isUserAdmin()) {
            return pageRedirect("auth/403.jsp");
        }

        Map<String, List<Punto>> modulePoints = new LinkedHashMap<>();
        List<String> modules = Arrays.asList("3412", "3421", "343", "3511", "352", "4141", "4222", "4231", "442", "4411", "451", "433", "4312", "4321", "532", "541");
        for (String module : modules) {
            for (int i = 0; i < 11; i++) {
                modulePoints.putIfAbsent(module, new ArrayList<Punto>());
                modulePoints.get(module).add(ModuleHelper.getPosPlayerByModule(module, i));
            }
        }

        model.addAttribute("mModules", modulePoints);

        return pageModuleChecker;
    }
}

class ValueComparator implements Comparator<Long> {

    Map<Long, Integer> base;

    public ValueComparator(Map<Long, Integer> base) {
        this.base = base;
    }

    // Note: this comparator imposes orderings that are inconsistent with
    // equals.
    @Override
    public int compare(Long a, Long b) {
        if (base.get(a) >= base.get(b)) {
            return -1;
        } else {
            return 1;
        } // returning 0 would merge keys
    }
}
