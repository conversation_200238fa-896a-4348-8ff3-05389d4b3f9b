package sics.model;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import sics.domain.Atleta;
import sics.domain.Button;
import sics.domain.Tags;
import sics.helper.GlobalHelper;

public class Azione implements Cloneable {

    @Expose
    private Long idFixture;
    private Long competitionId;
    @Expose
    private Long _id;
    @Expose
    private int _idType;
    @Expose
    private int _idTeam;
    @Expose
    private Map<Integer, Atleta> _player = new HashMap<>();
    @Expose
    private Atleta _playerTo;
//    private String _pos = "";
//    private String _pos1 = "";
//    private String _pos3D = "";

    @Expose
    private int _period_start_minute;
    @Expose
    private int _period_start_second;
    @Expose
    private int _period_end_minute;
    @Expose
    private int _period_end_second;

    @Expose
    private String mType = "";
    private String mTagCode;
    private Integer _indexTeam;
    @Expose
    private int mHalf;
    private String mResult = "-1";
    @Expose
    private String mResultReal;

    // campi usati per creare la lista mTags
    private Integer tagTypeId;
    private String tagTypeCode;
    private String tagNameIt;
    private String tagNameEn;
    private String tagNameFr;
    private String tagNameEs;
    private String tagNameRu;

    @Expose
    private ArrayList<Tags> mTags = new ArrayList<>();
    @Expose
    private Long mStart;
    @Expose
    private Long mEnd;
    @Expose
    protected Long mTactStart = -1l;

    @Expose
    private String mTeam = "";

    private String mHomeTeam = "";
    private String mIdHomeTeam = "";
    private String mAwayTeam = "";
    private String mIdAwayTeam = "";
    @Expose
    private String mNote = "";
    @Expose
    private String mPlayer = "";
    private String mPlayerTo = "";
    @Expose
    private String mIdEvent = "";
    private String mIdGroup = ""; // valore di raggruppamento delle azioni per capire le azioni associate
    private String mTeamColor = "";
    private String mTeamTextColor = "";

    @Expose
    private Punto mActionPos = new Punto(-1, -1);
    @Expose
    private Punto mActionPosEnd = new Punto(-1, -1);
    @Expose
    private Punto mActionPosNormalized = new Punto(-1, -1);
    @Expose
    private Punto mActionPosEndNormalized = new Punto(-1, -1);
    private double mActionAngle = -1;
    @Expose
    private int mActionAngleZone = -1;
    @Expose
    private double mActionDistance = -1;
    @Expose
    private PuntoTiro mShot3D = new PuntoTiro(-1, -1, -1);
    @Expose
    private PuntoTiro mShot3DNormalized = new PuntoTiro(-1, -1, -1);

    private Tags mTag;
    private int playerId;
    private int playerToId;
    private String posStartString, posEndString, pos3DString;
    @Expose
    private String config;
    @Expose
    private String tempoGioco;
    private String desc;
    private String descEn;
    private String descFr;
    private String descEs;
    private String descRu;
    @Expose
    private String durataAzione;
    @Expose
    private Long videoId;
    @Expose
    private String videoName;
    @Expose
    private String videoPathS3;
    @Expose
    private Long provider;
    @Expose
    private Button button;
    @Expose
    private String groupsetId;
    private String rilevante;
    @Expose
    private String user;
    private String tempoAzioneVideo;
    private boolean fromDB = false;
    private String languageFromPage;
    @Expose
    private String descrAzione, descrAzioneOverlay;

    private boolean hdAvailable = false;
    protected boolean tactAvailable = false;

    private int countAction = 0;

    private Boolean isTactical; // usato per definire se una clip è di tipo tattico o meno per le playlist
    private Long tableOrder;    // usato in pagina myPlaylistTv per metterle in ordine
    private Long updateBy;              // campo usato per caricare modifiche playlist_tv_event
    private Timestamp updateDate;       // campo usato per caricare modifiche playlist_tv_event
    private String updateDescription;   // campo usato per caricare modifiche playlist_tv_event
    private Boolean deleted;            // campo usato per caricare le righe cancellate di playlist_tv_event

    private Boolean isVisible;

    private static AzioneComparator azioneComparator;

    @Expose
    private String matchDate;
    @Expose
    private Integer matchDay;

    public final Map<Integer, Atleta> getPlayer() {
        return _player;
    }

    public final void setPlayer(Map<Integer, Atleta> Value) {
        _player = Value;
    }

    public final Atleta getPlayerTo() {
        return _playerTo;
    }

    public final void setPlayerTo(Atleta Value) {
        _playerTo = Value;
    }

    public int getIndexTeam() {
        return _indexTeam;
    }

    public void setIndexTeam(int indexTeam) {
        this._indexTeam = indexTeam;
    }

    public Azione() {
    }

    public Azione(Long id, String xml_idEvent, int idEventType, String eventType, int indiceTeam, int idTeam, int period_id, Long start_msec, Long end_msec, int start_minute, int start_second, String esito) {
        _id = id;
        mIdEvent = xml_idEvent;
        _idType = idEventType;
        mType = eventType;
        _indexTeam = indiceTeam;
        _idTeam = idTeam;
        mHalf = period_id;
        mStart = start_msec;
        mEnd = end_msec;
        _period_start_minute = start_minute;
        _period_start_second = start_second;
        mResult = esito;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        Azione action = (Azione) super.clone();
        return action;
    }

    @Override
    public boolean equals(Object o) {
        try {
            Azione item = (Azione) o;
            if (this.mIdEvent == item.mIdEvent) {
                return true;
            }
        } catch (Exception ex) {
        }
        return false;
    }

    public final int getPeriod_start_minute() {
        return _period_start_minute;
    }

    public final int getPeriod_start_second() {
        return _period_start_second;
    }

    public String getmHomeTeam() {
        return mHomeTeam;
    }

    public void setmHomeTeam(String m_SquadraCasa) {
        this.mHomeTeam = m_SquadraCasa;
    }

    public String getmIdHomeTeam() {
        return mIdHomeTeam;
    }

    public void setmIdHomeTeam(String m_idSquadraCasa) {
        this.mIdHomeTeam = m_idSquadraCasa;
    }

    public String getmAwayTeam() {
        return mAwayTeam;
    }

    public void setmAwayTeam(String mAwayTeam) {
        this.mAwayTeam = mAwayTeam;
    }

    public String getmIdAwayTeam() {
        return mIdAwayTeam;
    }

    public void setmIdAwayTeam(String mIdAwayTeam) {
        this.mIdAwayTeam = mIdAwayTeam;
    }

    public String getType() {
        return mType;
    }

    public void setType(String mType) {
        this.mType = mType;
    }

    public String getmTagCode() {
        return mTagCode;
    }

    public void setmTagCode(String mTagCode) {
        this.mTagCode = mTagCode;
    }

    public Long getmStart() {
        return mStart;
    }

    public void setmStart(Long mStart) {
        this.mStart = mStart;
    }

    public Long getmEnd() {
        return mEnd;
    }

    public void setmEnd(Long m_Fine) {
        this.mEnd = m_Fine;
    }

    public String getmDuration() {
        try {
            if (mEnd != null && mStart != null) {
                return GlobalHelper.msecToMinSec((mEnd - mStart), false, true, false);
            } else {
                throw new NullPointerException();
            }
        } catch (Exception ex) {
        }
        return "";
    }

    public int getHalf() {
        return mHalf;
    }

    public void setHalf(int m_Half) {
        this.mHalf = m_Half;
    }

    public String getTeam() {
        return mTeam;
    }

    public void setTeam(String m_Squadra) {
        this.mTeam = m_Squadra;
    }

    public String getmNote() {
        return mNote;
    }

    public void setmNote(String mNote) {
        this.mNote = mNote;
    }

    public String getmPlayer() {
        return mPlayer;
    }

    public void setmPlayer(String mPlayer) {
        this.mPlayer = mPlayer;
    }

    public String getIdEvent() {
        return mIdEvent;
    }

    public void setIdEvent(String mIdEvent) {
        this.mIdEvent = mIdEvent;
    }

    public String getmIdGroup() {
        return mIdGroup;
    }

    public void setmIdGroup(String mIdGroup) {
        this.mIdGroup = mIdGroup;
    }

    public String getmTeamColor() {
        return mTeamColor;
    }

    public void setmTeamColor(String mTeamColor) {
        this.mTeamColor = mTeamColor;
    }

    public String getmTeamTextColor() {
        return mTeamTextColor;
    }

    public void setmTeamTextColor(String mTeamTextColor) {
        this.mTeamTextColor = mTeamTextColor;
    }

    public Azione copy() {
        Azione copyBackup = new Azione();
        copyBackup.mType = this.mType;
        copyBackup.mStart = this.mStart;
        copyBackup.mEnd = this.mEnd;
        copyBackup.mHalf = this.mHalf;
        copyBackup.mTeam = this.mTeam;
        copyBackup.mHomeTeam = this.mHomeTeam;
        copyBackup.mAwayTeam = this.mAwayTeam;
        copyBackup.mNote = this.mNote;
        copyBackup.mPlayer = this.mPlayer;
        copyBackup.mIdEvent = this.mIdEvent;
        if (this.mTags != null) {
            copyBackup.mTags = this.mTags;
        }
        return copyBackup;
    }

    // compare necessario per il merge
    public int compareTo(Azione rem) {

        return 0;
    }

    public boolean haveTag(String tag) {
        for (int i = 0; i < mTags.size(); i++) {
            if (tag.equals(mTags.get(i).getCode())) {
                return true;
            }
        }
        return false;
    }

    public Punto getActionPos() {
        return mActionPos;
    }

    public void setActionPos(Punto mActionPos) {
        if (mActionPos != null) {
            this.mActionPos = mActionPos;
//            mActionDistance = GlobalHelper.distance(this.mActionPos, this.mActionPosEnd);
//            mActionAngle = GlobalHelper.angoloTraPunti(this.mActionPos, this.mActionPosEnd);
        }
    }

    public Punto getActionPosEnd() {
        return mActionPosEnd;
    }

    public void setActionPosEnd(Punto mActionPosEnd) {
        this.mActionPosEnd = mActionPosEnd;
    }

    public void setActionDetails() {
        if (this.mActionPosEndNormalized != null && this.mActionPosEndNormalized != null) {
            mActionDistance = GlobalHelper.distance(this.mActionPosNormalized, this.mActionPosEndNormalized);
            mActionAngle = GlobalHelper.angoloTraPunti(this.mActionPosNormalized, this.mActionPosEndNormalized);
            mActionAngleZone = GlobalHelper.zonaAngolo(mActionAngle);
        }
    }

    public Punto getmActionPosNormalized() {
        return mActionPosNormalized;
    }

    public void setmActionPosNormalized(Punto mActionPosNormalized) {
        this.mActionPosNormalized = mActionPosNormalized;
    }

    public Punto getmActionPosEndNormalized() {
        return mActionPosEndNormalized;
    }

    public void setmActionPosEndNormalized(Punto mActionPosEndNormalized) {
        this.mActionPosEndNormalized = mActionPosEndNormalized;
    }

    public double getmActionAngle() {
        return mActionAngle;
    }

    public void setmActionAngle(double mActionAngle) {
        this.mActionAngle = mActionAngle;
    }

    public int getmActionAngleZone() {
        return mActionAngleZone;
    }

    public void setmActionAngleZone(int mActionAngleZone) {
        this.mActionAngleZone = mActionAngleZone;
    }

    public double getmActionDistance() {
        return mActionDistance;
    }

    public void setmActionDistance(double mActionDistance) {
        this.mActionDistance = mActionDistance;
    }

    public PuntoTiro getShot3D() {
        return mShot3D;
    }

    public void setShot3D(PuntoTiro mShot3D) {
        this.mShot3D = mShot3D;
    }

    public PuntoTiro getmShot3DNormalized() {
        return mShot3DNormalized;
    }

    public void setmShot3DNormalized(PuntoTiro mShot3DNormalized) {
        this.mShot3DNormalized = mShot3DNormalized;
    }

    public String getmPlayerTo() {
        return mPlayerTo;
    }

    public void setmPlayerTo(String mPlayerTo) {
        this.mPlayerTo = mPlayerTo;
    }

    public Long getIdFixture() {
        return idFixture;
    }

    public void setIdFixture(Long idFixture) {
        this.idFixture = idFixture;
    }

    public Long getCompetitionId() {
        return competitionId;
    }

    public void setCompetitionId(Long competitionId) {
        this.competitionId = competitionId;
    }

    public String getPosStartString() {
        return posStartString;
    }

    public void setPosStartString(String posStartString) {
        this.posStartString = posStartString;
    }

    public String getPosEndString() {
        return posEndString;
    }

    public void setPosEndString(String posEndString) {
        this.posEndString = posEndString;
    }

    public String getPos3DString() {
        return pos3DString;
    }

    public void setPos3DString(String pos3DString) {
        this.pos3DString = pos3DString;
    }

    public Tags getmTag() {
        return mTag;
    }

    public void setmTag(Tags mTag) {
        this.mTag = mTag;
    }

    public int getPlayerId() {
        return playerId;
    }

    public void setPlayerId(int playerId) {
        this.playerId = playerId;
    }

    public int getPlayerToId() {
        return playerToId;
    }

    public void setPlayerToId(int playerToId) {
        this.playerToId = playerToId;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String halfTrim(String lang) {
        if (config.equals("BASKET2017.xml")) {
            switch (mHalf) {
                case 1:
                    tempoGioco = "1Q";
                    break;
                case 2:
                    tempoGioco = "2Q";
                    break;
                case 3:
                    tempoGioco = "3Q";
                    break;
                case 4:
                    tempoGioco = "4Q";
                    break;
                case 5:
                    if (lang.equalsIgnoreCase("it")) {
                        tempoGioco = "1QS";
                    } else {
                        tempoGioco = "1QEXT";
                    }
                    break;
                case 6:
                    if (lang.equalsIgnoreCase("it")) {
                        tempoGioco = "2QS";
                    } else {
                        tempoGioco = "2QEXT";
                    }
                    break;
                case 7:
                    if (lang.equalsIgnoreCase("it")) {
                        tempoGioco = "3QS";
                    } else {
                        tempoGioco = "3QEXT";
                    }
                    break;
                case 8:
                    if (lang.equalsIgnoreCase("it")) {
                        tempoGioco = "4QS";
                    } else {
                        tempoGioco = "4QEXT";
                    }
                    break;
            }
        } else {
            switch (mHalf) {
                case 1:
                    if (lang.equalsIgnoreCase("it")) {
                        tempoGioco = "1t";
                    } else {
                        tempoGioco = "1h";
                    }
                    break;
                case 2:
                    if (lang.equalsIgnoreCase("it")) {
                        tempoGioco = "2t";
                    } else {
                        tempoGioco = "2h";
                    }
                    break;
                case 3:
                    if (lang.equalsIgnoreCase("it")) {
                        tempoGioco = "1ts";
                    } else {
                        tempoGioco = "1ext";
                    }
                    break;
                case 4:
                    if (lang.equalsIgnoreCase("it")) {
                        tempoGioco = "2ts";
                    } else {
                        tempoGioco = "2ext";
                    }
                    break;
                case 5:
                    if (lang.equalsIgnoreCase("it")) {
                        tempoGioco = "rig";
                    } else {
                        tempoGioco = "pen";
                    }
                    break;
            }
        }

        return StringUtils.defaultIfEmpty(tempoGioco, "");
    }

    public String getTempoGioco() {
        return tempoGioco;
    }

    public void setTempoGioco(String tempoGioco) {
        this.tempoGioco = tempoGioco;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDescEn() {
        return descEn;
    }

    public void setDescEn(String descEn) {
        this.descEn = descEn;
    }

    public String getDescFr() {
        return descFr;
    }

    public void setDescFr(String descFr) {
        this.descFr = descFr;
    }

    public String getDescEs() {
        return descEs;
    }

    public void setDescEs(String descEs) {
        this.descEs = descEs;
    }

    public String getDescRu() {
        return descRu;
    }

    public void setDescRu(String descRu) {
        this.descRu = descRu;
    }

    public String getDurataAzione() {
        return getDurataAzioneMinSec(false);
    }

    public String getDurataAzioneMinSec(boolean minSec) {
        int secStart = _period_start_minute * 60 + _period_start_second;
        int secEnd = _period_end_minute * 60 + _period_end_second;
        int minutiAzione = (secEnd - secStart) / 60;
        int secondiAzione = (secEnd - secStart) % 60;
        if (minSec) {
            return minutiAzione + " min " + secondiAzione + " sec";
        } else {
            if (durataAzione == null || durataAzione.isEmpty()) {
                durataAzione = "";
                // se voglio espresso in minuti e secondi
                if (secondiAzione < 10) {
                    durataAzione = minutiAzione + ":0" + secondiAzione;
                } else {
                    durataAzione = minutiAzione + ":" + secondiAzione;
                }
                if (minutiAzione < 10) {
                    durataAzione = "0" + durataAzione;
                }
            }
        }
        return durataAzione;
    }

    public void setDurataAzione(String durataAzione) {
        this.durataAzione = durataAzione;
    }

    public int getPeriod_end_minute() {
        return _period_end_minute;
    }

    public void setPeriod_end_minute(int _period_end_minute) {
        this._period_end_minute = _period_end_minute;
    }

    public int getPeriod_end_second() {
        return _period_end_second;
    }

    public void setPeriod_end_second(int _period_end_second) {
        this._period_end_second = _period_end_second;
    }

    public String playerTrim() {
        String lower = mPlayer;
        try {
            lower = mPlayer.toLowerCase();
        } catch (Exception ex) {
        }
        return lower;
    }

    public String playerCamel() {
        String input = playerTrim();
        return playerCamel(input);
    }

    public String playerCamel(String input) {
        String camel = "";
        try {

            char firstChar = input.charAt(0);
            camel = camel + Character.toUpperCase(firstChar);
            for (int i = 1; i < input.length(); i++) {
                char currentChar = input.charAt(i);
                char previousChar = input.charAt(i - 1);
                if (previousChar == ' ') {
                    camel = camel + Character.toUpperCase(currentChar);
                } else {
                    camel = camel + currentChar;
                }
            }
        } catch (Exception ex) {
        }
        return camel;
    }

    public String playersCamel() {
        List<String> playerList = new ArrayList<>();

        for (Atleta player : _player.values()) {
            if (player != null && StringUtils.isNotBlank(player.getKnown_name())) {
                playerList.add(playerCamel(player.getKnown_name().toLowerCase()));
            }
        }

        return StringUtils.join(playerList, ", ");
    }

    public String descTrim() {
        String descTrim = desc.replace("\\n", " ");
        return descTrim;
    }

    public String descTrim(String lang) {
        String descTrim = button.descTrim(lang).replace("\\n", " ");
        return descTrim;
    }

    public String getNameFileExport() {
//        String nameFileExp = getMatch() + "-" + descTrim().replace(" ", "_") + "-" + mTeam + "" + "-" + +_period_start_minute + "m" + _period_start_second + "s.mp4";
        String nameFileExp = getMatch() + "-" + descTrim().replace(" ", "_") + "-" + mTeam + ".mp4";

        return nameFileExp;
    }

    public Long getVideoId() {
        return videoId;
    }

    public void setVideoId(Long videoid) {
        this.videoId = videoid;
    }

    public String getVideoName() {
        return videoName;
    }

    public void setVideoName(String videoname) {
        this.videoName = videoname;
    }

    public String getVideoPathS3() {
        return videoPathS3;
    }

    public void setVideoPathS3(String videoPathS3) {
        this.videoPathS3 = videoPathS3;
    }

    public Long getProvider() {
        return provider;
    }

    public void setProvider(Long provider) {
        this.provider = provider;
    }

    public Long getId() {
        return _id;
    }

    public void setId(Long _id) {
        this._id = _id;
    }

    public int getIdType() {
        return _idType;
    }

    public void setIdType(int _idType) {
        this._idType = _idType;
    }

    public int getIdTeam() {
        return _idTeam;
    }

    public void setIdTeam(int _idTeam) {
        this._idTeam = _idTeam;
    }

    public String getmType() {
        return mType;
    }

    public void setmType(String mType) {
        this.mType = mType;
    }

    public int getmHalf() {
        return mHalf;
    }

    public void setmHalf(int mHalf) {
        this.mHalf = mHalf;
    }

    public String getmResult() {
        return mResult;
    }

    public void setmResultReal() {
        if (mResult.equals("0")) {
            mResultReal = "P";
        } else if (mResult.equals("1")) {
            mResultReal = "N";
        } else if (mResult.equals("2")) {
            mResultReal = "";
        } else if (!mResult.equals("-1")) {
            mResultReal = mResult;
        }
    }

    public String getmResultReal() {
        return mResultReal;
    }

    public void setmResult(String mResult) {
        this.mResult = mResult;
    }

    public Integer getTagTypeId() {
        return tagTypeId;
    }

    public void setTagTypeId(Integer tagTypeId) {
        this.tagTypeId = tagTypeId;
    }

    public String getTagTypeCode() {
        return tagTypeCode;
    }

    public void setTagTypeCode(String tagTypeCode) {
        this.tagTypeCode = tagTypeCode;
    }

    public String getTagNameIt() {
        return tagNameIt;
    }

    public void setTagNameIt(String tagNameIt) {
        this.tagNameIt = tagNameIt;
    }

    public String getTagNameEn() {
        return tagNameEn;
    }

    public void setTagNameEn(String tagNameEn) {
        this.tagNameEn = tagNameEn;
    }

    public String getTagNameFr() {
        return tagNameFr;
    }

    public void setTagNameFr(String tagNameFr) {
        this.tagNameFr = tagNameFr;
    }

    public String getTagNameEs() {
        return tagNameEs;
    }

    public void setTagNameEs(String tagNameEs) {
        this.tagNameEs = tagNameEs;
    }

    public String getTagNameRu() {
        return tagNameRu;
    }

    public void setTagNameRu(String tagNameRu) {
        this.tagNameRu = tagNameRu;
    }

    public ArrayList<Tags> getmTags() {
        return mTags;
    }

    public void setmTags(ArrayList<Tags> mTags) {
        if (mTags != null) {
            this.mTags = mTags;
        }
    }

    public String getmTeam() {
        return mTeam;
    }

    public void setmTeam(String mTeam) {
        this.mTeam = mTeam;
    }

    public String getmIdEvent() {
        return mIdEvent;
    }

    public void setmIdEvent(String mIdEvent) {
        this.mIdEvent = mIdEvent;
    }

    public Punto getmActionPos() {
        return mActionPos;
    }

    public void setmActionPos(Punto mActionPos) {
        this.mActionPos = mActionPos;
    }

    public Punto getmActionPosEnd() {
        return mActionPosEnd;
    }

    public void setmActionPosEnd(Punto mActionPosEnd) {
        this.mActionPosEnd = mActionPosEnd;
    }

    public PuntoTiro getmShot3D() {
        return mShot3D;
    }

    public void setmShot3D(PuntoTiro mShot3D) {
        this.mShot3D = mShot3D;
    }

    public Button getButton() {
        return button;
    }

    public void setButton(Button button) {
        if (button != null) {
            this.button = button;
        }
    }

    public String getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(String groupsetId) {
        this.groupsetId = groupsetId;
    }

    public String getStartAzione() {
        String start;
        if (_period_start_second < 10) {
            start = _period_start_minute + ":0" + _period_start_second;
        } else {
            start = _period_start_minute + ":" + _period_start_second;
        }
        if (_period_start_minute < 10) {
            start = "0" + start;
        }
        return start;
    }

    public Long getSecStartAzione() {
        Long sec = mStart / 1000;
        return sec;
    }

    public Long getSecTactStart() {
        if (mTactStart != null && mTactStart >= 0) {
            Long sec = mTactStart / 1000;
            return sec;
        }
        return -1l;
    }

    public Long getSecEndAzione() {
        Long sec = mEnd / 1000;
        return sec;
    }

    public String getRilevante() {
        return rilevante;
    }

    public void setRilevante(String rilevante) {
        this.rilevante = rilevante;
    }

    public void setPeriod_start_minute(int _period_start_minute) {
        this._period_start_minute = _period_start_minute;
    }

    public void setPeriod_start_second(int _period_start_second) {
        this._period_start_second = _period_start_second;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getUser() {
        return user;
    }

    public String getTempoAzioneVideo() {
        return tempoAzioneVideo;
    }

    public void setTempoAzioneVideo(String tempoAzioneVideo) {
        this.tempoAzioneVideo = tempoAzioneVideo;
    }

    public boolean isFromDB() {
        return fromDB;
    }

    public void setFromDB(boolean fromDB) {
        this.fromDB = fromDB;
    }

    public String getLanguageFromPage() {
        return languageFromPage;
    }

    public void setLanguageFromPage(String languageFromPage) {
        this.languageFromPage = languageFromPage;
    }

    public boolean isHdAvailable() {
        return hdAvailable;
    }

    public void setHdAvailable(boolean hdAvailable) {
        this.hdAvailable = hdAvailable;
    }

    public int getCountAction() {
        return countAction;
    }

    public void setCountAction(int countAction) {
        this.countAction = countAction;
    }

    public static AzioneComparator getAzioneComparator() {
        if (azioneComparator == null) {
            azioneComparator = new AzioneComparator();
        }
        return azioneComparator;
    }

    static class AzioneComparator implements Comparator<Azione> {

        @Override
        public int compare(Azione o1, Azione o2) {
            if (o1.getmHalf() < o2.getmHalf()) {
                return -1;
            } else if (o1.getmHalf() > o2.getmHalf()) {
                return 1;
            } else {
                if (o1.getPeriod_start_minute() < o2.getPeriod_start_minute()) {
                    return -1;
                } else if (o1.getPeriod_start_minute() > o2.getPeriod_start_minute()) {
                    return 1;
                } else {
                    if (o1.getPeriod_start_second() <= o2.getPeriod_start_second()) {
                        return -1;
                    } else {
                        return 1;
                    }
                }
            }
        }

    }

    public boolean isAutorete() {
        boolean esito = false;
        for (Tags t : mTags) {
            // autorete
            if (t != null && (t.getId() == 159 || t.getId() == 144)) {
                esito = true;
                break;
            }
        }
        return esito;
    }

    public boolean isRigore() {
        boolean esito = false;
        for (Tags t : mTags) {
            // autorete
            if (t != null && t.getId() == 140) {
                esito = true;
                break;
            }
        }
        return esito;
    }

    public String getPlayerTabellino() {
        String name = mPlayer;
        if (isAutorete()) {
            name += " aut";
        } else if (isRigore()) {
            name += " (R)";
        }

        return name;
    }

    public String getInfoSearch() {

        String tags = "";
        for (Tags t : mTags) {
            tags += (tags.isEmpty() ? "" : ",") + t.getCode();
        }
        String players = "";
        for (int idAtl : _player.keySet()) {
            players += (players.isEmpty() ? "" : ",") + idAtl;
        }

        String searchString = this.config.replace(".xml", "") + "_" + button.getCod() + (tags.isEmpty() ? "" : "||") + tags + "#" + players + "#" + mHalf + "#" + groupsetId + "#" + _idTeam;
        //String searchString = this.config.replace(".xml", "") + "_" + ((button !=null) ? button.getCod() : "") + (tags.isEmpty() ? "" : "||") + tags + "#" + players + "#" + mHalf + "#" + groupsetId + "#" + _idTeam;
        return searchString;
    }

    public String getDescrAzioneToShow(String lang) {
        String tags = "";
        for (Tags t : mTags) {
            tags += (tags.isEmpty() ? "" : ",") + t.descLan(lang);
        }
        descrAzione = button.descTrim(lang) + "#" + tags + "#" + halfTrim(lang) + ", " + getStartAzione() + ", " + durataAzione + ", " + getTeam();

        List<String> playerList = new ArrayList<>();

        for (Atleta player : _player.values()) {
            if (player != null && StringUtils.isNotBlank(player.getKnown_name())) {
                if (playerList.size() < 3) {
                    playerList.add(playerCamel(player.getKnown_name().toLowerCase()));
                }
            }
        }
        descrAzioneOverlay = "<b>" + button.descTrim(lang) + "</b>" + (tags.isEmpty() ? "" : ": " + tags) + "<br/>" + getTeam() + " - " + StringUtils.join(playerList, ", ") + " - " + halfTrim(lang) + ", " + getStartAzione();

        return descrAzione;
        //CG return (button != null) ? button.descTrim(lang) + "#" + tags + "#" + halfTrim() + ", " + getStartAzione() + ", " + durataAzione + ", " + getTeam() : "";
    }

    public String getDescrAzione() {
        return descrAzione;
    }

    public void setDescrAzione(String descrAzione) {
        this.descrAzione = descrAzione;
    }

    public String getDescrAzioneOverlay() {
        return descrAzioneOverlay;
    }

    public void setDescrAzioneOverlay(String descrAzioneOverlay) {
        this.descrAzioneOverlay = descrAzioneOverlay;
    }

    public String getColonnaDescrizione(String lang, boolean includeNote) {
        String descr = "";
        descr = button.descTrim(lang);

        String tags = "";
        for (Tags t : mTags) {
            tags += (tags.isEmpty() ? "" : ", ") + t.descLan(lang);
        }

        descr += " " + tags;
        if (includeNote) {
            if (mNote != null && !mNote.isEmpty()) {
                descr += " " + mNote;
            }
        }

        return descr;
    }

    public String getStartMinutiSecondi() {
        return _period_start_minute + " min " + _period_start_second + " sec";
    }

    public String getMatch() {
        return mHomeTeam + "-" + mAwayTeam;
    }

    /**
     * @return the mTactStart
     */
    public Long getmTactStart() {
        return mTactStart;
    }

    /**
     * @param mTactStart the mTactStart to set
     */
    public void setmTactStart(Long mTactStart) {
        if (mTactStart != null) {
            this.mTactStart = mTactStart;
        } else {
            this.mTactStart = -1l;
        }
    }

    /**
     * @return the tactAvailable
     */
    public boolean isTactAvailable() {
        return tactAvailable;
    }

    /**
     * @param tactAvailable the tactAvailable to set
     */
    public void setTactAvailable(boolean tactAvailable) {
        this.tactAvailable = tactAvailable;
    }

    public Boolean getIsTactical() {
        return isTactical;
    }

    public void setIsTactical(Boolean isTactical) {
        this.isTactical = isTactical;
    }

    public Long getTableOrder() {
        return tableOrder;
    }

    public void setTableOrder(Long tableOrder) {
        this.tableOrder = tableOrder;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Timestamp getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateDescription() {
        return updateDescription;
    }

    public void setUpdateDescription(String updateDescription) {
        this.updateDescription = updateDescription;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Boolean getIsVisible() {
        return isVisible;
    }

    public void setIsVisible(Boolean isVisible) {
        this.isVisible = isVisible;
    }

    public String getMatchDate() {
        return matchDate;
    }

    public void setMatchDate(String matchDate) {
        this.matchDate = matchDate;
    }

    public Integer getMatchDay() {
        return matchDay;
    }

    public void setMatchDay(Integer matchDay) {
        this.matchDay = matchDay;
    }

    public void removeUnusedFields() {
        this.mHomeTeam = StringUtils.defaultIfEmpty(this.mHomeTeam, null);
        this.mIdHomeTeam = StringUtils.defaultIfEmpty(this.mIdHomeTeam, null);
        this.mAwayTeam = StringUtils.defaultIfEmpty(this.mAwayTeam, null);
        this.mIdAwayTeam = StringUtils.defaultIfEmpty(this.mIdAwayTeam, null);
        this.mNote = StringUtils.defaultIfEmpty(this.mNote, null);
//        this.mPlayer = StringUtils.defaultIfEmpty(this.mPlayer, null);
//        this.mPlayerTo = StringUtils.defaultIfEmpty(this.mPlayerTo, null);
        this.mIdEvent = StringUtils.defaultIfEmpty(this.mIdEvent, null);
        this.mIdGroup = StringUtils.defaultIfEmpty(this.mIdGroup, null);
        this.mTeamColor = StringUtils.defaultIfEmpty(this.mTeamColor, null);
        this.mTeamTextColor = StringUtils.defaultIfEmpty(this.mTeamTextColor, null);

        if (this._player != null && !this._player.isEmpty()) {
            for (Atleta player : _player.values()) {
                if (player != null) {
                    player.setHeight(StringUtils.defaultIfEmpty(player.getHeight(), null));
                    player.setCountry_it(StringUtils.defaultIfEmpty(player.getCountry_it(), null));
                    player.setCountry_en(StringUtils.defaultIfEmpty(player.getCountry_en(), null));
                    player.setCountry_logo(StringUtils.defaultIfEmpty(player.getCountry_logo(), null));
                    player.setFoot_it(StringUtils.defaultIfEmpty(player.getFoot_it(), null));
                    player.setFoot_en(StringUtils.defaultIfEmpty(player.getFoot_en(), null));
                    player.setFoot_fr(StringUtils.defaultIfEmpty(player.getFoot_fr(), null));
                    player.setFoot_es(StringUtils.defaultIfEmpty(player.getFoot_es(), null));
                    player.setPosition_detail_it(StringUtils.defaultIfEmpty(player.getPosition_detail_it(), null));
                    player.setPosition_detail_en(StringUtils.defaultIfEmpty(player.getPosition_detail_en(), null));
                    player.setPosition_detail_fr(StringUtils.defaultIfEmpty(player.getPosition_detail_fr(), null));
                    player.setPosition_detail_es(StringUtils.defaultIfEmpty(player.getPosition_detail_es(), null));
                    player.setPosition_secondary_it(StringUtils.defaultIfEmpty(player.getPosition_secondary_it(), null));
                    player.setPosition_secondary_en(StringUtils.defaultIfEmpty(player.getPosition_secondary_en(), null));
                    player.setPosition_secondary_fr(StringUtils.defaultIfEmpty(player.getPosition_secondary_fr(), null));
                    player.setPosition_secondary_es(StringUtils.defaultIfEmpty(player.getPosition_secondary_es(), null));
                }
            }
        }

        if (this._playerTo != null) {
            this._playerTo.setHeight(StringUtils.defaultIfEmpty(this._playerTo.getHeight(), null));
            this._playerTo.setCountry_it(StringUtils.defaultIfEmpty(this._playerTo.getCountry_it(), null));
            this._playerTo.setCountry_en(StringUtils.defaultIfEmpty(this._playerTo.getCountry_en(), null));
            this._playerTo.setCountry_logo(StringUtils.defaultIfEmpty(this._playerTo.getCountry_logo(), null));
            this._playerTo.setFoot_it(StringUtils.defaultIfEmpty(this._playerTo.getFoot_it(), null));
            this._playerTo.setFoot_en(StringUtils.defaultIfEmpty(this._playerTo.getFoot_en(), null));
            this._playerTo.setFoot_fr(StringUtils.defaultIfEmpty(this._playerTo.getFoot_fr(), null));
            this._playerTo.setFoot_es(StringUtils.defaultIfEmpty(this._playerTo.getFoot_es(), null));
            this._playerTo.setPosition_detail_it(StringUtils.defaultIfEmpty(this._playerTo.getPosition_detail_it(), null));
            this._playerTo.setPosition_detail_en(StringUtils.defaultIfEmpty(this._playerTo.getPosition_detail_en(), null));
            this._playerTo.setPosition_detail_fr(StringUtils.defaultIfEmpty(this._playerTo.getPosition_detail_fr(), null));
            this._playerTo.setPosition_detail_es(StringUtils.defaultIfEmpty(this._playerTo.getPosition_detail_es(), null));
            this._playerTo.setPosition_secondary_it(StringUtils.defaultIfEmpty(this._playerTo.getPosition_secondary_it(), null));
            this._playerTo.setPosition_secondary_en(StringUtils.defaultIfEmpty(this._playerTo.getPosition_secondary_en(), null));
            this._playerTo.setPosition_secondary_fr(StringUtils.defaultIfEmpty(this._playerTo.getPosition_secondary_fr(), null));
            this._playerTo.setPosition_secondary_es(StringUtils.defaultIfEmpty(this._playerTo.getPosition_secondary_es(), null));
        }
    }

    public String getJson(String language) {
        this.languageFromPage = language;
        halfTrim(languageFromPage);
        getDescrAzioneToShow(languageFromPage);

        removeUnusedFields();
        Gson gson = new Gson();
        return gson.toJson(this);
    }

    public String getJsonForVideo(String language) {
        this.languageFromPage = language;
        halfTrim(languageFromPage);
        getDescrAzioneToShow(languageFromPage);

        removeUnusedFields();
        Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().create();
        return gson.toJson(this);
    }
}
