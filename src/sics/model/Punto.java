/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.model;

import com.google.gson.annotations.Expose;

/**
 *
 * <AUTHOR>
 */
public class Punto implements Comparable<Punto> {

    @Expose
    public float x;
    @Expose
    public float y;
    @Expose
    public Boolean isDefault;

    public Punto() {
        x = -1;
        y = -1;
        
        isDefault = isDefault();
    }

    public Punto(String[] posSplitted) {
        x = Float.parseFloat(posSplitted[0]);
        y = Float.parseFloat(posSplitted[1]);
        
        isDefault = isDefault();
    }

    public Punto(float x, float y) {
        this.x = x;
        this.y = y;
        
        isDefault = isDefault();
    }

    public boolean isDefault() {
        return (x == -1 && y == -1);
    }

    @Override
    public int compareTo(Punto p) {
        if (this.x > p.x - 0.5 && this.x < p.x + 0.5 && this.y > p.y - 0.5 && this.y < p.y + 0.5) {
            return 0;
        } else {
            return 1;
        }
    }
    
    @Override
    public Punto clone() throws CloneNotSupportedException {
        return new Punto(this.x, this.y);
    }

    public float getX() {
        return x;
    }

    public void setX(float mX) {
        this.x = mX;
        isDefault = isDefault();
    }

    public float getY() {
        return y;
    }

    public void setY(float mY) {
        this.y = mY;
        isDefault = isDefault();
    }

    public String toXmlString() {
        String stringValue = String.format("%.2f", x) + ";" + String.format("%.2f", y);
        stringValue = stringValue.replace(",", ".");
        return stringValue;
    }
}
