/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.model;

import com.google.gson.annotations.Expose;

/**
 *
 * <AUTHOR>
 */
public class PuntoTiro extends Punto {

    @Expose
    private float z;

    public PuntoTiro(float x, float y, float mZ) {
        super(x, y);
        this.z = mZ;
    }

    public PuntoTiro(String[] posSplitted) {
        super(Float.parseFloat(posSplitted[0]), Float.parseFloat(posSplitted[1]));
        z = Float.parseFloat(posSplitted[2]);
    }

    /**
     * @return the z
     */
    public float getZ() {
        return z;
    }

    /**
     * @param z the z to set
     */
    public void setZ(float z) {
        this.z = z;
    }

    @Override
    public String toXmlString() {
        String stringValue = String.format("%.2f", x) + ";" + String.format("%.2f", y) + ";" + String.format("%.2f", z);
        stringValue = stringValue.replace(",", ".");
        return stringValue;
    }
}
