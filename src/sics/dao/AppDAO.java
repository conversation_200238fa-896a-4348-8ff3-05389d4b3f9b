package sics.dao;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import sics.domain.*;
import sics.model.Azione;

public interface AppDAO {

    public List<ChartData> getStartExportData(Integer timeFilterId, Long agentId) throws SQLException;
    
    public List<ChartData> getLicenseExpiredData(Integer timeFilterId, Long agentId) throws SQLException;
    
    public List<ChartData> getVideoDownloadData(Integer timeFilterId, Long agentId) throws SQLException;

    public List<ChartData> getVideoStreamData(Integer timeFilterId, String userIdFilter) throws SQLException;
    
    public List<ChartData> getUserLogData(Long userId, String from, String to) throws SQLException;
    
    public List<ChartData> getUserLast100LogData(Long userId) throws SQLException;
    
    public List<VtigerAgent> getVtigerAgents() throws SQLException;
    
    public List getUserAll(String name, String surname, String dateFrom, String dateTo, String application) throws SQLException;
    
    public List<User> getSicsTvUsers(Long timeFilterId, Long agentId) throws SQLException;

    public User getUser(Long id) throws SQLException;
    
    public List<User> getUsers(List<Long> ids) throws SQLException;

    public User getUser(String username) throws SQLException;
    
    public User getUserBySer(String ser) throws SQLException;

    public User getUserByMail(String mail) throws SQLException;

    public void saveUser(User item) throws SQLException;

    public List getCompetitionTactAllowed(Long idGro) throws SQLException;

    public void saveUser2(User item) throws SQLException;

    public void delUser(Long id) throws SQLException;

    public List getPlaylistUser(Long idGro, Long idUse) throws SQLException;

    public Playlist getPlaylist(Long id, Long idUse) throws SQLException;

    public Playlist getPlaylistByVideoName(String videoname, Long userId) throws SQLException;

    public Game getGameByVideoName(String videoname, Long ownerUserId, String language) throws SQLException;

    public void updatePlaylist(Long id, Long idUser, String name, String note) throws SQLException;

    public void setPlaylistSeen(Long id, Long idUse) throws SQLException;

    public Long getPlaylistUnseen(Long idUse, Long idGro) throws SQLException;

    public List getGameAll(Long groupId, String seasonIds, Long compId, Long teamId, Date from, Date to, String referee, Long matchdayId, String language) throws SQLException;

    public FixturePlaylistSize getFixturePlaylistSize(boolean fromUser, Long userId, Long groupsetId, String idSeasons, Long minSeason) throws SQLException;

    public List getGameAllDigital(Long groupId, String seasonIds, Long compId, Long teamId, Date from, Date to, String referee, Long matchdayId) throws SQLException;

    public Game getGame(Long id, String language) throws SQLException;
    
    public List<Game> getGamesByFixtureIds(List<Long> ids, Long groupsetId, String language) throws SQLException;

    public Game getGameByFixtureId(Long id, String language) throws SQLException;

    public List getGameBySeason(String seasonIds, Long groupId, Long startFrom, Boolean source, String language) throws SQLException;

    public List getGameDataAll(Long id) throws SQLException;

    public List getGameByCompetitionId(Long compId, String seasonIds, Long groupsetId, Long startFrom, Boolean source, Long countryId, Long groupId, String language) throws SQLException;

    public List getGameByTeamId(Long compId, String seasonIds, Long groupsetId, Long teamId, Long startFrom, Boolean source, String language, Integer limit, Long groupId) throws SQLException;

    public List getGameMulti(Long compId, String seasonIds, Long groupId, Long teamId, Long dayId, Boolean source, String language) throws SQLException;

    public List getGameByMultiFilter(Map<String, String> mapFilters, Long startFrom, Boolean source, String language) throws SQLException;

    public CalendarRange getMinAndMaxMatchdays() throws SQLException;

    public CalendarRange getMinAndMaxMatchdaysByCompetitionAndSeason(String seasonIds, Long idComp, Long countryId, Long groupId, Boolean personalSource) throws SQLException;

    public List getSeasonAll() throws SQLException;
    
    public Season getSeasonById(Long seasonId) throws SQLException;

    public List getSeasonAllAvailable(Long userId, Long groupset_id, List<Competition> allowedCompetitions, Long groupId, List<UserCompetitionException> competitionExceptions, Boolean personalSource) throws SQLException;
    
    public List getSeasonAllAvailableByParams(Long userId, Long groupsetId, Long countryId, Long intCompId, Long competitionId, Long teamId, List<Competition> allowedCompetitions, Long groupId, List<UserCompetitionException> competitionExceptions, Boolean personalSource) throws SQLException;
    
    public List getSeasonAllAvailableByPlayerId(Long playerId, Long groupsetId, List<UserCompetitionException> competitionExceptions, Boolean personalSource) throws SQLException;

    public Season getLastSeason(Long userId, Long groupsetId) throws SQLException;

    public Season getLastSeasonByCompetitionId(Long competitionId) throws SQLException;
    
    public List<Country> getInternationalCompetitionAll(String seasonIds, Long idSport,String language) throws SQLException;
    
    public List<Country> getInternationalCompetitionVisible(Long idGroupset, String seasonIds, Long idSport,String language) throws SQLException;
    
    public Country getInternationalCompetition(Long id,String language) throws SQLException;
    
    public List<Country> getCountryAll(String seasonIds, Long idSport, Long idGroupset, String allowedCompetitionIds, String language) throws SQLException;
    
    public List<Country> getBaseCountry(String seasonIds, Long idSport, String language) throws SQLException;
    
    public List<Country> getCountryVisible(Long idGroupset, String seasonIds, Long idSport,String language) throws SQLException;
    
    public Country getCountry(Long id,String language) throws SQLException;

    public List getCompetitionAll(Long idGroupset, String seasonIds, Boolean personalSource, Long idSport, List<Competition> allowedCompetitions, String language) throws SQLException;
    
    public List getCompetitionByCountry(Long idGroupset, String seasonIds, Boolean personalSource, Long idSport,String language, Long idCountry, Long idInternationalCompetition, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) throws SQLException;
    
    public List<Competition> getInternationalCompetitionByCountry(Long idGroupset, String seasonIds, Long idSport, String language, Long idCountry, List<UserCompetitionException> competitionExceptions) throws SQLException;

    public List getCompetitionByGroupsetId(Long idGroupset,String language) throws SQLException;

    public List getCompetitionBySeasons(Long idGroupset, String seasonId,String language) throws SQLException;

    public List getCompetitionByTeam(Long idGroupset, String seasonIds, Long teamId, Boolean source, List<Competition> allowedCompetitions, String language) throws SQLException;

    public List getCompetitionByPlayer(String seasonIds, Long playerId, Long groupsetId, Long groupId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException;

    public List getCompetitionByTeamPlayer(String seasonIds, Long playerId, Long teamId, Long groupsetId, Long groupId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException;

    public Competition getCompetition(Long id,String language) throws SQLException;
    
    public List<Competition> getCompetitions(String ids, String language) throws SQLException;

    public List getTeamAll(String language) throws SQLException;

    public Team getTeam(Long id, String language) throws SQLException;
    
    public List<Team> getTeams(String ids, String language) throws SQLException;

    public List getTeamByGroupsetId(Long idGroupset, String language, List<Competition> allowedCompetitions) throws SQLException;
    
    public List getNationalTeam(Long idSport, String seasonIds, Long idCountry, String language) throws SQLException;
    
    public List getNationalTeamByGroupsetId(Long idGroupset, String seasonIds, String language, Long idCountry, List<Competition> allowedCompetitions) throws SQLException;

    public List getTeamByCompetition(String seasonIds, Long CompId, Long idGro, Long sportId, Boolean personalSource, String gameIds, Long countryId, List<Competition> allowedCompetitions, Long groupId, String language) throws SQLException;

    public List getTeamByMultiFilter(String seasonIds, String compIds, String language) throws SQLException;

    public List getTeamByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language, List<UserCompetitionException> competitionExceptions) throws SQLException;
    
    public List getCompetitionByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language, List<UserCompetitionException> competitionExceptions) throws SQLException;
    
    public List<Country> getCountryByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language, List<UserCompetitionException> competitionExceptions) throws SQLException;

    public List getPlaylistDataAll(Long id) throws SQLException;

    public void addPlaylist(Playlist playlist) throws SQLException;

    public List getLogDataAll(Long idRes) throws SQLException;

    public void deleteGame(Long idFixture, Long idUser) throws SQLException;

    public void deletePlaylist(Long idPlaylist, Long idUser) throws SQLException;

    //Filtri per ricerca azioni utenti
    public List getLogDataFilter(Date dateFrom, Date dateTo, Long idVideo, String userName, String action, String application) throws SQLException;

    public void saveLogData(LogData item) throws SQLException;

    public List getDownloadDet(Long id) throws SQLException;

    public List getStreamingDet(Long id) throws SQLException;

    public Long getUserDownload(Long id) throws SQLException;
    
    public Long getUserXmlJsonExport(Long userId) throws SQLException;

    public Atleta getAtletaById(String seasonIds, Long playerId, Long teamId, Long groupsetId, Boolean personalSource, String language) throws SQLException;

    public List getAtletiByGame(Long idFixture, Long idTeamH, Long idTeamA, Long groupsetId, String language) throws SQLException;

    public List getAtletiByTeam(String seasonIds, Long idTeam, Long idGroupset, Boolean personalSource, Boolean checkPlaytime, String language) throws SQLException;

    public List getAtletiByTeamAndCompetition(String seasonIds, Long idTeam, Long idCompetition, Long idGroupset, Long groupId, Boolean personalSource, Boolean checkPlaytime, String language) throws SQLException;

    public List getAtletiSelezionati(Long idFixture, ArrayList<Long> players, Long idTeamH, Long idTeamA, String language) throws SQLException;

    public Atleta getAtleta(Long playerId, String language) throws SQLException;
    
    public List<Atleta> getAtletiByIds(String ids, String language) throws SQLException;

    public List getAtletiByRole(Long idFixture, Long roleId, Long teamId, String language) throws SQLException;

    public List getAtletiByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<UserCompetitionException> competitionExceptions) throws SQLException;

    public List getAtletiPersonalByPattern(String pattern, Long userId, Long groupsetId) throws SQLException;

    public Map getFasce(Long idConfiguration) throws SQLException;

    public Map getIntervalli() throws SQLException;

    public FilterGameWrapper readEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, User curUser, String language) throws SQLException;
    
    public FilterGameWrapper readEventMatchesByEventIds(String eventIds, Map<Long, Game> games, Long sportId, Long groupsetId, Long playlistTvId, List<Competition> allowedCompetitions, User curUser, String language) throws SQLException;

    public FilterGameWrapper readHighlightsEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, String seasonIds, String language) throws SQLException;

    public FilterGameWrapper readIPOEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, Long teamId, List<Competition> allowedCompetitions, Long userId, Long groupId, String seasonIds, String language) throws SQLException;

    public FilterGameWrapper readPlayerEventMatches(Long playerId, String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, User curUser, String language) throws SQLException;
    
    public FilterGameWrapper readTeamEventMatches(Long teamId, String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, User curUser, String language) throws SQLException;
    
    public FilterGameWrapper readPlayerBestEvents(Long playerId, Long teamId, Long competitionId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, String seasonIds, String language) throws SQLException;

    public FilterGameWrapper readPlayerHighlights(Long playerId, Long teamId, Long competitionId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, String seasonIds, String language) throws SQLException;

    public LinkedHashMap<String, Azione> getActionsByIdEvent(Long idFixture, ArrayList<String> idEvents, Long sportId, List<Competition> allowedCompetitions, Long userId, Long groupsetId, String language) throws SQLException;
    
    public List<Azione> getActionsByEventIds(String eventIds, Long sportId, List<Competition> allowedCompetitions, String language) throws SQLException;

    public List getActionsAvailable(boolean conTag, boolean soloSenzaTag, Long teamId, Long playerId, String seasonIds, Long competitionId, String fixtureId, Long groupsetId, Boolean personalSource, String gameIds, String event, List<Competition> allowedCompetitions, Long groupId, User curUser, String language) throws SQLException;
    
    public List<Azione> getActionsAvailableForCompetition(boolean conTag, String seasonIds, Long competitionId, Long teamId, Long playerId, Long groupsetId, String event, String from, String to) throws SQLException;

    public FilterGameWrapper getActionsByType(String idGame, String event, String filter, Long idComp, Long teamId, Long playerId, String seasonIds, Long limit, Long groupsetId, Long sportId, List<Competition> allowedCompetitions, String gameIds, Long userId, Long groupId, String from, String to, Boolean allSeasons, User curUser, String language) throws SQLException;

    public FilterGameWrapper getActionsSingleMatchByType(String idGame, String event, String filter, Long idComp, Long teamId, Long playerId, String seasonIds, Long limit, Long groupsetId, Long sportId, List<Competition> allowedCompetitions, Long userId, Long groupId, String language) throws SQLException;

    public Map<String, List> getPanel(String idPanels, Long idGroupset, String lang) throws SQLException;

    public Map<String, List> getPanelByName(String name, Long idGroupset, String language) throws SQLException;
    
    public Map<String, List> getPanelById(Long id, String language) throws SQLException;

    public Map<String, List> getPanelsByGroupset(Long idGroupset, String language) throws SQLException;

    public List getModuli() throws SQLException;

    public Pdata getScadenzaFromSerialNumber(String serialnumber) throws SQLException;

    public Long addPlaylistTv(PlaylistTV playlistTV) throws SQLException;
    
    public void addPlaylistTvEvents(String values) throws SQLException;
    
    public List<PlaylistTV> getPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<PlaylistTV> getPlaylistTvWithCounterByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public void deletePlaylistTvById(Long playlistId) throws SQLException;
    
    public List<PlaylistTVEvent> getPlaylistTvEventsById(Long playlistId) throws SQLException;
    
    public List<PlaylistTVEvent> getPlaylistTvEventsDeletedById(Long playlistId) throws SQLException;
    
    public PlaylistTV getPlaylistTvById(Long playlistId) throws SQLException;
    
    public void updatePlaylistTv(PlaylistTV playlistTV) throws SQLException;
    
    public void updatePlaylistShare(PlaylistTVShare playlistTVShare) throws SQLException;
    
    public void updatePlaylistTvEvent(PlaylistTVEvent event) throws SQLException;
    
    public void deletePlaylistTvEvents(Long playlistId, String eventIds, Long updateBy, Timestamp updateDate, String updateDescription) throws SQLException;
    
    public void permanentlyDeletePlaylistTvEvents(Long playlistId, String eventIds) throws SQLException;
    
    public Groupset getGroupsetById(Long groupsetId) throws SQLException;
    
    public List<User> getUserListByGroupsetId(Long groupsetId) throws SQLException;
    
    public List<PlaylistTVShare> getAllPlaylistTVShareByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public void addPlaylistTVShare(Long userId, Long playlistId, boolean editable) throws SQLException;
    
    public void deletePlaylistTVShare(Long playlistId) throws SQLException;
    
    public List<PlaylistTV> getSharedPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<PlaylistTV> getSharedPlaylistTvWithCounterByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<PlaylistTV> getSharingPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public PlaylistTVShare getSharedPlaylistTvByUserIdAndPlaylistId(Long userId, Long groupsetId, Long playlistId) throws SQLException;
    
    public List<PlaylistTVShare> getAllSharedPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public PlaylistTVSharePublic getPlaylistTvSharePublicByUrl(String url) throws SQLException;
    
    public PlaylistTVSharePublic getPlaylistTvSharePublicByUserIdAndPlaylistId(Long userId, Long playlistId) throws SQLException;
    
    public void updatePlaylistTvSharePublicClick(Long id, Long click) throws SQLException;
    
    public List<PlaylistTVSharePublic> getAllPlaylistTvPublicShareByUserId(Long userId) throws SQLException;
    
    public void addPlaylistTvSharePublic(PlaylistTVSharePublic share) throws SQLException;
    
    public void removePlaylistTvSharePublic(Long id) throws SQLException;
    
    public Season getLastSeasonForCountryId(Long userId, Long groupsetId, Long countryId, String allowedCompetitionIds, List<UserCompetitionException> competitionExceptions) throws SQLException;
    
    public Season getLastSeasonForCountryIdWithNationalTeams(Long userId, Long groupsetId, Long countryId, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) throws SQLException;
    
    public Season getLastSeasonForInternationalCompetitionId(Long userId, Long groupsetId, Long intCompId, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) throws SQLException;
    
    public PlaylistTVEvent getPlaylistTvLastUpdateEventById(Long playlistId) throws SQLException;
    
    public List<Game> getGameIdsListByFilters(String from, String where) throws SQLException;
    
    public List<StatsType> getAllStatsType(String language, Integer availability, String specificLanguage) throws SQLException;
    
    public List<TeamStats> getTeamStatsByCompetitionId(Long competitionId, Long countryId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByCompetitionId(Long competitionId, Long countryId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) throws SQLException;
    
    public List<TeamStats> getTeamStatsByCompetitionIdAndTeamId(Long competitionId, Long countryId, Long teamId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) throws SQLException;
    
    public List<TeamStats> getTeamStatsByCompetitionIdAndTeamIdForPlayers(Long competitionId, Long countryId, Long teamId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByPlayerIds(String playerIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, Boolean isGrouped, String language, String specificLanguage) throws SQLException;
    
    public Long addTeamStatsFilter(TeamStatsFilter filter) throws SQLException;
    
    public void addTeamStatsFilterDetails(List<TeamStatsFilterDetail> filterDetailList) throws SQLException;
    
    public void deleteTeamStatsFilterDetails(Long statsTypeFilterId) throws SQLException;
    
    public List<TeamStatsFilter> getTeamStatsFilterByUserId(Long userId, Long groupsetId, String tableType, String language) throws SQLException;
    
    public List<TeamStatsFilterDetail> getTeamStatsFilterDetailsByFilterId(Long filterId) throws SQLException;
    
    public TeamStatsFilter getTeamStatsFilterById(Long filterId, String language) throws SQLException;
    
    public void updateTeamStatsFilter(TeamStatsFilter filter) throws SQLException;
    
    public void updateTeamStatsFilterPreferred(Long isPreferred, String where) throws SQLException;
    
    public void deleteTeamStatsFilter(TeamStatsFilter filter) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByPlayerId(Long playerId, String competitions, String teams, String seasonIds, Long positionId, String statsTypeIds, Boolean averageCompetition, Long groupId, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByPlayerIdGroupByFixture(Long playerId, String competitions, String teams, String seasonIds, Long positionId, String statsTypeIds, Long groupId, String language, String specificLanguage, String from, String to, boolean isTabellino) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByFilters(String competitions, String seasonIds, Long positionId, String statsTypeIds, String language) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByFiltersExcludedPlayer(Long playerId, String competitions, String seasonIds, Long positionId, String statsTypeIds, Boolean averageCompetition, Long groupId, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) throws SQLException;
    
    public List<TeamStats> getPlayerStatsResumeCareer(Long playerId, String language, String specificLanguage) throws SQLException;
    
    public List<Game> getModuleAmountByTeam(Long teamId, Long compId, String seasonIds, Long groupId, String from, String to) throws SQLException;
    
    public List<TeamReportRanking> getTeamRankingBySeasonAndCompetition(String seasonIds, String competitions, Long groupId, String language, String from, String to) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByTeamAndCompetition(Long teamId, String seasonIds, String competitions, Long groupId, String language, String specificLanguage, String from, String to) throws SQLException;
    
    public List<TeamStats> getTeamStatsByCompetition(String seasonIds, String competitions, String statsTypeIdsForTables, Long groupId, Boolean groupedByCompetition, String language, String specificLanguage, String from, String to) throws SQLException;
    
    public List<TeamStats> getDefaultTeamStatsByCompetition(String seasonIds, String competitions, Long groupId, Boolean groupedByCompetition, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) throws SQLException;
    
    public List<TeamStats> getTeamStatsByCompetitionAndPosition(String seasonIds, String competitions, Long positionId, String statsTypeIds, Long groupId, Boolean groupedByCompetition, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) throws SQLException;
    
    public List<Atleta> getAtletiJerseyAndRoleByFilters(String seasonId, String teamId, String playerId, String language) throws SQLException;
    
    public List<TeamStats> getTeamPlayerAmountByCompetition(String seasonIds, String competitions, String language) throws SQLException;
    
    public TeamStats getCompetitionMinutesPlayed(String seasonIds, String competitions) throws SQLException;
    
    public Position getPositionById(Long positionId, String language) throws SQLException;
    
    public List<Position> getPositionByIds(String positionIds, String language) throws SQLException;
    
    public Long getGameAmountByCompetitions(String competitionIds, Long groupId, String seasonIds, String from, String to, boolean isTabellino) throws SQLException;
    
    public Team getLastTeamForPlayer(Long playerId, String language) throws SQLException;
    
    public List<Team> getLastTeamForPlayers(String playerIds, String language) throws SQLException;
    
    public Long getFixtureByGameId(Long gameId) throws SQLException;
    
    public List<Long> getFixturesByGameIds(String gameIds) throws SQLException;
    
    public List<Watchlist> getWatchlistByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public Watchlist getWatchlistById(Long watchlistId) throws SQLException;
    
    public List<WatchlistDetail> getWatchlistDetailByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<WatchlistDetail> getWatchlistDetailById(Long detailId) throws SQLException;
    
    public Long addWatchlist(Watchlist watchlist) throws SQLException;
    
    public void addWatchlistDetail(WatchlistDetail detail) throws SQLException;
    
    public void removePlayerFromWatchlist(Long playerId, Long watchlistId) throws SQLException;
    
    public void updateWatchlist(Watchlist watchlist) throws SQLException;
    
    public void deleteWatchlist(Watchlist watchlist) throws SQLException;
    
    public List<WatchlistShare> getAllWatchlistShareByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public void deleteWatchlistShare(Long watchlistId) throws SQLException;
    
    public void addWatchlistShare(Long userId, Long watchlistId, boolean editable) throws SQLException;
    
    public List<Watchlist> getWatchlistSharedWithUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<WatchlistDetail> getWatchlistDetailSharedWithUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<Favorites> getFavoritesByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public void addFavorites(Favorites favorites) throws SQLException;
    
    public void deleteFavorites(Long id) throws SQLException;
    
    public List<Shortcut> getShortcutsByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public void addShortcut(Shortcut shortcut) throws SQLException;
    
    public void deleteShortcut(Long id) throws SQLException;
    
    public void deleteShortcuts(String ids) throws SQLException;
    
    public Settings getSettingsByUserId(Long userId) throws SQLException;
    
    public void addSettings(Settings settings) throws SQLException;
    
    public void updateSettings(Settings settings) throws SQLException;
    
    public List<Long> getUserIdFromVtigerAgentId(Long agentId) throws SQLException;
    
    public Long addExport(Export export) throws SQLException;
    
    public void addExportDetail(String values) throws SQLException;
    
    public void updateExport(Export export) throws SQLException;
    
    public List<Export> getExportByUser(Long userId, Long groupsetId) throws SQLException;
    
    public List<ExportDetail> getExportDetailByUser(Long userId, Long groupsetId) throws SQLException;
    
    public Long getPlayerInLineupAmount(Long playerId, Long competitionId, Long teamId, String seasonIds, Long groupsetId, List<Competition> allowedCompetitions) throws SQLException;
    
    public Long getUserExportLimit(Long userId) throws SQLException;
    
    public Long getUserExportTimeUsed(Long userId) throws SQLException;
    
    public List<Export> getExportByUserAndData(Long userId, Long groupsetId, String from, String to) throws SQLException;
    
    public List<ExportDetail> getExportDetailByUserAndData(Long userId, Long groupsetId, String from, String to) throws SQLException;
    
    public List<Team> getCompetitionTeamGironi(Long teamId, String seasonIds, Long groupId, String language) throws SQLException;
    
    public List<Competition> getTeamGironi(Long competitionId, Long teamId, String seasonIds, List<Competition> allowedCompetitions, String language, Locale userLanguage) throws SQLException;
    
    public List<Competition> getPlayerGironi(Long playerId, Long competitionId, Long teamId, String seasonIds, List<Competition> allowedCompetitions, String language, Locale userLanguage) throws SQLException;
    
    public List<VtigerAgent> getUserVtigerAgent() throws SQLException;

    public List<UserCompetitionException> getCompetitionExceptionByGroupsetId(Long groupsetId) throws SQLException;
    
    public Team getGironeById(Long id, String language, Locale userLanguage) throws SQLException;
    
    public List<TeamStats> getTeamGameAmountByCompetitionId(Long competitionId, Long countryId, String gameIds, String seasonIds, Long groupId, Date from, Date to, String language) throws SQLException;
    
    public List<FixtureDetails> getFixtureDetails(List<Long> fixtureIds) throws SQLException;
    
    public List<Azione> checkEventTactical(Long groupsetId, List<String> eventIds) throws SQLException;
    
    public List<Atleta> getPlayerModulePositionAmount(Long playerId, Long teamId, Long competitionId, List<Competition> allowedCompetitions, Long groupId, String seasonIds, String from, String to) throws SQLException;
    
    public void addPlayerStatsSummary(List<TeamStats> playerSummary) throws SQLException;
    
    public void addPlayerStatsSummary(String values) throws SQLException;
    
    public void deletePlayerStatsSummary(Long playerId) throws SQLException;
    
    public List<TeamStats> getPlayerStatsForSummary(Long playerId) throws SQLException;
    
    public List<Long> getValidPlayersForSummary() throws SQLException;
    
    public List<TeamStats> getSmartSearchPlayerData(Map<String, String> filters) throws SQLException;
    
    public List<Cache> getSmartSearchPlayerBaseData(Long playerId, String seasonId) throws SQLException;
    
    public List<Cache> getSmartSearchTeamBaseData(Long teamId, String seasonId) throws SQLException;
    
    public List<Cache> getStatisticsData(Long playerId, String seasonId, String language) throws SQLException;
    
    public Cache getSmartSearchPlayerLastTeamData(Long playerId, String seasonId) throws SQLException;
    
    public List<Cache> getSmartSearchPlayerMatchData(Long playerId, String seasonId) throws SQLException;
    
    public List<Long> getSeasonAvailableForPlayer(Long playerId) throws SQLException;
    
    public List<Long> getSeasonAvailableForTeam(Long teamId) throws SQLException;
    
    public List<Long> getAllValidPlayers() throws SQLException;
    
    public List<Long> getAllValidTeams() throws SQLException;
    
    public List<StatsType> getStatsTypeFilters() throws SQLException;
    
    public List<CacheLanguageItem> getCacheCountryLanguages() throws SQLException;
    
    public List<CacheLanguageItem> getCacheFootLanguages() throws SQLException;
    
    public List<CacheLanguageItem> getCacheStatsTypeLanguages() throws SQLException;
    
    public List<CacheLanguageItem> getCacheCompetitionLanguages() throws SQLException;
    
    public List<CacheLanguageItem> getCacheTeamLanguages() throws SQLException;
    
    public List<CacheLanguageItem> getCachePositionLanguages() throws SQLException;
    
    public List<Competition> getTop5EuropeanCompetitions(String language) throws SQLException;
    
    public List<Competition> getTop5SouthAmericaCompetitions(String language) throws SQLException;

    public List<Country> getCountriesForUpload(String seasonIds, String language) throws SQLException;

    public List<Competition> getCompetitionsForUpload(String seasonIds, String language) throws SQLException;

    public List<Team> getTeamsForUpload(String seasonIds, String language) throws SQLException;

    public List<Game> getFixturesUploadableByCompetition(Long competitionId, String language) throws SQLException;

    public void insertFixtureVideo(Map<String, Object> parameters) throws SQLException;

    public Long getPlayerIdBySerialNumber(String serialNumber) throws SQLException;

    public void updateFixtureScore(Long fixtureId, Long homeScore, Long awayScore) throws SQLException;

    public void updateCompetitionLogo(Long competitionId, String logo) throws SQLException;

    public void updateTeamLogo(Long teamId, String logo) throws SQLException;

    public void updatePlayerPhoto(Long playerId, String photo) throws SQLException;

    public void updatePlayerAgentPhoto(Long agentId, String photo) throws SQLException;

    public String getFixtureFileproject(Long fixtureId) throws SQLException;

    public Long getGameIdByFixtureId(Long fixtureId) throws SQLException;

    public Long insertPersonalPlayer(Atleta player, Long userId, Long groupsetId) throws SQLException;

    public void updatePersonalPlayer(Atleta player) throws SQLException;

    public void deletePersonalPlayer(Long id) throws SQLException;

    public List<Atleta> getPlayerPersonalAll(Long userId, Long groupsetId, Boolean all) throws SQLException;

    public Atleta getPlayerPersonal(Long playerId, Long userId, Long groupsetId) throws SQLException;

    public List<Atleta> getPlayerPersonalByIds(String ids, Long userId, Long groupsetId) throws SQLException;

    public Atleta getPlayerPersonalByPlayerId(Long playerId, Long userId, Long groupsetId) throws SQLException;

    public ShadowTeam getShadowTeam(Long id) throws SQLException;

    public List<ShadowTeam> getShadowTeams(Long userId, Long groupsetId) throws SQLException;

    public List<ShadowTeamDetail> getShadowTeamDetails(Long shadowTeamId) throws SQLException;

    public Long insertShadowTeam(ShadowTeam shadowTeam, Long userId, Long groupsetId) throws SQLException;

    public void updateShadowTeam(ShadowTeam shadowTeam) throws SQLException;

    public void insertShadowTeamDetails(List<ShadowTeamDetail> shadowTeamDetails) throws SQLException;

    public void deleteShadowTeam(Long shadowTeamId) throws SQLException;

    public void deleteShadowTeamDetails(Long shadowTeamId) throws SQLException;

    public Long insertPersonalEvent(Map<String, String> parameters) throws SQLException;

    public void insertEventPlayer(Long eventId, Long playerId) throws SQLException;

    public void updatePersonalEvent(Map<String, String> parameters) throws SQLException;

    public void deletePersonalEvent(Long eventId) throws SQLException;

    public void deletePersonalEventPlayers(Long eventId) throws SQLException;

    public List<Azione> getPersonalEvents(Long groupsetId, String seasonIds, List<Competition> allowedCompetitions, User curUser, String language) throws SQLException;

    public Integer getFixturePlayerJerseyNumber(Long fixtureId, Long playerId) throws SQLException;

    public Integer getFullFixtureAmount(String competitionIds, String seasonIds, Long groupId, Date from, Date to) throws SQLException;

    public void updateTeamColor(Long teamId, String color) throws SQLException;

    public List<Atleta> getAtletiByAgentId(Long agentId, String language) throws SQLException;

    public List<PlayerAgent> getPlayerAgents(Integer sort, String language) throws SQLException;

    public PlayerAgent getPlayerAgent(Long agentId, String language) throws SQLException;

    public void updatePlayerAgentDetails(Long agentId, String name, String street, String location, String phone, String email, String website, Long countryId) throws SQLException;

    public List<Tags> getGroupsetEventType(String panelName, Long groupsetId) throws SQLException;

    public List<Tags> getGroupsetTagType(String panelName, Long groupsetId) throws SQLException;

    public List<String> getLastSeasonPair() throws SQLException;
}
