/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.dao.service;

import sics.domain.*;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.transaction.annotation.Transactional;
import sics.model.Azione;

@Transactional
public interface ManagerService {

    // statistiche admin page
    public List<ChartData> getStartExportData(Integer timeFilterId, Long agentId) throws SQLException;
    
    public List<ChartData> getLicenseExpiredData(Integer timeFilterId, Long agentId) throws SQLException;
    
    public List<ChartData> getVideoDownloadData(Integer timeFilterId, Long agentId) throws SQLException;
    
    public List<ChartData> getUserLogData(Long userId, String from, String to) throws SQLException;
    
    public List<ChartData> getUserLast100LogData(Long userId) throws SQLException;
    
    public List<VtigerAgent> getVtigerAgents() throws SQLException;
    
    public List getUserAll(String name, String surname, String dateFrom, String dateTo, String application) throws SQLException;
    
    public List<User> getSicsTvUsers(Long timeFilterId, Long agentId) throws SQLException;

    public User getUser(Long id) throws SQLException;
    
    public List<User> getUsers(List<Long> ids) throws SQLException;

    public User getUser(String username) throws SQLException;

    public User getUserByMail(String mail) throws SQLException;

    public void saveUser(User item) throws SQLException;

    public List getCompetitionTactAllowed(Long idGro) throws SQLException;

    //Inserita in più
    public void saveUser2(User item) throws SQLException;

    public void delUser(Long id) throws SQLException;

    public List getPlaylistUser(Long idGro, Long idUse) throws SQLException;

    public Playlist getPlaylist(Long id, Long idUse) throws SQLException;

    public Playlist getPlaylistByVideoName(String videoname, Long userId) throws SQLException;

    public Game getGameByVideoName(String videoname, Long ownerUserId, String language) throws SQLException;

    public void updatePlaylist(Long id, Long idUser, String name, String note) throws SQLException;

    public void setPlaylistSeen(Long id, Long idUse) throws SQLException;

    public FixturePlaylistSize getFixturePlaylistSize(boolean fromUser, Long userId, Long groupsetId, String idSeasons, Long minSeason) throws SQLException;

    public Long getPlaylistUnseen(Long idUse, Long idGro) throws SQLException;

    public void deleteGame(Long idFixture, Long idUser) throws SQLException;

    public void deletePlaylist(Long idPlaylist, Long idUser) throws SQLException;

    public List getGameAll(Long groupId, Long seasId, Long compId, Long teamId, Date from, Date to, String referee, Long matchdayId, String language) throws SQLException;

    public List getGameAllDigital(Long groupId, Long seasId, Long compId, Long teamId, Date from, Date to, String referee, Long matchdayId) throws SQLException;

    public Game getGame(Long id, String language) throws SQLException;
    
    public List<Game> getGamesByFixtureIds(List<Long> ids, Long groupsetId, String language) throws SQLException;

    public Game getGameByFixtureId(Long id, String language) throws SQLException;

    public List getGameBySeason(Long seasId, Long groupId, Long startFrom, Boolean source, String language) throws SQLException;

    public List getGameByCompetitionId(Long compId, Long seasId, Long groupId, Long startFrom, Boolean source, Long countryId, String language) throws SQLException;

    public List getGameByTeamId(Long compId, Long seasId, Long groupId, Long teamId, Long startFrom, Boolean source, String language, Integer limit) throws SQLException;

    public List getGameDataAll(Long id) throws SQLException;

    public CalendarRange getMinAndMaxMatchdays() throws SQLException;

    public CalendarRange getMinAndMaxMatchdaysByCompetitionAndSeason(Long idSeason, Long idComp, Long countryId, Boolean personalSource) throws SQLException;

    public List getSeasonAll() throws SQLException;
    
    public Season getSeasonById(Long seasonId) throws SQLException;

    public List getSeasonAllAvailable(Long userId, Long groupset_id, List<Competition> allowedCompetitions) throws SQLException;
    
    public List getSeasonAllAvailableByParams(Long userId, Long groupsetId, Long countryId, Long intCompId, Long competitionId, Long teamId, List<Competition> allowedCompetitions) throws SQLException;
    
    public List getSeasonAllAvailableByPlayerId(Long playerId) throws SQLException;

    public Season getLastSeason(Long userId, Long groupsetId) throws SQLException;
    
    public List<Country> getInternationalCompetitionAll(Long seasId, Long idSport,String language) throws SQLException;
    
    public List<Country> getInternationalCompetitionVisible(Long idGroupset, Long seasId, Long idSport,String language) throws SQLException;
    
    public Country getInternationalCompetition(Long id, String language) throws SQLException;

    public List<Country> getCountryAll(Long seasId, Long idSport, Long idGroupset, String allowedCompetitionIds, String language) throws SQLException;
    
    public List<Country> getBaseCountry(Long seasId, Long idSport, String language) throws SQLException;
    
    public List<Country> getCountryVisible(Long idGroupset, Long seasId, Long idSport,String language) throws SQLException;
    
    public Country getCountry(Long id, String language) throws SQLException;

    public List getCompetitionAll(Long idGroupset, Long seasId, Boolean personalSource, Long idSport, List<Competition> allowedCompetitions, String language) throws SQLException;
    
    public List getCompetitionByCountry(Long idGroupset, Long seasId, Boolean personalSource, Long idSport,String language, Long idCountry, Long idInternationalCompetition, List<Competition> allowedCompetitions) throws SQLException;
    
    public List<Competition> getInternationalCompetitionByCountry(Long idGroupset, Long seasId, Long idSport, String language, Long idCountry) throws SQLException;

    public List getCompetitionByGroupsetId(Long idGroupset, String language) throws SQLException;

    public List getCompetitionBySeasons(Long idGroupset, String seasId, String language) throws SQLException;

    public List getCompetitionByTeam(Long idGroupset, Long seasId, Long teamId, Boolean source, List<Competition> allowedCompetitions, String language) throws SQLException;

    public List getCompetitionByPlayer(Long seasId, Long playerId, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException;

    public List getCompetitionByTeamPlayer(Long seasId, Long playerId, Long teamId, Long groupsetId, Boolean personalSource, String language) throws SQLException;

    public Competition getCompetition(Long id, String language) throws SQLException;
    
    public List<Competition> getCompetitions(String ids, String language) throws SQLException;

    public List getTeamAll(String language) throws SQLException;

    public Team getTeam(Long id, String language) throws SQLException;
    
    public List<Team> getTeams(String ids, String language) throws SQLException;

    public List getTeamByGroupsetId(Long idGroupset, String language, List<Competition> allowedCompetitions) throws SQLException;

    public List getNationalTeam(Long idSport, Long idSeason, Long idCountry, String language) throws SQLException;
    
    public List getNationalTeamByGroupsetId(Long idGroupset, Long idSeason, String language, Long idCountry, List<Competition> allowedCompetitions) throws SQLException;
    
    public List getTeamByCompetition(Long seasId, Long compId, Long idGro, Long sportId, Boolean personalSource, String gameIds, Long countryId, List<Competition> allowedCompetitions, String language) throws SQLException;

    public List getTeamByMultiFilter(String seasIds, String compIds, String language) throws SQLException;

    public List getTeamByPattern(String pattern, Long sportId, Long seasonId, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException;
    
    public List getCompetitionByPattern(String pattern, Long sportId, Long seasonId, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException;
    
    public List<Country> getCountryByPattern(String pattern, Long sportId, Long seasonId, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException;

    public List getGameMulti(Long compId, Long seasId, Long groupId, Long teamId, Long dayId, Boolean source, String language) throws SQLException;

    public List getGameByMultiFilter(Map<String, String> mapFilters, Long startFrom, Boolean source, String language) throws SQLException;

    public List getPlaylistDataAll(Long id) throws SQLException;

    public void addPlaylist(Playlist playlist) throws SQLException;

    public List getLogDataAll(Long idRes) throws SQLException;

    public List getLogDataFilter(Date dateFrom, Date dateTo, Long idVideo, String userName, String action, String application) throws SQLException;

    public void saveLogData(LogData item) throws SQLException;

    public Long getUserDownload(Long idUser) throws SQLException;
    
    public Long getUserXmlJsonExport(Long userId) throws SQLException;

    //Statistiche download e streaming per singolo utente
    public List getDownloadDet(Long id) throws SQLException;

    public List getStreamingDet(Long id) throws SQLException;

    public Atleta getAtletaById(Long seasonId, Long playerId, Long teamId, Long groupsetId, Boolean personalSource, String language) throws SQLException;

    public List getAtletiByGame(Long idFixture, Long idTeamH, Long idTeamA, Long groupsetId, String language) throws SQLException;

    public List getAtletiByTeam(Long idSeason, Long idTeam, Long idGroupset, Boolean personalSource, String language) throws SQLException;

    public List getAtletiByTeamAndCompetition(Long idSeason, Long idTeam, Long idCompetition, Long idGroupset, Boolean personalSource, String language) throws SQLException;

    public List getAtletiSelezionati(Long idFixture, ArrayList<Long> players, Long idTeamH, Long idTeamA, String language) throws SQLException;
    
    public List<Atleta> getAtletiByIds(String ids, String language) throws SQLException;

    public List getAtletiByRole(Long idFixture, Long roleId, Long teamId, String language) throws SQLException;

    public List getAtletiByPattern(String pattern, Long sportId, Long seasonId, Long groupsetId, Boolean personalSource) throws SQLException;

    public Map getFasce(Long idConfiguration) throws SQLException;

    public Map getIntervalli() throws SQLException;

    public FilterGameWrapper readPlayerEventMatches(Long idPlayer, String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, String language) throws SQLException;

    public FilterGameWrapper readEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, String language) throws SQLException;
    
    public FilterGameWrapper readEventMatchesByEventIds(String eventIds, Map<Long, Game> games, Long sportId, Long groupsetId, Long playlistTvId, List<Competition> allowedCompetitions, User curUser, String language) throws SQLException;

    public FilterGameWrapper readHighlightsEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, String language) throws SQLException;

    public FilterGameWrapper readIPOEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, Long teamId, List<Competition> allowedCompetitions, Long userId, String language) throws SQLException;

    public LinkedHashMap<String, Azione> getActionsByIdEvent(Long idFixture, ArrayList<String> idEvents, Long sportId, List<Competition> allowedCompetitions, Long userId, Long groupsetId, String language) throws SQLException;
    
    public List<Azione> getActionsByEventIds(String eventIds, Long sportId, List<Competition> allowedCompetitions, String language) throws SQLException;

    public List getActionsAvailable(boolean conTag, boolean soloSenzaTag, Long teamId, Long playerId, Long seasonId, Long competitionId, String fixtureId, Long groupsetId, Boolean personalSource, String gameIds, String event, List<Competition> allowedCompetitions, String language) throws SQLException;

    public FilterGameWrapper getActionsByType(String idGame, String event, String filter, Long idComp, Long teamId, Long playerId, Long seasonId, Long limit, Long groupsetId, Long sportId, String gameIds, Long userId, String language) throws SQLException;

    public FilterGameWrapper getActionsSingleMatchByType(String idGame, String event, String filter, Long idComp, Long teamId, Long playerId, Long seasonId, Long limit, Long groupsetId, Long sportId, List<Competition> allowedCompetitions, Long userId, String language) throws SQLException;

    public Map<String, List> getPanel(String idPanels, Long idGroupset, String lang) throws SQLException;

    public Map<String, List> getPanelByName(String name, Long idGroupset) throws SQLException;
    
    public Map<String, List> getPanelById(Long id) throws SQLException;

    public Map<String, List> getPanelsByGroupset(Long idGroupset) throws SQLException;

    public List getModuli() throws SQLException;

    public Pdata getScadenzaFromSerialNumber(String serialnumber) throws SQLException;
    
    public Long addPlaylistTv(PlaylistTV playlistTV) throws SQLException;
    
    public void addPlaylistTvEvents(String values) throws SQLException;
    
    public List<PlaylistTV> getPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<PlaylistTV> getPlaylistTvWithCounterByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public void deletePlaylistTvById(Long playlistId) throws SQLException;
    
    public List<PlaylistTVEvent> getPlaylistTvEventsById(Long playlistId) throws SQLException;
    
    public List<PlaylistTVEvent> getPlaylistTvEventsDeletedById(Long playlistId) throws SQLException;
    
    public PlaylistTV getPlaylistTvById(Long playlistId) throws SQLException;
    
    public void updatePlaylistTv(PlaylistTV playlistTV) throws SQLException;
    
    public void updatePlaylistShare(PlaylistTVShare playlistTVShare) throws SQLException;
    
    public void updatePlaylistTvEvent(PlaylistTVEvent event) throws SQLException;
    
    public void deletePlaylistTvEvents(Long playlistId, String eventIds, Long updateBy, Timestamp updateDate, String updateDescription) throws SQLException;
    
    public void permanentlyDeletePlaylistTvEvents(Long playlistId, String eventIds) throws SQLException;
    
    public Groupset getGroupsetById(Long groupsetId) throws SQLException;
    
    public List<User> getUserListByGroupsetId(Long groupsetId) throws SQLException;
    
    public List<PlaylistTVShare> getAllPlaylistTVShareByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public void addPlaylistTVShare(Long userId, Long playlistId, boolean editable) throws SQLException;
    
    public void deletePlaylistTVShare(Long playlistId) throws SQLException;
    
    public List<PlaylistTV> getSharedPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<PlaylistTV> getSharedPlaylistTvWithCounterByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<PlaylistTV> getSharingPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public PlaylistTVShare getSharedPlaylistTvByUserIdAndPlaylistId(Long userId, Long groupsetId, Long playlistId) throws SQLException;
    
    public List<PlaylistTVShare> getAllSharedPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public PlaylistTVSharePublic getPlaylistTvSharePublicByUrl(String url) throws SQLException;
    
    public PlaylistTVSharePublic getPlaylistTvSharePublicByUserIdAndPlaylistId(Long userId, Long playlistId) throws SQLException;
    
    public void updatePlaylistTvSharePublicClick(Long id, Long click) throws SQLException;
    
    public List<PlaylistTVSharePublic> getAllPlaylistTvPublicShareByUserId(Long userId) throws SQLException;
    
    public void addPlaylistTvSharePublic(PlaylistTVSharePublic share) throws SQLException;
    
    public void removePlaylistTvSharePublic(Long id) throws SQLException;
    
    public Season getLastSeasonForCountryId(Long userId, Long groupsetId, Long countryId, String allowedCompetitionIds) throws SQLException;
    
    public Season getLastSeasonForCountryIdWithNationalTeams(Long userId, Long groupsetId, Long countryId, List<Competition> allowedCompetitions) throws SQLException;
    
    public Season getLastSeasonForInternationalCompetitionId(Long userId, Long groupsetId, Long intCompId, List<Competition> allowedCompetitions) throws SQLException;
    
    public PlaylistTVEvent getPlaylistTvLastUpdateEventById(Long playlistId) throws SQLException;
    
    public List<Game> getGameIdsListByFilters(String from, String where) throws SQLException;
    
    public List<StatsType> getAllStatsType(String language, Integer availability) throws SQLException;
    
    public List<TeamStats> getTeamStatsByCompetitionId(Long competitionId, Long countryId, String gameIds, String tableType, Long filterId, Long seasonId, String language) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByCompetitionId(Long competitionId, Long countryId, String gameIds, String tableType, Long filterId, Long seasonId, String language) throws SQLException;
    
    public List<TeamStats> getTeamStatsByCompetitionIdAndTeamId(Long competitionId, Long countryId, Long teamId, String gameIds, String tableType, Long filterId, Long seasonId, String language) throws SQLException;
    
    public List<TeamStats> getTeamStatsByCompetitionIdAndTeamIdForPlayers(Long competitionId, Long countryId, Long teamId, String gameIds, String tableType, Long filterId, Long seasonId, String language) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByPlayerIds(String playerIds, String tableType, Long filterId, Long seasonId, String language) throws SQLException;
    
    public Long addTeamStatsFilter(TeamStatsFilter filter) throws SQLException;
    
    public void addTeamStatsFilterDetails(List<TeamStatsFilterDetail> filterDetailList) throws SQLException;
    
    public void deleteTeamStatsFilterDetails(Long statsTypeFilterId) throws SQLException;
    
    public List<TeamStatsFilter> getTeamStatsFilterByUserId(Long userId, Long groupsetId, String tableType, String language) throws SQLException;
    
    public List<TeamStatsFilterDetail> getTeamStatsFilterDetailsByFilterId(Long filterId) throws SQLException;
    
    public TeamStatsFilter getTeamStatsFilterById(Long filterId, String language) throws SQLException;
    
    public void updateTeamStatsFilter(TeamStatsFilter filter) throws SQLException;
    
    public void updateTeamStatsFilterPreferred(Long isPreferred, String where) throws SQLException;
    
    public void deleteTeamStatsFilter(TeamStatsFilter filter) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByPlayerId(Long playerId, String competitions, String teams, Long seasonId, Long positionId, String statsTypeIds, Boolean averageCompetition, String language) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByPlayerIdGroupByFixture(Long playerId, String competitions, String teams, Long seasonId, Long positionId, String statsTypeIds, String language) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByFilters(String competitions, Long seasonId, Long positionId, String statsTypeIds, String language) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByFiltersExcludedPlayer(Long playerId, String competitions, Long seasonId, Long positionId, String statsTypeIds, Boolean averageCompetition, String language) throws SQLException;
    
    public List<TeamStats> getPlayerStatsResumeCareer(Long playerId, String language) throws SQLException;
    
    public List<Game> getModuleAmountByTeam(Long teamId, Long compId, Long seasonId) throws SQLException;
    
    public List<TeamReportRanking> getTeamRankingBySeasonAndCompetition(Long seasonId, String competitions, String language) throws SQLException;
    
    public List<TeamStats> getPlayerStatsByTeamAndCompetition(Long teamId, Long seasonId, String competitions, String language) throws SQLException;
    
    public List<TeamStats> getTeamStatsByCompetition(Long seasonId, String competitions, String statsTypeIdsForTables, String language) throws SQLException;
    
    public List<TeamStats> getDefaultTeamStatsByCompetition(Long seasonId, String competitions, String language) throws SQLException;
    
    public List<TeamStats> getTeamStatsByCompetitionAndPosition(Long seasonId, String competitions, Long positionId, String statsTypeIds, String language) throws SQLException;
    
    public List<Atleta> getAtletiJerseyAndRoleByFilters(String seasonId, String teamId, String playerId, String language) throws SQLException;
    
    public List<TeamStats> getTeamPlayerAmountByCompetition(Long seasonId, String competitions, String language) throws SQLException;
    
    public TeamStats getCompetitionMinutesPlayed(Long seasonId, String competitions) throws SQLException;
    
    public Position getPositionById(Long positionId, String language) throws SQLException;
    
    public List<Position> getPositionByIds(String positionIds, String language) throws SQLException;
    
    public Long getGameAmountByCompetitions(String competitionIds, Long seasonId) throws SQLException;
    
    public Team getLastTeamForPlayer(Long playerId, String language) throws SQLException;
    
    public List<Team> getLastTeamForPlayers(String playerIds, String language) throws SQLException;
    
    public Long getFixtureByGameId(Long gameId) throws SQLException;
    
    public List<Long> getFixturesByGameIds(String gameIds) throws SQLException;
    
    public List<Watchlist> getWatchlistByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public Watchlist getWatchlistById(Long watchlistId) throws SQLException;
    
    public List<WatchlistDetail> getWatchlistDetailByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<WatchlistDetail> getWatchlistDetailById(Long detailId) throws SQLException;
    
    public Long addWatchlist(Watchlist watchlist) throws SQLException;
    
    public void addWatchlistDetail(WatchlistDetail detail) throws SQLException;
    
    public void removePlayerFromWatchlist(Long playerId, Long watchlistId) throws SQLException;
    
    public void updateWatchlist(Watchlist watchlist) throws SQLException;
    
    public void deleteWatchlist(Watchlist watchlist) throws SQLException;
    
    public List<WatchlistShare> getAllWatchlistShareByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public void deleteWatchlistShare(Long watchlistId) throws SQLException;
    
    public void addWatchlistShare(Long userId, Long watchlistId, boolean editable) throws SQLException;
    
    public List<Watchlist> getWatchlistSharedWithUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<WatchlistDetail> getWatchlistDetailSharedWithUserId(Long userId, Long groupsetId) throws SQLException;
    
    public List<Favorites> getFavoritesByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public void addFavorites(Favorites favorites) throws SQLException;
    
    public void deleteFavorites(Long id) throws SQLException;
    
    public List<Shortcut> getShortcutsByUserId(Long userId, Long groupsetId) throws SQLException;
    
    public void addShortcut(Shortcut shortcut) throws SQLException;
    
    public void deleteShortcut(Long id) throws SQLException;
    
    public void deleteShortcuts(String ids) throws SQLException;
    
    public Settings getSettingsByUserId(Long userId) throws SQLException;
    
    public void addSettings(Settings settings) throws SQLException;
    
    public void updateSettings(Settings settings) throws SQLException;
    
    public List<Long> getUserIdFromVtigerAgentId(Long agentId) throws SQLException;
    
    public Long addExport(Export export) throws SQLException;
    
    public void addExportDetail(String values) throws SQLException;
    
    public void updateExport(Export export) throws SQLException;
    
    public List<Export> getExportByUser(Long userId, Long groupsetId) throws SQLException;
    
    public List<ExportDetail> getExportDetailByUser(Long userId, Long groupsetId) throws SQLException;
    
    public Long getPlayerInLineupAmount(Long playerId, Long competitionId, Long teamId, Long seasonId, Long groupsetId) throws SQLException;
    
    public Long getUserExportLimit(Long userId) throws SQLException;
    
    public Long getUserExportTimeUsed(Long userId) throws SQLException;
    
    public List<Export> getExportByUserAndData(Long userId, Long groupsetId, String from, String to) throws SQLException;
    
    public List<ExportDetail> getExportDetailByUserAndData(Long userId, Long groupsetId, String from, String to) throws SQLException;
    
    public List<Team> getCompetitionTeamGironi(Long competitionId, Long seasonId) throws SQLException;
    
    public List<VtigerAgent> getUserVtigerAgent() throws SQLException;

    public List<UserCompetitionException> getCompetitionExceptionByGroupsetId(Long groupsetId) throws SQLException;

    public Atleta getAtleta(Long playerId, String language) throws SQLException;
}
