package sics.dao.service;

import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import sics.dao.AppDAO;

import sics.domain.*;
import sics.model.Azione;

public class ManagerServiceImpl implements ManagerService {

    private AppDAO dao, daoProtezione;

    public void setAppDAO(AppDAO dao) {
        this.dao = dao;
    }

    public void setAppDAOProtezione(AppDAO dao) {
        this.daoProtezione = dao;
    }

    public List<ChartData> getStartExportData(Integer timeFilterId, Long agentId) throws SQLException {
        return dao.getStartExportData(timeFilterId, agentId);
    }

    public List<ChartData> getLicenseExpiredData(Integer timeFilterId, Long agentId) throws SQLException {
        return dao.getLicenseExpiredData(timeFilterId, agentId);
    }

    public List<ChartData> getVideoDownloadData(Integer timeFilterId, Long agentId) throws SQLException {
        return dao.getVideoDownloadData(timeFilterId, agentId);
    }

    public List<ChartData> getUserLogData(Long userId, String from, String to) throws SQLException {
        return dao.getUserLogData(userId, from, to);
    }

    public List<ChartData> getUserLast100LogData(Long userId) throws SQLException {
        return dao.getUserLast100LogData(userId);
    }

    public List<VtigerAgent> getVtigerAgents() throws SQLException {
        return dao.getVtigerAgents();
    }

    //ritorna lista con utenti
    public List getUserAll(String name, String surname, String dateFrom, String dateTo, String application) throws SQLException {
        return dao.getUserAll(name, surname, dateFrom, dateTo, application);
    }

    public List<User> getSicsTvUsers(Long timeFilterId, Long agentId) throws SQLException {
        return dao.getSicsTvUsers(timeFilterId, agentId);
    }

    public User getUser(Long id) throws SQLException {
        return dao.getUser(id);
    }

    public List<User> getUsers(List<Long> ids) throws SQLException {
        return dao.getUsers(ids);
    }

    public User getUser(String username) throws SQLException {
        User user = dao.getUser(username);
        if (user != null) {
            Pdata pdata = daoProtezione.getScadenzaFromSerialNumber(user.getSn_sicstv());
            if (pdata != null) {
                user.setDatascadenza(pdata.getDatascadenza());
            }
            // se gruppo giocatori allora carico il playerId
            if (Long.compare(user.getGroupsetId(), 457L) == 0) {
                Long playerId = dao.getPlayerIdBySerialNumber(user.getSn_sicstv());
                if (playerId != null) {
                    user.setPlayerId(playerId);
                }
            }
        }
        return user;
    }

    public User getUserByMail(String mail) throws SQLException {
        return dao.getUserByMail(mail);
    }

    public void updatePlaylist(Long id, Long idUser, String name, String note) throws SQLException {
        dao.updatePlaylist(id, idUser, name, note);
    }

    public void saveUser(User item) throws SQLException {
        dao.saveUser(item);
    }

    //Aggiunta in +
    public void saveUser2(User item) throws SQLException {
        dao.saveUser2(item);
    }

    public void delUser(Long id) throws SQLException {
        dao.delUser(id);
    }

    public List getPlaylistUser(Long idGro, Long idUse) throws SQLException {
        return dao.getPlaylistUser(idGro, idUse);
    }

    public Playlist getPlaylist(Long id, Long idUse) throws SQLException {
        return dao.getPlaylist(id, idUse);
    }

    public Playlist getPlaylistByVideoName(String videoname, Long userId) throws SQLException {
        return dao.getPlaylistByVideoName(videoname, userId);
    }

    public Game getGameByVideoName(String videoname, Long ownerUserId, String language) throws SQLException {
        return dao.getGameByVideoName(videoname, ownerUserId, language);
    }

    public void setPlaylistSeen(Long id, Long idUse) throws SQLException {
        dao.setPlaylistSeen(id, idUse);
    }

    public Long getPlaylistUnseen(Long idUse, Long idGro) throws SQLException {
        return dao.getPlaylistUnseen(idUse, idGro);
    }

    public Game getGame(Long id, String language) throws SQLException {
        return dao.getGame(id, language);
    }

    public List<Game> getGamesByFixtureIds(List<Long> ids, Long groupsetId, String language) throws SQLException {
        return dao.getGamesByFixtureIds(ids, groupsetId, language);
    }

    public Game getGameByFixtureId(Long id, String language) throws SQLException {
        return dao.getGameByFixtureId(id, language);
    }

    public List getGameByMultiFilter(Map<String, String> mapFilters, Long startFrom, Boolean source, String language) throws SQLException {
        return dao.getGameByMultiFilter(mapFilters, startFrom, source, language);
    }

    public List getCompetitionTactAllowed(Long idGro) throws SQLException {
        return dao.getCompetitionTactAllowed(idGro);
    }

    //Invoca il metodo dell' interfaccia DAO che interroga il database
    public CalendarRange getMinAndMaxMatchdays() throws SQLException {
        return dao.getMinAndMaxMatchdays();
    }

    public List getSeasonAll() throws SQLException {
        return dao.getSeasonAll();
    }

    public Season getSeasonById(Long seasonId) throws SQLException {
        return dao.getSeasonById(seasonId);
    }

    public List getSeasonAllAvailableByPlayerId(Long playerId, Long groupsetId, List<UserCompetitionException> competitionExceptions, Boolean personalSource) throws SQLException {
        return dao.getSeasonAllAvailableByPlayerId(playerId, groupsetId, competitionExceptions, personalSource);
    }

    public Season getLastSeason(Long userId, Long groupsetId) throws SQLException {
        return dao.getLastSeason(userId, groupsetId);
    }

    public FixturePlaylistSize getFixturePlaylistSize(boolean fromUser, Long userId, Long groupsetId, String idSeasons, Long minSeason) throws SQLException {
        return dao.getFixturePlaylistSize(fromUser, userId, groupsetId, idSeasons, minSeason);
    }

    public Country getInternationalCompetition(Long id, String language) throws SQLException {
        return dao.getInternationalCompetition(id, language);
    }

    public Country getCountry(Long id, String language) throws SQLException {
        return dao.getCountry(id, language);
    }

    public List getCompetitionByGroupsetId(Long idGroupset, String language) throws SQLException {
        return dao.getCompetitionByGroupsetId(idGroupset, language);
    }

    public List getCompetitionBySeasons(Long idGroupset, String seasId, String language) throws SQLException {
        return dao.getCompetitionBySeasons(idGroupset, seasId, language);
    }

    public void deleteGame(Long idFixture, Long idUser) throws SQLException {
        dao.deleteGame(idFixture, idUser);
    }

    public void deletePlaylist(Long idPlaylist, Long idUser) throws SQLException {
        dao.deletePlaylist(idPlaylist, idUser);
    }

    public Competition getCompetition(Long id, String language) throws SQLException {
        return dao.getCompetition(id, language);
    }

    public List<Competition> getCompetitions(String ids, String language) throws SQLException {
        return dao.getCompetitions(ids, language);
    }

    public List getTeamAll(String language) throws SQLException {
        return dao.getTeamAll(language);
    }

    public Team getTeam(Long id, String language) throws SQLException {
        return dao.getTeam(id, language);
    }

    public List<Team> getTeams(String ids, String language) throws SQLException {
        return dao.getTeams(ids, language);
    }

    public List getTeamByGroupsetId(Long idGroupset, String language, List<Competition> allowedCompetitions) throws SQLException {
        return dao.getTeamByGroupsetId(idGroupset, language, allowedCompetitions);
    }

    public List getTeamByMultiFilter(String seasIds, String compIds, String language) throws SQLException {
        return dao.getTeamByMultiFilter(seasIds, compIds, language);
    }

    public List getPlaylistDataAll(Long id) throws SQLException {
        return dao.getPlaylistDataAll(id);
    }

    public void addPlaylist(Playlist playlist) throws SQLException {
        dao.addPlaylist(playlist);
    }

    public List getGameDataAll(Long id) throws SQLException {
        return dao.getGameDataAll(id);
    }

    public List getLogDataAll(Long idRes) throws SQLException {
        return dao.getLogDataAll(idRes);
    }

    public List getLogDataFilter(Date dateFrom, Date dateTo, Long idVideo, String userName, String action, String application) throws SQLException {
        return dao.getLogDataFilter(dateFrom, dateTo, idVideo, userName, action, application);
    }

    public void saveLogData(LogData item) throws SQLException {
        dao.saveLogData(item);
    }

    public List getDownloadDet(Long id) throws SQLException {
        return dao.getDownloadDet(id);
    }

    public List getStreamingDet(Long id) throws SQLException {
        return dao.getStreamingDet(id);
    }

    public Long getUserDownload(Long id) throws SQLException {
        return dao.getUserDownload(id);
    }

    public Long getUserXmlJsonExport(Long userId) throws SQLException {
        return dao.getUserXmlJsonExport(userId);
    }

    public List getAtletiByGame(Long idFixture, Long idTeamH, Long idTeamA, Long groupsetId, String language) throws SQLException {
        return dao.getAtletiByGame(idFixture, idTeamH, idTeamA, groupsetId, language);
    }

    public List getAtletiSelezionati(Long idFixture, ArrayList<Long> players, Long idTeamH, Long idTeamA, String language) throws SQLException {
        return dao.getAtletiSelezionati(idFixture, players, idTeamH, idTeamA, language);
    }

    public List<Atleta> getAtletiByIds(String ids, String language) throws SQLException {
        return dao.getAtletiByIds(ids, language);
    }

    public List getAtletiByRole(Long idFixture, Long roleId, Long teamId, String language) throws SQLException {
        return dao.getAtletiByRole(idFixture, roleId, teamId, language);
    }

    public Map getFasce(Long idConfiguration) throws SQLException {
        return dao.getFasce(idConfiguration);
    }

    public Map getIntervalli() throws SQLException {
        return dao.getIntervalli();
    }

    public FilterGameWrapper readEventMatchesByEventIds(String eventIds, Map<Long, Game> games, Long sportId, Long groupsetId, Long playlistTvId, List<Competition> allowedCompetitions, User curUser, String language) throws SQLException {
        return dao.readEventMatchesByEventIds(eventIds, games, sportId, groupsetId, playlistTvId, allowedCompetitions, curUser, language);
    }

    public LinkedHashMap<String, Azione> getActionsByIdEvent(Long idFixture, ArrayList<String> idEvents, Long sportId, List<Competition> allowedCompetitions, Long userId, Long groupsetId, String language) throws SQLException {
        return dao.getActionsByIdEvent(idFixture, idEvents, sportId, allowedCompetitions, userId, groupsetId, language);
    }

    public List<Azione> getActionsByEventIds(String eventIds, Long sportId, List<Competition> allowedCompetitions, String language) throws SQLException {
        return dao.getActionsByEventIds(eventIds, sportId, allowedCompetitions, language);
    }

    public Map<String, List> getPanel(String idPanels, Long idGroupset, String lang) throws SQLException {
        return dao.getPanel(idPanels, idGroupset, lang);
    }

    public Map<String, List> getPanelByName(String name, Long idGroupset) throws SQLException {
        return dao.getPanelByName(name, idGroupset, null);
    }

    public Map<String, List> getPanelById(Long id) throws SQLException {
        return dao.getPanelById(id, null);
    }

    public Map<String, List> getPanelsByGroupset(Long idGroupset) throws SQLException {
        return dao.getPanelsByGroupset(idGroupset, null);
    }

    public List getModuli() throws SQLException {
        return dao.getModuli();
    }

    public Pdata getScadenzaFromSerialNumber(String serialnumber) throws SQLException {
        return daoProtezione.getScadenzaFromSerialNumber(serialnumber);
    }

    public Long addPlaylistTv(PlaylistTV playlistTV) throws SQLException {
        return dao.addPlaylistTv(playlistTV);
    }

    public void addPlaylistTvEvents(String values) throws SQLException {
        dao.addPlaylistTvEvents(values);
    }

    public List<PlaylistTV> getPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getPlaylistTvByUserId(userId, groupsetId);
    }

    public List<PlaylistTV> getPlaylistTvWithCounterByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getPlaylistTvWithCounterByUserId(userId, groupsetId);
    }

    public void deletePlaylistTvById(Long playlistId) throws SQLException {
        dao.deletePlaylistTvById(playlistId);
    }

    public List<PlaylistTVEvent> getPlaylistTvEventsById(Long playlistId) throws SQLException {
        return dao.getPlaylistTvEventsById(playlistId);
    }

    public List<PlaylistTVEvent> getPlaylistTvEventsDeletedById(Long playlistId) throws SQLException {
        return dao.getPlaylistTvEventsDeletedById(playlistId);
    }

    public PlaylistTV getPlaylistTvById(Long playlistId) throws SQLException {
        return dao.getPlaylistTvById(playlistId);
    }

    public void updatePlaylistTv(PlaylistTV playlistTV) throws SQLException {
        dao.updatePlaylistTv(playlistTV);
    }

    public void updatePlaylistShare(PlaylistTVShare playlistTVShare) throws SQLException {
        dao.updatePlaylistShare(playlistTVShare);
    }

    public void updatePlaylistTvEvent(PlaylistTVEvent event) throws SQLException {
        dao.updatePlaylistTvEvent(event);
    }

    public void deletePlaylistTvEvents(Long playlistId, String eventIds, Long updateBy, Timestamp updateDate, String updateDescription) throws SQLException {
        dao.deletePlaylistTvEvents(playlistId, eventIds, updateBy, updateDate, updateDescription);
    }

    public void permanentlyDeletePlaylistTvEvents(Long playlistId, String eventIds) throws SQLException {
        dao.permanentlyDeletePlaylistTvEvents(playlistId, eventIds);
    }

    public Groupset getGroupsetById(Long groupsetId) throws SQLException {
        return dao.getGroupsetById(groupsetId);
    }

    public List<User> getUserListByGroupsetId(Long groupsetId) throws SQLException {
        return dao.getUserListByGroupsetId(groupsetId);
    }

    public List<PlaylistTVShare> getAllPlaylistTVShareByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getAllPlaylistTVShareByUserId(userId, groupsetId);
    }

    public void addPlaylistTVShare(Long userId, Long playlistId, boolean editable) throws SQLException {
        dao.addPlaylistTVShare(userId, playlistId, editable);
    }

    public void deletePlaylistTVShare(Long playlistId) throws SQLException {
        dao.deletePlaylistTVShare(playlistId);
    }

    public List<PlaylistTV> getSharedPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getSharedPlaylistTvByUserId(userId, groupsetId);
    }

    public List<PlaylistTV> getSharedPlaylistTvWithCounterByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getSharedPlaylistTvWithCounterByUserId(userId, groupsetId);
    }

    public List<PlaylistTV> getSharingPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getSharingPlaylistTvByUserId(userId, groupsetId);
    }

    public PlaylistTVShare getSharedPlaylistTvByUserIdAndPlaylistId(Long userId, Long groupsetId, Long playlistId) throws SQLException {
        return dao.getSharedPlaylistTvByUserIdAndPlaylistId(userId, groupsetId, playlistId);
    }

    public List<PlaylistTVShare> getAllSharedPlaylistTvByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getAllSharedPlaylistTvByUserId(userId, groupsetId);
    }

    public PlaylistTVSharePublic getPlaylistTvSharePublicByUrl(String url) throws SQLException {
        return dao.getPlaylistTvSharePublicByUrl(url);
    }

    public PlaylistTVSharePublic getPlaylistTvSharePublicByUserIdAndPlaylistId(Long userId, Long playlistId) throws SQLException {
        return dao.getPlaylistTvSharePublicByUserIdAndPlaylistId(userId, playlistId);
    }

    public void updatePlaylistTvSharePublicClick(Long id, Long click) throws SQLException {
        dao.updatePlaylistTvSharePublicClick(id, click);
    }

    public List<PlaylistTVSharePublic> getAllPlaylistTvPublicShareByUserId(Long userId) throws SQLException {
        return dao.getAllPlaylistTvPublicShareByUserId(userId);
    }

    public void addPlaylistTvSharePublic(PlaylistTVSharePublic share) throws SQLException {
        dao.addPlaylistTvSharePublic(share);
    }

    public void removePlaylistTvSharePublic(Long id) throws SQLException {
        dao.removePlaylistTvSharePublic(id);
    }

    public Season getLastSeasonForCountryId(Long userId, Long groupsetId, Long countryId, String allowedCompetitionIds, List<UserCompetitionException> competitionExceptions) throws SQLException {
        return dao.getLastSeasonForCountryId(userId, groupsetId, countryId, allowedCompetitionIds, competitionExceptions);
    }

    public Season getLastSeasonForCountryIdWithNationalTeams(Long userId, Long groupsetId, Long countryId, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) throws SQLException {
        return dao.getLastSeasonForCountryIdWithNationalTeams(userId, groupsetId, countryId, allowedCompetitions, competitionExceptions);
    }

    public Season getLastSeasonForInternationalCompetitionId(Long userId, Long groupsetId, Long intCompId, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) throws SQLException {
        return dao.getLastSeasonForInternationalCompetitionId(userId, groupsetId, intCompId, allowedCompetitions, competitionExceptions);
    }

    public PlaylistTVEvent getPlaylistTvLastUpdateEventById(Long playlistId) throws SQLException {
        return dao.getPlaylistTvLastUpdateEventById(playlistId);
    }

    public List<Game> getGameIdsListByFilters(String from, String where) throws SQLException {
        return dao.getGameIdsListByFilters(from, where);
    }

    public List<StatsType> getAllStatsType(String language, Integer availability, String specificLanguage) throws SQLException {
        return dao.getAllStatsType(language, availability, specificLanguage);
    }

    public Long addTeamStatsFilter(TeamStatsFilter filter) throws SQLException {
        return dao.addTeamStatsFilter(filter);
    }

    public void addTeamStatsFilterDetails(List<TeamStatsFilterDetail> filterDetailList) throws SQLException {
        dao.addTeamStatsFilterDetails(filterDetailList);
    }

    public void deleteTeamStatsFilterDetails(Long statsTypeFilterId) throws SQLException {
        dao.deleteTeamStatsFilterDetails(statsTypeFilterId);
    }

    public List<TeamStatsFilter> getTeamStatsFilterByUserId(Long userId, Long groupsetId, String tableType, String language) throws SQLException {
        return dao.getTeamStatsFilterByUserId(userId, groupsetId, tableType, language);
    }

    public List<TeamStatsFilterDetail> getTeamStatsFilterDetailsByFilterId(Long filterId) throws SQLException {
        return dao.getTeamStatsFilterDetailsByFilterId(filterId);
    }

    public TeamStatsFilter getTeamStatsFilterById(Long filterId, String language) throws SQLException {
        return dao.getTeamStatsFilterById(filterId, language);
    }

    public void updateTeamStatsFilter(TeamStatsFilter filter) throws SQLException {
        dao.updateTeamStatsFilter(filter);
    }

    public void updateTeamStatsFilterPreferred(Long isPreferred, String where) throws SQLException {
        dao.updateTeamStatsFilterPreferred(isPreferred, where);
    }

    public void deleteTeamStatsFilter(TeamStatsFilter filter) throws SQLException {
        dao.deleteTeamStatsFilter(filter);
    }

    public List<TeamStats> getPlayerStatsResumeCareer(Long playerId, String language, String specificLanguage) throws SQLException {
        return dao.getPlayerStatsResumeCareer(playerId, language, specificLanguage);
    }

    public List<Atleta> getAtletiJerseyAndRoleByFilters(String seasonId, String teamId, String playerId, String language) throws SQLException {
        return dao.getAtletiJerseyAndRoleByFilters(seasonId, teamId, playerId, language);
    }

    public Position getPositionById(Long positionId, String language) throws SQLException {
        return dao.getPositionById(positionId, language);
    }

    public List<Position> getPositionByIds(String positionIds, String language) throws SQLException {
        return dao.getPositionByIds(positionIds, language);
    }

    public Team getLastTeamForPlayer(Long playerId, String language) throws SQLException {
        return dao.getLastTeamForPlayer(playerId, language);
    }

    public List<Team> getLastTeamForPlayers(String playerIds, String language) throws SQLException {
        return dao.getLastTeamForPlayers(playerIds, language);
    }

    public Long getFixtureByGameId(Long gameId) throws SQLException {
        return dao.getFixtureByGameId(gameId);
    }

    public List<Long> getFixturesByGameIds(String gameIds) throws SQLException {
        return dao.getFixturesByGameIds(gameIds);
    }

    public List<Watchlist> getWatchlistByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getWatchlistByUserId(userId, groupsetId);
    }

    public Watchlist getWatchlistById(Long watchlistId) throws SQLException {
        return dao.getWatchlistById(watchlistId);
    }

    public List<WatchlistDetail> getWatchlistDetailByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getWatchlistDetailByUserId(userId, groupsetId);
    }

    public List<WatchlistDetail> getWatchlistDetailById(Long detailId) throws SQLException {
        return dao.getWatchlistDetailById(detailId);
    }

    public Long addWatchlist(Watchlist watchlist) throws SQLException {
        return dao.addWatchlist(watchlist);
    }

    public void addWatchlistDetail(WatchlistDetail detail) throws SQLException {
        dao.addWatchlistDetail(detail);
    }

    public void removePlayerFromWatchlist(Long playerId, Long watchlistId) throws SQLException {
        dao.removePlayerFromWatchlist(playerId, watchlistId);
    }

    public void updateWatchlist(Watchlist watchlist) throws SQLException {
        dao.updateWatchlist(watchlist);
    }

    public void deleteWatchlist(Watchlist watchlist) throws SQLException {
        dao.deleteWatchlist(watchlist);
    }

    public List<WatchlistShare> getAllWatchlistShareByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getAllWatchlistShareByUserId(userId, groupsetId);
    }

    public void deleteWatchlistShare(Long watchlistId) throws SQLException {
        dao.deleteWatchlistShare(watchlistId);
    }

    public void addWatchlistShare(Long userId, Long watchlistId, boolean editable) throws SQLException {
        dao.addWatchlistShare(userId, watchlistId, editable);
    }

    public List<Watchlist> getWatchlistSharedWithUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getWatchlistSharedWithUserId(userId, groupsetId);
    }

    public List<WatchlistDetail> getWatchlistDetailSharedWithUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getWatchlistDetailSharedWithUserId(userId, groupsetId);
    }

    public List<Favorites> getFavoritesByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getFavoritesByUserId(userId, groupsetId);
    }

    public void addFavorites(Favorites favorites) throws SQLException {
        dao.addFavorites(favorites);
    }

    public void deleteFavorites(Long id) throws SQLException {
        dao.deleteFavorites(id);
    }

    public List<Shortcut> getShortcutsByUserId(Long userId, Long groupsetId) throws SQLException {
        return dao.getShortcutsByUserId(userId, groupsetId);
    }

    public void addShortcut(Shortcut shortcut) throws SQLException {
        dao.addShortcut(shortcut);
    }

    public void deleteShortcut(Long id) throws SQLException {
        dao.deleteShortcut(id);
    }

    public void deleteShortcuts(String ids) throws SQLException {
        dao.deleteShortcuts(ids);
    }

    public Settings getSettingsByUserId(Long userId) throws SQLException {
        return dao.getSettingsByUserId(userId);
    }

    public void addSettings(Settings settings) throws SQLException {
        dao.addSettings(settings);
    }

    public void updateSettings(Settings settings) throws SQLException {
        dao.updateSettings(settings);
    }

    public List<Long> getUserIdFromVtigerAgentId(Long agentId) throws SQLException {
        return dao.getUserIdFromVtigerAgentId(agentId);
    }

    public Long addExport(Export export) throws SQLException {
        return dao.addExport(export);
    }

    public void addExportDetail(String values) throws SQLException {
        dao.addExportDetail(values);
    }

    public void updateExport(Export export) throws SQLException {
        dao.updateExport(export);
    }

    public List<Export> getExportByUser(Long userId, Long groupsetId) throws SQLException {
        return dao.getExportByUser(userId, groupsetId);
    }

    public List<ExportDetail> getExportDetailByUser(Long userId, Long groupsetId) throws SQLException {
        return dao.getExportDetailByUser(userId, groupsetId);
    }

    public Long getUserExportLimit(Long userId) throws SQLException {
        return dao.getUserExportLimit(userId);
    }

    public Long getUserExportTimeUsed(Long userId) throws SQLException {
        return dao.getUserExportTimeUsed(userId);
    }

    public List<Export> getExportByUserAndData(Long userId, Long groupsetId, String from, String to) throws SQLException {
        return dao.getExportByUserAndData(userId, groupsetId, from, to);
    }

    public List<ExportDetail> getExportDetailByUserAndData(Long userId, Long groupsetId, String from, String to) throws SQLException {
        return dao.getExportDetailByUserAndData(userId, groupsetId, from, to);
    }

    public List<VtigerAgent> getUserVtigerAgent() throws SQLException {
        return dao.getUserVtigerAgent();
    }

    public List<UserCompetitionException> getCompetitionExceptionByGroupsetId(Long groupsetId) throws SQLException {
        return dao.getCompetitionExceptionByGroupsetId(groupsetId);
    }

    @Override
    public List getGameByCompetitionId(Long compId, Long seasId, Long groupId, Long startFrom, Boolean source, Long countryId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getGameByTeamId(Long compId, Long seasId, Long groupId, Long teamId, Long startFrom, Boolean source, String language, Integer limit) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public CalendarRange getMinAndMaxMatchdaysByCompetitionAndSeason(Long idSeason, Long idComp, Long countryId, Boolean personalSource) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getCompetitionByPlayer(Long seasId, Long playerId, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getCompetitionByTeamPlayer(Long seasId, Long playerId, Long teamId, Long groupsetId, Boolean personalSource, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getTeamByCompetition(Long seasId, Long compId, Long idGro, Long sportId, Boolean personalSource, String gameIds, Long countryId, List<Competition> allowedCompetitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getAtletiByTeamAndCompetition(Long idSeason, Long idTeam, Long idCompetition, Long idGroupset, Boolean personalSource, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getActionsAvailable(boolean conTag, boolean soloSenzaTag, Long teamId, Long playerId, Long seasonId, Long competitionId, String fixtureId, Long groupsetId, Boolean personalSource, String gameIds, String event, List<Competition> allowedCompetitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getTeamStatsByCompetitionId(Long competitionId, Long countryId, String gameIds, String tableType, Long filterId, Long seasonId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getPlayerStatsByCompetitionId(Long competitionId, Long countryId, String gameIds, String tableType, Long filterId, Long seasonId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getTeamStatsByCompetitionIdAndTeamId(Long competitionId, Long countryId, Long teamId, String gameIds, String tableType, Long filterId, Long seasonId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getTeamStatsByCompetitionIdAndTeamIdForPlayers(Long competitionId, Long countryId, Long teamId, String gameIds, String tableType, Long filterId, Long seasonId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getPlayerStatsByPlayerIds(String playerIds, String tableType, Long filterId, Long seasonId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getPlayerStatsByPlayerId(Long playerId, String competitions, String teams, Long seasonId, Long positionId, String statsTypeIds, Boolean averageCompetition, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getPlayerStatsByFiltersExcludedPlayer(Long playerId, String competitions, Long seasonId, Long positionId, String statsTypeIds, Boolean averageCompetition, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<Game> getModuleAmountByTeam(Long teamId, Long compId, Long seasonId) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getDefaultTeamStatsByCompetition(Long seasonId, String competitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getTeamStatsByCompetitionAndPosition(Long seasonId, String competitions, Long positionId, String statsTypeIds, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public Long getGameAmountByCompetitions(String competitionIds, Long seasonId) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<Team> getCompetitionTeamGironi(Long competitionId, Long seasonId) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public FilterGameWrapper readPlayerEventMatches(Long idPlayer, String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public FilterGameWrapper readEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public FilterGameWrapper readHighlightsEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public FilterGameWrapper readIPOEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, Long teamId, List<Competition> allowedCompetitions, Long userId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public FilterGameWrapper getActionsByType(String idGame, String event, String filter, Long idComp, Long teamId, Long playerId, Long seasonId, Long limit, Long groupsetId, Long sportId, String gameIds, Long userId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public FilterGameWrapper getActionsSingleMatchByType(String idGame, String event, String filter, Long idComp, Long teamId, Long playerId, Long seasonId, Long limit, Long groupsetId, Long sportId, List<Competition> allowedCompetitions, Long userId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getPlayerStatsByPlayerIdGroupByFixture(Long playerId, String competitions, String teams, Long seasonId, Long positionId, String statsTypeIds, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamReportRanking> getTeamRankingBySeasonAndCompetition(Long seasonId, String competitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getPlayerStatsByTeamAndCompetition(Long teamId, Long seasonId, String competitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getTeamStatsByCompetition(Long seasonId, String competitions, String statsTypeIdsForTables, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public Long getPlayerInLineupAmount(Long playerId, Long competitionId, Long teamId, Long seasonId, Long groupsetId) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getSeasonAllAvailable(Long userId, Long groupset_id, List<Competition> allowedCompetitions) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getSeasonAllAvailableByParams(Long userId, Long groupsetId, Long countryId, Long intCompId, Long competitionId, Long teamId, List<Competition> allowedCompetitions) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getGameAll(Long groupId, Long seasId, Long compId, Long teamId, Date from, Date to, String referee, Long matchdayId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getGameAllDigital(Long groupId, Long seasId, Long compId, Long teamId, Date from, Date to, String referee, Long matchdayId) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getGameBySeason(Long seasId, Long groupId, Long startFrom, Boolean source, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<Country> getInternationalCompetitionAll(Long seasId, Long idSport, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<Country> getInternationalCompetitionVisible(Long idGroupset, Long seasId, Long idSport, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<Country> getCountryAll(Long seasId, Long idSport, Long idGroupset, String allowedCompetitionIds, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<Country> getBaseCountry(Long seasId, Long idSport, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<Country> getCountryVisible(Long idGroupset, Long seasId, Long idSport, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getCompetitionAll(Long idGroupset, Long seasId, Boolean personalSource, Long idSport, List<Competition> allowedCompetitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getCompetitionByCountry(Long idGroupset, Long seasId, Boolean personalSource, Long idSport, String language, Long idCountry, Long idInternationalCompetition, List<Competition> allowedCompetitions) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<Competition> getInternationalCompetitionByCountry(Long idGroupset, Long seasId, Long idSport, String language, Long idCountry) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getCompetitionByTeam(Long idGroupset, Long seasId, Long teamId, Boolean source, List<Competition> allowedCompetitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getNationalTeam(Long idSport, Long idSeason, Long idCountry, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getNationalTeamByGroupsetId(Long idGroupset, Long idSeason, String language, Long idCountry, List<Competition> allowedCompetitions) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getTeamByPattern(String pattern, Long sportId, Long seasonId, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getCompetitionByPattern(String pattern, Long sportId, Long seasonId, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<Country> getCountryByPattern(String pattern, Long sportId, Long seasonId, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getGameMulti(Long compId, Long seasId, Long groupId, Long teamId, Long dayId, Boolean source, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public Atleta getAtletaById(Long seasonId, Long playerId, Long teamId, Long groupsetId, Boolean personalSource, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getAtletiByTeam(Long idSeason, Long idTeam, Long idGroupset, Boolean personalSource, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getAtletiByPattern(String pattern, Long sportId, Long seasonId, Long groupsetId, Boolean personalSource) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getPlayerStatsByFilters(String competitions, Long seasonId, Long positionId, String statsTypeIds, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getTeamPlayerAmountByCompetition(Long seasonId, String competitions, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public TeamStats getCompetitionMinutesPlayed(Long seasonId, String competitions) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List getSeasonAllAvailableByPlayerId(Long playerId) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public Season getLastSeasonForCountryId(Long userId, Long groupsetId, Long countryId, String allowedCompetitionIds) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public Season getLastSeasonForCountryIdWithNationalTeams(Long userId, Long groupsetId, Long countryId, List<Competition> allowedCompetitions) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public Season getLastSeasonForInternationalCompetitionId(Long userId, Long groupsetId, Long intCompId, List<Competition> allowedCompetitions) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<StatsType> getAllStatsType(String language, Integer availability) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<TeamStats> getPlayerStatsResumeCareer(Long playerId, String language) throws SQLException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    public Atleta getAtleta(Long playerId, String language) throws SQLException {
        return dao.getAtleta(playerId, language);
    }
}
