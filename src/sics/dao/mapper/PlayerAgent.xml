<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="PlayerAgent">

    <resultMap id="resultMapPlayerAgent"        type="PlayerAgent">
        <result property="id"                   column="id"/>
        <result property="name"                 column="name"/>
        <result property="street"               column="street"/>
        <result property="location"             column="location"/>
        <result property="countryId"            column="country_id"/>
        <result property="countryName"          column="country_name"/>
        <result property="countryLogo"          column="country_logo"/>
        <result property="phone"                column="phone"/>
        <result property="email"                column="email"/>
        <result property="website"              column="website"/>
        <result property="photo"                column="photo"/>
        
        <result property="playerAmount"         column="player_amount"/>
    </resultMap>
	
    <select id="getPlayerAgents" resultMap="resultMapPlayerAgent" parameterType="java.util.TreeMap">
        SELECT player_agent.id, player_agent.name, player_agent.street, player_agent.location, player_agent.country_id,
        (CASE WHEN country.name${language} IS NULL THEN country.name_it ELSE country.name${language} END) country_name, country.logo country_logo,
        player_agent.phone, player_agent.email, player_agent.website, player_agent.photo, COUNT(DISTINCT player.id) player_amount
        FROM player_agent
        LEFT JOIN country ON country.id = player_agent.country_id
        INNER JOIN player ON player.player_agent_id = player_agent.id
        WHERE player_agent.id != 1
        GROUP BY player_agent.id
        <if test="sort==1">
            ORDER BY player_amount DESC
        </if>
        <if test="sort==2">
            ORDER BY name
        </if>
    </select>
        
    <select id="getPlayerAgent" resultMap="resultMapPlayerAgent" parameterType="java.util.TreeMap">
        SELECT player_agent.id, player_agent.name, player_agent.street, player_agent.location, player_agent.country_id,
        (CASE WHEN country.name${language} IS NULL THEN country.name_it ELSE country.name${language} END) country_name, country.logo country_logo,
        player_agent.phone, player_agent.email, player_agent.website, player_agent.photo
        FROM player_agent
        LEFT JOIN country ON country.id = player_agent.country_id
        WHERE player_agent.id = ${agentId}
    </select>
    
    <update id="updatePlayerAgentPhoto" parameterType="java.util.TreeMap">
        UPDATE player_agent SET photo = "${photo}"
        WHERE id = ${agentId}
    </update>
    
    <update id="updatePlayerAgentDetails" parameterType="java.util.TreeMap">
        UPDATE player_agent SET name = CASE WHEN "${name}" = "" THEN NULL ELSE "${name}" END,
        street = CASE WHEN "${street}" = "" THEN NULL ELSE "${street}" END,
        location = CASE WHEN "${location}" = "" THEN NULL ELSE "${location}" END,
        phone = CASE WHEN "${phone}" = "" THEN NULL ELSE "${phone}" END,
        email = CASE WHEN "${email}" = "" THEN NULL ELSE "${email}" END,
        website = CASE WHEN "${website}" = "" THEN NULL ELSE "${website}" END,
        country_id = ${countryId}
        WHERE id = ${agentId}
    </update>
	
</mapper>