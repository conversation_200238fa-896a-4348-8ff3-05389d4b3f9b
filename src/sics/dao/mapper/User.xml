<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="User">

    <resultMap id="resultMapUser" type="User">
        <id property="id"		column="user_id"/>
        <result property="username"  column="webusername" />
        <result property="firstName" column="first_name" />
        <result property="lastName"	column="last_name" />
        <result property="role"		column="rolecode" />
        <result property="email"	 column="email" />
        <result property="password"	 column="webpassword" />
        <result property="groupsetId" column="groupset_id" />
        <result property="groupsetTeamId" column="team_id" />
        <result property="groupsetName" column="name" />
        <result property="maxDownload" column="max_download" />
        <result property="numDownload" column="num"/>
        <result property="numStreaming" column="num2"/> 
        <result property="savedSeason" column="last_season_id" />
        <result property="sn_sicstv" column="sn_sicstv" />
        <result property="sportId" column="sport_id" />
        <result property="levels" column="levels" />
        <result property="multi_login" column="multi_login" />
        <result property="tvLimitAPI" column="tv_limit_api" />
        <result property="language" column="language" />
        <result property="playerId"     column="player_id" />
        
        <result property="totalLogin" column="total_login" />
        <result property="totalAction" column="total_action" />
        <result property="lastLogin" column="last_login" />
        <result property="vtigerOrganization" column="organization_name" />
    </resultMap>

    <sql id="selectUser">
        user.id user_id, 
        user.webusername, 
        user.webpassword, 
        user.first_name, 
        user.last_name, 
        user.email,
        user.last_season_id,
        role.code rolecode,
        user.groupset_id, 
        groupset.name,
        groupset.team_id,
        groupset.max_download,
        user.sn_sicstv,
        groupset.sport_id sport_id,
        user.multi_login,
        GROUP_CONCAT(level.code) AS levels,
        user.tv_limit_api,
        user.language,
        pdata.datascadenza
        FROM user
        INNER JOIN role on role.id = user.role_id 
        INNER JOIN groupset on groupset.id= user.groupset_id
        INNER JOIN groupset_level ON groupset.id=groupset_level.groupset_id
        INNER JOIN level ON groupset_level.level_id=level.id
        INNER JOIN sics_protezione.pdata pdata ON pdata.sn = user.sn_sicstv
    </sql>

    <select id="getUserCloudSize" resultType="java.lang.Long" parameterType="java.lang.Long">
        select user.cloud_size
        from user
        where user.id=#{value}
    </select>
    
    <select id="getGroupsetCloudSize" resultType="java.lang.Long" parameterType="java.lang.Long">
        select groupset.cloud_size
        from groupset
        where groupset.id=#{value}
    </select>
    
    <select id="isGroupsetGuest" resultType="java.lang.Long" parameterType="java.lang.Long">
        select groupset.guest
        from groupset
        where groupset.id=#{value}
    </select>
    
    <select id="getFixturePlaylistSizeByUser" resultType="sics.domain.FixturePlaylistSize" parameterType="java.util.TreeMap">
        SELECT MAX(fixturePlaylistStat.totFixtureSize) AS fixtureSize,MAX(fixturePlaylistStat.totFixture) AS numFixture,MAX(fixturePlaylistStat.totPlaylistSize) AS playlistSize,MAX(fixturePlaylistStat.totPlaylist) AS numPlaylist
        FROM (
        SELECT SUM(size_video) AS totFixtureSize,COUNT(size_video) AS totFixture,0 AS totPlaylistSize,0 AS totPlaylist
        FROM fixture
        INNER JOIN game ON fixture.id=game.fixture_id
        WHERE game.groupset_id=${groupsetId}
        AND fixture.deleted=0 
        AND fixture.provider_id=3
        AND fixture.owner_user_id=${userId} AND fixture.video_name IS NOT NULL<if test="source==true"> AND fixture.season_id IN (${idSeasons})</if>
        UNION
        SELECT 0 AS totFixtureSize, 0 AS totFixture,SUM(size_video) AS totPlaylistSize,COUNT(size_video) AS totPlaylist
        FROM playlist
        WHERE groupset_id=${groupsetId} AND (uploaded_by=${userId})<if test="source==true"> AND playlist.date >= ${minDate}</if>
        AND deleted=0 
        AND uploaded_by=${userId}) AS fixturePlaylistStat
    </select>
    
    <select id="getFixturePlaylistSizeByGroupset" resultType="sics.domain.FixturePlaylistSize" parameterType="java.util.TreeMap">
        SELECT MAX(fixturePlaylistStat.totFixtureSize) AS fixtureSize,MAX(fixturePlaylistStat.totFixture) AS numFixture,MAX(fixturePlaylistStat.totPlaylistSize) AS playlistSize,MAX(fixturePlaylistStat.totPlaylist) AS numPlaylist
        FROM (
        SELECT SUM(size_video) AS totFixtureSize,COUNT(size_video) AS totFixture,0 AS totPlaylistSize,0 AS totPlaylist
        FROM fixture
        INNER JOIN game ON fixture.id=game.fixture_id
        WHERE game.groupset_id=${groupsetId}
        AND fixture.deleted=0 
        AND fixture.provider_id=3 AND fixture.video_name IS NOT NULL<if test="source==true"> AND fixture.season_id IN (${idSeasons})</if>
        UNION
        SELECT 0 AS totFixtureSize, 0 AS totFixture,SUM(size_video) AS totPlaylistSize,COUNT(size_video) AS totPlaylist
        FROM playlist
        WHERE groupset_id=${groupsetId} AND (public=${groupsetId} OR uploaded_by=${userId})<if test="source==true"> AND playlist.date >= ${minDate}</if>
        AND deleted=0) AS fixturePlaylistStat
    </select>

    <sql id="selectUserAdmin">
        select 
        count(case when logdata.action = "video_download" then 1 else null end) as num,
        count(case when logdata.action = "video_stream" then 1 else null end) as num2,
        user.id user_id,
        user.webusername, 
        user.webpassword, 
        user.first_name, 
        user.last_name, 
        user.email, 
        role.code rolecode,
        user.groupset_id, 
        groupset.name,
        groupset.max_download,
        user.multi_login,
        GROUP_CONCAT(level.code) AS levels
        from user
        inner join role on role.id = user.role_id 
        inner join groupset on groupset.id= user.groupset_id
        left join logdata on user.id = logdata.user_id
        INNER JOIN groupset_level ON groupset.id=groupset_level.groupset_id
        INNER JOIN level ON groupset_level.level_id=level.id
    </sql>

    <sql id="select">
        select <include refid="selectUser"/>
    </sql>
	
    <!-- Ritorna tutti gli utenti -->
    <select id="getUserAllAdmin" resultMap="resultMapUser" parameterType="java.util.TreeMap">
        <include refid="selectUserAdmin"/>
        ${userFilter}
        group by user.id
    </select>

    <!-- Ritorna tutti gli utenti aia -->
    <select id="getUserAllAia" resultMap="resultMapUser">
        <include refid="select"/>
        where user.active=1 and role.code = "ROLE_REF" and user.sn_sicstv LIKE "%600"
    </select>

    <!-- Ritorna tutti gli utenti -->
    <select id="getUserAll" resultMap="resultMapUser" parameterType="java.util.TreeMap">
        <include refid="select"/> 
        ${userFilter}
        order by user_id 
    </select>
    
    <select id="getUserListByGroupsetId" resultMap="resultMapUser" parameterType="java.util.TreeMap">
        SELECT user.id user_id, 
        user.webusername, 
        user.webpassword, 
        user.first_name, 
        user.last_name, 
        user.email
        FROM user
        INNER JOIN sics_protezione.pdata pdata ON pdata.sn = user.sn_sicstv
        WHERE ${filter}
        order by user_id 
    </select>
		
    <select id="getUser" resultMap="resultMapUser" parameterType="java.lang.Long">
        <include refid="select"/>
        where user.active=1 and user.id=#{value}  and user.sn_sicstv LIKE "%600"
    </select>
    
    <select id="getUsers" resultMap="resultMapUser" parameterType="java.util.TreeMap">
        SELECT user.id user_id, 
        user.webusername, 
        user.webpassword, 
        user.first_name, 
        user.last_name, 
        user.email,
        user.last_season_id,
        role.code rolecode,
        user.groupset_id, 
        groupset.name,
        groupset.team_id,
        groupset.max_download,
        user.sn_sicstv,
        groupset.sport_id sport_id,
        user.multi_login,
        pdata.datascadenza,
        user.tv_limit_api,
        user.language
        FROM user
        INNER JOIN role on role.id = user.role_id 
        INNER JOIN groupset on groupset.id = user.groupset_id
        INNER JOIN sics_protezione.pdata pdata ON pdata.sn = user.sn_sicstv
        where user.active = 1 and user.id IN(${ids}) and user.sn_sicstv LIKE "%600"
    </select>
    
    <select id="getSicsTvUsers" resultMap="resultMapUser" parameterType="java.util.TreeMap">
        SELECT user.id user_id, 
        user.webusername, 
        user.webpassword, 
        user.first_name, 
        user.last_name, 
        user.email,
        user.last_season_id,
        role.code rolecode,
        user.groupset_id, 
        groupset.name,
        groupset.team_id,
        groupset.max_download,
        user.sn_sicstv,
        groupset.sport_id sport_id,
        user.multi_login,
        pdata.datascadenza,
        user.tv_limit_api,
        user.language,
        logincount.total_login,
        actioncount.total_action,
        lastlogin.date last_login
        FROM user
        INNER JOIN role ON role.id = user.role_id 
        INNER JOIN groupset ON groupset.id = user.groupset_id
        INNER JOIN sics_protezione.pdata pdata ON pdata.sn = user.sn_sicstv
        LEFT JOIN (
            SELECT user_id, COUNT(*) total_login
            FROM logdata
            WHERE ACTION = "LOGIN_CORRECT" AND application = "sicstv"
            <if test="timeFilterId == 1">
                AND logdata.date >= NOW() - INTERVAL 1 DAY
            </if>
            <if test="timeFilterId == 2">
                AND logdata.date >= NOW() - INTERVAL 1 WEEK
            </if>
            <if test="timeFilterId == 3">
                AND logdata.date >= NOW() - INTERVAL 1 MONTH
            </if>
            <if test="timeFilterId == 4">
                AND logdata.date >= NOW() - INTERVAL 1 YEAR
            </if>
            GROUP BY user_id
        ) logincount ON logincount.user_id = user.id
        LEFT JOIN (
            SELECT user_id, COUNT(*) total_action
            FROM logdata
            WHERE application = "sicstv"
            AND action NOT IN("SESSION_DESTROYED")
            <if test="timeFilterId == 1">
                AND logdata.date >= NOW() - INTERVAL 1 DAY
            </if>
            <if test="timeFilterId == 2">
                AND logdata.date >= NOW() - INTERVAL 1 WEEK
            </if>
            <if test="timeFilterId == 3">
                AND logdata.date >= NOW() - INTERVAL 1 MONTH
            </if>
            <if test="timeFilterId == 4">
                AND logdata.date >= NOW() - INTERVAL 1 YEAR
            </if>
            GROUP BY user_id
        ) actioncount ON actioncount.user_id = user.id
        LEFT JOIN (
            SELECT user_id, MAX(DATE) DATE
            FROM logdata
            WHERE ACTION = "LOGIN_CORRECT" AND application = "sicstv"
            <if test="timeFilterId == 1">
                AND logdata.date >= NOW() - INTERVAL 1 DAY
            </if>
            <if test="timeFilterId == 2">
                AND logdata.date >= NOW() - INTERVAL 1 WEEK
            </if>
            <if test="timeFilterId == 3">
                AND logdata.date >= NOW() - INTERVAL 1 MONTH
            </if>
            <if test="timeFilterId == 4">
                AND logdata.date >= NOW() - INTERVAL 1 YEAR
            </if>
            GROUP BY user_id
        ) lastlogin ON lastlogin.user_id = user.id
        WHERE user.active = 1 AND user.sn_sicstv LIKE "%600"
        AND pdata.datascadenza >= NOW() - INTERVAL 1 YEAR
        <if test="userIdFilter != null">
            AND user.id IN (${userIdFilter})
        </if>
        GROUP BY user.id
        ORDER BY user.first_name, user.last_name
    </select>

    <select id="getUserByUsername" resultMap="resultMapUser" parameterType="java.lang.String">
        <include refid="select"/>
        where user.active=1 and user.webusername=#{value} and user.sn_sicstv LIKE "%600"
    </select>
    
    <select id="getUserBySer" resultMap="resultMapUser" parameterType="java.lang.String">
        <include refid="select"/>
        where user.active=1 and user.ser=#{value} and user.sn_sicstv LIKE "%600"
    </select>

    <select id="getUserByMail" resultMap="resultMapUser" parameterType="java.lang.String">
        <include refid="select"/>
        where user.active=1 and user.email=#{value} and not user.webusername is null and user.sn_sicstv LIKE "%600"
    </select>
    
    <select id="getPlayerIdBySerialNumber" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT cf_1018
        FROM vtiger.vtiger_assets
        INNER JOIN vtiger.vtiger_contactscf ON vtiger_contactscf.contactid = vtiger_assets.contact
        INNER JOIN vtiger.vtiger_assetscf ON vtiger_assetscf.assetsid = vtiger_assets.assetsid
        WHERE serialnumber = '${serialNumber}' AND vtiger_assetscf.cf_868 > NOW()
    </select>

    <insert id="addUser" parameterType="User">
        insert into user
        (id,webusername,webpassword,email)
        values
        (#{id},#{username},#{password},#{email})
    </insert>

    <!-- Aggiornamento nome utente -->
    <insert id="updUserName" parameterType="User">
        update user set
        first_name = #{firstName},
        last_name = #{lastName},
        email = #{email}
        where id = #{id}
    </insert>

    <insert id="updUser" parameterType="User">
        update user set
        webusername = #{username},
        webpassword = #{password},
        email = #{email},
        last_season_id = #{savedSeason}
        where id = #{id}
    </insert>

    <insert id="delUser" parameterType="java.lang.Long">
        delete from user where user.id=#{value}
    </insert>

</mapper>
