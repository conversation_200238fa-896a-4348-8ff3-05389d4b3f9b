<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Azione">

    <resultMap id="resultMapTag" type="Tags">
        <result property="_id"  column="idTagType" />
        <result property="code"  column="tagType" />
        <result property="desc"  column="tagName" />
        <result property="endesc"  column="tagNameEn" />
        <result property="frdesc"  column="tagNameFr" />
        <result property="esdesc"  column="tagNameEs" />
        <result property="rudesc"  column="tagNameRu" />
        <result property="eventTypeId"  column="eventTypeId" />
    </resultMap>

    <select id="selectButtonTags" resultMap="resultMapTag" >
        SELECT tag_type.id  idTagType, 
        tag_type.code  tagType, 
        tag_type.desc_it tagName, 
        tag_type.desc_en tagNameEn,
        tag_type.desc_fr tagNameFr,
        tag_type.desc_es tagNameEs,
        tag_type.desc_ru tagNameRu
        FROM tag_type INNER JOIN event_type ON tag_type.event_type_id = event_type.id
        WHERE event_type.id = #{id}
    </select>
    
    <select id="getGroupsetEventType" resultMap="resultMapTag">
        SELECT event_type.id idTagType, event_type.code tagType
        FROM event_type
        INNER JOIN panel ON panel.id = event_type.panel_id
        WHERE (panel.name = "${panelName}.xml" OR panel.name = "SICS_TV_PERSONAL.xml") AND event_type.groupset_id = ${groupsetId} AND panel.groupset_id = ${groupsetId}
    </select>
    
    <select id="getGroupsetTagType" resultMap="resultMapTag">
        SELECT tag_type.id idTagType, tag_type.code tagType
        FROM tag_type
        WHERE event_type_id IN(
        SELECT event_type.id FROM event_type INNER JOIN panel ON panel.id = event_type.panel_id WHERE (panel.name = "${panelName}.xml" OR panel.name = "SICS_TV_PERSONAL.xml") AND panel.groupset_id = ${groupsetId} AND event_type.groupset_id = ${groupsetId}
        ) AND groupset_id = ${groupsetId}
    </select>
    
    <select id="selectButtonTagsGrouped" resultMap="resultMapTag" parameterType="java.util.TreeMap">
        SELECT tag_type.id  idTagType, 
        tag_type.code  tagType, 
        tag_type.desc_it tagName, 
        tag_type.desc_en tagNameEn,
        tag_type.desc_fr tagNameFr,
        tag_type.desc_es tagNameEs,
        tag_type.desc_ru tagNameRu,
        event_type.id eventTypeId
        FROM tag_type INNER JOIN event_type ON tag_type.event_type_id = event_type.id
        WHERE event_type.id IN(${id})
    </select>
	
    <resultMap id="resultMapAzione" type="Azione">
        <result property="idFixture"  column="idfixture" />
        <result property="competitionId"  column="competitionId" />
        <result property="_idTeam"  column="idteam" />
        <result property="mIdEvent"  column="idxmlevent" />
        <result property="_id"  column="eventid" />
        <result property="_idType"  column="idEventType" />
        <result property="mType"  column="eventType" />
        <result property="mTagCode"  column="tag_code" />
        <result property="mHalf"  column="half" />
        <result property="mStart"  column="startms" />
        <result property="mEnd"  column="endms" />
        <result property="mTactStart"  column="starttactms" />
        <result property="mHomeTeam"  column="hometeam" />
        <result property="mAwayTeam"  column="awayteam" />
        <result property="mIdHomeTeam"  column="hometeamid" />
        <result property="mIdAwayTeam"  column="awayteamid" />
        <result property="mNote"  column="note" />
        <result property="_period_start_minute"  column="startmin" />
        <result property="_period_start_second"  column="startsec" />
        <result property="_period_end_minute"  column="endmin" />
        <result property="_period_end_second"  column="endsec" />
        <result property="mResult"  column="esito" />
        <result property="posStartString"  column="coordinate_start" />
        <result property="posEndString"  column="coordinate_end" />
        <result property="pos3DString"  column="coordinate_height" />
        <result property="playerId"  column="idplayer" />
        <result property="playerToId"  column="idplayerto" />
        <result property="mTeam" column="teamName" />
        <result property="config" column="panelname" />
        <result property="videoName" column="videoname" />
        <result property="desc" column="eventDescType" />
        <result property="descEn" column="eventDescTypeEn" />
        <result property="descFr" column="eventDescTypeFr" />
        <result property="descEs" column="eventDescTypeEs" />
        <result property="descRu" column="eventDescTypeRu" />
        <result property="hdAvailable" column="hd" />
        <result property="user" column="author" />
        <result property="groupsetId" column="groupset" />
        <result property="countAction" column="numAction" />
        <result property="isTactical" column="is_tactical" />
        
        <result property="tagTypeId" column="idTagType"/>
        <result property="tagTypeCode" column="tagType"/>
        <result property="tagNameIt" column="tagName"/>
        <result property="tagNameEn" column="tagNameEn"/>
        <!--<collection property="mTags" resultMap="resultMapTag" />-->
    </resultMap>
	
    <!-- 
        manca gestione di:
            note,rilevante,user,role_id,durataAzione,tempoAzioneVideo
    -->
	
    <sql id="selectAzione">
        event.fixture_id  idfixture,
        fixture.competition_id competitionId,
        event.team_id  idteam,
        event.xml_idevent idxmlevent,
        event.id eventid, 
        event_type.id  idEventType, 
        event_type.code  eventType, 
        event.period_id  half, 
        period_start_msec  startms, 
        period_end_msec  endms, 
        tactical_start_msec starttactms,
        period_start_minute  startmin, 
        period_start_second  startsec, 	
        period_end_minute  endmin, 
        period_end_second  endsec,
        event.result  esito, 
        coordinate_start, 
        coordinate_end, 
        coordinate_height, 
        tag_type.id  idTagType, 
        tag_type.code  tagType, 
        tag_type.desc_it tagName,
        tag_type.desc_en tagNameEn,
        tag_type.desc_fr tagNameFr,
        tag_type.desc_es tagNameEs,
        tag_type.desc_ru tagNameRu,
        event_player.player_id  idplayer,
        event_player.playerto_id  idplayerto, 
        tHome.id hometeamid,
        tAway.id awayteamid,
        event.note note,
        panel.name  panelname,
        fixture.video_name videoname,
        event_type.desc_it eventDescType,
        event_type.desc_en eventDescTypeEn,
        event_type.desc_fr eventDescTypeFr,
        event_type.desc_es eventDescTypeEs,
        event_type.desc_ru eventDescTypeRu,
        CASE WHEN IFNULL(fixture.video_quality, 0) > 0 THEN 1 ELSE 0 END hd,
        event.author author,
        event.groupset_id  groupset
        FROM ${database}event
        INNER JOIN ${database}fixture ON fixture.id=event.fixture_id 
        LEFT JOIN event_type ON event.event_type_id=event_type.id 
        LEFT JOIN panel ON event_type.panel_id = panel.id
        LEFT JOIN ${database}event_tag ON event.id=event_tag.event_id 
        LEFT JOIN tag_type ON event_tag.tag_type_id=tag_type.id 
        LEFT JOIN ${database}event_player ON event.id=event_player.event_id 
        INNER JOIN team ON team.id = CASE WHEN event.team_id = fixture.home_team_id THEN fixture.home_team_id ELSE fixture.away_team_id END 
        INNER JOIN team tHome ON tHome.id = fixture.home_team_id 
        INNER JOIN team tAway ON tAway.ID = fixture.away_team_id 
    </sql>
    
    <sql id="selectAzioneBasket">
        event.fixture_id  idfixture, 
        fixture.competition_id competitionId,
        event.team_id  idteam,
        event.xml_idevent idxmlevent,
        event.id eventid, 
        event_type.id  idEventType, 
        event_type.code  eventType, 
        event.period_id  half, 
        period_start_msec  startms, 
        period_end_msec  endms, 
        tactical_start_msec starttactms,
        period_start_minute  startmin, 
        period_start_second  startsec, 	
        period_end_minute  endmin, 
        period_end_second  endsec, 
        event_result.result  esito, 
        coordinate_start, 
        coordinate_end, 
        coordinate_height, 
        tag_type.id  idTagType, 
        tag_type.code  tagType, 
        tag_type.desc_it tagName,
        tag_type.desc_en tagNameEn,
        tag_type.desc_fr tagNameFr,
        tag_type.desc_es tagNameEs,
        tag_type.desc_ru tagNameRu,
        event_player.player_id  idplayer,
        event_player.playerto_id  idplayerto, 
        panel.name  panelname,
        fixture.video_name videoname,
        event_type.desc_it eventDescType,
        event_type.desc_en eventDescTypeEn,
        event_type.desc_fr eventDescTypeFr,
        event_type.desc_es eventDescTypeEs,
        event_type.desc_ru eventDescTypeRu,
        CASE WHEN IFNULL(fixture.video_quality, 0) > 0 THEN 1 ELSE 0 END hd,
        event.author author,
        event.groupset_id  groupset  
        FROM ${database}event LEFT JOIN ${database}fixture ON fixture.id=event.fixture_id 
        LEFT JOIN event_type ON event.event_type_id=event_type.id 
        LEFT JOIN panel ON event_type.panel_id = panel.id
        LEFT JOIN event_tag ON event.id=event_tag.event_id 
        LEFT JOIN tag_type ON event_tag.tag_type_id=tag_type.id 
        LEFT JOIN event_player ON event.id=event_player.event_id 
        LEFT JOIN player p1 ON p1.id = event_player.player_id
        LEFT JOIN player p2 ON p2.id = event_player.playerto_id
        LEFT JOIN team ON team.id = CASE WHEN event.team_id = fixture.home_team_id THEN fixture.home_team_id ELSE fixture.away_team_id END
        LEFT JOIN event_result ON event.id = event_result.event_id
    </sql>
    
    <sql id="selectAzioneCount">
        event.fixture_id  idfixture, 
        fixture.competition_id competitionId,
        event.team_id  idteam,
        event.xml_idevent idxmlevent,
        event.id eventid, 
        event_type.id  idEventType, 
        event_type.code  eventType, 
        event.period_id  half, 
        period_start_msec  startms, 
        period_end_msec  endms, 
        tactical_start_msec starttactms,
        period_start_minute  startmin, 
        period_start_second  startsec, 	
        period_end_minute  endmin, 
        period_end_second  endsec, 
        event.result  esito, 
        coordinate_start, 
        coordinate_end, 
        coordinate_height, 
        tag_type.id  idTagType, 
        tag_type.code  tagType, 
        tag_type.desc_it tagName,
        tag_type.desc_en tagNameEn,
        tag_type.desc_fr tagNameFr,
        tag_type.desc_es tagNameEs,
        tag_type.desc_ru tagNameRu,
        event_player.player_id  idplayer,
        event_player.playerto_id  idplayerto, 
        tHome.id hometeamid,
        tAway.id awayteamid,
        event.note note,
        panel.name  panelname,
        fixture.video_name videoname,
        event_type.desc_it eventDescType,
        event_type.desc_en eventDescTypeEn,
        event_type.desc_fr eventDescTypeFr,
        event_type.desc_es eventDescTypeEs,
        event_type.desc_ru eventDescTypeRu,
        CASE WHEN IFNULL(fixture.video_quality, 0) > 0 THEN 1 ELSE 0 END hd,
        event.author author,
        event.groupset_id  groupset,
        COUNT(*) numAction
        FROM ${database}event
        INNER JOIN ${database}fixture ON fixture.id=event.fixture_id 
        INNER JOIN game ON game.fixture_id = fixture.id 
        LEFT JOIN event_type ON event.event_type_id=event_type.id 
        LEFT JOIN panel ON event_type.panel_id = panel.id
        LEFT JOIN event_tag ON event.id=event_tag.event_id 
        LEFT JOIN tag_type ON event_tag.tag_type_id=tag_type.id 
        LEFT JOIN event_player ON event.id=event_player.event_id 
        INNER JOIN team ON team.id = CASE WHEN event.team_id = fixture.home_team_id THEN fixture.home_team_id ELSE fixture.away_team_id END 
        INNER JOIN team tHome ON tHome.id = fixture.home_team_id 
        INNER JOIN team tAway ON tAway.ID = fixture.away_team_id 
    </sql>
    
    <sql id="selectAzioneLimited">
        tb1.idfixture, 
        tbl.competition_id,
        tb1.idteam,
        tb1.idxmlevent,
        tb1.eventid, 
        tb1.idEventType, 
        tb1.eventType, 
        tb1.half, 
        tb1.startms, 
        tb1.endms,
        tb1.starttactms, 
        tb1.startmin, 
        tb1.startsec, 	
        tb1.endmin, 
        tb1.endsec, 
        tb1.esito, 
        tb1.coordinate_start, 
        tb1.coordinate_end, 
        tb1.coordinate_height, 
        tb1.idTagType,
        tb1.tagType,
        tb1.tagName,
        tb1.tagNameEn,
        tb1.idplayer,
        tb1.idplayerto, 
        tb1.hometeam,
        tb1.awayteam,
        tb1.hometeamid,
        tb1.awayteamid,
        tb1.note,
        tb1.teamName,
        tb1.panelname,
        tb1.videoname,
        tb1.eventDescType,
        tb1.eventDescTypeEn,
        tbl.eventDescTypeFr,
        tbl.eventDescTypeEs,
        tbl.eventDescTypeRu,
        tb1.hd,
        tb1.author,
        tb1.groupset,
        tb1.tag_code,
        COUNT(*) numAction 
        FROM (SELECT 
    </sql>
    
    <sql id="selectInner">
        event.fixture_id  idfixture, 
        fixture.competition_id competitionId,
        event.team_id  idteam,
        event.xml_idevent idxmlevent,
        event.id eventid, 
        event_type.id  idEventType, 
        event_type.code  eventType, 
        event.period_id  half, 
        period_start_msec  startms, 
        period_end_msec  endms, 
        tactical_start_msec starttactms,
        period_start_minute  startmin, 
        period_start_second  startsec, 	
        period_end_minute  endmin, 
        period_end_second  endsec, 
        event.result  esito, 
        coordinate_start, 
        coordinate_end, 
        coordinate_height, 
        tag_type.id  idTagType, 
        tag_type.code  tagType, 
        tag_type.desc_it tagName,
        tag_type.desc_en tagNameEn,
        tag_type.desc_fr tagNameFr,
        tag_type.desc_es tagNameEs,
        tag_type.desc_ru tagNameRu,
        event_player.player_id  idplayer,
        event_player.playerto_id  idplayerto,
        tHome.id hometeamid,
        tAway.id awayteamid,
        event.note note,
        panel.name  panelname,
        fixture.video_name videoname,
        event_type.desc_it eventDescType,
        event_type.desc_en eventDescTypeEn,
        event_type.desc_fr eventDescTypeFr,
        event_type.desc_es eventDescTypeEs,
        event_type.desc_ru eventDescTypeRu,
        CASE WHEN IFNULL(fixture.video_quality, 0) > 0 THEN 1 ELSE 0 END hd,
        event.author author,
        event.groupset_id  groupset,
        tag_type.code tag_code
        FROM ${database}event
        INNER JOIN ${database}fixture ON fixture.id=event.fixture_id 
        LEFT JOIN event_type ON event.event_type_id=event_type.id 
        LEFT JOIN panel ON event_type.panel_id = panel.id 
        LEFT JOIN event_tag ON event.id=event_tag.event_id 
        LEFT JOIN tag_type ON event_tag.tag_type_id=tag_type.id 
        LEFT JOIN event_player ON event.id=event_player.event_id 
        INNER JOIN team ON team.id = CASE WHEN event.team_id = fixture.home_team_id THEN fixture.home_team_id ELSE fixture.away_team_id END 
        INNER JOIN team tHome ON tHome.id = fixture.home_team_id 
        INNER JOIN team tAway ON tAway.ID = fixture.away_team_id 
    </sql>
    
    <sql id="selectAzioneMinimalist">
        tb1.idEventType,
        tb1.eventType,
        tb1.author, 
        tb1.panelname,
        tb1.tag_code,
        COUNT(*) numAction 
        FROM (SELECT 
    </sql>
    
    <sql id="selectInnerMinimalist">
        event_type.id idEventType,
        event_type.code eventType,
        event.author author, 
        panel.name panelname, 
        tag_type.code tag_code 
        FROM ${database}event 
        INNER JOIN ${database}fixture ON fixture.id = event.fixture_id 
        LEFT JOIN event_type ON event.event_type_id = event_type.id 
        LEFT JOIN panel ON event_type.panel_id = panel.id 
        LEFT JOIN ${database}event_tag ON event.id = event_tag.event_id 
        LEFT JOIN tag_type ON event_tag.tag_type_id = tag_type.id 
        LEFT JOIN ${database}event_player ON event.id=event_player.event_id 
    </sql>
    
    <select id="readHightlightsEventsMatches" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,
        (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        WHERE fixture.id IN (${idFixtures}) and event.groupset_id IN (-1,#{idGroup})
        AND fixture.deleted = 0
        ${where}<!--${strFilter}-->
    </select>
    
    <select id="readIPOEventsMatches" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        WHERE fixture.id IN (${idFixtures}) AND event.groupset_id IN (-1,#{idGroup}) 
        AND event.xml_idevent IN(
        SELECT DISTINCT xml_idevent 
        FROM oi_trend
        WHERE fixture_id IN (${idFixtures})
        AND fixture.competition_id IN (${allowedCompetitions})
        AND fixture.video_name IS NOT NULL 
        AND fixture.deleted = 0
        AND groupset_id IN (-1,#{idGroup})
        AND team_id=#{idTeam}
        AND delta>0
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        )
    </select>
	
    <!-- ritorna tutti le azioni di una partita-->
    <select id="readEventsMatches" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        <if test="isGuest == true">
            LEFT JOIN event_owner ON event_owner.event_id = event.id
        </if>
        WHERE fixture.id IN (${idFixtures})
        AND fixture.competition_id IN (${allowedCompetitions})
        AND fixture.video_name IS NOT NULL
        AND fixture.deleted = 0
        AND event.groupset_id IN (-1,#{idGroup})
        AND event.event_type_id NOT IN (267,927)
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        <if test="isGuest == true">
            AND IFNULL(event_owner.user_id, ${userId}) = ${userId}
        </if>
        <!--${strFilter}-->
    </select>
    
    <select id="readEventsMatchesByEventIds" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        <if test="isGuest != null and isGuest == true">
            LEFT JOIN event_owner ON event_owner.event_id = event.id
        </if>
        WHERE event.groupset_id IN (-1,#{idGroup})
        <if test="allowedCompetitions != null">
            AND fixture.competition_id IN (${allowedCompetitions})
        </if>
        AND fixture.video_name IS NOT NULL
        AND fixture.deleted = 0
        AND event.event_type_id NOT IN (267,927)
        AND event.id IN(${eventIds})
        <if test="isGuest != null and isGuest == true">
            AND IFNULL(event_owner.user_id, ${userId}) = ${userId}
        </if>
    </select>
    
    <select id="readPlayerEventsMatches" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        <if test="isGuest == true">
            LEFT JOIN event_owner ON event_owner.event_id = event.id
        </if>
        WHERE fixture.id IN (${idFixtures})
        AND fixture.competition_id IN (${allowedCompetitions})
        AND fixture.video_name IS NOT NULL
        AND fixture.deleted = 0
        AND event_player.player_id IN (${playerId})
        AND event.groupset_id IN (-1,#{idGroup})
        AND event.event_type_id NOT IN(267, 284)
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        <if test="isGuest == true">
            AND IFNULL(event_owner.user_id, ${userId}) = ${userId}
        </if>
        <!--${strFilter}-->
    </select>
    
    <select id="readTeamEventsMatches" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        <if test="isGuest == true">
            LEFT JOIN event_owner ON event_owner.event_id = event.id
        </if>
        WHERE fixture.id IN (${idFixtures})
        AND fixture.competition_id IN (${allowedCompetitions})
        AND fixture.video_name IS NOT NULL
        AND fixture.deleted = 0
        AND event.team_id = ${teamId}
        AND event.groupset_id IN (-1,#{idGroup})
        AND event.event_type_id NOT IN(267, 927)
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        <if test="isGuest == true">
            AND IFNULL(event_owner.user_id, ${userId}) = ${userId}
        </if>
    </select>
    
    <select id="readPlayerBestEvents" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        WHERE fixture.competition_id IN (${allowedCompetitions})
        AND fixture.video_name IS NOT NULL
        AND event_player.player_id IN (${playerId})
        AND event.groupset_id IN (-1,#{idGroup})
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        AND fixture.season_id IN (${seasonIds})
        AND fixture.deleted = 0
        ${where}
    </select>
    
    <select id="readPlayerHighlights" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        WHERE fixture.competition_id IN (${allowedCompetitions})
        AND fixture.video_name IS NOT NULL
        AND event_player.player_id IN (${playerId})
        AND event.groupset_id IN (-1,#{idGroup})
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        AND fixture.season_id IN (${seasonIds})
        AND fixture.deleted = 0
        ${where}
        ORDER BY fixture.game_date DESC, event.period_start_msec DESC
        LIMIT 500
    </select>
    
    <!-- ritorna tutti le azioni di una partita basket-->
    <select id="readEventsMatchesBasket" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzioneBasket"/>
        WHERE fixture.id IN (#{idFixtures}) and event.groupset_id IN (-1,#{idGroup})
    </select>
    
    <select id="readPlayerEventsMatchesBasket" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzioneBasket"/>
        WHERE fixture.id IN (#{idFixtures}) and event_player.player_id IN (${playerId}) and event.groupset_id IN (-1,#{idGroup})
    </select>
	
    <select id="getActionsByFilter" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        ${where}
    </select>
    
    <select id="getActionsByFilterBasket" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzioneBasket"/>
        ${where}
    </select>
    
    
    <select id="getActionsSingleMatchByFilter" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        ${where}
    </select>
    
    <select id="getActionsSingleMatchByFilterBasket" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzioneBasket"/>
        INNER JOIN game ON game.fixture_id=fixture.id
        ${where}
    </select>
    
    
    
    <select id="getActionsLimited" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        ${where}
    </select>
    

    <!-- trova gli idEvent delle azioni - NON PIU USATA-->    
    <select id="getActionsLimitedTag" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,(CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,
        (CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,<include refid="selectAzioneCount"/>
        ${where} 
        AND event.event_type_id NOT IN (267,927)
        GROUP BY CONCAT(event_type.id,"#" ,tag_type.id)
    </select>
    
    <!-- trova gli idEvent delle azioni-->    
    <select id="getActionsLimitedEvent" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select <include refid="selectAzioneMinimalist"/> 
        <include refid="selectInnerMinimalist"/> 
        ${where} 
        <if test="isMultiFixture == true">
            AND event.event_type_id NOT IN (267,927)
        </if>
        <if test="isMultiFixture == false">
            AND event.event_type_id NOT IN (267,927) 
        </if>
        GROUP BY event.id) AS tb1
        GROUP BY idEventType
    </select>
    <select id="getActionsLimitedEventByTag" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select <include refid="selectAzioneMinimalist"/> 
        <include refid="selectInnerMinimalist"/> 
        ${where} 
        <if test="isMultiFixture == true">
            AND event.event_type_id NOT IN (267,927)
        </if>
        <if test="isMultiFixture == false">
            AND event.event_type_id NOT IN (267,927) 
        </if>
        GROUP BY event.id, tag_type.id) AS tb1
        GROUP BY idEventType, tag_code
    </select>
    
    <!-- trova gli idEvent delle azioni - NON PIU USATA-->    
    <select id="getActionsLimitedTagByGameIds" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,(CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,
        (CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,<include refid="selectAzioneCount"/> 
        ${where} AND tag_type.id IS NOT NULL 
        AND event.event_type_id NOT IN (267,927)
        GROUP BY CONCAT(event_type.id,"#" ,tag_type.id)
    </select>
    
    <!-- trova gli idEvent delle azioni-->    
    <select id="getActionsLimitedEventByGameIds" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select <include refid="selectAzioneMinimalist"/> 
        <include refid="selectInnerMinimalist"/> 
        ${where} 
        <if test="isMultiFixture == true">
            AND event.event_type_id NOT IN (267,927)
        </if>
        <if test="isMultiFixture == false">
            AND event.event_type_id NOT IN (267,927) 
        </if>
        GROUP BY event.id) AS tb1
        GROUP BY idEventType
    </select>
    <select id="getActionsLimitedEventByGameIdsAndTag" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select <include refid="selectAzioneMinimalist"/> 
        <include refid="selectInnerMinimalist"/> 
        ${where} 
        <if test="isMultiFixture == true">
            AND event.event_type_id NOT IN (267,927)
        </if>
        <if test="isMultiFixture == false">
            AND event.event_type_id NOT IN (267,927) 
        </if>
        GROUP BY event.id, tag_type.id) AS tb1
        GROUP BY idEventType, tag_code
    </select>
    <select id="getActionsLimitedEventByGameIdsOnlyNoTag" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select <include refid="selectAzioneMinimalist"/> 
        <include refid="selectInnerMinimalist"/> 
        ${where} 
        <if test="isMultiFixture == true">
            AND event.event_type_id NOT IN (7,927)
        </if>
        <if test="isMultiFixture == false">
            AND event.event_type_id NOT IN (267,927) 
        </if>
        AND tag_type.id IS NULL 
        GROUP BY event.id) AS tb1
        GROUP BY idEventType
    </select>
    
	
    <!-- ritorna goal(25) e ammonizioni(31) - NON USATA ??-->
    <select id="readAzioniTabellinoMatches" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        WHERE fixture.id IN (#{where}) AND event.event_type_id IN(25,31) and event.groupset_id = -1
        GROUP BY startms
        ORDER BY event_type.code
    </select>
    
    <!-- ritorna goal(25) e ammonizioni(31) - NON USATA ??-->
    <select id="readAzioniTabellinoMatchesBasket" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzioneBasket"/>
        WHERE fixture.id IN (#{where}) AND event.event_type_id IN(25,31) and event.groupset_id = -1
        GROUP BY startms
        ORDER BY event_type.code
    </select>
    
    <select id="checkEventTactical" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        SELECT event.id eventid, event.fixture_id idfixture, CASE WHEN groupset_level.id IS NULL THEN 0 ELSE 1 END is_tactical
        FROM event
        INNER JOIN fixture ON fixture.id = event.fixture_id
        LEFT JOIN groupset_comp_tact ON groupset_comp_tact.competition_id = fixture.competition_id AND groupset_comp_tact.groupset_id = ${groupsetId}
        LEFT JOIN groupset_level ON groupset_level.groupset_id = groupset_comp_tact.groupset_id AND groupset_level.level_id = 6
        WHERE event.id IN(${eventIds})
        GROUP BY event.id
    </select>
    
    <select id="getPersonalEvents" resultMap="resultMapAzione" parameterType="java.util.TreeMap">
        select (CASE WHEN tHome.name${language} IS NULL THEN tHome.name ELSE tHome.name${language} END) hometeam,(CASE WHEN tAway.name${language} IS NULL THEN tAway.name ELSE tAway.name${language} END) awayteam,(CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) teamName,<include refid="selectAzione"/>
        <if test="isGuest == true">
            LEFT JOIN event_owner ON event_owner.event_id = event.id
        </if>
        WHERE event.groupset_id = ${groupsetId}
        AND fixture.competition_id IN (${allowedCompetitions})
        AND fixture.season_id IN (${seasonIds})
        AND fixture.video_name IS NOT NULL
        AND fixture.deleted = 0
        AND event.event_type_id = 1156
        <if test="isGuest == true">
            AND IFNULL(event_owner.user_id, ${userId}) = ${userId}
        </if>
    </select>
    
    <select id="addPersonalEvent" parameterType="java.util.TreeMap" resultType="java.lang.Long">
        SELECT addPersonalEvent(${eventTypeId}, ${fixtureId}, ${teamId}, ${periodId}, ${periodStartMinute}, ${periodStartSecond}, ${periodEndMinute}, ${periodEndSecond}, ${periodStartMsec}, ${periodEndMsec}, "${note}", "${xmlIdEvent}", ${groupsetId}, "${author}") last_insert_id
    </select>
    
    <insert id="addPersonalEventOwner" parameterType="java.util.TreeMap">
        INSERT INTO event_owner(event_id, user_id)
        VALUES(${eventId}, ${userId})
    </insert>
    
    <update id="updatePersonalEvent" parameterType="java.util.TreeMap">
        UPDATE event
        SET team_id = ${teamId},
        period_id = ${periodId},
        period_start_minute = ${periodStartMinute},
        period_start_second = ${periodStartSecond},
        period_end_minute = ${periodEndMinute},
        period_end_second = ${periodEndSecond},
        period_start_msec = ${periodStartMsec},
        period_end_msec = ${periodEndMsec},
        note = "${note}",
        author = "${author}"
        WHERE id = ${eventId}
    </update>
    
    <insert id="addEventPlayer" parameterType="java.util.TreeMap">
        INSERT INTO event_player(event_id, player_id)
        VALUES(${eventId}, ${playerId})
    </insert>
    
    <delete id="deletePersonalEvent" parameterType="java.util.TreeMap">
        DELETE FROM event WHERE id = ${eventId} AND event_type_id = 1156
    </delete>
    
    <delete id="deletePersonalEventPlayers" parameterType="java.util.TreeMap">
        DELETE FROM event_player WHERE event_id = ${eventId}
    </delete>
    
</mapper>
