<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Export">

    <resultMap id="resultMapExport"         type="Export">
        <id property="id"                   column="export_id"/>
        <id property="application"          column="application"/>
        <id property="userId"               column="user_id"/>
        <id property="groupsetId"           column="groupset_id"/>
        <id property="name"                 column="name"/>
        <id property="type"                 column="type"/>
        <id property="statusCode"           column="status_code"/>
        <id property="clipAmount"           column="clip_amount"/>
        <id property="startTime"            column="start_time"/>
        <id property="totalTime"            column="total_time"/>
        <id property="totalBilled"          column="total_billed"/>
        <id property="totalVideoDuration"   column="total_video_duration"/>
        <id property="playlistId"           column="playlist_id"/>
    </resultMap>
    
    <resultMap id="resultMapExportDetail"   type="ExportDetail">
        <id property="id"                   column="detail_id"/>
        <id property="exportId"             column="export_id"/>
        <id property="type"                 column="type"/>
        <id property="statusCode"           column="status_code"/>
        <id property="message"              column="message"/>
        <id property="clipIndex"            column="clip_index"/>
        <id property="clipStartMs"          column="clip_start_ms"/>
        <id property="clipDurationMs"       column="clip_duration_ms"/>
        <id property="videoName"            column="video_name"/>
        <id property="startTime"            column="start_time"/>
        <id property="executionTime"        column="execution_time"/>
    </resultMap>

    <select id="getExportByUser" resultMap="resultMapExport" parameterType="java.util.TreeMap">
        SELECT id export_id, application, user_id, groupset_id, name, type, status_code, clip_amount,
        start_time, total_time, total_billed, total_video_duration, playlist_id
        FROM export
        WHERE user_id = ${userId} AND groupset_id = ${groupsetId} AND application = 1
        ORDER BY start_time DESC
    </select>
    
    <select id="getExportDetailByUser" resultMap="resultMapExportDetail" parameterType="java.util.TreeMap">
        SELECT export_detail.id detail_id, export_id, export_detail.type, export_detail.status_code, message, clip_index, clip_start_ms,
        clip_duration_ms, video_name, export_detail.start_time, execution_time
        FROM export
        INNER JOIN export_detail ON export_detail.export_id = export.id
        WHERE export.user_id = ${userId} AND export.groupset_id = ${groupsetId} AND application = 1
        ORDER BY export_id DESC, clip_index
    </select>
    
    <select id="getExportByUserAndData" resultMap="resultMapExport" parameterType="java.util.TreeMap">
        SELECT id export_id, application, user_id, groupset_id, name, type, status_code, clip_amount,
        start_time, total_time, total_billed, total_video_duration, playlist_id
        FROM export
        WHERE user_id = ${userId} AND groupset_id = ${groupsetId} AND application = 1
        AND start_time BETWEEN STR_TO_DATE("${from} 00:00:01", "%Y-%m-%d %H:%i:%s") AND STR_TO_DATE("${to} 23:59:59", "%Y-%m-%d %H:%i:%s")
        ORDER BY start_time DESC
    </select>
    
    <select id="getExportDetailByUserAndData" resultMap="resultMapExportDetail" parameterType="java.util.TreeMap">
        SELECT export_detail.id detail_id, export_id, export_detail.type, export_detail.status_code, message, clip_index, clip_start_ms,
        clip_duration_ms, video_name, export_detail.start_time, execution_time
        FROM export
        INNER JOIN export_detail ON export_detail.export_id = export.id
        WHERE export.user_id = ${userId} AND export.groupset_id = ${groupsetId} AND application = 1
        AND export.start_time BETWEEN STR_TO_DATE("${from} 00:00:01", "%Y-%m-%d %H:%i:%s") AND STR_TO_DATE("${to} 23:59:59", "%Y-%m-%d %H:%i:%s")
        ORDER BY export_id DESC, clip_index
    </select>
    
    <select id="addExport" parameterType="Export" resultType="java.lang.Long">
        SELECT addExport(#{application}, #{userId}, #{groupsetId}, #{name}, #{type}, #{clipAmount}, #{totalVideoDuration}) last_insert_id
    </select>
    
    <select id="getUserLimit" parameterType="java.util.TreeMap" resultType="java.lang.Long">
        SELECT CASE WHEN user.export_time_limit IS NULL THEN groupset.export_time_limit ELSE user.export_time_limit END minutes_limit
        FROM user
        INNER JOIN groupset ON groupset.id = user.groupset_id
        WHERE user.id = ${userId}
    </select>
    
    <select id="getUserTimeUsed" parameterType="java.util.TreeMap" resultType="java.lang.Long">
        SELECT SUM(IFNULL(total_video_duration, 0)) time_used_ms
        FROM export
        WHERE user_id = ${userId}
        AND start_time >= NOW() - INTERVAL 1 WEEK
    </select>
    
    <insert id="addExportDetails" parameterType="java.util.TreeMap">
        INSERT IGNORE INTO export_detail (export_id, type, status_code, message, clip_index, clip_start_ms, clip_duration_ms, video_name, start_time, execution_time)
        VALUES ${values}
    </insert>
    
    <update id="updateExport" parameterType="Export">
        UPDATE export SET status_code = #{statusCode}, total_time = #{totalTime}, total_billed = #{totalBilled}, playlist_id = #{playlistId}
        WHERE id = #{id}
    </update>
    
</mapper>