<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Cache">

    <resultMap id="resultMapCache"                  type="Cache">
        <result property="playerId"                 column="player_id"/>
        <result property="playerFirstName"          column="player_first_name"/>
        <result property="playerLastName"           column="player_last_name"/>
        <result property="playerKnownName"          column="player_known_name"/>
        <result property="playerPhoto"              column="player_photo"/>
        <result property="playerBornDate"           column="player_born_date"/>
        <result property="playerCountryId"          column="player_country_id"/>
        <result property="playerCountryName"        column="player_country_name"/>
        <result property="playerCountryLogo"        column="player_country_logo"/>
        <result property="playerHeight"             column="player_height"/>
        <result property="playerFootId"             column="player_foot_id"/>
        <result property="playerFoot"               column="player_foot"/>
        <result property="playerLastTeamId"         column="player_last_team_id"/>
        <result property="playerLastTeamName"       column="player_last_team_name"/>
        <result property="playerLastTeamLogo"       column="player_last_team_logo"/>
        <result property="playerLastTeamGenere"     column="player_last_team_genere"/>
        <result property="playerPositionId"         column="player_position_id"/>
        <result property="playerPosition"           column="player_position"/>
        
        <result property="fixtureId"                column="fixture_id"/>
        <result property="fixtureHomeTeamId"        column="fixture_home_team_id"/>
        <result property="fixtureHomeTeam"          column="fixture_home_team"/>
        <result property="fixtureAwayTeamId"        column="fixture_away_team_id"/>
        <result property="fixtureAwayTeam"          column="fixture_away_team"/>
        <result property="fixtureMatchDay"          column="fixture_matchday"/>
        
        <result property="teamId"                   column="team_id"/>
        <result property="teamName"                 column="team_name"/>
        <result property="teamLogo"                 column="team_logo"/>
        <result property="teamGenere"               column="team_genere"/>
        
        <result property="competitionId"            column="competition_id"/>
        <result property="competitionName"          column="competition_name"/>
        <result property="competitionLogo"          column="competition_logo"/>
        <result property="competitionGenere"        column="competition_genere"/>
        
        <result property="statsTypeId"              column="stats_type_id"/>
        <result property="statsTypeDescription"     column="stats_type_description"/>
        <result property="statsTypeDivider"         column="stats_type_divider"/>
        <result property="statsTypeAverageTypeId"   column="stats_type_average_type_id"/>
        <result property="statsTypeAmount"          column="stats_type_amount"/>
        
        <result property="modulePosition"           column="module_position"/>
        <result property="substitute"               column="substitute"/>
        <result property="red"                      column="red"/>
        <result property="nationalTeam"             column="national_team"/>
        <result property="totalMatches"             column="total_matches"/>
    </resultMap>
    
    <resultMap id="resultMapCacheLanguageItem"      type="CacheLanguageItem">
        <result property="id"                       column="id"/>
        <result property="nameIt"                   column="name_it"/>
        <result property="nameEn"                   column="name_en"/>
        
        <result property="realNameIt"               column="real_name_it"/>
        <result property="realNameEn"               column="real_name_en"/>
    </resultMap>
    
    <sql id="selectPlayer">
        player.id player_id, player.first_name player_first_name, player.last_name player_last_name,
        player.known_name player_known_name, player.photo player_photo, player.born_date player_born_date, country.id player_country_id,
        country.logo player_country_logo, player.height player_height, foot.id player_foot_id, stats_type.id stats_type_id,
        SUM(player_stats.value) stats_type_amount, stats_type.divider stats_type_divider, stats_type.avg_type_id stats_type_average_type_id,
        competition.id competition_id, competition.logo competition_logo, competition.genere competition_genere, team.id team_id,
        team.logo team_logo, team.genere team_genere
    </sql>
    
    <sql id="selectTeam">
        fixture.id fixture_id, fixture.matchday fixture_matchday, home.id fixture_home_team_id, away.id fixture_away_team, stats_type.id stats_type_id,
        SUM(player_stats.value) stats_type_amount, stats_type.divider stats_type_divider, stats_type.avg_type_id stats_type_average_type_id,
        competition.id competition_id, competition.logo competition_logo, competition.genere competition_genere, team.id team_id, team.logo team_logo, team.genere team_genere
    </sql>
    
    <select id="getSmartSearchPlayerBaseData" resultMap="resultMapCache" parameterType="java.util.TreeMap">
        SELECT <include refid="selectPlayer"/>
        FROM player
        INNER JOIN player_stats ON player_stats.player_id = player.id
        INNER JOIN fixture ON fixture.id = player_stats.fixture_id
        INNER JOIN fixture_player ON fixture_player.fixture_id = fixture.id AND fixture_player.player_id = player.id
        INNER JOIN stats_type ON stats_type.id = player_stats.type_id
        INNER JOIN foot ON foot.id = player.foot_id
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN country competition_country ON competition_country.id = competition.country_id
        INNER JOIN international_competition ON international_competition.id = competition.international_competition_id
        INNER JOIN country ON country.id = player.country_id
        INNER JOIN team ON team.id = fixture_player.team_id
        WHERE fixture.season_id IN(${seasonId})
        AND player_stats.player_id = ${playerId}
        GROUP BY player.id, fixture.competition_id, fixture_player.team_id, player_stats.type_id
    </select>
    
    <select id="getSmartSearchTeamBaseData" resultMap="resultMapCache" parameterType="java.util.TreeMap">
        SELECT <include refid="selectTeam"/>
        FROM team
        INNER JOIN player_stats ON player_stats.team_id = team.id
        INNER JOIN fixture ON fixture.id = player_stats.fixture_id
        INNER JOIN stats_type ON stats_type.id = player_stats.type_id
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN country competition_country ON competition_country.id = competition.country_id
        INNER JOIN international_competition ON international_competition.id = competition.international_competition_id
        INNER JOIN country ON country.id = team.country_id
        INNER JOIN team home ON home.id = fixture.home_team_id
        INNER JOIN team away ON away.id = fixture.away_team_id
        WHERE fixture.season_id IN(${seasonId})
        AND team.id = ${teamId}
        GROUP BY fixture.id, fixture.competition_id, player_stats.type_id
    </select>
    
    <select id="getStatisticsData" resultMap="resultMapCache" parameterType="java.util.TreeMap">
        SELECT <include refid="selectPlayer"/>
        FROM player
        INNER JOIN player_stats ON player_stats.player_id = player.id
        INNER JOIN fixture ON fixture.id = player_stats.fixture_id
        INNER JOIN fixture_player ON fixture_player.fixture_id = fixture.id AND fixture_player.player_id = player.id
        INNER JOIN stats_type ON stats_type.id = player_stats.type_id
        INNER JOIN foot ON foot.id = player.foot_id
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN country competition_country ON competition_country.id = competition.country_id
        INNER JOIN international_competition ON international_competition.id = competition.international_competition_id
        INNER JOIN country ON country.id = player.country_id
        INNER JOIN team ON team.id = fixture_player.team_id
        WHERE fixture.season_id IN(${seasonId})
        AND player_stats.player_id = ${playerId}
        GROUP BY player.id, fixture.id, fixture.competition_id, fixture_player.team_id, player_stats.type_id
    </select>
    
    <select id="getSmartSearchPlayerLastTeamData" resultMap="resultMapCache" parameterType="java.util.TreeMap">
        SELECT tbl.*, position.id player_position_id
        FROM (
            SELECT team.id team_id, team.logo team_logo, team.genere team_genere
            FROM team
            INNER JOIN fixture_player ON fixture_player.player_id = ${playerId} AND fixture_player.team_id = team.id
            INNER JOIN fixture ON fixture.id = fixture_player.fixture_id
            WHERE fixture.season_id IN(${seasonId}) AND team.national_team = 0
            ORDER BY fixture.game_date DESC
            LIMIT 1
        ) tbl
        INNER JOIN team_player ON team_player.player_id = ${playerId} AND team_player.team_id = tbl.team_id
        LEFT JOIN position ON position.id = team_player.position_id
        ORDER BY team_player.season_id DESC
        LIMIT 1
    </select>
    
    <select id="getSmartSearchPlayerMatchData" resultMap="resultMapCache" parameterType="java.util.TreeMap">
        SELECT fixture.competition_id, fixture_player.team_id, fixture_player.module_position, fixture_player.substitute,
        fixture_player.red, team.national_team, COUNT(DISTINCT fixture.id) total_matches
        FROM fixture_player
        INNER JOIN fixture ON fixture.id = fixture_player.fixture_id
        INNER JOIN team ON team.id = fixture_player.team_id
        WHERE fixture_player.player_id = ${playerId} AND fixture.season_id IN(${seasonId})
        GROUP BY fixture.competition_id, fixture_player.team_id, fixture_player.module_position, fixture_player.substitute, fixture_player.red, team.national_team
    </select>
    
    <!-- Language Cache -->
    <select id="getBaseCountry" resultMap="resultMapCacheLanguageItem" parameterType="java.util.TreeMap">
        SELECT id,
               (CASE WHEN country.name_it IS NULL THEN country.name_it ELSE country.name_it END) name_it,
               (CASE WHEN country.name_en IS NULL THEN country.name_it ELSE country.name_en END) name_en
        FROM country
    </select>
    <select id="getBaseFoot" resultMap="resultMapCacheLanguageItem" parameterType="java.util.TreeMap">
        SELECT id,
               (CASE WHEN foot.desc_it IS NULL THEN foot.desc_it ELSE foot.desc_it END) name_it,
               (CASE WHEN foot.desc_en IS NULL THEN foot.desc_it ELSE foot.desc_en END) name_en
        FROM foot
    </select>
    <select id="getBaseStatsType" resultMap="resultMapCacheLanguageItem" parameterType="java.util.TreeMap">
        SELECT id,
               (CASE WHEN stats_type.desc_it IS NULL THEN stats_type.desc_it ELSE stats_type.desc_it END) name_it,
               (CASE WHEN stats_type.desc_en IS NULL THEN stats_type.desc_it ELSE stats_type.desc_en END) name_en
        FROM stats_type
    </select>
    <select id="getBaseCompetition" resultMap="resultMapCacheLanguageItem" parameterType="java.util.TreeMap">
        SELECT competition.id,
               CONCAT (
                    CASE WHEN competition.country_id > 0 THEN
                        (CASE WHEN competition_country.name_it IS NULL THEN competition_country.name_it ELSE competition_country.name_it END)
                    ELSE
                        (CASE WHEN international_competition.name_it IS NULL THEN international_competition.name_it ELSE international_competition.name_it END)
                    END,
                    '. ',
                    (CASE WHEN competition.name IS NULL THEN competition.name ELSE competition.name END)
               ) name_it,
               CONCAT (
                    CASE WHEN competition.country_id > 0 THEN
                        (CASE WHEN competition_country.name_en IS NULL THEN competition_country.name_it ELSE competition_country.name_en END)
                    ELSE
                        (CASE WHEN international_competition.name_en IS NULL THEN international_competition.name_it ELSE international_competition.name_en END)
                    END,
                    '. ',
                    (CASE WHEN competition.name_en IS NULL THEN competition.name ELSE competition.name_en END)
               ) name_en,
               (CASE WHEN competition.name IS NULL THEN competition.name ELSE competition.name END) real_name_it,
               (CASE WHEN competition.name_en IS NULL THEN competition.name ELSE competition.name_en END) real_name_en
        FROM competition
        INNER JOIN country competition_country ON competition_country.id = competition.country_id
        INNER JOIN international_competition ON international_competition.id = competition.international_competition_id
        WHERE competition.groupset_id = -1
    </select>
    <select id="getBaseTeam" resultMap="resultMapCacheLanguageItem" parameterType="java.util.TreeMap">
        SELECT id,
               (CASE WHEN team.name IS NULL THEN team.name ELSE team.name END) name_it,
               (CASE WHEN team.name_en IS NULL THEN team.name ELSE team.name_en END) name_en
        FROM team
        WHERE groupset_id = -1
    </select>
    <select id="getBasePosition" resultMap="resultMapCacheLanguageItem" parameterType="java.util.TreeMap">
        SELECT id,
               (CASE WHEN position.desc_it IS NULL THEN position.desc_it ELSE position.desc_it END) name_it,
               (CASE WHEN position.desc_en IS NULL THEN position.desc_it ELSE position.desc_en END) name_en
        FROM position
        WHERE sport_id = 0
    </select>
    
    <!-- Extra -->
    <!-- Ritorna lista di season da elaborare per il giocatore -->
    <select id="getSeasonAvailableForPlayer" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT DISTINCT season.id
        FROM player_stats
        INNER JOIN fixture ON fixture.id = player_stats.fixture_id
        INNER JOIN season ON season.id = fixture.season_id
        WHERE player_stats.player_id = ${playerId} AND season.visible = 1
    </select>
    
    <select id="getSeasonAvailableForTeam" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT DISTINCT season.id
        FROM player_stats
        INNER JOIN fixture ON fixture.id = player_stats.fixture_id
        INNER JOIN season ON season.id = fixture.season_id
        WHERE player_stats.team_id = ${teamId} AND season.visible = 1
    </select>
    
    <select id="getAllValidPlayers" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT DISTINCT player_id
        FROM fixture
        INNER JOIN player_stats ON player_stats.fixture_id = fixture.id AND player_stats.type_id = 1
        WHERE fixture.season_id IN (2021, 2022, 2023, 21, 22, 23, 24)
    </select>
    
    <select id="getAllValidTeams" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT DISTINCT team_id
        FROM fixture
        INNER JOIN player_stats ON player_stats.fixture_id = fixture.id AND player_stats.type_id = 1
        WHERE fixture.season_id IN (2021, 2022, 2023, 21, 22, 23, 24)
    </select>
</mapper>