<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Atleta">

    <resultMap id="resultMapAtleta" type="Atleta">
        <result property="id"  column="idPlayer"/>
        <result property="personalId"  column="personalId"/>
        <result property="known_name"  column="name"/>
        <result property="first_name"  column="firstname"/>
        <result property="last_name"  column="lastname"/>
        <result property="bornDate"  column="borndate"/>
        <result property="numero" column="number"/>
        <result property="matchNumber" column="matchNumber"/>
        <result property="idRuolo"	column="position_id"/>
        <result property="ruolo"	column="position"/>
        <result property="descrizione_ruolo_it"	column="position_desc_it"/>
        <result property="descrizione_ruolo_en"	column="position_desc_en"/>
        <result property="descrizione_ruolo_fr"	column="position_desc_fr"/>
        <result property="descrizione_ruolo_es"	column="position_desc_es"/>
        <result property="descrizione_ruolo_ru"	column="position_desc_ru"/>
        <result property="team" column="team_id"/>
        <result property="teamName" column="team"/>
        <result property="teamLogo" column="team_logo"/>
        <result property="fromPeriod" column="fromPeriod"/>
        <result property="toPeriod" column="toPeriod"/>
        <result property="fromMinute" column="fromPeriodMinute"/>
        <result property="toMinute" column="toPeriodMinute"/>
        <result property="redCard" column="redCard"/>
        <result property="mPosition" column="coordinate"/>
        <result property="module" column="module"/>
        <result property="module_position" column="modulePosition"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="groupset_id" column="teamPlayerGroupsetId"/>
        <result property="photo" column="photo"/>
        <result property="height" column="height"/>
        <result property="actionAmount" column="action_amount"/>
        <result property="shared" column="shared"/>
        <result property="footId" column="foot_id"/>
        <result property="idPosition" column="position_id"/>
        <result property="index" column="index"/>
        <result property="agentId" column="agent_id"/>
        <result property="agentName" column="agent_name"/>
        <result property="marketValue" column="market_value"/>
        <result property="contractExpires" column="contract_expires"/>
        <result property="seasonId" column="season_id"/>
        <result property="playtime" column="playtime"/>
        <result property="competitionIds" column="competition_ids"/>
    </resultMap>
    
    <resultMap id="resultMapGeneralAtleta" type="Atleta">
        <result property="id"  column="idPlayer"/>
        <result property="personalId"  column="personalId"/>
        <result property="known_name"  column="name"/>
        <result property="first_name"  column="firstname"/>
        <result property="last_name"  column="lastname"/>
        <result property="bornDate"  column="borndate"/>
        <result property="numero" column="number"/>
        <result property="idRuolo"	column="position_id"/>
        <result property="ruolo"	column="position"/>
        <result property="descrizione_ruolo_it"	column="position_desc_it"/>
        <result property="descrizione_ruolo_en"	column="position_desc_en"/>
        <result property="descrizione_ruolo_fr"	column="position_desc_fr"/>
        <result property="descrizione_ruolo_es"	column="position_desc_es"/>
        <result property="descrizione_ruolo_ru"	column="position_desc_ru"/>
        <result property="team" column="team_id"/>
        <result property="teamName" column="team"/>
        <result property="teamLogo" column="team_logo"/>
        <result property="photo" column="photo"/>
        <result property="height" column="height"/>
        <result property="country_it" column="country_it"/>
        <result property="country_en" column="country_en"/>
        <result property="country_logo" column="country_logo"/>
        <result property="foot_it" column="foot_it"/>
        <result property="foot_en" column="foot_en"/>
        <result property="foot_fr" column="foot_fr"/>
        <result property="foot_es" column="foot_es"/>
        <result property="foot_ru" column="foot_ru"/>
        <result property="position_detail_it" column="position_detail_it"/>
        <result property="position_detail_en" column="position_detail_en"/>
        <result property="position_detail_fr" column="position_detail_fr"/>
        <result property="position_detail_es" column="position_detail_es"/>
        <result property="position_secondary_it" column="position_secondary_it"/>
        <result property="position_secondary_en" column="position_secondary_en"/>
        <result property="position_secondary_fr" column="position_secondary_fr"/>
        <result property="position_secondary_es" column="position_secondary_es"/>
        <result property="shared" column="shared"/>
        <result property="footId" column="foot_id"/>
        <result property="idPosition" column="position_id"/>
        <result property="index" column="index"/>
        <result property="agentId" column="agent_id"/>
        <result property="agentName" column="agent_name"/>
        <result property="marketValue" column="market_value"/>
        <result property="contractExpires" column="contract_expires"/>
        <result property="seasonId" column="season_id"/>
        <result property="playtime" column="playtime"/>
        <result property="competitionIds" column="competition_ids"/>
    </resultMap>
	
    <resultMap id="resultMapJson" type="Atleta">
        <result property="id"  column="idPlayer"/>
        <result property="personalId"  column="personalId"/>
        <result property="known_name"  column="name"/>
        <result property="first_name"  column="firstname"/>
        <result property="last_name"  column="lastname"/>
        <result property="bornDate"  column="borndate"/>
        <result property="photo"  column="photo"/>
        <result property="height"  column="height"/>
        <result property="shared" column="shared"/>
        <result property="footId" column="foot_id"/>
        <result property="idPosition" column="position_id"/>
        <result property="teamName" column="team"/>
        
        <result property="seasonName"	column="season_name"/>
    </resultMap>
    
    <resultMap id="resultMapPosition" type="Position">
        <result property="id"           column="position_id"/>
        <result property="code"         column="position_code"/>
        <result property="description"  column="position_description"/>
    </resultMap>
	
    <sql id="selectAtleta">
        player.id idPlayer,
        player.known_name name,
        player.first_name firstname,
        player.last_name lastname,
        player.born_date borndate,
        position.id position_id,
        position.code position,
        position.desc_it position_desc_it,
        position.desc_en position_desc_en,
        position.desc_fr position_desc_fr,
        position.desc_es position_desc_es,
        position.desc_ru position_desc_ru,
        fixture_player.jersey_num matchNumber,
        fixture_player.jersey_num number,
        fixture_player.play_time playtime,
        team.id team_id,
        team.logo team_logo,
        from_period_id fromPeriod,
        to_period_id toPeriod,
        from_period_minute fromPeriodMinute,
        to_period_minute toPeriodMinute,
        red redCard,
        coordinate coordinate,
        module_position modulePosition, 
        team_player.groupset_id teamPlayerGroupsetId,
        player.photo,
        player.height,
        player_agent.name agent_name,
        player.market_value,
        team_player.contract_expires,
        player_agent.id agent_id,
        team_player.season_id
        FROM videomatch.player
        LEFT JOIN player_agent ON player_agent.id = player.player_agent_id
        INNER JOIN videomatch.team_player ON team_player.player_id = player.id
        INNER JOIN videomatch.fixture_player ON fixture_player.player_id = player.id AND fixture_player.team_id = team_player.team_id
        INNER JOIN videomatch.fixture ON fixture.id = fixture_player.fixture_id
        INNER JOIN videomatch.team ON team.id = team_player.team_id
        INNER JOIN videomatch.position ON position.id = team_player.position_id
    </sql>
    
    <sql id="selectGeneralAtleta">
        player.id idPlayer,
        player.known_name NAME,
        player.first_name firstname,
        player.last_name lastname,
        player.born_date borndate,
        position.id position_id,
        position.code position,
        position.desc_it position_desc_it,
        position.desc_en position_desc_en,
        position.desc_fr position_desc_fr,
        position.desc_es position_desc_es,
        position.desc_ru position_desc_ru,
        team_player.jersey_num number,
        team_player.groupset_id,
        team.logo team_logo,
        player.photo, 
        player.height,
        country.name_it AS country_it,
        country.name_en AS country_en,
        country.logo AS country_logo,
        foot.id foot_id,
        foot.desc_it AS foot_it,
        foot.desc_en AS foot_en,
        foot.desc_fr AS foot_fr,
        foot.desc_es AS foot_es,
        foot.desc_ru AS foot_ru,
        position_detail.desc_it AS position_detail_it,
        position_detail.desc_en AS position_detail_en,
        position_detail.desc_fr AS position_detail_fr,
        position_detail.desc_es AS position_detail_es,
        pdet.desc_it AS position_secondary_it,
        pdet.desc_en AS position_secondary_en,
        pdet.desc_fr AS position_secondary_fr,
        pdet.desc_es AS position_secondary_es,
        player_agent.name agent_name,
        player.market_value,
        team_player.contract_expires,
        player_agent.id agent_id,
        team_player.season_id
        FROM videomatch.player
        LEFT JOIN player_agent ON player_agent.id = player.player_agent_id
        INNER JOIN videomatch.team_player ON team_player.player_id = player.id
        INNER JOIN position_detail ON position_detail.id=team_player.position_detail_id
        INNER JOIN position_detail pdet ON pdet.id=team_player.position_secondary_id 
        INNER JOIN videomatch.team ON team.id = team_player.team_id
        INNER JOIN videomatch.position ON position.id = team_player.position_id
        INNER JOIN country ON player.country_id=country.id
        INNER JOIN foot ON foot.id=player.foot_id
    </sql>
    
    <select id="getAtletaById" resultMap="resultMapGeneralAtleta" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team,
        <include refid="selectGeneralAtleta"/>
        WHERE player.id = #{idPlayer}
        <if test="!seasonIds.equals('-1')"> AND team_player.season_id IN(${seasonIds})</if>
        <if test="idTeam!=-1"> AND team_player.team_id=#{idTeam}</if>
    </select>
	
    <!-- ritorna tutti i giocatori di una partita-->
    <select id="getAtletiByGame" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        SELECT <include refid="selectAtleta"/>
        WHERE fixture.id =  #{fixtureId} AND team.id IN (#{homeId},#{awayId}) AND (team.id = fixture.home_team_id OR team.id = fixture.away_team_id)
        <![CDATA[
        AND team_player.season_id = CASE WHEN team.solar = 0 THEN CASE WHEN fixture.season_id < 2000 THEN fixture.season_id +2000 ELSE fixture.season_id END ELSE CASE WHEN fixture.season_id > 2000 THEN YEAR(fixture.game_date) -2000 ELSE fixture.season_id END END 
        ]]>
        AND fixture_player.team_id = team_player.team_id
        AND fixture_player.groupset_id IN (-1,#{groupId})
        AND (fixture_player.coordinate != "-1;-1" OR fixture_player.red > -1)
        GROUP BY player.id 
        ORDER BY module_position,fixture_player.jersey_num
    </select>
    
    <select id="getAtletiByGames" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        SELECT <include refid="selectAtleta"/>
        WHERE ${where}
        AND (team.id = fixture.home_team_id OR team.id = fixture.away_team_id)
        <![CDATA[
        AND team_player.season_id = CASE WHEN team.solar = 0 THEN CASE WHEN fixture.season_id < 2000 THEN fixture.season_id +2000 ELSE fixture.season_id END ELSE CASE WHEN fixture.season_id > 2000 THEN YEAR(fixture.game_date) -2000 ELSE fixture.season_id END END 
        ]]>
        AND fixture_player.team_id = team_player.team_id
        AND fixture_player.groupset_id IN (-1,${groupId})
        AND (fixture_player.coordinate != "-1;-1" OR fixture_player.red > -1)
        GROUP BY player.id, team.id
        ORDER BY module_position,fixture_player.jersey_num
    </select>
	
    <!-- ritorna tutti i giocatori di una squadra DAFARE tolto group by player.id -->
    <select id="getAtletiByTeam" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team,
        <include refid="selectAtleta"/>
        INNER JOIN competition ON competition.id = fixture.competition_id
        WHERE team.id = #{teamId} and fixture.season_id IN(${seasonIds})
        AND team_player.team_id = ${teamId} AND team_player.season_id IN(${seasonIds})
        AND team_player.groupset_id IN (-1,#{groupId})
        AND fixture_player.play_time > 0
        <if test="source==true">
            AND fixture_player.groupset_id = #{groupId} 
            AND fixture.provider_id = 3
            AND fixture.deleted = 0
        </if>
        <if test="source==false">
            AND fixture.provider_id = 2
        </if>
        GROUP BY player.id
        ORDER BY fixture.game_date DESC
    </select>
	
    <!-- ritorna tutti i giocatori di una squadra per competizione DAFARE tolto group by player.id -->
    <select id="getAtletiByTeamAndCompetition" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team,
        <include refid="selectAtleta"/>
        INNER JOIN competition ON competition.id = fixture.competition_id 
        WHERE team.id = #{teamId} AND fixture_player.groupset_id IN (-1,#{groupsetId}) AND (team.id = fixture.home_team_id OR team.id = fixture.away_team_id)
        AND team_player.team_id = ${teamId} AND team_player.season_id IN(${seasonIds})
        AND fixture.season_id IN(${seasonIds}) AND competition.id = #{competitionId} 
        AND fixture_player.team_id = #{teamId}
        AND fixture_player.play_time > 0
        <if test="source==true">
            AND fixture_player.groupset_id = #{groupsetId} 
            AND fixture.provider_id = 3
            AND fixture.deleted = 0
        </if>
        <if test="source==false">
            AND fixture.provider_id = 2
        </if>
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        GROUP BY player.id
        ORDER BY fixture.game_date DESC
    </select>

    <!-- ritorna tutti i giocatori di una squadra DAFARE tolto group by player.id -->
    <select id="getAtletiAllByTeam" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team,
        player.id idPlayer,
        player.known_name name,
        player.first_name firstname,
        player.last_name lastname,
        player.born_date borndate,
        position.id position_id,
        position.code position,
        position.desc_it position_desc_it,
        position.desc_en position_desc_en,
        position.desc_fr position_desc_fr,
        position.desc_es position_desc_es,
        position.desc_ru position_desc_ru,
        team_player.jersey_num number,
        SUM(IFNULL(fixture_player.play_time, 0)) playtime,
        GROUP_CONCAT(DISTINCT fixture.competition_id) competition_ids,
        team.id team_id,
        team.logo team_logo,
        team_player.groupset_id teamPlayerGroupsetId,
        player.photo,
        player.height,
        player_agent.name agent_name,
        player.market_value,
        team_player.contract_expires,
        player_agent.id agent_id,
        team_player.season_id
        FROM videomatch.player
        LEFT JOIN player_agent ON player_agent.id = player.player_agent_id
        INNER JOIN videomatch.team_player ON team_player.player_id = player.id
        LEFT JOIN videomatch.fixture_player ON fixture_player.player_id = player.id AND fixture_player.team_id = team_player.team_id
        LEFT JOIN videomatch.fixture ON fixture.id = fixture_player.fixture_id
        INNER JOIN videomatch.team ON team.id = team_player.team_id
        INNER JOIN videomatch.position ON position.id = team_player.position_id
        WHERE team.id = ${teamId}
        AND team_player.team_id = ${teamId} AND team_player.season_id IN(${seasonIds})
        AND fixture.season_id IN(${seasonIds})
        AND team_player.groupset_id IN (-1,${groupId})
        GROUP BY player.id
    </select>
	
    <select id="getAtletiByAgentId" resultMap="resultMapGeneralAtleta" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team,
        <include refid="selectGeneralAtleta"/>
        INNER JOIN team_player_last ON team_player_last.player_id = player.id
        INNER JOIN fixture ON fixture.id = team_player_last.fixture_id
        WHERE player.player_agent_id = ${agentId}
        AND team_player.team_id = team_player_last.team_id AND team_player.season_id = fixture.season_id
        AND fixture.season_id IN (SELECT id FROM season WHERE VISIBLE = 1)
        GROUP BY player.id
    </select>
        
    <!-- ritorna tutti i giocatori selezionati di una partita-->
    <select id="getAtletiSelezionati" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team,
        <include refid="selectAtleta"/>
        ${where}  
    </select>
    
    <select id="getAtleta" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team,
        <include refid="selectAtleta"/>
        WHERE player.id = ${playerId}
        GROUP BY player.id
    </select>
    
    <select id="getAtletiByIds" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team,
        <include refid="selectAtleta"/>
        WHERE player.id IN (${ids})
        GROUP BY player.id
    </select>
	
    <!-- ritorna tutti i giocatori di una partita-->
    <select id="getAtletiByRole" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team,
        <include refid="selectAtleta"/>
        ${where}  
    </select>
	
    <!-- ritorna tutti i giocatori il cui nome conosciuto contiene i caratteri in input -->
    <!--select id="getAtletiByPattern" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        <include refid="select"/>
        where player.known_name LIKE "%${value}%"
        group by player.id
        order by case when player.known_name like '${value}%' then 0 else 1 end, player.known_name
    </select-->
    
    <select id="getAtletiByPattern" resultMap="resultMapJson" parameterType="java.util.TreeMap">
        select distinct player.id idPlayer, player.known_name name, player.first_name firstname, player.last_name lastname, player.born_date borndate,
        player.photo, player.height, season.name season_name
        from player
        INNER JOIN team_player_competition ON team_player_competition.player_id = player.id 
        INNER JOIN season ON season.id = team_player_competition.season_id 
        ${where}
    </select>
    
    <select id="getAtletiPersonalByPattern" resultMap="resultMapJson" parameterType="java.util.TreeMap">
        SELECT IFNULL(player.known_name, player_personal.known_name) name,
        IFNULL(player.first_name, player_personal.first_name) firstname,
        IFNULL(player.last_name, player_personal.last_name) lastname,
        IFNULL(player.photo, player_personal.photo) photo,
        IFNULL(player.born_date, player_personal.born_date) borndate,
        IFNULL(player.height, player_personal.height) height,
        foot.id foot_id,
        foot.desc_it AS foot_it,
        foot.desc_en AS foot_en,
        foot.desc_fr AS foot_fr,
        foot.desc_es AS foot_es,
        foot.desc_ru AS foot_ru,
        position.id position_id,
        position.code position,
        position.desc_it position_desc_it,
        position.desc_en position_desc_en,
        position.desc_fr position_desc_fr,
        position.desc_es position_desc_es,
        position.desc_ru position_desc_ru,
        player_personal.owner_team team,
        player.id idPlayer,
        player_personal.id personalId,
        player_personal.user_id user_id,
        player_personal.groupset_id teamPlayerGroupsetId,
        player_personal.shared,
        user.id user_id,
        CONCAT(user.first_name, " ", user.last_name) user_name
        FROM player_personal
        LEFT JOIN player ON player.id = player_personal.player_id
        LEFT JOIN foot ON foot.id = IFNULL(player.foot_id, player_personal.foot_id)
        LEFT JOIN position ON position.id = player_personal.position_id
        INNER JOIN user ON user.id = player_personal.user_id
        WHERE (
        IFNULL(player.known_name, player_personal.known_name) LIKE "%${pattern}%"
        OR CONCAT(IFNULL(player.first_name, player_personal.first_name), " ", IFNULL(player.last_name, player_personal.last_name)) LIKE "%${pattern}%"
        OR CONCAT(IFNULL(player.last_name, player_personal.last_name), " ", IFNULL(player.first_name, player_personal.first_name)) LIKE "%${pattern}%"
        )
        AND ((player_personal.user_id = ${userId} AND player_personal.groupset_id = ${groupsetId}) OR (player_personal.groupset_id = ${groupsetId} AND player_personal.shared = 1))
    </select>
    
    <select id="getAtletiJerseyAndRoleByFilters" resultMap="resultMapAtleta" parameterType="java.lang.String">
        SELECT player.id idPlayer, team_player.jersey_num number, position.id position_id, position.code position,
        position.desc_it position_desc_it, position.desc_en position_desc_en, position.desc_fr position_desc_fr, position.desc_es position_desc_es, position.desc_ru position_desc_ru
        FROM team_player
        INNER JOIN player ON player.id = team_player.player_id
        INNER JOIN position ON position.id = team_player.position_id AND position.sport_id = 0
        WHERE team_player.season_id IN (${seasonId}) AND team_player.team_id IN (${teamId}) AND team_player.player_id IN (${playerId})
    </select>
    
    <select id="getPositionById" resultMap="resultMapPosition" parameterType="java.lang.Long">
        SELECT position.id position_id, position.code position_code,
        (CASE WHEN position.desc${language} IS NULL THEN position.desc_it ELSE position.desc${language} END) position_description
        FROM position
        WHERE id = ${positionId}
    </select>
    
    <select id="getPositionByIds" resultMap="resultMapPosition" parameterType="java.lang.String">
        SELECT position.id position_id, position.code position_code,
        (CASE WHEN position.desc${language} IS NULL THEN position.desc_it ELSE position.desc${language} END) position_description
        FROM position
        WHERE id IN(${positionId})
    </select>
    
    <select id="getPlayerInLineupAmount" resultType="java.lang.Long" parameterType="java.lang.Long">
        SELECT COUNT(fixture_player.id)
        FROM fixture_player
        INNER JOIN fixture ON fixture_player.fixture_id = fixture.id
        WHERE fixture_player.player_id = ${playerId}
        AND fixture_player.groupset_id = ${groupsetId}
        AND fixture.season_id IN(${seasonIds})
        <![CDATA[
        AND fixture_player.module_position <= 11
        ]]>
        <if test="competitionId != null and competitionId != -1">
            AND fixture.competition_id = ${competitionId}
        </if>
        <if test="teamId != null and teamId != -1">
            AND fixture_player.team_id = ${teamId}
        </if>
        AND fixture.competition_id IN (${allowedCompetitions})
    </select>
    
    <select id="getPlayerModulePositionAmount" resultMap="resultMapAtleta" parameterType="java.util.TreeMap">
        SELECT CASE WHEN fixture.home_team_id = fixture_player.team_id THEN fixture.home_module ELSE fixture.away_module END module,
        fixture_player.module_position - 1 module_position, COUNT(DISTINCT fixture.id) action_amount
        FROM fixture
        INNER JOIN fixture_player ON fixture_player.fixture_id = fixture.id AND fixture_player.player_id = ${playerId} AND fixture_player.groupset_id = -1
        WHERE fixture.season_id IN (${seasonIds})
        <![CDATA[
        AND fixture_player.module_position <= 11
        ]]>
        <if test="competitionId != null and competitionId != -1">
            AND fixture.competition_id = ${competitionId}
        </if>
        <if test="teamId != null and teamId != -1">
            AND fixture_player.team_id = ${teamId}
        </if>
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        <if test="from != null">
        <![CDATA[
            AND fixture.game_date >= STR_TO_DATE("${from}", "%d/%m/%Y")
        ]]>
        </if>
        <if test="to != null">
        <![CDATA[
            AND fixture.game_date <= STR_TO_DATE("${to}", "%d/%m/%Y")
        ]]>
        </if>
        AND fixture.competition_id IN (${allowedCompetitions})
        GROUP BY module, fixture_player.module_position
        ORDER BY action_amount DESC
    </select>
    
    <update id="updatePlayerPhoto" parameterType="java.util.TreeMap">
        UPDATE player SET photo = "${photo}"
        WHERE id = ${playerId}
    </update>
    
    <select id="getPlayerPersonalAll" parameterType="java.util.TreeMap" resultMap="resultMapAtleta">
        SELECT IFNULL(player.known_name, player_personal.known_name) name,
        IFNULL(player.first_name, player_personal.first_name) firstname,
        IFNULL(player.last_name, player_personal.last_name) lastname,
        IFNULL(player.photo, player_personal.photo) photo,
        IFNULL(player.born_date, player_personal.born_date) borndate,
        IFNULL(player.height, player_personal.height) height,
        foot.id foot_id,
        foot.desc_it AS foot_it,
        foot.desc_en AS foot_en,
        foot.desc_fr AS foot_fr,
        foot.desc_es AS foot_es,
        foot.desc_ru AS foot_ru,
        position.id position_id,
        position.code position,
        position.desc_it position_desc_it,
        position.desc_en position_desc_en,
        position.desc_fr position_desc_fr,
        position.desc_es position_desc_es,
        position.desc_ru position_desc_ru,
        player_personal.owner_team team,
        player.id idPlayer,
        player_personal.id personalId,
        player_personal.user_id user_id,
        player_personal.groupset_id teamPlayerGroupsetId,
        player_personal.shared,
        user.id user_id,
        CONCAT(user.first_name, " ", user.last_name) user_name
        FROM player_personal
        LEFT JOIN player ON player.id = player_personal.player_id
        LEFT JOIN foot ON foot.id = IFNULL(player.foot_id, player_personal.foot_id)
        LEFT JOIN position ON position.id = player_personal.position_id
        INNER JOIN user ON user.id = player_personal.user_id
        WHERE ((player_personal.user_id = ${userId} AND player_personal.groupset_id = ${groupsetId}) OR (player_personal.groupset_id = ${groupsetId} AND shared = 1))
        <if test="all==false">
            AND player_personal.player_id IS NULL
        </if>
        ORDER BY player_personal.player_id
    </select>
    
    <select id="getPlayerPersonal" parameterType="java.util.TreeMap" resultMap="resultMapAtleta">
        SELECT IFNULL(player.known_name, player_personal.known_name) name,
        IFNULL(player.first_name, player_personal.first_name) firstname,
        IFNULL(player.last_name, player_personal.last_name) lastname,
        IFNULL(player.photo, player_personal.photo) photo,
        IFNULL(player.born_date, player_personal.born_date) borndate,
        IFNULL(player.height, player_personal.height) height,
        foot.id foot_id,
        foot.desc_it AS foot_it,
        foot.desc_en AS foot_en,
        foot.desc_fr AS foot_fr,
        foot.desc_es AS foot_es,
        foot.desc_ru AS foot_ru,
        position.id position_id,
        position.code position,
        position.desc_it position_desc_it,
        position.desc_en position_desc_en,
        position.desc_fr position_desc_fr,
        position.desc_es position_desc_es,
        position.desc_ru position_desc_ru,
        player_personal.owner_team team,
        player.id idPlayer,
        player_personal.id personalId,
        player_personal.user_id user_id,
        player_personal.groupset_id teamPlayerGroupsetId,
        player_personal.shared,
        user.id user_id,
        CONCAT(user.first_name, " ", user.last_name) user_name
        FROM player_personal
        LEFT JOIN player ON player.id = player_personal.player_id
        LEFT JOIN foot ON foot.id = IFNULL(player.foot_id, player_personal.foot_id)
        LEFT JOIN position ON position.id = player_personal.position_id
        INNER JOIN user ON user.id = player_personal.user_id
        WHERE player_personal.id = ${playerId}
        AND ((player_personal.user_id = ${userId} AND player_personal.groupset_id = ${groupsetId}) OR (player_personal.groupset_id = ${groupsetId} AND player_personal.shared = 1))
    </select>
    
    <select id="getPlayerPersonalByIds" parameterType="java.util.TreeMap" resultMap="resultMapAtleta">
        SELECT IFNULL(player.known_name, player_personal.known_name) name,
        IFNULL(player.first_name, player_personal.first_name) firstname,
        IFNULL(player.last_name, player_personal.last_name) lastname,
        IFNULL(player.photo, player_personal.photo) photo,
        IFNULL(player.born_date, player_personal.born_date) borndate,
        IFNULL(player.height, player_personal.height) height,
        foot.id foot_id,
        foot.desc_it AS foot_it,
        foot.desc_en AS foot_en,
        foot.desc_fr AS foot_fr,
        foot.desc_es AS foot_es,
        foot.desc_ru AS foot_ru,
        position.id position_id,
        position.code position,
        position.desc_it position_desc_it,
        position.desc_en position_desc_en,
        position.desc_fr position_desc_fr,
        position.desc_es position_desc_es,
        position.desc_ru position_desc_ru,
        player_personal.owner_team team,
        player.id idPlayer,
        player_personal.id personalId,
        player_personal.user_id user_id,
        player_personal.groupset_id teamPlayerGroupsetId,
        player_personal.shared,
        user.id user_id,
        CONCAT(user.first_name, " ", user.last_name) user_name
        FROM player_personal
        LEFT JOIN player ON player.id = player_personal.player_id
        LEFT JOIN foot ON foot.id = IFNULL(player.foot_id, player_personal.foot_id)
        LEFT JOIN position ON position.id = player_personal.position_id
        INNER JOIN user ON user.id = player_personal.user_id
        WHERE player_personal.id IN (${ids})
        AND ((player_personal.user_id = ${userId} AND player_personal.groupset_id = ${groupsetId}) OR (player_personal.groupset_id = ${groupsetId} AND player_personal.shared = 1))
    </select>
    
    <select id="getPlayerPersonalByPlayerId" parameterType="java.util.TreeMap" resultMap="resultMapAtleta">
        SELECT IFNULL(player.known_name, player_personal.known_name) name,
        IFNULL(player.first_name, player_personal.first_name) firstname,
        IFNULL(player.last_name, player_personal.last_name) lastname,
        IFNULL(player.photo, player_personal.photo) photo,
        IFNULL(player.born_date, player_personal.born_date) borndate,
        IFNULL(player.height, player_personal.height) height,
        foot.id foot_id,
        foot.desc_it AS foot_it,
        foot.desc_en AS foot_en,
        foot.desc_fr AS foot_fr,
        foot.desc_es AS foot_es,
        foot.desc_ru AS foot_ru,
        position.id position_id,
        position.code position,
        position.desc_it position_desc_it,
        position.desc_en position_desc_en,
        position.desc_fr position_desc_fr,
        position.desc_es position_desc_es,
        position.desc_ru position_desc_ru,
        player_personal.owner_team team,
        player.id idPlayer,
        player_personal.id personalId,
        player_personal.user_id user_id,
        player_personal.groupset_id teamPlayerGroupsetId,
        player_personal.shared,
        user.id user_id,
        CONCAT(user.first_name, " ", user.last_name) user_name
        FROM player_personal
        LEFT JOIN player ON player.id = player_personal.player_id
        LEFT JOIN foot ON foot.id = IFNULL(player.foot_id, player_personal.foot_id)
        LEFT JOIN position ON position.id = player_personal.position_id
        INNER JOIN user ON user.id = player_personal.user_id
        WHERE player_personal.player_id = ${playerId}
        AND ((player_personal.user_id = ${userId} AND player_personal.groupset_id = ${groupsetId}) OR (player_personal.groupset_id = ${groupsetId} AND player_personal.shared = 1))
    </select>
    
    <select id="insertPersonalPlayer" parameterType="java.util.TreeMap" resultType="java.lang.Long">
        SELECT addPersonalPlayer("${firstName}", "${lastName}", ${knownName}, ${photo}, ${bornDate},
        ${height}, ${footId}, ${positionId}, ${ownerTeam}, ${playerId}, ${userId}, ${groupsetId}, ${shared}) last_insert_id
    </select>
    
    <update id="updatePersonalPlayer" parameterType="java.util.TreeMap">
        UPDATE player_personal SET first_name = "${firstName}",
        last_name = "${lastName}",
        known_name = ${knownName},
        photo = ${photo},
        born_date = CASE WHEN ${bornDate} IS NULL THEN NULL ELSE STR_TO_DATE(${bornDate}, "%Y-%m-%d") END,
        height = ${height},
        foot_id = ${footId},
        position_id = ${positionId},
        owner_team = ${ownerTeam},
        player_id = ${playerId},
        shared = ${shared}
        WHERE id = ${id}
    </update>
    
    <update id="deletePersonalPlayer" parameterType="java.util.TreeMap">
        DELETE FROM player_personal WHERE id = ${id}
    </update>

</mapper>
