<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="PlaylistTV">

    <resultMap  id="resultMapPlaylistTV"        type="PlaylistTV">
        <id     property="id"                   column="playlist_tv_id"/>
        <result property="userId"               column="playlist_tv_user_id"/>
        <result property="groupsetId"           column="playlist_tv_groupset_id"/>
        <result property="name"                 column="playlist_tv_name"/>
        <result property="description"          column="playlist_tv_description"/>
        <result property="date"                 column="playlist_tv_date"/>
        <result property="updateBy"             column="playlist_tv_update_by"/>
        <result property="updateDate"           column="playlist_tv_update_date"/>
        <result property="updateDescription"    column="playlist_tv_update_description"/>
        <result property="clipAmount"           column="playlist_tv_clip_amount"/>
        <result property="shared"               column="playlist_tv_shared"/>
    </resultMap>
    
    <resultMap  id="resultMapPlaylistTVEvent"           type="PlaylistTVEvent">
        <id     property="id"                           column="playlist_tv_event_id"/>
        <result property="playlistId"                   column="playlist_tv_event_playlistId"/>
        <result property="eventId"                      column="playlist_tv_event_eventId"/>
        <result property="event_type_code"              column="playlist_tv_event_event_type_code"/>
        <result property="event_period_start_msec"      column="playlist_tv_event_event_period_start_msec"/>
        <result property="event_period_end_msec"        column="playlist_tv_event_event_period_end_msec"/>
        <result property="event_period_start_second"    column="playlist_tv_event_event_period_start_second"/>
        <result property="event_period_end_second"      column="playlist_tv_event_event_period_end_second"/>
        <result property="event_period_start_minute"    column="playlist_tv_event_event_period_start_minute"/>
        <result property="event_period_end_minute"      column="playlist_tv_event_event_period_end_minute"/>
        <result property="event_period_id"              column="playlist_tv_event_event_period_id"/>
        <result property="event_note"                   column="playlist_tv_event_event_note"/>
        <result property="event_xml_idevent"            column="playlist_tv_event_event_xml_idevent"/>
        <result property="event_fixture_id"             column="playlist_tv_event_event_fixture_id"/>
        <result property="event_tactical_start_msec"    column="playlist_tv_event_event_tactical_start_msec"/>
        <result property="event_groupset_id"            column="playlist_tv_event_event_groupset_id"/>
        <result property="event_team_id"                column="playlist_tv_event_event_team_id"/>
        <result property="event_result"                 column="playlist_tv_event_event_result"/>
        <result property="event_author"                 column="playlist_tv_event_event_author"/>
        <result property="event_type_desc"              column="playlist_tv_event_event_type_desc"/>
        <result property="event_type_desc_en"           column="playlist_tv_event_event_type_desc_en"/>
        <result property="event_type_desc_fr"           column="playlist_tv_event_event_type_desc_fr"/>
        <result property="event_type_desc_es"           column="playlist_tv_event_event_type_desc_es"/>
        <result property="event_type_desc_ru"           column="playlist_tv_event_event_type_desc_ru"/>
        <result property="tag_type_code"                column="playlist_tv_event_tag_type_code"/>
        <result property="tag_type_desc"                column="playlist_tv_event_tag_type_desc"/>
        <result property="tag_type_desc_en"             column="playlist_tv_event_tag_type_desc_en"/>
        <result property="tag_type_desc_fr"             column="playlist_tv_event_tag_type_desc_fr"/>
        <result property="tag_type_desc_es"             column="playlist_tv_event_tag_type_desc_es"/>
        <result property="tag_type_desc_ru"             column="playlist_tv_event_tag_type_desc_ru"/>
        <result property="team_name"                    column="playlist_tv_event_team_name"/>
        <result property="player_know_name"             column="playlist_tv_event_player_know_name"/>
        <result property="panel_name"                   column="playlist_tv_event_panel_name"/>
        <result property="isTactical"                   column="playlist_tv_event_istactical"/>
        <result property="order"                        column="playlist_tv_event_order"/>
        <result property="deleted"                      column="playlist_tv_event_deleted"/>
        <result property="updateBy"                     column="playlist_tv_event_update_by"/>
        <result property="updateDate"                   column="playlist_tv_event_update_date"/>
        <result property="updateDescription"            column="playlist_tv_event_update_description"/>
    </resultMap>
    
    <resultMap  id="resultMapPlaylistTVShare"   type="PlaylistTVShare">
        <id     property="id"                   column="playlist_tv_share_id"/>
        <result property="userId"               column="playlist_tv_share_user_id"/>
        <result property="playlistId"           column="playlist_tv_share_playlist_id"/>
        <result property="editable"             column="playlist_tv_share_editable"/>
        <result property="played"               column="playlist_tv_share_played"/>
    </resultMap>
    
    <resultMap  id="resultMapPlaylistTVSharePublic"     type="PlaylistTVSharePublic">
        <id     property="id"                           column="playlist_tv_share_public_id"/>
        <result property="playlistId"                   column="playlist_tv_share_public_playlist_id"/>
        <result property="link"                         column="playlist_tv_share_public_link"/>
        <result property="click"                        column="playlist_tv_share_public_click"/>
        <result property="maxClick"                     column="playlist_tv_share_public_max_click"/>
        <result property="userId"                       column="playlist_tv_share_public_user_id"/>
    </resultMap>
    
    <resultMap  id="resultMapPlaylistTVNewId"   type="java.lang.Long">
        <result property="last_insert_id"       column="last_insert_id"/>
    </resultMap>

    <!-- STATIC SECTION --> 
    <sql id="selectAllFields">
        SELECT playlist_tv.id playlist_tv_id, playlist_tv.user_id playlist_tv_user_id, playlist_tv.groupset_id playlist_tv_groupset_id,
        playlist_tv.name playlist_tv_name, playlist_tv.description playlist_tv_description, playlist_tv.date playlist_tv_date, 
        playlist_tv.update_by playlist_tv_update_by, playlist_tv.update_date playlist_tv_update_date, playlist_tv.update_description playlist_tv_update_description
        FROM playlist_tv
    </sql>
    <sql id="selectAllFieldsEvent">
        SELECT playlist_tv_event.id playlist_tv_event_id,
        playlist_tv_event.playlist_id playlist_tv_event_playlistId,
        playlist_tv_event.event_id playlist_tv_event_eventId,
        playlist_tv_event.event_type_code playlist_tv_event_event_type_code,
        playlist_tv_event.event_period_start_msec playlist_tv_event_event_period_start_msec,
        playlist_tv_event.event_period_end_msec playlist_tv_event_event_period_end_msec,
        playlist_tv_event.event_period_start_second playlist_tv_event_event_period_start_second,
        playlist_tv_event.event_period_end_second playlist_tv_event_event_period_end_second,
        playlist_tv_event.event_period_start_minute playlist_tv_event_event_period_start_minute,
        playlist_tv_event.event_period_end_minute playlist_tv_event_event_period_end_minute,
        playlist_tv_event.event_period_id playlist_tv_event_event_period_id,
        playlist_tv_event.event_note playlist_tv_event_event_note,
        playlist_tv_event.event_xml_idevent playlist_tv_event_event_xml_idevent,
        playlist_tv_event.event_fixture_id playlist_tv_event_event_fixture_id,
        playlist_tv_event.event_tactical_start_msec playlist_tv_event_event_tactical_start_msec,
        playlist_tv_event.event_groupset_id playlist_tv_event_event_groupset_id,
        playlist_tv_event.event_team_id playlist_tv_event_event_team_id,
        playlist_tv_event.event_result playlist_tv_event_event_result,
        playlist_tv_event.event_author playlist_tv_event_event_author,
        playlist_tv_event.event_type_desc playlist_tv_event_event_type_desc,
        playlist_tv_event.event_type_desc_en playlist_tv_event_event_type_desc_en,
        playlist_tv_event.event_type_desc_fr playlist_tv_event_event_type_desc_fr,
        playlist_tv_event.event_type_desc_es playlist_tv_event_event_type_desc_es,
        playlist_tv_event.event_type_desc_ru playlist_tv_event_event_type_desc_ru,
        playlist_tv_event.tag_type_code playlist_tv_event_tag_type_code,
        playlist_tv_event.tag_type_desc playlist_tv_event_tag_type_desc,
        playlist_tv_event.tag_type_desc_en playlist_tv_event_tag_type_desc_en,
        playlist_tv_event.tag_type_desc_fr playlist_tv_event_tag_type_desc_fr,
        playlist_tv_event.tag_type_desc_es playlist_tv_event_tag_type_desc_es,
        playlist_tv_event.tag_type_desc_ru playlist_tv_event_tag_type_desc_ru,
        playlist_tv_event.team_name playlist_tv_event_team_name,
        playlist_tv_event.player_know_name playlist_tv_event_player_know_name,
        playlist_tv_event.panel_name playlist_tv_event_panel_name,
        playlist_tv_event.istactical playlist_tv_event_istactical,
        playlist_tv_event.order playlist_tv_event_order,
        playlist_tv_event.deleted playlist_tv_event_deleted,
        playlist_tv_event.update_by playlist_tv_event_update_by,
        playlist_tv_event.update_date playlist_tv_event_update_date,
        playlist_tv_event.update_description playlist_tv_event_update_description
        FROM playlist_tv_event
    </sql>
    <sql id="selectAllFieldsShare">
        SELECT playlist_tv_share.id playlist_tv_share_id, playlist_tv_share.user_id playlist_tv_share_user_id, playlist_tv_share.playlist_id playlist_tv_share_playlist_id,
        playlist_tv_share.editable playlist_tv_share_editable, playlist_tv_share.played playlist_tv_share_played
        FROM playlist_tv_share
    </sql>
    <sql id="selectAllFieldsSharePublic">
        SELECT playlist_tv_share_public.id playlist_tv_share_public_id, playlist_tv_share_public.playlist_id playlist_tv_share_public_playlist_id,
        playlist_tv_share_public.link playlist_tv_share_public_link, playlist_tv_share_public.click playlist_tv_share_public_click,
        playlist_tv_share_public.max_click playlist_tv_share_public_max_click, playlist_tv_share_public.user_id playlist_tv_share_public_user_id
        FROM playlist_tv_share_public
    </sql>
    <!-- STATIC SECTION --> 



    <!-- INSERT SECTION --> 
    <select id="addPlaylist" parameterType="PlaylistTV" resultMap="resultMapPlaylistTVNewId">
        SELECT addPlaylistTv_v2(#{userId}, #{groupsetId}, #{name}, #{description}, #{date}) last_insert_id
    </select>
    
    <insert id="addPlaylistEvent" parameterType="java.lang.String">
        INSERT IGNORE INTO playlist_tv_event (playlist_id, event_id, event_type_code, event_period_start_msec, event_period_end_msec,
        event_period_start_second, event_period_end_second, event_period_start_minute, event_period_end_minute, event_period_id, event_note,
        event_xml_idevent, event_fixture_id, event_tactical_start_msec, event_groupset_id, event_team_id, event_result, event_author,
        event_type_desc, event_type_desc_en, event_type_desc_fr, event_type_desc_es, event_type_desc_ru, tag_type_code, tag_type_desc, tag_type_desc_en,
        tag_type_desc_fr, tag_type_desc_es, tag_type_desc_ru, team_name, player_know_name, panel_name,
        istactical, `order`, update_by, update_date, update_description)
        ${values}
    </insert>
    <insert id="addPlaylistTVShare" parameterType="java.util.TreeMap">
        INSERT IGNORE INTO playlist_tv_share (user_id, playlist_id, editable)
        VALUES (${userId}, ${playlistId}, ${editable})
    </insert>
    <insert id="addPlaylistTVSharePublic" parameterType="java.util.TreeMap">
        INSERT IGNORE INTO playlist_tv_share_public (playlist_id, link, click, max_click, user_id)
        VALUES (${playlistId}, "${link}", ${click}, ${maxClick}, ${userId})
    </insert>
    <!-- INSERT SECTION --> 
    
    
    
    <!-- DELETE SECTION --> 
    <delete id = "deletePlaylistById" parameterType="java.util.TreeMap">
        DELETE FROM playlist_tv WHERE id = ${playlistId};
    </delete>
    <delete id = "deletePlaylistEvents" parameterType="java.util.TreeMap">
        UPDATE playlist_tv_event SET deleted = 1, update_by = ${updateBy}, update_date = "${updateDate}",
        update_description = "${updateDescription}" WHERE playlist_id = ${playlistId} AND event_id IN(${eventIds});
    </delete>
    <delete id = "permanentlyDeletePlaylistEvents" parameterType="java.util.TreeMap">
        DELETE FROM playlist_tv_event WHERE playlist_id = ${playlistId} AND event_id IN(${eventIds});
    </delete>
    <delete id = "deletePlaylistShare" parameterType="java.util.TreeMap">
        DELETE FROM playlist_tv_share WHERE playlist_id = ${playlistId};
    </delete>
    <delete id = "deletePlaylistSharePublic" parameterType="java.util.TreeMap">
        DELETE FROM playlist_tv_share_public WHERE id = ${id};
    </delete>
    <!-- DELETE SECTION --> 
    
    
    
    <!-- UPDATE SECTION --> 
    <update id = "updatePlaylist" parameterType="PlaylistTV">
        UPDATE playlist_tv SET name = #{name}, description = #{description}, groupset_id = #{groupsetId},
        update_by = #{updateBy}, update_date = #{updateDate}, update_description = #{updateDescription}
        WHERE id = #{id} AND user_id = #{userId}
    </update>
    <update id = "updatePlaylistShare" parameterType="PlaylistTV">
        UPDATE playlist_tv_share SET played = #{played}
        WHERE id = #{id} AND user_id = #{userId} AND playlist_id = #{playlistId}
    </update>
    <update id = "updatePlaylistEvent" parameterType="PlaylistTVEvent">
        UPDATE playlist_tv_event SET event_note = #{note},
        event_period_start_msec = #{startMsec}, event_period_end_msec = #{endMsec},
        event_period_start_second = #{startSec}, event_period_end_second = #{endSec},
        event_period_start_minute = #{startMin}, event_period_end_minute = #{endMin},
        event_tactical_start_msec = #{tactStartMsec}, playlist_tv_event.order = #{order},
        playlist_tv_event.deleted = #{deleted}, update_by = #{updateBy}, update_date = #{updateDate},
        update_description = #{updateDescription}
        WHERE playlist_id = #{playlistId} AND event_id = #{eventId}
    </update>
    <update id = "updatePlaylistTvSharePublicClick" parameterType="java.lang.Long">
        UPDATE playlist_tv_share_public SET click = #{click}
        WHERE id = #{id}
    </update>
    <!-- UPDATE SECTION --> 
    
    
    
    <!-- SELECT SECTION --> 
    <select id="getPlaylistById" resultMap="resultMapPlaylistTV" parameterType="java.lang.Long">
        <include refid="selectAllFields"/>
        WHERE id = #{playlistId}
    </select>
    <select id="getFirstFivePlaylist" resultMap="resultMapPlaylistTV" parameterType="java.lang.Long">
        <include refid="selectAllFields"/>
        WHERE ((user_id = #{userId} AND groupset_id = #{groupsetId}) OR (user_id = #{userId} AND groupset_id IS NULL))
    </select>
    <select id="getFirstFivePlaylistWithCounter" resultMap="resultMapPlaylistTV" parameterType="java.lang.Long">
        SELECT *, COUNT(*) playlist_tv_clip_amount FROM (
        <include refid="selectAllFields"/>
        LEFT JOIN playlist_tv_event ON playlist_tv.id = playlist_tv_event.playlist_id
        WHERE user_id = #{userId} AND groupset_id = #{groupsetId} AND IFNULL(deleted, 0) != 1) tmp
        GROUP BY playlist_tv_id
    </select>
    
    <select id="getPlaylistEventsById" resultMap="resultMapPlaylistTVEvent" parameterType="java.lang.Long">
        <include refid="selectAllFieldsEvent"/>
        WHERE playlist_id = #{playlistId} AND IFNULL(deleted, 0) != 1
        ORDER BY playlist_tv_event.order
    </select>
    <select id="getPlaylistEventsDeletedById" resultMap="resultMapPlaylistTVEvent" parameterType="java.lang.Long">
        <include refid="selectAllFieldsEvent"/>
        WHERE playlist_id = #{playlistId} AND IFNULL(deleted, 0) = 1
        ORDER BY playlist_tv_event.order
    </select>
    <select id="getLastUpdateEventById" resultMap="resultMapPlaylistTVEvent" parameterType="java.lang.Long">
        <include refid="selectAllFieldsEvent"/>
        WHERE playlist_id = #{playlistId}
        ORDER BY playlist_tv_event.update_date DESC
        LIMIT 1
    </select>
    
    <select id="getAllSharedByUserId" resultMap="resultMapPlaylistTVShare" parameterType="java.util.TreeMap">
        <include refid="selectAllFieldsShare"/>
        INNER JOIN playlist_tv ON playlist_tv_share.playlist_id = playlist_tv.id
        WHERE playlist_tv.user_id = ${userId} AND playlist_tv.groupset_id = ${groupsetId}
    </select>
    
    <select id="getAllShareByUserId" resultMap="resultMapPlaylistTV" parameterType="java.util.TreeMap">
        SELECT playlist_tv.id playlist_tv_id, playlist_tv.user_id playlist_tv_user_id, playlist_tv.groupset_id playlist_tv_groupset_id,
        playlist_tv.name playlist_tv_name, playlist_tv.description playlist_tv_description, playlist_tv.date playlist_tv_date,
        playlist_tv.update_by playlist_tv_update_by, playlist_tv.update_date playlist_tv_update_date, playlist_tv.update_description playlist_tv_update_description,
        1 playlist_tv_shared
        FROM playlist_tv
        LEFT JOIN playlist_tv_share ON playlist_tv_share.playlist_id = playlist_tv.id
        INNER JOIN user shareowner ON shareowner.id = playlist_tv.user_id
        WHERE playlist_tv.groupset_id = ${groupsetId} AND shareowner.groupset_id = ${groupsetId} AND shareowner.id != ${userId} AND playlist_tv_share.user_id = ${userId}
    </select>
    <select id="getAllSharedByUserIdAndPlaylistId" resultMap="resultMapPlaylistTVShare" parameterType="java.util.TreeMap">
        <include refid="selectAllFieldsShare"/>
        INNER JOIN playlist_tv ON playlist_tv_share.playlist_id = playlist_tv.id
        WHERE playlist_tv_share.user_id = ${userId} AND playlist_tv.groupset_id = ${groupsetId} AND playlist_tv_share.playlist_id = ${playlistId}
    </select>
    <select id="getAllSharedToUserId" resultMap="resultMapPlaylistTVShare" parameterType="java.util.TreeMap">
        <include refid="selectAllFieldsShare"/>
        INNER JOIN playlist_tv ON playlist_tv_share.playlist_id = playlist_tv.id
        WHERE playlist_tv_share.user_id = ${userId} AND playlist_tv.groupset_id = ${groupsetId}
    </select>
    <select id="getAllShareByUserIdWithCounter" resultMap="resultMapPlaylistTV" parameterType="java.util.TreeMap">
        SELECT *, COUNT(*) playlist_tv_clip_amount FROM (
        SELECT playlist_tv.id playlist_tv_id, playlist_tv.user_id playlist_tv_user_id, playlist_tv.groupset_id playlist_tv_groupset_id,
        playlist_tv.name playlist_tv_name, playlist_tv.description playlist_tv_description, playlist_tv.date playlist_tv_date,
        playlist_tv.update_by playlist_tv_update_by, playlist_tv.update_date playlist_tv_update_date, playlist_tv.update_description playlist_tv_update_description,
        1 playlist_tv_shared
        FROM playlist_tv
        LEFT JOIN playlist_tv_share ON playlist_tv_share.playlist_id = playlist_tv.id
        LEFT JOIN playlist_tv_event ON playlist_tv.id = playlist_tv_event.playlist_id
        INNER JOIN user shareowner ON shareowner.id = playlist_tv.user_id
        WHERE playlist_tv.groupset_id = ${groupsetId} AND shareowner.groupset_id = ${groupsetId} AND shareowner.id != ${userId} AND playlist_tv_share.user_id = ${userId} AND IFNULL(playlist_tv_event.deleted, 0) != 1) tmp
        GROUP BY playlist_tv_id
    </select>
    <select id="getAllSharingByUserId" resultMap="resultMapPlaylistTV" parameterType="java.util.TreeMap">
        SELECT playlist_tv.id playlist_tv_id, playlist_tv.user_id playlist_tv_user_id, playlist_tv.groupset_id playlist_tv_groupset_id,
        playlist_tv.name playlist_tv_name, playlist_tv.description playlist_tv_description, playlist_tv.date playlist_tv_date,
        playlist_tv.update_by playlist_tv_update_by, playlist_tv.update_date playlist_tv_update_date, playlist_tv.update_description playlist_tv_update_description,
        1 playlist_tv_shared
        FROM playlist_tv
        INNER JOIN playlist_tv_share ON playlist_tv_share.playlist_id = playlist_tv.id
        INNER JOIN user shareowner ON shareowner.id = playlist_tv.user_id
        WHERE playlist_tv.groupset_id = ${groupsetId} AND shareowner.groupset_id = ${groupsetId} AND shareowner.id = ${userId}
    </select>
    <select id="getPublicLinkByUrl" resultMap="resultMapPlaylistTVSharePublic" parameterType="java.util.TreeMap">
        <include refid="selectAllFieldsSharePublic"/>
        WHERE playlist_tv_share_public.link = "${link}"
    </select>
    <select id="getPublicLinkSharedByUserIdAndPlaylistId" resultMap="resultMapPlaylistTVSharePublic" parameterType="java.util.TreeMap">
        <include refid="selectAllFieldsSharePublic"/>
        WHERE playlist_tv_share_public.user_id = ${userId} AND playlist_tv_share_public.playlist_id = ${playlistId}
    </select>
    <select id="getAllPublicShareByUserId" resultMap="resultMapPlaylistTVSharePublic" parameterType="java.util.TreeMap">
        <include refid="selectAllFieldsSharePublic"/>
        WHERE playlist_tv_share_public.user_id = ${userId}
    </select>
    <!-- SELECT SECTION --> 
</mapper>