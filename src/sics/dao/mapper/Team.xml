<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Team">

    <resultMap id="resultMapTeam"                       type="Team">
        <id property="id"                               column="team_id"/>
        <result property="mainTeamId"                   column="main_team_id" />
        <result property="name"                         column="team_name" />
        <result property="logo"                         column="team_logo" />
        <result property="countryId"                    column="country_id" />
        <result property="color"                        column="team_color" />
        <result property="nationalTeam"                 column="team_national_team" />
        <result property="groupsetId"                   column="groupset_id" />
        
        <result property="seasonName"                   column="season_name" />
        <result property="countryName"                  column="country_name" />
        
        <result property="playerId"                     column="player_id" />
        <result property="playerPositionId"             column="player_position_id"/>
        <result property="playerRoleDescription"        column="player_position_description"/>
        <result property="playerDetailRoleDescription"  column="player_position_detail_description"/>
        <result property="playerFootId"                 column="player_foot_id"/>
        <result property="playerFoot"                   column="player_foot"/>
        <result property="groupId"                      column="group_id" />
        <result property="groupName"                    column="group_name" />
        <result property="groupOrder"                   column="group_order" />
    </resultMap>
	
    <select id="getTeamAll" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        from team
        order by team.name
    </select>

    <select id="getTeam" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        from team
        where team.id=#{where}
    </select>
    
    <select id="getTeams" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        from team
        where team.id IN(${ids})
    </select>
	
    <!-- ricavo i team disponibili a partire dalle partite relative ai campionati abilitati sul groupset e verifico siano partite sics e NON CONDIVISE
    2022/02 Edit includo partite condivise con video e non eliminate per sezione "Personale" di SICS.tv
    -->
    <select id="getTeamByGroupsetId" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM fixture INNER JOIN team ON team.id = fixture.home_team_id
        WHERE fixture.competition_id IN (${allowedCompetitions}) AND fixture.deleted = 0 AND fixture.video_name IS NOT NULL AND fixture.season_id IN (${seasonIds})
        UNION
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM fixture INNER JOIN team ON team.id = fixture.away_team_id
        WHERE fixture.competition_id IN (${allowedCompetitions}) AND fixture.deleted = 0 AND fixture.video_name IS NOT NULL AND fixture.season_id IN (${seasonIds})
        ORDER BY team_name
    </select>
    
    <select id="getNationalTeamByGroupsetId" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM fixture 
        INNER JOIN team ON team.id = fixture.home_team_id 
        WHERE fixture.competition_id IN (${allowedCompetitions})
        AND fixture.video_name IS NOT NULL 
        AND team.national_team=1 
        AND fixture.deleted=0 
        AND fixture.video_name IS NOT NULL 
        AND provider_id!=3
        AND fixture.season_id IN(SELECT id FROM season WHERE visible = 1)
        AND team.country_id=${idCountry} 
        UNION
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM fixture 
        INNER JOIN team ON team.id = fixture.away_team_id 
        WHERE fixture.competition_id IN (${allowedCompetitions})
        AND fixture.video_name IS NOT NULL 
        AND team.national_team=1 
        AND fixture.deleted=0 
        AND fixture.video_name IS NOT NULL 
        AND provider_id!=3
        AND fixture.season_id IN(SELECT id FROM season WHERE visible = 1)
        AND team.country_id=${idCountry} 
        ORDER BY team_name
    </select>
    
    <select id="getNationalTeam" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.order team_order, team.name team_name_it,
        team.color team_color, team.national_team team_national_team, team.groupset_id
        FROM fixture 
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN sport ON competition.sport_id = sport.id
        INNER JOIN team ON team.id = fixture.home_team_id 
        INNER JOIN game ON fixture.id = game.fixture_id
        WHERE sport.id=${idSport} 
        AND team.national_team=1 
        AND fixture.season_id IN(${seasonIds})
        AND provider_id!=3
        AND game.groupset_id=-1  
        AND fixture.video_name IS NOT NULL 
        AND team.country_id=${idCountry} 
        UNION
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.order team_order, team.name team_name_it,
        team.color team_color, team.national_team team_national_team, team.groupset_id
        FROM fixture 
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN sport ON competition.sport_id = sport.id
        INNER JOIN team ON team.id = fixture.away_team_id 
        INNER JOIN game ON fixture.id = game.fixture_id
        WHERE sport.id=${idSport} 
        AND team.national_team=1 
        AND fixture.season_id IN(${seasonIds})
        AND provider_id!=3
        AND game.groupset_id=-1 
        AND fixture.video_name IS NOT NULL 
        AND team.country_id=${idCountry} 
        ORDER BY team_order, team_name_it DESC
    </select>
	
    <!-- ottengo i team in base alla competizione e alla stagione a partire dalle partite e verificando che  -->
    <select id="getTeamByCompetition" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM fixture 
        INNER JOIN team ON team.id = fixture.home_team_id 
        WHERE fixture.competition_id = ${compId} 
        AND fixture.competition_id IN (${allowedCompetitions}) 
        AND fixture.video_name IS NOT NULL 
        AND fixture.season_id IN(${seasonIds})
        AND team.sport_id = ${sportId} 
        <if test="source==false">
            AND team.groupset_id = -1 
            AND provider_id != 3 
        </if>
        <if test="source==true">
            AND provider_id = 3 
            AND fixture.deleted = 0 
            AND fixture.video_name IS NOT NULL 
        </if>
        <if test="countryId != null">
            AND team.country_id = ${countryId}
        </if>
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        UNION
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM fixture 
        INNER JOIN team ON team.id = fixture.away_team_id 
        WHERE fixture.competition_id = ${compId} 
        AND fixture.season_id IN(${seasonIds})
        AND fixture.competition_id IN (${allowedCompetitions}) 
        AND fixture.video_name IS NOT NULL 
        AND team.sport_id = ${sportId} 
        <if test="source==false">
            AND team.groupset_id = -1 
            AND provider_id != 3 
        </if>
        <if test="source==true">
            AND provider_id = 3 
            AND fixture.deleted=0 
            AND fixture.video_name IS NOT NULL 
        </if>
        <if test="countryId != null">
            AND team.country_id = ${countryId}
        </if>
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        ORDER BY team_name 
    </select>
        
    <!--NON USATA-->
    <select id="getTeamByCompetitionPersonal" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM fixture INNER JOIN team ON team.id = fixture.home_team_id INNER JOIN game ON fixture.id = game.fixture_id
        WHERE fixture.competition_id = #{compId} and fixture.season_id IN(${seasonIds}) and game.groupset_id = #{idGro} and team.sport_id = #{sportId} and fixture.provider_id = 3 
        AND fixture.deleted = 0
        UNION
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM fixture INNER JOIN team ON team.id = fixture.away_team_id INNER JOIN game ON fixture.id = game.fixture_id
        WHERE fixture.competition_id = #{compId} and fixture.season_id IN(${seasonIds}) and game.groupset_id = #{idGro} and team.sport_id = #{sportId} and fixture.provider_id = 3 
        AND fixture.deleted = 0
        order by team_name 
    </select>
	
    <!--NON USATA-->
    <select id="getTeamByMultiFilter" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM fixture INNER JOIN team ON team.id = fixture.home_team_id INNER JOIN game ON fixture.id = game.fixture_id
        ${where}
        UNION
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM fixture INNER JOIN team ON team.id = fixture.away_team_id INNER JOIN game ON fixture.id = game.fixture_id
        ${where}
        ORDER BY team_name
    </select>
	
    <!-- ritorna tutti i team il cui nome contiene i caratteri in input -->
    <!--
<select id="getTeamByPattern" resultMap="resultMapTeam" parameterType="java.lang.String">
            select distinct team.id team_id, team.name team_name
    FROM team th inner join fixture on fixture.home_team_id = th.id inner join team ta on ta.id = fixture.away_team_id
            ${value}
    </select> -->
    <select id="getTeamByPatternPersonal" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN team.name${language.replace("_it", "")} IS NULL THEN team.name ELSE team.name${language.replace("_it", "")} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, season.name season_name, team.color team_color,
        (CASE WHEN country.name${language} IS NULL THEN country.name_it ELSE country.name${language} END) country_name, team.national_team team_national_team,
        team.groupset_id
        FROM fixture INNER JOIN team ON team.id = fixture.home_team_id
        INNER JOIN season ON season.id = fixture.season_id INNER JOIN country ON country.id = team.country_id
        ${where}
        <if test="competitionException != null">
            ${competitionException}
        </if>
        GROUP BY team.id, season.id
        UNION
        SELECT DISTINCT (CASE WHEN team.name${language.replace("_it", "")} IS NULL THEN team.name ELSE team.name${language.replace("_it", "")} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, season.name season_name, team.color team_color,
        (CASE WHEN country.name${language} IS NULL THEN country.name_it ELSE country.name${language} END) country_name, team.national_team team_national_team,
        team.groupset_id
        FROM fixture INNER JOIN team ON team.id = fixture.away_team_id
        INNER JOIN season ON season.id = fixture.season_id INNER JOIN country ON country.id = team.country_id
        ${where}
        <if test="competitionException != null">
            ${competitionException}
        </if>
        GROUP BY team.id, season.id
        ${order}
    </select>
        
    <select id="getTeamByPatternSICS" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN team.name${language.replace("_it", "")} IS NULL THEN team.name ELSE team.name${language.replace("_it", "")} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, season.name season_name, team.color team_color,
        (CASE WHEN country.name${language} IS NULL THEN country.name_it ELSE country.name${language} END) country_name, team.national_team team_national_team,
        team.groupset_id
        FROM fixture INNER JOIN team ON team.id = fixture.home_team_id INNER JOIN season ON season.id = fixture.season_id
        INNER JOIN country ON country.id = team.country_id
        ${where}
        <if test="competitionException != null">
            ${competitionException}
        </if>
        GROUP BY team.id, season.id
        UNION
        SELECT DISTINCT (CASE WHEN team.name${language.replace("_it", "")} IS NULL THEN team.name ELSE team.name${language.replace("_it", "")} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, season.name season_name, team.color team_color,
        (CASE WHEN country.name${language} IS NULL THEN country.name_it ELSE country.name${language} END) country_name, team.national_team team_national_team,
        team.groupset_id
        FROM fixture INNER JOIN team ON team.id = fixture.away_team_id INNER JOIN season ON season.id = fixture.season_id
        INNER JOIN country ON country.id = team.country_id
        ${where}
        <if test="competitionException != null">
            ${competitionException}
        </if>
        GROUP BY team.id, season.id
        ${order}
    </select>
    
    <!-- ritorna i team in cui è presente un giocatore nella stagione-->
    <select id="getTeamByPlayer" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id
        FROM team
        INNER JOIN fixture_player on fixture_player.team_id = team.id
        INNER JOIN fixture on fixture.id=fixture_player.fixture_id
        where fixture_player.player_id = #{idPlayer} AND fixture.season_id IN(${seasonIds})
        <if test="source==true">
            AND fixture_player.groupset_id = #{groupsetId} 
        </if>
        <if test="source==false">
            AND fixture_player.groupset_id = -1
        </if>
    </select>
    
<!--    <select id="getLastTeamForPlayer" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language} IS NULL THEN team.name ELSE team.name${language} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team,
        team.groupset_id, team_player.position_id player_position_id, player.foot_id player_foot_id
        FROM team
        INNER JOIN fixture_player ON fixture_player.player_id = ${playerId} AND fixture_player.team_id = team.id
        INNER JOIN fixture ON fixture.id = fixture_player.fixture_id
        INNER JOIN team_player ON team_player.season_id = fixture.season_id AND team_player.team_id = team.id AND team_player.player_id = ${playerId}
        INNER JOIN player ON player.id = ${playerId}
        WHERE team.national_team = 0
        ORDER BY fixture.game_date DESC
        LIMIT 1
    </select>-->
    
<!--    <select id="getLastTeamForPlayers" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language.replace("_it", "")} IS NULL THEN team.name ELSE team.name${language.replace("_it", "")} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team, team.groupset_id,
        fixture_player.player_id, (CASE WHEN position.desc${language} IS NULL THEN position.desc_it ELSE position.desc${language} END) player_position_description,
        (CASE WHEN position_detail.desc${language} IS NULL THEN position_detail.desc_it ELSE position_detail.desc${language} END) player_position_detail_description,
        player.photo player_photo, (CASE WHEN foot.desc${language} IS NULL THEN foot.desc_it ELSE foot.desc${language} END) player_foot
        FROM (
            SELECT MAX(fixture.id) max_fixture_id, fixture_player.player_id
            FROM fixture_player 
            INNER JOIN fixture ON fixture.id = fixture_player.fixture_id 
            INNER JOIN team ON team.id = fixture_player.team_id
            WHERE fixture_player.player_id IN(${playerIds}) AND team.national_team = 0 
            GROUP BY fixture_player.player_id
            ORDER BY fixture.game_date DESC
        ) fixture_max
        INNER JOIN fixture ON fixture.id = fixture_max.max_fixture_id
        INNER JOIN fixture_player ON fixture_player.fixture_id = fixture.id AND fixture_player.player_id = fixture_max.player_id
        INNER JOIN team ON team.id = fixture_player.team_id
        INNER JOIN season ON season.id = fixture.season_id 
        INNER JOIN team_player ON team_player.player_id = fixture_player.player_id 
        AND team_player.team_id = team.id 
        <![CDATA[
        AND team_player.season_id = CASE WHEN team.solar = 0 THEN CASE WHEN fixture.season_id < 2000 THEN fixture.season_id +2000 ELSE fixture.season_id END ELSE CASE WHEN fixture.season_id > 2000 THEN YEAR(fixture.game_date) -2000 ELSE fixture.season_id END END 
        ]]>
        AND team_player.groupset_id = -1 
        INNER JOIN position ON position.id = team_player.position_id AND position.sport_id = 0 
        INNER JOIN position_detail ON position_detail.id = team_player.position_detail_id AND position_detail.sport_id = 0 
        INNER JOIN player ON player.id = fixture_player.player_id 
        INNER JOIN foot ON foot.id = player.foot_id
        WHERE team.national_team = 0 AND season.visible = 1 
        GROUP BY fixture_max.player_id
    </select>-->
    
    <select id="getLastTeamForPlayers" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN team.name${language.replace("_it", "")} IS NULL THEN team.name ELSE team.name${language.replace("_it", "")} END) team_name,
        team.id team_id, team.main_team_id, team.logo team_logo, team.country_id country_id, team.color team_color, team.national_team team_national_team, team.groupset_id,
        team_player_last.player_id, (CASE WHEN position.desc${language} IS NULL THEN position.desc_it ELSE position.desc${language} END) player_position_description,
        (CASE WHEN position_detail.desc${language} IS NULL THEN position_detail.desc_it ELSE position_detail.desc${language} END) player_position_detail_description,
        player.photo player_photo, (CASE WHEN foot.desc${language} IS NULL THEN foot.desc_it ELSE foot.desc${language} END) player_foot
        FROM team_player_last
        INNER JOIN fixture ON fixture.id = team_player_last.fixture_id
        INNER JOIN team ON team.id = team_player_last.team_id
        INNER JOIN team_player ON team_player.player_id = team_player_last.player_id AND team_player.team_id = team.id
        AND team_player.season_id = fixture.season_id AND team_player.groupset_id = -1 
        INNER JOIN position ON position.id = team_player.position_id AND position.sport_id = 0 
        INNER JOIN position_detail ON position_detail.id = team_player.position_detail_id AND position_detail.sport_id = 0 
        INNER JOIN player ON player.id = team_player_last.player_id 
        INNER JOIN foot ON foot.id = player.foot_id
        WHERE team.national_team = 0
        AND team_player_last.player_id IN(${playerIds})
        GROUP BY team_player_last.player_id
    </select>
    
    <select id="getCompetitionTeamGironi" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT team.id team_id, team.main_team_id, team.name team_name, group.id group_id, group.order group_order,
        (CASE WHEN group.name${language.replace("_it", "")} IS NULL THEN group.name ELSE group.name${language.replace("_it", "")} END) group_name
        FROM fixture
        INNER JOIN videomatch.group ON group.id = fixture.group_id
        INNER JOIN team team ON team.id = fixture.away_team_id
        WHERE fixture.competition_id = ${competitionId} AND fixture.season_id IN(${seasonIds})
        AND fixture.video_name IS NOT NULL AND fixture.owner_user_id = 1
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        GROUP BY team.id, group.id
        UNION
        SELECT team.id team_id, team.main_team_id, team.name team_name, group.id group_id, group.order group_order,
        (CASE WHEN group.name${language.replace("_it", "")} IS NULL THEN group.name ELSE group.name${language.replace("_it", "")} END) group_name
        FROM fixture
        INNER JOIN videomatch.group ON group.id = fixture.group_id
        INNER JOIN team team ON team.id = fixture.home_team_id
        WHERE fixture.competition_id = ${competitionId} AND fixture.season_id IN(${seasonIds})
        AND fixture.video_name IS NOT NULL AND fixture.owner_user_id = 1
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        GROUP BY team.id, group.id
    </select>
    
    <select id="getGironeById" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT id group_id, group.order group_order,
        (CASE WHEN group.name${language.replace("_it", "")} IS NULL THEN group.name ELSE group.name${language.replace("_it", "")} END) group_name
        FROM videomatch.group
        WHERE id = ${id}
    </select>
    
    <select id="getTeamsForUpload" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN team.name${language.replace("_it", "")} IS NULL THEN team.name ELSE team.name${language.replace("_it", "")} END) team_name
        FROM team
        WHERE team.groupset_id = -1 AND team.sport_id = 0
        ORDER BY team_name
    </select>
    
    <update id="updateTeamLogo" parameterType="java.util.TreeMap">
        UPDATE team SET logo = "${logo}"
        WHERE id = ${teamId}
    </update>
    
    <update id="updateTeamColor" parameterType="java.util.TreeMap">
        UPDATE team SET color = "${color}"
        WHERE id = ${teamId}
    </update>
    
</mapper>
