<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Game">
   
    <resultMap id="resultMapGame" type="Game">
        <id property="id"			column="id"/>
        <result property="homeTeamId" column="home_team_id" />
        <result property="awayTeamId" column="away_team_id" />
        <result property="homeTeam" column="home_team_des" />
        <result property="awayTeam" column="away_team_des" />
        <result property="homeTeamScore" column="home_score" />
        <result property="awayTeamScore" column="away_score" />
        <result property="homeColor" column="colorHome" />
        <result property="awayColor" column="colorAway" />
        <result property="homeLogo" column="logoHome" />
        <result property="awayLogo" column="logoAway" />
        <result property="homeModule" column="homeModule" />
        <result property="awayModule" column="awayModule" />
        <result property="tacticalVideo" column="tacticalVideo" />
        <result property="date"		column="game_date" />
        <result property="competitionId" column="competition_id" />
        <result property="groupId"          column="group_id"/>
        <result property="providerId" column="provider_id" />
        <result property="seasonId" column="season_id" />
        <result property="matchday" column="matchday" />
        <result property="videoId"	column="video_id" />
        <result property="videoName"	column="video_name" />
        <result property="hd"	column="hd" />
        <result property="fhd"	column="fhd" />
        <result property="awayTeamScore" column="away_score" />
        <result property="refereeId" column="referee_id" />
        <result property="refereeName" column="referee_name" />
        <result property="assistant1Id" column="assistant1_id" />
        <result property="assistant1Name" column="ass1_name" />
        <result property="assistant2Id" column="assistant2_id" />
        <result property="assistant2Name" column="ass2_name" />
        <result property="assistantGoal1Id" column="assistantp1_id" />
        <result property="assistantGoal1Name" column="assp1_name" />
        <result property="assistantGoal2Id" column="assistantp2_id" />
        <result property="assistantGoal2Name" column="assp2_name" />
        <result property="assistant4Id" column="assistant4_id" />
        <result property="assistant4Name" column="ass4_name" />
        <result property="fileproject" column="fileproject" />
        <result property="idFixture" column="idFixture" />
        <result property="competitionName" column="competitionName" />
        <result property="tagged" column="tagged" />
        <result property="loadedOnDB" column="loadedDB" />
        <result property="existReport" column="reportExist" />
        <result property="existReportEN" column="reportExistEN" />
        <result property="ownerUserId" column="ownerUserId"/>
        <result property="ownerName" column="owner_name"/>
        <result property="groupsetId" column="groupset_id"/>
        <result property="videoQuality" column="video_quality"/>
        <result property="minVideoQuality" column="min_video_quality"/>
        <result property="counter" column="counter"/>
        <result property="analysisLevel" column="analysis_level"/>
        <result property="teamSx" column="team_sx"/>
        <result property="teamSx2" column="team_sx_2"/>
		
        <!--result property="assistantP1Id" column="assistantp1_id" /-->
        <!--result property="assistantP2Id" column="assistantp2_id" /-->
        <!--result property="assistantOId" column="assistanto_id" /-->

    </resultMap>

    <resultMap id="resultMapGameCount" type="CalendarRange">
        <result property="minMatchDay"	column="minmatchday" />
        <result property="maxMatchDay"	column="maxmatchday" />
    </resultMap>
    
    <resultMap id="resultMapFixtureDetails" type="FixtureDetails">
        <result property="id"               column="id" />
        <result property="fixtureId"        column="fixture_id" />
        <result property="teamSx"           column="teamSx" />
        <result property="teamSx2"          column="teamSx2" />
        <result property="homeTeamColor"    column="home_team_color" />
        <result property="awayTeamColor"    column="away_team_color" />
        <result property="startTime1"       column="starttime1" />
        <result property="endTime1"         column="endtime1" />
        <result property="startTime2"       column="starttime2" />
        <result property="endTime2"         column="endtime2" />
        <result property="startTime3"       column="starttime3" />
        <result property="endTime3"         column="endtime3" />
        <result property="startTime4"       column="starttime4" />
        <result property="endTime4"         column="endtime4" />
    </resultMap>

    <sql id="selectGame">
        fixture.home_team_id, fixture.away_team_id, fixture.game_date, fixture.home_score, fixture.away_score, fixture.competition_id, fixture.group_id,
        fixture.provider_id, fixture.season_id, fixture.matchday, fixture.video_id,fixture.video_name, fixture.video_name,
        IF(fixture.video_quality>=1,1,0) hd,IF(fixture.video_quality>=2,1,0) fhd,fixture.home_module homeModule,fixture.away_module awayModule,fixture.tactical_video tacticalVideo, 
        teamh.color colorHome,teama.color colorAway, teamh.logo logoHome, teama.logo logoAway,
        referee_id, assistant1_id, assistant2_id, assistant4_id, assistantp1_id, assistantp2_id, assistanto_id,
        ref.last_name referee_name, ass1.last_name ass1_name, ass2.last_name ass2_name, ass4.last_name ass4_name,assp1.last_name assp1_name, assp2.last_name assp2_name, fixture.id idFixture,
        fixture.report_en reportExistEN,fixture.report reportExist,fixture.owner_user_id ownerUserId, CONCAT(user.last_name, " ", user.first_name) AS owner_name,
        fixture.video_quality, fixture.min_video_quality min_video_quality, fixture.analysis_level_id analysis_level, fixture_details.teamSx team_sx, fixture_details.teamSx2 team_sx_2
        from fixture
        inner join team teamh on teamh.id=fixture.home_team_id
        inner join team teama on teama.id=fixture.away_team_id
        inner join competition ON competition.id = fixture.competition_id
        inner join groupset_competition_tv ON competition.id = groupset_competition_tv.competition_id
        left join referee ref on ref.id=fixture.referee_id
        left join referee ass1 on ass1.id=fixture.assistant1_id
        left join referee ass2 on ass2.id=fixture.assistant2_id
        left join referee assp1 on assp1.id=fixture.assistantp1_id
        left join referee assp2 on assp2.id=fixture.assistantp2_id
        left join referee ass4 on ass4.id=fixture.assistant4_id
        LEFT JOIN user ON user.id=fixture.owner_user_id
        LEFT JOIN fixture_details ON fixture_details.fixture_id = fixture.id
    </sql>
    
    <sql id="selectGameNew">
        fixture.home_team_id, fixture.away_team_id, fixture.game_date, fixture.home_score, fixture.away_score, fixture.competition_id, fixture.group_id, 
        fixture.provider_id, fixture.season_id, fixture.matchday, fixture.video_id,fixture.video_name, fixture.video_name,
        IF(fixture.video_quality>=1,1,0) hd,IF(fixture.video_quality>=2,1,0) fhd,fixture.home_module homeModule,fixture.away_module awayModule,fixture.tactical_video tacticalVideo, 
        teamh.color colorHome,teama.color colorAway, teamh.logo logoHome, teama.logo logoAway,
        referee_id, assistant1_id, assistant2_id, assistant4_id, assistantp1_id, assistantp2_id, assistanto_id,
        ref.last_name referee_name, ass1.last_name ass1_name, ass2.last_name ass2_name, ass4.last_name ass4_name,assp1.last_name assp1_name, assp2.last_name assp2_name,
        fixture.id idFixture, fixture.report_en reportExistEN, fixture.report reportExist, fixture.owner_user_id ownerUserId,
        CONCAT(user.last_name, " ", user.first_name) AS owner_name, fixture.video_quality, fixture.min_video_quality min_video_quality, fixture.analysis_level_id analysis_level
        from fixture
        inner join team teamh on teamh.id=fixture.home_team_id
        inner join team teama on teama.id=fixture.away_team_id
        inner join competition ON competition.id = fixture.competition_id
        inner join groupset_competition_tv ON competition.id = groupset_competition_tv.competition_id
        left join referee ref on ref.id=fixture.referee_id
        left join referee ass1 on ass1.id=fixture.assistant1_id
        left join referee ass2 on ass2.id=fixture.assistant2_id
        left join referee assp1 on assp1.id=fixture.assistantp1_id
        left join referee assp2 on assp2.id=fixture.assistantp2_id
        left join referee ass4 on ass4.id=fixture.assistant4_id
        LEFT JOIN user ON user.id=fixture.owner_user_id
    </sql>
    
    <sql id="selectGameMulti">
        fixture.home_team_id, fixture.away_team_id, fixture.game_date, fixture.home_score, fixture.away_score, fixture.competition_id, fixture.group_id,
        fixture.provider_id, fixture.season_id, fixture.matchday, fixture.video_id,fixture.video_name, fixture.video_name,
        IF(fixture.video_quality>=1,1,0) hd,IF(fixture.video_quality>=2,1,0) fhd,fixture.home_module homeModule,fixture.away_module awayModule,fixture.tactical_video tacticalVideo, 
        teamh.color colorHome,teama.color colorAway, teamh.logo logoHome, teama.logo logoAway,
        referee_id, assistant1_id, assistant2_id, assistant4_id, assistantp1_id, assistantp2_id, assistanto_id,
        ref.last_name referee_name, ass1.last_name ass1_name, ass2.last_name ass2_name, ass4.last_name ass4_name,assp1.last_name assp1_name, assp2.last_name assp2_name,
        fixture.id idFixture, fixture.report reportExist,fixture.report_en reportExistEN,fixture.owner_user_id ownerUserId, CONCAT(user.last_name, " ", user.first_name) AS owner_name,
        fixture.video_quality, fixture.min_video_quality min_video_quality, fixture.analysis_level_id analysis_level
        from fixture
        inner join team teamh on teamh.id=fixture.home_team_id
        inner join team teama on teama.id=fixture.away_team_id
        inner join competition ON competition.id = fixture.competition_id
        left join referee ref on ref.id=fixture.referee_id
        left join referee ass1 on ass1.id=fixture.assistant1_id
        left join referee ass2 on ass2.id=fixture.assistant2_id
        left join referee assp1 on assp1.id=fixture.assistantp1_id
        left join referee assp2 on assp2.id=fixture.assistantp2_id
        left join referee ass4 on ass4.id=fixture.assistant4_id
        LEFT JOIN user ON user.id=fixture.owner_user_id
    </sql>
	
    <!--sql id="groupBy">
        GROUP BY game.id
    </sql-->
	
    <select id="getGameAll" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGame"/>
        where groupset_competition_tv.groupset_id = #{idGro} and provider_id != 3
        <if test="strFilter != null">
            ${strFilter}
        </if>
        group by fixture.id
        order by game_date desc, matchday desc, fixture.competition_id, home_team_des, away_team_des
    </select>

    <select id="getGame" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGame"/>
        where fixture.id=#{where}
        group by fixture.id
    </select>
    
    <select id="getGamesByFixtureIds" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGameNew"/>
        where fixture.id IN (${where})
        group by fixture.id
    </select>
    
    <select id="getGameByVideoName" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGame"/>
        where fixture.video_name="${videoname}" AND fixture.owner_user_id=${ownerId} AND fixture.deleted=0 AND fixture.provider_id=3
        group by fixture.id
    </select>
	
    <select id="getGameByFixtureId" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGame"/>
        where fixture.id=#{where}
        group by fixture.id
    </select>
    
    <select id="getGameByListFixtureId" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGame"/>
        ${where}
    </select>
	
    <select id="getGameByCompetitionId" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGameNew"/>
        WHERE fixture.competition_id=#{compId} 
        AND fixture.season_id IN(${seasonIds})
        AND groupset_competition_tv.groupset_id = #{idGro} 
        <if test="source==false">
            AND provider_id != 3 
        </if>
        <if test="source==true">
            AND provider_id = 3 
            AND fixture.deleted = 0 
            AND fixture.video_name IS NOT NULL 
        </if>
        <if test="countryId != null">
            AND (teamh.country_id = #{countryId} OR teama.country_id = #{countryId})
        </if>
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        <![CDATA[
        AND fixture.game_date <= DATE(NOW() + INTERVAL 1 DAY)
        ]]>
        AND IFNULL(fixture.matchday, 0) != 99
        ORDER BY game_date desc, matchday desc, fixture.competition_id, home_team_des, away_team_des
        limit #{startFrom},25
    </select>
	
    <select id="getGameByTeamId" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGameNew"/>
        WHERE fixture.season_id IN(${seasonIds})
        <if test="compId != null">
            AND fixture.competition_id=#{compId} 
        </if>
        AND groupset_competition_tv.groupset_id = #{idGro} 
        <if test="source==false">
            AND provider_id != 3 
        </if>
        <if test="source==true">
            AND provider_id = 3 
            AND fixture.deleted = 0 
            AND fixture.video_name IS NOT NULL 
        </if>
        AND (fixture.away_team_id=#{teamId} OR fixture.home_team_id = #{teamId})
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        <![CDATA[
        AND fixture.game_date <= DATE(NOW() + INTERVAL 1 DAY)
        ]]> 
        ORDER BY fixture.game_date desc
        limit #{startFrom},#{limit}
    </select>
	
    <select id="getGameMulti" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGame"/>
        where fixture.competition_id=#{compId} 
        <if test="source==false">
            AND provider_id != 3 
        </if>
        <if test="source==true">
            AND provider_id = 3 
            AND fixture.deleted = 0
        </if>
        AND fixture.season_id IN(${seasonIds})
        AND groupset_competition_tv.groupset_id = #{idGro} 
        AND fixture.matchday =#{dayId} 
        AND (fixture.away_team_id=#{teamId} or fixture.home_team_id = #{teamId})
        GROUP BY fixture.id
        ORDER BY fixture.game_date desc
    </select>
	
    <select id="getGameBySeason" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGameNew"/>
        WHERE fixture.season_id IN(${seasonIds})
        AND groupset_competition_tv.groupset_id = #{idGro} 
        <if test="source==false">
            AND fixture.provider_id != 3 
        </if>
        <if test="source==true">
            AND fixture.provider_id = 3 
            AND fixture.owner_groupset_id = #{idGro}
            AND fixture.deleted = 0 
            AND fixture.video_name IS NOT NULL 
        </if>
        <![CDATA[
        AND fixture.game_date <= DATE(NOW() + INTERVAL 1 DAY)
        ]]>
        ORDER BY game_date desc, matchday desc, fixture.competition_id, home_team_des, away_team_des
        limit #{startFrom},25
    </select>

    <!-- Torna il valore massimo delle partite che possiedono un video  -->
    <select id="getMinAndMaxMatchdays" resultMap="resultMapGameCount" >
        select max(matchday) maxmatchday, min(matchday) minmatchday
        from fixture 
        <!--join con competition per filtrare le giornate in base al campionato -->
        inner join competition on fixture.competition_id=competition.id 
        where ((provider_id = 0 AND NOT video_id IS NULL) OR provider_id != 3) <!-- and competition.id=#{value}-->
		
    </select>
	
    <!-- Torna il valore massimo e minimo delle partite che possiedono un video  -->
    <select id="getMinAndMaxMatchdaysByCompetitionAndSeason" resultMap="resultMapGameCount" parameterType="java.util.TreeMap">
        select max(matchday) maxmatchday, min(matchday) minmatchday
        from fixture 
        <!--join con competition per filtrare le giornate in base al campionato -->
        inner join competition on fixture.competition_id=competition.id
        <if test="countryId != null">
            inner join team teamh on teamh.id=fixture.home_team_id
            inner join team teama on teama.id=fixture.away_team_id
        </if>
        where (video_name IS NOT NULL
        <![CDATA[
        OR fixture.game_date <= DATE(NOW() + INTERVAL 1 DAY))
        ]]> 
        <if test="compId != null">
            AND fixture.competition_id = #{compId}
        </if>
        AND fixture.season_id IN(${seasonIds}) <!-- and competition.id=#{value}-->
        <if test="countryId != null">
            AND (teamh.country_id = #{countryId} OR teama.country_id = #{countryId})
        </if>
        <if test="source==true">
            AND fixture.provider_id = 3
            AND fixture.deleted = 0
        </if>
        <if test="source==false">
            AND fixture.provider_id != 3
        </if>
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
    </select>
	
    <select id="getGameByMultiFilter" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select distinct (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGameMulti"/> 
        ${where}
    </select>
    
    <select id="getGameIdsByFilter" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select distinct fixture.id idFixture
        from event 
        inner join fixture ON fixture.id = event.fixture_id 
        inner join team ON team.id = event.team_id 
        left join event_type ON event.event_type_id = event_type.id 
        left join event_tag ON event.id = event_tag.event_id 
        left join event_player ON event.id = event_player.event_id 
        left join tag_type ON event_tag.tag_type_id=tag_type.id 
        left join panel ON event_type.panel_id = panel.id 
        ${where}
    </select>
    
    <select id="getGameIdsByFilters" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select distinct fixture.id idFixture
        ${from}
        ${where}
    </select>
    
    <select id="getModuleByTeam" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        SELECT home_team_id, away_team_id, home_module homeModule, away_module awayModule, COUNT(*) counter
        FROM fixture
        WHERE ((home_team_id = ${teamId} AND home_module IS NOT NULL) OR (away_team_id = ${teamId} AND away_module IS NOT NULL))
        AND fixture.season_id IN(${seasonIds})
        <if test="compId != null">
            AND fixture.competition_id = ${compId}
        </if>
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        <if test="from != null">
        <![CDATA[
            AND fixture.game_date >= STR_TO_DATE("${from}", "%d/%m/%Y")
        ]]>
        </if>
        <if test="to != null">
        <![CDATA[
            AND fixture.game_date <= STR_TO_DATE("${to}", "%d/%m/%Y")
        ]]>
        </if>
        GROUP BY home_module, away_module
        ORDER BY counter DESC
    </select>
    
    <select id="getFixtureByGameId" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT fixture_id
        FROM game
        WHERE id = ${gameId}
    </select>
    
    <select id="getFixturesByGameIds" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT fixture_id
        FROM game
        WHERE id IN(${gameIds})
    </select>
    
    <select id="getFixtureDetails" resultMap="resultMapFixtureDetails" parameterType="java.util.TreeMap">
        SELECT id, fixture_id, teamSx, teamSx2, home_team_color, away_team_color, starttime1, endtime1, starttime2, endtime2, starttime3, endtime3, starttime4, endtime4
        FROM fixture_details
        WHERE fixture_id IN(${fixtureIds})
    </select>
    
    <select id="getFixturesUploadableByCompetition" resultMap="resultMapGame" parameterType="java.util.TreeMap">
        select DISTINCT (CASE WHEN teamh.name${language} IS NULL THEN teamh.name ELSE teamh.name${language} END) home_team_des, (CASE WHEN teama.name${language} IS NULL THEN teama.name ELSE teama.name${language} END) away_team_des,(CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competitionName,<include refid="selectGameNew"/>
        LEFT JOIN fixture_video ON fixture_video.fixture_id = fixture.id
        WHERE fixture.season_id IN(SELECT id FROM season WHERE visible = 1)
        AND fixture.competition_id = ${competitionId}
        AND provider_id != 3
        AND fixture_video.id IS NULL
        AND fixture.video_name IS NULL
        <![CDATA[
        AND fixture.game_date >= DATE(NOW() - INTERVAL 1 YEAR) AND fixture.game_date <= NOW() + INTERVAL 1 DAY
        ]]>
        ORDER BY game_date DESC, matchday DESC, fixture.competition_id, home_team_des, away_team_des
    </select>
    
    <update id="deleteGame" parameterType="java.util.TreeMap">
        UPDATE fixture SET fixture.deleted = 1
        WHERE fixture.id = #{idFixture} AND fixture.owner_user_id=#{idUser} AND fixture.provider_id=3
    </update>
    
    <insert id="insertFixtureVideo" parameterType="java.util.TreeMap">
        INSERT IGNORE INTO fixture_video (competition_id, fixture_id, type, link, kickOff, halfTime_1_End,
        halfTime, halfTime_2_End, extraTime_1_Start, extraTime_1_End, extraTime_2_Start, extraTime_2_End,
        penaltyShootout_Start, penaltyShootout_End, download_status, user_id, note)
        ${values}
    </insert>
    
    <update id="updateFixtureScore" parameterType="java.util.TreeMap">
        UPDATE fixture SET fixture.home_score = ${homeScore}, fixture.away_score = ${awayScore}
        WHERE fixture.id = ${fixtureId}
    </update>
    
    <select id="getFixtureFileproject" resultType="java.lang.String" parameterType="java.util.TreeMap">
        SELECT fileproject
        FROM game
        WHERE fixture_id = ${fixtureId} AND groupset_id = -1
    </select>
    
    <select id="getGameIdByFixtureId" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT id
        FROM game
        WHERE fixture_id = ${fixtureId} AND groupset_id = -1
    </select>
    
    <select id="getFixturePlayerJerseyNumber" resultType="java.lang.Integer" parameterType="java.util.TreeMap">
        SELECT jersey_num
        FROM fixture_player
        WHERE fixture_id = ${fixtureId} AND player_id = ${playerId}
        GROUP BY player_id
    </select>

</mapper>
