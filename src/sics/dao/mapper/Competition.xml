<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Competition">

    <resultMap id="resultMapCompetition"                type="Competition">
        <id property="id"                               column="competition_id"/>
        <result property="name"                         column="competition_name" />
        <result property="sportId"                      column="competition_sport_id"/>
        <result property="panelId"                      column="competition_panel_id"/>
        <result property="logo"                         column="logo" />
        <result property="visible"                      column="visible" />
        <result property="countryId"                    column="competition_country_id"/>
        <result property="internationalCompetitionId"   column="international_competition_id"/>
        <result property="ranking"                      column="ranking"/>
        <result property="groupsetId"                   column="groupset_id"/>
        
        <result property="seasonName"                   column="season_name" />
        
        <result property="groupId"                      column="group_id" />
        <result property="groupName"                    column="group_name" />
    </resultMap>

    <sql id="select">
        competition.id competition_id, competition.sport_id competition_sport_id, sport.panel_id competition_panel_id,
        competition.country_id competition_country_id, competition.international_competition_id international_competition_id,
        competition.ranking ranking, competition.groupset_id
        from competition
        INNER JOIN sport ON competition.sport_id = sport.id
    </sql>
	
    <select id="getCompetitionTactAllowed" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT competition.id
        FROM groupset_comp_tact
        INNER JOIN groupset ON groupset.id = groupset_comp_tact.groupset_id
        INNER JOIN competition ON competition.id = groupset_comp_tact.competition_id
        INNER JOIN groupset_level ON groupset_level.groupset_id = groupset_comp_tact.groupset_id
        WHERE groupset_level.level_id=6 AND groupset.id = #{idGroup}
        ORDER BY groupset.id, groupset.name
    </select>
    
    <select id="getCompetitionByCountry" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT DISTINCT competition.id, (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        competition.logo, groupset_competition_tv.groupset_id IS NOT NULL AS visible, competition.ranking ranking, competition.groupset_id, MAX(season.name) season_name
        FROM fixture
        INNER JOIN season ON season.id = fixture.season_id
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN sport ON competition.sport_id = sport.id
        LEFT JOIN groupset_competition_tv ON groupset_competition_tv.competition_id = competition.id AND groupset_competition_tv.groupset_id = #{idGro}
        WHERE sport.id = #{idSport}
        AND fixture.season_id IN(SELECT id FROM season WHERE visible = 1)
        <if test="source==false">
            AND provider_id != 3 
            AND competition.groupset_id = -1 
            <if test="idCountry!=0">
                AND competition.country_id = #{idCountry} 
            </if>
            <if test="idInternationalCompetition!=0">
                AND competition.international_competition_id = #{idInternationalCompetition} 
            </if>
        </if>
        <if test="source==true">
            AND provider_id = 3 
            AND competition.groupset_id IN (-1,#{idGro}) 
        </if> 
        <if test="competitionException != null">
            ${competitionException}
        </if>
        AND fixture.deleted = 0
        AND fixture.competition_id IN (${allowedCompetitions})
        AND fixture.video_name IS NOT NULL
        AND fixture.competition_id NOT IN (708)
        GROUP BY competition.id
        ORDER BY visible DESC, competition.order, competition_name
    </select>
    
    <select id="getInternationalCompetitionByCountry" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT DISTINCT competition.id, (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        competition.logo, groupset_competition_tv.groupset_id IS NOT NULL AS visible, competition.ranking ranking, competition.groupset_id, MAX(season.name) season_name
        FROM fixture
        INNER JOIN season ON season.id = fixture.season_id
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN international_competition ON international_competition.id = competition.international_competition_id
        INNER JOIN sport ON competition.sport_id = sport.id
        INNER JOIN team ON team.id = fixture.home_team_id
        INNER JOIN team away ON away.id = fixture.away_team_id
        LEFT JOIN groupset_competition_tv ON groupset_competition_tv.competition_id = competition.id AND groupset_competition_tv.groupset_id = #{idGro}
        WHERE sport.id = #{idSport}
        AND fixture.season_id IN(SELECT id FROM season WHERE visible = 1)
        AND (team.country_id = #{idCountry} OR away.country_id = #{idCountry})
        AND competition.groupset_id = -1
        AND international_competition.id > 0
        AND provider_id != 3
        AND competition.groupset_id = -1
        AND fixture.video_name IS NOT NULL
        <if test="competitionException != null">
            ${competitionException}
        </if>
        GROUP BY competition.id
        ORDER BY visible DESC, competition.order, competition_name
    </select>
    
    <select id="getCompetitionAll" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT DISTINCT competition.id, (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        competition.logo, groupset_competition_tv.groupset_id IS NOT NULL AS visible, competition.ranking ranking, competition.groupset_id
        FROM fixture
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN sport ON competition.sport_id = sport.id
        LEFT JOIN groupset_competition_tv ON groupset_competition_tv.competition_id = competition.id AND groupset_competition_tv.groupset_id = #{idGro}
        WHERE sport.id = #{idSport}	
        AND fixture.season_id IN(${seasonIds})
        <if test="source==false">
            AND provider_id != 3 
            AND competition.groupset_id = -1 
        </if>
        <if test="source==true">
            AND fixture.competition_id IN (${allowedCompetitions})
            AND fixture.video_name IS NOT NULL 
            AND provider_id = 3 
            AND fixture.owner_groupset_id = ${idGro}
            AND fixture.deleted = 0 
        </if> 
        AND fixture.video_name IS NOT NULL
        order by visible DESC,competition.order,competition_name
    </select>
	
    <!--NON PIU USATA-->
    <select id="getCompetitionBySeasons" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT DISTINCT competition.id, (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        competition.logo, competition.ranking ranking, competition.groupset_id
        FROM fixture
        INNER JOIN game ON fixture.id = game.fixture_id
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN sport ON competition.sport_id = sport.id
        INNER JOIN groupset_competition_tv ON groupset_competition_tv.competition_id = competition.id
        WHERE groupset_competition_tv.groupset_id = #{idGro} 
        AND game.groupset_id = #{idGro} 
        AND fixture.season_id IN (#{idSea}) 
        AND fixture.provider_id = 2 
        AND fixture.video_name IS NOT NULL
        order by competition_name
    </select>
	
    <!-- ricavo le competizioni in base al team a partire dalle partite giocate -->
    <select id="getCompetitionByTeam" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT DISTINCT competition.id, (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        competition.logo, competition.ranking ranking, competition.groupset_id
        FROM fixture
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN sport ON competition.sport_id = sport.id
        INNER JOIN groupset_competition_tv ON groupset_competition_tv.competition_id = competition.id
        WHERE groupset_competition_tv.groupset_id = #{idGro} 
        <if test="source==false">
            AND provider_id != 3 
        </if>
        <if test="source==true">
            AND provider_id = 3 
            AND fixture.deleted=0 
        </if>
        AND fixture.competition_id IN (${allowedCompetitions})
        AND fixture.video_name IS NOT NULL 
        AND fixture.season_id IN(${seasonIds})
        AND fixture.video_name IS NOT NULL 
        AND (fixture.home_team_id = #{idTeam} OR fixture.away_team_id = #{idTeam})
        ORDER BY competition_name
    </select>
	
    <select id="getCompetitionByPlayer" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT DISTINCT competition.id, (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        competition.logo, competition.ranking ranking, competition.groupset_id
        FROM fixture
        INNER JOIN fixture_player ON fixture_player.fixture_id = fixture.id
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN sport ON competition.sport_id = sport.id
        INNER JOIN groupset_competition_tv ON groupset_competition_tv.competition_id = competition.id
        WHERE fixture.season_id IN(${seasonIds}) AND fixture.video_name IS NOT NULL AND fixture_player.player_id = #{idPlayer} AND fixture_player.coordinate != "-1;-1" 
        <if test="source==true">
            AND fixture_player.groupset_id = #{idGroup} 
        </if>
        <if test="source==false">
            AND fixture_player.groupset_id IN (-1,#{idGroup}) 
        </if>
        <if test="groupId != null">
            AND fixture.group_id = ${groupId}
        </if>
        AND fixture.competition_id IN (${allowedCompetitions})
        ORDER BY competition_name
    </select>
        
    <select id="getCompetitionByTeamPlayer" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT DISTINCT competition.id, (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        competition.logo, competition.ranking ranking, competition.groupset_id
        FROM fixture
        INNER JOIN fixture_player ON fixture_player.fixture_id = fixture.id
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN sport ON competition.sport_id = sport.id
        INNER JOIN groupset_competition_tv ON groupset_competition_tv.competition_id = competition.id
        WHERE fixture.season_id IN(${seasonIds}) AND fixture.video_name IS NOT NULL AND fixture_player.player_id = #{idPlayer} AND fixture_player.coordinate != "-1;-1" 
        <if test="source==true">
            AND fixture_player.groupset_id = #{idGroup} 
        </if>
        <if test="source==false">
            AND fixture_player.groupset_id IN (-1,#{idGroup}) 
        </if>
        AND (fixture.home_team_id=#{idTeam} OR fixture.away_team_id=#{idTeam})
        <if test="groupId != null">
        AND fixture.group_id = ${groupId}
        </if>
        AND fixture.competition_id IN (${allowedCompetitions})
        ORDER BY competition_name
    </select>

    <select id="getCompetition" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        <include refid="select"/>
        where competition.id=#{id}
    </select>
    
    <select id="getCompetitions" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        <include refid="select"/>
        where competition.id IN (${ids})
    </select>
	
    <select id="getCompetitionByGroupsetId" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        <include refid="select"/>
        INNER JOIN groupset_competition_tv ON groupset_competition_tv.competition_id = competition.id
        WHERE groupset_competition_tv.groupset_id = #{idGro} 
    </select>
    
    <select id="getCompetitionByPatternPersonal" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        competition.id competition_id, competition.logo logo, season.name season_name, competition.ranking ranking, competition.groupset_id
        FROM fixture
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN season ON season.id = fixture.season_id
        ${where}
        <if test="competitionException != null">
            ${competitionException}
        </if>
        GROUP BY competition.id, season.id
        ${order}
    </select>
    
    <select id="getCompetitionByPatternSICS" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT DISTINCT (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END) competition_name,
        competition.id competition_id, competition.logo logo, season.name season_name, competition.ranking ranking, competition.groupset_id
        FROM fixture
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN season ON season.id = fixture.season_id
        ${where}
        <if test="competitionException != null">
            ${competitionException}
        </if>
        GROUP BY competition.id, season.id
        ${order}
    </select>
    
    <select id="getTeamGironi" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT competition.id competition_id, (CASE WHEN competition.name${language.replace("_it", "")} IS NULL THEN competition.name ELSE competition.name${language.replace("_it", "")} END) competition_name, competition.logo,
        group.id group_id, (CASE WHEN group.name${language.replace("_it", "")} IS NULL THEN group.name ELSE group.name${language.replace("_it", "")} END) group_name
        FROM fixture
        INNER JOIN videomatch.group ON group.id = fixture.group_id
        INNER JOIN competition ON competition.id = fixture.competition_id
        WHERE fixture.away_team_id = ${teamId} AND fixture.season_id IN(${seasonIds})
        AND fixture.video_name IS NOT NULL AND fixture.owner_user_id = 1
        <if test="competitionId != -1">
        AND fixture.competition_id = ${competitionId}
        </if>
        AND fixture.competition_id IN (${allowedCompetitions})
        GROUP BY group.id
        UNION
        SELECT competition.id competition_id, (CASE WHEN competition.name${language.replace("_it", "")} IS NULL THEN competition.name ELSE competition.name${language.replace("_it", "")} END) competition_name, competition.logo,
        group.id group_id, (CASE WHEN group.name${language.replace("_it", "")} IS NULL THEN group.name ELSE group.name${language.replace("_it", "")} END) group_name
        FROM fixture
        INNER JOIN videomatch.group ON group.id = fixture.group_id
        INNER JOIN competition ON competition.id = fixture.competition_id
        WHERE fixture.home_team_id = ${teamId} AND fixture.season_id IN(${seasonIds})
        AND fixture.video_name IS NOT NULL AND fixture.owner_user_id = 1
        <if test="competitionId != -1">
        AND fixture.competition_id = ${competitionId}
        </if>
        AND fixture.competition_id IN (${allowedCompetitions})
        GROUP BY group.id
    </select>
    
    <select id="getPlayerGironi" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT competition.id competition_id, (CASE WHEN competition.name${language.replace("_it", "")} IS NULL THEN competition.name ELSE competition.name${language.replace("_it", "")} END) competition_name, competition.logo,
        group.id group_id, (CASE WHEN group.name${language.replace("_it", "")} IS NULL THEN group.name ELSE group.name${language.replace("_it", "")} END) group_name
        FROM fixture
        INNER JOIN videomatch.group ON group.id = fixture.group_id
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN fixture_player ON fixture_player.fixture_id = fixture.id AND fixture_player.team_id = fixture.away_team_id AND fixture_player.player_id = ${playerId}
        WHERE fixture.season_id IN(${seasonIds}) AND fixture.video_name IS NOT NULL AND fixture.owner_user_id = 1
        <if test="competitionId != -1">
        AND fixture.competition_id = ${competitionId}
        </if>
        <if test="teamId != -1">
        AND fixture.away_team_id = ${teamId}
        </if>
        AND fixture.competition_id IN (${allowedCompetitions})
        GROUP BY group.id
        UNION
        SELECT competition.id competition_id, (CASE WHEN competition.name${language.replace("_it", "")} IS NULL THEN competition.name ELSE competition.name${language.replace("_it", "")} END) competition_name, competition.logo,
        group.id group_id, (CASE WHEN group.name${language.replace("_it", "")} IS NULL THEN group.name ELSE group.name${language.replace("_it", "")} END) group_name
        FROM fixture
        INNER JOIN videomatch.group ON group.id = fixture.group_id
        INNER JOIN competition ON competition.id = fixture.competition_id
        INNER JOIN fixture_player ON fixture_player.fixture_id = fixture.id AND fixture_player.team_id = fixture.home_team_id AND fixture_player.player_id = ${playerId}
        WHERE fixture.season_id IN(${seasonIds}) AND fixture.video_name IS NOT NULL AND fixture.owner_user_id = 1
        <if test="competitionId != -1">
        AND fixture.competition_id = ${competitionId}
        </if>
        <if test="teamId != -1">
        AND fixture.away_team_id = ${teamId}
        </if>
        AND fixture.competition_id IN (${allowedCompetitions})
        GROUP BY group.id
    </select>
    
    <select id="getTop5EuropeanCompetitions" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT CONCAT((CASE WHEN country.name${language == '' ? '_it' : language} IS NULL THEN country.name_it ELSE country.name${language == '' ? '_it' : language} END), '. ', (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END)) competition_name,
        <include refid="select"/>
        INNER JOIN country ON country.id = competition.country_id
        where competition.id IN (2, 427, 235, 435, 78)
    </select>
    
    <select id="getTop5SouthAmericaCompetitions" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT CONCAT((CASE WHEN country.name${language == '' ? '_it' : language} IS NULL THEN country.name_it ELSE country.name${language == '' ? '_it' : language} END), '. ', (CASE WHEN competition.name${language} IS NULL THEN competition.name ELSE competition.name${language} END)) competition_name,
        <include refid="select"/>
        INNER JOIN country ON country.id = competition.country_id
        where competition.id IN (387, 491, 422, 552, 390, 569, 508, 624)
    </select>

    <select id="getCompetitionsForUpload" resultMap="resultMapCompetition" parameterType="java.util.TreeMap">
        SELECT DISTINCT competition.id, (CASE WHEN competition.name${language.replace("_it", "")} IS NULL THEN competition.name ELSE competition.name${language.replace("_it", "")} END) competition_name,
        competition.logo, competition.ranking ranking, competition.country_id competition_country_id, competition.international_competition_id, competition.groupset_id
        FROM fixture
            INNER JOIN competition ON competition.id = fixture.competition_id
        WHERE fixture.season_id IN(${seasonIds})
            AND provider_id != 3
            AND competition.groupset_id = -1
            AND fixture.video_name IS NULL
        ORDER BY competition_name
    </select>
    
    <update id="updateCompetitionLogo" parameterType="java.util.TreeMap">
        UPDATE competition SET logo = "${logo}"
        WHERE id = ${competitionId}
    </update>

</mapper>
