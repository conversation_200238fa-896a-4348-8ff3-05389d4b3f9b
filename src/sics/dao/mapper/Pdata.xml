<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Pdata">

	<resultMap id="resultMapPdata" type="Pdata">
		<result property="datacreazione"  column="datacreazione" />
		<result property="datascadenza"  column="datascadenza" />
		<result property="serialnumber"  column="serialnumber" />
		<result property="firstName"  column="firstname" />
		<result property="lastName"  column="lastname" />
	</resultMap>
	
	<sql id="selectPdata">
		pdata.datacreazione datacreazione,
		pdata.datascadenza datascadenza,
		pdata.sn serialnumber,
		pdata.nome firstname,
		pdata.cognome lastname
		FROM pdata 
	</sql>

	<sql id="select">
		select distinct <include refid="selectPdata"/>
	</sql>
	
	<!-- ritorna i dati con le scadenze in base al serialnumber  -->
	<select id="getScadenzaFromSerialNumber" resultMap="resultMapPdata" parameterType="java.lang.String">
		<include refid="select"/>
		where pdata.pcode = "600" AND pdata.sn LIKE "${value}"
	</select>

</mapper>
