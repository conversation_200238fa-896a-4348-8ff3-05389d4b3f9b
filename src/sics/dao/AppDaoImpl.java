package sics.dao;

import java.awt.Color;
import sics.domain.*;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Map;
import java.util.TreeMap;
import org.mybatis.spring.support.SqlSessionDaoSupport;
import sics.helper.DateHelper;
import sics.helper.GlobalHelper;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import sics.domain.Fascia;
import sics.helper.AzioneComparatorHelper;
import sics.helper.DescriptionEventComparatorHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.helper.StringHelper;
import sics.model.Azione;
import sics.model.Punto;
import sics.model.PuntoTiro;

public class AppDaoImpl extends SqlSessionDaoSupport implements AppDAO {

    public static List<String> EVENT_TYPE_NOT_ALLOWED = new ArrayList<>(
            Arrays.asList(
                    // "PASS", // Passaggio
                    "POS", // Fase di Possesso
                    "TRA", // Transizione
                    "BBC", // Controllo Palla Errato
                    "BLC", // Interruzione video
                    "COND", // Conduzione
                    "STOP" // Interruzione Gioco
            )
    );

    public static List<String> EVENT_TYPE_NOT_ALLOWED_FOR_TEAM = new ArrayList<>(
            Arrays.asList(
                    "PASS", // Passaggio
                    "POS", // Fase di Possesso
                    "TRA", // Transizione
                    "BBC", // Controllo Palla Errato
                    "BLC", // Interruzione video
                    "COND", // Conduzione
                    "STOP" // Interruzione Gioco
            )
    );

    public static List<String> EVENT_TYPE_ALLOWED_FOR_VIDEO = new ArrayList<>(
            Arrays.asList(
                    "PASS" // Passaggio
            )
    );

    // ---------------------------------------------------------------------------------------
    @Override
    public List<ChartData> getStartExportData(Integer timeFilterId, Long agentId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("timeFilterId", timeFilterId);
        map.put("agentId", agentId);
        if (Long.compare(agentId, 0) != 0) {
            List<Long> userIdFilter = getUserIdFromVtigerAgentId(agentId);
            if (userIdFilter != null && !userIdFilter.isEmpty()) {
                map.put("userIdFilter", StringUtils.join(userIdFilter, ","));
            }
        }
        return getSqlSession().selectList("ChartData.getStartExportData", map);
    }

    @Override
    public List<ChartData> getLicenseExpiredData(Integer timeFilterId, Long agentId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("timeFilterId", timeFilterId);
        map.put("agentId", agentId);
        if (Long.compare(agentId, 0) != 0) {
            List<Long> userIdFilter = getUserIdFromVtigerAgentId(agentId);
            if (userIdFilter != null && !userIdFilter.isEmpty()) {
                map.put("userIdFilter", StringUtils.join(userIdFilter, ","));
            }
        }
        return getSqlSession().selectList("ChartData.getLicenseExpiredData", map);
    }

    @Override
    public List<ChartData> getVideoDownloadData(Integer timeFilterId, Long agentId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("timeFilterId", timeFilterId);
        map.put("agentId", agentId);
        if (Long.compare(agentId, 0) != 0) {
            List<Long> userIdFilter = getUserIdFromVtigerAgentId(agentId);
            if (userIdFilter != null && !userIdFilter.isEmpty()) {
                map.put("userIdFilter", StringUtils.join(userIdFilter, ","));
            }
        }
        return getSqlSession().selectList("ChartData.getVideoDownloadData", map);
    }

    @Override
    public List<ChartData> getVideoStreamData(Integer timeFilterId, String userIdFilter) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("timeFilterId", timeFilterId);
        map.put("userIdFilter", userIdFilter);
        return getSqlSession().selectList("ChartData.getVideoStreamData", map);
    }

    @Override
    public List<ChartData> getUserLogData(Long userId, String from, String to) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("userId", userId);
        map.put("from", from);
        map.put("to", to);
        return getSqlSession().selectList("ChartData.getUserLogData", map);
    }

    @Override
    public List<ChartData> getUserLast100LogData(Long userId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("userId", userId);
        return getSqlSession().selectList("ChartData.getUserLast100LogData", map);
    }

    @Override
    public List<VtigerAgent> getVtigerAgents() throws SQLException {
        return getSqlSession().selectList("Vtiger.getVtigerAgents");
    }

    //lista tutti utenti Aia o altri
    @Override
    public List getUserAll(String name, String surname, String dateFrom, String dateTo, String application) throws SQLException {
        //if (idRes.equals(GlobalHelper.kApplicationAia)) {
        //	return getSqlSession().selectList("User.getUserAllAia");
        //} else {

        TreeMap map = new TreeMap();
        String userFilter = "WHERE user.active='" + GlobalHelper.kApplicationSicsTv + "'";

        if (name != null && !name.equals("")) {
            userFilter += " AND user.first_name LIKE \"%" + name + "%\"";
        }

        if (surname != null && !surname.equals("")) {
            userFilter += " AND user.last_name LIKE \"%" + surname + "%\"";
        }

        if (dateFrom != null && dateTo != null) {
            userFilter += " AND date > '" + dateFrom + "'" + " AND date <= '" + dateTo + "' ";
        }
//        if (role != "") {
//            userFilter += " and role.code = '" + role + "' ";
//        }
        if (application.equals("sicstv")) {
            userFilter += " AND application IN ('sicstv','svs') AND user.sn_sicstv LIKE \"%600\"";
        } else if (application.equals("VM")) {
            userFilter += " AND application = 'VM'";
        }

        map.put("userFilter", userFilter);
        return getSqlSession().selectList("User.getUserAllAdmin", map);
        //}
    }

    @Override
    public List<User> getSicsTvUsers(Long timeFilterId, Long agentId) throws SQLException {
        if (agentId == null) {
            // se l'agente non ha alcuna licenza assegnata allora non ci sono utenti.
            // arriva qua a null perchè non risulta essere un agente
            return new ArrayList<>();
        }
        TreeMap map = new TreeMap();
        map.put("timeFilterId", timeFilterId);
        map.put("agentId", agentId);
        if (Long.compare(agentId, 0) != 0) {
            List<Long> userIdFilter = getUserIdFromVtigerAgentId(agentId);
            if (userIdFilter != null && !userIdFilter.isEmpty()) {
                map.put("userIdFilter", StringUtils.join(userIdFilter, ","));
            }
        }
        List<User> userList = getSqlSession().selectList("User.getSicsTvUsers", map);
        // faccio query diversa perchè molto più veloce
        List<User> organizationList = getSqlSession().selectList("Vtiger.getVtigerContactOrganizations");
        for (User user : userList) {
            for (User organization : organizationList) {
                if (user.getSn_sicstv().equals(organization.getSn_sicstv())) {
                    user.setVtigerOrganization(organization.getVtigerOrganization());
                    break;
                }
            }
        }

        return userList;
    }

    @Override
    public User getUser(Long id) throws SQLException {
        List items = getSqlSession().selectList("User.getUser", id);
        return (items != null && !items.isEmpty()) ? (User) items.get(0) : null;
    }

    @Override
    public List<User> getUsers(List<Long> ids) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("ids", StringUtils.join(ids, ","));
        return getSqlSession().selectList("User.getUsers", map);
    }

    @Override
    public User getUser(String username) throws SQLException {
        List items = getSqlSession().selectList("User.getUserByUsername", username);
        User user = (items != null && !items.isEmpty()) ? (User) items.get(0) : null;
        if (user != null) {
            Long isGuest = (Long) getSqlSession().selectOne("User.isGroupsetGuest", user.getGroupsetId());
            Long totalSpace;
            try {
                if (isGuest == 1) {
                    user.setGuest(true);
                    totalSpace = (Long) getSqlSession().selectOne("User.getUserCloudSize", user.getId());
                } else {
                    user.setGuest(false);
                    totalSpace = (Long) getSqlSession().selectOne("User.getGroupsetCloudSize", user.getGroupsetId());
                }
            } catch (Exception e) {
                totalSpace = 0l;
            }
            user.setTotalSpace(totalSpace != null ? totalSpace : 0);
        }
        return user;
    }

    @Override
    public User getUserBySer(String ser) throws SQLException {
        List items = getSqlSession().selectList("User.getUserBySer", ser);
        User user = (items != null && !items.isEmpty()) ? (User) items.get(0) : null;
        if (user != null) {
            Long isGuest = (Long) getSqlSession().selectOne("User.isGroupsetGuest", user.getGroupsetId());
            Long totalSpace;
            try {
                if (isGuest == 1) {
                    user.setGuest(true);
                    totalSpace = (Long) getSqlSession().selectOne("User.getUserCloudSize", user.getId());
                } else {
                    user.setGuest(false);
                    totalSpace = (Long) getSqlSession().selectOne("User.getGroupsetCloudSize", user.getGroupsetId());
                }
            } catch (Exception e) {
                totalSpace = 0l;
            }
            user.setTotalSpace(totalSpace != null ? totalSpace : 0);
        }
        return user;
    }

    @Override
    public FixturePlaylistSize getFixturePlaylistSize(boolean fromUser, Long userId, Long groupsetId, String idSeasons, Long minSeason) throws SQLException {
        FixturePlaylistSize fixturePlaylistSize;
        TreeMap map = new TreeMap();
        map.put("groupsetId", groupsetId);
        map.put("userId", userId);
        if (idSeasons != null && !idSeasons.isEmpty() && minSeason != null) {

            Calendar cStart;
            if (minSeason < 2000) {
                cStart = new GregorianCalendar();
                cStart.set(minSeason.intValue() + 2000, 0, 1);
            } else {
                cStart = new GregorianCalendar();
                cStart.set(minSeason.intValue(), 6, 15);
            }

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String from = sdf.format(cStart.getTime());
            from = "'" + from + "'";
            map.put("idSeasons", idSeasons);
            map.put("minDate", from);
            map.put("source", true);
        } else {
            map.put("source", false);
        }
        if (fromUser) {
            fixturePlaylistSize = (FixturePlaylistSize) getSqlSession().selectOne("User.getFixturePlaylistSizeByUser", map);
        } else if (groupsetId != null) {
            fixturePlaylistSize = (FixturePlaylistSize) getSqlSession().selectOne("User.getFixturePlaylistSizeByGroupset", map);
        } else {
            fixturePlaylistSize = new FixturePlaylistSize();
        }
        return fixturePlaylistSize;
    }

    @Override
    public User getUserByMail(String mail) throws SQLException {
        List items = getSqlSession().selectList("User.getUserByMail", mail);
        return (items != null && !items.isEmpty()) ? (User) items.get(0) : null;
    }

    @Override
    public void saveUser(User item) throws SQLException {
        if (item.getId() == null) {
            getSqlSession().insert("User.addUser", item);
        } else {
            getSqlSession().update("User.updUser", item);
        }
    }

    @Override
    public void updatePlaylist(Long id, Long idUser, String name, String note) throws SQLException {
        if (id != null && idUser != null && name != null && !name.isEmpty()) {
            TreeMap map = new TreeMap();
            map.put("id", id);
            map.put("idUser", idUser);
            map.put("name", name.trim());
            if (note != null) {
                map.put("note", note);
                map.put("source", true);
            } else {
                map.put("source", false);
            }
            //map.put("note", note);
            getSqlSession().update("Playlist.updatePlaylist", map);
        } else {
            throw new SQLException();
        }
    }

    //Aggiunto in più
    @Override
    public void saveUser2(User item) throws SQLException {
        if (item.getId() == null) {
            getSqlSession().insert("User.addUser", item);
        } else {
            getSqlSession().update("User.updUserName", item);
        }
    }

    @Override
    public void delUser(Long id) throws SQLException {
        getSqlSession().update("User.delUser", id);
    }

    @Override
    public void deleteGame(Long idFixture, Long idUser) throws SQLException {
        if (idFixture != null && idUser != null) {
            TreeMap map = new TreeMap();
            map.put("idFixture", idFixture);
            map.put("idUser", idUser);
            getSqlSession().update("Game.deleteGame", map);
        }
    }

    @Override
    public void deletePlaylist(Long idPlaylist, Long idUser) throws SQLException {
        if (idPlaylist != null && idUser != null) {
            TreeMap map = new TreeMap();
            map.put("idPlaylist", idPlaylist);
            map.put("idUser", idUser);
            getSqlSession().update("Playlist.deletePlaylist", map);
        }
    }

    // ---------------------------------------------------------------------------------------
    @Override
    public List getPlaylistUser(Long idGro, Long idUse) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("idUse", idUse);
        map.put("idGro", idGro);
        List list = getSqlSession().selectList("Playlist.getPlaylistUser", map);
        ArrayList<Playlist> listPlaylist = new ArrayList<>();
        Map<Long, Playlist> mapPlaylist = new HashMap<>();
        for (Object objPlaylist : list) {
            Playlist pl = (Playlist) objPlaylist;
            Long uploadedBy = pl.getUserUploadId();
            Long userIdPl = pl.getUserId();
            String idGroupsetPublic = pl.getVideoPublic();
            // se la playlist è stata caricata dall'utente oppure
            // se la playlist è stata condivisa all'utente oppure
            // se la playlist è stata condivisa al gruppo ma non condivisa dall'utente
            if ((uploadedBy != null && Objects.equals(uploadedBy, idUse))
                    || (userIdPl != null && userIdPl.equals(idUse))
                    || (idGroupsetPublic != null && idGro.toString().equals(idGroupsetPublic) && userIdPl == null)) {
                if (!mapPlaylist.containsKey(pl.getId())) {
                    // se sono playlist condivise all'utente o esportazioni di sicstv allora sono viste
                    if (userIdPl == null || !userIdPl.equals(idUse)) {
                        pl.setSeen(true);
                    }
                    mapPlaylist.put(pl.getId(), pl);
                    listPlaylist.add(pl);
                }
            }
        }
        return listPlaylist;
    }

    @Override
    public Playlist getPlaylist(Long id, Long idUse) throws SQLException {
        List items = getSqlSession().selectList("Playlist.getPlaylist", id);
        Playlist pl = null;
        if (items != null) {
            for (Object obj : items) {
                pl = (Playlist) obj;
                if (pl != null && (Objects.equals(pl.getUserId(), idUse) || pl.getUserId() == null)) {
                    if (pl.getGroupsetId() != 2) {
                        if (pl.getUploadLastName() == null) {
                            pl.setUploadLastName("");
                        }
                        if (pl.getUploadFirstName() == null) {
                            pl.setUploadFirstName("");
                        }
                        pl.setUploadedBy(StringHelper.capitalize(pl.getUploadLastName()) + " " + StringHelper.capitalize(pl.getUploadFirstName()));
                    } else {
                        pl.setUploadedBy("SICS");
                    }
                    break;
                }
            }
        }
        return pl;
    }

    @Override
    public Playlist getPlaylistByVideoName(String videoname, Long userId) throws SQLException {
        TreeMap map = new TreeMap();
        if (userId != null) {
            map.put("source", true);
            map.put("userId", userId);
        } else {
            map.put("source", false);
        }
        map.put("videoname", videoname);
        List items = getSqlSession().selectList("Playlist.getPlaylistByVideoName", map);
        Playlist pl = null;
        if (items != null) {
            for (Object obj : items) {
                pl = (Playlist) obj;
                break;
            }
        }
        return pl;
    }

    @Override
    public Game getGameByVideoName(String videoname, Long ownerUserId, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("videoname", videoname);
        map.put("ownerId", ownerUserId);
        map.put("language", language);
        List items = getSqlSession().selectList("Game.getGameByVideoName", map);
        Game pl = null;
        if (items != null) {
            for (Object obj : items) {
                pl = (Game) obj;
                break;
            }
        }
        return pl;
    }

    @Override
    public void setPlaylistSeen(Long id, Long idUse) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("idUse", idUse);
        map.put("id", id);
        getSqlSession().update("Playlist.setPlaylistSeen", map);
    }

    @Override
    public Long getPlaylistUnseen(Long idUse, Long idGro) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("idUser", idUse);
        map.put("idGroup", idGro);
        Long count = (Long) getSqlSession().selectOne("Playlist.getPlaylistUnseen", map);
        return count;
    }

    @Override
    public List getPlaylistDataAll(Long id) throws SQLException {
        return getSqlSession().selectList("PlaylistData.getPlaylistDataAll", id);
    }

    @Override
    public void addPlaylist(Playlist playlist) throws SQLException {
        getSqlSession().insert("Playlist.addPlaylist", playlist);
    }

    @Override
    public List getGameDataAll(Long id) throws SQLException {
        return getSqlSession().selectList("GameData.getGameDataAll", id);
    }

    //Ritorna una classe con il numero massimo e minimo di partite della giornata
    @Override
    public CalendarRange getMinAndMaxMatchdaysByCompetitionAndSeason(String seasonIds, Long idComp, Long countryId, Long groupId, Boolean personalSource) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("compId", idComp);
        map.put("seasonIds", seasonIds);
        map.put("countryId", countryId);
        map.put("groupId", groupId);
        map.put("source", personalSource);
        GlobalHelper.adjustQueryParams(map);
        List<CalendarRange> result = getSqlSession().selectList("Game.getMinAndMaxMatchdaysByCompetitionAndSeason", map);
        return (result != null && !result.isEmpty()) ? result.get(0) : null;
    }

    //Ritorna una variabile long con il numero massimo di partite della giornata
    @Override
    public CalendarRange getMinAndMaxMatchdays() throws SQLException {
        List<CalendarRange> result = getSqlSession().selectList("Game.getMinAndMaxMatchdays", null);
        return (result != null && !result.isEmpty()) ? result.get(0) : null;
    }

    @Override
    public List getSeasonAll() throws SQLException {
        return getSqlSession().selectList("Season.getSeasonAll", null);
    }

    @Override
    public Season getSeasonById(Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("value", seasonId);
        return (Season) getSqlSession().selectOne("Season.getSeason", map);
    }

    @Override
    public List getSeasonAllAvailable(Long userId, Long groupset_id, List<Competition> allowedCompetitions, Long groupId, List<UserCompetitionException> competitionExceptions, Boolean personalSource) throws SQLException {
        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(fixture.season_id = CASE WHEN fixture.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE fixture.season_id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("groupset_id", groupset_id);
        map.put("user_id", userId);
        map.put("groupId", groupId);
        map.put("personalSource", BooleanUtils.isTrue(personalSource));
        return getSqlSession().selectList("Season.getSeasonAllAvailable", map);
    }

    @Override
    public List getSeasonAllAvailableByParams(Long userId, Long groupsetId, Long countryId, Long intCompId, Long competitionId, Long teamId, List<Competition> allowedCompetitions, Long groupId, List<UserCompetitionException> competitionExceptions, Boolean personalSource) throws SQLException {
        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(fixture.season_id = CASE WHEN fixture.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE fixture.season_id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("user_id", userId);
        map.put("groupsetId", groupsetId);
        map.put("countryId", countryId);
        map.put("intCompId", intCompId);
        map.put("competitionId", competitionId);
        map.put("teamId", teamId);
        map.put("groupId", groupId);
        map.put("personalSource", BooleanUtils.isTrue(personalSource));
        return getSqlSession().selectList("Season.getSeasonAllAvailableByParams", map);
    }

    @Override
    public List getSeasonAllAvailableByPlayerId(Long playerId, Long groupsetId, List<UserCompetitionException> competitionExceptions, Boolean personalSource) throws SQLException {
        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(season.id = CASE WHEN team_player_competition.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE season.id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        map.put("playerId", playerId);
        map.put("groupsetId", groupsetId);
        map.put("personalSource", BooleanUtils.isTrue(personalSource));
        return getSqlSession().selectList("Season.getSeasonAllAvailableByPlayerId", map);
    }

    // NON USATA
    @Override
    public Season getLastSeason(Long userId, Long groupsetId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("groupsetId", groupsetId);
        map.put("userId", userId);
        List items = getSqlSession().selectList("Season.lastSeason", map);
        return (items != null && !items.isEmpty()) ? (Season) items.get(0) : null;
    }

    @Override
    public Season getLastSeasonByCompetitionId(Long competitionId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("competitionId", competitionId);
        return (Season) getSqlSession().selectOne("Season.getLastSeasonByCompetitionId", map);
    }

    @Override
    public List<Country> getCountryVisible(Long idGroupset, String seasonIds, Long idSport, String language) throws SQLException {
        TreeMap map = new TreeMap();
//        map.put("idSea", seasId);
        map.put("idGro", idGroupset);
        map.put("idSport", idSport);
        map.put("language", language);
        return getSqlSession().selectList("Country.getCountryVisible", map);
    }

    @Override
    public List getCountryAll(String seasonIds, Long idSport, Long idGroupset, String allowedCompetitionIds, String language) throws SQLException {
        // dato che è tanto lenta, provo a prendere i game.id e filtrare per quelli al posto di competition_id
//        String from = " FROM game INNER JOIN fixture ON fixture.id = game.fixture_id";
//        from += " INNER JOIN competition ON competition.id = fixture.competition_id";
//        String where = " WHERE";
//        where += " fixture.season_id = " + seasId;
//        where += " AND game.groupset_id = -1";
//        where += " AND competition.id IN(" + allowedCompetitionIds + ")";
//        List<Game> gameIdsList = getGameIdsListByFilters(from, where);
//        List<String> gameIds = new ArrayList<>();
//        for (Game game : gameIdsList) {
//            gameIds.add(game.getId().toString());
//        }

        List<Season> seasons = getSeasonAll();
        List<String> seasonIdList = new ArrayList<>();
        for (Season season : seasons) {
            seasonIdList.add(season.getId().toString());
        }

        final TreeMap map = new TreeMap();
//        map.put("idSea", seasId);
        map.put("idSport", idSport);
        map.put("idGroupset", idGroupset);
        map.put("language", language);
        map.put("competitionIds", allowedCompetitionIds);
        map.put("seasonIds", StringUtils.join(seasonIdList, ","));
        GlobalHelper.adjustQueryParams(map);

        final CountDownLatch latch = new CountDownLatch(2);
        final List<Country> homeValues = new ArrayList<>();
        final List<Country> awayValues = new ArrayList<>();

        try {
            new Thread() {
                @Override
                public void run() {
                    try {
                        homeValues.addAll(getSqlSession().selectList("Country.getCountryAllWithVisibleHome", map));
                    } catch (Exception ex) {
                        GlobalHelper.reportError(ex);
                    } finally {
                        latch.countDown();
                    }
                }
            }.start();
            new Thread() {
                @Override
                public void run() {
                    try {
                        awayValues.addAll(getSqlSession().selectList("Country.getCountryAllWithVisibleAway", map));
                    } catch (Exception ex) {
                        GlobalHelper.reportError(ex);
                    } finally {
                        latch.countDown();
                    }
                }
            }.start();
            latch.await();

            for (Country country : homeValues) {
                for (Country secondCountry : awayValues) {
                    if (country.getName().equals(secondCountry.getName())
                            && Long.compare(country.getCompetitionId(), secondCountry.getCompetitionId()) == 0) {
                        country.setGameAmount(country.getGameAmount() + secondCountry.getGameAmount());
                        break;
                    }
                }
            }
        } catch (InterruptedException ex) {
            GlobalHelper.reportError(ex);
        }

//        map.put("gameIds", StringUtils.join(gameIds, ","));
        return homeValues;
    }

    @Override
    public List getBaseCountry(String seasonIds, Long idSport, String language) throws SQLException {
        TreeMap map = new TreeMap();
//        map.put("idSea", seasId);
        map.put("idSport", idSport);
        map.put("language", language);
        return getSqlSession().selectList("Country.getCountryAll", map);
    }

    @Override
    public Country getCountry(Long id, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("id", id);
        map.put("language", language);
        List items = getSqlSession().selectList("Country.getCountry", map);
        return (items != null && !items.isEmpty()) ? (Country) items.get(0) : null;
    }

    @Override
    public List getInternationalCompetitionVisible(Long idGroupset, String seasonIds, Long idSport, String language) throws SQLException {
        TreeMap map = new TreeMap();
        //map.put("idSea", seasId);
        map.put("idGro", idGroupset);
        map.put("idSport", idSport);
        map.put("language", language);
        return getSqlSession().selectList("InternationalCompetition.getInternationalCompetitionVisible", map);
    }

    @Override
    public List getInternationalCompetitionAll(String seasonIds, Long idSport, String language) throws SQLException {
        TreeMap map = new TreeMap();
        //map.put("idSea", seasId);
        map.put("idSport", idSport);
        map.put("language", language);
        return getSqlSession().selectList("InternationalCompetition.getInternationalCompetitionAll", map);
    }

    @Override
    public Country getInternationalCompetition(Long id, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("id", id);
        map.put("language", language);
        List items = getSqlSession().selectList("InternationalCompetition.getInternationalCompetition", map);
        return (items != null && !items.isEmpty()) ? (Country) items.get(0) : null;
    }

    @Override
    public List getCompetitionAll(Long idGroupset, String seasonIds, Boolean source, Long idSport, List<Competition> allowedCompetitions, String language) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("seasonIds", seasonIds);
        map.put("idGro", idGroupset);
        map.put("idSport", idSport);
        map.put("source", source);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Competition.getCompetitionAll", map);
    }

    @Override
    public List getCompetitionByCountry(Long idGroupset, String seasonIds, Boolean source, Long idSport, String language, Long idCountry, Long idInternationalCompetition, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(fixture.season_id = CASE WHEN fixture.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE fixture.season_id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        // map.put("seasonIds", seasonIds);
        map.put("idGro", idGroupset);
        map.put("idSport", idSport);
        map.put("source", source);
        map.put("language", language);
        map.put("idCountry", idCountry);
        map.put("idInternationalCompetition", idInternationalCompetition);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Competition.getCompetitionByCountry", map);
    }

    @Override
    public List<Competition> getInternationalCompetitionByCountry(Long idGroupset, String seasonIds, Long idSport, String language, Long idCountry, List<UserCompetitionException> competitionExceptions) throws SQLException {
        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(fixture.season_id = CASE WHEN fixture.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE fixture.season_id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        // map.put("seasonIds", seasonIds);
        map.put("idGro", idGroupset);
        map.put("idSport", idSport);
        map.put("language", language);
        map.put("idCountry", idCountry);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Competition.getInternationalCompetitionByCountry", map);
    }

    @Override
    public List getCompetitionBySeasons(Long idGroupset, String seasId, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("idSea", seasId);
        map.put("idGro", idGroupset);
        map.put("language", language);
        return getSqlSession().selectList("Competition.getCompetitionBySeasons", map);
    }

    @Override
    public List getCompetitionByGroupsetId(Long idGroupset, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("idGro", idGroupset);
        map.put("language", language);
        List<Competition> competitions = getSqlSession().selectList("Competition.getCompetitionByGroupsetId", map);

        // escludo per TUTTI la competizione "TEST", id 272
        List<Competition> competitionToExclude = new ArrayList<>();
        for (Competition competition : competitions) {
            if (Long.compare(competition.getId(), 272L) == 0) {
                competitionToExclude.add(competition);
                // per ora faccio il break perchè c'è solo questa
                break;
            }
        }
        if (!competitionToExclude.isEmpty()) {
            competitions.removeAll(competitionToExclude);
        }
        return competitions;
    }

    @Override
    public List getCompetitionByTeam(Long idGroupset, String seasonIds, Long teamId, Boolean source, List<Competition> allowedCompetitions, String language) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("seasonIds", seasonIds);
        map.put("idGro", idGroupset);
        map.put("idTeam", teamId);
        map.put("source", source);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Competition.getCompetitionByTeam", map);
    }

    @Override
    public List getCompetitionByPlayer(String seasonIds, Long playerId, Long groupsetId, Long groupId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("seasonIds", seasonIds);
        map.put("idPlayer", playerId);
        map.put("idGroup", groupsetId);
        map.put("groupId", groupId);
        map.put("source", personalSource);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Competition.getCompetitionByPlayer", map);
    }

    @Override
    public List getCompetitionByTeamPlayer(String seasonIds, Long playerId, Long teamId, Long groupsetId, Long groupId, Boolean personalSource, List<Competition> allowedCompetitions, String language) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("seasonIds", seasonIds);
        map.put("idPlayer", playerId);
        map.put("idTeam", teamId);
        map.put("idGroup", groupsetId);
        map.put("groupId", groupId);
        map.put("source", personalSource);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Competition.getCompetitionByTeamPlayer", map);
    }

    @Override
    public Competition getCompetition(Long id, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("id", id);
        map.put("language", language);
        List items = getSqlSession().selectList("Competition.getCompetition", map);
        return (items != null && !items.isEmpty()) ? (Competition) items.get(0) : null;
    }

    @Override
    public List<Competition> getCompetitions(String ids, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("ids", ids);
        map.put("language", language);
        return getSqlSession().selectList("Competition.getCompetitions", map);
    }

    @Override
    public List getTeamAll(String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("language", language);
        return getSqlSession().selectList("Team.getTeamAll", map);
    }

    @Override
    public List getTeamByCompetition(String seasonIds, Long compId, Long idGro, Long sportId, Boolean source, String gameIds, Long countryId, List<Competition> allowedCompetitions, Long groupId, String language) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("seasonIds", seasonIds);
        map.put("compId", compId);
        map.put("idGro", idGro);
        map.put("sportId", sportId);
        map.put("source", source);
        map.put("language", language);
        map.put("gameIds", gameIds);
        map.put("countryId", countryId);
        map.put("groupId", groupId);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Team.getTeamByCompetition", map);
    }

    // NON USATA
    @Override
    public List getTeamByMultiFilter(String seasIds, String compIds, String language) throws SQLException {

        // da sistemare se serve
        // da sistemare se serve
        // da sistemare se serve
        if (!seasIds.isEmpty() || !compIds.isEmpty()) {
            String where = "where game.groupset_id = 2";
            if (!seasIds.isEmpty()) {
                where += " and fixture.season_id IN (" + seasIds + ")";
            }
            if (!compIds.isEmpty()) {
                where += " and fixture.competition_id IN (" + compIds + ")";
            }
            TreeMap map = new TreeMap();
//            map.put("where", where);
            map.put("language", language);
            //where += " ORDER BY team.name";
            return getSqlSession().selectList("Team.getTeamByMultiFilter", map);
        } else {
            return getTeamAll(language);
        }
    }

    @Override
    public List getTeamByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language, List<UserCompetitionException> competitionExceptions) throws SQLException {
        // 08/08/2023: è stato deciso di non filtrare per seasonId, guarderò comunque per season.visible = 1
        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(fixture.season_id = CASE WHEN fixture.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE fixture.season_id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }
        if (personalSource) {
            String where = " where team.name LIKE \"%" + pattern + "%\" and sport_id = " + sportId + " and season.visible = 1 and fixture.provider_id = 3 AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ") AND fixture.video_name IS NOT NULL ";
            String order = " ORDER BY case when team_name like \"%" + pattern + "%\" then 0 else 1 end, team_name, season_name DESC";
            map.put("where", where);
            map.put("order", order);
            map.put("language", language);
            return getSqlSession().selectList("Team.getTeamByPatternPersonal", map);
        } else {
            String name = " (CASE WHEN team.name" + language.replace("_it", "") + " IS NULL THEN team.name ELSE team.name" + language.replace("_it", "") + " END) ";
            String where = " where " + name + " LIKE \"%" + pattern + "%\" AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ") AND fixture.video_name IS NOT NULL and sport_id = " + sportId + " and season.visible = 1 and fixture.provider_id = 2 and fixture.owner_user_id=1";
            String order = " ORDER BY case when team_name like \"%" + pattern + "%\" then 0 else 1 end, team_name, season_name DESC";
            map.put("where", where);
            map.put("order", order);
            map.put("language", language);
            return getSqlSession().selectList("Team.getTeamByPatternSICS", map);
        }
    }

    @Override
    public List getCompetitionByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language, List<UserCompetitionException> competitionExceptions) throws SQLException {
        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(fixture.season_id = CASE WHEN fixture.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE fixture.season_id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        if (personalSource) {
            String where = " where competition.name LIKE \"%" + pattern + "%\" and sport_id = " + sportId + " and season.visible = 1 and fixture.provider_id = 3 AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ") AND fixture.video_name IS NOT NULL ";
            String order = " ORDER BY case when competition_name like \"%" + pattern + "%\" then 0 else 1 end, competition_name, season_name DESC";
            map.put("where", where);
            map.put("order", order);
            map.put("language", language);
            return getSqlSession().selectList("Competition.getCompetitionByPatternPersonal", map);
        } else {
            String name = " (CASE WHEN competition.name" + language + " IS NULL THEN competition.name ELSE competition.name" + language + " END) ";
            String where = " where " + name + " LIKE \"%" + pattern + "%\" AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ") AND fixture.video_name IS NOT NULL and sport_id = " + sportId + " and season.visible = 1 and fixture.provider_id = 2 and fixture.owner_user_id=1";
            String order = " ORDER BY case when competition_name like \"%" + pattern + "%\" then 0 else 1 end, competition_name, season_name DESC";
            map.put("where", where);
            map.put("order", order);
            map.put("language", language);
            return getSqlSession().selectList("Competition.getCompetitionByPatternSICS", map);
        }
    }

    @Override
    public List<Country> getCountryByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<Competition> allowedCompetitions, String language, List<UserCompetitionException> competitionExceptions) throws SQLException {
        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(fixture.season_id = CASE WHEN fixture.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE fixture.season_id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        if (personalSource) {
            String where = " where country.name_it LIKE \"%" + pattern + "%\" and sport_id = " + sportId + " and season.visible = 1 and fixture.provider_id = 3 AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ")";
            String order = " ORDER BY case when country_name like \"%" + pattern + "%\" then 0 else 1 end, country_name, season_name DESC";
            map.put("where", where);
            map.put("order", order);
            map.put("language", language);
            return getSqlSession().selectList("Country.getCountryByPatternPersonal", map);
        } else {
            String name = " (CASE WHEN country.name" + language + " IS NULL THEN country.name_it ELSE country.name" + language + " END)";
            String where = " where " + name + " LIKE \"%" + pattern + "%\" AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ") and sport_id = " + sportId + " and season.visible = 1 and fixture.provider_id = 2 and fixture.owner_user_id = 1";
            String order = " ORDER BY case when country_name like \"%" + pattern + "%\" then 0 else 1 end, country_name, season_name DESC";
            map.put("where", where);
            map.put("order", order);
            map.put("language", language);
            return getSqlSession().selectList("Country.getCountryByPatternSICS", map);
        }
    }

    @Override
    public Team getTeam(Long id, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("where", id);
        map.put("language", language);
        List items = getSqlSession().selectList("Team.getTeam", map);
        return (items != null && !items.isEmpty()) ? (Team) items.get(0) : null;
    }

    @Override
    public List<Team> getTeams(String ids, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("ids", ids);
        map.put("language", language);
        return getSqlSession().selectList("Team.getTeams", map);
    }

    @Override
    public List getTeamByGroupsetId(Long idGroupset, String language, List<Competition> allowedCompetitions) throws SQLException {
        List<Season> seasons = getSeasonAll();

        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        List<String> seasonIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }
        for (Season season : seasons) {
            seasonIdList.add(season.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("seasonIds", StringUtils.join(seasonIdList, ","));
        map.put("idGro", idGroupset);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Team.getTeamByGroupsetId", map);
    }

    @Override
    public List getNationalTeamByGroupsetId(Long idGroupset, String seasonIds, String language, Long idCountry, List<Competition> allowedCompetitions) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("idGro", idGroupset);
        map.put("language", language);
        // map.put("seasonIds", seasonIds);
        map.put("idCountry", idCountry);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Team.getNationalTeamByGroupsetId", map);
    }

    // NON USATA
    @Override
    public List getNationalTeam(Long idSport, String seasonIds, Long idCountry, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("idSport", idSport);
        map.put("seasonIds", seasonIds);
        map.put("idCountry", idCountry);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Team.getNationalTeam", map);
    }

    // ---------------------------------------------------------------------------------------
    @Override
    public List getGameAll(Long groupId, String seasonIds, Long compId, Long teamId, Date from, Date to, String referee, Long matchdayId, String language) throws SQLException {

        TreeMap map = new TreeMap();
        map.put("idGro", groupId);

        String strFilter = "";

        if (seasonIds != null) {
            strFilter += " and fixture.season_id IN(" + seasonIds + ")";
        }
        if (compId != null) {
            strFilter += " and fixture.competition_id=" + compId;
        }
        if (teamId != null) {
            strFilter += " and (fixture.home_team_id=" + teamId + " or fixture.away_team_id=" + teamId + ")";

        }

        /*Rimosso perchè non fa visualizzare le partite nella library.htm*/
 /*if( referee != null && referee.length()>0 ) {
		 strFilter = " and (ref.last_name like '"+referee+"%' or ass1.last_name like '"+referee+"%' or ass2.last_name like '"+referee+"%' or ass4.last_name like '"+referee+"%')" ;
		 }
		 if( matchdayId != null ) {
		 strFilter += " and fixture.matchday="+matchdayId;
		 } else {
		 if( from != null && to != null) {
		 strFilter += " and (fixture.game_date >= '"+DateHelper.toSqlDate(from)+"' and fixture.game_date <='"+DateHelper.toSqlDate(DateHelper.sumDates(to, 1))+"') ";
		 } else if( from != null && to == null) {
		 strFilter += " and fixture.game_date >= '"+DateHelper.toSqlDate(from)+"' ";
		 } else if( from == null && to != null) {
		 strFilter += " and fixture.game_date <= '"+DateHelper.toSqlDate(DateHelper.sumDates(to, 1))+"' ";
		 }
		 }
		 //String strFilter = "";
		 if (seasId != null) {
		 strFilter += " and fixture.season_id=" + seasId;
		 }
		 if (compId != null) {
		 strFilter += " and fixture.competition_id=" + compId;
		 }
         */
        if (groupId != null && groupId.equals(GlobalHelper.kGroupsetAia)) {
            if (referee != null && referee.length() > 0) {
                strFilter = " and (ref.last_name like '" + referee + "%' or ass1.last_name like '" + referee + "%' or ass2.last_name like '" + referee + "%' or ass4.last_name like '" + referee + "%')";
            }
        }

        if (matchdayId != null) {
            strFilter += " and fixture.matchday=" + matchdayId;
        } else {
            if (from != null && to != null) {
                strFilter += " and (fixture.game_date >= '" + DateHelper.toSqlDate(from) + "' and fixture.game_date <='" + DateHelper.toSqlDate(DateHelper.sumDates(to, 1)) + "') ";
            } else if (from != null && to == null) {
                strFilter += " and fixture.game_date >= '" + DateHelper.toSqlDate(from) + "' ";
            } else if (from == null && to != null) {
                strFilter += " and fixture.game_date <= '" + DateHelper.toSqlDate(DateHelper.sumDates(to, 1)) + "' ";
            }
        }
        map.put("strFilter", (strFilter.isEmpty() ? null : strFilter));
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Game.getGameAll", map);
    }

    @Override
    public List getGameAllDigital(Long groupId, String seasonIds, Long compId, Long teamId, Date from, Date to, String referee, Long matchdayId) throws SQLException {

        TreeMap map = new TreeMap();
        map.put("idGro", groupId);

        String strFilter = "";

        if (seasonIds != null) {
            strFilter += " and fixture.season_id IN(" + seasonIds + ")";
        }
        if (compId != null) {
            strFilter += " and fixture.competition_id=" + compId;
        }
        if (teamId != null) {
            strFilter += " and (fixture.home_team_id=" + teamId + " or fixture.away_team_id=" + teamId + ")";

        }

        /*Rimosso perchè non fa visualizzare le partite nella library.htm*/
 /*if( referee != null && referee.length()>0 ) {
		 strFilter = " and (ref.last_name like '"+referee+"%' or ass1.last_name like '"+referee+"%' or ass2.last_name like '"+referee+"%' or ass4.last_name like '"+referee+"%')" ;
		 }
		 if( matchdayId != null ) {
		 strFilter += " and fixture.matchday="+matchdayId;
		 } else {
		 if( from != null && to != null) {
		 strFilter += " and (fixture.game_date >= '"+DateHelper.toSqlDate(from)+"' and fixture.game_date <='"+DateHelper.toSqlDate(DateHelper.sumDates(to, 1))+"') ";
		 } else if( from != null && to == null) {
		 strFilter += " and fixture.game_date >= '"+DateHelper.toSqlDate(from)+"' ";
		 } else if( from == null && to != null) {
		 strFilter += " and fixture.game_date <= '"+DateHelper.toSqlDate(DateHelper.sumDates(to, 1))+"' ";
		 }
		 }
		 //String strFilter = "";
		 if (seasId != null) {
		 strFilter += " and fixture.season_id=" + seasId;
		 }
		 if (compId != null) {
		 strFilter += " and fixture.competition_id=" + compId;
		 }
         */
        if (groupId.equals(GlobalHelper.kGroupsetAia)) {
            if (referee != null && referee.length() > 0) {
                strFilter = " and (ref.last_name like '" + referee + "%' or ass1.last_name like '" + referee + "%' or ass2.last_name like '" + referee + "%' or ass4.last_name like '" + referee + "%')";
            }
        }

        if (matchdayId != null) {
            strFilter += " and fixture.matchday=" + matchdayId;
        } else {
            if (from != null && to != null) {
                strFilter += " and (fixture.game_date >= '" + DateHelper.toSqlDate(from) + "' and fixture.game_date <='" + DateHelper.toSqlDate(DateHelper.sumDates(to, 1)) + "') ";
            } else if (from != null && to == null) {
                strFilter += " and fixture.game_date >= '" + DateHelper.toSqlDate(from) + "' ";
            } else if (from == null && to != null) {
                strFilter += " and fixture.game_date <= '" + DateHelper.toSqlDate(DateHelper.sumDates(to, 1)) + "' ";
            }
        }

        strFilter += " and fixture.id IN(SELECT DISTINCT(fixture_id) FROM history INNER JOIN game ON game.id = history.game_id WHERE level_id = 3)";
        map.put("strFilter", strFilter);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Game.getGameAll", map);
    }

    @Override
    public Game getGame(Long id, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("where", id);
        map.put("language", language);
        List items = getSqlSession().selectList("Game.getGame", map);
        return (items != null && !items.isEmpty()) ? (Game) items.get(0) : null;
    }

    @Override
    public List<Game> getGamesByFixtureIds(List<Long> ids, Long groupsetId, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("where", StringUtils.join(ids, ","));
        map.put("groupsetId", groupsetId);
        map.put("language", language);
        return getSqlSession().selectList("Game.getGamesByFixtureIds", map);
    }

    @Override
    public Game getGameByFixtureId(Long id, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("where", id);
        map.put("language", language);
        List items = getSqlSession().selectList("Game.getGameByFixtureId", map);
        return (items != null && !items.isEmpty()) ? (Game) items.get(0) : null;
    }

    @Override
    public List getGameByCompetitionId(Long compId, String seasonIds, Long groupsetId, Long startFrom, Boolean source, Long countryId, Long groupId, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("idGro", groupsetId);
        map.put("compId", compId);
        map.put("seasonIds", seasonIds);
        map.put("startFrom", startFrom);
        map.put("source", source);
        map.put("countryId", countryId);
        map.put("groupId", groupId);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Game.getGameByCompetitionId", map);
    }

    @Override
    public List getGameBySeason(String seasonIds, Long groupId, Long startFrom, Boolean source, String language) {
        TreeMap map = new TreeMap();
        map.put("idGro", groupId);
        map.put("seasonIds", seasonIds);
        map.put("startFrom", startFrom);
        map.put("source", source);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Game.getGameBySeason", map);
    }

    @Override
    public List getGameByTeamId(Long compId, String seasonIds, Long groupsetId, Long teamId, Long startFrom, Boolean source, String language, Integer limit, Long groupId) {
        TreeMap map = new TreeMap();
        map.put("idGro", groupsetId);
        map.put("compId", compId);
        map.put("seasonIds", seasonIds);
        map.put("teamId", teamId);
        map.put("startFrom", startFrom);
        map.put("source", source);
        map.put("language", language);
        map.put("limit", limit);
        map.put("groupId", groupId);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Game.getGameByTeamId", map);
    }

    @Override
    public List getGameMulti(Long compId, String seasonIds, Long groupId, Long teamId, Long dayId, Boolean source, String language) {
        TreeMap map = new TreeMap();
        map.put("idGro", groupId);
        map.put("compId", compId);
        map.put("seasonIds", seasonIds);
        map.put("teamId", teamId);
        map.put("dayId", dayId);
        map.put("source", source);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Game.getGameMulti", map);
    }

    @Override
    public List getGameByMultiFilter(Map<String, String> mapFilters, Long startFrom, Boolean source, String language) {
//    public List getGameByMultiFilter(String groupId, String seasonId, String compId, String teamId, String idPlayer, String dateF, String dateT, String homeAway, String module,String moduleVs Long startFrom) {
        if (mapFilters.containsKey("groupsetId") && mapFilters.containsKey("seasonId")) {

            String where = "";
            if (mapFilters.containsKey("playerId") && !mapFilters.get("playerId").isEmpty() && !source) {
                // non faccio join se personal perchè non ci sono le statistiche
                where += " INNER JOIN player_stats ON player_stats.fixture_id = fixture.id AND player_stats.player_id = " + mapFilters.get("playerId");
            }
            where += " left join fixture_player on fixture.id = fixture_player.fixture_id where fixture.season_id IN (" + mapFilters.get("seasonId") + ")";

            if (mapFilters.containsKey("compId") && !mapFilters.get("compId").isEmpty()) {
                where += " and fixture.competition_id IN (" + mapFilters.get("compId") + ")";
            }

            if (mapFilters.containsKey("playerId") && !mapFilters.get("playerId").isEmpty()) {
                where += " AND fixture_player.player_id IN (" + mapFilters.get("playerId") + ")";
            }

            if (mapFilters.containsKey("groupId") && mapFilters.get("groupId") != null && !mapFilters.get("groupId").isEmpty()) {
                where += " AND fixture.group_id = " + mapFilters.get("groupId");
            }

            if (mapFilters.containsKey("teamId") && !mapFilters.get("teamId").isEmpty()) {
                // nella pagina team.jsp, applicando questo filtro tira fuori solo le partite taggate
                // invece servono tutte (usato per calcolare partite giocate, vinte, perse)
                if (!mapFilters.containsKey("ignoreFixturePlayer")) {
                    where += " AND fixture_player.team_id IN (" + mapFilters.get("teamId") + ")";
                }

                if (mapFilters.containsKey("homeAway")) {
                    switch (mapFilters.get("homeAway")) {
                        case "home":
                            where += " and fixture.home_team_id IN (" + mapFilters.get("teamId") + ")";
                            break;
                        case "away":
                            where += " and fixture.away_team_id IN (" + mapFilters.get("teamId") + ")";
                            break;
                        case "vs":
                            // se è vs devo cercare le partite in cui le sue squadre hanno giocato contro
                            String[] splitTeam = mapFilters.get("teamId").split(",");
                            if (splitTeam.length == 2) {
                                where += " and ((fixture.home_team_id = " + splitTeam[0] + " and fixture.away_team_id = (" + splitTeam[1] + ")) "
                                        + "or (fixture.home_team_id = " + splitTeam[1] + " and fixture.away_team_id = (" + splitTeam[0] + ")))";
                            }
                            break;
                        default:
                            where += " and (fixture.away_team_id IN (" + mapFilters.get("teamId") + ") or fixture.home_team_id IN (" + mapFilters.get("teamId") + "))";
                            break;
                    }
                } else {
                    where += " and (fixture.away_team_id IN (" + mapFilters.get("teamId") + ") or fixture.home_team_id IN (" + mapFilters.get("teamId") + "))";
                }

            }
            if (mapFilters.containsKey("dateF") && !mapFilters.get("dateF").isEmpty()) {
                where += " and fixture.game_date >= '" + mapFilters.get("dateF") + "'";
            }
            if (mapFilters.containsKey("dateT") && !mapFilters.get("dateT").isEmpty()) {
                where += " and fixture.game_date <= '" + mapFilters.get("dateT") + "'";
            }

            if (mapFilters.containsKey("module") && !mapFilters.get("module").isEmpty()) {
                if (mapFilters.containsKey("teamId") && !mapFilters.get("teamId").isEmpty()) {
                    where += " and ((fixture.home_team_id IN (" + mapFilters.get("teamId") + ") and fixture.home_module = '" + mapFilters.get("module") + "') or (fixture.away_team_id IN (" + mapFilters.get("teamId") + ") and fixture.away_module = '" + mapFilters.get("module") + "'))";
                } else {
                    where += " and (fixture.home_module = '" + mapFilters.get("module") + "' or fixture.away_module = '" + mapFilters.get("module") + "')";
                }
            }
            if (mapFilters.containsKey("moduleVs") && !mapFilters.get("moduleVs").isEmpty()) {
                if (mapFilters.containsKey("teamId") && !mapFilters.get("teamId").isEmpty()) {
                    where += " and ((fixture.home_team_id IN (" + mapFilters.get("teamId") + ") and fixture.away_module = '" + mapFilters.get("moduleVs") + "') or (fixture.away_team_id IN (" + mapFilters.get("teamId") + ") and fixture.home_module = '" + mapFilters.get("moduleVs") + "'))";
                } else {
                    where += " and (fixture.home_module = '" + mapFilters.get("module") + "' or fixture.away_module = '" + mapFilters.get("module") + "')";
                }
            }

            if (mapFilters.containsKey("score") && !mapFilters.get("score").isEmpty() && mapFilters.containsKey("teamId") && !mapFilters.get("teamId").isEmpty()) {
                String[] splitScore = mapFilters.get("score").split(",");
                for (String res : splitScore) {
                    if (!res.isEmpty()) {
                        switch (res) {
                            case "V":
                                where += " and ((fixture.home_team_id IN (" + mapFilters.get("teamId") + ") and fixture.home_score > fixture.away_score) or (fixture.away_team_id IN (" + mapFilters.get("teamId") + ") and fixture.away_score > fixture.home_score))";
                                break;

                            case "N":
                                where += " and ((fixture.home_team_id IN (" + mapFilters.get("teamId") + ") and fixture.home_score = fixture.away_score) or (fixture.away_team_id IN (" + mapFilters.get("teamId") + ") and fixture.away_score = fixture.home_score))";
                                break;

                            case "P":
                                where += " and ((fixture.home_team_id IN (" + mapFilters.get("teamId") + ") and fixture.home_score < fixture.away_score) or (fixture.away_team_id IN (" + mapFilters.get("teamId") + ") and fixture.away_score < fixture.home_score))";
                                break;
                        }

                    }
                }

            }

            if (source) {
                where += " AND fixture.video_name IS NOT NULL AND fixture.provider_id=3 AND fixture.deleted=0";
            } else {
                where += " AND teama.groupset_id = -1 AND teamh.groupset_id = -1 AND fixture.provider_id != 3";
            }

            // filtro per partite selezionate
            if (mapFilters.containsKey("gameIds") && !mapFilters.get("gameIds").isEmpty()) {
                where += " AND fixture.id IN (" + mapFilters.get("gameIds") + ")";
            }

            if (mapFilters.containsKey("countryId")) {
                where += " AND (teamh.country_id = " + mapFilters.get("countryId") + " OR teama.country_id = " + mapFilters.get("countryId") + ")";
            }

            if (mapFilters.containsKey("competitionIds")) {
                where += " AND fixture.competition_id IN(" + mapFilters.get("competitionIds") + ")";
            }

            if (mapFilters.containsKey("from")) {
                String from = mapFilters.get("from");
                if (StringUtils.isNotBlank(from)) {
                    where += " AND fixture.game_date >= STR_TO_DATE(\"" + from + "\", \"%d/%m/%Y\")";
                }
            }

            if (mapFilters.containsKey("to")) {
                String to = mapFilters.get("to");
                if (StringUtils.isNotBlank(to)) {
                    where += " AND fixture.game_date <= STR_TO_DATE(\"" + to + "\", \"%d/%m/%Y\")";
                }
            }

            if (mapFilters.containsKey("matchDay")) {
                String matchDay = mapFilters.get("matchDay");
                if (StringUtils.isNotBlank(matchDay)) {
                    where += " AND fixture.matchday = " + matchDay;
                }
            }

            // togliere partite giornata 99 -> fabio baggio 09/12/2024
            where += " AND IFNULL(fixture.matchday, 0) != 99";

            where += " GROUP BY fixture.id ORDER BY fixture.game_date desc";
            if (startFrom >= 0) {
                where += " LIMIT " + startFrom.toString() + ",25";
            }
            TreeMap map = new TreeMap();
            map.put("where", where);
            map.put("language", language);
            return getSqlSession().selectList("Game.getGameByMultiFilter", map);
        } else {
            return new ArrayList<Game>();
        }

    }

    @Override
    public List getLogDataAll(Long idRes) throws SQLException {
        TreeMap map = new TreeMap();
        if (idRes.equals(GlobalHelper.kApplicationAia)) {
            map.put("app", "aia");
        } else {
            map.put("app", "sicstv");
        }
        return getSqlSession().selectList("LogData.getLogDataAll", map);
    }

    @Override
    public void saveLogData(LogData item) throws SQLException {
        getSqlSession().insert("LogData.addLogData", item);
    }

    //Filtro su azioni degli utenti
    @Override
    public List getLogDataFilter(Date dateFrom, Date dateTo, Long idVideo, String userName, String action, String application) throws SQLException {

        TreeMap map = new TreeMap();
        String logFilter = "where";

        if (!userName.isEmpty()) {
            logFilter += " logdata.loginname = '" + userName + "' ";
            if (dateFrom != null && dateTo != null) {
                logFilter += " and logdata.date between '" + DateHelper.toSqlDate(dateFrom) + "' and '" + DateHelper.toSqlDate(DateHelper.sumDates(dateTo, 1)) + "'";
            }
            if (idVideo != null) {
                logFilter += " and logdata.video_id = '" + idVideo + "' ";
            }
            if (!action.isEmpty()) {
                logFilter += " and logdata.action IN (" + action + ") ";
            }
            if (!application.isEmpty()) {
                logFilter += " and logdata.application = '" + application + "' ";
            }
        } else {
            if (idVideo != null) {
                logFilter += " logdata.video_id = '" + idVideo + "' ";
                if (dateFrom != null && dateTo != null) {
                    logFilter += " and logdata.date between '" + DateHelper.toSqlDate(dateFrom) + "' and '" + DateHelper.toSqlDate(DateHelper.sumDates(dateTo, 1)) + "'";
                }
                if (!action.isEmpty()) {
                    logFilter += " and logdata.action IN (" + action + ") ";
                }
                if (!application.isEmpty()) {
                    logFilter += " and logdata.application = '" + application + "' ";
                }
            } else if (!action.isEmpty()) {
                logFilter += " logdata.action IN (" + action + ") ";
                if (dateFrom != null & dateTo != null) {
                    logFilter += " and logdata.date between '" + DateHelper.toSqlDate(dateFrom) + "' and '" + DateHelper.toSqlDate(DateHelper.sumDates(dateTo, 1)) + "'";
                }
                if (!application.isEmpty()) {
                    logFilter += " and logdata.application = '" + application + "' ";
                }
            } else if (!application.isEmpty()) {
                logFilter += " logdata.application = '" + application + "' ";
                if (dateFrom != null & dateTo != null) {
                    logFilter += " and logdata.date between '" + DateHelper.toSqlDate(dateFrom) + "' and '" + DateHelper.toSqlDate(DateHelper.sumDates(dateTo, 1)) + "'";
                }
            } else if (dateFrom != null && dateTo != null) {
                logFilter += " logdata.date between '" + DateHelper.toSqlDate(dateFrom) + "' and '" + DateHelper.toSqlDate(DateHelper.sumDates(dateTo, 1)) + "'";
            }
        }
        map.put("logFilter", logFilter);
        if (action.isEmpty() && dateFrom == null && dateTo == null && userName.isEmpty() && idVideo == null) {
            String logAll = "";
            map.put("logFilter", logAll);
        }
        return getSqlSession().selectList("LogData.getLogDataFilter", map);
    }

    //Dettagli Download singolo utente
    @Override
    public List getDownloadDet(Long id) throws SQLException {
        return getSqlSession().selectList("LogData.selectDownload", id);
    }

    //Dettagli Streaming singolo utente
    @Override
    public List getStreamingDet(Long id) throws SQLException {
        return getSqlSession().selectList("LogData.selectStreaming", id);
    }

    @Override
    public Long getUserDownload(Long idUser) throws SQLException {
        Calendar c = new GregorianCalendar();
        c.add(Calendar.DATE, - 7);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String c7 = sdf.format(c.getTime());
        TreeMap map = new TreeMap();
        map.put("userId", idUser);
        //map.put("userId", 300);
        map.put("dateFrom", c7);
        Long result = (Long) getSqlSession().selectOne("LogData.getUserDownload", map);
        return result;
    }

    @Override
    public Long getUserXmlJsonExport(Long userId) throws SQLException {
        Calendar c = new GregorianCalendar();
        c.add(Calendar.DATE, - 7);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String c7 = sdf.format(c.getTime());
        TreeMap map = new TreeMap();
        map.put("userId", userId);
        map.put("dateFrom", c7);
        Long result = (Long) getSqlSession().selectOne("LogData.getUserXmlJsonExport", map);
        return result;
    }

    @Override
    public Atleta getAtletaById(String seasonIds, Long playerId, Long teamId, Long groupsetId, Boolean personalSource, String language) throws SQLException {
        Atleta player = null;
        TreeMap map = new TreeMap();
        map.put("idPlayer", playerId);
        map.put("seasonIds", seasonIds);
        map.put("idTeam", teamId == null ? -1 : teamId);
        map.put("source", personalSource);
        map.put("groupsetId", groupsetId);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        List listPlayers = getSqlSession().selectList("Atleta.getAtletaById", map);
        if (!listPlayers.isEmpty()) {
            player = (Atleta) listPlayers.get(0);
        }
        if (player != null && player.getPosition_detail_it().isEmpty()) {
            if (listPlayers.size() > 1) {
                for (Atleta at : (List<Atleta>) listPlayers) {
                    if (!at.getPosition_secondary_it().isEmpty() || !at.getPosition_detail_it().isEmpty()) {
                        player = at;
                    }
                }
            }
        }

        if (player != null) {
            List teams = getSqlSession().selectList("Team.getTeamByPlayer", map);
            for (Object objTeam : teams) {
                Team team = (Team) objTeam;
                if (team != null) {
                    player.getTeamPlayer().add(team);
                }
            }
        }
        return player;
    }

    @Override
    public List getAtletiByGame(Long idFixture, Long idTeamH, Long idTeamA, Long groupsetId, String language) throws SQLException {

        TreeMap map = new TreeMap();
        map.put("fixtureId", idFixture);
        map.put("homeId", idTeamH);
        map.put("awayId", idTeamA);
        map.put("groupId", groupsetId);
        map.put("language", language);
        return getSqlSession().selectList("Atleta.getAtletiByGame", map);
    }

    @Override
    public List getAtletiByTeam(String seasonIds, Long idTeam, Long idGroupset, Boolean personalSource, Boolean checkPlaytime, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("seasonIds", seasonIds);
        map.put("teamId", idTeam);
        map.put("groupId", idGroupset);
        map.put("source", personalSource);
        map.put("checkPlaytime", BooleanUtils.isTrue(checkPlaytime));
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        if ((Boolean) map.get("checkPlaytime")) {
            return getSqlSession().selectList("Atleta.getAtletiByTeam", map);
        } else {
            return getSqlSession().selectList("Atleta.getAtletiAllByTeam", map);
        }
    }

    @Override
    public List getAtletiByTeamAndCompetition(String seasonIds, Long idTeam, Long idCompetition, Long idGroupset, Long groupId, Boolean personalSource, Boolean checkPlaytime, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("seasonIds", seasonIds);
        map.put("teamId", idTeam);
        map.put("competitionId", idCompetition);
        map.put("groupsetId", idGroupset);
        map.put("groupId", groupId);
        map.put("source", personalSource);
        map.put("checkPlaytime", BooleanUtils.isNotFalse(checkPlaytime));
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        if ((Boolean) map.get("checkPlaytime")) {
            return getSqlSession().selectList("Atleta.getAtletiByTeamAndCompetition", map);
        } else {
            return getSqlSession().selectList("Atleta.getAtletiAllByTeamAndCompetition", map);
        }
    }

    @Override
    public List getAtletiSelezionati(Long idFixture, ArrayList<Long> players, Long idTeamH, Long idTeamA, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("language", language);
        String where = "where fixture.id =" + idFixture + " AND team.id IN (" + idTeamH + "," + idTeamA + ")";
        if (!players.isEmpty()) {
            where += " AND player.id IN (";
            for (Long playerId : players) {
                where += playerId + ",";
            }
            where = where.substring(0, where.length() - 1) + ") GROUP BY player.id";
        }

        map.put("where", where);
        return getSqlSession().selectList("Atleta.getAtletiSelezionati", map);
    }

    @Override
    public Atleta getAtleta(Long playerId, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        map.put("language", language);
        return (Atleta) getSqlSession().selectOne("Atleta.getAtleta", map);
    }

    @Override
    public List<Atleta> getAtletiByIds(String ids, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("ids", ids);
        map.put("language", language);
        return getSqlSession().selectList("Atleta.getAtletiByIds", map);
    }

    @Override
    public List getAtletiByPattern(String pattern, Long sportId, String seasonIds, Long groupsetId, Boolean personalSource, List<UserCompetitionException> competitionExceptions) throws SQLException {
        /*
        TODO: GESTIRE personalSource, SU TEAM_PLAYER_COMPETITION NON SCRIVO QUELLE PERSONALI, QUINDI BISOGNERA LANCIARE QUERY DIVERSE
         */

        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(season.id = CASE WHEN team_player_competition.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE season.id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        String where = " where player.sport_id = " + sportId + " and season.visible = 1";
        String conditions = "(player.known_name LIKE \"%" + pattern + "%\" OR CONCAT(player.last_name, ' ', player.first_name) LIKE \"%" + pattern + "%\" OR CONCAT(player.first_name, ' ', player.last_name) LIKE \"%" + pattern + "%\" ";
        if (StringUtils.contains(pattern, " ")) {
            List<String> extraConditions = new ArrayList<>();
            for (String word : StringUtils.split(pattern, " ")) {
                extraConditions.add("(player.known_name LIKE \"%" + word + "%\" OR CONCAT(player.last_name, ' ', player.first_name) LIKE \"%" + word + "%\" OR CONCAT(player.first_name, ' ', player.last_name) LIKE \"%" + word + "%\")");
            }
            conditions += "OR(" + StringUtils.join(extraConditions, " AND ");
            conditions += "))";
        } else {
            conditions += ")";
        }
        where += " AND " + conditions;

        if (personalSource) {
            where += " AND player.groupset_id = " + groupsetId;
        } else {
            where += " AND player.groupset_id IN (-1," + groupsetId + ")";
        }
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            where += map.get("competitionException");
        }
        where += " AND player.visible = 1";
        where += " GROUP BY player.id, season.id";
        where += " ORDER BY case when " + conditions + " then 0 else 1 end, player.known_name, season_name DESC";
        map.put("where", where);
        return getSqlSession().selectList("Atleta.getAtletiByPattern", map);
    }

    @Override
    public List getAtletiPersonalByPattern(String pattern, Long userId, Long groupsetId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("pattern", pattern);
        map.put("userId", userId);
        map.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Atleta.getAtletiPersonalByPattern", map);
    }

    @Override
    public List getAtletiByRole(Long idFixture, Long roleId, Long teamId, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("language", language);
        String where = "where fixture.id =" + idFixture;
        if (roleId != null) {
            where += " AND team_player.position_id=" + roleId;
        }
        where += " AND team.id =" + teamId + " GROUP BY player.id";
        map.put("where", where);
        return getSqlSession().selectList("Atleta.getAtletiByRole", map);
    }

    @Override
    public Map getFasce(Long idConfiguration) throws SQLException {
        Map<Integer, ArrayList<Fascia>> mapFasce = new HashMap<>();
        List listFasce = getSqlSession().selectList("Fascia.getFasceByConfigId", idConfiguration);
        ArrayList<Fascia> listFascia;
        for (Object obj : listFasce) {
            Fascia fascia = (Fascia) obj;
            listFascia = mapFasce.get(fascia.getmRangeId());
            if (listFascia == null) {
                listFascia = new ArrayList<>();
                mapFasce.put(fascia.getmRangeId(), listFascia);
            }
            listFascia.add(fascia);
        }
        return mapFasce;
    }

    @Override
    public Map getIntervalli() throws SQLException {
        Map<Integer, ArrayList<Intervallo>> mapIntervalli = new HashMap<>();
        List listIntervalli = getSqlSession().selectList("Intervallo.getIntervalli");
        ArrayList<Intervallo> listIntervallo;
        for (Object obj : listIntervalli) {
            Intervallo intervallo = (Intervallo) obj;
            listIntervallo = mapIntervalli.get(intervallo.getTypeIntervallo());
            if (listIntervallo == null) {
                listIntervallo = new ArrayList<>();
                mapIntervalli.put(intervallo.getTypeIntervallo(), listIntervallo);
            }
            listIntervallo.add(intervallo);
        }
        return mapIntervalli;
    }

    @Override
    public LinkedHashMap<String, Azione> getActionsByIdEvent(Long idFixture, ArrayList<String> idEvents, Long sportId, List<Competition> allowedCompetitions, Long userId, Long groupsetId, String language) {
        LinkedHashMap<String, Azione> mapActions = new LinkedHashMap<>();
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("language", language);
        String where = " where fixture.id = " + idFixture;
        if (!idEvents.isEmpty()) {
            where += " AND xml_idevent IN(";
            for (String idEvent : idEvents) {
                where += "'" + idEvent + "',";
            }
            where = where.substring(0, where.length() - 1) + ")";
        }
        where += " AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ")";
        where += " AND event.groupset_id IN(-1," + groupsetId + ") ";
        map.put("where", where);
        GlobalHelper.adjustQueryParams(map);
        List listEvents;
        if (sportId == 0) {
            listEvents = getSqlSession().selectList("Azione.getActionsByFilter", map);
        } else {
            listEvents = getSqlSession().selectList("Azione.getActionsByFilterBasket", map);
        }
        listEvents = loadButtonTags(listEvents);
        for (Object obj : listEvents) {
            Azione action = (Azione) obj;
            mapActions.put(action.getmIdEvent(), action);
        }
        addAdditionalInfoAction(listEvents, new HashMap<Long, Game>(), userId, groupsetId, language);

        return mapActions;
    }

    @Override
    public List<Azione> getActionsByEventIds(String eventIds, Long sportId, List<Competition> allowedCompetitions, String language) {
        String where = " where event.id IN(" + eventIds + ")";

        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }
        where += " AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ")";

        map.put("where", where);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        List<Azione> result;
        if (sportId == 0) {
            result = getSqlSession().selectList("Azione.getActionsByFilter", map);
        } else {
            result = getSqlSession().selectList("Azione.getActionsByFilterBasket", map);
        }

        // devo caricare i player altrimenti non vengono trascritti
        Map<Integer, Atleta> cache = new HashMap<>();
        for (Azione azione : result) {
            if (azione.getPlayerId() != 0) {
                if (!cache.containsKey(azione.getPlayerId())) {
                    try {
                        // perchè playerId è int ?!?!?!?!
                        Atleta atleta = getAtletaById("-1", (long) azione.getPlayerId(), -1L, -1L, false, language);
                        if (atleta != null) {
                            cache.put(azione.getPlayerId(), atleta);
                        }
                    } catch (SQLException ex) {
                        Logger.getLogger(AppDaoImpl.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }

                if (cache.get(azione.getPlayerId()) != null) {
                    azione.setmPlayer(cache.get(azione.getPlayerId()).getKnown_name());
                }
            }
        }
        result = removeDuplicatedActions(result);

        // devo caricare Game perchè mi servono dati da game
        // si potrebbe mettere parametro per stabilire se serve o meno caricare game...
        Map<Long, Game> games = new HashMap<>();
        for (Azione azione : result) {
            Long idFixture = azione.getIdFixture();
            if (games.get(idFixture) == null) { // non carico tutte le volte la stessa partita
                try {
                    games.put(idFixture, getGameByFixtureId(idFixture, language));
                } catch (SQLException ex) {
                    Logger.getLogger(AppDaoImpl.class.getName()).log(Level.SEVERE, null, ex);
                }
            }

            Game game = games.get(idFixture);
            if (game != null) { // extra check, dovrebbe essere null solo se il game non esiste più
                if (game.getMinVideoQuality() == 0) {
                    if (BooleanUtils.isNotFalse(game.getHd())) { // se ho HD metto comunque la massima risoluzione
                        azione.setVideoPathS3(game.getVideoPathHDS3());
                    } else {
                        azione.setVideoPathS3(game.getVideoPathS3());
                    }
                } else if (game.getMinVideoQuality() == 1) { // se ho minimo HD non posso salvare path video SD
                    azione.setVideoPathS3(game.getVideoPathHDS3());
                }
                azione.setVideoId(game.getVideoId());
                azione.setmHomeTeam(game.getHomeTeam());
                azione.setmAwayTeam(game.getAwayTeam());
                azione.setVideoName(game.getVideoName() != null ? game.getVideoName() : "");
                azione.setProvider(game.getProviderId());
            }
        }

        result = loadButtonTags(result);
        return result;
    }

    public List<Azione> removeDuplicatedActions(List<Azione> actionList) {
        List<Azione> result = new ArrayList<>();
        List<Long> alreadyAddedIds = new ArrayList<>();

        for (Azione azione : actionList) {
            if (!alreadyAddedIds.contains(azione.getId())) {
                alreadyAddedIds.add(azione.getId());
                result.add(azione);
            }
        }

        return result;
    }

    private FilterGameWrapper addAdditionalInfoAction(List listEvents, Map<Long, Game> games, Long userId, Long groupsetId, String language) {
        return addAdditionalInfoAction(listEvents, games, userId, groupsetId, null, language);
    }

    private FilterGameWrapper addAdditionalInfoAction(List listEvents, Map<Long, Game> games, Long userId, Long groupsetId, List<Competition> allowedCompetitions, String language) {
        /*
            A partire dai Game mi leggo i giocatori che hanno giocato in ciascun Game
         */
//        for (Game game : games.values()) {
//            loadPlayersForGame(game, groupsetId);
//        }
        Map<Long, FixtureDetails> fixtureDetailsMap = new HashMap<>();
        if (games != null && !games.isEmpty()) {
            loadPlayersForGames(new ArrayList<>(games.values()), groupsetId);

            List<Long> fixtureIds = new ArrayList<>();
            for (Game fixture : games.values()) {
                if (!fixtureIds.contains(fixture.getIdFixture())) {
                    fixtureIds.add(fixture.getIdFixture());
                }
            }

            if (!fixtureIds.isEmpty()) {
                try {
                    List<FixtureDetails> fixtureDetails = getFixtureDetails(fixtureIds);

                    if (fixtureDetails != null && !fixtureDetails.isEmpty()) {
                        for (FixtureDetails fixtureDetail : fixtureDetails) {
                            fixtureDetailsMap.put(fixtureDetail.getFixtureId(), fixtureDetail);
                            for (Game fixture : games.values()) {
                                if (fixtureDetail.getFixtureId() != null && fixture.getIdFixture() != null
                                        && Long.compare(fixture.getIdFixture(), fixtureDetail.getFixtureId()) == 0) {
                                    fixture.setFixtureDetails(fixtureDetail);
                                }
                            }
                        }
                    }
                } catch (SQLException ex) {
                    GlobalHelper.reportError(ex);
                }
            }
        }

        // carico settings per aggiungere tempo ad inizio o fine clip se necessario
        Settings userSettings = null;
        if (userId != null) {
            try {
                userSettings = getSettingsByUserId(userId);
            } catch (SQLException ex) {
                // suppressed
            }
        }

        /*
            Dalla lista delle azioni che mi vengono restituite in cui ci possono essere più righe per ciascuna azioni dovute alla presenza di tag diversi per la stessa azione,
            mi creo un'azione unica con tutti i tag a cui aggiungo la posizione corretta e i giocatori
         */
        Map<Long, Competition> allowedCompetitionsMap = null;
        if (allowedCompetitions != null && !allowedCompetitions.isEmpty()) {
            allowedCompetitionsMap = new HashMap<>();
            for (Competition competition : allowedCompetitions) {
                allowedCompetitionsMap.put(competition.getId(), competition);
            }
        }

        Map<Long, ArrayList<Azione>> mapResult = new LinkedHashMap<>();
        for (Object obj : listEvents) {
            Azione azione = (Azione) obj;

            // evito di impostare campo se non necessario
            // in pagina video controllo se non è undefined quindi non serve metterlo a false
            if (allowedCompetitionsMap != null && azione.getCompetitionId() != null) {
                if (allowedCompetitionsMap.containsKey(azione.getCompetitionId())) {
                    azione.setIsVisible(true);
                }
            }

            Long idFixture = azione.getIdFixture();
            if (games.get(idFixture) == null) { // se arrivo da playlist e sono multipartita devo caricare il game
                try {
                    games.put(idFixture, getGameByFixtureId(idFixture, language));
                    loadPlayersForGame(games.get(idFixture), groupsetId);
                } catch (SQLException ex) {
                    Logger.getLogger(AppDaoImpl.class.getName()).log(Level.SEVERE, null, ex);
                }
            }

            if (games.get(idFixture) != null) {
                Game game = games.get(idFixture);

                // Gestione Settings - Allungo inizio o fine clip
                if (userSettings != null) {
                    if (userSettings.getEventAdditionalStart() > 0) {
                        boolean canContinue = false;
                        double startSeconds = azione.getPeriod_start_second();
                        startSeconds = startSeconds - userSettings.getEventAdditionalStartSec();
                        if (startSeconds < 0) { // passo al minuto prima
                            if (azione.getPeriod_start_minute() > 0) { // se sono ad inizio video non posso tornare indietro
                                azione.setPeriod_start_minute(azione.getPeriod_start_minute() - 1);
                                azione.setPeriod_start_second((int) Math.floor(60 + startSeconds)); // uso la + perchè 60 + -2 = 58
                                canContinue = true;
                            }
                        } else if (startSeconds > 59) { // passo al minuto dopo
                            azione.setPeriod_start_minute(azione.getPeriod_start_minute() + 1);
                            azione.setPeriod_start_second((int) Math.floor(startSeconds - 60));
                            canContinue = true;
                        } else {
                            azione.setPeriod_start_second((int) Math.floor(startSeconds));
                            canContinue = true;
                        }

                        if (canContinue) {
                            azione.setmStart(azione.getmStart() - userSettings.getEventAdditionalStart());
                        }
                    }
                    if (userSettings.getEventAdditionalEnd() > 0) {
                        double endSeconds = azione.getPeriod_end_second();
                        endSeconds = endSeconds + userSettings.getEventAdditionalEndSec();
                        if (endSeconds > 59) { // passo al minuto dopo
                            azione.setPeriod_end_minute(azione.getPeriod_end_minute() + 1);
                            azione.setPeriod_end_second((int) Math.floor(endSeconds - 60));
                        } else if (endSeconds < 0) { // passo al minuto prima
                            azione.setPeriod_end_minute(azione.getPeriod_end_minute() - 1);
                            azione.setPeriod_end_second((int) Math.floor(60 + endSeconds)); // uso la + perchè 60 + -2 = 58
                        } else {
                            azione.setPeriod_end_second((int) Math.floor(endSeconds));
                        }
                        azione.setmEnd(azione.getmEnd() + userSettings.getEventAdditionalEnd());
                    }
                }

                int idTeam = azione.getIdTeam();
                Map<String, Azione> azioni = null;
                int indiceSquadra = 0;

                if (idTeam == game.getHomeTeamId()) {
                    azioni = game.getAzioniHome();
                    indiceSquadra = 1;
                } else if (idTeam == game.getAwayTeamId()) {
                    azioni = game.getAzioniAway();
                    indiceSquadra = 2;
                }

                if (azioni == null) {
                    return new FilterGameWrapper(games, mapResult);
                }

                Azione az;
                String xml_idevent = azione.getIdEvent();
                boolean actionAdded = false;
                // aggiungo alla lista di azioni in game l'azione corrente
                if (azioni.containsKey(xml_idevent)) {
                    az = azioni.get(xml_idevent);
                    actionAdded = true;
                } else {
                    az = azione;
                    az.setIndexTeam(indiceSquadra);

                    //### POSIZIONI ###
                    String pos = az.getPosStartString();
                    if (pos != null) {
                        String[] parti = pos.split(";");
                        az.setActionPos(new Punto(Float.parseFloat(parti[0]), Float.parseFloat(parti[1])));
                        pos = az.getPosEndString();
                        if (pos != null) {
                            parti = pos.split(";");
                            az.setActionPosEnd(new Punto(Float.parseFloat(parti[0]), Float.parseFloat(parti[1])));
                            // DAFARE per ora non serve
                            pos = az.getPos3DString();
                            if (pos != null) {
                                parti = pos.split(";");
                                az.setShot3D(new PuntoTiro(Float.parseFloat(parti[0]), Float.parseFloat(parti[1]), Float.parseFloat(parti[2])));
                            }
                        }
                    }
                    azioni.put(az.getIdEvent(), az);

                    //### SQUADRA A SINISTRA ###
                    FixtureDetails details = fixtureDetailsMap.get(az.getIdFixture());
                    if (details != null) {
                        // check dati
                        if (details.getTeamSx() == null) {
                            details.setTeamSx(game.getHomeTeamId().longValue());
                        }

                        Map<String, String> teamOnHalf = new HashMap<>();
                        if (Long.compare(details.getTeamSx(), game.getHomeTeamId()) == 0) {
                            teamOnHalf.put("1", game.getHomeTeam());
                            teamOnHalf.put("2", game.getAwayTeam());
                        } else {
                            teamOnHalf.put("1", game.getAwayTeam());
                            teamOnHalf.put("2", game.getHomeTeam());
                        }
                        if (details.getTeamSx2() != null) {
                            if (Long.compare(details.getTeamSx2(), game.getHomeTeamId()) == 0) {
                                teamOnHalf.put("3", game.getHomeTeam());
                                teamOnHalf.put("4", game.getAwayTeam());
                            } else {
                                teamOnHalf.put("3", game.getAwayTeam());
                                teamOnHalf.put("4", game.getHomeTeam());
                            }
                        }

                        try {
                            Punto[] normalizzati = GlobalHelper.normalizzaPosizioneAzione(teamOnHalf, az, game, true, false);
                            az.setmActionPosNormalized(normalizzati[0]);
                            az.setmActionPosEndNormalized(normalizzati[1]);
                            az.setActionDetails();
                            if (az.getmShot3D() != null && !az.getmShot3D().isDefault && normalizzati.length > 2) {
                                PuntoTiro shot3dNormalized = new PuntoTiro(normalizzati[2].x, normalizzati[2].y, az.getmShot3D().getZ());
                                az.setmShot3DNormalized(shot3dNormalized);
                            }
                        } catch (CloneNotSupportedException ex) {
                            GlobalHelper.reportError(ex);
                        }

                        // colore maglia
                        if (Long.compare(az.getIdTeam(), game.getHomeTeamId()) == 0) {
                            if (StringUtils.isNotBlank(details.getHomeTeamColor())) {
                                az.setmTeamColor("#" + details.getHomeTeamColor());
                                az.setmTeamTextColor("#" + Integer.toHexString(GlobalHelper.getColoreInContrasto(Color.decode("#" + details.getHomeTeamColor())).getRGB()).substring(2));
                            }
                        } else if (Long.compare(az.getIdTeam(), game.getAwayTeamId()) == 0) {
                            if (StringUtils.isNotBlank(details.getAwayTeamColor())) {
                                az.setmTeamColor("#" + details.getAwayTeamColor());
                                az.setmTeamTextColor("#" + Integer.toHexString(GlobalHelper.getColoreInContrasto(Color.decode("#" + details.getAwayTeamColor())).getRGB()).substring(2));
                            }
                        }
                    } else {
                        // todo, capire cosa fare
                    }
                    //rinvio necessario per determinare team a sx
//                    if (game.getTeamSx() == 0 && az.getType().equals("RIN")) {
//                        if (az.getActionPos().x < 50) {
//                            if (az.getHalf() == 1) {
//                                game.setTeamSx(az.getIndexTeam());
//                            } else {
//                                game.setTeamSx((az.getIndexTeam() + 1) % 2);
//                            }
//                        } else {
//                            if (az.getHalf() == 1) {
//                                game.setTeamSx((az.getIndexTeam() + 1) % 2);
//                            } else {
//                                game.setTeamSx(az.getIndexTeam());
//                            }
//                        }
//                    }
                }

                if (mapResult.get(idFixture) == null) {
                    ArrayList<Azione> listActions = new ArrayList<>();
                    listActions.add(az);
                    mapResult.put(idFixture, listActions);
                } else if (!actionAdded) {
                    mapResult.get(idFixture).add(az);
                }
                if ((az == null)) {
                    return new FilterGameWrapper(games, mapResult);
                }

                //### TAGS ###
                //leggo tag.code per il controllo assegnaFalliRigoreSubiti
//				String tagType = rdr.getString("tagType");
//				if (tagType != null) {
//					Tags t = new Tags(rdr.getInt("idTagType"), rdr.getString("tagType"));
                Tags t = azione.getmTag();
                if (t != null && !(az.getmTags().contains(t))) {
                    //controllo che il tag non sia già stato inserito
                    az.getmTags().add(t);
                }
//				}

                //### PLAYERS ###
                // inserisce players solo alle azioni della squadra selezionata
//                    if (withPlayers && (az.getidTeam() == idTeamSelected)) {
                // servono gli eventiPlayers di tutte le squadre per le medie
                //se c'è un player
                int idPlayer = azione.getPlayerId();
                if (!(az.getPlayer().containsKey(idPlayer)) || az.getPlayer().get(idPlayer) == null) {
                    //controllo che il player non sia già stato inserito
                    Map<Integer, Atleta> formazione;
                    if (az.getIndexTeam() == 1) {
                        formazione = game.getFormazione1();
                    } else {
                        formazione = game.getFormazione2();
                    }
                    if (formazione.containsKey(idPlayer)) {
                        //controllo che il player sia nella formazione
                        az.getPlayer().put(idPlayer, formazione.get(idPlayer));
                        az.setmPlayer((az.getmPlayer().isEmpty() ? "" : az.getmPlayer() + ", ") + formazione.get(idPlayer).getKnown_name());
                    }

                    // cerco di sistemare anche gli altri presenti dentro se a null
                    if (!az.getPlayer().isEmpty()) {
                        for (Integer playerId : az.getPlayer().keySet()) {
                            if (az.getPlayer().get(playerId) == null) {
                                if (formazione.containsKey(playerId)) {
                                    az.getPlayer().put(playerId, formazione.get(playerId));
                                }
                            }
                        }
                    }
                }
                // playerTo

                //se c'è un playerTo
                int idPlayerTo = azione.getPlayerToId();
                if (az.getPlayerTo() == null) {
                    //controllo che il player non sia già stato inserito
                    Map<Integer, Atleta> formazione;
                    if (az.getIndexTeam() == 1) {
                        formazione = game.getFormazione1();
                    } else {
                        formazione = game.getFormazione2();
                    }
                    if (formazione.containsKey(idPlayerTo)) {
                        //controllo che il player sia nella formazione
                        az.setPlayerTo(formazione.get(idPlayerTo));
                        az.setmPlayerTo(az.getmPlayerTo() + "," + ((formazione.get(idPlayer) != null) ? formazione.get(idPlayer).getKnown_name() : ""));
                    }
                }

                int secStart = az.getPeriod_start_minute() * 60 + az.getPeriod_start_second();
                int secEnd = az.getPeriod_end_minute() * 60 + az.getPeriod_end_second();
                int minutiAzione = (secEnd - secStart) / 60;
                int secondiAzione = (secEnd - secStart) % 60;
                String durataAzione;
                if (secondiAzione < 10) {
                    durataAzione = minutiAzione + ":0" + secondiAzione;
                } else {
                    durataAzione = minutiAzione + ":" + secondiAzione;
                }
                if (minutiAzione < 10) {
                    durataAzione = "0" + durataAzione;
                }
                az.setDurataAzione(durataAzione);
                az.setVideoId(game.getVideoId());
                az.setmHomeTeam(game.getHomeTeam());
                az.setmAwayTeam(game.getAwayTeam());
                az.setVideoName(game.getVideoName() != null ? game.getVideoName() : "");
                // OLD - IMPOSTAVA SD ANCHE PER I VIDEO CON MIN VIDEO QUALITY A 1
//                if (game.getVideoName() != null && (az.getVideoPathS3() == null || az.getVideoPathS3().isEmpty())) {
//                    az.setVideoPathS3(game.getVideoPathS3());
//                }
                if (game.getVideoName() != null) {
                    if (game.getMinVideoQuality() == 0) {
                        if (BooleanUtils.isNotFalse(game.getHd())) { // se ho HD metto comunque la massima risoluzione
                            azione.setVideoPathS3(game.getVideoPathHDS3());
                        } else {
                            azione.setVideoPathS3(game.getVideoPathS3());
                        }
                    } else if (game.getMinVideoQuality() == 1) { // se ho minimo HD non posso salvare path video SD
                        azione.setVideoPathS3(game.getVideoPathHDS3());
                    }
                }
                // apertura modifica clip da playlist. Caso della modifica del tattico
                if (BooleanUtils.isTrue(az.getIsTactical())) {
                    az.setVideoPathS3(game.getVideoPathTACTS3());
                }
                // se non è visibile allora tolgo il path del video così da mostrarlo come "errore"
                if (allowedCompetitionsMap != null && az.getIsVisible() == null) {
                    az.setVideoPathS3("");
                    az.setVideoName("");
                    game.setVideoName("");
                }

                az.setProvider(game.getProviderId());
                az.setFromDB(true);
            }
        }

        // ordino ciascuna lista in ordine di tempo
        for (ArrayList<Azione> list : mapResult.values()) {
            Collections.sort(list, new AzioneComparatorHelper());
        }

        return new FilterGameWrapper(games, mapResult);
    }

    public void loadPlayersForGame(Game game, Long groupsetId) {
        TreeMap map = new TreeMap();
        map.put("fixtureId", game.getIdFixture());
        map.put("homeId", game.getHomeTeamId());
        map.put("awayId", game.getAwayTeamId());
        map.put("groupId", groupsetId);

        List listPlayers = getSqlSession().selectList("Atleta.getAtletiByGame", map);
        for (Object obj : listPlayers) {
            Atleta atleta = (Atleta) obj;
            // aggiungo al game le formazioni
            if (game.getHomeTeamId().equals(atleta.getTeam())) {
                if (atleta.getId() != null && !game.getFormazione1().containsKey(atleta.getId())) {
                    game.getFormazione1().put(atleta.getId(), atleta);
                }
            }
            if (game.getAwayTeamId().equals(atleta.getTeam())) {
                if (atleta.getId() != null && !game.getFormazione2().containsKey(atleta.getId())) {
                    game.getFormazione2().put(atleta.getId(), atleta);
                }
            }
        }

        if (!listPlayers.isEmpty()) {
            Atleta atl = (Atleta) listPlayers.get(0);
            game.setDrawCampoTabellino(atl.getmPosition().getX() != -1);
        }
    }

    public void loadPlayersForGames(List<Game> games, Long groupsetId) {
        String where = "(";
        for (Game game : games) {
            // fixture.id =  #{fixtureId} AND team.id IN (#{homeId},#{awayId})
            if (where.length() > 1) {
                where += " OR ";
            }

            where += "(fixture.id = " + game.getIdFixture() + " AND team.id IN (" + game.getHomeTeamId() + "," + game.getAwayTeamId() + "))";
        }
        where += ")";

        TreeMap map = new TreeMap();
        map.put("where", where);
        map.put("groupId", groupsetId);

        List listPlayers = getSqlSession().selectList("Atleta.getAtletiByGames", map);
        for (Game game : games) {
            for (Object obj : listPlayers) {
                Atleta atleta = (Atleta) obj;
                // aggiungo al game le formazioni
                if (game.getHomeTeamId().equals(atleta.getTeam())) {
                    if (atleta.getId() != null && !game.getFormazione1().containsKey(atleta.getId())) {
                        game.getFormazione1().put(atleta.getId(), atleta);
                    }
                }
                if (game.getAwayTeamId().equals(atleta.getTeam())) {
                    if (atleta.getId() != null && !game.getFormazione2().containsKey(atleta.getId())) {
                        game.getFormazione2().put(atleta.getId(), atleta);
                    }
                }
            }

            if (!listPlayers.isEmpty()) {
                Atleta atl = (Atleta) listPlayers.get(0);
                game.setDrawCampoTabellino(atl.getmPosition().getX() != -1);
            }
        }
    }

    @Override
    public List getActionsAvailable(boolean conTag, boolean soloSenzaTag, Long teamId, Long playerId, String seasonIds, Long competitionId, String fixtureId, Long groupsetId, Boolean personalSource, String gameIds, String event, List<Competition> allowedCompetitions, Long groupId, User curUser, String language) {
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        String where = "";
        // deve stare qua perchè aggiunge una join
        if (BooleanUtils.isTrue(curUser.getGuest())) {
            where += " LEFT JOIN event_owner ON event_owner.event_id = event.id";
        }

        where += " WHERE fixture.season_id IN(" + seasonIds + ")";

        if (teamId != null && teamId > 0) {
            where += " AND team_id = " + teamId;
        }
        if (playerId != null && playerId > 0) {
//            where += " AND (event_player.player_id = " + playerId + " OR event_player.playerto_id = " + playerId + ")";
            where += " AND (event_player.player_id = " + playerId + ")";
        }

        if (competitionId != null && competitionId > 0) {
            where += " AND competition_id = " + competitionId;
        }
        if (fixtureId != null && !fixtureId.isEmpty()) {
            where += " AND fixture.id IN (" + fixtureId + ")";
        }
        if (groupId != null) {
            where += " AND fixture.group_id = " + groupId;
        }

        if (personalSource) {
            where += " AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ") AND fixture.provider_id = 3";
        } else { // aggiunto filtro su event.groupset_id altrimenti carica le righe degli eventi creati da utenti che non siamo noi e sballa i contatori nella tab avanzate
            where += " AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ") AND fixture.provider_id = 2";
        }

        if (gameIds != null && !gameIds.isEmpty()) {
            //                List<Long> fixtureIds = getFixturesByGameIds(gameIds);
            List<String> fixtureIds = new ArrayList<>(Arrays.asList(StringUtils.split(gameIds, ",")));
            where += " AND fixture.id IN (" + StringUtils.join(fixtureIds, ",") + ")";
        }

        where += " AND event.groupset_id IN(-1," + groupsetId + ") ";

        if (event != null && !event.isEmpty()) {
            // NON SERVE METTERE ID AL POSTO DEI CODICI PERCHE' NON VIENE MAI USATA CON L'EVENT (23/01/2025)

            Map<String, Integer> panelEventTypeMap = new HashMap<>();
            Map<String, Integer> panelTagTypeMap = new HashMap<>();
            String config = null;
            String[] splitted = event.split("\\|");
            where += " AND ((";
            for (int i = 0; i < splitted.length; i++) {
                List<Integer> eventTypeList = new ArrayList<>();
                List<Integer> subEventList = new ArrayList<>();

                String subEvent = splitted[i];
                String[] splitconfig = subEvent.split("-_-");
                config = splitconfig[0];
                if (StringUtils.isNotBlank(config)) {
                    if (panelEventTypeMap.isEmpty()) {
                        try {
                            List<Tags> panelEventTypes = getGroupsetEventType(config, StringUtils.equalsIgnoreCase(config, "SICS2015") ? -1l : groupsetId);
                            List<Tags> panelTagTypes = getGroupsetTagType(config, StringUtils.equalsIgnoreCase(config, "SICS2015") ? -1l : groupsetId);
                            for (Tags eventType : panelEventTypes) {
                                panelEventTypeMap.put(eventType.getCode(), eventType.getId());
                            }
                            for (Tags tagType : panelTagTypes) {
                                panelTagTypeMap.put(tagType.getCode(), tagType.getId());
                            }
                        } catch (SQLException ex) {
                            GlobalHelper.reportError(ex);
                        }
                    }
                }
                // evt = TIF#TIF-1@TIF-2
                String[] evt = splitconfig[1].split("#");
                if (!splitconfig[1].contains("#")) {
                    if (splitconfig[1].contains("@")) {
                        evt = splitconfig[1].split("@");
                    }
                }
                if (evt.length == 2) {
                    for (String tagType : evt[1].split("@")) {
                        subEventList.add(panelTagTypeMap.get(tagType));
                    }
                }

                String[] splitFond = evt[0].split("-");
                for (String s : splitFond) {
                    if (!s.isEmpty()) {
                        eventTypeList.add(panelEventTypeMap.get(s));
                    }
                }

                if (i > 0) {
                    where += " OR (";
                }
                where += "event_type.id IN (" + StringUtils.join(eventTypeList, ",") + ")";
                boolean genTagFound = false;
//                String genTag = evt[0] + "-GEN";
                if (!subEventList.isEmpty()) {
                    where += " AND ";
                    if (subEventList.contains(null)) {
                        genTagFound = true;

                        where += " ((tag_type.code IS NULL) ";
                        subEventList.remove(null);
                        if (!subEventList.isEmpty()) {
                            where += " OR ";
                        } else {
                            where += " ) ";
                        }
                    }

                    if (!subEventList.isEmpty()) {
                        where += " (event_tag.tag_type_id IN (" + StringUtils.join(subEventList, ",") + ")))";
                    }
                    if (genTagFound) {
                        where += ")";
                    }
                } else {
                    where += ")";
                }
            }
            where += ")";
        }

        if (BooleanUtils.isTrue(curUser.getGuest())) {
            where += " AND IFNULL(event_owner.user_id, " + curUser.getId() + ") = " + curUser.getId();
        }
        /*
            Faccio una query con i filtri impostati per determinare gli idEvent delle azioni da caricare
         */
        List<Azione> listEvents;
        TreeMap map = new TreeMap();
        map.put("language", language);
        map.put("where", where);
        map.put("database", GlobalHelper.getDatabase(seasonIds));
        map.put("isMultiFixture", (fixtureId == null || fixtureId.contains(",") || fixtureId.isEmpty()));
        GlobalHelper.adjustQueryParams(map);
        if (soloSenzaTag) {
            listEvents = getSqlSession().selectList("Azione.getActionsLimitedEventByGameIdsOnlyNoTag", map);

            for (Azione action : listEvents) {
                action.setmTagCode(action.getType() + "-GEN");
            }
        } else {
            if (conTag) {
                //            if (gameIds == null || gameIds.isEmpty()) {
                //                listEvents = getSqlSession().selectList("Azione.getActionsLimitedTag", map);
                //            } else {
                //                listEvents = getSqlSession().selectList("Azione.getActionsLimitedTagByGameIds", map);
                //            }
                if (gameIds == null || gameIds.isEmpty()) {
                    listEvents = getSqlSession().selectList("Azione.getActionsLimitedEventByTag", map);
                } else {
                    listEvents = getSqlSession().selectList("Azione.getActionsLimitedEventByGameIdsAndTag", map);
                }
            } else {
                if (gameIds == null || gameIds.isEmpty()) {
                    listEvents = getSqlSession().selectList("Azione.getActionsLimitedEvent", map);
                } else {
                    listEvents = getSqlSession().selectList("Azione.getActionsLimitedEventByGameIds", map);
                }
            }
        }
        // non lancio la loadButtonTags perchè raggruppa per eventId e la query ritorna lo stesso eventId per tag diversi
        // non sembra aver alcun impatto sulle altre pagine, anche perchè viene usato solo per abilitare / disabilitare i tasti
        // di filtro delle azioni, oltre a contare quante azioni ci sono
        //listEvents = loadButtonTags(listEvents);

        return listEvents;
    }

    @Override
    public List<Azione> getActionsAvailableForCompetition(boolean conTag, String seasonIds, Long competitionId, Long teamId, Long playerId, Long groupsetId, String event, String from, String to) {
        String where = " WHERE fixture.season_id IN(" + seasonIds + ")";

        if (competitionId != null && competitionId > 0) {
            where += " AND competition_id = " + competitionId;
        }
        if (teamId != null && teamId > 0) {
            where += " AND team_id = " + teamId;
        }
        if (playerId != null && playerId > 0) {
            where += " AND event_player.player_id = " + playerId;
        }
        where += " AND fixture.provider_id = 2";

        if (StringUtils.isNotBlank(from) && StringUtils.isNotBlank(to)) {
            where += " AND fixture.game_date BETWEEN '" + from + "' AND '" + to + "'";
        }
        where += " AND event.groupset_id IN(-1," + groupsetId + ") ";

        if (event != null && !event.isEmpty()) {
            // SAREBBE DA FARE MA MANCA IL NOME DEL PANNELLO -> DOVREBBE ESSERE LA QUERY CHE POPOLA LA TAB SEARCH DENTRO ALLA PAGINA DELLA COMPETIZIONE

            String[] splitted = event.split("\\|");
            where += " AND ((";
            for (int i = 0; i < splitted.length; i++) {
                String fondFilter = "";
                List<String> subEventList = new ArrayList<>();

                String subEvent = splitted[i];
                String[] splitconfig = subEvent.split("-_-");
                // evt = TIF#TIF-1@TIF-2
                String[] evt = splitconfig[1].split("#");
                if (!splitconfig[1].contains("#")) {
                    if (splitconfig[1].contains("@")) {
                        evt = splitconfig[1].split("@");
                    }
                }
                if (evt.length == 2) {
                    subEventList.addAll(Arrays.asList(evt[1].split("@")));
                }

                String[] splitFond = evt[0].split("-");
                for (String s : splitFond) {
                    if (!s.isEmpty()) {
                        fondFilter += (fondFilter.isEmpty() ? "'" : ",'") + s + "'";
                    }
                }

                if (i > 0) {
                    where += " OR (";
                }
                where += "event_type.code IN (" + fondFilter + ")";
                boolean genTagFound = false;
                String genTag = evt[0] + "-GEN";
                if (!subEventList.isEmpty()) {
                    where += " AND ";
                    if (subEventList.contains(genTag)) {
                        genTagFound = true;

                        where += " ((tag_type.code IS NULL) ";
                        subEventList.remove(genTag);
                        if (!subEventList.isEmpty()) {
                            where += " OR ";
                        } else {
                            where += " ) ";
                        }
                    }

                    if (!subEventList.isEmpty()) {
                        where += " (tag_type.code IN ('" + StringUtils.join(subEventList, "','") + "')))";
                    }
                    if (genTagFound) {
                        where += ")";
                    }
                } else {
                    where += ")";
                }
            }
            where += ")";
        }

        List<Azione> listEvents;
        TreeMap map = new TreeMap();
        map.put("where", where);
        map.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(map);
        if (conTag) {
            listEvents = getSqlSession().selectList("Azione.getActionsLimitedEventByTag", map);
        } else {
            listEvents = getSqlSession().selectList("Azione.getActionsLimitedEvent", map);
        }
        // non lancio la loadButtonTags perchè raggruppa per eventId e la query ritorna lo stesso eventId per tag diversi
        // non sembra aver alcun impatto sulle altre pagine, anche perchè viene usato solo per abilitare / disabilitare i tasti
        // di filtro delle azioni, oltre a contare quante azioni ci sono
        //listEvents = loadButtonTags(listEvents);

        return listEvents;
    }

    @Override
    public FilterGameWrapper getActionsSingleMatchByType(String idGame, String event, String filter, Long idComp, Long teamId, Long playerId, String seasonIds, Long limit, Long groupsetId, Long sportId, List<Competition> allowedCompetitions, Long userId, Long groupId, String language) throws SQLException {
        // ATT#ATT-4@ATT-5
//        String[] splitconfig = event.split("-_-");
//        String config = splitconfig[0];
//        // evt = TIF#TIF-1@TIF-2
//        String[] evt = splitconfig[1].split("#");
//        String[] splitFond = evt[0].split("-");
//        String fondFilter = "";
//        for (String s : splitFond) {
//            if (!s.isEmpty()) {
//                fondFilter += (fondFilter.isEmpty() ? "'" : ",'") + s + "'";
//            }
//        }

        String where = " where fixture.season_id IN(" + seasonIds + ")";
        if (groupId != null) {
            where += " and fixture.group_id = " + groupId;
        }
        /* filter
        0 dateFrom
        1 dateTo
        2 module team
        3 module opponent team
        4 score (V vittoria, N pareggio, P sconfitta)
        5 casa o trasferta (home team in casa, away team in trasferta)
         */
        String[] gameFilter = filter.split("\\,");
        if (gameFilter.length == 7) {
            DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
            DateFormat dfDB = new SimpleDateFormat("yyyy-MM-dd");
            // 0 dateFrom
            if (!gameFilter[0].isEmpty()) {
                try {
                    String fromDate = dfDB.format(formatter.parse(gameFilter[0]));
                    where += " and fixture.game_date >= '" + fromDate + "'";
                } catch (ParseException ex) {

                }
            }
            // 1 dateTo
            if (!gameFilter[1].isEmpty()) {
                try {
                    String toDate = dfDB.format(formatter.parse(gameFilter[1]));
                    where += " and fixture.game_date <= '" + toDate + "'";
                } catch (ParseException ex) {

                }
            }
            // 2 module
            if (!gameFilter[2].isEmpty() && teamId > -1) {
                where += " and ((fixture.home_team_id = " + teamId + " and fixture.home_module = '" + gameFilter[2] + "') or (fixture.away_team_id = " + teamId + " and fixture.away_module = '" + gameFilter[2] + "'))";
            }
            // 3 module vs
            if (!gameFilter[3].isEmpty()) {
                where += " and ((fixture.away_team_id = " + teamId + " and fixture.home_module = '" + gameFilter[3] + "') or (fixture.home_team_id = " + teamId + " and fixture.away_module = '" + gameFilter[3] + "'))";
            }
            // 4 score
            if (!gameFilter[4].isEmpty()) {
                switch (gameFilter[4]) {
                    case "V":
                        where += " and ((fixture.home_team_id IN (" + teamId + ") and fixture.home_score > fixture.away_score) or (fixture.away_team_id IN (" + teamId + ") and fixture.away_score > fixture.home_score))";
                        break;

                    case "N":
                        where += " and ((fixture.home_team_id IN (" + teamId + ") and fixture.home_score = fixture.away_score) or (fixture.away_team_id IN (" + teamId + ") and fixture.away_score = fixture.home_score))";
                        break;

                    case "P":
                        where += " and ((fixture.home_team_id IN (" + teamId + ") and fixture.home_score < fixture.away_score) or (fixture.away_team_id IN (" + teamId + ") and fixture.away_score < fixture.home_score))";
                        break;
                }
            }
            // 5 casa o trasferta
            if (!gameFilter[5].isEmpty()) {
                switch (gameFilter[5]) {
                    case "home":
                        where += " and fixture.home_team_id IN (" + teamId + ")";
                        break;
                    case "away":
                        where += " and fixture.away_team_id IN (" + teamId + ")";
                        break;
                }
            }
        }

        Map<String, Integer> panelEventTypeMap = new HashMap<>();
        Map<String, Integer> panelTagTypeMap = new HashMap<>();
        String config = null;
        if (event != null && !event.isEmpty()) {
            String[] splitted = event.split("\\|");
            where += " AND ((";
            for (int i = 0; i < splitted.length; i++) {
                List<Integer> eventTypeList = new ArrayList<>();
                List<Integer> subEventList = new ArrayList<>();

                String subEvent = splitted[i];
                String[] splitconfig = subEvent.split("-_-");
                config = splitconfig[0];
                if (StringUtils.isNotBlank(config)) {
                    if (panelEventTypeMap.isEmpty()) {
                        List<Tags> panelEventTypes = getGroupsetEventType(config, StringUtils.equalsIgnoreCase(config, "SICS2015") ? -1l : groupsetId);
                        List<Tags> panelTagTypes = getGroupsetTagType(config, StringUtils.equalsIgnoreCase(config, "SICS2015") ? -1l : groupsetId);
                        for (Tags eventType : panelEventTypes) {
                            panelEventTypeMap.put(eventType.getCode(), eventType.getId());
                        }
                        for (Tags tagType : panelTagTypes) {
                            panelTagTypeMap.put(tagType.getCode(), tagType.getId());
                        }
                    }
                }
                // evt = TIF#TIF-1@TIF-2
                String[] evt = splitconfig[1].split("#");
                if (!splitconfig[1].contains("#")) {
                    if (splitconfig[1].contains("@")) {
                        evt = splitconfig[1].split("@");
                    }
                }
                if (evt.length == 2) {
                    for (String tagType : evt[1].split("@")) {
                        subEventList.add(panelTagTypeMap.get(tagType));
                    }
                }

                String[] splitFond = evt[0].split("-");
                for (String s : splitFond) {
                    if (!s.isEmpty()) {
                        eventTypeList.add(panelEventTypeMap.get(s));
                    }
                }

                if (i > 0) {
                    where += " OR (";
                }
                where += "event_type.id IN (" + StringUtils.join(eventTypeList, ",") + ")";
                if (!subEventList.isEmpty()) {
                    where += " AND event_tag.tag_type_id IN (" + StringUtils.join(subEventList, ",") + "))";
                } else {
                    where += ")";
                }
            }
            where += ")";
        }

        if (teamId != null && teamId > 0) {
            where += " AND team_id = " + teamId;
        }
        if (playerId != null && playerId > 0) {
            where += " AND (event_player.player_id = " + playerId + ")";
        }

        if (idComp != null && idComp > 0) {
            where += " AND competition_id = " + idComp;
        }

        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }
        where += " AND fixture.competition_id IN (" + StringUtils.join(competitionIdList, ",") + ")";
        where += " AND fixture.deleted = 0 ";
        where += " AND event.groupset_id IN(-1," + groupsetId + ") ";
        where += " AND panel.name IN('" + config + ".xml', 'SICS_TV_PERSONAL.xml') GROUP BY xml_idevent ORDER BY fixture.game_date DESC LIMIT " + (limit * 100);
        // DAFARE 2021-02-01 aggiunto where per usare game_id su hightlighted
        //where = "LEFT JOIN game ON game.fixture_id=fixture.id " + where;
        /*
            Faccio una query con i filtri impostati per determinare gli idEvent delle azioni da caricare
         */
        TreeMap map = new TreeMap();
        map.put("language", language);
        map.put("where", where);
        map.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(map);
        List listIdEvents = getSqlSession().selectList("Azione.getActionsLimited", map);
        listIdEvents = loadButtonTags(listIdEvents);
        // se non trovo eventi ritorno un oggetto vuoto
        if (listIdEvents.isEmpty()) {
            return new FilterGameWrapper();
        }
        where = " WHERE event.xml_idevent IN (";

        for (Object obj : listIdEvents) {
            Azione action = (Azione) obj;
            where += (where.endsWith("(") ? "" : ",") + "'" + action.getmIdEvent() + "'";
        }

        where += ")";
        where += " and fixture.id=" + idGame;
        // DAFARE perchè filtrare team e player? non sono già filtrati nella query precedente?
        if (teamId != null && teamId > 0) {
            where += " AND team_id = " + teamId;
        }
        if (playerId != null && playerId > 0) {
            where += " AND (event_player.player_id = " + playerId + " OR event_player.playerto_id = " + playerId + ")";
        }
        where += " AND fixture.competition_id IN(" + StringUtils.join(competitionIdList, ",") + ") ORDER BY fixture.game_date DESC";

        /*
            Dalla lista degli idEvent ricercati mi carico le informazioni delle azioni
            NB. è necessario farlo in due query perché nel DB per ciascun tag di un'azione c'è una riga quindi un azione con più tag 
            comparirebbe più volte e applicando il limit 100 mi ritroverei alla fine con meno azioni.
         */
        TreeMap map2 = new TreeMap();
        map2.put("language", language);
        map2.put("where", where);
        map2.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(map2);
        List listEvents;
        if (sportId == 0) {
            listEvents = getSqlSession().selectList("Azione.getActionsSingleMatchByFilter", map2);
        } else {
            listEvents = getSqlSession().selectList("Azione.getActionsSingleMatchByFilterBasket", map2);
        }
        listEvents = loadButtonTags(listEvents);

        // Dalle azioni mi ricavo una mappa con chiave l'idFixture e valore il Game di riferimento della partita a cui fa riferimento l'azione
        Map<Long, Game> games = new HashMap<>();
        for (Object obj : listEvents) {
            Azione action = (Azione) obj;
            Game game = games.get(action.getIdFixture());
            if (game == null) {
                try {
                    games.put(action.getIdFixture(), null);
                } catch (Exception ex) {

                }
            }
        }

        where = " where fixture.id IN (";
        for (Long idFixture : games.keySet()) {
            where += (where.endsWith("(") ? "" : ",") + idFixture.toString();
        }
        where += ") GROUP BY fixture.id";

        /*
            Mi carico i dati dei Game relativi alle azioni lette
         */
        List gamesList;
        if (!games.isEmpty()) {
            TreeMap map3 = new TreeMap();
            map3.put("where", where);
            map3.put("language", language);
            gamesList = getSqlSession().selectList("Game.getGameByListFixtureId", map3);
        } else {
            TreeMap map3 = new TreeMap();
            map3.put("where", Long.valueOf(idGame));
            map3.put("language", language);
            gamesList = getSqlSession().selectList("Game.getGameByFixtureId", map3);
        }
        games.clear();
        for (Object objGame : gamesList) {
            Game game = (Game) objGame;
            games.put(game.getIdFixture(), game);
        }

        return addAdditionalInfoAction(listEvents, games, userId, groupsetId, language);
    }

    @Override
    public FilterGameWrapper getActionsByType(String idGame, String event, String filter, Long idComp, Long teamId, Long playerId, String seasonIds, Long limit, Long groupsetId, Long sportId, List<Competition> allowedCompetitions, String gameIds, Long userId, Long groupId, String from, String to, Boolean allSeasons, User curUser, String language) {
        // ATT#ATT-4@ATT-5
//        String[] splitted = event.split("\\|");
//        List<String> subEventList = new ArrayList<>();
//        String config = null, fondFilter = "";
//        for (String subEvent : splitted) {
//            String[] splitconfig = subEvent.split("-_-");
//            config = splitconfig[0]; // SICS2015
//            // evt = TIF#TIF-1@TIF-2
//            String[] evt = splitconfig[1].split("#");
//            if (evt.length == 2) {
//                subEventList.addAll(Arrays.asList(evt[1].split("@")));
//            }
//            
//            String[] splitFond = evt[0].split("-");
//            for (String s : splitFond) {
//                if (!s.isEmpty()) {
//                    fondFilter += (fondFilter.isEmpty() ? "'" : ",'") + s + "'";
//                }
//            }
//        }

        String where = "";
        // deve stare qua perchè aggiunge una join
        if (BooleanUtils.isTrue(curUser.getGuest())) {
            where += " LEFT JOIN event_owner ON event_owner.event_id = event.id";
        }

        if (BooleanUtils.isTrue(allSeasons)) {
            seasonIds = ""; // altrimenti viene usato il db frammentato se ci sono le stagioni
            where += " where fixture.season_id IN(SELECT id FROM season WHERE visible = 1)";
        } else {
            where += " where fixture.season_id IN(" + seasonIds + ")";
        }
        if (gameIds != null && !gameIds.isEmpty()) { // viene usato nel caso in cui filtro per X partite nella pagina team o player
            List<String> fixtureIds = new ArrayList<>(Arrays.asList(StringUtils.split(gameIds, ",")));
            where += " AND fixture.id IN (" + StringUtils.join(fixtureIds, ",") + ")";
        } else if (!idGame.isEmpty()) {
            where += " and fixture.id = " + idGame;
        }
        if (groupId != null) {
            where += " and fixture.group_id = " + groupId;
        }
        if (StringUtils.isNotBlank(from) && StringUtils.isNotBlank(to)) {
            where += " and fixture.game_date BETWEEN '" + from + "' AND '" + to + "'";
        } else if (StringUtils.isNotBlank(from)) {
            where += " and fixture.game_date >= '" + from + "'";
        } else if (StringUtils.isNotBlank(to)) {
            where += " and fixture.game_date <= '" + to + "'";
        }
        // non filtro per le competizioni a cui hai accesso
        // poi passo tutte le azioni e uso il campo "isVisible" per definire se posso cliccare
        // nell'azione
        if (BooleanUtils.isNotTrue(allSeasons)) {
            List<String> competitionIdList = new ArrayList<>();
            for (Competition comp : allowedCompetitions) {
                competitionIdList.add(comp.getId().toString());
            }
            where += " and fixture.competition_id IN(" + StringUtils.join(competitionIdList, ",") + ")";
        }

        /* filter
        0 dateFrom
        1 dateTo
        2 module team
        3 module opponent team
        4 score (V vittoria, N pareggio, P sconfitta)
        5 casa o trasferta (home team in casa, away team in trasferta)
         */
        String[] gameFilter = filter.split("\\,");
        if (gameFilter.length == 7) {
            DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
            DateFormat dfDB = new SimpleDateFormat("yyyy-MM-dd");
            // 0 dateFrom
            if (!gameFilter[0].isEmpty()) {
                try {
                    String fromDate = dfDB.format(formatter.parse(gameFilter[0]));
                    where += " and fixture.game_date >= '" + fromDate + "'";
                } catch (ParseException ex) {

                }
            }
            // 1 dateTo
            if (!gameFilter[1].isEmpty()) {
                try {
                    String toDate = dfDB.format(formatter.parse(gameFilter[1]));
                    where += " and fixture.game_date <= '" + toDate + "'";
                } catch (ParseException ex) {

                }
            }
            // 2 module
            if (!gameFilter[2].isEmpty() && teamId > -1) {
                where += " and ((fixture.home_team_id = " + teamId + " and fixture.home_module = '" + gameFilter[2] + "') or (fixture.away_team_id = " + teamId + " and fixture.away_module = '" + gameFilter[2] + "'))";
            }
            // 3 module vs
            if (!gameFilter[3].isEmpty()) {
                where += " and ((fixture.away_team_id = " + teamId + " and fixture.home_module = '" + gameFilter[3] + "') or (fixture.home_team_id = " + teamId + " and fixture.away_module = '" + gameFilter[3] + "'))";
            }
            // 4 score
            if (!gameFilter[4].isEmpty()) {
                switch (gameFilter[4]) {
                    case "V":
                        where += " and ((fixture.home_team_id IN (" + teamId + ") and fixture.home_score > fixture.away_score) or (fixture.away_team_id IN (" + teamId + ") and fixture.away_score > fixture.home_score))";
                        break;

                    case "N":
                        where += " and ((fixture.home_team_id IN (" + teamId + ") and fixture.home_score = fixture.away_score) or (fixture.away_team_id IN (" + teamId + ") and fixture.away_score = fixture.home_score))";
                        break;

                    case "P":
                        where += " and ((fixture.home_team_id IN (" + teamId + ") and fixture.home_score < fixture.away_score) or (fixture.away_team_id IN (" + teamId + ") and fixture.away_score < fixture.home_score))";
                        break;
                }
            }
            // 5 casa o trasferta
            if (!gameFilter[5].isEmpty()) {
                switch (gameFilter[5]) {
                    case "home":
                        where += " and fixture.home_team_id IN (" + teamId + ")";
                        break;
                    case "away":
                        where += " and fixture.away_team_id IN (" + teamId + ")";
                        break;
                }
            }
        }

        Map<String, Integer> panelEventTypeMap = new HashMap<>();
        Map<String, Integer> panelTagTypeMap = new HashMap<>();
        String config = null;
        if (event != null && !event.isEmpty()) {
            String[] splitted = event.split("\\|");
            where += " AND ((";
            for (int i = 0; i < splitted.length; i++) {
                List<Integer> eventTypeList = new ArrayList<>();
                List<Integer> tagWithOr = new ArrayList<>();
                List<Integer> tagWithAnd = new ArrayList<>();
                List<Integer> tagWithNot = new ArrayList<>();

                String subEvent = splitted[i];
                String[] splitconfig = subEvent.split("-_-");
                config = splitconfig[0];
                if (StringUtils.isNotBlank(config)) {
                    if (panelEventTypeMap.isEmpty()) {
                        try {
                            List<Tags> panelEventTypes = getGroupsetEventType(config, StringUtils.equalsIgnoreCase(config, "SICS2015") ? -1l : groupsetId);
                            List<Tags> panelTagTypes = getGroupsetTagType(config, StringUtils.equalsIgnoreCase(config, "SICS2015") ? -1l : groupsetId);
                            for (Tags eventType : panelEventTypes) {
                                panelEventTypeMap.put(eventType.getCode(), eventType.getId());
                            }
                            for (Tags tagType : panelTagTypes) {
                                panelTagTypeMap.put(tagType.getCode(), tagType.getId());
                            }
                        } catch (SQLException ex) {
                            GlobalHelper.reportError(ex);
                        }
                    }
                }
                // evt = TIF#TIF-1@TIF-2
                String[] evt = splitconfig[1].split("#");
                if (!splitconfig[1].contains("#")) {
                    if (splitconfig[1].contains("@")) {
                        evt = splitconfig[1].split("@");
                    }
                }
                if (evt.length == 2) {
                    for (String tmpSubEvent : evt[1].split("@")) {
                        // {0}PASS-1 OPPURE PASS-1
                        if (!tmpSubEvent.contains("{")) { // per tutti tranne che per le statistiche non ho la parte {X}
                            // metto quindi modalità 0, ovvero OR
                            tagWithOr.add(panelTagTypeMap.get(tmpSubEvent));
                        } else { // se è presente allora metto la modalità giusta
                            String modality = StringUtils.substringBetween(tmpSubEvent, "{", "}");
                            String tmpSubEventReal = tmpSubEvent.replace("{" + modality + "}", "");
                            switch (modality) {
                                case "0":
                                    tagWithOr.add(panelTagTypeMap.get(tmpSubEventReal));
                                    break;
                                case "1":
                                    tagWithAnd.add(panelTagTypeMap.get(tmpSubEventReal));
                                    break;
                                case "2":
                                    tagWithNot.add(panelTagTypeMap.get(tmpSubEventReal));
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }

                String[] splitFond = evt[0].split("-");
                for (String s : splitFond) {
                    if (!s.isEmpty()) {
                        eventTypeList.add(panelEventTypeMap.get(s));
                    }
                }

                if (i > 0) {
                    where += " OR (";
                }
                where += "event_type.id IN (" + StringUtils.join(eventTypeList, ",") + ")";
                if (!tagWithOr.isEmpty() || !tagWithAnd.isEmpty() || !tagWithNot.isEmpty()) {
                    where += " AND ";

                    if (!tagWithNot.isEmpty()) {
                        where += " (event_tag.tag_type_id NOT IN (" + StringUtils.join(tagWithNot, ",") + "))";
                        if (!tagWithOr.isEmpty() || !tagWithAnd.isEmpty()) {
                            where += " AND ";
                        } else {
                            where += ")";
                        }
                    }

                    // PER OR E AND DEVO PER FORZA APPLICARE TUTTO IN OR E FARE IL FILTRO DOPO
                    // PER IL NOT INVECE POSSO FARLO QUA IN QUERY
                    List<Integer> allowedTags = new ArrayList<>();
                    allowedTags.addAll(tagWithOr);
                    allowedTags.addAll(tagWithAnd);

                    // GESTIONE TAG "-GEN", TASTO "Generico"
//                    String genTag = evt[0] + "-GEN";
                    boolean genTagFound = false;
                    if (allowedTags.contains(null)) {
                        genTagFound = true;
                        where += " ((event_tag.tag_type_id IS NULL)";
                        allowedTags.remove(null);

                        if (!allowedTags.isEmpty()) {
                            where += " OR ";
                        } else {
                            where += " ))";
                        }
                    }

                    if (!allowedTags.isEmpty()) {
                        where += " (event_tag.tag_type_id IN (" + StringUtils.join(allowedTags, ",") + ")))";
                        if (genTagFound) {
                            where += ")";
                        }
                    }
                } else {
                    where += ")";
                }
            }
            where += ")";
        }

        if (teamId != null && teamId > 0) {
            where += " AND team_id = " + teamId;
        }
        if (playerId != null && playerId > 0) {
            where += " AND (event_player.player_id = " + playerId + ")";
        }

        if (idComp != null && idComp > 0) {
            where += " AND competition_id = " + idComp;
        }

        if (BooleanUtils.isTrue(curUser.getGuest())) {
            where += " AND IFNULL(event_owner.user_id, " + curUser.getId() + ") = " + curUser.getId();
        }

        where += " AND fixture.deleted = 0 ";
        where += " AND event.groupset_id IN(-1," + groupsetId + ") ";
        where += " AND panel.name IN('" + config + ".xml', 'SICS_TV_PERSONAL.xml') ";
        String groupByToAdd = " GROUP BY xml_idevent ORDER BY fixture.game_date DESC LIMIT " + (limit * 100);
        where += groupByToAdd;
        // DAFARE 2021-02-01 aggiunto where per usare game_id su hightlighted
        //where = "LEFT JOIN game ON game.fixture_id=fixture.id " + where;
        /*
            Prima di tutto carico la lista di game.id per filtrare poi le query successive
         */
        TreeMap map0 = new TreeMap();
//        map0.put("where", where);
//        List<Game> gameIdsList = getSqlSession().selectList("Game.getGameIdsByFilter", map0);
//        
//        where = where.replace(groupByToAdd, "");
//        if (!gameIdsList.isEmpty()) {
//            where += " AND game.id IN(";
//            boolean oneAdded = false;
//            for (Game game : gameIdsList) {
//                if (oneAdded) where += ",";
//                where += game.getId().toString();
//                oneAdded = true;
//            }
//            where += ")";
//        }
//        where += groupByToAdd;

        /*
            Faccio una query con i filtri impostati per determinare gli idEvent delle azioni da caricare
         */
//        map0 = new TreeMap();
        map0.put("where", where);
        map0.put("database", GlobalHelper.getDatabase(seasonIds));
        map0.put("language", language);
        GlobalHelper.adjustQueryParams(map0);
        List listIdEvents = getSqlSession().selectList("Azione.getActionsLimited", map0);
//        loadButtonTags(listIdEvents);
        // se non trovo eventi ritorno un oggetto vuoto
        if (listIdEvents.isEmpty()) {
            return new FilterGameWrapper();
        }
        where = " WHERE event.id IN (";

        for (Object obj : listIdEvents) {
            Azione action = (Azione) obj;
            where += (where.endsWith("(") ? "" : ",") + "'" + action.getId() + "'";
        }

        where += ")";
        // DAFARE perchè filtrare team e player? non sono già filtrati nella query precedente?
        if (teamId != null && teamId > 0) {
            where += " AND team_id = " + teamId;
        }
        if (playerId != null && playerId > 0) {
            where += " AND (event_player.player_id = " + playerId + " OR event_player.playerto_id = " + playerId + ")";
        }
//        where += " AND game.groupset_id IN(-1," + groupsetId + ")";

//        if (!gameIdsList.isEmpty()) {
//            where += " AND game.id IN(";
//            boolean oneAdded = false;
//            for (Game game : gameIdsList) {
//                if (oneAdded) where += ",";
//                where += game.getId().toString();
//                oneAdded = true;
//            }
//            where += ") ORDER BY fixture.game_date DESC";
//        } else {
//            where += " ORDER BY fixture.game_date DESC";
//        }
        where += " ORDER BY fixture.game_date DESC";

        /*
            Dalla lista degli idEvent ricercati mi carico le informazioni delle azioni
            NB. è necessario farlo in due query perché nel DB per ciascun tag di un'azione c'è una riga quindi un azione con più tag 
            comparirebbe più volte e applicando il limit 100 mi ritroverei alla fine con meno azioni.
         */
        List listEvents;

        TreeMap map = new TreeMap();
        map.put("where", where);
        map.put("database", GlobalHelper.getDatabase(seasonIds));
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        if (sportId == 0) {
            listEvents = getSqlSession().selectList("Azione.getActionsByFilter", map);
        } else {
            listEvents = getSqlSession().selectList("Azione.getActionsByFilterBasket", map);
        }
        listEvents = loadButtonTags(listEvents, event);

        // Dalle azioni mi ricavo una mappa con chiave l'idFixture e valore il Game di riferimento della partita a cui fa riferimento l'azione
        Map<Long, Game> games = new LinkedHashMap<>();
        for (Object obj : listEvents) {
            Azione action = (Azione) obj;
            Game game = games.get(action.getIdFixture());
            if (game == null) {
                try {
                    games.put(action.getIdFixture(), null);
                } catch (Exception ex) {

                }
            }
        }

        /*
            Mi carico i dati dei Game relativi alle azioni lette
         */
        where = " where fixture.id IN (";
        for (Long idFixture : games.keySet()) {
            where += (where.endsWith("(") ? "" : ",") + idFixture.toString();
        }
        where += ") GROUP BY fixture.id ORDER BY fixture.game_date DESC";

        TreeMap map2 = new TreeMap();
        map2.put("where", where);
        map2.put("language", language);
        List gamesList = getSqlSession().selectList("Game.getGameByListFixtureId", map2);
        games = new LinkedHashMap<>();
        for (Object objGame : gamesList) {
            Game game = (Game) objGame;
            games.put(game.getIdFixture(), game);
        }

        return addAdditionalInfoAction(listEvents, games, userId, groupsetId, allowedCompetitions, language);
    }

    @Override
    public List getCompetitionTactAllowed(Long idGro) throws SQLException {
        List<Long> listGroupset;
        TreeMap mapTree = new TreeMap();
        mapTree.put("idGroup", idGro);
        listGroupset = getSqlSession().selectList("Competition.getCompetitionTactAllowed", mapTree);
        return listGroupset;
    }

    @Override
    public FilterGameWrapper readHighlightsEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, String seasonIds, String language) throws SQLException {
        List listEvents = null;
        TreeMap mapTree = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        mapTree.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        mapTree.put("idGroup", groupsetId);
        mapTree.put("idFixtures", idFixtures);
        String eventType = " (";
        String tagType = " (";
        for (int i = 0; i < GlobalHelper.kFastFilterHighlights.size(); i++) {
            String[] splitEventType = GlobalHelper.kFastFilterHighlights.get(i).split("#");
            eventType += i + 1 < GlobalHelper.kFastFilterHighlights.size() ? "'" + splitEventType[0] + "'," : "'" + splitEventType[0] + "')";
            String[] splitTags = splitEventType[1].split("@");
            for (int j = 0; j < splitTags.length; j++) {
                tagType += (j + 1 < splitTags.length) ? "'" + splitTags[j] + "'," : "'" + splitTags[j] + "'";
            }
            if (i + 1 < GlobalHelper.kFastFilterHighlights.size()) {
                tagType += ",";
            }
        }
        tagType += ")";
        String where = " and event_type.code IN" + eventType + " and tag_type.code IN" + tagType;
        where += " AND fixture.competition_id IN ( " + StringUtils.join(competitionIdList, ",") + ")";
        if (groupId != null) {
            where += " AND fixture.group_id = " + groupId;
        }
        mapTree.put("where", where);
        mapTree.put("language", language);
        mapTree.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(mapTree);
        //mapTree.put("strFilter", (eventTypeFilter!=null) ? " AND event_type.code='"+eventTypeFilter+"'" : "");
        if (sportId == 0) {
            listEvents = getSqlSession().selectList("Azione.readHightlightsEventsMatches", mapTree);
        } else {
//            listEvents = getSqlSession().selectList("Azione.readEventsMatchesBasket", mapTree);
        }
        listEvents = loadButtonTags(listEvents);

        return addAdditionalInfoAction(listEvents, games, userId, groupsetId, language);
    }

    @Override
    public FilterGameWrapper readIPOEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, Long teamId, List<Competition> allowedCompetitions, Long userId, Long groupId, String seasonIds, String language) throws SQLException {
        List listEvents = null;
        TreeMap mapTree = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        mapTree.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        mapTree.put("idGroup", groupsetId);
        mapTree.put("idFixtures", idFixtures);
        mapTree.put("groupId", groupId);
        mapTree.put("language", language);
        mapTree.put("idTeam", teamId);
        mapTree.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(mapTree);
        if (sportId == 0) {
            listEvents = getSqlSession().selectList("Azione.readIPOEventsMatches", mapTree);
        } else {
//            listEvents = getSqlSession().selectList("Azione.readEventsMatchesBasket", mapTree);
        }
        listEvents = loadButtonTags(listEvents);

        return addAdditionalInfoAction(listEvents, games, userId, groupsetId, language);
    }

    @Override
    public FilterGameWrapper readEventMatches(String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions/*, String eventTypeFilter*/, Long userId, Long groupId, User curUser, String language) throws SQLException {
        List<Azione> listEvents;
        TreeMap mapTree = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        mapTree.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        mapTree.put("idGroup", groupsetId);
        mapTree.put("idFixtures", idFixtures);
        mapTree.put("groupId", groupId);
        mapTree.put("language", language);
        mapTree.put("isGuest", BooleanUtils.isTrue(curUser.getGuest()));
        mapTree.put("userId", curUser.getId());
        mapTree.put("database", GlobalHelper.getDatabase(curUser.getSavedSeasonForQuery()));
        GlobalHelper.adjustQueryParams(mapTree);

        //mapTree.put("strFilter", (eventTypeFilter!=null) ? " AND event_type.code='"+eventTypeFilter+"'" : "");
        if (games != null) {
            List<String> gameIds = new ArrayList<>();
            for (Game game : games.values()) {
                gameIds.add(game.getIdFixture().toString());
            }
            mapTree.put("gameIds", StringUtils.join(gameIds, ","));
        }
        GlobalHelper.adjustQueryParams(mapTree);
        if (sportId == 0) {
            listEvents = getSqlSession().selectList("Azione.readEventsMatches", mapTree);
        } else {
            listEvents = getSqlSession().selectList("Azione.readEventsMatchesBasket", mapTree);
        }
        listEvents = loadButtonTags(listEvents);

        return addAdditionalInfoAction(listEvents, games, userId, groupsetId, language);
    }

    @Override
    public FilterGameWrapper readEventMatchesByEventIds(String eventIds, Map<Long, Game> games, Long sportId, Long groupsetId, Long playlistTvId, List<Competition> allowedCompetitions, User curUser, String language) throws SQLException {
        List<Azione> listEvents;
        TreeMap mapTree = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        if (allowedCompetitions != null) { // se arrivi da /auth/videoPlaylistTv e non sei loggato arriva null -> ovvero TUTTE le competizioni
            for (Competition comp : allowedCompetitions) {
                competitionIdList.add(comp.getId().toString());
            }

            mapTree.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        }
        mapTree.put("eventIds", eventIds);
        mapTree.put("idGroup", groupsetId);
        mapTree.put("language", language);
        if (curUser != null) {
            // questa funzione viene usata nelle playlist pubbliche
            mapTree.put("isGuest", BooleanUtils.isTrue(curUser.getGuest()));
            mapTree.put("userId", curUser.getId());
        }

        GlobalHelper.adjustQueryParams(mapTree);
        if (sportId == 0) {
            listEvents = getSqlSession().selectList("Azione.readEventsMatchesByEventIds", mapTree);
        } else { // TODO
            listEvents = getSqlSession().selectList("Azione.readEventsMatchesBasket", mapTree);
        }
        listEvents = loadButtonTags(listEvents);

        if (playlistTvId != null) {
            List<String> eventIdsList = Arrays.asList(eventIds.split(","));
            List<PlaylistTVEvent> playlistTvEventList = getPlaylistTvEventsById(playlistTvId);
            // carico anche quelli cancellati
            List<PlaylistTVEvent> playlistTvEventDeletedList = getPlaylistTvEventsDeletedById(playlistTvId);
            playlistTvEventList.addAll(playlistTvEventDeletedList);
            Map<Long, Game> gameCache = new HashMap<>();
            // carico tutti i game in 1 sola query
            List<Long> fixtureIds = new ArrayList<>();
            for (PlaylistTVEvent event : playlistTvEventList) {
                if (eventIdsList.contains(event.getEventId().toString())) {
                    Long idFixture = event.getEvent_fixture_id();
                    if (!fixtureIds.contains(idFixture)) {
                        fixtureIds.add(idFixture);
                    }
                }
            }
            if (!fixtureIds.isEmpty()) {
                List<Game> gameList = getGamesByFixtureIds(fixtureIds, groupsetId, language);
                for (Game game : gameList) {
                    gameCache.put(game.getIdFixture(), game);
                }
            }

            for (PlaylistTVEvent event : playlistTvEventList) {
                if (eventIdsList.contains(event.getEventId().toString())) {
                    Long idFixture = event.getEvent_fixture_id();
                    if (gameCache.get(idFixture) == null) { // devo caricare il game
                        gameCache.put(idFixture, getGameByFixtureId(idFixture, language));
                    }
                    Game game = gameCache.get(idFixture);

                    boolean found = false;
                    for (Azione azione : listEvents) {
                        if (Long.compare(azione.getId(), event.getEventId()) == 0) {
                            azione.setmType(event.getEvent_type_code());
                            azione.setmStart(event.getEvent_period_start_msec());
                            azione.setmEnd(event.getEvent_period_end_msec());
                            azione.setPeriod_start_second(event.getEvent_period_start_second());
                            azione.setPeriod_end_second(event.getEvent_period_end_second());
                            azione.setPeriod_start_minute(event.getEvent_period_start_minute());
                            azione.setPeriod_end_minute(event.getEvent_period_end_minute());
                            azione.setmHalf(event.getEvent_period_id());
                            azione.setmNote(event.getEvent_note());
                            azione.setmIdEvent(event.getEvent_xml_idevent());
                            azione.setIdFixture(event.getEvent_fixture_id());
                            azione.setmTactStart(event.getEvent_tactical_start_msec());
                            azione.setGroupsetId(event.getEvent_groupset_id());
                            azione.setIdTeam(event.getEvent_team_id());
                            azione.setmResult(event.getEvent_result());
                            azione.setUser(event.getEvent_author());
                            Button button = new Button();
                            button.setCod(event.getEvent_type_code());
                            button.setDesc(event.getEvent_type_desc());
                            button.setEndesc(event.getEvent_type_desc_en());
                            button.setFrdesc(event.getEvent_type_desc_fr());
                            button.setEsdesc(event.getEvent_type_desc_es());
                            button.setRudesc(event.getEvent_type_desc_ru());
                            azione.setButton(button);

                            ArrayList<Tags> tags = new ArrayList();
                            if (event.getTag_type_code() != null && event.getTag_type_desc() != null && event.getTag_type_desc_en() != null
                                    && event.getTag_type_desc_fr() != null && event.getTag_type_desc_es() != null && event.getTag_type_desc_ru() != null) {
                                String[] codeSplitted = event.getTag_type_code().split(",");
                                String[] descSplitted = event.getTag_type_desc().split(",");
                                String[] descEnSplitted = event.getTag_type_desc_en().split(",");
                                String[] descFrSplitted = event.getTag_type_desc_fr().split(",");
                                String[] descEsSplitted = event.getTag_type_desc_es().split(",");
                                String[] descRuSplitted = event.getTag_type_desc_ru().split(",");
                                for (int i = 0; i < Math.max(codeSplitted.length, descSplitted.length); i++) {
                                    Tags tag = new Tags();
                                    if (codeSplitted.length > i) {
                                        tag.setCode(codeSplitted[i]);
                                    }
                                    if (descSplitted.length > i) {
                                        tag.setDesc(descSplitted[i]);
                                    }
                                    if (descEnSplitted.length > i) {
                                        tag.setEndesc(descEnSplitted[i]);
                                    }
                                    if (descFrSplitted.length > i) {
                                        tag.setFrdesc(descFrSplitted[i]);
                                    }
                                    if (descEsSplitted.length > i) {
                                        tag.setEsdesc(descEsSplitted[i]);
                                    }
                                    if (descRuSplitted.length > i) {
                                        tag.setRudesc(descRuSplitted[i]);
                                    }
                                    tags.add(tag);
                                }
                            }
                            azione.setmTags(tags);
                            azione.setVideoId(game.getVideoId());
                            azione.setVideoName(game.getVideoName());
                            azione.setProvider(game.getProviderId());
                            azione.setmTeam(event.getTeam_name());
                            azione.setmHomeTeam(game.getHomeTeam());
                            azione.setmAwayTeam(game.getAwayTeam());
                            // azione.setmPlayer(event.getPlayer_know_name());
                            if (game.getMinVideoQuality() == 0) {
                                if (BooleanUtils.isNotFalse(game.getHd())) { // se ho HD metto comunque la massima risoluzione
                                    azione.setVideoPathS3(GlobalHelper.pathS3(game.getVideoName(1), "video", true));
                                } else {
                                    azione.setVideoPathS3(GlobalHelper.pathS3(game.getVideoName(0), "video", true));
                                }
                            } else if (game.getMinVideoQuality() == 1) { // se ho minimo HD non posso salvare path video SD
                                azione.setVideoPathS3(GlobalHelper.pathS3(game.getVideoName(1), "video", true));
                            }
                            azione.setHdAvailable(game.getMinVideoQuality() > 0);
                            azione.setConfig(event.getPanel_name());
                            azione.setIsTactical(event.getIsTactical());
                            azione.setTactAvailable(BooleanUtils.isTrue(event.getIsTactical()));
                            azione.setTableOrder(event.getOrder());
                            azione.setUpdateBy(event.getUpdateBy());
                            azione.setUpdateDate(event.getUpdateDate());
                            azione.setUpdateDescription(event.getUpdateDescription());
                            azione.setDeleted(event.getDeleted());

                            found = true;
                            break;
                        }
                    }

                    if (!found) { // significa che l'evento base è stato cancellato
                        Azione azione = new Azione();
                        azione.setId(event.getEventId());
                        azione.setmType(event.getEvent_type_code());
                        azione.setmStart(event.getEvent_period_start_msec());
                        azione.setmEnd(event.getEvent_period_end_msec());
                        azione.setPeriod_start_second(event.getEvent_period_start_second());
                        azione.setPeriod_end_second(event.getEvent_period_end_second());
                        azione.setPeriod_start_minute(event.getEvent_period_start_minute());
                        azione.setPeriod_end_minute(event.getEvent_period_end_minute());
                        azione.setmHalf(event.getEvent_period_id());
                        azione.setmNote(event.getEvent_note());
                        azione.setmIdEvent(event.getEvent_xml_idevent());
                        azione.setIdFixture(event.getEvent_fixture_id());
                        azione.setmTactStart(event.getEvent_tactical_start_msec());
                        azione.setGroupsetId(event.getEvent_groupset_id());
                        azione.setIdTeam(event.getEvent_team_id());
                        azione.setmResult(event.getEvent_result());
                        azione.setUser(event.getEvent_author());
                        Button button = new Button();
                        button.setCod(event.getEvent_type_code());
                        button.setDesc(event.getEvent_type_desc());
                        button.setEndesc(event.getEvent_type_desc_en());
                        button.setFrdesc(event.getEvent_type_desc_fr());
                        button.setEsdesc(event.getEvent_type_desc_es());
                        button.setRudesc(event.getEvent_type_desc_ru());
                        azione.setButton(button);

                        ArrayList<Tags> tags = new ArrayList();
                        if (event.getTag_type_code() != null && event.getTag_type_desc() != null && event.getTag_type_desc_en() != null
                                && event.getTag_type_desc_fr() != null && event.getTag_type_desc_es() != null && event.getTag_type_desc_ru() != null) {
                            String[] codeSplitted = event.getTag_type_code().split(",");
                            String[] descSplitted = event.getTag_type_desc().split(",");
                            String[] descEnSplitted = event.getTag_type_desc_en().split(",");
                            String[] descFrSplitted = event.getTag_type_desc_fr().split(",");
                            String[] descEsSplitted = event.getTag_type_desc_es().split(",");
                            String[] descRuSplitted = event.getTag_type_desc_ru().split(",");
                            for (int i = 0; i < Math.max(codeSplitted.length, descSplitted.length); i++) {
                                Tags tag = new Tags();
                                if (codeSplitted.length > i) {
                                    tag.setCode(codeSplitted[i]);
                                }
                                if (descSplitted.length > i) {
                                    tag.setDesc(descSplitted[i]);
                                }
                                if (descEnSplitted.length > i) {
                                    tag.setEndesc(descEnSplitted[i]);
                                }
                                if (descFrSplitted.length > i) {
                                    tag.setFrdesc(descFrSplitted[i]);
                                }
                                if (descEsSplitted.length > i) {
                                    tag.setEsdesc(descEsSplitted[i]);
                                }
                                if (descRuSplitted.length > i) {
                                    tag.setRudesc(descRuSplitted[i]);
                                }
                                tags.add(tag);
                            }
                        }
                        azione.setmTags(tags);
                        azione.setVideoId(game.getVideoId());
                        azione.setVideoName(game.getVideoName());
                        azione.setProvider(game.getProviderId());
                        azione.setmTeam(event.getTeam_name());
                        azione.setmHomeTeam(game.getHomeTeam());
                        azione.setmAwayTeam(game.getAwayTeam());
                        // azione.setmPlayer(event.getPlayer_know_name());
                        if (game.getMinVideoQuality() == 0) {
                            if (BooleanUtils.isNotFalse(game.getHd())) { // se ho HD metto comunque la massima risoluzione
                                azione.setVideoPathS3(GlobalHelper.pathS3(game.getVideoName(1), "video", true));
                            } else {
                                azione.setVideoPathS3(GlobalHelper.pathS3(game.getVideoName(0), "video", true));
                            }
                        } else if (game.getMinVideoQuality() == 1) { // se ho minimo HD non posso salvare path video SD
                            azione.setVideoPathS3(GlobalHelper.pathS3(game.getVideoName(1), "video", true));
                        }
                        azione.setHdAvailable(game.getMinVideoQuality() > 0);
                        azione.setConfig(event.getPanel_name());
                        azione.setIsTactical(event.getIsTactical());
                        azione.setTactAvailable(BooleanUtils.isTrue(event.getIsTactical()));
                        azione.setTableOrder(event.getOrder());
                        azione.setUpdateBy(event.getUpdateBy());
                        azione.setUpdateDate(event.getUpdateDate());
                        azione.setUpdateDescription(event.getUpdateDescription());
                        azione.setDeleted(event.getDeleted());
                        listEvents.add(azione);
                    }
                }
            }

            // riordino gli eventi per farli tornare sempre in ordine della playlist
            List<Azione> sortedListEvents = new ArrayList<>();
            for (PlaylistTVEvent event : playlistTvEventList) {
                for (Azione tmpEvent : listEvents) {
                    if (event.getEventId() != null && tmpEvent.getId() != null
                            && Long.compare(event.getEventId(), tmpEvent.getId()) == 0) {
                        sortedListEvents.add(tmpEvent);
                    }
                }
            }
            // se manca qualcosa, non so se sia possibile ma meglio controllare
            if (sortedListEvents.size() != listEvents.size()) {
                List<Azione> eventsMissed = new ArrayList<>();
                boolean found = false;
                for (Azione event : listEvents) {
                    for (Azione tmpEvent : sortedListEvents) {
                        if (event.getId() != null && tmpEvent.getId() != null
                                && Long.compare(event.getId(), tmpEvent.getId()) == 0) {
                            found = true;
                            break;
                        }
                    }

                    if (!found) {
                        eventsMissed.add(event);
                    }
                }

                if (!eventsMissed.isEmpty()) {
                    sortedListEvents.addAll(eventsMissed);
                }
            }

        }

        // loadButtonTags(listEvents);
        return addAdditionalInfoAction(listEvents, games, (curUser != null ? curUser.getId() : null), groupsetId, language);
    }

    @Override
    public FilterGameWrapper readPlayerEventMatches(Long playerId, String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions/*, String eventTypeFilter*/, Long userId, Long groupId, User curUser, String language) throws SQLException {
        List listEvents;
        TreeMap mapTree = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        mapTree.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        mapTree.put("playerId", playerId);
        mapTree.put("idGroup", groupsetId);
        mapTree.put("idFixtures", idFixtures);
        mapTree.put("groupId", groupId);
        mapTree.put("language", language);
        mapTree.put("isGuest", BooleanUtils.isTrue(curUser.getGuest()));
        mapTree.put("userId", curUser.getId());
        mapTree.put("database", GlobalHelper.getDatabase(curUser.getSavedSeasonForQuery()));
        GlobalHelper.adjustQueryParams(mapTree);
        //mapTree.put("strFilter", (eventTypeFilter!=null) ? " AND event_type.code='"+eventTypeFilter+"'" : "");
        if (sportId == 0) {
            listEvents = getSqlSession().selectList("Azione.readPlayerEventsMatches", mapTree);
        } else {
            listEvents = getSqlSession().selectList("Azione.readPlayerEventsMatchesBasket", mapTree);
        }
        listEvents = loadButtonTags(listEvents);

        return addAdditionalInfoAction(listEvents, games, userId, groupsetId, language);
    }

    @Override
    public FilterGameWrapper readTeamEventMatches(Long teamId, String idFixtures, Map<Long, Game> games, Long sportId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, User curUser, String language) throws SQLException {
        List listEvents;
        TreeMap mapTree = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        mapTree.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        mapTree.put("teamId", teamId);
        mapTree.put("idGroup", groupsetId);
        mapTree.put("idFixtures", idFixtures);
        mapTree.put("groupId", groupId);
        mapTree.put("language", language);
        mapTree.put("isGuest", BooleanUtils.isTrue(curUser.getGuest()));
        mapTree.put("userId", curUser.getId());
        mapTree.put("database", GlobalHelper.getDatabase(curUser.getSavedSeasonForQuery()));
        GlobalHelper.adjustQueryParams(mapTree);

        if (sportId == 0) {
            listEvents = getSqlSession().selectList("Azione.readTeamEventsMatches", mapTree);
        } else {
            // TODO, non la ho fatta dato che per ora non c'è nulla di basket
            listEvents = getSqlSession().selectList("Azione.readTeamEventsMatches", mapTree);
        }
        listEvents = loadButtonTags(listEvents);

        return addAdditionalInfoAction(listEvents, games, userId, groupsetId, language);
    }

    @Override
    public FilterGameWrapper readPlayerBestEvents(Long playerId, Long teamId, Long competitionId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, String seasonIds, String language) throws SQLException {
        List<Azione> listEvents;

        Atleta player = getAtletaById(seasonIds, playerId, teamId, groupsetId, false, language);
        if (player != null) {
            TreeMap mapTree = new TreeMap();
            List<String> competitionIdList = new ArrayList<>();
            for (Competition comp : allowedCompetitions) {
                competitionIdList.add(comp.getId().toString());
            }

            String event = null, where = "";
            switch (player.getRuolo()) {
                case "P":
                    event = "SICS-_-ITD|SICS-_-PAR|SICS-_-USC|SICS-_-PIC#PIC-4|SICS-_-PCF";
                    break;
                case "D":
                    event = "SICS-_-RTF|SICS-_-TIF|SICS-_-ASS|SICS-_-PCF|SICS-_-PLF|SICS-_-ITD|SICS-_-DLL#DLL-0@DLL-1|SICS-_-REC|SICS-_-PIF#PIF-10";
                    break;
                case "C":
                    event = "SICS-_-RTF|SICS-_-TIF|SICS-_-ASS|SICS-_-PCF|SICS-_-DRF|SICS-_-TRF|SICS-_-PLF|SICS-_-ITD|SICS-_-REC|SICS-_-PIF#PIF-10";
                    break;
                case "A":
                    event = "SICS-_-RTF|SICS-_-TIF#TIF-15|SICS-_-ASS|SICS-_-PCF|SICS-_-TRF|SICS-_-DRF|SICS-_-PLF|SICS-_-PIF#PIF-10|SICS-_-ITD";
                    break;
            }

            if (event != null && !event.isEmpty()) {
                String[] splitted = event.split("\\|");
                where += " AND ((";
                for (int i = 0; i < splitted.length; i++) {
                    String fondFilter = "";
                    List<String> subEventList = new ArrayList<>();

                    String subEvent = splitted[i];
                    String[] splitconfig = subEvent.split("-_-");
                    // evt = TIF#TIF-1@TIF-2
                    String[] evt = splitconfig[1].split("#");
                    if (!splitconfig[1].contains("#")) {
                        if (splitconfig[1].contains("@")) {
                            evt = splitconfig[1].split("@");
                        }
                    }
                    if (evt.length == 2) {
                        subEventList.addAll(Arrays.asList(evt[1].split("@")));
                    }

                    String[] splitFond = evt[0].split("-");
                    for (String s : splitFond) {
                        if (!s.isEmpty()) {
                            fondFilter += (fondFilter.isEmpty() ? "'" : ",'") + s + "'";
                        }
                    }

                    if (i > 0) {
                        where += " OR (";
                    }
                    where += "event_type.code IN (" + fondFilter + ")";
                    boolean genTagFound = false;
                    String genTag = evt[0] + "-GEN";
                    if (!subEventList.isEmpty()) {
                        where += " AND ";
                        if (subEventList.contains(genTag)) {
                            genTagFound = true;

                            where += " ((tag_type.code IS NULL) ";
                            subEventList.remove(genTag);
                            if (!subEventList.isEmpty()) {
                                where += " OR ";
                            } else {
                                where += " ) ";
                            }
                        }

                        if (!subEventList.isEmpty()) {
                            where += " (tag_type.code IN ('" + StringUtils.join(subEventList, "','") + "')))";
                        }
                        if (genTagFound) {
                            where += ")";
                        }
                    } else {
                        where += ")";
                    }
                }
                where += ")";

                if (teamId != null && teamId > 0) {
                    where += " AND team_id = " + teamId;
                }

                if (competitionId != null && competitionId > 0) {
                    where += " AND competition_id = " + competitionId;
                }

                mapTree.put("where", where);
                mapTree.put("database", GlobalHelper.getDatabase(seasonIds));
                mapTree.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
                mapTree.put("playerId", playerId);
                mapTree.put("idGroup", groupsetId);
                mapTree.put("groupId", groupId);
                mapTree.put("seasonIds", seasonIds);
                mapTree.put("language", language);
                GlobalHelper.adjustQueryParams(mapTree);
                listEvents = getSqlSession().selectList("Azione.readPlayerBestEvents", mapTree);
                listEvents = loadButtonTags(listEvents);

                List<Azione> eventsToRemove = new ArrayList<>();
                for (Azione tmpEvent : listEvents) {
                    if (tmpEvent.getType() != null && (tmpEvent.getType().equals("ITD") || tmpEvent.getType().equals("DRF"))) {
                        if (tmpEvent.getmResult() == null || !tmpEvent.getmResult().equals("0")) { // 0 = POSITIVO
                            eventsToRemove.add(tmpEvent);
                        }
                    }
                }
                listEvents.removeAll(eventsToRemove);

                // Dalle azioni mi ricavo una mappa con chiave l'idFixture e valore il Game di riferimento della partita a cui fa riferimento l'azione
                Map<Long, Game> games = new HashMap<>();
                for (Object obj : listEvents) {
                    Azione action = (Azione) obj;
                    Game game = games.get(action.getIdFixture());
                    if (game == null) {
                        try {
                            games.put(action.getIdFixture(), null);
                        } catch (Exception ex) {

                        }
                    }
                }

                where = " where fixture.id IN (";
                for (Long idFixture : games.keySet()) {
                    where += (where.endsWith("(") ? "" : ",") + idFixture.toString();
                }
                where += ") GROUP BY fixture.id";

                /*
                    Mi carico i dati dei Game relativi alle azioni lette
                 */
                List<Game> gamesList = new ArrayList<>();
                if (!games.isEmpty()) {
                    TreeMap map3 = new TreeMap();
                    map3.put("where", where);
                    map3.put("language", language);
                    gamesList = getSqlSession().selectList("Game.getGameByListFixtureId", map3);
                }
                games = new HashMap<>();
                for (Game game : gamesList) {
                    games.put(game.getIdFixture(), game);
                }

                return addAdditionalInfoAction(listEvents, games, userId, groupsetId, language);
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public FilterGameWrapper readPlayerHighlights(Long playerId, Long teamId, Long competitionId, Long groupsetId, List<Competition> allowedCompetitions, Long userId, Long groupId, String seasonIds, String language) throws SQLException {
        List<Azione> listEvents;

        Atleta player = getAtletaById(seasonIds, playerId, teamId, groupsetId, false, language);
        if (player != null) {
            TreeMap mapTree = new TreeMap();
            List<String> competitionIdList = new ArrayList<>();
            for (Competition comp : allowedCompetitions) {
                competitionIdList.add(comp.getId().toString());
            }

            String event = null, where = "";
            switch (player.getRuolo()) {
                case "P":
                    event = "SICS-_-ITD|SICS-_-PAR|SICS-_-USC|SICS-_-PIC#PIC-4|SICS-_-PCF";
                    break;
                case "D":
                    event = "SICS-_-RTF|SICS-_-TIF|SICS-_-ASS|SICS-_-PCF|SICS-_-PLF|SICS-_-ITD|SICS-_-DLL#DLL-0@DLL-1|SICS-_-REC|SICS-_-PIF#PIF-10";
                    break;
                case "C":
                    event = "SICS-_-RTF|SICS-_-TIF|SICS-_-ASS|SICS-_-PCF|SICS-_-DRF|SICS-_-TRF|SICS-_-PLF|SICS-_-ITD|SICS-_-REC|SICS-_-PIF#PIF-10";
                    break;
                case "A":
                    event = "SICS-_-RTF|SICS-_-TIF#TIF-15|SICS-_-ASS|SICS-_-PCF|SICS-_-TRF|SICS-_-DRF|SICS-_-PLF|SICS-_-PIF#PIF-10|SICS-_-ITD";
                    break;
            }

            if (event != null && !event.isEmpty()) {
                String[] splitted = event.split("\\|");
                where += " AND ((";
                for (int i = 0; i < splitted.length; i++) {
                    String fondFilter = "";
                    List<String> subEventList = new ArrayList<>();

                    String subEvent = splitted[i];
                    String[] splitconfig = subEvent.split("-_-");
                    // evt = TIF#TIF-1@TIF-2
                    String[] evt = splitconfig[1].split("#");
                    if (!splitconfig[1].contains("#")) {
                        if (splitconfig[1].contains("@")) {
                            evt = splitconfig[1].split("@");
                        }
                    }
                    if (evt.length == 2) {
                        subEventList.addAll(Arrays.asList(evt[1].split("@")));
                    }

                    String[] splitFond = evt[0].split("-");
                    for (String s : splitFond) {
                        if (!s.isEmpty()) {
                            fondFilter += (fondFilter.isEmpty() ? "'" : ",'") + s + "'";
                        }
                    }

                    if (i > 0) {
                        where += " OR (";
                    }
                    where += "event_type.code IN (" + fondFilter + ")";
                    boolean genTagFound = false;
                    String genTag = evt[0] + "-GEN";
                    if (!subEventList.isEmpty()) {
                        where += " AND ";
                        if (subEventList.contains(genTag)) {
                            genTagFound = true;

                            where += " ((tag_type.code IS NULL) ";
                            subEventList.remove(genTag);
                            if (!subEventList.isEmpty()) {
                                where += " OR ";
                            } else {
                                where += " ) ";
                            }
                        }

                        if (!subEventList.isEmpty()) {
                            where += " (tag_type.code IN ('" + StringUtils.join(subEventList, "','") + "')))";
                        }
                        if (genTagFound) {
                            where += ")";
                        }
                    } else {
                        where += ")";
                    }
                }
                where += ")";

                /*String where = "";
                // esludo ATT, DIF, PIF, PIC, RTF, RTS
                where += " AND event.event_type_id NOT IN(1, 2, 3, 4, 25, 26)";*/

                if (teamId != null && teamId > 0) {
                    where += " AND team_id = " + teamId;
                }

                if (competitionId != null && competitionId > 0) {
                    where += " AND competition_id = " + competitionId;
                }

                mapTree.put("where", where);
                mapTree.put("database", GlobalHelper.getDatabase(seasonIds));
                mapTree.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
                mapTree.put("playerId", playerId);
                mapTree.put("idGroup", groupsetId);
                mapTree.put("groupId", groupId);
                mapTree.put("seasonIds", seasonIds);
                mapTree.put("language", language);
                GlobalHelper.adjustQueryParams(mapTree);
                listEvents = getSqlSession().selectList("Azione.readPlayerHighlights", mapTree);
                listEvents = loadButtonTags(listEvents);

                List<Azione> eventsToRemove = new ArrayList<>();
                for (Azione tmpEvent : listEvents) {
                    if (tmpEvent.getType() != null && (tmpEvent.getType().equals("ITD") || tmpEvent.getType().equals("DRF"))) {
                        if (tmpEvent.getmResult() == null || !tmpEvent.getmResult().equals("0")) { // 0 = POSITIVO
                            eventsToRemove.add(tmpEvent);
                        }
                    }
                }
                listEvents.removeAll(eventsToRemove);

                // Dalle azioni mi ricavo una mappa con chiave l'idFixture e valore il Game di riferimento della partita a cui fa riferimento l'azione
                Map<Long, Game> games = new HashMap<>();
                for (Object obj : listEvents) {
                    Azione action = (Azione) obj;
                    Game game = games.get(action.getIdFixture());
                    if (game == null) {
                        try {
                            games.put(action.getIdFixture(), null);
                        } catch (Exception ex) {

                        }
                    }
                }

                where = " where fixture.id IN (";
                for (Long idFixture : games.keySet()) {
                    where += (where.endsWith("(") ? "" : ",") + idFixture.toString();
                }
                where += ") GROUP BY fixture.id";

            /*
                    Mi carico i dati dei Game relativi alle azioni lette
             */
                List<Game> gamesList = new ArrayList<>();
                if (!games.isEmpty()) {
                    TreeMap map3 = new TreeMap();
                    map3.put("where", where);
                    map3.put("language", language);
                    gamesList = getSqlSession().selectList("Game.getGameByListFixtureId", map3);
                }
                games = new HashMap<>();
                for (Game game : gamesList) {
                    games.put(game.getIdFixture(), game);
                }

                return addAdditionalInfoAction(listEvents, games, userId, groupsetId, language);
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public Map<String, List> getPanel(String idPanels, Long idGroupset, String lang) {

        TreeMap mapPanels = new TreeMap();
        mapPanels.put("idPanels", idPanels);
        mapPanels.put("idGroup", idGroupset);
        List<Button> listButtons = getSqlSession().selectList("Button.getPanel", mapPanels);
        //List listButtons = getSqlSession().selectList("Button.getPanel", idPanels);
        Map<String, List> map = new HashMap<>();
        List<String> codeList = new ArrayList<>();
        List<String> idList = new ArrayList<>();
        for (Button currentButton : listButtons) {
            if (!codeList.contains("'" + currentButton.getCod() + "'")) { // no duplicati
                codeList.add("'" + currentButton.getCod() + "'");
            }
            if (!idList.contains(currentButton.getConfigId())) { // no duplicati
                idList.add(currentButton.getConfigId());
            }

//            TreeMap mapTree = new TreeMap();
//            mapTree.put("id", currentButton.getConfigId());
//            mapTree.put("code", "'" + currentButton.getCod() + "'");
//            currentButton.setTag(getSqlSession().selectList("Button.getPanelTags", mapTree));
        }

        // tags management
        TreeMap mapTree = new TreeMap();
        mapTree.put("id", StringUtils.join(idList, ","));
        mapTree.put("code", StringUtils.join(codeList, ","));
        List<Tags> tagList = getSqlSession().selectList("Button.getPanelTags", mapTree);
        for (Button currentButton : listButtons) {
            if (!currentButton.getConfigId().equals("1") || !EVENT_TYPE_NOT_ALLOWED.contains(currentButton.getCod()) || EVENT_TYPE_ALLOWED_FOR_VIDEO.contains(currentButton.getCod())) {
                for (Tags tags : tagList) {
                    if (Long.compare(tags.getPanelId(), Long.parseLong(currentButton.getConfigId())) == 0) {
                        if (StringUtils.equalsIgnoreCase(tags.getEventTypeCode(), currentButton.getCod())) {
                            currentButton.getTag().add(tags);
                        }
                    }
                }

                if (Long.compare(Long.parseLong(currentButton.getConfigId()), 1L) == 0) {
                    if (currentButton.getTag() == null) {
                        currentButton.setTag(new ArrayList<>());
                    }

                    Tags tag = new Tags(999, currentButton.getCod() + "-GEN", "Generico", "Generic", "Generic", "Generic", "Generic");
                    tag.setPanelId(Long.valueOf(currentButton.getConfigId()));
                    currentButton.getTag().add(tag);
                }

                String key = currentButton.getConfig().split("\\.")[0];
                List listaEventiPanel = map.get(key);
                if (listaEventiPanel == null) {
                    listaEventiPanel = new ArrayList<>();
                    map.put(key, listaEventiPanel);
                }

                EventDescription eventDescription = new EventDescription();
                eventDescription.setCode(currentButton.getCod());
                eventDescription.setDescription(currentButton.descTrim("it"));
                eventDescription.setDescriptionEn(currentButton.descTrim("en"));
                eventDescription.setDescriptionFr(currentButton.descTrim("fr"));
                eventDescription.setDescriptionEs(currentButton.descTrim("es"));
                eventDescription.setDescriptionRu(currentButton.descTrim("ru"));
                //inserisco i tag del panel nell'evento
                eventDescription.setTag(currentButton.getTag());
                listaEventiPanel.add(eventDescription);
            }
        }

        for (List lista : map.values()) {
            Collections.sort(lista, new DescriptionEventComparatorHelper(lang));
        }
        return map;
    }

    @Override
    public Map<String, List> getPanelByName(String name, Long idGroupset, String language) {
        TreeMap mapPanels = new TreeMap();
        mapPanels.put("name", name);
        mapPanels.put("idGroup", idGroupset);
        List listButtons = getSqlSession().selectList("Button.getPanelByName", mapPanels);

        Map<String, ArrayList<Button>> mapTemp = new HashMap<>();
        // mappa con chiave il nome della configurazione e valore la lista di bottoni
        for (Object obj : listButtons) {
            Button ButtonCorrente = ((Button) obj);

            String key = ButtonCorrente.getConfig().split("\\.")[0];
            List listaBottoni = mapTemp.get(key);
            if (listaBottoni == null) {

                mapTemp.put(key, new ArrayList<Button>());
            }
            mapTemp.get(key).add(ButtonCorrente);
        }

        Map<String, List> map = new HashMap<>();
        List listTags;
        for (Entry<String, ArrayList<Button>> entrySet : mapTemp.entrySet()) {
            ArrayList<Button> listbuttons = entrySet.getValue();
            if (!listbuttons.isEmpty()) {
                String codeList = "";
                for (Button btn : listbuttons) {
                    codeList += (codeList.isEmpty() ? "'" : ",'") + btn.getCod() + "'";
                }

                TreeMap mapTree = new TreeMap();
                mapTree.put("id", listbuttons.get(0).getConfigId());
                mapTree.put("code", codeList);
                // ottengo la lista di tutti i tag relativi ai codici dei bottoni della configurazione
                listTags = getSqlSession().selectList("Button.getPanelTags", mapTree);

                List listaEventiPanel = new ArrayList<>();
                map.put(entrySet.getKey(), listaEventiPanel);
                // scorro la lista dei bottoni e assegno a ciascuno i propri tag
                for (Button btn : listbuttons) {
                    for (Object tagObj : listTags) {
                        Tags t = (Tags) tagObj;
                        if (btn.getCod().equals(t.getCode().split("-")[0])) {
                            btn.getTag().add(t);
                        }
                    }
                    EventDescription nuovaDescrizione = new EventDescription();
                    nuovaDescrizione.setCode(btn.getCod());
                    nuovaDescrizione.setDescription(btn.descTrim("it"));
                    nuovaDescrizione.setDescriptionEn(btn.descTrim("en"));
                    nuovaDescrizione.setDescriptionFr(btn.descTrim("fr"));
                    nuovaDescrizione.setDescriptionEs(btn.descTrim("es"));
                    nuovaDescrizione.setDescriptionRu(btn.descTrim("ru"));
                    //inserisco i tag del panel nell'evento
                    nuovaDescrizione.setTag(btn.getTag());
                    listaEventiPanel.add(nuovaDescrizione);
                }

            }

        }

        for (List lista : map.values()) {
            Collections.sort(lista, new DescriptionEventComparatorHelper(language));

        }
        return map;
    }

    @Override
    public Map<String, List> getPanelById(Long id, String language) {
        TreeMap mapPanels = new TreeMap();
        mapPanels.put("id", id);
        List listButtons = getSqlSession().selectList("Button.getPanelById", mapPanels);

        Map<String, ArrayList<Button>> mapTemp = new HashMap<>();
        // mappa con chiave il nome della configurazione e valore la lista di bottoni
        for (Object obj : listButtons) {
            Button ButtonCorrente = ((Button) obj);

            String key = ButtonCorrente.getConfig().split("\\.")[0];
            List listaBottoni = mapTemp.get(key);
            if (listaBottoni == null) {

                mapTemp.put(key, new ArrayList<Button>());
            }
            mapTemp.get(key).add(ButtonCorrente);
        }

        Map<String, List> map = new HashMap<>();
        //List listTags;
        for (Entry<String, ArrayList<Button>> entrySet : mapTemp.entrySet()) {
            ArrayList<Button> listbuttons = entrySet.getValue();
            if (!listbuttons.isEmpty()) {
                String codeList = "";
                for (Button btn : listbuttons) {
                    codeList += (codeList.isEmpty() ? "'" : ",'") + btn.getCod() + "'";
                }

                /*TreeMap mapTree = new TreeMap();
                mapTree.put("id", listbuttons.get(0).getConfigId());
                mapTree.put("code", codeList);
                // ottengo la lista di tutti i tag relativi ai codici dei bottoni della configurazione
                listTags = getSqlSession().selectList("Button.getPanelTags", mapTree);*/
                List listaEventiPanel = new ArrayList<>();
                map.put(entrySet.getKey(), listaEventiPanel);
                // scorro la lista dei bottoni e assegno a ciascuno i propri tag
                for (Button btn : listbuttons) {
                    /*for (Object tagObj : listTags) {
                        Tags t = (Tags) tagObj;
                        if (btn.getCod().equals(t.getCode().split("-")[0])) {
                            btn.getTag().add(t);
                        }
                    }*/
                    EventDescription nuovaDescrizione = new EventDescription();
                    nuovaDescrizione.setCode(btn.getCod());
                    nuovaDescrizione.setDescription(btn.descTrim("it"));
                    nuovaDescrizione.setDescriptionEn(btn.descTrim("en"));
                    nuovaDescrizione.setDescriptionFr(btn.descTrim("fr"));
                    nuovaDescrizione.setDescriptionEs(btn.descTrim("es"));
                    nuovaDescrizione.setDescriptionRu(btn.descTrim("ru"));
                    //inserisco i tag del panel nell'evento
                    nuovaDescrizione.setTag(btn.getTag());
                    listaEventiPanel.add(nuovaDescrizione);
                }

            }

        }

        if (id == 1L) { // se pannello SICS2015 aggiungo tasto "Generico"
            List<EventDescription> eventTypeToRemove = new ArrayList<>();
            for (List<EventDescription> eventDescriptionList : map.values()) {
                for (EventDescription eventDescription : eventDescriptionList) {
                    if (EVENT_TYPE_NOT_ALLOWED.contains(eventDescription.getCode())) {
                        eventTypeToRemove.add(eventDescription);
                    } else {
                        List<Tags> tagList = eventDescription.getTag();
                        if (tagList == null) {
                            tagList = new ArrayList<>();
                        }

                        Tags tag = new Tags(999, eventDescription.getCode() + "-GEN", "Generico", "Generic", "Generic", "Generic", "Generic");
                        tagList.add(tag);
                    }
                }
            }

            for (List<EventDescription> eventDescriptionList : map.values()) {
                eventDescriptionList.removeAll(eventTypeToRemove);
            }
        }

        for (List lista : map.values()) {
            Collections.sort(lista, new DescriptionEventComparatorHelper(language));

        }
        return map;
    }

//    @Override
//    public Map<String, List> getPanelByName(String name, Long idGroupset) {
//        TreeMap mapPanels = new TreeMap();
//        mapPanels.put("name", name);
//        mapPanels.put("idGroup", idGroupset);
//        List listButtons = getSqlSession().selectList("Button.getPanelByName", mapPanels);
//
//        Map<String, List> map = new HashMap<String, List>();
//        for (Object obj : listButtons) {
//            Button ButtonCorrente = ((Button) obj);
//            TreeMap mapTree = new TreeMap();
//            mapTree.put("id", ButtonCorrente.getConfigId());
//            mapTree.put("code","'" + ButtonCorrente.getCod() + "'");
//            ButtonCorrente.setTag(getSqlSession().selectList("Button.getPanelTags", mapTree));
//            String key = ButtonCorrente.getConfig().split("\\.")[0];
//            List listaEventiPanel = map.get(key);
//            if (listaEventiPanel == null) {
//                listaEventiPanel = new ArrayList<>();
//                map.put(key, listaEventiPanel);
//            }
//            EventDescription nuovaDescrizione = new EventDescription();
//            nuovaDescrizione.setId(ButtonCorrente.getCod());
//            nuovaDescrizione.setDescription(ButtonCorrente.getDesc());
//            //inserisco i tag del panel nell'evento
//            nuovaDescrizione.setTag(ButtonCorrente.getTag());
//            listaEventiPanel.add(nuovaDescrizione);
//        }
//        for (List lista : map.values()) {
//            Collections.sort(lista, new DescriptionEventComparatorHelper());
//
//        }
//        return map;
//    }
    @Override
    public Map<String, List> getPanelsByGroupset(Long idGroupset, String language) {
        List listButtons = getSqlSession().selectList("Button.getPanelsByGroupset", idGroupset);

        Map<String, ArrayList<Button>> mapTemp = new HashMap<>();
        // mappa con chiave il nome della configurazione e valore la lista di bottoni
        for (Object obj : listButtons) {
            Button ButtonCorrente = ((Button) obj);

            String key = ButtonCorrente.getConfig().split("\\.")[0];
            List listaBottoni = mapTemp.get(key);
            if (listaBottoni == null) {

                mapTemp.put(key, new ArrayList<Button>());
            }
            mapTemp.get(key).add(ButtonCorrente);
        }

        Map<String, List> map = new HashMap<>();
        List listTags;
        for (Entry<String, ArrayList<Button>> entrySet : mapTemp.entrySet()) {
            ArrayList<Button> listbuttons = entrySet.getValue();
            if (!listbuttons.isEmpty()) {
                String codeList = "";
                for (Button btn : listbuttons) {
                    codeList += (codeList.isEmpty() ? "'" : ",'") + btn.getCod() + "'";
                }

                TreeMap mapTree = new TreeMap();
                mapTree.put("id", listbuttons.get(0).getConfigId());
                mapTree.put("code", codeList);
                // ottengo la lista di tutti i tag relativi ai codici dei bottoni della configurazione
                listTags = getSqlSession().selectList("Button.getPanelTags", mapTree);

                List listaEventiPanel = new ArrayList<>();
                map.put(entrySet.getKey(), listaEventiPanel);
                // scorro la lista dei bottoni e assegno a ciascuno i propri tag
                for (Button btn : listbuttons) {
                    for (Object tagObj : listTags) {
                        Tags t = (Tags) tagObj;
                        if (btn.getCod().equals(t.getCode().split("-")[0])) {
                            btn.getTag().add(t);
                        }
                    }
                    EventDescription nuovaDescrizione = new EventDescription();
                    nuovaDescrizione.setCode(btn.getCod());
                    nuovaDescrizione.setDescription(btn.descTrim("it"));
                    nuovaDescrizione.setDescriptionEn(btn.descTrim("en"));
                    nuovaDescrizione.setDescriptionFr(btn.descTrim("fr"));
                    nuovaDescrizione.setDescriptionEs(btn.descTrim("es"));
                    nuovaDescrizione.setDescriptionRu(btn.descTrim("ru"));
                    //inserisco i tag del panel nell'evento
                    nuovaDescrizione.setTag(btn.getTag());
                    listaEventiPanel.add(nuovaDescrizione);
                }

            }

        }

        for (List lista : map.values()) {
            Collections.sort(lista, new DescriptionEventComparatorHelper(language));

        }
        return map;
    }

//    @Override
//    public Map<String, List> getPanelsByGroupset(Long idGroupset) {
//        List listButtons = getSqlSession().selectList("Button.getPanelsByGroupset", idGroupset);
//        Locale locale = LocaleContextHolder.getLocale();
//        Map<String, List> map = new HashMap<String, List>();
//        for (Object obj : listButtons) {
//            Button ButtonCorrente = ((Button) obj);
//            TreeMap mapTree = new TreeMap();
//            mapTree.put("id", ButtonCorrente.getConfigId());
//            mapTree.put("code", ButtonCorrente.getCod());
//            ButtonCorrente.setTag(getSqlSession().selectList("Button.getPanelTags", mapTree));
//            String key = ButtonCorrente.getConfig().split("\\.")[0];
//            List listaEventiPanel = map.get(key);
//            if (listaEventiPanel == null) {
//                listaEventiPanel = new ArrayList<>();
//                map.put(key, listaEventiPanel);
//            }
//            EventDescription nuovaDescrizione = new EventDescription();
//            nuovaDescrizione.setId(ButtonCorrente.getCod());
//            nuovaDescrizione.setDescription(ButtonCorrente.descTrim(locale.getLanguage()));
//            //inserisco i tag del panel nell'evento
//            nuovaDescrizione.setTag(ButtonCorrente.getTag());
//            listaEventiPanel.add(nuovaDescrizione);
//        }
//        for (List lista : map.values()) {
//            Collections.sort(lista, new DescriptionEventComparatorHelper());
//
//        }
//        return map;
//    }
    @Override
    public List getModuli() {
        return getSqlSession().selectList("Modulo.getModuli", null);
    }

    @Override
    public Pdata getScadenzaFromSerialNumber(String pattern) throws SQLException {
        List items = getSqlSession().selectList("Pdata.getScadenzaFromSerialNumber", pattern);
        return (!items.isEmpty() ? (Pdata) items.get(0) : null);
    }

    public List<Azione> loadButtonTags(List<Azione> listEvents) {
        return loadButtonTags(listEvents, null);
    }

    public List<Azione> loadButtonTags(List<Azione> listEvents, String event) {
        // gestione button -> evito di fare una query per ogni azione, gruppo ed eseguo una sola query
        if (!listEvents.isEmpty()) { // per skippare partite non taggate
            // prima di tutto faccio manualmente il "GROUP BY" per mettere i tag negli eventi
            Map<String, Azione> groupedListEvents = new LinkedHashMap<>();
            for (Object obj : listEvents) {
                Azione azione = (Azione) obj;

                Azione tmpAzione = groupedListEvents.get(azione.getmIdEvent());
                if (tmpAzione == null) {
                    if (azione.getTagTypeId() != null) {
                        if (azione.getmTags() == null) {
                            azione.setmTags(new ArrayList<Tags>());
                        }

                        Tags tag = new Tags(azione.getTagTypeId(), azione.getTagTypeCode(), azione.getTagNameIt(), azione.getTagNameEn(), azione.getTagNameFr(), azione.getTagNameEs(), azione.getTagNameRu());
                        azione.getmTags().add(tag);
                    }

                    if (azione.getPlayerId() > 0) {
                        azione.getPlayer().put(azione.getPlayerId(), null);
                    }

                    groupedListEvents.put(azione.getmIdEvent(), azione);
                } else {
                    if (azione.getTagTypeId() != null) {
                        Tags tag = new Tags(azione.getTagTypeId(), azione.getTagTypeCode(), azione.getTagNameIt(), azione.getTagNameEn(), azione.getTagNameFr(), azione.getTagNameEs(), azione.getTagNameRu());

                        // devo controllare che non ci sia già perchè nel caso in cui l'azione ha più giocatori verrebbero fuori duplicati
                        boolean canContinue = true;
                        for (Tags tmpTag : tmpAzione.getmTags()) {
                            if (Integer.compare(tmpTag.getId(), tag.getId()) == 0) {
                                canContinue = false;
                            }
                        }

                        if (canContinue) {
                            tmpAzione.getmTags().add(tag);
                        }
                    }

                    if (azione.getPlayerId() > 0) {
                        tmpAzione.getPlayer().put(azione.getPlayerId(), null);
                    }
                }
            }

            // APPLICO ORA FILTRO OR, AND (IL NOT VIENE FATTO IN QUERY)
            List<EventTagsFilter> tagFilters = new ArrayList<>();
            if (event != null && !event.isEmpty()) {
                String[] splitted = event.split("\\|");
                for (String splitted1 : splitted) {
                    EventTagsFilter tagFilter = new EventTagsFilter();
                    String subEvent = splitted1;
                    String[] splitconfig = subEvent.split("-_-");
                    // evt = TIF#TIF-1@TIF-2
                    String[] evt = splitconfig[1].split("#");
                    if (!splitconfig[1].contains("#")) {
                        if (splitconfig[1].contains("@")) {
                            evt = splitconfig[1].split("@");
                        }
                    }
                    tagFilter.setEventTypeCode(evt[0]);
                    if (evt.length == 2) {
                        for (String tmpSubEvent : evt[1].split("@")) {
                            // {0}PASS-1 OPPURE PASS-1
                            if (!tmpSubEvent.contains("{")) { // per tutti tranne che per le statistiche non ho la parte {X}
                                // metto quindi modalità 0, ovvero OR
                                tagFilter.getTagWithOr().add(tmpSubEvent);
                            } else { // se è presente allora metto la modalità giusta
                                String modality = StringUtils.substringBetween(tmpSubEvent, "{", "}");
                                String tmpSubEventReal = tmpSubEvent.replace("{" + modality + "}", "");
                                switch (modality) {
                                    case "0":
                                        tagFilter.getTagWithOr().add(tmpSubEventReal);
                                        break;
                                    case "1":
                                        tagFilter.getTagWithAnd().add(tmpSubEventReal);
                                        break;
                                    case "2":
                                        tagFilter.getTagWithNot().add(tmpSubEventReal);
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }

                        tagFilters.add(tagFilter);
                    }
                }
            }
            listEvents = new ArrayList<>(groupedListEvents.values());

            // SE DEVO APPLICO I FILTRI AND E NOT
            if (!tagFilters.isEmpty()) {
                boolean somethingToCheck = false;
                for (EventTagsFilter tagFilter : tagFilters) {
                    if (!tagFilter.getTagWithAnd().isEmpty()) {
                        somethingToCheck = true;
                        break;
                    } else if (!tagFilter.getTagWithNot().isEmpty()) {
                        somethingToCheck = true;
                        break;
                    }
                }

                if (somethingToCheck) {
                    List<Object> eventToRemove = new ArrayList<>();
                    for (Object obj : listEvents) {
                        Azione azione = (Azione) obj;

                        for (EventTagsFilter tagFilter : tagFilters) {
                            if (eventToRemove.contains(obj)) {
                                break;
                            }

                            if (azione.getmType().equals(tagFilter.getEventTypeCode())) { // se stesso event_type
                                // CONTROLLO PER NOT E AND
                                List<String> tmpTagWithAnd = new ArrayList<>(tagFilter.getTagWithAnd());
                                for (Tags eventTag : azione.getmTags()) {
                                    if (tagFilter.getTagWithNot().contains(eventTag.getCode())) { // NOT
                                        eventToRemove.add(obj);
                                        break;
                                    } else if (tmpTagWithAnd.contains(eventTag.getCode())) { // AND
                                        tmpTagWithAnd.remove(eventTag.getCode());
                                    }
                                }

                                if (!tmpTagWithAnd.isEmpty()) { // significa che non ci sono tutti quindi riga da togliere
                                    eventToRemove.add(obj);
                                }
                            }
                        }
                    }

                    listEvents.removeAll(eventToRemove);
                }
            }

            List<String> idEventTypeList = new ArrayList<>();
            for (Object obj : listEvents) {
                Azione azione = (Azione) obj;
                if (azione.getButton() == null) {
                    Button button = new Button();
                    button.setCod(azione.getmType());
                    button.setConfig(null);
                    button.setConfigId(null);
                    button.setDesc(azione.getDesc());
                    button.setEndesc(azione.getDescEn());
                    button.setFrdesc(azione.getDescFr());
                    button.setEsdesc(azione.getDescEs());
                    button.setRudesc(azione.getDescRu());
                    button.setTag(new ArrayList<>());
                    azione.setButton(button);
                }

                if (azione.getButton() != null && !idEventTypeList.contains("'" + azione.getIdType() + "'")) {
                    idEventTypeList.add("'" + azione.getIdType() + "'");
                }
            }

            TreeMap mapTree = new TreeMap();
            mapTree.put("id", StringUtils.join(idEventTypeList, ","));
            GlobalHelper.adjustQueryParams(mapTree);
            List<Tags> tagList = getSqlSession().selectList("Azione.selectButtonTagsGrouped", mapTree);
            for (Azione azione : listEvents) {
                if (azione.getButton() != null) { // safety
                    Button button = new Button(); // devo creare nuovo button altrimenti i tag vengono duplicati
                    button.setCod(azione.getButton().getCod());
                    button.setConfig(azione.getButton().getConfig());
                    button.setConfigId(azione.getButton().getConfigId());
                    button.setDesc(azione.getButton().getDesc());
                    button.setEndesc(azione.getButton().getEndesc());
                    button.setFrdesc(azione.getButton().getFrdesc());
                    button.setEsdesc(azione.getButton().getEsdesc());
                    button.setRudesc(azione.getButton().getRudesc());
                    button.setTag(new ArrayList<>());
                    azione.setButton(button);

                    for (Tags tag : tagList) {
                        if (tag.getEventTypeId() != null && azione.getIdType() == tag.getEventTypeId()) {
                            azione.getButton().getTag().add(tag);
                        }
                    }
                }
            }
        }

        return listEvents;
    }

    @Override
    public Long addPlaylistTv(PlaylistTV playlistTV) {
        TreeMap params = new TreeMap();
        params.put("userId", playlistTV.getUserId());
        params.put("groupsetId", playlistTV.getGroupsetId());
        params.put("name", playlistTV.getName());
        params.put("description", playlistTV.getDescription());
        params.put("date", playlistTV.getDate());
        Long id = (Long) getSqlSession().selectOne("PlaylistTV.addPlaylist", params);
        return id;
    }

    @Override
    public void addPlaylistTvEvents(String values) {
        TreeMap params = new TreeMap();
        params.put("values", values);
        getSqlSession().insert("PlaylistTV.addPlaylistEvent", params);
    }

    @Override
    public List<PlaylistTV> getPlaylistTvByUserId(Long userId, Long groupsetId) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        List<PlaylistTV> playlistList = getSqlSession().selectList("PlaylistTV.getFirstFivePlaylist", params);
        loadUserFromPlaylistTv(playlistList, null);
        return playlistList;
    }

    @Override
    public List<PlaylistTV> getPlaylistTvWithCounterByUserId(Long userId, Long groupsetId) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        List<PlaylistTV> playlistList = getSqlSession().selectList("PlaylistTV.getFirstFivePlaylistWithCounter", params);
        loadUserFromPlaylistTv(playlistList, null);
        return playlistList;
    }

    @Override
    public void deletePlaylistTvById(Long playlistId) {
        TreeMap params = new TreeMap();
        params.put("playlistId", playlistId);
        getSqlSession().delete("PlaylistTV.deletePlaylistById", params);
    }

    @Override
    public List<PlaylistTVEvent> getPlaylistTvEventsById(Long playlistId) {
        TreeMap params = new TreeMap();
        params.put("playlistId", playlistId);
        List<PlaylistTVEvent> eventList = getSqlSession().selectList("PlaylistTV.getPlaylistEventsById", params);

        long counter = 1;
        boolean atLeastOneUpdated = false;
        for (PlaylistTVEvent event : eventList) {
            if (event.getOrder() != null) {
                counter = event.getOrder() + 1;
            }
        }
        for (PlaylistTVEvent event : eventList) {
            if (event.getOrder() == null) {
                event.setOrder(counter);
                updatePlaylistTvEvent(event);
                counter++;
                atLeastOneUpdated = true;
            }
        }
        if (atLeastOneUpdated) { // devo ordinare di nuovo la lista per ordine del campo "order"
            Collections.sort(eventList, new Comparator<PlaylistTVEvent>() {
                @Override
                public int compare(PlaylistTVEvent o1, PlaylistTVEvent o2) {
                    return o1.getOrder().compareTo(o2.getOrder());
                }
            });
        }

        return eventList;
    }

    @Override
    public List<PlaylistTVEvent> getPlaylistTvEventsDeletedById(Long playlistId) {
        TreeMap params = new TreeMap();
        params.put("playlistId", playlistId);
        List<PlaylistTVEvent> eventList = getSqlSession().selectList("PlaylistTV.getPlaylistEventsDeletedById", params);

        long counter = 1;
        for (PlaylistTVEvent event : eventList) {
            if (event.getOrder() == null) {
                event.setOrder(counter);
                updatePlaylistTvEvent(event);
                counter++;
            } else {
                counter = event.getOrder() + 1;
            }
        }

        return eventList;
    }

    @Override
    public PlaylistTV getPlaylistTvById(Long playlistId) {
        TreeMap params = new TreeMap();
        params.put("playlistId", playlistId);
        List<PlaylistTV> items = getSqlSession().selectList("PlaylistTV.getPlaylistById", params);
        PlaylistTV item = (!items.isEmpty() ? items.get(0) : null);
        loadUserFromPlaylistTv(null, item);
        return item;
    }

    @Override
    public void updatePlaylistTv(PlaylistTV playlistTV) {
        TreeMap params = new TreeMap();
        params.put("id", playlistTV.getId());
        params.put("userId", playlistTV.getUserId());
        params.put("groupsetId", playlistTV.getGroupsetId());
        params.put("name", playlistTV.getName());
        params.put("description", playlistTV.getDescription());
        params.put("updateBy", playlistTV.getUpdateBy());
        params.put("updateDate", playlistTV.getUpdateDate());
        params.put("updateDescription", playlistTV.getUpdateDescription());
        getSqlSession().update("PlaylistTV.updatePlaylist", params);
    }

    @Override
    public void updatePlaylistShare(PlaylistTVShare playlistTVShare) {
        TreeMap params = new TreeMap();
        params.put("id", playlistTVShare.getId());
        params.put("userId", playlistTVShare.getUserId());
        params.put("playlistId", playlistTVShare.getPlaylistId());
        params.put("played", playlistTVShare.getPlayed());
        getSqlSession().update("PlaylistTV.updatePlaylistShare", params);
    }

    @Override
    public void updatePlaylistTvEvent(PlaylistTVEvent event) {
        // gestisco note, startMsec e endMSec
        TreeMap params = new TreeMap();
        params.put("playlistId", event.getPlaylistId());
        params.put("eventId", event.getEventId());
        params.put("note", event.getEvent_note());
        params.put("startMsec", event.getEvent_period_start_msec());
        params.put("endMsec", event.getEvent_period_end_msec());
        params.put("startSec", event.getEvent_period_start_second());
        params.put("endSec", event.getEvent_period_end_second());
        params.put("startMin", event.getEvent_period_start_minute());
        params.put("endMin", event.getEvent_period_end_minute());
        params.put("tactStartMsec", event.getEvent_tactical_start_msec());
        params.put("order", event.getOrder());
        params.put("deleted", event.getDeleted());
        params.put("updateBy", event.getUpdateBy());
        params.put("updateDate", event.getUpdateDate());
        params.put("updateDescription", event.getUpdateDescription());
        getSqlSession().update("PlaylistTV.updatePlaylistEvent", params);
    }

    @Override
    public void deletePlaylistTvEvents(Long playlistId, String eventIds, Long updateBy, Timestamp updateDate, String updateDescription) {
        TreeMap params = new TreeMap();
        params.put("playlistId", playlistId);
        params.put("eventIds", eventIds);
        params.put("updateBy", updateBy);
        params.put("updateDate", updateDate);
        params.put("updateDescription", updateDescription);
        getSqlSession().update("PlaylistTV.deletePlaylistEvents", params);
    }

    @Override
    public void permanentlyDeletePlaylistTvEvents(Long playlistId, String eventIds) {
        TreeMap params = new TreeMap();
        params.put("playlistId", playlistId);
        params.put("eventIds", eventIds);
        getSqlSession().update("PlaylistTV.permanentlyDeletePlaylistEvents", params);
    }

    public void loadUserFromPlaylistTv(List<PlaylistTV> playlistTvList, PlaylistTV singlePlaylist) {
        if (singlePlaylist != null) {
            try {
                User user = getUser(singlePlaylist.getUserId());
                singlePlaylist.setUser(user);
            } catch (SQLException ex) {
                Logger.getLogger(AppDaoImpl.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else if (playlistTvList != null && !playlistTvList.isEmpty()) {
            Map<Long, User> cache = new HashMap<>();

            for (PlaylistTV playlist : playlistTvList) {
                if (!cache.containsKey(playlist.getUserId())) {
                    try {
                        User user = getUser(playlist.getUserId());
                        cache.put(user.getId(), user);
                    } catch (SQLException ex) {
                        Logger.getLogger(AppDaoImpl.class.getName()).log(Level.SEVERE, null, ex);
                    }
                }
                playlist.setUser(cache.get(playlist.getUserId()));
            }
        }
    }

    @Override
    public Groupset getGroupsetById(Long groupsetId) {
        TreeMap params = new TreeMap();
        params.put("groupsetId", groupsetId);
        List<Groupset> items = getSqlSession().selectList("Groupset.getGroupsetById", params);
        Groupset item = (!items.isEmpty() ? items.get(0) : null);
        return item;
    }

    @Override
    public List<User> getUserListByGroupsetId(Long groupsetId) {
        TreeMap params = new TreeMap();
        String filter = " user.groupset_id = " + groupsetId + " AND NOW() < pdata.datascadenza ";
        params.put("filter", filter);
        return getSqlSession().selectList("User.getUserListByGroupsetId", params);
    }

    @Override
    public List<PlaylistTVShare> getAllPlaylistTVShareByUserId(Long userId, Long groupsetId) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("PlaylistTV.getAllSharedByUserId", params);
    }

    @Override
    public void addPlaylistTVShare(Long userId, Long playlistId, boolean editable) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("playlistId", playlistId);
        params.put("editable", editable ? 1 : 0);
        getSqlSession().insert("PlaylistTV.addPlaylistTVShare", params);
    }

    @Override
    public void deletePlaylistTVShare(Long playlistId) {
        TreeMap params = new TreeMap();
        params.put("playlistId", playlistId);
        getSqlSession().delete("PlaylistTV.deletePlaylistShare", params);
    }

    @Override
    public List<PlaylistTV> getSharedPlaylistTvByUserId(Long userId, Long groupsetId) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        List<PlaylistTV> playlistList = getSqlSession().selectList("PlaylistTV.getAllShareByUserId", params);
        loadUserFromPlaylistTv(playlistList, null);
        return playlistList;
    }

    @Override
    public List<PlaylistTV> getSharedPlaylistTvWithCounterByUserId(Long userId, Long groupsetId) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        List<PlaylistTV> playlistList = getSqlSession().selectList("PlaylistTV.getAllShareByUserIdWithCounter", params);
        loadUserFromPlaylistTv(playlistList, null);
        return playlistList;
    }

    @Override
    public List<PlaylistTV> getSharingPlaylistTvByUserId(Long userId, Long groupsetId) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        List<PlaylistTV> playlistList = getSqlSession().selectList("PlaylistTV.getAllSharingByUserId", params);
        loadUserFromPlaylistTv(playlistList, null);
        return playlistList;
    }

    @Override
    public PlaylistTVShare getSharedPlaylistTvByUserIdAndPlaylistId(Long userId, Long groupsetId, Long playlistId) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        params.put("playlistId", playlistId);
        List<PlaylistTVShare> playlistList = getSqlSession().selectList("PlaylistTV.getAllSharedByUserIdAndPlaylistId", params);
        PlaylistTVShare item = (!playlistList.isEmpty() ? playlistList.get(0) : null);
        return item;
    }

    @Override
    public List<PlaylistTVShare> getAllSharedPlaylistTvByUserId(Long userId, Long groupsetId) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        List<PlaylistTVShare> playlistList = getSqlSession().selectList("PlaylistTV.getAllSharedToUserId", params);
        return playlistList;
    }

    @Override
    public PlaylistTVSharePublic getPlaylistTvSharePublicByUrl(String url) {
        TreeMap params = new TreeMap();
        params.put("link", url);
        List<PlaylistTVSharePublic> playlistList = getSqlSession().selectList("PlaylistTV.getPublicLinkByUrl", params);
        PlaylistTVSharePublic item = (!playlistList.isEmpty() ? playlistList.get(0) : null);
        return item;
    }

    @Override
    public PlaylistTVSharePublic getPlaylistTvSharePublicByUserIdAndPlaylistId(Long userId, Long playlistId) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("playlistId", playlistId);
        List<PlaylistTVSharePublic> playlistList = getSqlSession().selectList("PlaylistTV.getPublicLinkSharedByUserIdAndPlaylistId", params);
        PlaylistTVSharePublic item = (!playlistList.isEmpty() ? playlistList.get(0) : null);
        return item;
    }

    @Override
    public void updatePlaylistTvSharePublicClick(Long id, Long click) {
        TreeMap params = new TreeMap();
        params.put("id", id);
        params.put("click", click);
        getSqlSession().update("PlaylistTV.updatePlaylistTvSharePublicClick", params);
    }

    @Override
    public List<PlaylistTVSharePublic> getAllPlaylistTvPublicShareByUserId(Long userId) {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        List<PlaylistTVSharePublic> playlistList = getSqlSession().selectList("PlaylistTV.getAllPublicShareByUserId", params);
        return playlistList;
    }

    @Override
    public void addPlaylistTvSharePublic(PlaylistTVSharePublic share) {
        TreeMap params = new TreeMap();
        params.put("playlistId", share.getPlaylistId());
        params.put("link", share.getLink());
        params.put("click", share.getClick());
        params.put("maxClick", share.getMaxClick());
        params.put("userId", share.getUserId());
        getSqlSession().insert("PlaylistTV.addPlaylistTVSharePublic", params);
    }

    @Override
    public void removePlaylistTvSharePublic(Long id) {
        TreeMap params = new TreeMap();
        params.put("id", id);
        getSqlSession().delete("PlaylistTV.deletePlaylistSharePublic", params);
    }

    @Override
    public Season getLastSeasonForCountryId(Long userId, Long groupsetId, Long countryId, String allowedCompetitionIds, List<UserCompetitionException> competitionExceptions) throws SQLException {
        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(fixture.season_id = CASE WHEN fixture.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE fixture.season_id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        map.put("groupsetId", groupsetId);
        map.put("userId", userId);
        map.put("countryId", countryId);
        map.put("competitionIds", allowedCompetitionIds);
        List items = getSqlSession().selectList("Season.lastSeasonForCountryId", map);
        return (items != null && !items.isEmpty()) ? (Season) items.get(0) : null;
    }

    @Override
    public Season getLastSeasonForCountryIdWithNationalTeams(Long userId, Long groupsetId, Long countryId, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) throws SQLException {
        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(fixture.season_id = CASE WHEN fixture.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE fixture.season_id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("groupsetId", groupsetId);
        map.put("userId", userId);
        map.put("countryId", countryId);
        List items = getSqlSession().selectList("Season.lastSeasonForCountryIdWithNationalTeams", map);
        return (items != null && !items.isEmpty()) ? (Season) items.get(0) : null;
    }

    @Override
    public Season getLastSeasonForInternationalCompetitionId(Long userId, Long groupsetId, Long intCompId, List<Competition> allowedCompetitions, List<UserCompetitionException> competitionExceptions) throws SQLException {
        TreeMap map = new TreeMap();
        if (competitionExceptions != null && !competitionExceptions.isEmpty()) {
            String competitionExceptionsFilter = " AND ";
            List<String> exceptionFilters = new ArrayList<>();
            for (UserCompetitionException exception : competitionExceptions) {
                exceptionFilters.add("(fixture.season_id = CASE WHEN fixture.competition_id = " + exception.getCompetitionId() + " THEN " + exception.getSeasonId() + " ELSE fixture.season_id END)");
            }
            competitionExceptionsFilter += StringUtils.join(exceptionFilters, " AND ");
            map.put("competitionException", competitionExceptionsFilter);
        }

        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("groupsetId", groupsetId);
        map.put("userId", userId);
        map.put("intCompId", intCompId);
        List items = getSqlSession().selectList("Season.lastSeasonForInternationalCompetitionId", map);
        return (items != null && !items.isEmpty()) ? (Season) items.get(0) : null;
    }

    @Override
    public PlaylistTVEvent getPlaylistTvLastUpdateEventById(Long playlistId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playlistId", playlistId);
        List<PlaylistTVEvent> eventList = getSqlSession().selectList("PlaylistTV.getLastUpdateEventById", params);
        return (eventList != null && !eventList.isEmpty()) ? eventList.get(0) : null;
    }

    @Override
    public List<Game> getGameIdsListByFilters(String from, String where) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("from", from);
        params.put("where", where);
        List<Game> result = getSqlSession().selectList("Game.getGameIdsByFilters", params);
        return result;
    }

    @Override
    public List<StatsType> getAllStatsType(String language, Integer availability, String specificLanguage) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        List<StatsType> result = getSqlSession().selectList("TeamStats.getAllStatsType", params);

        if (availability != null) {
            int availabilityToRemove = 0;
            if (availability == 1) {
                availabilityToRemove = 2;
            } else if (availability == 2) {
                availabilityToRemove = 1;
            }

            if (availabilityToRemove > 0) {
                List<StatsType> wrongStatsType = new ArrayList<>();
                for (StatsType type : result) {
                    if (type.getAvailability() != null && Integer.compare(type.getAvailability(), availabilityToRemove) == 0) {
                        wrongStatsType.add(type);
                    }
                }

                if (!wrongStatsType.isEmpty()) {
                    result.removeAll(wrongStatsType);
                }
            }
        }
        return result;
    }

    @Override
    public List<TeamStats> getTeamStatsByCompetitionId(Long competitionId, Long countryId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) throws SQLException {
        TreeMap params = new TreeMap();

        if (filterId != null) { // sembra essere molto più veloce a fare la cosa in 2 query diverse...
            List<TeamStatsFilterDetail> filterDetail = getTeamStatsFilterDetailsByFilterId(filterId);
            List<String> statsList = new ArrayList<>();
            for (TeamStatsFilterDetail detail : filterDetail) {
                statsList.add(detail.getStatsTypeId().toString());
            }
            if (!statsList.contains("1")) {
                statsList.add("1"); // Minuti Giocati obbligatori
            }

            params.put("filterStatsIds", StringUtils.join(statsList, ","));
        }
        params.put("competitionId", competitionId);
        params.put("countryId", countryId);
        // non uso più gameIds -> tolto join da game -> passiamo sempre per groupset = -1 quindi non serve verificare nulla
//        params.put("gameIds", gameIds);
        params.put("tableType", tableType);
        params.put("filterId", filterId);
        params.put("seasonIds", seasonIds);
        params.put("groupId", groupId);
        if (from != null && to != null) {
            params.put("from", DateHelper.toStringInputDate(from));
            params.put("to", DateHelper.toStringInputDate(to));
        }
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("isTabellino", isTabellino);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        List<TeamStats> result = getSqlSession().selectList("TeamStats.getAllTeamsStatsByCompetitionId", params);
        return result;
    }

    @Override
    public List<TeamStats> getPlayerStatsByCompetitionId(Long competitionId, Long countryId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) throws SQLException {
        TreeMap params = new TreeMap();

        List<String> statsList = new ArrayList<>();
        if (filterId != null) { // sembra essere molto più veloce a fare la cosa in 2 query diverse...
            List<TeamStatsFilterDetail> filterDetail = getTeamStatsFilterDetailsByFilterId(filterId);
            for (TeamStatsFilterDetail detail : filterDetail) {
                statsList.add(detail.getStatsTypeId().toString());
            }
            if (!statsList.contains("1")) {
                statsList.add("1"); // Minuti Giocati obbligatori
            }
            params.put("filterStatsIds", StringUtils.join(statsList, ","));
        }
        params.put("competitionId", competitionId);
        params.put("countryId", countryId);
        params.put("gameIds", gameIds);
        params.put("tableType", tableType);
        params.put("filterId", filterId);
        params.put("seasonIds", seasonIds);
        params.put("groupId", groupId);
        if (from != null && to != null) {
            params.put("from", DateHelper.toStringInputDate(from));
            params.put("to", DateHelper.toStringInputDate(to));
        }
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("isTabellino", isTabellino);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("TeamStats.getAllPlayerStatsByCompetitionId", params);

//        final List<TeamStats> result = new ArrayList<>();
//        if (statsList.size() >= 3) {
//            try {
//                final CountDownLatch latch = new CountDownLatch(3);
//                final TreeMap finalFirstParams = new TreeMap(params);
//                final TreeMap finalSecondParams = new TreeMap(params);
//                final TreeMap finalThirdParams = new TreeMap(params);
//                int half = statsList.size() / 3;
//                finalFirstParams.put("filterStatsIds", StringUtils.join(statsList.subList(0, half), ","));
//                finalSecondParams.put("filterStatsIds", StringUtils.join(statsList.subList(half, half * 2), ","));
//                finalThirdParams.put("filterStatsIds", StringUtils.join(statsList.subList((half * 2), statsList.size()), ","));
//
//                new Thread() {
//                    @Override
//                    public void run() {
//                        try {
//                            result.addAll(getSqlSession().selectList("TeamStats.getAllPlayerStatsByCompetitionId", finalFirstParams));
//                        } catch (Exception ex) {
//                            GlobalHelper.reportError(ex);
//                        } finally {
//                            latch.countDown();
//                        }
//                    }
//                }.start();
//                new Thread() {
//                    @Override
//                    public void run() {
//                        try {
//                            result.addAll(getSqlSession().selectList("TeamStats.getAllPlayerStatsByCompetitionId", finalSecondParams));
//                        } catch (Exception ex) {
//                            GlobalHelper.reportError(ex);
//                        } finally {
//                            latch.countDown();
//                        }
//                    }
//                }.start();
//                new Thread() {
//                    @Override
//                    public void run() {
//                        try {
//                            result.addAll(getSqlSession().selectList("TeamStats.getAllPlayerStatsByCompetitionId", finalThirdParams));
//                        } catch (Exception ex) {
//                            GlobalHelper.reportError(ex);
//                        } finally {
//                            latch.countDown();
//                        }
//                    }
//                }.start();
//                latch.await();
//            } catch (InterruptedException ex) {
//                GlobalHelper.reportError(ex);
//            }
//        } else if (!statsList.isEmpty()) {
//            params.put("filterStatsIds", StringUtils.join(statsList, ","));
//            result.addAll(getSqlSession().selectList("TeamStats.getAllPlayerStatsByCompetitionId", params));
//        } else {
//            result.addAll(getSqlSession().selectList("TeamStats.getAllPlayerStatsByCompetitionId", params));
//        }
//        return result;
    }

    @Override
    public List<TeamStats> getTeamStatsByCompetitionIdAndTeamId(Long competitionId, Long countryId, Long teamId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) throws SQLException {
        TreeMap params = new TreeMap();

        if (filterId != null) { // sembra essere molto più veloce a fare la cosa in 2 query diverse...
            List<TeamStatsFilterDetail> filterDetail = getTeamStatsFilterDetailsByFilterId(filterId);
            List<String> statsList = new ArrayList<>();
            for (TeamStatsFilterDetail detail : filterDetail) {
                statsList.add(detail.getStatsTypeId().toString());
            }
            if (!statsList.contains("1")) {
                statsList.add("1"); // Minuti Giocati obbligatori
            }

            params.put("filterStatsIds", StringUtils.join(statsList, ","));
        }
        params.put("competitionId", competitionId);
        params.put("countryId", countryId);
        params.put("teamId", teamId);
        params.put("gameIds", gameIds);
        params.put("tableType", tableType);
        params.put("filterId", filterId);
        params.put("seasonIds", seasonIds);
        params.put("groupId", groupId);
        if (from != null && to != null) {
            params.put("from", DateHelper.toStringInputDate(from));
            params.put("to", DateHelper.toStringInputDate(to));
        }
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("isTabellino", isTabellino);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        List<TeamStats> result = getSqlSession().selectList("TeamStats.getAllTeamsStatsByCompetitionIdAndTeamId", params);
        return result;
    }

    @Override
    public List<TeamStats> getTeamStatsByCompetitionIdAndTeamIdForPlayers(Long competitionId, Long countryId, Long teamId, String gameIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, String language, String specificLanguage, boolean isTabellino) throws SQLException {
        TreeMap params = new TreeMap();

        if (filterId != null) { // sembra essere molto più veloce a fare la cosa in 2 query diverse...
            List<TeamStatsFilterDetail> filterDetail = getTeamStatsFilterDetailsByFilterId(filterId);
            List<String> statsList = new ArrayList<>();
            for (TeamStatsFilterDetail detail : filterDetail) {
                statsList.add(detail.getStatsTypeId().toString());
            }
            if (!statsList.contains("1")) {
                statsList.add("1"); // Minuti Giocati obbligatori
            }
            params.put("filterStatsIds", StringUtils.join(statsList, ","));
        }
        params.put("competitionId", competitionId);
        params.put("countryId", countryId);
        params.put("teamId", teamId);
        params.put("gameIds", gameIds);
        params.put("tableType", tableType);
        params.put("filterId", filterId);
        params.put("seasonIds", seasonIds);
        params.put("groupId", groupId);
        if (from != null && to != null) {
            params.put("from", DateHelper.toStringInputDate(from));
            params.put("to", DateHelper.toStringInputDate(to));
        }
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("isTabellino", isTabellino);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        List<TeamStats> result = getSqlSession().selectList("TeamStats.getAllTeamsStatsByCompetitionIdAndTeamIdForPlayers", params);
        return result;
    }

    @Override
    public List<TeamStats> getPlayerStatsByPlayerIds(String playerIds, String tableType, Long filterId, String seasonIds, Long groupId, Date from, Date to, Boolean isGrouped, String language, String specificLanguage) throws SQLException {
        TreeMap params = new TreeMap();

        if (filterId != null) { // sembra essere molto più veloce a fare la cosa in 2 query diverse...
            List<TeamStatsFilterDetail> filterDetail = getTeamStatsFilterDetailsByFilterId(filterId);
            List<String> statsList = new ArrayList<>();
            for (TeamStatsFilterDetail detail : filterDetail) {
                statsList.add(detail.getStatsTypeId().toString());
            }
            if (!statsList.contains("1")) {
                statsList.add("1"); // Minuti Giocati obbligatori
            }
            params.put("filterStatsIds", StringUtils.join(statsList, ","));
        }
        params.put("playerIds", playerIds);
        params.put("tableType", tableType);
        params.put("filterId", filterId);
        params.put("seasonIds", seasonIds);
        params.put("groupId", groupId);
        if (from != null && to != null) {
            params.put("from", DateHelper.toStringInputDate(from));
            params.put("to", DateHelper.toStringInputDate(to));
        }
        params.put("isGrouped", BooleanUtils.isTrue(isGrouped));
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        List<TeamStats> result = getSqlSession().selectList("TeamStats.getAllTeamsStatsByPlayerIds", params);
        return result;
    }

    @Override
    public Long addTeamStatsFilter(TeamStatsFilter filter) throws SQLException {
        TreeMap params = new TreeMap();
        if (filter.getTableType().equalsIgnoreCase("teams")) {
            params.put("tableType", 0);
        } else if (filter.getTableType().equalsIgnoreCase("matches")) {
            params.put("tableType", 1);
        } else if (filter.getTableType().equalsIgnoreCase("players")) {
            params.put("tableType", 2);
        } else if (filter.getTableType().equalsIgnoreCase("competitionPlayers")) {
            params.put("tableType", 3);
        } else if (filter.getTableType().equalsIgnoreCase("playerOverview1")) {
            params.put("tableType", 41);
        } else if (filter.getTableType().equalsIgnoreCase("playerOverview2")) {
            params.put("tableType", 42);
        } else if (filter.getTableType().equalsIgnoreCase("playerOverview3")) {
            params.put("tableType", 43);
        } else if (filter.getTableType().equalsIgnoreCase("playerOverview4")) {
            params.put("tableType", 44);
        } else if (filter.getTableType().equalsIgnoreCase("teamOverview")) {
            params.put("tableType", 5);
        } else if (filter.getTableType().equalsIgnoreCase("watchlistPlayer")) {
            params.put("tableType", 6);
        } else if (filter.getTableType().equalsIgnoreCase("smartSearch")) {
            params.put("tableType", 7);
        }
        params.put("userId", filter.getUserId());
        params.put("groupsetId", filter.getGroupsetId());
        params.put("name", filter.getName());
        params.put("isPreferred", filter.getIsPreferred());
        Long id = (Long) getSqlSession().selectOne("TeamStats.addFilter", params);
        return id;
    }

    @Override
    public void addTeamStatsFilterDetails(List<TeamStatsFilterDetail> filterDetailList) throws SQLException {
        TreeMap params = new TreeMap();
        String values = "VALUES";
        for (TeamStatsFilterDetail detail : filterDetailList) {
            values += "(";
            values += detail.getStatsTypeFilterId() + ",";
            values += detail.getStatsTypeId() + ",";
            values += (detail.getColumnName() != null ? "'" + detail.getColumnName() + "'" : "NULL") + ",";
            values += detail.getOrder() + ",";
            values += (detail.getSort() != null ? detail.getSort() : "NULL");
            values += "),";
        }
        values = values.substring(0, values.length() - 1);

        params.put("values", values);
        getSqlSession().insert("TeamStats.addFilterDetails", params);
    }

    @Override
    public void deleteTeamStatsFilterDetails(Long statsTypeFilterId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("statsTypeFilterId", statsTypeFilterId);
        getSqlSession().insert("TeamStats.deleteFilterDetails", params);
    }

    @Override
    public List<TeamStatsFilter> getTeamStatsFilterByUserId(Long userId, Long groupsetId, String tableType, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        params.put("tableType", tableType);
        params.put("language", language);
        List<TeamStatsFilter> result = getSqlSession().selectList("TeamStats.getAllTeamStatsFilterByUserId", params);

        // per i profili ho 1 solo filtro quindi non carico i default di sics
        if (!StringUtils.contains(tableType, "Overview")) {
            TeamStatsFilter tmpFilter = new TeamStatsFilter();
            tmpFilter.setName("Generic");
            tmpFilter.setId(-1L);
            tmpFilter.setOrder(1);
            tmpFilter.setTableType(tableType);
            tmpFilter.setIsSics(true);
            tmpFilter.setIsPreferred(false);
            result.add(0, tmpFilter);

            tmpFilter = new TeamStatsFilter();
            tmpFilter.setName("Defensive");
            tmpFilter.setId(-2L);
            tmpFilter.setOrder(2);
            tmpFilter.setTableType(tableType);
            tmpFilter.setIsSics(true);
            tmpFilter.setIsPreferred(false);
            result.add(1, tmpFilter);

            tmpFilter = new TeamStatsFilter();
            tmpFilter.setName("Offensive");
            tmpFilter.setId(-3L);
            tmpFilter.setOrder(3);
            tmpFilter.setTableType(tableType);
            tmpFilter.setIsSics(true);
            tmpFilter.setIsPreferred(false);
            result.add(2, tmpFilter);

            tmpFilter = new TeamStatsFilter();
            tmpFilter.setName("Duels");
            tmpFilter.setId(-4L);
            tmpFilter.setOrder(4);
            tmpFilter.setTableType(tableType);
            tmpFilter.setIsSics(true);
            tmpFilter.setIsPreferred(false);
            result.add(3, tmpFilter);

            tmpFilter = new TeamStatsFilter();
            tmpFilter.setName("Passes");
            tmpFilter.setId(-5L);
            tmpFilter.setOrder(5);
            tmpFilter.setTableType(tableType);
            tmpFilter.setIsSics(true);
            tmpFilter.setIsPreferred(false);
            result.add(4, tmpFilter);

            tmpFilter = new TeamStatsFilter();
            tmpFilter.setName("Goalkeeper");
            tmpFilter.setId(-6L);
            tmpFilter.setOrder(6);
            tmpFilter.setTableType(tableType);
            tmpFilter.setIsSics(true);
            tmpFilter.setIsPreferred(false);
            result.add(5, tmpFilter);
        }
        return result;
    }

    @Override
    public List<TeamStatsFilterDetail> getTeamStatsFilterDetailsByFilterId(Long filterId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("filterId", filterId);
        List<TeamStatsFilterDetail> result = getSqlSession().selectList("TeamStats.getAllTeamStatsFilterDetailsByFilterId", params);

        if (Long.compare(filterId, -1L) == 0) {
            // generic
            List<TeamStatsFilterDetail> details = new ArrayList<>();
            details.add(new TeamStatsFilterDetail(-1L, 8L, "statsType8", 1));
            details.add(new TeamStatsFilterDetail(-1L, 44L, "statsType44", 2));
            details.add(new TeamStatsFilterDetail(-1L, 57L, "statsType57", 3));
            details.add(new TeamStatsFilterDetail(-1L, 58L, "statsType58", 4));
            details.add(new TeamStatsFilterDetail(-1L, 1L, "statsType1", 5));
            details.add(new TeamStatsFilterDetail(-1L, 36L, "statsType36", 6));
            details.add(new TeamStatsFilterDetail(-1L, 2L, "statsType2", 7));
            details.add(new TeamStatsFilterDetail(-1L, 63L, "statsType63", 8));
            details.add(new TeamStatsFilterDetail(-1L, 20L, "statsType20", 9));
            details.add(new TeamStatsFilterDetail(-1L, 48L, "statsType48", 10));
            return details;
        } else if (Long.compare(filterId, -2L) == 0) {
            List<TeamStatsFilterDetail> details = new ArrayList<>();
            details.add(new TeamStatsFilterDetail(-2L, 41L, "statsType41", 1));
            details.add(new TeamStatsFilterDetail(-2L, 43L, "statsType43", 2));
            details.add(new TeamStatsFilterDetail(-2L, 75L, "statsType75", 3));
            details.add(new TeamStatsFilterDetail(-2L, 73L, "statsType73", 4));
            details.add(new TeamStatsFilterDetail(-2L, 74L, "statsType74", 5));
            details.add(new TeamStatsFilterDetail(-2L, 46L, "statsType46", 6));
            details.add(new TeamStatsFilterDetail(-2L, 47L, "statsType47", 7));
            details.add(new TeamStatsFilterDetail(-2L, 100L, "statsType100", 8));
            return details;
        } else if (Long.compare(filterId, -3L) == 0) {
            List<TeamStatsFilterDetail> details = new ArrayList<>();
            details.add(new TeamStatsFilterDetail(-3L, 63L, "statsType63", 1));
            details.add(new TeamStatsFilterDetail(-3L, 45L, "statsType45", 2));
            details.add(new TeamStatsFilterDetail(-3L, 26L, "statsType26", 3));
            details.add(new TeamStatsFilterDetail(-3L, 33L, "statsType33", 4));
            details.add(new TeamStatsFilterDetail(-3L, 36L, "statsType36", 5));
            details.add(new TeamStatsFilterDetail(-3L, 30L, "statsType30", 6));
            details.add(new TeamStatsFilterDetail(-3L, 37L, "statsType37", 7));
            details.add(new TeamStatsFilterDetail(-3L, 2L, "statsType2", 8));
            details.add(new TeamStatsFilterDetail(-3L, 20L, "statsType20", 9));
            return details;
        } else if (Long.compare(filterId, -4L) == 0) {
            List<TeamStatsFilterDetail> details = new ArrayList<>();
            details.add(new TeamStatsFilterDetail(-4L, 48L, "statsType48", 1));
            details.add(new TeamStatsFilterDetail(-4L, 53L, "statsType53", 2));
            details.add(new TeamStatsFilterDetail(-4L, 55L, "statsType55", 3));
            details.add(new TeamStatsFilterDetail(-4L, 50L, "statsType50", 4));
            details.add(new TeamStatsFilterDetail(-4L, 44L, "statsType44", 5));
            details.add(new TeamStatsFilterDetail(-4L, 57L, "statsType57", 6));
            details.add(new TeamStatsFilterDetail(-4L, 58L, "statsType58", 7));
            return details;
        } else if (Long.compare(filterId, -5L) == 0) {
            List<TeamStatsFilterDetail> details = new ArrayList<>();
            details.add(new TeamStatsFilterDetail(-5L, 10L, "statsType10", 1));
            details.add(new TeamStatsFilterDetail(-5L, 8L, "statsType8", 2));
            details.add(new TeamStatsFilterDetail(-5L, 11L, "statsType11", 3));
            details.add(new TeamStatsFilterDetail(-5L, 16L, "statsType16", 4));
            details.add(new TeamStatsFilterDetail(-5L, 24L, "statsType24", 5));
            return details;
        } else if (Long.compare(filterId, -6L) == 0) {
            List<TeamStatsFilterDetail> details = new ArrayList<>();
            details.add(new TeamStatsFilterDetail(-6L, 85L, "statsType85", 1));
            details.add(new TeamStatsFilterDetail(-6L, 101L, "statsType101", 2));
            details.add(new TeamStatsFilterDetail(-6L, 84L, "statsType84", 3));
            details.add(new TeamStatsFilterDetail(-6L, 96L, "statsType96", 4));
            details.add(new TeamStatsFilterDetail(-6L, 97L, "statsType97", 5));
            details.add(new TeamStatsFilterDetail(-6L, 60L, "statsType60", 6));
            details.add(new TeamStatsFilterDetail(-6L, 59L, "statsType59", 7));
            return details;
        }
        return result;
    }

    @Override
    public TeamStatsFilter getTeamStatsFilterById(Long filterId, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("filterId", filterId);
        params.put("language", language);
        TeamStatsFilter result = (TeamStatsFilter) getSqlSession().selectOne("TeamStats.getTeamStatsFilterById", params);
        return result;
    }

    @Override
    public void updateTeamStatsFilter(TeamStatsFilter filter) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("id", filter.getId());
        params.put("name", filter.getName());
        getSqlSession().update("TeamStats.updateTeamStatsFilter", params);
    }

    @Override
    public void updateTeamStatsFilterPreferred(Long isPreferred, String where) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("isPreferred", isPreferred);
        params.put("where", where);
        getSqlSession().update("TeamStats.updateTeamStatsFilterPreferred", params);
    }

    @Override
    public void deleteTeamStatsFilter(TeamStatsFilter filter) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("id", filter.getId());
        getSqlSession().delete("TeamStats.deleteFilter", params);
    }

    @Override
    public List<TeamStats> getPlayerStatsByPlayerId(Long playerId, String competitions, String teams, String seasonIds, Long positionId, String statsTypeIds, Boolean averageCompetition, Long groupId, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("competitions", competitions);
        params.put("teams", teams);
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("seasonIds", seasonIds);
        params.put("averageCompetition", averageCompetition);
        if (statsTypeIds != null && !statsTypeIds.isEmpty() && BooleanUtils.isNotTrue(averageCompetition)) {
            params.put("positionId", null);
        } else {
            params.put("positionId", positionId);
        }
        params.put("statsTypeIds", statsTypeIds);
        params.put("groupId", groupId);

        if (StringUtils.isNotBlank(gameIds)) {
//            List<Long> fixtureIds = getFixturesByGameIds(gameIds);
            List<String> fixtureIds = new ArrayList<>(Arrays.asList(StringUtils.split(gameIds, ",")));
            if (!fixtureIds.isEmpty()) {
                params.put("fixtureIds", StringUtils.join(fixtureIds, ","));
            }
        }
        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));
        params.put("isTabellino", isTabellino);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("TeamStats.getPlayerStatsByPlayerId", params);
    }

    @Override
    public List<TeamStats> getPlayerStatsByPlayerIdGroupByFixture(Long playerId, String competitions, String teams, String seasonIds, Long positionId, String statsTypeIds, Long groupId, String language, String specificLanguage, String from, String to, boolean isTabellino) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("competitions", (competitions == null || competitions.isEmpty() ? null : competitions));
        params.put("teams", (teams == null || teams.isEmpty() ? null : teams));
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("seasonIds", seasonIds);
        if (statsTypeIds != null && !statsTypeIds.isEmpty()) {
            params.put("positionId", null);
        } else {
            params.put("positionId", positionId);
        }
        params.put("statsTypeIds", statsTypeIds);
        params.put("groupId", groupId);
        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));
        params.put("isTabellino", isTabellino);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("TeamStats.getPlayerStatsByPlayerIdGroupByFixture", params);
    }

    @Override
    public List<TeamStats> getPlayerStatsByFilters(String competitions, String seasonIds, Long positionId, String statsTypeIds, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("competitions", competitions);
        params.put("language", language);
        params.put("seasonIds", seasonIds);
        if (statsTypeIds != null && !statsTypeIds.isEmpty()) {
            params.put("positionId", null);
        } else {
            params.put("positionId", positionId);
        }
        params.put("statsTypeIds", statsTypeIds);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("TeamStats.getPlayerStatsByFilters", params);
    }

    @Override
    public List<TeamStats> getPlayerStatsByFiltersExcludedPlayer(Long playerId, String competitions, String seasonIds, Long positionId, String statsTypeIds, Boolean averageCompetition, Long groupId, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("competitions", competitions);
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("seasonIds", seasonIds);
        params.put("averageCompetition", averageCompetition);
        params.put("groupId", groupId);
        if (statsTypeIds != null && !statsTypeIds.isEmpty() && BooleanUtils.isNotFalse(averageCompetition)) {
            params.put("positionId", null);
        } else {
            params.put("positionId", positionId);
        }
        if (StringUtils.isNotBlank(gameIds)) {
//            List<Long> fixtureIds = getFixturesByGameIds(gameIds);
            List<String> fixtureIds = new ArrayList<>(Arrays.asList(StringUtils.split(gameIds, ",")));
            if (!fixtureIds.isEmpty()) {
                params.put("fixtureIds", StringUtils.join(fixtureIds, ","));
            }
        }

        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));
        params.put("isTabellino", isTabellino);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);

        final List<TeamStats> result = new ArrayList<>();
        if (statsTypeIds != null) {
            List<String> statsList = Arrays.asList(statsTypeIds.split(","));
            if (statsList.size() >= 3) {
                try {
                    final CountDownLatch latch = new CountDownLatch(2);
                    final TreeMap finalFirstParams = new TreeMap(params);
                    final TreeMap finalSecondParams = new TreeMap(params);
                    int half = statsList.size() / 2;
                    finalFirstParams.put("statsTypeIds", StringUtils.join(statsList.subList(0, half), ","));
                    finalSecondParams.put("statsTypeIds", StringUtils.join(statsList.subList(half, statsList.size()), ","));

                    new Thread() {
                        @Override
                        public void run() {
                            try {
                                result.addAll(getSqlSession().selectList("TeamStats.getPlayerStatsByFiltersExcludedPlayerId", finalFirstParams));
                            } catch (Exception ex) {
                                GlobalHelper.reportError(ex);
                            } finally {
                                latch.countDown();
                            }
                        }
                    }.start();
                    new Thread() {
                        @Override
                        public void run() {
                            try {
                                result.addAll(getSqlSession().selectList("TeamStats.getPlayerStatsByFiltersExcludedPlayerId", finalSecondParams));
                            } catch (Exception ex) {
                                GlobalHelper.reportError(ex);
                            } finally {
                                latch.countDown();
                            }
                        }
                    }.start();
                    latch.await();
                } catch (InterruptedException ex) {
                    GlobalHelper.reportError(ex);
                }
            } else {
                params.put("statsTypeIds", statsTypeIds);
                result.addAll(getSqlSession().selectList("TeamStats.getPlayerStatsByFiltersExcludedPlayerId", params));
            }
        } else {
            params.put("statsTypeIds", statsTypeIds);
            result.addAll(getSqlSession().selectList("TeamStats.getPlayerStatsByFiltersExcludedPlayerId", params));
        }

        return result;
    }

    @Override
    public List<TeamStats> getPlayerStatsResumeCareer(Long playerId, String language, String specificLanguage) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        return getSqlSession().selectList("TeamStats.getPlayerStatsResumeCareer", params);
    }

    @Override
    public List<Game> getModuleAmountByTeam(Long teamId, Long compId, String seasonIds, Long groupId, String from, String to) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("teamId", teamId);
        params.put("compId", compId);
        params.put("seasonIds", seasonIds);
        params.put("groupId", groupId);
        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("Game.getModuleByTeam", params);
    }

    @Override
    public List<TeamReportRanking> getTeamRankingBySeasonAndCompetition(String seasonIds, String competitions, Long groupId, String language, String from, String to) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("seasonIds", seasonIds);
        params.put("competitionId", competitions);
        params.put("groupId", groupId);
        params.put("language", language);
        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("TeamStats.getTeamRankingBySeasonAndCompetition", params);
    }

    @Override
    public List<TeamStats> getPlayerStatsByTeamAndCompetition(Long teamId, String seasonIds, String competitions, Long groupId, String language, String specificLanguage, String from, String to) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("teamId", teamId);
        params.put("seasonIds", seasonIds);
        params.put("competitionId", competitions);
        params.put("groupId", groupId);
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("TeamStats.getPlayerStatsByTeamAndCompetition", params);
    }

    @Override
    public List<TeamStats> getTeamStatsByCompetition(String seasonIds, String competitions, String statsTypeIdsForTables, Long groupId, Boolean groupedByCompetition, String language, String specificLanguage, String from, String to) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("seasonIds", seasonIds);
        params.put("competitionId", competitions);
        params.put("statsTypeIds", statsTypeIdsForTables);
        params.put("groupId", groupId);
        params.put("groupedByCompetition", groupedByCompetition);
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("TeamStats.getTeamStatsByCompetition", params);
    }

    @Override
    public List<TeamStats> getDefaultTeamStatsByCompetition(String seasonIds, String competitions, Long groupId, Boolean groupedByCompetition, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("defaultStats", "true");
        params.put("seasonIds", seasonIds);
        params.put("competitionId", competitions);
        params.put("groupId", groupId);
        params.put("groupedByCompetition", groupedByCompetition);
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("isTabellino", isTabellino);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);

        if (StringUtils.isNotBlank(gameIds)) {
//            List<Long> fixtureIds = getFixturesByGameIds(gameIds);
            List<String> fixtureIds = new ArrayList<>(Arrays.asList(StringUtils.split(gameIds, ",")));
            if (!fixtureIds.isEmpty()) {
                params.put("fixtureIds", StringUtils.join(fixtureIds, ","));
            }
        }
        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));

        return getSqlSession().selectList("TeamStats.getTeamStatsByCompetition", params);
    }

    @Override
    public List<TeamStats> getTeamStatsByCompetitionAndPosition(String seasonIds, String competitions, Long positionId, String statsTypeIds, Long groupId, Boolean groupedByCompetition, String gameIds, String language, String specificLanguage, String from, String to, boolean isTabellino) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("seasonIds", seasonIds);
        params.put("competitionId", competitions);
//        if (statsTypeIds != null && !statsTypeIds.isEmpty()) {
//            params.put("positionId", null);
//        } else {
//            params.put("positionId", positionId);
//        }
        params.put("positionId", positionId);
        params.put("statsTypeIds", statsTypeIds);
        params.put("groupId", groupId);
        params.put("groupedByCompetition", groupedByCompetition);
        params.put("language", language);
        params.put("specificLanguage", specificLanguage);
        params.put("isTabellino", isTabellino);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);

        if (StringUtils.isNotBlank(gameIds)) {
//            List<Long> fixtureIds = getFixturesByGameIds(gameIds);
            List<String> fixtureIds = new ArrayList<>(Arrays.asList(StringUtils.split(gameIds, ",")));
            if (!fixtureIds.isEmpty()) {
                params.put("fixtureIds", StringUtils.join(fixtureIds, ","));
            }
        }
        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));

        return getSqlSession().selectList("TeamStats.getTeamStatsByCompetition", params);
    }

    @Override
    public List<Atleta> getAtletiJerseyAndRoleByFilters(String seasonId, String teamId, String playerId, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("seasonId", seasonId);
        params.put("teamId", teamId);
        params.put("playerId", playerId);
        params.put("language", language);
        return getSqlSession().selectList("Atleta.getAtletiJerseyAndRoleByFilters", params);
    }

    @Override
    public List<TeamStats> getTeamPlayerAmountByCompetition(String seasonIds, String competitions, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("seasonIds", seasonIds);
        params.put("competitionId", competitions);
        params.put("language", language);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("TeamStats.getTeamPlayerAmountByCompetition", params);
    }

    @Override
    public TeamStats getCompetitionMinutesPlayed(String seasonIds, String competitions) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("seasonIds", seasonIds);
        params.put("competitionId", competitions);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        TeamStats result = (TeamStats) getSqlSession().selectOne("TeamStats.getCompetitionMinutesPlayed", params);
        return result;
    }

    @Override
    public Position getPositionById(Long positionId, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("positionId", positionId);
        params.put("language", language);
        Position result = (Position) getSqlSession().selectOne("Atleta.getPositionById", params);
        return result;
    }

    @Override
    public List<Position> getPositionByIds(String positionIds, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("positionIds", positionIds);
        params.put("language", language);
        return getSqlSession().selectList("Atleta.getPositionByIds", params);
    }

    @Override
    public Long getGameAmountByCompetitions(String competitionIds, Long groupId, String seasonIds, String from, String to, boolean isTabellino) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("competitionIds", competitionIds);
        params.put("groupId", groupId);
        params.put("seasonIds", seasonIds);
        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));
        params.put("isTabellino", isTabellino);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        return (Long) getSqlSession().selectOne("TeamStats.getGameAmountByCompetitions", params);
    }

    @Override
    public Team getLastTeamForPlayer(Long playerId, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerIds", playerId);
        params.put("language", language);
        return (Team) getSqlSession().selectOne("Team.getLastTeamForPlayers", params);
    }

    @Override
    public List<Team> getLastTeamForPlayers(String playerIds, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerIds", playerIds);
        params.put("language", language);
        return getSqlSession().selectList("Team.getLastTeamForPlayers", params);
    }

    @Override
    public Long getFixtureByGameId(Long gameId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("gameId", gameId);
        return (Long) getSqlSession().selectOne("Game.getFixtureByGameId", params);
    }

    @Override
    public List<Long> getFixturesByGameIds(String gameIds) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("gameIds", gameIds);
        return getSqlSession().selectList("Game.getFixturesByGameIds", params);
    }

    @Override
    public List<Watchlist> getWatchlistByUserId(Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Watchlist.getWatchlistByUserId", params);
    }

    @Override
    public Watchlist getWatchlistById(Long watchlistId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("watchlistId", watchlistId);
        return (Watchlist) getSqlSession().selectOne("Watchlist.getWatchlistById", params);
    }

    @Override
    public List<WatchlistDetail> getWatchlistDetailByUserId(Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Watchlist.getWatchlistDetailByUserId", params);
    }

    @Override
    public List<WatchlistDetail> getWatchlistDetailById(Long detailId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("watchlistId", detailId);
        return getSqlSession().selectList("Watchlist.getWatchlistDetailById", params);
    }

    @Override
    public Long addWatchlist(Watchlist watchlist) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", watchlist.getUserId());
        params.put("groupsetId", watchlist.getGroupsetId());
        params.put("name", watchlist.getName());
        params.put("type", 1);
        Long id = (Long) getSqlSession().selectOne("Watchlist.addWatchlist", params);
        return id;
    }

    @Override
    public void addWatchlistDetail(WatchlistDetail detail) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("watchlistId", detail.getWatchlistId());
        params.put("playerId", detail.getPlayerId());
        params.put("teamId", detail.getTeamId());
        getSqlSession().insert("Watchlist.addWatchlistDetail", params);
    }

    @Override
    public void removePlayerFromWatchlist(Long playerId, Long watchlistId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("watchlistId", watchlistId);
        getSqlSession().delete("Watchlist.removePlayerFromWatchlist", params);
    }

    @Override
    public void updateWatchlist(Watchlist watchlist) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("id", watchlist.getId());
        params.put("name", watchlist.getName());
        getSqlSession().update("Watchlist.updateWatchlist", params);
    }

    @Override
    public void deleteWatchlist(Watchlist watchlist) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("id", watchlist.getId());
        getSqlSession().delete("Watchlist.deleteWatchlist", params);
    }

    @Override
    public List<WatchlistShare> getAllWatchlistShareByUserId(Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Watchlist.getAllWatchlistShareByUserId", params);
    }

    @Override
    public void deleteWatchlistShare(Long watchlistId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("watchlistId", watchlistId);
        getSqlSession().delete("Watchlist.deleteWatchlistShare", params);
    }

    @Override
    public void addWatchlistShare(Long userId, Long watchlistId, boolean editable) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("watchlistId", watchlistId);
        params.put("editable", editable);
        getSqlSession().insert("Watchlist.addWatchlistShare", params);
    }

    @Override
    public List<Watchlist> getWatchlistSharedWithUserId(Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Watchlist.getWatchlistSharedWithUserId", params);
    }

    @Override
    public List<WatchlistDetail> getWatchlistDetailSharedWithUserId(Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Watchlist.getWatchlistDetailSharedWithUserId", params);
    }

    @Override
    public List<Favorites> getFavoritesByUserId(Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Favorites.getFavoritesByUserId", params);
    }

    @Override
    public void addFavorites(Favorites favorites) throws SQLException {
        getSqlSession().insert("Favorites.addFavorites", favorites);
    }

    @Override
    public void deleteFavorites(Long id) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("id", id);
        getSqlSession().delete("Favorites.deleteFavorites", params);
    }

    @Override
    public List<Shortcut> getShortcutsByUserId(Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Shortcut.getShortcutByUserId", params);
    }

    @Override
    public void addShortcut(Shortcut shortcut) throws SQLException {
        getSqlSession().insert("Shortcut.addShortcut", shortcut);
    }

    @Override
    public void deleteShortcut(Long id) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("id", id);
        getSqlSession().delete("Shortcut.deleteShortcut", params);
    }

    @Override
    public void deleteShortcuts(String ids) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("ids", ids);
        getSqlSession().delete("Shortcut.deleteShortcuts", params);
    }

    @Override
    public Settings getSettingsByUserId(Long userId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        return (Settings) getSqlSession().selectOne("Settings.getSettingsByUserId", params);
    }

    @Override
    public void addSettings(Settings settings) throws SQLException {
        if (StringUtils.isBlank(settings.getTvLanguage())) {
            // per sicurezza
            settings.setTvLanguage("en");
        }
        getSqlSession().insert("Settings.addSettings", settings);
    }

    @Override
    public void updateSettings(Settings settings) throws SQLException {
        if (StringUtils.isBlank(settings.getTvLanguage())) {
            // per sicurezza
            settings.setTvLanguage("en");
        }
        getSqlSession().update("Settings.updateSettings", settings);
    }

    @Override
    public List<Long> getUserIdFromVtigerAgentId(Long agentId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("agentId", agentId);
        return getSqlSession().selectList("Vtiger.getVtigerAgentClients", params);
    }

    @Override
    public Long addExport(Export export) throws SQLException {
        return (Long) getSqlSession().selectOne("Export.addExport", export);
    }

    @Override
    public void addExportDetail(String values) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("values", values);
        getSqlSession().insert("Export.addExportDetails", params);
    }

    @Override
    public void updateExport(Export export) throws SQLException {
        getSqlSession().update("Export.updateExport", export);
    }

    @Override
    public List<Export> getExportByUser(Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Export.getExportByUser", params);
    }

    @Override
    public List<ExportDetail> getExportDetailByUser(Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Export.getExportDetailByUser", params);
    }

    @Override
    public Long getPlayerInLineupAmount(Long playerId, Long competitionId, Long teamId, String seasonIds, Long groupsetId, List<Competition> allowedCompetitions) throws SQLException {
        TreeMap params = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        params.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        params.put("playerId", playerId);
        params.put("competitionId", competitionId);
        params.put("teamId", teamId);
        params.put("seasonIds", seasonIds);
        params.put("groupsetId", groupsetId);
        GlobalHelper.adjustQueryParams(params);
        return (Long) getSqlSession().selectOne("Atleta.getPlayerInLineupAmount", params);
    }

    @Override
    public Long getUserExportLimit(Long userId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        return (Long) getSqlSession().selectOne("Export.getUserLimit", params);
    }

    @Override
    public Long getUserExportTimeUsed(Long userId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        return (Long) getSqlSession().selectOne("Export.getUserTimeUsed", params);
    }

    @Override
    public List<Export> getExportByUserAndData(Long userId, Long groupsetId, String from, String to) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        params.put("from", from);
        params.put("to", to);
        return getSqlSession().selectList("Export.getExportByUserAndData", params);
    }

    @Override
    public List<ExportDetail> getExportDetailByUserAndData(Long userId, Long groupsetId, String from, String to) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        params.put("from", from);
        params.put("to", to);
        return getSqlSession().selectList("Export.getExportDetailByUserAndData", params);
    }

    @Override
    public List<Team> getCompetitionTeamGironi(Long competitionId, String seasonIds, Long groupId, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("competitionId", competitionId);
        params.put("seasonIds", seasonIds);
        params.put("groupId", groupId);
        params.put("language", language);
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("Team.getCompetitionTeamGironi", params);
    }

    @Override
    public List<Competition> getTeamGironi(Long competitionId, Long teamId, String seasonIds, List<Competition> allowedCompetitions, String language, Locale userLanguage) throws SQLException {
        TreeMap params = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        params.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        params.put("competitionId", (competitionId == null ? -1 : competitionId));
        params.put("teamId", (teamId == null ? -1 : teamId));
        params.put("seasonIds", seasonIds);
        params.put("language", language);
        GlobalHelper.adjustQueryParams(params);
        List<Competition> result = getSqlSession().selectList("Competition.getTeamGironi", params);
        for (Competition girone : result) {
            if (girone.getGroupId() != null) {
                if (Long.compare(girone.getGroupId(), 0) == 0) {
                    girone.setGroupName(StringUtils.upperCase(SpringApplicationContextHelper.getMessage("menu.user.girone.finali", userLanguage)));
                } else {
                    girone.setGroupName(StringUtils.upperCase(SpringApplicationContextHelper.getMessage("menu.user.girone", userLanguage) + " " + girone.getGroupName()));
                }
            }
        }
        return result;
    }

    @Override
    public List<Competition> getPlayerGironi(Long playerId, Long competitionId, Long teamId, String seasonIds, List<Competition> allowedCompetitions, String language, Locale userLanguage) throws SQLException {
        TreeMap params = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        params.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        params.put("playerId", playerId);
        params.put("competitionId", (competitionId == null ? -1 : competitionId));
        params.put("teamId", (teamId == null ? -1 : teamId));
        params.put("seasonIds", seasonIds);
        params.put("language", language);
        GlobalHelper.adjustQueryParams(params);
        List<Competition> result = getSqlSession().selectList("Competition.getPlayerGironi", params);
        for (Competition girone : result) {
            if (girone.getGroupId() != null) {
                if (Long.compare(girone.getGroupId(), 0) == 0) {
                    girone.setGroupName(StringUtils.upperCase(SpringApplicationContextHelper.getMessage("menu.user.girone.finali", userLanguage)));
                } else {
                    girone.setGroupName(StringUtils.upperCase(SpringApplicationContextHelper.getMessage("menu.user.girone", userLanguage) + " " + girone.getGroupName()));
                }
            }
        }
        return result;
    }

    @Override
    public List<VtigerAgent> getUserVtigerAgent() throws SQLException {
        return getSqlSession().selectList("Vtiger.getUserVtigerAgent");
    }

    @Override
    public List<UserCompetitionException> getCompetitionExceptionByGroupsetId(Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Groupset.getCompetitionExceptionByGroupsetId", params);
    }

    @Override
    public Team getGironeById(Long id, String language, Locale userLanguage) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("id", id);
        params.put("language", language);
        Team girone = (Team) getSqlSession().selectOne("Team.getGironeById", params);
        if (girone.getGroupId() != null) {
            if (Long.compare(girone.getGroupId(), 0) == 0) {
                girone.setGroupName(StringUtils.upperCase(SpringApplicationContextHelper.getMessage("menu.user.girone.finali", userLanguage)));
            } else {
                girone.setGroupName(girone.getGroupName());
            }
        }
        return girone;
    }

    @Override
    public List<TeamStats> getTeamGameAmountByCompetitionId(Long competitionId, Long countryId, String gameIds, String seasonIds, Long groupId, Date from, Date to, String language) throws SQLException {
        TreeMap params = new TreeMap();

        params.put("competitionId", competitionId);
        params.put("countryId", countryId);
        params.put("seasonIds", seasonIds);
        params.put("groupId", groupId);
        if (from != null && to != null) {
            params.put("from", DateHelper.toStringInputDate(from));
            params.put("to", DateHelper.toStringInputDate(to));
        }
        params.put("language", language);
        params.put("database", GlobalHelper.getDatabase(seasonIds));
        GlobalHelper.adjustQueryParams(params);
        List<TeamStats> result = getSqlSession().selectList("TeamStats.getTeamGameAmountByCompetitionId", params);
        return result;
    }

    @Override
    public List<FixtureDetails> getFixtureDetails(List<Long> fixtureIds) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("fixtureIds", StringUtils.join(fixtureIds, ","));
        List<FixtureDetails> result = getSqlSession().selectList("Game.getFixtureDetails", params);
        return result;
    }

    @Override
    public List<Azione> checkEventTactical(Long groupsetId, List<String> eventIds) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("groupsetId", groupsetId);
        params.put("eventIds", StringUtils.join(eventIds, ","));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("Azione.checkEventTactical", params);
    }

    @Override
    public List<Atleta> getPlayerModulePositionAmount(Long playerId, Long teamId, Long competitionId, List<Competition> allowedCompetitions, Long groupId, String seasonIds, String from, String to) throws SQLException {
        TreeMap params = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        params.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        params.put("playerId", playerId);
        params.put("teamId", (teamId != null && teamId != -1L ? teamId : null));
        params.put("competitionId", (competitionId != null && competitionId != -1L ? competitionId : null));
        params.put("groupId", groupId);
        params.put("seasonIds", seasonIds);
        params.put("from", StringUtils.defaultIfBlank(from, null));
        params.put("to", StringUtils.defaultIfBlank(to, null));
        GlobalHelper.adjustQueryParams(params);
        return getSqlSession().selectList("Atleta.getPlayerModulePositionAmount", params);
    }

    @Override
    public void addPlayerStatsSummary(List<TeamStats> playerSummary) throws SQLException {
        TreeMap params = new TreeMap();
        String values = "VALUES";
        for (TeamStats summary : playerSummary) {
            values += "(";
            values += summary.getPlayerId() + ",";
            values += summary.getTeamId() + ",";
            values += summary.getCompetitionId() + ",";
            values += summary.getSeasonId() + ",";
            values += summary.getStatsTypeId() + ",";
            values += summary.getEventAmount();
            values += "),";
        }
        values = values.substring(0, values.length() - 1);

        params.put("values", values);
        getSqlSession().insert("TeamStats.addPlayerStatsSummary", params);
    }

    @Override
    public void addPlayerStatsSummary(String values) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("values", values);
        getSqlSession().insert("TeamStats.addPlayerStatsSummary", params);
    }

    @Override
    public void deletePlayerStatsSummary(Long playerId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        getSqlSession().insert("TeamStats.deletePlayerStatsSummary", params);
    }

    @Override
    public List<TeamStats> getPlayerStatsForSummary(Long playerId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        return getSqlSession().selectList("TeamStats.getPlayerStatsForSummary", params);
    }

    @Override
    public List<Long> getValidPlayersForSummary() throws SQLException {
        return getSqlSession().selectList("TeamStats.getValidPlayersForSummary");
    }

    @Override
    public List<TeamStats> getSmartSearchPlayerData(Map<String, String> filters) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("seasonIds", filters.get("seasonIds"));
        params.put("database", GlobalHelper.getDatabase(filters.get("seasonIds")));
        GlobalHelper.adjustQueryParams(params);

        return getSqlSession().selectList("TeamStats.getSmartSearchPlayerData", params);
    }

    @Override
    public List<Cache> getSmartSearchPlayerBaseData(Long playerId, String seasonId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("seasonId", seasonId);

        return getSqlSession().selectList("Cache.getSmartSearchPlayerBaseData", params);
    }

    @Override
    public List<Cache> getSmartSearchTeamBaseData(Long teamId, String seasonId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("teamId", teamId);
        params.put("seasonId", seasonId);

        return getSqlSession().selectList("Cache.getSmartSearchTeamBaseData", params);
    }

    @Override
    public List<Cache> getStatisticsData(Long playerId, String seasonId, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("seasonId", seasonId);
        params.put("language", language);

        return getSqlSession().selectList("Cache.getStatisticsData", params);
    }

    @Override
    public Cache getSmartSearchPlayerLastTeamData(Long playerId, String seasonId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("seasonId", seasonId);

        Cache lastTeam = (Cache) getSqlSession().selectOne("Cache.getSmartSearchPlayerLastTeamData", params);
        if (lastTeam == null) {
            lastTeam = new Cache();
            lastTeam.setTeamName("N.A.");
        }

        return lastTeam;
    }

    @Override
    public List<Cache> getSmartSearchPlayerMatchData(Long playerId, String seasonId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("seasonId", seasonId);

        return getSqlSession().selectList("Cache.getSmartSearchPlayerMatchData", params);
    }

    @Override
    public List<Long> getSeasonAvailableForPlayer(Long playerId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);

        return getSqlSession().selectList("Cache.getSeasonAvailableForPlayer", params);
    }

    @Override
    public List<Long> getSeasonAvailableForTeam(Long teamId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("teamId", teamId);

        return getSqlSession().selectList("Cache.getSeasonAvailableForTeam", params);
    }

    @Override
    public List<Long> getAllValidPlayers() throws SQLException {
        return getSqlSession().selectList("Cache.getAllValidPlayers");
    }

    @Override
    public List<Long> getAllValidTeams() throws SQLException {
        return getSqlSession().selectList("Cache.getAllValidTeams");
    }

    @Override
    public List<StatsType> getStatsTypeFilters() throws SQLException {
        return getSqlSession().selectList("TeamStats.getStatsTypeFilters");
    }

    @Override
    public List<CacheLanguageItem> getCacheCountryLanguages() throws SQLException {
        return getSqlSession().selectList("Cache.getBaseCountry");
    }

    @Override
    public List<CacheLanguageItem> getCacheFootLanguages() throws SQLException {
        return getSqlSession().selectList("Cache.getBaseFoot");
    }

    @Override
    public List<CacheLanguageItem> getCacheStatsTypeLanguages() throws SQLException {
        return getSqlSession().selectList("Cache.getBaseStatsType");
    }

    @Override
    public List<CacheLanguageItem> getCacheCompetitionLanguages() throws SQLException {
        return getSqlSession().selectList("Cache.getBaseCompetition");
    }

    @Override
    public List<CacheLanguageItem> getCacheTeamLanguages() throws SQLException {
        return getSqlSession().selectList("Cache.getBaseTeam");
    }

    @Override
    public List<CacheLanguageItem> getCachePositionLanguages() throws SQLException {
        return getSqlSession().selectList("Cache.getBasePosition");
    }

    @Override
    public List<Competition> getTop5EuropeanCompetitions(String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("language", language);

        return getSqlSession().selectList("Competition.getTop5EuropeanCompetitions", params);
    }

    @Override
    public List<Competition> getTop5SouthAmericaCompetitions(String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("language", language);

        return getSqlSession().selectList("Competition.getTop5SouthAmericaCompetitions", params);
    }

    @Override
    public List<Country> getCountriesForUpload(String seasonIds, String language) throws SQLException {
        final TreeMap map = new TreeMap();
        map.put("seasonIds", seasonIds);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Country.getCountriesForUpload", map);
    }

    @Override
    public List<Competition> getCompetitionsForUpload(String seasonIds, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("seasonIds", seasonIds);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Competition.getCompetitionsForUpload", map);
    }

    @Override
    public List<Team> getTeamsForUpload(String seasonIds, String language) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("seasonIds", seasonIds);
        map.put("language", language);
        GlobalHelper.adjustQueryParams(map);
        return getSqlSession().selectList("Team.getTeamsForUpload", map);
    }

    @Override
    public List<Game> getFixturesUploadableByCompetition(Long competitionId, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("competitionId", competitionId);
        params.put("language", language);

        return getSqlSession().selectList("Game.getFixturesUploadableByCompetition", params);
    }

    @Override
    public void insertFixtureVideo(Map<String, Object> parameters) throws SQLException {
        TreeMap params = new TreeMap();
        String values = "VALUES(";
        Object element = parameters.get("competitionId");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("fixtureId");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("type");
        values += StringUtils.defaultIfEmpty((String) element, "2") + ","; // type
        element = parameters.get("link");
        values += (element != null && StringUtils.isNotBlank((String) element) ? "\"" + (String) element + "\"" : "NULL") + ",";
        element = parameters.get("firstTimeStart");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("firstTimeEnd");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("secondTimeStart");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("secondTimeEnd");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("thirdTimeStart");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("thirdTimeEnd");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("fourthTimeStart");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("fourthTimeEnd");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("fifthTimeStart");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("fifthTimeEnd");
        values += (element != null ? (Long) element : "NULL") + ",";
        values += "0,"; // download_status
        element = parameters.get("userId");
        values += (element != null ? (Long) element : "NULL") + ",";
        element = parameters.get("note");
        values += (element != null && StringUtils.isNotBlank((String) element) ? "\"" + (String) element + "\"" : "NULL") + ")";
        params.put("values", values);

        getSqlSession().insert("Game.insertFixtureVideo", params);
    }

    @Override
    public Long getPlayerIdBySerialNumber(String serialNumber) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("serialNumber", serialNumber);

        return (Long) getSqlSession().selectOne("User.getPlayerIdBySerialNumber", params);
    }

    @Override
    public void updateFixtureScore(Long fixtureId, Long homeScore, Long awayScore) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("fixtureId", fixtureId);
        params.put("homeScore", homeScore);
        params.put("awayScore", awayScore);

        getSqlSession().update("Game.updateFixtureScore", params);
    }

    @Override
    public void updateCompetitionLogo(Long competitionId, String logo) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("competitionId", competitionId);
        params.put("logo", logo);

        getSqlSession().update("Competition.updateCompetitionLogo", params);
    }

    @Override
    public void updateTeamLogo(Long teamId, String logo) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("teamId", teamId);
        params.put("logo", logo);

        getSqlSession().update("Team.updateTeamLogo", params);
    }

    @Override
    public void updatePlayerPhoto(Long playerId, String photo) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("photo", photo);

        getSqlSession().update("Atleta.updatePlayerPhoto", params);
    }

    @Override
    public void updatePlayerAgentPhoto(Long agentId, String photo) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("agentId", agentId);
        params.put("photo", photo);

        getSqlSession().update("PlayerAgent.updatePlayerAgentPhoto", params);
    }

    @Override
    public String getFixtureFileproject(Long fixtureId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("fixtureId", fixtureId);

        return (String) getSqlSession().selectOne("Game.getFixtureFileproject", params);
    }

    @Override
    public Long getGameIdByFixtureId(Long fixtureId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("fixtureId", fixtureId);

        return (Long) getSqlSession().selectOne("Game.getGameIdByFixtureId", params);
    }

    @Override
    public Long insertPersonalPlayer(Atleta player, Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("firstName", player.getFirst_name());
        params.put("lastName", player.getLast_name());
        params.put("knownName", StringUtils.isBlank(player.getBornDate()) ? "NULL" : "\"" + player.getKnown_name() + "\"");
        params.put("photo", StringUtils.isBlank(player.getPhoto()) ? "NULL" : "\"" + player.getPhoto() + "\"");
        params.put("bornDate", StringUtils.isBlank(player.getBornDate()) ? "NULL" : "\"" + player.getBornDate() + "\"");
        params.put("height", StringUtils.defaultIfEmpty(player.getHeight(), "NULL"));
        params.put("footId", player.getFootId() != null ? player.getFootId() : "NULL");
        params.put("positionId", player.getIdPosition() != null ? player.getIdPosition() : "NULL");
        params.put("ownerTeam", StringUtils.isBlank(player.getTeamName()) ? "NULL" : "\"" + player.getTeamName() + "\"");
        params.put("playerId", player.getId() != null ? player.getId() : "NULL");
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        params.put("shared", BooleanUtils.isTrue(player.getShared()) ? "1" : "0");

        return (Long) getSqlSession().selectOne("Atleta.insertPersonalPlayer", params);
    }

    @Override
    public void updatePersonalPlayer(Atleta player) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("firstName", player.getFirst_name());
        params.put("lastName", player.getLast_name());
        params.put("knownName", StringUtils.isBlank(player.getKnown_name()) ? "NULL" : "\"" + player.getKnown_name() + "\"");
        params.put("photo", StringUtils.isBlank(player.getPhoto()) ? "NULL" : "\"" + player.getPhoto() + "\"");
        params.put("bornDate", StringUtils.isBlank(player.getBornDate()) ? "NULL" : "\"" + player.getBornDate() + "\"");
        params.put("height", StringUtils.defaultIfEmpty(StringUtils.replaceIgnoreCase(player.getHeight(), " cm", ""), "NULL"));
        params.put("footId", player.getFootId() != null ? player.getFootId() : "NULL");
        params.put("positionId", player.getIdPosition() != null ? player.getIdPosition() : "NULL");
        params.put("ownerTeam", StringUtils.isBlank(player.getTeamName()) ? "NULL" : "\"" + player.getTeamName() + "\"");
        params.put("playerId", player.getId() != null ? player.getId() : "NULL");
        params.put("shared", BooleanUtils.isTrue(player.getShared()) ? "1" : "0");
        params.put("id", player.getPersonalId());

        getSqlSession().update("Atleta.updatePersonalPlayer", params);
    }

    @Override
    public void deletePersonalPlayer(Long id) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("id", id);

        getSqlSession().delete("Atleta.deletePersonalPlayer", params);
    }

    @Override
    public List<Atleta> getPlayerPersonalAll(Long userId, Long groupsetId, Boolean all) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        params.put("all", BooleanUtils.isTrue(all));

        return getSqlSession().selectList("Atleta.getPlayerPersonalAll", params);
    }

    @Override
    public Atleta getPlayerPersonal(Long playerId, Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);

        return (Atleta) getSqlSession().selectOne("Atleta.getPlayerPersonal", params);
    }

    @Override
    public List<Atleta> getPlayerPersonalByIds(String ids, Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("ids", ids);
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);

        return getSqlSession().selectList("Atleta.getPlayerPersonalByIds", params);
    }

    @Override
    public Atleta getPlayerPersonalByPlayerId(Long playerId, Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("playerId", playerId);
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);

        return (Atleta) getSqlSession().selectOne("Atleta.getPlayerPersonalByPlayerId", params);
    }

    @Override
    public ShadowTeam getShadowTeam(Long id) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("id", id);

        return (ShadowTeam) getSqlSession().selectOne("ShadowTeam.getShadowTeam", params);
    }

    @Override
    public List<ShadowTeam> getShadowTeams(Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);

        return getSqlSession().selectList("ShadowTeam.getShadowTeams", params);
    }

    @Override
    public List<ShadowTeamDetail> getShadowTeamDetails(Long shadowTeamId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("shadowTeamId", shadowTeamId);

        return getSqlSession().selectList("ShadowTeam.getShadowTeamDetails", params);
    }

    @Override
    public Long insertShadowTeam(ShadowTeam shadowTeam, Long userId, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("name", StringUtils.defaultIfEmpty(shadowTeam.getName(), "New Team Project"));
        params.put("module", shadowTeam.getModule());
        params.put("userId", userId);
        params.put("groupsetId", groupsetId);
        params.put("shared", BooleanUtils.isTrue(shadowTeam.getShared()) ? "1" : "0");

        return (Long) getSqlSession().selectOne("ShadowTeam.insertShadowTeam", params);
    }

    @Override
    public void updateShadowTeam(ShadowTeam shadowTeam) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("id", shadowTeam.getId());
        params.put("name", StringUtils.defaultIfEmpty(shadowTeam.getName(), "New Shadow Team"));
        params.put("module", shadowTeam.getModule());
        params.put("shared", BooleanUtils.isTrue(shadowTeam.getShared()) ? "1" : "0");

        getSqlSession().update("ShadowTeam.updateShadowTeam", params);
    }

    @Override
    public void insertShadowTeamDetails(List<ShadowTeamDetail> shadowTeamDetails) throws SQLException {
        TreeMap params = new TreeMap();

        List<String> values = new ArrayList<>();
        for (ShadowTeamDetail detail : shadowTeamDetails) {
            List<String> tmpValues = new ArrayList<>();
            tmpValues.add(detail.getShadowTeamId().toString());
            tmpValues.add(detail.getPlayerPersonalId() != null ? detail.getPlayerPersonalId().toString() : "NULL");
            tmpValues.add(detail.getModulePosition().toString());
            tmpValues.add(StringUtils.isNotBlank(detail.getModulePositionCoords()) ? "\"" + detail.getModulePositionCoords() + "\"" : "NULL");
            tmpValues.add(StringUtils.isNotBlank(detail.getModulePositionColor()) ? "\"" + detail.getModulePositionColor() + "\"" : "NULL");
            tmpValues.add(detail.getSort().toString());
            values.add("(" + StringUtils.join(tmpValues, ",") + ")");
        }
        params.put("values", StringUtils.join(values, ","));

        getSqlSession().insert("ShadowTeam.insertShadowTeamDetail", params);
    }

    @Override
    public void deleteShadowTeam(Long shadowTeamId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("shadowTeamId", shadowTeamId);

        getSqlSession().delete("ShadowTeam.deleteShadowTeam", params);
    }

    @Override
    public void deleteShadowTeamDetails(Long shadowTeamId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("shadowTeamId", shadowTeamId);

        getSqlSession().delete("ShadowTeam.deleteShadowTeamDetail", params);
    }

    @Override
    public Long insertPersonalEvent(Map<String, String> parameters) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("eventTypeId", parameters.get("eventTypeId"));
        params.put("fixtureId", parameters.get("fixtureId"));
        params.put("teamId", parameters.get("teamId"));
        params.put("periodId", parameters.get("periodId"));
        params.put("periodStartMinute", parameters.get("periodStartMinute"));
        params.put("periodStartSecond", parameters.get("periodStartSecond"));
        params.put("periodEndMinute", parameters.get("periodEndMinute"));
        params.put("periodEndSecond", parameters.get("periodEndSecond"));
        params.put("periodStartMsec", parameters.get("periodStartMsec"));
        params.put("periodEndMsec", parameters.get("periodEndMsec"));
        params.put("note", parameters.get("note"));
        params.put("xmlIdEvent", parameters.get("xmlIdEvent"));
        params.put("groupsetId", parameters.get("groupsetId"));
        params.put("author", parameters.get("author"));

        Long eventId = (Long) getSqlSession().selectOne("Azione.addPersonalEvent", params);

        params.clear();
        params.put("eventId", eventId);
        params.put("userId", parameters.get("userId"));
        getSqlSession().insert("Azione.addPersonalEventOwner", params);

        return eventId;
    }

    @Override
    public void insertEventPlayer(Long eventId, Long playerId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("eventId", eventId);
        params.put("playerId", playerId);

        getSqlSession().insert("Azione.addEventPlayer", params);
    }

    @Override
    public void updatePersonalEvent(Map<String, String> parameters) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("eventId", parameters.get("eventId"));
        params.put("teamId", parameters.get("teamId"));
        params.put("periodId", parameters.get("periodId"));
        params.put("periodStartMinute", parameters.get("periodStartMinute"));
        params.put("periodStartSecond", parameters.get("periodStartSecond"));
        params.put("periodEndMinute", parameters.get("periodEndMinute"));
        params.put("periodEndSecond", parameters.get("periodEndSecond"));
        params.put("periodStartMsec", parameters.get("periodStartMsec"));
        params.put("periodEndMsec", parameters.get("periodEndMsec"));
        params.put("note", parameters.get("note"));
        params.put("author", parameters.get("author"));

        getSqlSession().update("Azione.updatePersonalEvent", params);
    }

    @Override
    public void deletePersonalEvent(Long eventId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("eventId", eventId);

        getSqlSession().delete("Azione.deletePersonalEvent", params);
    }

    @Override
    public void deletePersonalEventPlayers(Long eventId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("eventId", eventId);

        getSqlSession().delete("Azione.deletePersonalEventPlayers", params);
    }

    @Override
    public List<Azione> getPersonalEvents(Long groupsetId, String seasonIds, List<Competition> allowedCompetitions, User curUser, String language) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("groupsetId", groupsetId);
        map.put("seasonIds", seasonIds);
        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("language", language);
        map.put("isGuest", BooleanUtils.isTrue(curUser.getGuest()));
        map.put("userId", curUser.getId());
        GlobalHelper.adjustQueryParams(map);

        List<Azione> events = getSqlSession().selectList("Azione.getPersonalEvents", map);
        loadButtonTags(events);

        List<Integer> playerIds = new ArrayList<>();
        for (Azione event : events) {
            if (event.getPlayerId() > 0) {
                if (!playerIds.contains(event.getPlayerId())) {
                    playerIds.add(event.getPlayerId());
                }
            }
        }

        if (!playerIds.isEmpty()) {
            List<Atleta> players = getAtletiByIds(StringUtils.join(playerIds, ","), language);
            if (players != null && !players.isEmpty()) {
                for (Azione event : events) {
                    for (Atleta player : players) {
                        if (event.getPlayerId() > 0 && event.getPlayerId() == player.getId()) {
                            event.setmPlayer(player.getKnown_name());
                        }
                    }
                }
            }
        }

        return events;
    }

    @Override
    public Integer getFixturePlayerJerseyNumber(Long fixtureId, Long playerId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("fixtureId", fixtureId);
        params.put("playerId", playerId);

        return (Integer) getSqlSession().selectOne("Game.getFixturePlayerJerseyNumber", params);
    }

    @Override
    public Integer getFullFixtureAmount(String competitionIds, String seasonIds, Long groupId, Date from, Date to) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("competitionIds", competitionIds);
        params.put("seasonIds", seasonIds);
        params.put("groupId", groupId);
        if (from != null && to != null) {
            params.put("from", DateHelper.toStringInputDate(from));
            params.put("to", DateHelper.toStringInputDate(to));
        }
        GlobalHelper.adjustQueryParams(params);

        return (Integer) getSqlSession().selectOne("TeamStats.getFullFixtureAmount", params);
    }

    @Override
    public void updateTeamColor(Long teamId, String color) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("teamId", teamId);
        params.put("color", color);

        getSqlSession().update("Team.updateTeamColor", params);
    }

    @Override
    public List<Atleta> getAtletiByAgentId(Long agentId, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("agentId", agentId);
        params.put("language", language);

        return getSqlSession().selectList("Atleta.getAtletiByAgentId", params);
    }

    @Override
    public List<PlayerAgent> getPlayerAgents(Integer sort, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("sort", sort);
        params.put("language", language);
        return getSqlSession().selectList("PlayerAgent.getPlayerAgents", params);
    }

    @Override
    public PlayerAgent getPlayerAgent(Long agentId, String language) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("agentId", agentId);
        params.put("language", language);

        return (PlayerAgent) getSqlSession().selectOne("PlayerAgent.getPlayerAgent", params);
    }

    @Override
    public void updatePlayerAgentDetails(Long agentId, String name, String street, String location, String phone, String email, String website, Long countryId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("agentId", agentId);
        params.put("name", name);
        params.put("street", street);
        params.put("location", location);
        params.put("phone", phone);
        params.put("email", email);
        params.put("website", website);
        params.put("countryId", countryId);

        getSqlSession().update("PlayerAgent.updatePlayerAgentDetails", params);
    }

    @Override
    public List<Tags> getGroupsetEventType(String panelName, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("panelName", panelName);
        params.put("groupsetId", groupsetId);
        GlobalHelper.adjustQueryParams(params);

        return getSqlSession().selectList("Azione.getGroupsetEventType", params);
    }

    @Override
    public List<Tags> getGroupsetTagType(String panelName, Long groupsetId) throws SQLException {
        TreeMap params = new TreeMap();
        params.put("panelName", panelName);
        params.put("groupsetId", groupsetId);
        GlobalHelper.adjustQueryParams(params);

        return getSqlSession().selectList("Azione.getGroupsetTagType", params);
    }

    @Override
    public List<String> getLastSeasonPair() throws SQLException {
        return getSqlSession().selectList("Season.getLastSeasonPair");
    }
}
