package sics.helper;

import com.amazonaws.AmazonClientException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.HttpMethod;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.cloudfront.CloudFrontUrlSigner;
import com.amazonaws.services.s3.*;
import com.amazonaws.services.s3.model.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ibm.icu.text.Transliterator;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.geom.AffineTransform;
import java.awt.geom.Ellipse2D;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.awt.image.ImagingOpException;
import java.awt.image.RenderedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.sql.SQLException;
import java.text.Normalizer;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import java.util.TreeMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.imageio.ImageIO;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.xmlrpc.XmlRpcClient;
import org.imgscalr.Scalr;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.input.SAXBuilder;
import org.jdom2.output.Format;
import org.jdom2.output.XMLOutputter;
import org.springframework.beans.BeansException;
import org.springframework.mobile.device.Device;
import org.springframework.mobile.device.DeviceUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.support.WebApplicationContextUtils;
import sics.model.Punto;
import sics.dao.service.ManagerService;
import sics.domain.Atleta;
import sics.domain.Competition;
import sics.domain.EventDescription;
import sics.domain.Fascia;
import sics.domain.Game;
import sics.domain.LogData;
import sics.domain.PlayerAgent;
import sics.domain.Playlist;
import sics.domain.Season;
import sics.domain.Tags;
import sics.domain.Team;
import sics.domain.TeamStats;
import sics.domain.User;
import sics.domain.UserAction;
import sics.domain.UserActions;
import sics.listener.SicsTvServletContextListener;
import sics.model.Azione;
import sics.service.UserService;
import sun.security.rsa.RSAPrivateKeyImpl;
import sun.security.util.DerInputStream;

public class GlobalHelper {

    public static final String kBeanResources = "messageSource";
    public static final String kBeanAppManager = "managerService";
    public static final String kBeanAppDao = "appDAO";
    public static final String kBeanAppDaoProtezione = "appDAOProtezione";
    public static final String kBeanUtente = "sicsTvUser";
    public static final String kBeanLanguage = "sicsTvLang";
    public static final String kBeanS3Client = "AmazonS3Client";
    public static final String kBeanS3ClientPlaylist = "AmazonS3ClientPlaylist";
    public static final String kBeanCountry = "sicsTvCountry";
    public static final String kLastRequestDate = "sicsTvLastReqestDate";
    public static final String kRequestsList = "sicsTvRequestsList";
    public static final String kActions = "sicsTvActions";
    public static final String kMaxSessionDuration = "sicsTvMaxSessionDuration";

    public static final String kRoleReferee = "ROLE_REF";
    public static final String kRoleAdmin = "ROLE_ADMIN";
    public static final Long kGroupsetAia = new Long("3");
    public static final Long kGroupsetSics = new Long("2");
    private static final String mPlaylistGamePageShare = "https://server.sics.it/sicstv/auth/video.htm";
    //Tipi di utente
    public static final Long kApplicationAia = new Long("0");
    public static final Long kApplicationSicsTv = new Long("1");

    public static final Long kProviderVzaar = new Long("0");
    public static final Long kProviderAruba = new Long("1");
    public static final Long kProviderS3 = new Long("2");

    public static final String kActionLoginCorrect = "LOGIN_CORRECT";
    public static final String kActionSessionUpdated = "SESSION_UPDATED";
    public static final String kActionSessionMaintained = "SESSION_MAINTAINED";
    public static final String kActionSessionExpired = "SESSION_EXPIRED";
    public static final String kActionSessionDestroyed = "SESSION_DESTROYED";
    public static final String kActionSessionInactivity = "SESSION_INACTIVITY";
    public static final String kActionLoginError = "LOGIN_ERROR";
    public static final String kActionVideoStreaming = "VIDEO_STREAM";
    public static final String kActionVideoDownload = "VIDEO_DOWNLOAD";
    public static final String kActionPlaylistDownload = "PLAYLIST_DOWNLOAD";
    public static final String kActionXmlJsonDownload = "XML_JSON_DOWNLOAD";
    public static final String kActionLogoutSession = "LOGOUT_SESSION";
    public static final String kActionLogoutTimeout = "LOGOUT_TIMEOUT";
    public static final String kActionLicenseExpired = "LICENSE_EXPIRED";
    public static final String kActionStartExport = "START_EXPORT";
    public static final String kActionEndExport = "END_EXPORT";
    public static final String kActionNewPlaylistTv = "NEW_PLAYLIST_TV";
    public static final String kActionNewWatchlist = "NEW_WATCHLIST";
    public static final String kActionNewPersonalEvent = "NEW_PERSONAL_EVENT";
    public static final String kActionFixtureUploadStart = "FIXTURE_UPLOAD_START";
    public static final String kActionDeviceNormal = "WEB";
    public static final String kActionDeviceMobile = "MOBILE";
    public static final String kActionDeviceTablet = "TABLET";
    private static final String kCloudFrontPublicUrl = "https://d12cq458jk3aa4.cloudfront.net/";
    // private static final String kCloudFrontUrl = "https://d2xqftweek72uv.cloudfront.net/";
    private static final String kCloudFrontUrl = "https://d12cq458jk3aa4.cloudfront.net/";
    private static final String kCloudFrontPlaylistUrl = "https://dze6v4lsfawt2.cloudfront.net/";

    public static ObjectMapper mapper = new ObjectMapper();

    private static final int latoLungoField = 105;
    private static final int latoCortoField = 68;
    private static final int moltCampo = 4;
    private static BufferedImage kImgCampo = null;

    public static AmazonS3 s3Client = null;
    public static AmazonS3 s3PlaylistClient = null;
    public static AmazonS3 s3PlaylistReport = null;
    public static AmazonS3 s3UploadClient = null;
    public static AmazonS3 s3LogoClient = null;

    public static String kBucketVideo = "it.sics.svs";
//    public static String kBucketVideo = "it.sics.test";
//    public static String kBucketVideo = "sicstv.s3-accelerate.amazonaws.com";

    public static String kBucketDigital = "it.sics.ds.data";
    public static String kBucketPlaylist = "it.sics.svs.playlist";
    public static String kBucketReport = "it.sics.svs.report";
    public static String kBucketTest = "it.sics.test";
    public static String kBucketUpload = "it.sics.upload";
    public static String kBucketUploadFast = "itsicsupload";
    public static final String kUrlServer = "https://server.sics.it/";
    public static XmlRpcClient serverMail = null;

    public static String kServletContextPathExport = "";
    public static String kServletContextPathBin = "";
    public static String kServletContextPathLoghi = "";

    private static String kServletExportPathCache = "";
    public static String kPathFFmpeg = "";
    private static final PrivateKey kPrivateKey = retrievePrivateKey();
    public static final java.util.ResourceBundle kResources = java.util.ResourceBundle.getBundle("pers/sicstv/ApplicationResources");

    //in base al ruolo passi i filtri da fare in modo da toglierli dalla pagina web visibile a tutti e quindi parametrizzo la popolazione del div dei filtri veloci
    public static final LinkedHashMap<String, String[]> kFastFilterTeamMap = new LinkedHashMap<String, String[]>();
    public static final LinkedHashMap<String, String[]> kFastFilterTeamAgainstMap = new LinkedHashMap<String, String[]>();
    public static final LinkedHashMap<String, String[]> kFastFilterGoalkeeperMap = new LinkedHashMap<String, String[]>();
    public static final LinkedHashMap<String, String[]> kFastFilterDefenderMap = new LinkedHashMap<String, String[]>();
    public static final LinkedHashMap<String, String[]> kFastFilterMidfielderMap = new LinkedHashMap<String, String[]>();
    public static final LinkedHashMap<String, String[]> kFastFilterForwarderMap = new LinkedHashMap<String, String[]>();
    public static final LinkedHashMap<String, String[]> kFastFilterPlayerBasketMap = new LinkedHashMap<String, String[]>();
    public static final LinkedHashMap<String, String[]> kFastFilterTeamBasketMap = new LinkedHashMap<String, String[]>();
    public static final LinkedHashMap<String, String[]> kFastFilterTeamBasketAgainstMap = new LinkedHashMap<String, String[]>();
    public static final ArrayList<String> kFastFilterHighlights = new ArrayList<>();

    //NUOVI PARAMETRI PER ESPORTAZIONE
    public static int MAX_CLIP_EXPORT = 250;
    public static long MIN_SINGLE_CLIP_LENGHT = 1000;//in ms --> 1 sec
    public static long MAX_SINGLE_CLIP_LENGHT = 5 * 60 * 1000;//in ms --> 5 min
    public static long MAX_TOTAL_LENGHT = 120 * 60 * 1000;//in ms --> 2h --> 120 min
    public static final boolean USE_LAMBDA = true;

    public static Integer kLatoLungoCampo = 105;
    public static Integer kLatoCortoCampo = 68;

    public static boolean useFrammentedDatabase = false;

    static {
        try {
            serverMail = new XmlRpcClient(kUrlServer + "vmrepo/mail.php");
        } catch (Exception e) {
            System.err.println("ERRORE MAIL");
        }
        kFastFilterHighlights.add("ATT#ATT-1@ATT-3@ATT-4");
        kFastFilterHighlights.add("PIF#PIF-8@PIF-9@PIF-7");
        kFastFilterHighlights.add("FAF#FAF-6");
        // inizializzazione mappa con i testi dei filtri rapidi
        kFastFilterTeamMap.put("ATT", new String[]{"filtri.team.azioniattacco", "true", "0"});
        kFastFilterTeamMap.put("RTF", new String[]{"filtri.team.reti", "true", "0"});
        kFastFilterTeamMap.put("PIF#PIF-0@PIF-1", new String[]{"filtri.team.punizioni", "true", "0"});
        kFastFilterTeamMap.put("PIF#PIF-2", new String[]{"filtri.team.punizionidirette", "true", "0"});
        kFastFilterTeamMap.put("PIF#PIF-3", new String[]{"filtri.team.corner", "true", "0"});
        kFastFilterTeamMap.put("TIF#TIF-10", new String[]{"filtri.team.rigori", "true", "0"});
        kFastFilterTeamMap.put("TIF", new String[]{"filtri.team.tiri", "true", "0"});
        kFastFilterTeamMap.put("PCF", new String[]{"filtri.team.passchiave", "true", "0"});
        kFastFilterTeamMap.put("PLF", new String[]{"filtri.team.pallelaterali", "true", "0"});
        kFastFilterTeamMap.put("PIF#PIF-5", new String[]{"filtri.team.rimesselaterali", "true", "0"});

        kFastFilterTeamAgainstMap.put("DIF", new String[]{"filtri.team.avversario.azioniattacco", "true", "0"});
        kFastFilterTeamAgainstMap.put("RTS", new String[]{"filtri.team.avversario.reti", "true", "0"});
        kFastFilterTeamAgainstMap.put("PIC#PIC-0@PIC-1", new String[]{"filtri.team.avversario.punizioni", "true", "0"});
        kFastFilterTeamAgainstMap.put("PIC#PIC-2", new String[]{"filtri.team.avversario.punizionidirette", "true", "0"});
        kFastFilterTeamAgainstMap.put("PIC#PIC-3", new String[]{"filtri.team.avversario.corner", "true", "0"});
        kFastFilterTeamAgainstMap.put("TIS#TIS-10", new String[]{"filtri.team.avversario.rigori", "true", "0"});
        kFastFilterTeamAgainstMap.put("TIS", new String[]{"filtri.team.avversario.tiri", "true", "0"});
        kFastFilterTeamAgainstMap.put("PCS", new String[]{"filtri.team.avversario.passchiave", "true", "0"});
        kFastFilterTeamAgainstMap.put("PLS", new String[]{"filtri.team.avversario.pallelaterali", "true", "0"});
        kFastFilterTeamAgainstMap.put("PIC#PIC-5", new String[]{"filtri.team.avversario.rimesselaterali", "true", "0"});

        kFastFilterGoalkeeperMap.put("RTS", new String[]{"filtri.player.retisubite", "true", "0"});
        kFastFilterGoalkeeperMap.put("ITD", new String[]{"filtri.player.interventodecisivo", "true", "0"});
        kFastFilterGoalkeeperMap.put("PAR", new String[]{"filtri.player.parate", "true", "0"});
        kFastFilterGoalkeeperMap.put("USC", new String[]{"filtri.player.uscite", "true", "0"});
        kFastFilterGoalkeeperMap.put("RIN", new String[]{"filtri.player.giocoportiere", "true", "0"});
        kFastFilterGoalkeeperMap.put("TIS#TIS-10", new String[]{"filtri.player.rigorisubiti", "true", "0"});
        kFastFilterGoalkeeperMap.put("TIS#TIS-15", new String[]{"filtri.player.tirisubiti.porta", "true", "0"});
        kFastFilterGoalkeeperMap.put("TIS", new String[]{"filtri.player.tirisubiti", "true", "0"});

        kFastFilterDefenderMap.put("ASS", new String[]{"filtri.player.assist", "true", "0"});
        kFastFilterDefenderMap.put("RTF", new String[]{"filtri.player.goal", "true", "0"});
        kFastFilterDefenderMap.put("PCF", new String[]{"filtri.player.passchiave", "true", "0"});
        kFastFilterDefenderMap.put("DLL-DLS#DLL-0@DLS-0", new String[]{"filtri.player.duellitackle", "true", "0"});
        kFastFilterDefenderMap.put("DLL-DLS#DLL-1@DLS-1", new String[]{"filtri.player.duelliaerei", "true", "0"});
        kFastFilterDefenderMap.put("DRS", new String[]{"filtri.player.dribblingsubiti", "true", "0"});
        kFastFilterDefenderMap.put("REC", new String[]{"filtri.player.recupera", "true", "0"});
        kFastFilterDefenderMap.put("FAF", new String[]{"filtri.player.falli", "true", "0"});
        kFastFilterDefenderMap.put("ITD", new String[]{"filtri.player.interventodecisivo", "true", "0"});
        kFastFilterDefenderMap.put("CRF", new String[]{"filtri.player.cross", "true", "0"});
        kFastFilterDefenderMap.put("GPR", new String[]{"filtri.player.golprocurato", "true", "0"});
        kFastFilterDefenderMap.put("PIF#PIF-0@PIF-1", new String[]{"filtri.player.punizioniindirette", "true", "0"});
        kFastFilterDefenderMap.put("PIF#PIF-2", new String[]{"filtri.player.punizionidirette", "true", "0"});
        kFastFilterDefenderMap.put("TIF#TIF-10", new String[]{"filtri.player.rigori", "true", "0"});

        kFastFilterMidfielderMap.put("ASS", new String[]{"filtri.player.assist", "true", "0"});
        kFastFilterMidfielderMap.put("RTF", new String[]{"filtri.player.goal", "true", "0"});
        kFastFilterMidfielderMap.put("PCF", new String[]{"filtri.player.passchiave", "true", "0"});
        kFastFilterMidfielderMap.put("TIF", new String[]{"filtri.player.tiri", "true", "0"});
        kFastFilterMidfielderMap.put("DRF", new String[]{"filtri.player.dribbling", "true", "0"});
        kFastFilterMidfielderMap.put("DRS", new String[]{"filtri.player.dribblingsubiti", "true", "0"});
        kFastFilterMidfielderMap.put("DLL-DLS#DLL-0@DLS-0", new String[]{"filtri.player.duellitackle", "true", "0"});
        kFastFilterMidfielderMap.put("DLL-DLS#DLL-1@DLS-1", new String[]{"filtri.player.duelliaerei", "true", "0"});
        kFastFilterMidfielderMap.put("PER", new String[]{"filtri.player.perde", "true", "0"});
        kFastFilterMidfielderMap.put("REC", new String[]{"filtri.player.recupera", "true", "0"});
        kFastFilterMidfielderMap.put("CRF", new String[]{"filtri.player.cross", "true", "0"});
        kFastFilterMidfielderMap.put("GPR", new String[]{"filtri.player.golprocurato", "true", "0"});
        kFastFilterMidfielderMap.put("PIF#PIF-0@PIF-1", new String[]{"filtri.player.punizioniindirette", "true", "0"});
        kFastFilterMidfielderMap.put("PIF#PIF-2", new String[]{"filtri.player.punizionidirette", "true", "0"});
        kFastFilterMidfielderMap.put("TIF#TIF-10", new String[]{"filtri.player.rigori", "true", "0"});

        kFastFilterForwarderMap.put("ASS", new String[]{"filtri.player.assist", "true", "0"});
        kFastFilterForwarderMap.put("RTF", new String[]{"filtri.player.goal", "true", "0"});
        kFastFilterForwarderMap.put("PCF", new String[]{"filtri.player.passchiave", "true", "0"});
        kFastFilterForwarderMap.put("GPR", new String[]{"filtri.player.golprocurato", "true", "0"});
        kFastFilterForwarderMap.put("TIF", new String[]{"filtri.player.tiri", "true", "0"});
        kFastFilterForwarderMap.put("DRF", new String[]{"filtri.player.dribbling", "true", "0"});
        kFastFilterForwarderMap.put("DLL-DLS#DLL-0@DLS-0", new String[]{"filtri.player.duellitackle", "true", "0"});
        kFastFilterForwarderMap.put("DLL-DLS#DLL-1@DLS-1", new String[]{"filtri.player.duelliaerei", "true", "0"});
        kFastFilterForwarderMap.put("FAS", new String[]{"filtri.player.fallisubiti", "true", "0"});
        kFastFilterForwarderMap.put("CRF", new String[]{"filtri.player.cross", "true", "0"});
        kFastFilterForwarderMap.put("PIF#PIF-0@PIF-1", new String[]{"filtri.player.punizioniindirette", "true", "0"});
        kFastFilterForwarderMap.put("PIF#PIF-2", new String[]{"filtri.player.punizionidirette", "true", "0"});
        kFastFilterForwarderMap.put("TIF#TIF-10", new String[]{"filtri.player.rigori", "true", "0"});

        kFastFilterPlayerBasketMap.put("ATT", new String[]{"filtri.player.basket.attacco", "true", "0"});
        kFastFilterPlayerBasketMap.put("DIF", new String[]{"filtri.player.basket.difesa", "true", "0"});
        kFastFilterPlayerBasketMap.put("RIM", new String[]{"filtri.player.basket.rimessa", "true", "0"});
        kFastFilterPlayerBasketMap.put("TF", new String[]{"filtri.player.basket.tiro", "true", "0"});
        kFastFilterPlayerBasketMap.put("TLB", new String[]{"filtri.player.basket.tirolibero", "true", "0"});
        kFastFilterPlayerBasketMap.put("REB", new String[]{"filtri.player.basket.rimbalzo", "true", "0"});
        kFastFilterPlayerBasketMap.put("PP", new String[]{"filtri.player.basket.pallapersa", "true", "0"});
        kFastFilterPlayerBasketMap.put("ASS", new String[]{"filtri.player.basket.assist", "true", "0"});
        kFastFilterPlayerBasketMap.put("LP", new String[]{"filtri.player.basket.lowpost", "true", "0"});
        kFastFilterPlayerBasketMap.put("STOP", new String[]{"filtri.player.basket.stoppata", "true", "0"});
        kFastFilterPlayerBasketMap.put("REC", new String[]{"filtri.player.basket.recupero", "true", "0"});
        kFastFilterPlayerBasketMap.put("FAF", new String[]{"filtri.player.basket.fallo", "true", "0"});

        kFastFilterTeamBasketMap.put("ATT", new String[]{"filtri.player.basket.attacco", "true", "0"});
        kFastFilterTeamBasketMap.put("RIM", new String[]{"filtri.player.basket.rimessa", "true", "0"});
        kFastFilterTeamBasketMap.put("TF", new String[]{"filtri.player.basket.tiro", "true", "0"});
        kFastFilterTeamBasketMap.put("TLB", new String[]{"filtri.player.basket.tirolibero", "true", "0"});
        kFastFilterTeamBasketMap.put("REB", new String[]{"filtri.player.basket.rimbalzo", "true", "0"});
        kFastFilterTeamBasketMap.put("ASS", new String[]{"filtri.player.basket.assist", "true", "0"});
        kFastFilterTeamBasketMap.put("LP", new String[]{"filtri.player.basket.lowpost", "true", "0"});
        kFastFilterTeamBasketMap.put("STOP", new String[]{"filtri.player.basket.stoppata", "true", "0"});
        kFastFilterTeamBasketMap.put("FAF", new String[]{"filtri.player.basket.fallo", "true", "0"});
        kFastFilterTeamBasketMap.put("PP", new String[]{"filtri.player.basket.pallapersa", "true", "0"});
        kFastFilterTeamBasketMap.put("REC", new String[]{"filtri.player.basket.recupero", "true", "0"});

        kFastFilterTeamBasketAgainstMap.put("DIF", new String[]{"filtri.player.basket.difesa", "true", "0"});
        kFastFilterTeamBasketAgainstMap.put("RID", new String[]{"filtri.player.basket.avversario.rimessa", "true", "0"});
        kFastFilterTeamBasketAgainstMap.put("TS", new String[]{"filtri.player.basket.avversario.tiro", "true", "0"});
        kFastFilterTeamBasketAgainstMap.put("TLBS", new String[]{"filtri.player.basket.avversario.tirolibero", "true", "0"});
        kFastFilterTeamBasketAgainstMap.put("REBD", new String[]{"filtri.player.basket.avversario.rimbalzo", "true", "0"});
        kFastFilterTeamBasketAgainstMap.put("ASSS", new String[]{"filtri.player.basket.avversario.assist", "true", "0"});
        kFastFilterTeamBasketAgainstMap.put("LPD", new String[]{"filtri.player.basket.avversario.lowpost", "true", "0"});
        kFastFilterTeamBasketAgainstMap.put("STOPS", new String[]{"filtri.player.basket.avversario.stoppata", "true", "0"});
        kFastFilterTeamBasketAgainstMap.put("FAS", new String[]{"filtri.player.basket.avversario.fallo", "true", "0"});

    }

    public static void initS3ClientLogo() {
        int count = 0;
        Boolean ok = false;
        while (!ok && count < 10) {
            try {
                String accessKey_user_playlist = "********************";
                String secretKey = "BSH5GdR1ccBJe+OX8Ej3KKRjqhw0ij+IdC+k6ml9";
                AWSCredentials credentials = new BasicAWSCredentials(accessKey_user_playlist, secretKey);
                com.amazonaws.regions.Region usWest2 = com.amazonaws.regions.Region.getRegion(Regions.EU_WEST_1);
                ClientConfiguration clientConfig = new ClientConfiguration();
                clientConfig.setProtocol(Protocol.HTTP);
                // DAFARE test per problema nel download
                clientConfig.setSocketTimeout(240 * 1000);
                s3LogoClient = new AmazonS3Client(credentials, clientConfig);
                s3LogoClient.setRegion(usWest2);
                ok = true;

            } catch (IllegalArgumentException ex) {
                reportError(ex);
                //Logger.getLogger(DatiPartita.class.getName()).log(Level.SEVERE, null, ex);
            }
            count++;
            if (!ok) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                }
            }
        }
    }

    private static PrivateKey retrievePrivateKey() {
        PrivateKey privateKey = null;
        try {
            byte[] bytesDer = readContentIntoByteArray(new File(GlobalHelper.class.getResource("/pers/sicstv/cfinit.dll").getFile()));/*Files.readAllBytes((Paths.get(GlobalHelper.class.getResource("/pers/sicstv/cfinit.dll").toURI())));*/
            DerInputStream derInput = new DerInputStream(bytesDer);
            privateKey = RSAPrivateKeyImpl.parse(derInput.getDerValue());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return privateKey;
    }

    private static byte[] readContentIntoByteArray(File file) {
        FileInputStream fileInputStream = null;
        byte[] bFile = new byte[(int) file.length()];
        try {
            //convert file into array of bytes
            fileInputStream = new FileInputStream(file);
            fileInputStream.read(bFile);
            fileInputStream.close();
//            for (int i = 0; i < bFile.length; i++) {
//                System.out.print((char) bFile[i]);
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bFile;
    }

    public static String formatTime(Long val) {
        Long totalSec = val / 1000;
        Long hours = (totalSec / 3600) % 24;
        Long minutes = (totalSec / 60) % 60;
        Long seconds = (totalSec % 60);
        return (hours > 0 ? (hours < 10 ? "0" + hours : hours) + ":" : "") + (minutes < 10 ? "0" + minutes : minutes) + ":" + (seconds < 10 ? "0" + seconds : seconds);
    }

    public static String formatTimeSeconds(Long val) {
        return val / 1000 + "s";
    }

    public static void writeLogData(HttpSession session, String action, String username, String password, Long userId) {
        writeLogData(session, null, action, username, password, userId, null);
    }

    public static void writeLogData(UserService service, String action, String username, String password, Long userId) {
        writeLogData(null, service, action, username, password, userId, null);
    }

    public static void writeLogData(HttpSession session, String action, String username, String password, Long userId, String video) {
        writeLogData(session, null, action, username, password, userId, video);
    }

    public static boolean addPlaylist(Playlist playlist) {
        try {
            WebApplicationContext ctx = WebApplicationContextUtils.getWebApplicationContext(SicsTvServletContextListener.getContext());
            ManagerService manager = (ManagerService) ctx.getBean(GlobalHelper.kBeanAppManager);
            if (playlist != null && playlist.getId() == -1) {
                manager.addPlaylist(playlist);
            }
            return true;
        } catch (SQLException | BeansException e) {
            reportError(e);
        }
        return false;
    }

    public static void writeLogData(HttpSession session, UserService service, String action, String username, String password, Long userId, String video) {

        try {

            ManagerService manager = null;
            if (service == null) {
                ServletContext servletContext = session.getServletContext();
                if (servletContext != null) {
                    WebApplicationContext ctx = WebApplicationContextUtils.getWebApplicationContext(servletContext);
                    manager = (ManagerService) ctx.getBean(GlobalHelper.kBeanAppManager);
                }
            }

            LogData logData = new LogData();
            logData.setLoginname(username);
            logData.setPassword(password);
            logData.setUserId(userId);
            logData.setAction(action);
            logData.setVideo(video);

            ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (sra != null) {
                Device device = DeviceUtils.getCurrentDevice(sra.getRequest());
                if (device != null) {
                    if (device.isTablet()) {
                        logData.setDevice(GlobalHelper.kActionDeviceTablet);
                    } else if (device.isMobile()) {
                        logData.setDevice(GlobalHelper.kActionDeviceMobile);
                    } else if (device.isNormal()) {
                        logData.setDevice(GlobalHelper.kActionDeviceNormal);
                    }
                }
            }

            logData.setApplication(SpringServletContextHelper.getInitParameter("webAppRootKey"));

            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null) {
                WebAuthenticationDetails details = (WebAuthenticationDetails) authentication.getDetails();
                logData.setAddressId(details.getRemoteAddress());
                logData.setSessionId(details.getSessionId());
            }

            if (service != null) {
                service.saveLogData(logData);
            } else if (manager != null) {
                manager.saveLogData(logData);
            }

            if (StringUtils.isNotBlank(action)) {
                if (StringUtils.equals(action, GlobalHelper.kActionVideoStreaming)) {
                    GlobalHelper.updateUserActionsCounter(session, UserAction.VIDEO_STREAM);
                } else if (StringUtils.equals(action, GlobalHelper.kActionVideoDownload)) {
                    GlobalHelper.updateUserActionsCounter(session, UserAction.VIDEO_DOWNLOAD);
                } else if (StringUtils.equals(action, GlobalHelper.kActionNewPlaylistTv)) {
                    GlobalHelper.updateUserActionsCounter(session, UserAction.NEW_PLAYLIST);
                } else if (StringUtils.equals(action, GlobalHelper.kActionNewWatchlist)) {
                    GlobalHelper.updateUserActionsCounter(session, UserAction.NEW_WATCHLIST);
                } else if (StringUtils.equals(action, GlobalHelper.kActionXmlJsonDownload)) {
                    GlobalHelper.updateUserActionsCounter(session, UserAction.DOWNLOAD_XML_JSON);
                } else if (StringUtils.equals(action, GlobalHelper.kActionStartExport)) {
                    GlobalHelper.updateUserActionsCounter(session, UserAction.START_EXPORT);
                } else if (StringUtils.equals(action, GlobalHelper.kActionLoginCorrect)) {
                    GlobalHelper.updateUserActionsCounter(session, UserAction.LOGIN_CORRECT);
                } else if (StringUtils.equals(action, GlobalHelper.kActionLoginError)) {
                    GlobalHelper.updateUserActionsCounter(session, UserAction.LOGIN_ERROR);
                } else if (StringUtils.equals(action, GlobalHelper.kActionLicenseExpired)) {
                    GlobalHelper.updateUserActionsCounter(session, UserAction.LICENSE_EXPIRED);
                }
            }

        } catch (Exception se) {
            System.out.println("Exception during userLogin: " + se.getMessage());
        }

    }

//    public static String pathS3(String name, AmazonS3Client s3Client) {
//        return pathS3(name, s3Client, "it.sics.svs");
//    }
//
//    public static String pathS3(String name, AmazonS3Client s3Client, String bucketName) {
//        String value = "";
//        try {
////			String accessKey = "********************";
////			String secretKey = "EAk+N/aQBAc5frsEzI3YFjCLL98e24BtqL/nRinj";
////			AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
////			
////			//DAFARE così ottengo una path http://it.sics.svs.s3-eu-west-1.amazonaws.com/ e parte il play
////			ClientConfiguration clientConfig = new ClientConfiguration();
////			clientConfig.setProtocol(Protocol.HTTP);
////			AmazonS3 s3Client = new AmazonS3Client(credentials, clientConfig);
//
//            //DAFARE così ottengo una path https://it.sics.svs.s3-eu-west-1.amazonaws.com/ parte il download
//            //ma prima appare: "Hai tentato di accedere a it.sics.svs.s3-eu-west-1.amazonaws.com ma in realtà hai raggiunto un server che si identifica come 
//            //*.s3-eu-west-1.amazonaws.com. Questo problema potrebbe essere dovuto a un errore di configurazione sul server o a qualcosa di più grave.
//            //Forse un utente malintenzionato in rete sta tentando di indurti a visitare una versione falsa (e potenzialmente dannosa) di it.sics.svs.s3-eu-west-1.amazonaws.com."
//            //AmazonS3 s3Client = new AmazonS3Client(credentials);
//            //s3Client.setEndpoint("s3-eu-west-1.amazonaws.com");
//            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, name);
//
//            Date dt = new Date();
//            Calendar c = Calendar.getInstance();
//            c.setTime(dt);
//            c.add(Calendar.DATE, 1);
//            dt = c.getTime();
//            request.setExpiration(dt);
//
//            value = s3Client.generatePresignedUrl(request).toString();
//            //}
//
//            //value =  s3Client.generatePresignedUrl(bucketName, name, dt, HttpMethod.GET).toString();
//        } catch (Exception ex) {
//            reportError(ex);
//        }
//        return value;
//    }
    public static void reportError(Exception exception) {

        try {

            StackTraceElement[] st = exception.getStackTrace();
            String stackTrace = "";
            for (int i = 0; i < st.length; i++) {
                StackTraceElement stackTraceElement = st[i];
                stackTrace += stackTraceElement.toString() + "\n";
            }

            //this.log.error(stackTrace);
            exception.printStackTrace();

//			String contenuto = exception.getClass().toString() + ": " + exception.getMessage() + "\n" + "\n" + stackTrace;
//			MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
//			mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), "<EMAIL>", null, null, null, null, "Errore sics.tv", contenuto, null);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void reportError(String error, String classe) {

        try {

//			String contenuto = classe + ": " + error + "\n";
//			MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
//			mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), "<EMAIL>", null, null, null, null, "Errore sics.tv", contenuto, null);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static String msecToMinSec(Long count, Boolean showHours, Boolean showSeconds, Boolean showDecimi) {
        String result = "";
        try {
            if (showHours) {
                Long hours = (long) Math.round(((count / 1000) / 60 / 60));
                Long minutes = ((count / 1000) / 60) % 60;
                Long seconds = (count / 1000) % 60;
                if (showSeconds) {
                    String decimiString = "";
                    if (showDecimi) {
                        Long decimi = (count % 1000) / 10;
                        decimiString = "." + truncateString("0" + Long.toString(Math.round(decimi)), 2);
                    } else if (count % 1000 >= 500) {
                        seconds += 1;
                        if (seconds == 60) {
                            seconds = 0L;
                            minutes += 1;
                            if (minutes == 60) {
                                minutes = 0L;
                                hours += 1;
                            }
                        }
                    }
                    Long minLong = (long) Math.round(minutes);
                    String minutesString = Long.toString(minLong);
                    if (minLong < 100) {
                        minutesString = truncateString("0" + Long.toString(minLong), 2);
                    }
                    result = truncateString("0" + Long.toString(hours), 2) + ":" + minutesString + ":" + truncateString("0" + Long.toString(Math.round(seconds)), 2) + decimiString;
                } else {
                    if (count % 1000 >= 500) {
                        seconds += 1;
                    }
                    if (seconds >= 30) {
                        minutes += 1;
                        if (minutes == 60) {
                            minutes = 0L;
                            hours += 1;
                        }
                    }
                    result = truncateString("0" + Long.toString(hours), 2) + ":" + truncateString("0" + Long.toString(Math.round(minutes)), 2);
                }
            } else {
                Long minutes = (long) Math.round((count / 1000) / 60);
                Long seconds = (count / 1000) % 60;
                if (showSeconds) {
                    String decimiString = "";
                    if (showDecimi) {
                        Long decimi = (count % 1000) / 10;
                        decimiString = "." + truncateString("0" + Long.toString(Math.round(decimi)), 2);
                    } else if (count % 1000 >= 500) {
                        seconds += 1;
                        if (seconds == 60) {
                            seconds = 0L;
                            minutes += 1;
                        }
                    }
                    Long minLong = (long) Math.round(minutes);
                    String minutesString = Long.toString(minLong);
                    if (minLong < 100) {
                        minutesString = truncateString("0" + Long.toString(minLong), 2);
                    }
                    result = minutesString + ":" + truncateString("0" + Long.toString(Math.round(seconds)), 2) + decimiString;
                } else {
                    if (count % 1000 >= 500) {
                        seconds += 1;
                    }
                    if (seconds >= 30) {
                        minutes += 1;
                    }
                    Long minLong = (long) Math.round(minutes);
                    String minutesString = Long.toString(minLong);
                    if (minLong < 100) {
                        minutesString = truncateString("0" + Long.toString(minLong), 2);
                    }
                    result = minutesString;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public static String msecToMinSecPrimiSecondi(Long count, Boolean showHours, Boolean showSeconds, Boolean showDecimi) {
        String minSec = msecToMinSec(count, showHours, showSeconds, showDecimi);
        minSec = minSec.replaceFirst("^0+(?!$)", ""); // rimuove gli zeri all'inizio della stringa
        if (minSec.charAt(0) == ':') {
            minSec = minSec.replace(":", "");
        } else {
            minSec = minSec.replace(":", "'");
        }
        minSec += "''";
        return minSec;
    }

    public static String truncateString(String string, int val) {
        String result;
        if (string.length() > 2) {
            result = string.substring(string.length() - 2, string.length());
        } else {
            result = string;
        }
        return result;
    }

    public static Long frameToMilliseconds(Integer frame) {
        return new Long(frame * 40);
    }

    public static Fascia getFasciaByCode(String code) {
        Fascia fascia = null;
        try {
            switch (code) {
                case "1-1":
                    fascia = new Fascia("Walk", 0f, 8f, Color.WHITE);
                    break;
                case "1-2":
                    fascia = new Fascia("Jog", 8f, 13f, Color.YELLOW);
                    break;
                case "1-3":
                    fascia = new Fascia("(Low) LS Run", 13f, 16f, Color.ORANGE);
                    break;
                case "1-4":
                    fascia = new Fascia("Run", 16f, 19f, Color.PINK);
                    break;
                case "1-5":
                    fascia = new Fascia("(High) LS Run", 19f, 22f, Color.BLUE);
                    break;
                case "1-6":
                    fascia = new Fascia("(Maximum) LS Run", 22f, Float.MAX_VALUE, Color.RED);
                    break;
                case "2-1":
                    fascia = new Fascia("MD", -Float.MAX_VALUE, -3f, Color.BLACK);
                    break;
                case "2-2":
                    fascia = new Fascia("HD", -3f, -2f, Color.DARK_GRAY);
                    break;
                case "2-3":
                    fascia = new Fascia("ID", -2f, -1f, Color.MAGENTA);
                    break;
                case "2-4":
                    fascia = new Fascia("LD", -1f, 0f, Color.LIGHT_GRAY);
                    break;
                case "2-5":
                    fascia = new Fascia("LA", 0f, 1f, Color.WHITE);
                    break;
                case "2-6":
                    fascia = new Fascia("IA", 1f, 2f, Color.YELLOW);
                    break;
                case "2-7":
                    fascia = new Fascia("HA", 2f, 3f, Color.BLUE);
                    break;
                case "2-8":
                    fascia = new Fascia("MA", 3f, Float.MAX_VALUE, Color.RED);
                    break;
                case "3-1":
                    fascia = new Fascia("LP", 0f, 10f, Color.WHITE);
                    break;
                case "3-2":
                    fascia = new Fascia("IP", 10f, 20f, Color.YELLOW);
                    break;
                case "3-3":
                    fascia = new Fascia("HP", 20f, 35f, Color.GRAY);
                    break;
                case "3-4":
                    fascia = new Fascia("EP", 35f, 55f, Color.BLUE);
                    break;
                case "3-5":
                    fascia = new Fascia("MP", 55f, Float.MAX_VALUE, Color.RED);
                    break;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return fascia;
    }

    public static boolean checkAtletaPresente(ArrayList<Atleta> players, Integer id) {
        boolean trovato = false;
        for (Atleta player : players) {
            if (player.getId().equals(id)) {
                trovato = true;
                break;
            }
        }
        return trovato;
    }

    public static String saveConfigAnalysis(String pathConfig, String tabName, String code, String textButton, Boolean delete) {
        String id = "";
        if (delete) {
            tabName = tabName.replace("Remove Tab", "").trim();
        }
        try {
            SAXBuilder builder;
            Document document;
            Element config;
            if (new File(pathConfig).exists()) {
                builder = new SAXBuilder();
                document = builder.build(pathConfig);
                config = document.getRootElement();
            } else {
                config = new Element("CONFIG");
                document = new Document(config);
            }

            List listTabs = config.getChildren("TAB");
            Element tab = null;
            for (Object node : listTabs) {
                Element t = (Element) node;
                if (t.getAttributeValue("name").equalsIgnoreCase(tabName)) {
                    if (delete) {
                        config.removeContent(t);
                    } else {
                        tab = t;
                    }
                }
            }

            if (!delete) {
                if (tab == null) {
                    tab = new Element("TAB");
                    tab.setAttribute("name", tabName);
                    tab.setAttribute("count", "0");
                    config.addContent(tab);
                }

                if (!code.isEmpty() && !textButton.isEmpty()) {
                    id = tabName + tab.getAttributeValue("count");
                    Integer next = Integer.parseInt(tab.getAttributeValue("count")) + 1;
                    Element button = new Element("BUTTON");
                    button.setAttribute("id", id);
                    button.setAttribute("code", code);
                    button.setAttribute("text", textButton);
                    tab.addContent(button);

                    tab.setAttribute("count", (next).toString());
                }
            }

            XMLOutputter outputter = new XMLOutputter(Format.getPrettyFormat().setOmitDeclaration(true));
            outputter.output(document, new FileOutputStream(pathConfig));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return id;
    }

    public static void removeButtonAnalysis(String pathConfig, String id) {
        try {
            SAXBuilder builder;
            Document document;
            Element config;
            if (new File(pathConfig).exists()) {
                builder = new SAXBuilder();
                document = builder.build(pathConfig);
                config = document.getRootElement();
                List listTabs = config.getChildren("TAB");
                for (Object node : listTabs) {
                    Element t = (Element) node;
                    List buttons = t.getChildren("BUTTON");
                    for (Object b : buttons) {
                        Element button = (Element) b;
                        if (button.getAttributeValue("id").equalsIgnoreCase(id)) {
                            t.removeContent(button);
                            break;
                        }
                    }
                }

                XMLOutputter outputter = new XMLOutputter(Format.getPrettyFormat().setOmitDeclaration(true));
                outputter.output(document, new FileOutputStream(pathConfig));
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static String readConfigAnalysis(String pathConfig) {
        String html = "";
        String ul = "<ul id=\"sortable\">";
        String divs = "";

        try {
            SAXBuilder builder;
            Document document;
            Element config;
            if (new File(pathConfig).exists()) {
                builder = new SAXBuilder();
                document = builder.build(pathConfig);
                config = document.getRootElement();
                List listTabs = config.getChildren("TAB");
                for (Object node : listTabs) {
                    Element t = (Element) node;
                    List buttons = t.getChildren("BUTTON");
                    String name = t.getAttributeValue("name");
                    String idTab = name.replace(" ", "");
                    ul += "<li><a href=\"#" + idTab + "\">" + name + "</a> <span class=\"ui-icon ui-icon-close\" role=\"presentation\">Remove Tab</span></li>";
                    divs += "<div class=\"tabsContent\" id=\"" + idTab + "\">";
                    for (Object b : buttons) {
                        Element button = (Element) b;
                        String code = button.getAttributeValue("code").replace("|", "&");
                        String id = button.getAttributeValue("id");
                        divs += "<div id='" + id + "' class='button' onclick='changeButtonStatus(event);'><p class='textbutton'><input type='hidden' id='" + code + "' name='" + code + "' value='" + code + "'/>" + button.getAttributeValue("text") + ""
                                + "<span class='removeButton'><img src='/" + SpringServletContextHelper.getInitParameter("webAppRootKey") + "/images/deleteWhite.png' alt='Remove' onclick=''/></span></p></div>";
                    }
                    divs += "</div>";
                }
                ul += "</ul>";
            }
            html = ul + divs;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return html;
    }

    public static BufferedImage drawFieldFormazioni(Game game, String path, String nomeImg) {
        BufferedImage field = null;
        try {
            int wField = 8 * latoLungoField;
            int hField = 8 * latoCortoField;
            int dimEllipse = 35;

            if (game.getHomeColor() == null || game.getAwayColor() == null) {
                game.setHomeColor("255x255x0x0");
                game.setAwayColor("255x0x0x255");
            } else if (game.getHomeColor().equals(game.getAwayColor())) {
                Color newColor = game.getAwayColor().brighter().brighter();
                if (newColor.equals(game.getAwayColor())) {
                    newColor = game.getAwayColor().darker().darker();
                }
                game.setAwayColor(GlobalHelper.getColorStringFromColor(newColor));
            }

            field = imageToBufferedImage(ImageIO.read(new File(path + "campo_2023_linee-h.png")).getScaledInstance(wField, hField, Image.SCALE_SMOOTH));
//			field = drawEmptyHorizontalField(wField, hField);
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            ArrayList<Atleta> team1 = new ArrayList<>();
            for (Atleta atleta : game.getFormazione1().values()) {
                team1.add(atleta);
            }
            Collections.sort(team1, Atleta.getAtletaComparator());

            ModuleHelper.setPositionPlayersByModuleReport(team1, game.getHomeModule().replace("-", ""));

            ArrayList<Rectangle2D> rectanglesNames = new ArrayList<>();
            int i = 0;
            for (Atleta player : team1) {
                sics.analysis.Punto point = player.getPosReport(0l, wField, hField, true, true);
                if (((player.getModule_position() != null && player.getModule_position() <= 11)) && point.getX() != -1 && point.getY() != -1) {

                    disegnaPallini(field, (int) point.getX(), (int) point.getY(), dimEllipse, game.getHomeColor());
                    fieldGraphics.setFont(new Font("Roboto", Font.PLAIN, 20));

                    fieldGraphics.setPaint(setColoreTesto(game.getHomeColor()));
                    //scrivo numero giocatori
                    if (player.getMatchNumber() < 10) {
                        fieldGraphics.drawString(player.getMatchNumber().toString(), point.getX() - 5, point.getY() + 7);
                    } else {
                        fieldGraphics.drawString(player.getMatchNumber().toString(), point.getX() - 10, point.getY() + 8);
                    }
                    //scrivo nomi giocatori
                    fieldGraphics.setPaint(Color.WHITE);
                    fieldGraphics.setFont(new Font("Roboto", Font.BOLD, 15));

                    int height = fieldGraphics.getFontMetrics().getHeight();
                    int width = fieldGraphics.getFontMetrics().stringWidth(player.getKnown_name());
                    float nLettere = GlobalHelper.formatName(player.getKnown_name()).length();
//                    fieldGraphics.drawString(GlobalHelper.formatName(player.getKnown_name()), point.getX() - ((nLettere / 2) * 9), point.getY() + dimEllipse / 2 + 18);
                    Rectangle2D rr = new Rectangle2D.Float(point.getX() + dimEllipse / 2 - (width / 2), point.getY() + dimEllipse / 2 + height, width, height);
                    boolean contained = false;
                    for (Rectangle2D r : rectanglesNames) {
                        if (r.intersects(rr)) {
                            contained = true;
                            break;
                        }
                    }
                    if (contained) {
                        rr = new Rectangle2D.Float(point.getX() + dimEllipse / 2 - (width / 2), point.getY() - height, width, height);
                        rectanglesNames.add(rr);
                        fieldGraphics.drawString(GlobalHelper.formatName(player.getKnown_name()), point.getX() + dimEllipse / 2 - (width / 2), point.getY() - height);
                    } else {
                        rectanglesNames.add(rr);
                        fieldGraphics.drawString(GlobalHelper.formatName(player.getKnown_name()), point.getX() + dimEllipse / 2 - (width / 2), point.getY() + dimEllipse / 2 + height);
                    }
                }

            }
            ArrayList<Atleta> team2 = new ArrayList<>();
            for (Atleta atleta : game.getFormazione2().values()) {
                team2.add(atleta);
            }
            Collections.sort(team2, Atleta.getAtletaComparator());

            ModuleHelper.setPositionPlayersByModuleReport(team2, game.getAwayModule().replace("-", ""));
            for (Atleta player : team2) {
                sics.analysis.Punto point = player.getPosReport(0l, wField, hField, false, true);
                if (((player.getModule_position() != null && player.getModule_position() <= 11)) && point.getX() != -1 && point.getY() != -1) {
                    disegnaPallini(field, (int) point.getX(), (int) point.getY(), dimEllipse, game.getAwayColor());
                    fieldGraphics.setFont(new Font("Roboto", Font.PLAIN, 20));
                    fieldGraphics.setPaint(setColoreTesto(game.getAwayColor()));
                    //scrivo numero giocatori
                    if (player.getMatchNumber() < 10) {
                        fieldGraphics.drawString(player.getMatchNumber().toString(), point.getX() - 5, point.getY() + 7);
                    } else {
                        fieldGraphics.drawString(player.getMatchNumber().toString(), point.getX() - 10, point.getY() + 8);
                    }
                    //scrivo nomi giocatori
                    fieldGraphics.setPaint(Color.WHITE);
                    fieldGraphics.setFont(new Font("Roboto", Font.BOLD, 15));

                    int height = fieldGraphics.getFontMetrics().getHeight();
                    int width = fieldGraphics.getFontMetrics().stringWidth(player.getKnown_name());
                    float nLettere = GlobalHelper.formatName(player.getKnown_name()).length();
//                    fieldGraphics.drawString(GlobalHelper.formatName(player.getKnown_name()), point.getX() - ((nLettere / 2) * 9), point.getY() + dimEllipse / 2 + 18);
                    Rectangle2D rr = new Rectangle2D.Float(point.getX() + dimEllipse / 2 - (width / 2), point.getY() + dimEllipse / 2 + height, width, height);
                    boolean contained = false;
                    for (Rectangle2D r : rectanglesNames) {
                        if (r.intersects(rr)) {
                            contained = true;
                            break;
                        }
                    }
                    if (contained) {
                        rr = new Rectangle2D.Float(point.getX() + dimEllipse / 2 - (width / 2), point.getY() - height, width, height);
                        rectanglesNames.add(rr);
                        fieldGraphics.drawString(GlobalHelper.formatName(player.getKnown_name()), point.getX() + dimEllipse / 2 - (width / 2), point.getY() - height);
                    } else {
                        rectanglesNames.add(rr);
                        fieldGraphics.drawString(GlobalHelper.formatName(player.getKnown_name()), point.getX() + dimEllipse / 2 - (width / 2), point.getY() + dimEllipse / 2 + height);
                    }

//                    float nLettere = GlobalHelper.formatName(player.getKnown_name()).length();
//                    fieldGraphics.drawString(GlobalHelper.formatName(player.getKnown_name()), point.getX() - ((nLettere / 2) * 9), point.getY() - 20);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        File tabellinoFolder = new File(path + File.separator + "tabellino" + File.separator);
        if (!tabellinoFolder.exists()) {
            tabellinoFolder.mkdirs();
        }
        File outputfile = new File(path + File.separator + "tabellino" + File.separator + nomeImg);
        if (outputfile.exists()) {
            outputfile.delete();
        }
        try {
            ImageIO.write(field, "png", outputfile);
        } catch (IOException ex) {
            Logger.getLogger(GlobalHelper.class.getName()).log(Level.SEVERE, null, ex);
        }

        return field;
    }

    public static String getColorStringFromColor(Color color) {
        return String.valueOf(color.getAlpha()) + "x" + String.valueOf(color.getRed()) + "x" + String.valueOf(color.getGreen()) + "x" + String.valueOf(color.getBlue());
    }

    public static String getColorStringFromFixtureDetails(String color) {
        // Ensure the string is 6 characters long
        if (color.length() == 6) {
            // Extract RGB components
            String redHex = color.substring(0, 2);
            String greenHex = color.substring(2, 4);
            String blueHex = color.substring(4, 6);

            // Convert hex to decimal
            int red = Integer.parseInt(redHex, 16);
            int green = Integer.parseInt(greenHex, 16);
            int blue = Integer.parseInt(blueHex, 16);

            // Format the output
            return "255x" + red + "x" + green + "x" + blue;
        } else {
            return null;
        }
    }

    public static Color getColorFromString(String colorTeamString) {
        if (colorTeamString == null) {
            return null;
        }
        String[] colorString = colorTeamString.split("x");
        Color colorTeam;
        if (colorString.length == 4) {
            colorTeam = new Color(Integer.parseInt(colorString[1]), Integer.parseInt(colorString[2]), Integer.parseInt(colorString[3]), Integer.parseInt(colorString[0]));
        } else {
            colorTeam = Color.BLUE;
        }
        return colorTeam;
    }

    public static Color setColoreTesto(Color c) {
        Color txtColor = Color.WHITE;
        if (c.getGreen() > 190) {
            txtColor = Color.BLACK;
        } else if (c.getRed() > 190) {
            if ((c.getGreen() > 190 || c.getBlue() > 190) || (c.getGreen() > 127 || c.getBlue() > 127)) {
                txtColor = Color.BLACK;
            }
        } else if (c.getBlue() > 190) {
            if ((c.getGreen() > 190 || c.getRed() > 190) || (c.getGreen() > 128 || c.getRed() > 128)) {
                txtColor = Color.BLACK;
            }
        }
        return txtColor;
    }

    public static String formatName(String nome) {
        String nomeCorretto = "";
        try {
            if (nome != null && !nome.equalsIgnoreCase("")) {
                nome = nome.toLowerCase();
                String[] stringa = nome.split(" ");
                for (String s : stringa) {
                    nomeCorretto = nomeCorretto + String.valueOf(s.charAt(0)).toUpperCase() + s.substring(1) + " ";
                }
            }
            nomeCorretto = nomeCorretto.trim();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return nomeCorretto;
    }

    public static void disegnaPallini(BufferedImage field, int x, int y, int size, Color color) {
        try {
            Ellipse2D circle = new Ellipse2D.Double(x - (size / 2), y - (size / 2), size, size);
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            fieldGraphics.setPaint(Color.BLACK);
            fieldGraphics.draw(circle);
            fieldGraphics.setPaint(color);
            fieldGraphics.fill(circle);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static BufferedImage imageToBufferedImage(final Image image) {

        BufferedImage bufferedImage = null;
        try {
            if (image != null) {
                bufferedImage = new BufferedImage(image.getWidth(null), image.getHeight(null), BufferedImage.TYPE_INT_ARGB);
                final Graphics2D g2 = bufferedImage.createGraphics();
                g2.drawImage(image, 0, 0, null);
                g2.dispose();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return bufferedImage;
    }

    public static int getMinuteEvent(int half, int minute, int second) {
        int outMin = 0;
        try {
            if (second >= 30) {
                minute += 1;
            }
            outMin = (half == 2) ? 45 + minute : minute;
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return outMin;
    }

    public static String pathS3(String name, String type) {
        return pathS3(name, type, false);
    }

    public static String createCloudFrontSignedURL(String name, Date expirationDate) {
        String signedUrl = null;
        try {
            String keyPairId = "K1SRL0K7SLP3VU";

            Date dateLessThan = new Date();
            if (expirationDate != null) {
                dateLessThan = expirationDate;
            } else {
                Calendar c = Calendar.getInstance();
                c.setTime(dateLessThan);
                c.add(Calendar.DATE, 7);
//            c.add(Calendar.DATE, 30);
                dateLessThan = c.getTime();
            }
            signedUrl = CloudFrontUrlSigner.getSignedURLWithCannedPolicy(name, keyPairId, kPrivateKey, dateLessThan);
        } catch (Exception e) {
            signedUrl = name;
        }
        if (signedUrl == null) {
            signedUrl = name;
        }
        return signedUrl;
    }

    public static String pathS3(String name, String type, Boolean cloudFront) {
        if (s3Client == null) {
            initS3Client();
        }
        String value = "";
        try {

            String key = name;
            Date expiration = new Date();
            Long msec = expiration.getTime();
//            msec += 1000 * 60 * 5; // 5 min.
//            msec += 1000 * 60 * 10; // 10 min.
//            msec += 1000 * 60 * 60; // 1 hour.

            Integer h24 = 1000 * 60 * 60 * 24;
            msec += h24;
//            msec += h24 * 6;

            String bucketName = kBucketVideo;
            if (type.equals("report")) {
                bucketName = kBucketReport;
            } else if (type.equals("digital")) {
                bucketName = kBucketDigital;
            } else if (type.equals("playlist")) {
                bucketName = kBucketPlaylist;
                // durata 1 + 6 giorni
                msec += h24 * 6;
            }

            //&& !name.contains("-SB-")
            if (bucketName.equals(kBucketVideo) && cloudFront) {
                value = createCloudFrontSignedURL(getUrlEncoded(name, false), null);
//                value = kCloudFrontUrl + URLEncoder.encode(name, StandardCharsets.UTF_8.toString());
            } else if (bucketName.equals(kBucketPlaylist) && cloudFront) {
                value = createCloudFrontSignedURL(getUrlEncoded(name, true), null);
            } else {
                expiration.setTime(msec);
                String bn = "/";
                GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bn + bucketName, key);
                generatePresignedUrlRequest.setMethod(HttpMethod.GET); // Default.

                generatePresignedUrlRequest.setExpiration(expiration);
                URL url = null;
                if (type.equals("playlist")) {
                    url = s3PlaylistClient.generatePresignedUrl(generatePresignedUrlRequest);
                } else {
                    url = s3Client.generatePresignedUrl(generatePresignedUrlRequest);
                }
                value = url.toString();
            }
        } catch (IllegalArgumentException | AmazonClientException ex) {
            ex.printStackTrace();
//        } catch (Exception ex){
//            LoggerHelper.severe(ex, "GloabalHelper.pathS3");
        }
        return value;
    }

    public static void initS3UploadClient() {
        try {
            if (s3UploadClient == null) {
                String accessKey_user_playlist = "********************";
                String secretKey = "R8x1cq/p8rjqXG84K7pBP+ehSQGdsStRGNjs8fBk";
                AWSCredentials credentials = new BasicAWSCredentials(accessKey_user_playlist, secretKey);
                com.amazonaws.regions.Region usWest2 = com.amazonaws.regions.Region.getRegion(Regions.EU_WEST_1);
                ClientConfiguration clientConfig = new ClientConfiguration();
                clientConfig.setProtocol(Protocol.HTTP);
                // DAFARE test per problema nel download
                clientConfig.setSocketTimeout(240 * 1000);
                s3UploadClient = new AmazonS3Client(credentials, clientConfig);
                s3UploadClient.setRegion(usWest2);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static void initS3Client() {
        try {

            // DAFARE prendere credentials da file?
            String accessKey = "********************";
            String secretKey = "EAk+N/aQBAc5frsEzI3YFjCLL98e24BtqL/nRinj";
//            String accessKey = "********************";
//            String secretKey = "Vs8tHV3bvQPVpNgYysTEuIMk06GthyhZlat6r+8O";
            AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
            ClientConfiguration clientConfig = new ClientConfiguration();
            clientConfig.setProtocol(Protocol.HTTPS);
            // DAFARE test per problema nel download
            clientConfig.setSocketTimeout(240 * 1000);
            s3Client = new AmazonS3Client(credentials, clientConfig);
            com.amazonaws.regions.Region usWest2 = com.amazonaws.regions.Region.getRegion(Regions.EU_WEST_1);
            s3Client.setRegion(usWest2);
//                String bucketName = "sicstv.s3-accelerate.amazonaws.com";
//                s3Client.setBucketAccelerateConfiguration(new SetBucketAccelerateConfigurationRequest(bucketName, new BucketAccelerateConfiguration(BucketAccelerateStatus.Enabled)));
            //DAFARE test AccelerateMode
//                s3Client.setS3ClientOptions(S3ClientOptions.builder().setAccelerateModeEnabled(true).build());

            accessKey = "********************";
            secretKey = "xueIdIoMTwcPINMCFjSOfsuuBsxtRSr6t0auk8Nw";
            credentials = new BasicAWSCredentials(accessKey, secretKey);
            s3PlaylistClient = new AmazonS3Client(credentials, clientConfig);
            s3PlaylistClient.setRegion(usWest2);

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static String generateIdentifierFromTime() {
        Date now = Calendar.getInstance().getTime();
        Calendar cal = Calendar.getInstance();
        cal.setTime(now);
        int year = cal.get(Calendar.YEAR);
        int month = cal.get(Calendar.MONTH) + 1;
        int day = cal.get(Calendar.DAY_OF_MONTH);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int minute = cal.get(Calendar.MINUTE);
        int second = cal.get(Calendar.SECOND);
        int millisecond = cal.get(Calendar.MILLISECOND);
        String date = year + (month > 9 ? "" : "0") + month + (day > 9 ? "" : "0") + day;
        String time = (hour > 9 ? "" : "0") + hour + (minute > 9 ? "" : "0") + minute + (second > 9 ? "" : "0") + second + (millisecond > 99 ? "" : "0" + (millisecond > 9 ? "" : "0")) + millisecond;
        return date + time;
    }

    public static String getPathExportCache() {
        if (kServletExportPathCache.isEmpty()) {
            kServletExportPathCache = kServletContextPathExport + File.separator + "cache" + File.separator;
            if (!new File(kServletExportPathCache).exists()) {
                new File(kServletExportPathCache).mkdirs();
            }
        }
        return kServletExportPathCache;
    }

    public static boolean findActionClip(String key) {
        try {
            return new File(getPathExportCache() + key).exists();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return false;
    }

    public static void deleteDirectory(File path) {
        try {
            if (path.exists()) {
                File[] files = path.listFiles();
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            path.delete();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static String getPathFFmpeg() {
        if (isWindows()) {
            kPathFFmpeg = GlobalHelper.kServletContextPathBin + "ffmpeg214.exe";
        } else {
            kPathFFmpeg = GlobalHelper.kServletContextPathBin + "ffmpeg";
        }

        return kPathFFmpeg;
    }

    public static String pathPlaylistShare(Playlist playlist, boolean cloudFront) {
        String pathSicsTv = "";
        try {
            if (s3Client == null) {
                initS3Client();
            }
            String key = playlist.getVideoName();
            Date expiration = new Date();
            Long msec = expiration.getTime();
//            msec += 1000 * 60 * 5; // 5 min.
//            msec += 1000 * 60 * 10; // 10 min.
//            msec += 1000 * 60 * 60; // 1 hour.
            Integer h24 = 1000 * 60 * 60 * 24;
            msec += h24;
//            msec += h24 * 6;
            msec += h24 * 6;
            if (cloudFront) {
                pathSicsTv = pathS3(key, "playlist", true);
            } else {
                String bucketName = kBucketPlaylist;
                expiration.setTime(msec);
                String bn = "/";
                GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bn + bucketName, key);
                generatePresignedUrlRequest.setMethod(HttpMethod.GET); // Default.
                generatePresignedUrlRequest.setExpiration(expiration);
                URL url = s3PlaylistClient.generatePresignedUrl(generatePresignedUrlRequest);
                byte[] encBytes = Base64.encodeBase64(url.toString().getBytes());
                String nameEnc = new String(encBytes);
                pathSicsTv = mPlaylistGamePageShare + "?game=0&playlist=" + playlist.getUserUploadId() + "&videoname=" + nameEnc;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return pathSicsTv;
    }

    public static String pathGameShare(Game game, boolean cloudFront) {
        String pathSicsTv = "";
        try {
            if (s3Client == null) {
                initS3Client();
            }
            String key = game.getVideoName();
            Date expiration = new Date();
            Long msec = expiration.getTime();
//            msec += 1000 * 60 * 5; // 5 min.
//            msec += 1000 * 60 * 10; // 10 min.
//            msec += 1000 * 60 * 60; // 1 hour.
            Integer h24 = 1000 * 60 * 60 * 24;
            msec += h24;
            msec += h24 * 6;
            if (cloudFront) {
                pathSicsTv = pathS3(key, "video", true);
            } else {
                String bucketName = kBucketVideo;
                //&& !name.contains("-SB-")
//            if (bucketName.equals(kBucketVideo) && cloudFront) {
//                value = "https://d12cq458jk3aa4.cloudfront.net/" + URLEncoder.encode(name, StandardCharsets.UTF_8.toString());
//            }
                expiration.setTime(msec);
                String bn = "/";
                GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bn + bucketName, key);
                generatePresignedUrlRequest.setMethod(HttpMethod.GET); // Default.
                generatePresignedUrlRequest.setExpiration(expiration);
                URL url = s3Client.generatePresignedUrl(generatePresignedUrlRequest);
                byte[] encBytes = Base64.encodeBase64(url.toString().getBytes());
                String nameEnc = new String(encBytes);
                pathSicsTv = mPlaylistGamePageShare + "?game=" + game.getOwnerUserId() + "&playlist=0&videoname=" + nameEnc;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return pathSicsTv;
    }

    public static void mergeConfigMap(List listConfigTarget, List listConfigToAdd) {
        try {
            // scorro i bottoni da aggiungere e per ciascuno vedo se fare merge(se è già presente) oppure aggiungo se non c'è
            for (Object objToAdd : listConfigToAdd) {
                EventDescription evtDescrToAdd = (EventDescription) objToAdd;
                if (evtDescrToAdd != null) {
                    boolean trovato = false;
                    for (Object objTarget : listConfigTarget) {
                        EventDescription evtDescrTarget = (EventDescription) objTarget;
                        if (evtDescrTarget.getCode().equals(evtDescrToAdd.getCode())) {
                            trovato = true;
                            mergeListTags(evtDescrTarget.getTag(), evtDescrToAdd.getTag());
                            break;
                        }
                    }
                    if (!trovato) {
                        listConfigTarget.add(evtDescrToAdd);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private static void mergeListTags(List listTagsTarget, List listTagsToAdd) {
        try {
            for (Object objTagToAdd : listTagsToAdd) {
                Tags tagToAdd = (Tags) objTagToAdd;
                if (tagToAdd != null) {
                    boolean trovato = false;
                    for (Object objTagTarget : listTagsTarget) {
                        if (objTagTarget != null) {
                            Tags tagTarget = (Tags) objTagTarget;
                            if (tagTarget.getCode().equals(tagToAdd.getCode())) {
                                trovato = true;
                                break;
                            }
                        }
                    }
                    if (!trovato) {
                        // se non ho trovato il tag lo aggiungo alla lista da unire
                        listTagsTarget.add(tagToAdd);
                    }
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public static boolean isWindows() {
        String OS = System.getProperty("os.name");
        return OS.startsWith("Windows");
    }

    public static <K, V> LinkedHashMap<K, V> reverseMap(LinkedHashMap<K, V> in) {
        if (in == null) {
            return null;
        }
        LinkedHashMap<K, V> m = new LinkedHashMap<K, V>();
        List<K> keys = new ArrayList<K>(in.keySet());
        List<V> values = new ArrayList<V>(in.values());
        for (int i = in.size() - 1; i >= 0; i--) {
            m.put(keys.get(i), values.get(i));
        }
        return m;
    }

    // elimina file dal bucket S3
    public static boolean deleteS3File(String file_name, String type) {
        Boolean result = false;
        try {
            if (type.equals("playlist")) {
                if (s3PlaylistClient == null) {
                    initS3Client();
                }
                s3PlaylistClient.deleteObject(new DeleteObjectRequest("it.sics.svs.playlist", file_name));
            } else if (type.equals("thumb")) {
                if (s3PlaylistClient == null) {
                    initS3Client();
                }
                s3PlaylistClient.deleteObject(new DeleteObjectRequest("it.sics.svs.thumb", file_name));
            } else if (type.equals("game")) {
                GlobalHelper.initS3UploadClient();
                s3UploadClient.deleteObject(new DeleteObjectRequest(kBucketVideo, file_name));
            }
            result = true;
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return result;
    }

    public static String getUrlEncoded(String video, boolean playlist) {
        String result = "";
        try {
            if (playlist) {
                result = kCloudFrontPlaylistUrl + URLEncoder.encode(video, StandardCharsets.UTF_8.toString());
            } else {
                result = kCloudFrontUrl + URLEncoder.encode(video, StandardCharsets.UTF_8.toString());
            }
        } catch (Exception e) {
            result = "";
        }
        return result;
    }

    public static void sendExceptionMail(HttpServletRequest request, Exception ex) {
        ex.printStackTrace();
        StackTraceElement[] st = ex.getStackTrace();
        String StackTrace = "";
        for (StackTraceElement stackTraceElement : st) {
            StackTrace += stackTraceElement.toString() + "\n";
        }

        String contenuto = "";
        if (request != null) {
            HttpSession session = request.getSession(false);
            User curUser = null;
            if (session != null) {
                Object userToCast = session.getAttribute(GlobalHelper.kBeanUtente);
                if (userToCast != null) {
                    curUser = (User) userToCast;
                }
            }

            contenuto = ex.getClass().toString() + ": " + ex.getMessage() + "\n" + "\n" + StackTrace;
            contenuto += "<br><br>Link: " + request.getRequestURL() + "?" + request.getQueryString();
            if (curUser != null) {
                contenuto += "<br>User: ";
                if (curUser.getFirstName() != null) {
                    contenuto += curUser.getFirstName() + " ";
                }
                if (curUser.getLastName() != null) {
                    contenuto += curUser.getLastName();
                }
                if (curUser.getGroupsetId() != null) {
                    contenuto += "<br>GroupsetId: " + curUser.getGroupsetId();
                }
                if (curUser.getSavedSeason() != null) {
                    contenuto += "<br>SeasonId: " + curUser.getSavedSeason();
                }
            }
        } else {
            contenuto = ex.getClass().toString() + ": " + ex.getMessage() + "\n" + "\n" + StackTrace;
        }
        MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd"));
        mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), "<EMAIL>", null, null, null, null, "Errore sics.tv", contenuto, null, "");
    }

    public static BufferedImage getFieldByModule(Game game, String path, String module) {
        BufferedImage field = null;
        try {
            int wField = 8 * latoLungoField;
            int hField = 8 * latoCortoField;
            int dimEllipse = 40;

            if (game.getHomeColor() == null) {
                game.setHomeColor("255x255x0x0");
            }

            field = imageToBufferedImage(ImageIO.read(new File(path + "campo_2023_linee-h.png")).getScaledInstance(wField, hField, Image.SCALE_SMOOTH));
            Graphics2D fieldGraphics = (Graphics2D) field.createGraphics();
            fieldGraphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            ArrayList<Atleta> team1 = new ArrayList<>();
            for (Atleta atleta : game.getFormazione1().values()) {
                team1.add(atleta);
            }
            Collections.sort(team1, Atleta.getAtletaComparator());

            ModuleHelper.setPositionPlayersByModuleReport(team1, game.getHomeModule().replace("-", ""));
            for (Atleta player : team1) {
                sics.analysis.Punto point = player.getPosReport(0l, wField, hField, true, false);
                if (((player.getModule_position() != null && player.getModule_position() <= 11)) && point.getX() != -1 && point.getY() != -1) {
                    disegnaPallini(field, (int) point.getX(), (int) point.getY(), dimEllipse, game.getHomeColor());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return rotateImageBy(field, -90);
    }

    public static BufferedImage rotateImageBy(BufferedImage source, double degrees) {
        // The size of the original image
        int w = source.getWidth();
        int h = source.getHeight();
        // The angel of the rotation in radians
        double rads = Math.toRadians(degrees);
        // Some nice math which demonstrates I have no idea what I'm talking about
        // Okay, this calculates the amount of space the image will need in
        // order not be clipped when it's rotated
        double sin = Math.abs(Math.sin(rads));
        double cos = Math.abs(Math.cos(rads));
        int newWidth = (int) Math.floor(w * cos + h * sin);
        int newHeight = (int) Math.floor(h * cos + w * sin);

        // A new image, into which the original can be painted
        BufferedImage rotated = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = rotated.createGraphics();
        // The transformation which will be used to actually rotate the image
        // The translation, actually makes sure that the image is positioned onto
        // the viewable area of the image
        AffineTransform at = new AffineTransform();
        at.translate((newWidth - w) / 2, (newHeight - h) / 2);

        // And we rotate about the center of the image...
        int x = w / 2;
        int y = h / 2;
        at.rotate(rads, x, y);
        g2d.setTransform(at);
        // And we paint the original image onto the new image
        g2d.drawImage(source, 0, 0, null);
        g2d.dispose();

        return rotated;
    }

    public static String imageToBase64String(RenderedImage image, String type) {
        String ret = null;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, type, bos);
            byte[] bytes = bos.toByteArray();
            // Usa java.util.Base64 per la codifica
            ret = java.util.Base64.getEncoder().encodeToString(bytes);
            // La codifica standard di Base64 non aggiunge separatori di riga,
            // quindi la riga seguente potrebbe non essere più necessaria, ma la lascio per sicurezza
            ret = ret.replace(System.lineSeparator(), "");
        } catch (IOException e) {
            // È buona pratica gestire l'eccezione in modo più specifico o rilanciarla
            // avvolta in un'eccezione non controllata appropriata, se necessario.
            throw new RuntimeException("Errore durante la codifica dell'immagine in Base64", e);
        } finally {
            // È buona norma chiudere lo stream nel blocco finally
            try {
                bos.close();
            } catch (IOException e) {
                // Logga l'errore o gestiscilo come appropriato
                System.err.println("Errore nella chiusura di ByteArrayOutputStream: " + e.getMessage());
            }
        }
        return ret;
    }


    public static String getURLEconded(String link) {
        try {
            return URLEncoder.encode(link, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(TeamStats.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "Error";
    }

    public static String getURLDecoded(String link) {
        try {
            return URLDecoder.decode(link, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(TeamStats.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "Error";
    }

    public static Map<Long, Long> sortHashMapByValues(Map<Long, Long> passedMap) {
        List<Map.Entry<Long, Long>> entryList = new ArrayList<>(passedMap.entrySet());

        // Ordina la lista in base ai valori
        Collections.sort(entryList, new Comparator<Map.Entry<Long, Long>>() {
            @Override
            public int compare(Map.Entry<Long, Long> entry1, Map.Entry<Long, Long> entry2) {
                return entry2.getValue().compareTo(entry1.getValue());
            }
        });

        // Crea una LinkedHashMap ordinata per mantenere l'ordine
        Map<Long, Long> sortedMap = new LinkedHashMap<>();
        for (Map.Entry<Long, Long> entry : entryList) {
            sortedMap.put(entry.getKey(), entry.getValue());
        }

        return sortedMap;
    }

    public static boolean checkValidExport(List<Azione> actionsToExport) {
        boolean result = true;
        try {
            long totalLenght = 0;//in ms
            if (!actionsToExport.isEmpty()) {
                for (Azione action : actionsToExport) {
                    if ((action.getmEnd() - action.getmStart()) < 1000 && (action.getmEnd() - action.getmStart()) > MAX_SINGLE_CLIP_LENGHT) {
                        result = false;
                        break;
                    }
                    totalLenght += (action.getmEnd() - action.getmStart());
                }
                if (result) {
                    if (totalLenght < MIN_SINGLE_CLIP_LENGHT || totalLenght > MAX_TOTAL_LENGHT) {
                        result = false;
                    }
                }
            } else {
                result = false;
            }
        } catch (Exception e) {
            result = false;
        }
        return result;
    }

    public static long getActionsTotalDuration(List<Azione> actions) {
        try {
            long totalLenght = 0;//in ms
            if (!actions.isEmpty()) {
                for (Azione action : actions) {
                    totalLenght += (action.getmEnd() - action.getmStart());
                }

                return totalLenght;
            } else {
                return 0;
            }
        } catch (Exception e) {
            return -1;
        }
    }

    public static String generateClipPayload(String video_key, String num_export, String video_res, String multi_pack, String start_action, String dur_action, String user_id, String groupset_id, String timestamp, String name_export) {
        String payload = "{\n"
                + "\"video_key\": \"" + video_key + "\",\n"
                + "\"num_export\": \"" + num_export + "\",\n"
                + "\"video_res\": \"" + video_res + "\",\n"
                + "\"multi_pack\": \"" + multi_pack + "\",\n"
                + "\"start_action\": \"" + start_action + "\",\n"
                + "\"dur_action\": \"" + dur_action + "\",\n"
                + "\"user_id\": \"" + user_id + "\",\n"
                + "\"groupset_id\": \"" + groupset_id + "\",\n"
                + "\"timestamp\": \"" + timestamp + "\",\n"
                + "\"test_case\": \"\",\n"
                + "\"name_export\": \"" + name_export + "\"\n"
                + "}";
        return payload;
    }

    public static String generateConcatZipPayload(ArrayListSynchro<String> list, String name) {
        String payload = "{\n";
        payload += "\"name\": \"" + name + "\",\n";
        payload += "\"test_case\": \"\",\n";
        payload += "\"clips\": [";
        for (int i = 0; i < list.size(); i++) {
            if (i + 1 < list.size()) {
                payload += "\"" + list.get(i) + "\",\n";
            } else {
                payload += "\"" + list.get(i) + "\"\n]}";
            }
        }
        return payload;
    }

    private static String cicloUltimaAccentata(String p, String find, String to) {

        int occorrenze = StringUtils.countMatches(p, find);
        for (int i = 0; i < occorrenze; i++) {
            int index = p.indexOf(find);
            if (index == p.length() - 1) {
                // se è 1 significa che è l'ultima
                p = p.substring(0, p.length() - 1) + to;
            } else {
                //NIENTE
                //p = p.substring(0, index) + to.replace("'", "") + p.substring(index + 1, p.length());
            }
        }
        return p;
    }

    /**
     * Convert accented letters to the equivalent unaccented letter.
     *
     * @param accented string to convert
     *
     * @return string with accented letters converted to equivalent unaccented
     * letters
     */
    public static String removeAccents(String accented) {
//        if (accented.contains("Mömmö")) {
//            String aaa = "";
//        }
        accented = sostituisciSoloUltimaLettereAccentata(accented);
//        accented = accented.toUpperCase();
        // convert accented chars to equivalent unaccented char + dead char accent pair.
        // See http://www.unicode.org/unicode/reports/tr15/tr15-23.html no understand the NFD transform.
        final String normalized = Normalizer.normalize(accented, Normalizer.Form.NFD);
        // remove the dead char accents, leaving just the unaccented chars.
        // Stripped string should have the same length as the original accented String.
//        String q0 = normalized.replaceAll("[^\\p{ASCII}]", "");
//        String q1 = normalized.replaceAll("\\p{M}", "");
//        String q3 = StringUtils.stripAccents(normalized);
//        String q4 = StringUtils.stripAccents(accented);

        StringBuilder sb = new StringBuilder(accented.length());
        for (int i = 0; i < normalized.length(); i++) {
            char c = normalized.charAt(i);
            if (Character.getType(c) != Character.NON_SPACING_MARK) {
                sb.append(c);
            }
        }
        String ret = sb.toString();
        String a1 = ret.replace("ł", "l").replace("Ł", "L").replace("Đ", "DJ").replace("đ", "dj");
        String result = transliterate(a1);
        if (!result.isEmpty()) {
            return result;
        } else {
            return a1;
        }
    }

    private static String transliterate(String value) {
        String a1 = "";
        try {
//            String german_DIN_5007_2Rules = "$beforeLower = [[:Mn:][:Me:]]* [:Lowercase:];\n"
//                    + "\\u00e4 > ae;\n"
//                    + "\\u00f6 > oe;\n"
//                    + "\\u00fc > ue;\n"
//                    + "\\u00c4 } $beforeLower > Ae;\n"
//                    + "\\u00d6 } $beforeLower > Oe;\n"
//                    + "\\u00dc } $beforeLower > Ue;\n"
//                    + "\\u00c4 > AE;\n"
//                    + "\\u00d6 > OE;\n"
//                    + "\\u00dc > UE;\n";
            Transliterator accentsConverter = Transliterator.getInstance("Latin-ASCII;NFD; [:M:] Remove; NFC; [^\\p{ASCII}] Remove"/*.replace("::NFD();", german_DIN_5007_2Rules + "\n::NFD();")*/);
//            a1 = sostituisciLettereAccentate(value);
            a1 = accentsConverter.transliterate(value);
        } catch (Exception e) {
            System.err.println("Exception transliterate: " + e.getMessage());
            a1 = "";
//            LoggerHelper.severe(e, "ERRORE transliterate");
        }
        return a1;
    }

    public static String getTimeString(long millis) {
        String result = "";
        try {
            Date d = new Date(millis);
            SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss.SSS");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
            result = sdf.format(d);
        } catch (Exception e) {
            System.err.println("Exception getTimeString: " + e.getMessage());
            result = "";
        }
        return result;
    }

    public static String getTimestamp() {
        String result = "";
        try {
            SimpleDateFormat sdfDate = new SimpleDateFormat("ddMMyyyy-HHmmss");
            Date now = new Date();
            result = sdfDate.format(now);
        } catch (Exception e) {
            System.err.println("Exception getTimestamp: " + e.getMessage());
            result = "";
        }
        return result;
    }

    private static String sostituisciSoloUltimaLettereAccentata(String text) {
        String result = text.toLowerCase();
        try {
            String[] parti = text.split(" ");
            result = "";

            for (String p : parti) {
                String find = "à";
                String to = "a'";
                p = cicloUltimaAccentata(p, find, to);
                find = "é";
                to = "e'";
                p = cicloUltimaAccentata(p, find, to);
                find = "è";
                to = "e'";
                p = cicloUltimaAccentata(p, find, to);
                find = "ì";
                to = "i'";
                p = cicloUltimaAccentata(p, find, to);
                find = "ò";
                to = "o'";
                p = cicloUltimaAccentata(p, find, to);
                find = "ù";
                to = "u'";
                p = cicloUltimaAccentata(p, find, to);

                if (!result.isEmpty()) {
                    result += " ";
                }
                result += p;
            }
        } catch (Exception e) {
            System.err.println("Exception sostituisciSoloUltimaLettereAccentata: " + e.getMessage());
//            LoggerHelper.severe(ex, "sostituisciLettereAccentate");
        }
        return result;
    }

    public static String msecToMinSec(Integer count, Boolean showHours, Boolean showSeconds, Boolean showDecimi) {
        String result = "";
        try {
            if (showHours) {
                Integer hours = (int) Math.round(((count / 1000) / 60 / 60));
                Integer minutes = ((count / 1000) / 60) % 60;
                Integer seconds = (count / 1000) % 60;

                if (showSeconds) {
                    String decimiString = "";
                    if (showDecimi) {
                        Integer decimi = (count % 1000) / 10;
                        decimiString = "." + truncateString("0" + Integer.toString(Math.round(decimi)), 2);
                    } else if (count % 1000 >= 500) {
                        seconds += 1;
                        if (seconds == 60) {
                            seconds = 0;
                            minutes += 1;
                            if (minutes == 60) {
                                minutes = 0;
                                hours += 1;
                            }
                        }
                    }
                    Integer minLong = Math.round(minutes);
                    String minutesString = Integer.toString(minLong);
                    if (minLong < 100) {
                        minutesString = truncateString("0" + Integer.toString(minLong), 2);
                    }
                    result = truncateString("0" + Integer.toString(hours), 2) + ":" + minutesString + ":" + truncateString("0" + Integer.toString(Math.round(seconds)), 2) + decimiString;
                } else {
                    if (count % 1000 >= 500) {
                        seconds += 1;
                    }
                    if (seconds >= 30) {
                        minutes += 1;
                        if (minutes == 60) {
                            minutes = 0;
                            hours += 1;
                        }
                    }
                    result = truncateString("0" + Integer.toString(hours), 2) + ":" + truncateString("0" + Integer.toString(Math.round(minutes)), 2);
                }
            } else {
                Integer minutes = (int) Math.round((count / 1000) / 60);
                Integer seconds = (count / 1000) % 60;

                if (showSeconds) {
                    String decimiString = "";
                    if (showDecimi) {
                        Integer decimi = (count % 1000) / 10;
                        decimiString = "." + truncateString("0" + Integer.toString(Math.round(decimi)), 2);
                    } else if (count % 1000 >= 500) {
                        seconds += 1;
                        if (seconds == 60) {
                            seconds = 0;
                            minutes += 1;
                        }
                    }
                    Integer minLong = Math.round(minutes);
                    String minutesString = Integer.toString(minLong);
                    if (minLong < 100) {
                        minutesString = truncateString("0" + Integer.toString(minLong), 2);
                    }

                    result = minutesString + ":" + truncateString("0" + Integer.toString(Math.round(seconds)), 2) + decimiString;
                } else {
                    if (count % 1000 >= 500) {
                        seconds += 1;
                    }
                    if (seconds >= 30) {
                        minutes += 1;
                    }
                    Integer minLong = Math.round(minutes);
                    String minutesString = Integer.toString(minLong);
                    if (minLong < 100) {
                        minutesString = truncateString("0" + Integer.toString(minLong), 2);
                    }
                    result = minutesString;
                }
            }
        } catch (Exception e) {
            System.err.println("ERROR msecToMinSec: " + e.getMessage());
//            LoggerHelper.severe(e, "GlobalHelper.msecToMinSec");
        }
        return result;
    }

    public static String escapeJson(String raw) {
        String escaped = raw;
        escaped = escaped.replace("\\", "\\\\");
        escaped = escaped.replace("\"", "\\\"");
        escaped = escaped.replace("\b", "\\b");
        escaped = escaped.replace("\f", "\\f");
        escaped = escaped.replace("\n", "\\n");
        escaped = escaped.replace("\r", "\\r");
        escaped = escaped.replace("\t", "\\t");
        // TODO: escape other non-printing characters using uXXXX notation
        return escaped;
    }

    public static void removeWrongGironi(List<Competition> gironi) {
        Map<Long, List<Competition>> groupedByCompetition = new HashMap<>();
        for (Competition girone : gironi) {
            if (girone.getId() != null) {
                groupedByCompetition.putIfAbsent(girone.getId(), new ArrayList<Competition>());
                groupedByCompetition.get(girone.getId()).add(girone);
            }
        }

        List<Competition> gironiToRemove = new ArrayList<>();
        for (Long competitionId : groupedByCompetition.keySet()) {
            boolean remove = false;
            for (Competition girone : groupedByCompetition.get(competitionId)) {
                if (girone.getGroupId() != null && groupedByCompetition.get(competitionId).size() == 1 && Long.compare(girone.getGroupId(), 0) == 0) {
                    remove = true;
                }
            }

            if (remove) {
                gironiToRemove.addAll(groupedByCompetition.get(competitionId));
            }
        }

        if (!gironiToRemove.isEmpty()) {
            gironi.removeAll(gironiToRemove);
        }
    }

    public static double distance(sics.model.Punto A, sics.model.Punto B) {
        if (A != null && !A.isDefault && B != null && !B.isDefault) {
            double d1 = A.x - B.x;
            double d2 = A.y - B.y;
            return Math.sqrt(d1 * d1 + d2 * d2);
        } else {
            return 0;
        }
    }

    public static Color getColoreInContrasto(Color c) {
        Color txtColor = Color.WHITE;
        if (c.getGreen() > 190) {
            txtColor = Color.BLACK;
        } else if (c.getRed() > 190) {
            if ((c.getGreen() > 190 || c.getBlue() > 190) || (c.getGreen() > 127 || c.getBlue() > 127)) {
                txtColor = Color.BLACK;
            }
        } else if (c.getBlue() > 190) {
            if ((c.getGreen() > 190 || c.getRed() > 190) || (c.getGreen() > 128 || c.getRed() > 128)) {
                txtColor = Color.BLACK;
            }
        }
        return txtColor;
    }

    public static Double angoloTraPunti(sics.model.Punto p0, sics.model.Punto p1) {
        Double angolo = null;
        try {
            double deltaX = p1.x - p0.x;
            double deltaY = p1.y - p0.y;
            // Utilizza atan2 per calcolare l'angolo
            angolo = Math.atan2(deltaY, deltaX);
            angolo = Math.toDegrees(angolo);
        } catch (Exception ex) {
            reportError(ex);
        }
        return angolo;
    }

    public static int zonaAngolo(double angle) {
        if (angle <= 30 && angle >= -30) {
            // verticale
            return 3;
        } else if (angle > 30 && angle <= 75) {
            // diagonale avanti dx
            return 4;
        } else if (angle < -30 && angle >= -75) {
            // diagonale avanti sx
            return 2;
        } else if (angle > 75 && angle <= 105) {
            // laterale dx
            return 5;
        } else if (angle < -75 && angle >= -105) {
            // laterale sx
            return 1;
        } else if (angle > 105 || angle < -105) {
            // indietro
            return 6;
        } else {
            return -1;
        }
    }

    /**
     *
     * @param teamLeftForHalf contiene la squadra a sinistra per ogni tempo
     * @param a
     * @param matchData
     * @param forzaSquadreASx mette tutte le azioni come se la squadra
     * attaccasse verso destra
     * @param forza forza la rotazione sempre (? per importazione??)
     * @return array con p1 (partenza), p2 (arrivo), p3 (Shot3D) normalizzati
     * @throws java.lang.CloneNotSupportedException
     */
    public static Punto[] normalizzaPosizioneAzione(Map<String, String> teamLeftForHalf, Azione a, Game matchData, Boolean forzaSquadreASx, Boolean forza) throws CloneNotSupportedException {
        // DAFARE modificare la posizione dell'azione oppure ritornare il punto modificato?
        sics.model.Punto[] p = {a.getmActionPos().clone(), a.getmActionPosEnd().clone(), a.getmShot3D().clone()};
        try {
            //
            Boolean ruota = false;
            // vedi DSFilter normalizzaPosizioneAzioni mMultimatch
            Punto pos1 = p[0];
            Punto pos2 = p[1];
            Punto pos3 = p[2];
//            if (pos2.isDefault()) {
//                pos2 = null;
//                p[1] = null;
//            }
//            if (pos3.isDefault()) {
//                pos3 = null;
//                p[2] = null;
//            }
            if (forza) {
                ruota = true;
            } else {
                String half = "" + a.getHalf();
                if (teamLeftForHalf.containsKey(half)) {
                    if (forzaSquadreASx) {
                        // se è multimatch allora devo mettere tutte le azioni come se la squadra attaccasse verso destra
                        if (!teamLeftForHalf.get(half).equals(a.getmTeam())) {
                            ruota = true;
                        }
                    } else if (matchData.getHomeTeam().equals(teamLeftForHalf.get("1"))) {
                        if (matchData.getAwayTeam().equals(a.getmTeam()) && teamLeftForHalf.get(half).equals(a.getmTeam())) {
                            // se la squadra dell'azione è il team2 ed è anche la squadra che attacca da sinistra nell'half di riferimento allora devo normalizzare
                            ruota = true;
                        } else if (matchData.getHomeTeam().equals(a.getmTeam()) && !teamLeftForHalf.get(half).equals(a.getmTeam())) {
                            // se la squadra dell'azione è il team1 e non è la squadra che attacca da sinistra allora devo normalizzare
                            ruota = true;
                        }
                    } else if (matchData.getAwayTeam().equals(teamLeftForHalf.get("1"))) {
                        if (matchData.getHomeTeam().equals(a.getmTeam()) && teamLeftForHalf.get(half).equals(a.getmTeam())) {
                            // se la squadra dell'azione è il team2 ed è anche la squadra che attacca da sinistra nell'half di riferimento allora devo normalizzare
                            ruota = true;
                        } else if (matchData.getAwayTeam().equals(a.getmTeam()) && !teamLeftForHalf.get(half).equals(a.getmTeam())) {
                            // se la squadra dell'azione è il team1 e non è la squadra che attacca da sinistra allora devo normalizzare
                            ruota = true;

                        }
                    }
                }
            }
            if (ruota) {
                pos1.x = GlobalHelper.kLatoLungoCampo - pos1.x;
                pos1.y = GlobalHelper.kLatoCortoCampo - pos1.y;
                if (!pos2.isDefault()) {
                    pos2.x = GlobalHelper.kLatoLungoCampo - pos2.x;
                    pos2.y = GlobalHelper.kLatoCortoCampo - pos2.y;
                }
//                if (!pos3.isDefault()) {
//                    pos3.x = GlobalHelper.kLatoLungoCampo - pos3.x;
//                    pos3.y = GlobalHelper.kLatoCortoCampo - pos3.y;
//                }
            }
        } catch (Exception ex) {
            reportError(ex);
        }
        return p;
    }

    public static Long getCorrectSeasonId(List<Season> seasons, Long currentSeasonId) {
        boolean onlySolar = true;
        for (Season season : seasons) {
            if (season.getId() != null && season.getId() >= 2000) {
                onlySolar = false;
                break;
            }
        }

        if (onlySolar) {
            for (Season season : seasons) {
                if (Long.compare(season.getId(), currentSeasonId - 1999) == 0) {
                    return season.getId();
                }
            }
            for (Season season : seasons) {
                if (Long.compare(season.getId(), currentSeasonId - 2000) == 0) {
                    return season.getId();
                }
            }
        } else {
            for (Season season : seasons) {
                if (Long.compare(season.getId(), currentSeasonId) == 0) {
                    return season.getId();
                }
            }
        }

        return currentSeasonId;
    }

    // Verifica se il punto è all'interno di una delle due aree
    public static boolean checkInArea(Punto p) {
        boolean result = false;
        try {
            if (p.x >= 0 && p.x <= 16.5 && p.y >= 13.84 && p.y <= 54.16
                    || p.x >= 88.5 && p.x <= GlobalHelper.kLatoLungoCampo && p.y >= 13.84 && p.y <= 54.16) {
                return true;
            } else {
                return false;
            }
        } catch (Exception ex) {
            reportError(ex);
        }
        return result;
    }

    public static Float calcolaDistanzaDalFondo(Azione az, Integer teamSx, Integer teamSx2, Game fixture) {
        Float result = GlobalHelper.kLatoLungoCampo.floatValue();
        try {
            Integer tSx = teamSx;
            if (az.getmHalf() == 3 || az.getmHalf() == 4) {
                tSx = teamSx2;
            }
            if (az.getIdTeam() == fixture.getHomeTeamId()) {
                // team 1
                if (az.getmHalf() == 1 || az.getmHalf() == 3) {
                    // 1T o 1TS
                    if (tSx == 1) {
                        return GlobalHelper.kLatoLungoCampo - az.getmActionPos().x;
                    } else {
                        return az.getmActionPos().x;
                    }
                } else // 2T o 2TS
                if (tSx == 1) {
                    return az.getmActionPos().x;
                } else {
                    return GlobalHelper.kLatoLungoCampo - az.getmActionPos().x;
                }
            } else // team 2
            if (az.getmHalf() == 1 || az.getmHalf() == 3) {
                // 1T o 1TS
                if (tSx == 2) {
                    return GlobalHelper.kLatoLungoCampo - az.getmActionPos().x;
                } else {
                    return az.getmActionPos().x;
                }
            } else // 2T o 2TS
            if (tSx == 2) {
                return az.getmActionPos().x;
            } else {
                return GlobalHelper.kLatoLungoCampo - az.getmActionPos().x;
            }
        } catch (Exception ex) {
            reportError(ex);
        }
        return result;
    }

    public static Float calcolaDistanzaDaLatoCorto(Azione az, Integer teamSx, Integer teamSx2, Game fixture) {
        Float result = GlobalHelper.kLatoCortoCampo.floatValue();
        try {
            Integer tSx = teamSx;
            if (az.getmHalf() == 3 || az.getmHalf() == 4) {
                tSx = teamSx2;
            }
            if (az.getIdTeam() == fixture.getHomeTeamId()) {
                // team 1
                if (az.getmHalf() == 1 || az.getmHalf() == 3) {
                    // 1T o 1TS
                    if (tSx == 1) {
                        return GlobalHelper.kLatoCortoCampo - az.getmActionPos().y;
                    } else {
                        return az.getmActionPos().y;
                    }
                } else // 2T o 2TS
                if (tSx == 1) {
                    return az.getmActionPos().y;
                } else {
                    return GlobalHelper.kLatoCortoCampo - az.getmActionPos().y;
                }
            } else // team 2
            if (az.getmHalf() == 1 || az.getmHalf() == 3) {
                // 1T o 1TS
                if (tSx == 2) {
                    return GlobalHelper.kLatoCortoCampo - az.getmActionPos().y;
                } else {
                    return az.getmActionPos().y;
                }
            } else // 2T o 2TS
            if (tSx == 2) {
                return az.getmActionPos().y;
            } else {
                return GlobalHelper.kLatoCortoCampo - az.getmActionPos().y;
            }
        } catch (Exception ex) {
            reportError(ex);
        }
        return result;
    }

    public static boolean isActionInFirstHalf(Azione action) {
        return action.getmHalf() == 1;
    }

    public static BufferedImage logoForUpload(File sourceFile, int width, int height) {

        BufferedImage originalImage = null;
        BufferedImage resizedImage = null;
        try {
            originalImage = ImageIO.read(sourceFile);
            float rapporto = (float) width / height;
            float rapImg = (float) originalImage.getWidth() / originalImage.getHeight();
            if (rapImg < rapporto) {
                // width img + stretta, adattare larghezza
                // wImg : hImg = width : height
                int wImg = (int) ((float) (originalImage.getHeight() * width) / height);
                int pad = (wImg - originalImage.getWidth()) / 2;
                if (pad == 0) {
                    resizedImage = ImageIO.read(sourceFile);
                } else {
                    resizedImage = Scalr.pad(originalImage, pad, new Color(255, 255, 255, 0));
                    //resizedImage = Scalr.crop(resizedImage, pad, 0, originalImage.getWidth(), resizedImage.getHeight());
                    resizedImage = Scalr.crop(resizedImage, 0, pad, resizedImage.getWidth(), originalImage.getHeight());
                }
            } else if (rapImg > rapporto) {
                // width img + larga, adattare altezza
                // wImg : hImg = width : height
                int hImg = (int) ((float) (originalImage.getWidth() * height) / width);
                int pad = (hImg - originalImage.getHeight()) / 2;
                if (pad == 0) {
                    resizedImage = ImageIO.read(sourceFile);
                } else {
                    resizedImage = Scalr.pad(originalImage, pad, new Color(255, 255, 255, 0));
                    resizedImage = Scalr.crop(resizedImage, pad, 0, originalImage.getWidth(), resizedImage.getHeight());
                }
            } else {
                resizedImage = ImageIO.read(sourceFile);
            }
            resizedImage = Scalr.resize(resizedImage, Scalr.Method.BALANCED, Scalr.Mode.AUTOMATIC, width, height);

        } catch (IOException | ImagingOpException | IllegalArgumentException e) {
            reportError(e);
        } finally {
            originalImage.flush();
        }
        return resizedImage;
    }

    public static boolean insertCompetitionLogo(File img, Competition competition, User curUser, UserService mService) {
        boolean res = false;
        BufferedImage resizedImage = null;
        File finalFile = null;
        try {
            String pathDest = img.getAbsolutePath();
            resizedImage = GlobalHelper.logoForUpload(img, 120, 120);
            String pathDir = img.getParent();

            String name = competition.getName().replaceAll(" ", "_").replaceAll("[^a-zA-Z0-9_]", "");
            if (competition.getId() > 0) {
                name += "_" + competition.getId().toString();
            }
            if (Long.compare(curUser.getGroupsetId(), 2) == 0) {
                pathDest = pathDir + File.separator + name + ".png";
            } else {
                pathDest = pathDir + File.separator + curUser.getGroupsetId() + File.separator + name + ".png";
            }

            finalFile = new File(pathDest);
            if (!finalFile.exists()) {
                finalFile.mkdirs();
            }
            ImageIO.write(resizedImage, "png", finalFile);

            UploaderLogo upl = new UploaderLogo();
            String keyName = upl.upload(finalFile, curUser.getGroupsetId(), 1, null);
            if (!keyName.isEmpty()) {
                String logo;
                if (Long.compare(curUser.getGroupsetId(), 2) == 0) {
                    logo = name;
                } else {
                    logo = curUser.getGroupsetId() + "/" + name;
                }
                mService.updateCompetitionLogo(competition.getId(), logo);
                res = true;
            }

            resizedImage.flush();
            finalFile.delete();
        } catch (IOException | IllegalArgumentException ex) {
            reportError(ex);
        } finally {
            if (resizedImage != null) {
                resizedImage.flush();
            }
        }
        return res;
    }

    public static boolean insertTeamLogo(File img, Team team, User curUser, UserService mService) {
        boolean res = false;
        BufferedImage resizedImage = null;
        File finalFile = null;
        try {
            String pathDest = img.getAbsolutePath();
            resizedImage = GlobalHelper.logoForUpload(img, 120, 120);
            String pathDir = img.getParent();

            String name = team.getName().replaceAll(" ", "_").replaceAll("[^a-zA-Z0-9_]", "");
            if (team.getId() > 0) {
                name += "_" + team.getId().toString();
            }
            if (Long.compare(curUser.getGroupsetId(), 2) == 0) {
                pathDest = pathDir + File.separator + name + ".png";
            } else {
                pathDest = pathDir + File.separator + curUser.getGroupsetId() + File.separator + name + ".png";
            }

            finalFile = new File(pathDest);
            if (!finalFile.exists()) {
                finalFile.mkdirs();
            }
            ImageIO.write(resizedImage, "png", finalFile);

            UploaderLogo upl = new UploaderLogo();
            String keyName = upl.upload(finalFile, curUser.getGroupsetId(), 2, null);
            if (!keyName.isEmpty()) {
                String logo;
                if (Long.compare(curUser.getGroupsetId(), 2) == 0) {
                    logo = name;
                } else {
                    logo = curUser.getGroupsetId() + "/" + name;
                }
                mService.updateTeamLogo(team.getId(), logo);
                res = true;
            }

            resizedImage.flush();
            finalFile.delete();
        } catch (IOException | IllegalArgumentException ex) {
            reportError(ex);
        } finally {
            if (resizedImage != null) {
                resizedImage.flush();
            }
        }
        return res;
    }

    public static boolean insertPlayerPhoto(File img, Atleta player, User curUser, UserService mService) {
        boolean res = false;
        BufferedImage resizedImage = null;
        File finalFile = null;
        try {
            String pathDest = img.getAbsolutePath();
            resizedImage = GlobalHelper.logoForUpload(img, 120, 120);
            String pathDir = img.getParent();

            String name = player.getKnown_name().replaceAll(" ", "_").replaceAll("[^a-zA-Z0-9_]", "");
            if (player.getId() > 0) {
                name += "_" + player.getId().toString();
            }
            if (Long.compare(curUser.getGroupsetId(), 2) == 0) {
                pathDest = pathDir + File.separator + name + ".png";
            } else {
                pathDest = pathDir + File.separator + curUser.getGroupsetId() + File.separator + name + ".png";
            }

            finalFile = new File(pathDest);
            if (!finalFile.exists()) {
                finalFile.mkdirs();
            }
            ImageIO.write(resizedImage, "png", finalFile);

            UploaderLogo upl = new UploaderLogo();
            String keyName = upl.upload(finalFile, curUser.getGroupsetId(), 0, null);
            if (!keyName.isEmpty()) {
                String logo;
                if (Long.compare(curUser.getGroupsetId(), 2) == 0) {
                    logo = name;
                } else {
                    logo = curUser.getGroupsetId() + "/" + name;
                }
                mService.updatePlayerPhoto(player.getId().longValue(), logo);
                res = true;
            }

            resizedImage.flush();
            finalFile.delete();
        } catch (IOException | IllegalArgumentException ex) {
            reportError(ex);
        } finally {
            if (resizedImage != null) {
                resizedImage.flush();
            }
        }
        return res;
    }

    public static boolean insertPlayerAgentPhoto(File img, PlayerAgent agent, User curUser, UserService mService) {
        boolean res = false;
        BufferedImage resizedImage = null;
        File finalFile = null;
        try {
            String pathDest = img.getAbsolutePath();
            resizedImage = GlobalHelper.logoForUpload(img, 120, 120);
            String pathDir = img.getParent();

            String name = agent.getName().replaceAll(" ", "_").replaceAll("[^a-zA-Z0-9_]", "");
            if (agent.getId() > 0) {
                name += "_" + agent.getId().toString();
            }
            if (Long.compare(curUser.getGroupsetId(), 2) == 0) {
                pathDest = pathDir + File.separator + name + ".png";
            } else {
                pathDest = pathDir + File.separator + curUser.getGroupsetId() + File.separator + name + ".png";
            }

            finalFile = new File(pathDest);
            if (!finalFile.exists()) {
                finalFile.mkdirs();
            }
            ImageIO.write(resizedImage, "png", finalFile);

            UploaderLogo upl = new UploaderLogo();
            String keyName = upl.upload(finalFile, curUser.getGroupsetId(), 4, null);
            if (!keyName.isEmpty()) {
                String logo;
                if (Long.compare(curUser.getGroupsetId(), 2) == 0) {
                    logo = name;
                } else {
                    logo = curUser.getGroupsetId() + "/" + name;
                }
                mService.updatePlayerAgentPhoto(agent.getId(), logo);
                res = true;
            }

            resizedImage.flush();
            finalFile.delete();
        } catch (IOException | IllegalArgumentException ex) {
            reportError(ex);
        } finally {
            if (resizedImage != null) {
                resizedImage.flush();
            }
        }
        return res;
    }

    public static String removeUnwantedCharacter(String text) {
        return removeUnwantedCharacter(text, false);
    }

    public static String removeUnwantedCharacter(String text, boolean keepSpaces) {
        text = text.replaceAll(" ", "_").replaceAll("[^a-zA-Z0-9_]", "");
        if (keepSpaces) {
            text = text.replaceAll("_", " ");
        }
        return text;
    }

    public static boolean isLocalEnvironment() {
        File file = new File("C:/lavori/sicstv");
        return file.exists();
    }

    public static void updateUserActionsCounter(HttpSession session, UserAction action) {
        UserActions userActions = (UserActions) session.getAttribute(GlobalHelper.kActions);
        if (userActions == null) {
            userActions = new UserActions();
            session.setAttribute(GlobalHelper.kActions, userActions);
        }

        switch (action) {
            case VIDEO_STREAM:
                userActions.setVideoStream(userActions.getVideoStream() + 1);
                userActions.setTotal(userActions.getTotal() + 1);
                break;
            case LOGIN_CORRECT:
                userActions.setLoginCorrect(userActions.getLoginCorrect() + 1);
                userActions.setTotal(userActions.getTotal() + 1);
                break;
            case LOGIN_ERROR:
                userActions.setLoginError(userActions.getLoginError() + 1);
                userActions.setTotal(userActions.getTotal() + 1);
                break;
            case LICENSE_EXPIRED:
                userActions.setLicenseExpired(userActions.getLicenseExpired() + 1);
                userActions.setTotal(userActions.getTotal() + 1);
                break;
            case NEW_PLAYLIST:
                userActions.setPlaylistTv(userActions.getPlaylistTv() + 1);
                userActions.setTotal(userActions.getTotal() + 1);
                break;
            case VIDEO_DOWNLOAD:
                userActions.setVideoDownload(userActions.getVideoDownload() + 1);
                userActions.setTotal(userActions.getTotal() + 1);
                break;
            case NEW_WATCHLIST:
                userActions.setWatchlist(userActions.getWatchlist() + 1);
                userActions.setTotal(userActions.getTotal() + 1);
                break;
            case DOWNLOAD_XML_JSON:
                userActions.setDownloadXmlJson(userActions.getDownloadXmlJson() + 1);
                userActions.setTotal(userActions.getTotal() + 1);
                break;
            case START_EXPORT:
                userActions.setExport(userActions.getExport() + 1);
                userActions.setTotal(userActions.getTotal() + 1);
                break;
        }
    }

    public static String getDatabase(String seasonIds) {
        String database = "";
        if (useFrammentedDatabase) {
            Long seasonId = null;
            if (StringUtils.isNotBlank(seasonIds)) {
                if (StringUtils.contains(seasonIds, ",")) {
                    // controllo che siano tutte la stessa stagione altrimenti non posso usare quello frammentato
                    List<Long> seasonIdList = new ArrayList<>();
                    for (String tmpSeasonId : StringUtils.split(seasonIds, ",")) {
                        Long value = Long.valueOf(tmpSeasonId);
                        if (value < 2000) {
                            value += 2000;
                        }
                        if (!seasonIdList.contains(value)) {
                            seasonIdList.add(value);
                        }
                    }

                    if (seasonIdList.size() == 1) {
                        seasonId = seasonIdList.get(0);
                    }
                } else {
                    if (NumberUtils.isNumber(seasonIds)) {
                        seasonId = Long.valueOf(seasonIds);
                    }
                }

                if (seasonId != null) {
                    if (seasonId < 2000) {
                        seasonId += 2000;
                    }
                    database = "videomatch_" + seasonId + ".";
                }
            }
        }

        return database;
    }

    public static void adjustQueryParams(TreeMap map) {
        if (!map.containsKey("database")) {
            map.put("database", "");
        }
    }

    public static String formatNumber(double number) {
        if (number >= 1000000) {
            double millions = number / 1000000;
            // If the value is a whole number (e.g., 3.0), format without decimals.
            if (millions % 1 == 0) {
                return String.format("%.0fM", millions);
            } else {
                return String.format("%.1fM", millions);
            }
        } else if (number >= 1000) {
            double thousands = number / 1000;
            if (thousands % 1 == 0) {
                return String.format("%.0fk", thousands);
            } else {
                return String.format("%.1fk", thousands);
            }
        } else {
            return String.valueOf(number);
        }
    }
}
