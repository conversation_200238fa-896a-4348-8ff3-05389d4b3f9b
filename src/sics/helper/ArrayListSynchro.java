package sics.helper;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.function.Consumer;

public class ArrayListSynchro<V> extends ArrayList<V> {

    public ArrayListSynchro() {
        super();
    }

    @Override
    public synchronized boolean isEmpty() {
        return super.isEmpty();
    }

    @Override
    public synchronized boolean add(V value) {
        return super.add(value);
    }

    public synchronized boolean addAll(ArrayList<? extends V> m) {
        return super.addAll(m);
    }

    public synchronized V get(int index) {
        return super.get(index);
    }

    public synchronized void clear() {
        super.clear();
    }

    @Override
    public synchronized boolean contains(Object o) {
        return super.contains(o);
    }

    @Override
    public synchronized V remove(int index) {
        return super.remove(index);
    }

    @Override
    public synchronized int size() {
        return super.size();
    }

    @Override
    public synchronized Iterator<V> iterator() {
        return super.iterator();
    }

    @Override
    public synchronized void forEach(Consumer<? super V> action) {
        super.forEach(action);
    }

}