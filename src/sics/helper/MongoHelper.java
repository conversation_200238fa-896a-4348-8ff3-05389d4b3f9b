package sics.helper;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.mongodb.MongoClient;
import static com.mongodb.MongoClient.getDefaultCodecRegistry;
import com.mongodb.MongoClientOptions;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import static com.mongodb.client.model.Accumulators.addToSet;
import static com.mongodb.client.model.Accumulators.max;
import static com.mongodb.client.model.Accumulators.min;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Indexes.ascending;
import static com.mongodb.client.model.Indexes.descending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import org.bson.codecs.configuration.CodecProvider;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.ClassModel;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import sics.domain.Cache;
import static com.mongodb.client.model.Aggregates.*;
import com.mongodb.client.model.BsonField;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.lt;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.or;
import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import static java.util.Collections.singletonList;
import java.util.Comparator;
import java.util.Date;
import java.util.Locale;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpSession;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang.time.DateUtils;
import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;
import org.bson.types.ObjectId;
import sics.domain.CacheFieldRange;
import sics.domain.CacheFieldValues;
import sics.domain.Translation;
import sics.domain.User;
import sics.domain.UserActions;

/**
 *
 * <AUTHOR>
 */
public class MongoHelper {

    // mongodb
    private static MongoClient mongoClient;
    private static MongoDatabase mongoDatabase;
    private static Session session;
    private static int assignedPort;

    // json
    private static ObjectMapper mapper;

    private static final Logger logger = Logger.getLogger(MongoHelper.class.getName());

    // in questo se accendi mongodb in locale utilizza il tuo server in locale
    // altrimenti si collega al mongodb nel server
    private static void init() {
        try {
            // json
            mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            SimpleModule module = new SimpleModule();
            module.addSerializer(Date.class, new DateJsonSerializer());
            module.addDeserializer(Date.class, new DateJsonDeserializer());
            module.addSerializer(ObjectId.class, new ObjectIdJsonSerializer());
            module.addDeserializer(ObjectId.class, new ObjectIdJsonDeserializer());
            mapper.registerModule(module);

            MongoClientOptions options = MongoClientOptions.builder()
                    .serverSelectionTimeout(1000)
                    .connectTimeout(10000)
                    .socketTimeout(60000)
                    .build();
            mongoClient = new MongoClient("localhost", options);
            mongoDatabase = mongoClient.getDatabase("sicstv");

            // lancio una query per capire se la connessione c'è
            // in questo modo se non c'è acceso mongo in locale, viene fatta la
            // connessione in SSH nel server
            MongoIterable<String> result = mongoDatabase.listCollectionNames();
            result.first();
        } catch (Exception ex) {
//            GlobalHelper.reportError(ex);
            initServer();
        }
    }

    private static void initServer() {
        try {
            String sshUsername = "admin157278759";  // admin81828046
            String sshPassword = "zWMuP1H'r/fB";      // c613p4b080
            String sshHost = "************";
            int sshPort = 22; // Default SSH port

            String mongoHost = "localhost"; // MongoDB host running on localhost through SSH tunnel
            int mongoPort = 27017; // Default MongoDB port
//            int localPort = 27025; // Local port for tunnel

            JSch jsch = new JSch();
            session = jsch.getSession(sshUsername, sshHost, sshPort);
            session.setPassword(sshPassword);

            // Disable strict host key checking
            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);

            // Connect to SSH server
            session.connect();

            // Create the tunnel
            assignedPort = session.setPortForwardingL(0, mongoHost, mongoPort);
            mongoClient = new MongoClient("localhost", assignedPort);
            mongoDatabase = mongoClient.getDatabase("sicstv");
        } catch (JSchException ex) {
            GlobalHelper.reportError(ex);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static void closeConnection() {
        // Close the SSH tunnel and session when done
        if (session != null) {
            try {
                System.out.println("Closing Mongo SSH Connection...");
                session.delPortForwardingL(assignedPort);
                session.disconnect();
                System.out.println("Mongo SSH Connection Closed.");
            } catch (JSchException ex) {
                GlobalHelper.reportError(ex);
            }
        }
    }

    public static void deleteAllDocuments(String collection) {
        getMongoDatabase().getCollection(collection).drop();
    }

    public static void deleteDocuments(String baseField, Long id, String collection) {
        getMongoDatabase().getCollection(collection).deleteMany(eq(baseField, id));
    }

    public static void deleteAndInsertDocuments(String baseField, Long id, String collection, List<Document> document) {
        getMongoDatabase().getCollection(collection).deleteMany(eq(baseField, id));
        getMongoDatabase().getCollection(collection).insertMany(document);
    }

    public static void deleteAndInsertDocument(String baseField, Long id, String collection, Document document) {
        getMongoDatabase().getCollection(collection).deleteMany(eq(baseField, id));
        getMongoDatabase().getCollection(collection).insertOne(document);
    }

    public static void insertDocument(String collection, Document document) {
        getMongoDatabase().getCollection(collection).insertOne(document);
    }

    public static void insertDocuments(String collection, List<Document> documents) {
        getMongoDatabase().getCollection(collection).insertMany(documents);
    }

    public static List<Translation> getTranslations() {
        ClassModel<Translation> model = ClassModel.builder(Translation.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Translation> collection = getMongoDatabase().getCollection(StringUtils.lowerCase("translations"), Translation.class).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(ne("cancelled", true)), Translation.class).into(new ArrayList<Translation>());
    }

    public static void updateDocument(String collectionName, Object objectClass) {
        try {
            MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(StringUtils.defaultIfEmpty(collectionName, objectClass.getClass().getSimpleName())));
            collection.replaceOne(
                    new Document("_id", PropertyUtils.getSimpleProperty(objectClass, "id")),
                    toDocument(objectClass)
            );
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static List<Cache> getSmartSearchDataByParams(Map<String, String> params, Map<String, String> tableParams) {
        List<Bson> filters = new ArrayList<>();
        boolean isCompetition = false, isTeam = false;

        String collection = "smart_search_player";
        if (params.containsKey("competitionName")) {
            if (!params.containsKey("isTop5European") && !params.containsKey("isTop5SouthAmerica")) {
                collection += "_competition";
                isCompetition = true;
            }
        }
        if (params.containsKey("playerLastTeamName")) {
            isTeam = true;
        }
        if (params.containsKey("language")) {
            collection += "_" + params.get("language");
        }
        if (params.containsKey("timeframe")) {
            if (params.get("timeframe").equals("0")) {
                collection += "_current_season";
            } else {
                collection += "_previous_season";
            }
        }

        // applico tutti i filtri dalla pagina
        for (String param : params.keySet()) {
            if (StringUtils.equals(param, "genere")) {
                int genere = Integer.parseInt(params.get(param));
                if (isCompetition && isTeam) {
                    //filters.add(and(or(eq("competitionGenere", 0), eq("teamGenere", 0))));
                    filters.add(eq("competitionGenere", genere));
                    filters.add(eq("playerLastTeamGenere", genere));
                } else if (isCompetition) {
                    filters.add(eq("competitionGenere", genere));
                } else {
                    filters.add(eq("playerLastTeamGenere", genere));
                }
            } else if (!param.startsWith("range")) {
                if (!StringUtils.equals(param, "language") && !StringUtils.equals(param, "timeframe")
                        && !StringUtils.equals(param, "isTop5European") && !StringUtils.equals(param, "isTop5SouthAmerica")) {
                    if ((StringUtils.equals(param, "competitionName") && isCompetition) || (!StringUtils.equals(param, "competitionName"))) {
                        String competions = params.get(param);
                        if (StringUtils.contains(competions, "||")) {
                            List<String> validCompetitions = Arrays.asList(StringUtils.splitByWholeSeparator(competions, "||"));
                            filters.add(in(param, validCompetitions));
                        } else {
                            filters.add(eq(param, params.get(param)));
                        }
                    }

//                    if (param.equals("playerLastTeamName") && isTeam) {
//                        // se filtro per team devo filtrare sulla colonna 'teamName'
//                        // altrimenti se filtro su 'playerLastTeamName' potrei prendere più righe
//                        filters.add(eq("teamName", params.get(param)));
//                    } else {
//                        filters.add(eq(param, params.get(param)));
//                    }
                }
            } else {
                String values = params.get(param);
                List<String> splitted = Arrays.asList(StringUtils.split(values, ","));
                if (splitted.size() == 2) {
                    if (param.contains("playerValue")) {
                        double min = Double.parseDouble(splitted.get(0));
                        if (min == 0D) {
                            // se a 0 prendo anche quelli che non hanno il valore di mercato
                            // deciso da Michele il 20/02/2025
                            filters.add(or(and(gte(StringUtils.replace(param, "range", ""), min), lte(StringUtils.replace(param, "range", ""), Double.valueOf(splitted.get(1)))), eq(StringUtils.replace(param, "range", ""), null)));
                        } else {
                            filters.add(gte(StringUtils.replace(param, "range", ""), min));
                            filters.add(lte(StringUtils.replace(param, "range", ""), Double.valueOf(splitted.get(1))));
                        }
                    } else {
                        filters.add(gte(StringUtils.replace(param, "range", ""), Double.valueOf(splitted.get(0))));
                        filters.add(lte(StringUtils.replace(param, "range", ""), Double.valueOf(splitted.get(1))));
                    }
                }
            }
        }

        if (params.containsKey("isTop5European")) {
            filters.add(in("playerCompetitions", Arrays.asList(params.get("isTop5European").split(","))));
        } else if (params.containsKey("isTop5SouthAmerica")) {
            filters.add(in("playerCompetitions", Arrays.asList(params.get("isTop5SouthAmerica").split(","))));
        }

        int skip = 0, limit = 0;
        if (tableParams.containsKey("start")) {
            skip = Integer.parseInt(tableParams.get("start"));
        }
        if (tableParams.containsKey("length")) {
            limit = Integer.parseInt(tableParams.get("length"));
        }

        Bson sort = null;
        if (tableParams.containsKey("sort")) {
            String sortColumn = tableParams.get("sort");
            List<String> splitted = Arrays.asList(StringUtils.split(sortColumn, ";"));
            if (splitted.size() == 2) {
                String columnName = splitted.get(0);
                String type = splitted.get(1);

                if (columnName.startsWith("statsType")) {
                    if (StringUtils.equalsIgnoreCase(type, "asc")) {
                        sort = orderBy(ascending(columnName));
                    } else {
                        sort = orderBy(descending(columnName));
                    }
                } else {
                    if (StringUtils.equalsIgnoreCase(type, "asc")) {
                        sort = orderBy(ascending(columnName));
                    } else {
                        sort = orderBy(descending(columnName));
                    }
                }
            }
        }

        ClassModel<Cache> model = ClassModel.builder(Cache.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(new SmartSearchPlayerCodecProvider(pojoCodecProvider)));

        MongoCollection<Cache> mongoCollection = getMongoDatabase().getCollection(collection, Cache.class).withCodecRegistry(pojoCodecRegistry);
        if (filters.isEmpty()) {
            List<Cache> result = mongoCollection
                    .find()
                    .skip(skip)
                    .limit(limit)
                    .sort(sort)
                    .into(new ArrayList<Cache>());

            Long totalDocuments = mongoCollection.countDocuments();
            for (Cache row : result) {
                row.setTotalRecords(totalDocuments.intValue());
                row.setFilteredRecords(totalDocuments.intValue());
            }
            return result;
        } else {
            List<Cache> result = mongoCollection
                    .find(and(filters))
                    .skip(skip)
                    .limit(limit)
                    .sort(sort)
                    .into(new ArrayList<Cache>());

            Long totalDocuments = mongoCollection.countDocuments(and(filters));
            for (Cache row : result) {
                row.setTotalRecords(totalDocuments.intValue());
                row.setFilteredRecords(totalDocuments.intValue());
            }
            return result;
        }
    }

    public static List<CacheFieldValues> getSmartSearchBaseValuesByParams(Map<String, String> params, Locale requestLocale) {
        String collection = "smart_search_player_competition";
        if (params.containsKey("language")) {
            collection += "_" + params.get("language");
        }
        collection += "_current_season";

        MongoCollection<Document> mongoCollection = getMongoDatabase().getCollection(collection);
        AggregateIterable<Document> result = mongoCollection
                .aggregate(singletonList(
                        group(null,
                                addToSet("competition", "$competitionName"),
                                addToSet("team", "$playerLastTeamName"),
                                addToSet("country", "$playerCountryName"),
                                addToSet("role", "$playerPosition"),
                                addToSet("roleDetailed", "$playerPositionDetail"),
                                addToSet("foot", "$playerFoot")
                        )
                ));

        // dovrebbe essere solo uno
        if (result != null) {
            List<CacheFieldValues> values = new ArrayList<>();
            for (Document doc : result) {
                CacheFieldValues value = new CacheFieldValues();
                value.setFieldName("competition");
                value.setFieldValues(new ArrayList<String>());
                List<String> competitions = (ArrayList<String>) doc.get("competition");
                Collections.sort(competitions, new Comparator<String>() {
                    @Override
                    public int compare(String o1, String o2) {
                        return o1.compareTo(o2);
                    }
                });
                value.getFieldValues().addAll(competitions);
                // maschi, aggiungo top 5 competizioni europee e americane
                value.getFieldValues().add(0, SpringApplicationContextHelper.getMessage("smart.search.top.5.europe", requestLocale));
                value.getFieldValues().add(1, SpringApplicationContextHelper.getMessage("smart.search.top.5.south.america", requestLocale));
                value.setMultipleChoice(true);
                values.add(value);

                value = new CacheFieldValues();
                value.setFieldName("team");
                value.setFieldValues(new ArrayList<String>());
                List<String> teams = (ArrayList<String>) doc.get("team");
                Collections.sort(teams, new Comparator<String>() {
                    @Override
                    public int compare(String o1, String o2) {
                        return o1.compareTo(o2);
                    }
                });
                value.getFieldValues().addAll(teams);
                values.add(value);

                value = new CacheFieldValues();
                value.setFieldName("country");
                value.setFieldValues(new ArrayList<String>());
                List<String> countries = (ArrayList<String>) doc.get("country");
                Collections.sort(countries, new Comparator<String>() {
                    @Override
                    public int compare(String o1, String o2) {
                        return o1.compareTo(o2);
                    }
                });
                value.getFieldValues().addAll(countries);
                values.add(value);

                value = new CacheFieldValues();
                value.setFieldName("role");
                value.setFieldValues(new ArrayList<String>());
                List<String> roles = (ArrayList<String>) doc.get("role");
                Collections.sort(roles, new Comparator<String>() {
                    @Override
                    public int compare(String o1, String o2) {
                        return o1.compareTo(o2);
                    }
                });
                value.getFieldValues().addAll(roles);
                values.add(value);

                value = new CacheFieldValues();
                value.setFieldName("role.detailed");
                value.setFieldValues(new ArrayList<String>());
                List<String> roleDetailed = (ArrayList<String>) doc.get("roleDetailed");
                Collections.sort(roleDetailed, new Comparator<String>() {
                    @Override
                    public int compare(String o1, String o2) {
                        return o1.compareTo(o2);
                    }
                });
                value.getFieldValues().addAll(roleDetailed);
                values.add(value);

                value = new CacheFieldValues();
                value.setFieldName("foot");
                value.setFieldValues(new ArrayList<String>());
                List<String> foots = (ArrayList<String>) doc.get("foot");
                Collections.sort(foots, new Comparator<String>() {
                    @Override
                    public int compare(String o1, String o2) {
                        return o1.compareTo(o2);
                    }
                });
                value.getFieldValues().addAll(foots);
                values.add(value);
            }

            return values;
        }

        return null;
    }

    public static List<CacheFieldValues> getSmartSearchValuesByParams(Map<String, String> params, Locale requestLocale) {
        String collection = "smart_search_player_competition";
        if (params.containsKey("language")) {
            collection += "_" + params.get("language");
        }
        if (params.containsKey("timeframe")) {
            if (params.get("timeframe").equals("0")) {
                collection += "_current_season";
            } else {
                collection += "_previous_season";
            }
        }

        // PER ORA GESTISCE COMPETIZIONE, TEAM E GENERE
        // SE FILTRO GENERE, AGGIORNO COMPETIZIONE, TEAM E COUNTRY
        // SE FILTRO COMPETIZIONE, AGGIORNO TEAM E COUNTRY
        // SE FILTRO TEAM, AGGIORNO SOLO COUNTRY
        boolean isGenere = false, isCompetition = false, isTeam = false, isRole = false;
        if (params.containsKey("playerLastTeamName")) {
            isTeam = true;
        } else if (params.containsKey("competitionName")) {
            isCompetition = true;
        } else if (params.containsKey("playerPosition")) {
            isRole = true;
        } else if (params.containsKey("genere")) {
            isGenere = true;
        }

        List<Bson> filters = new ArrayList<>();
        List<BsonField> fields = new ArrayList<>();
        if (isTeam) {
            filters.add(eq("playerLastTeamName", params.get("playerLastTeamName")));
            if (params.containsKey("competitionName")) {
                if (params.containsKey("isTop5European")) {
                    filters.add(in("playerCompetitions", Arrays.asList(params.get("isTop5European").split(","))));
                } else if (params.containsKey("isTop5SouthAmerica")) {
                    filters.add(in("playerCompetitions", Arrays.asList(params.get("isTop5SouthAmerica").split(","))));
                } else {
                    filters.add(eq("competitionName", params.get("competitionName")));
                }
            }

            fields.add(addToSet("country", "$playerCountryName"));
        } else if (isCompetition) {
            if (params.containsKey("isTop5European")) {
                filters.add(in("playerCompetitions", Arrays.asList(params.get("isTop5European").split(","))));
            } else if (params.containsKey("isTop5SouthAmerica")) {
                filters.add(in("playerCompetitions", Arrays.asList(params.get("isTop5SouthAmerica").split(","))));
            } else {
                filters.add(eq("competitionName", params.get("competitionName")));
            }

            fields.add(addToSet("team", "$playerLastTeamName"));
            fields.add(addToSet("country", "$playerCountryName"));
        } else if (isGenere) {
            int genere = Integer.parseInt(params.get("genere"));
            filters.add(eq("competitionGenere", genere));

            fields.add(addToSet("competition", "$competitionName"));
            fields.add(addToSet("team", "$playerLastTeamName"));
            fields.add(addToSet("country", "$playerCountryName"));
        } else if (isRole) {
            String position = params.get("playerPosition");
            filters.add(eq("playerPosition", position));

            fields.add(addToSet("roleDetailed", "$playerPositionDetail"));
        }
        fields.add(addToSet("role", "$playerPosition"));
        fields.add(addToSet("foot", "$playerFoot"));

        if (isGenere || isCompetition || isTeam || isRole) {
            MongoCollection<Document> mongoCollection = getMongoDatabase().getCollection(collection);
            AggregateIterable<Document> result;
            if (filters.isEmpty()) {
                result = mongoCollection
                        .aggregate(singletonList(
                                group(null,
                                        fields
                                )
                        ));
            } else {
                result = mongoCollection
                        .aggregate(Arrays.asList(
                                match(and(filters)),
                                group(null,
                                        fields
                                )
                        ));
            }

            // dovrebbe essere solo uno
            if (result != null) {
                List<CacheFieldValues> values = new ArrayList<>();
                for (Document doc : result) {
                    CacheFieldValues value;
                    if (isGenere) {
                        value = new CacheFieldValues();
                        value.setFieldName("competition");
                        value.setFieldValues(new ArrayList<String>());
                        List<String> competitions = (ArrayList<String>) doc.get("competition");
                        Collections.sort(competitions, new Comparator<String>() {
                            @Override
                            public int compare(String o1, String o2) {
                                return o1.compareTo(o2);
                            }
                        });
                        value.getFieldValues().addAll(competitions);
                        int genere = Integer.parseInt(params.get("genere"));
                        if (genere == 0) {
                            // maschi, aggiungo top 5 competizioni europee e americane
                            value.getFieldValues().add(0, SpringApplicationContextHelper.getMessage("smart.search.top.5.europe", requestLocale));
                            value.getFieldValues().add(1, SpringApplicationContextHelper.getMessage("smart.search.top.5.south.america", requestLocale));
                        }
                        values.add(value);
                    }
                    if (isGenere || isCompetition) {
                        value = new CacheFieldValues();
                        value.setFieldName("team");
                        value.setFieldValues(new ArrayList<String>());
                        List<String> teams = (ArrayList<String>) doc.get("team");
                        Collections.sort(teams, new Comparator<String>() {
                            @Override
                            public int compare(String o1, String o2) {
                                return o1.compareTo(o2);
                            }
                        });
                        value.getFieldValues().addAll(teams);
                        values.add(value);
                    }
                    if (isGenere || isCompetition || isTeam) {
                        value = new CacheFieldValues();
                        value.setFieldName("country");
                        value.setFieldValues(new ArrayList<String>());
                        List<String> countries = (ArrayList<String>) doc.get("country");
                        Collections.sort(countries, new Comparator<String>() {
                            @Override
                            public int compare(String o1, String o2) {
                                return o1.compareTo(o2);
                            }
                        });
                        value.getFieldValues().addAll(countries);
                        values.add(value);

                        value = new CacheFieldValues();
                        value.setFieldName("role");
                        value.setFieldValues(new ArrayList<String>());
                        List<String> roles = (ArrayList<String>) doc.get("role");
                        Collections.sort(roles, new Comparator<String>() {
                            @Override
                            public int compare(String o1, String o2) {
                                return o1.compareTo(o2);
                            }
                        });
                        value.getFieldValues().addAll(roles);
                        values.add(value);

                        value = new CacheFieldValues();
                        value.setFieldName("foot");
                        value.setFieldValues(new ArrayList<String>());
                        List<String> foots = (ArrayList<String>) doc.get("foot");
                        Collections.sort(foots, new Comparator<String>() {
                            @Override
                            public int compare(String o1, String o2) {
                                return o1.compareTo(o2);
                            }
                        });
                        value.getFieldValues().addAll(foots);
                        values.add(value);
                    }
                    if (isRole) {
                        value = new CacheFieldValues();
                        value.setFieldName("role.detailed");
                        value.setFieldValues(new ArrayList<String>());
                        List<String> roleDetailed = (ArrayList<String>) doc.get("roleDetailed");
                        Collections.sort(roleDetailed, new Comparator<String>() {
                            @Override
                            public int compare(String o1, String o2) {
                                return o1.compareTo(o2);
                            }
                        });
                        value.getFieldValues().addAll(roleDetailed);
                        values.add(value);
                    }
                }

                return values;
            }
        }

        return null;
    }

    public static List<CacheFieldRange> getSmartSearchBaseRangeByParams(Map<String, String> params) {
        String collection = "smart_search_player";
        if (params.containsKey("language")) {
            collection += "_" + params.get("language");
        }
        if (params.containsKey("timeframe")) {
            if (params.get("timeframe").equals("0")) {
                collection += "_current_season";
            } else {
                collection += "_previous_season";
            }
        }

        MongoCollection<Document> mongoCollection = getMongoDatabase().getCollection(collection);
        AggregateIterable<Document> result = mongoCollection
                .aggregate(singletonList(
                        group(null,
                                min("minAge", "$playerAge"),
                                max("maxAge", "$playerAge"),
                                min("minHeight", "$playerHeight"),
                                max("maxHeight", "$playerHeight"),
                                min("minMatches", "$totalMatches"),
                                max("maxMatches", "$totalMatches"),
                                min("minMinutes", "$statsType1"),
                                max("maxMinutes", "$statsType1")
                        )
                ));

        // dovrebbe essere solo uno
        if (result != null) {
            List<CacheFieldRange> values = new ArrayList<>();
            for (Document doc : result) {
                CacheFieldRange value = new CacheFieldRange();
                value.setFieldName("playerAge");
                value.setMinValue(doc.getInteger("minAge").doubleValue());
                value.setMaxValue(doc.getInteger("maxAge").doubleValue());
                values.add(value);

                value = new CacheFieldRange();
                value.setFieldName("playerHeight");
                value.setMinValue(doc.getInteger("minHeight").doubleValue());
                value.setMaxValue(doc.getInteger("maxHeight").doubleValue());
                values.add(value);

                value = new CacheFieldRange();
                value.setFieldName("totalMatches");
                value.setMinValue(doc.getInteger("minMatches").doubleValue());
                value.setMaxValue(doc.getInteger("maxMatches").doubleValue());
                values.add(value);

                value = new CacheFieldRange();
                value.setFieldName("statsType1");
                value.setMinValue(doc.getDouble("minMinutes"));
                value.setMaxValue(doc.getDouble("maxMinutes"));
                values.add(value);
            }

            return values;
        }

        return null;
    }

    // questo dovrebbe essere usato passando un parametro "statsType[x]"
    public static CacheFieldRange getSmartSearchRangeByParams(Map<String, String> params) {
        String collection = "smart_search_player";
        if (params.containsKey("language")) {
            collection += "_" + params.get("language");
        }
        if (params.containsKey("timeframe")) {
            if (params.get("timeframe").equals("0")) {
                collection += "_current_season";
            } else {
                collection += "_previous_season";
            }
        }
        String typeId = null, fieldName = null;
        for (String param : params.keySet()) {
            if (param.startsWith("statsType")) {
                typeId = param;
                break;
            } else if (!param.equals("language") && !param.equals("timeframe") && !param.equals("genere")) {
                fieldName = param;
                break;
            }
        }

        List<Bson> filters = new ArrayList<>();
        if (params.containsKey("genere")) {
            filters.add(eq("playerLastTeamGenere", Integer.valueOf(params.get("genere"))));
        }

        if (StringUtils.isNotBlank(typeId)) {
            MongoCollection<Document> mongoCollection = getMongoDatabase().getCollection(collection);
            AggregateIterable<Document> result;
            if (filters.isEmpty()) {
                result = mongoCollection
                        .aggregate(singletonList(
                                group(null,
                                        min("minAge", "$" + typeId),
                                        max("maxAge", "$" + typeId)
                                )
                        ));
            } else {
                result = mongoCollection
                        .aggregate(Arrays.asList(
                                match(and(filters)),
                                group(null,
                                        min("minAge", "$" + typeId),
                                        max("maxAge", "$" + typeId)
                                )
                        ));
            }

            // dovrebbe essere solo uno
            if (result != null) {
                for (Document doc : result) {
                    CacheFieldRange value = new CacheFieldRange();
                    value.setFieldName(typeId);
                    value.setMinValue(doc.getDouble("minAge"));
                    value.setMaxValue(doc.getDouble("maxAge"));
                    return value;
                }
            }
        } else if (StringUtils.isNotBlank(fieldName)) { // campi tipo età, altezza, totale partite...
            MongoCollection<Document> mongoCollection = getMongoDatabase().getCollection(collection);
            AggregateIterable<Document> result;
            if (filters.isEmpty()) {
                result = mongoCollection
                        .aggregate(singletonList(
                                group(null,
                                        min("minAge", "$" + fieldName),
                                        max("maxAge", "$" + fieldName)
                                )
                        ));
            } else {
                result = mongoCollection
                        .aggregate(Arrays.asList(
                                match(and(filters)),
                                group(null,
                                        min("minAge", "$" + fieldName),
                                        max("maxAge", "$" + fieldName)
                                )
                        ));
            }

            // dovrebbe essere solo uno
            if (result != null) {
                for (Document doc : result) {
                    CacheFieldRange value = new CacheFieldRange();
                    value.setFieldName(fieldName);
                    if (doc.get("minAge") instanceof Long) {
                        value.setMinValue(doc.getLong("minAge").doubleValue());
                        value.setMaxValue(doc.getLong("maxAge").doubleValue());
                    } else {
                        value.setMinValue(doc.getInteger("minAge").doubleValue());
                        value.setMaxValue(doc.getInteger("maxAge").doubleValue());
                    }
                    return value;
                }
            }
        }

        return null;
    }

    public static void saveSession(HttpSession session) {
        List<String> requestToExclude = new ArrayList<>(
                Arrays.asList(
                        "/sicstv/user/getUnseen.htm",
                        "/sicstv/user/getUserLimits.htm",
                        "/sicstv/user/getSource.htm",
                        "/sicstv/user/getFavorites.htm",
                        "/sicstv/user/getShortcuts.htm",
                        "/sicstv/user/getSeason.htm",
                        "/sicstv/user/refreshMatches.htm",
                        "/sicstv/user/searchCalendarByCompetition.htm",
                        "/sicstv/user/saveTab.htm",
                        "/sicstv/user/filterMatches.htm",
                        "/sicstv/user/smartSearchValues.htm",
                        "/sicstv/user/smartSearchPlayerData.htm",
                        "/sicstv/user/getStatsTypeMinAndMax.htm",
                        "/sicstv/user/changeSeason.htm",
                        "/sicstv/user/goTo.htm"
                )
        );
        String collection = "user_requests";

        try {
            if (session.getAttribute(GlobalHelper.kBeanUtente) != null) {
                User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);

                if (curUser != null) {
                    Map<String, Integer> sessionRequestList = (Map<String, Integer>) session.getAttribute(GlobalHelper.kRequestsList);
                    UserActions userActions = (UserActions) session.getAttribute(GlobalHelper.kActions);
                    if (sessionRequestList != null && !sessionRequestList.isEmpty()) {
                        // in teoria se non è null di base non è mai vuota, ma non si sa mai...
                        Document document = new Document();
                        document.append("userId", curUser.getId());
                        document.append("groupsetId", curUser.getGroupsetId());
                        if (StringUtils.isNotBlank(curUser.getGroupsetName())) {
                            document.append("groupsetName", curUser.getGroupsetName());
                        }
                        document.append("creation", new Date());

                        // informazioni sulla sessione
                        document.append("sessionCreation", new Date(session.getCreationTime()));
                        long durationInMillis = new Date().getTime() - new Date(session.getCreationTime()).getTime();
                        long second = (durationInMillis / 1000) % 60;
                        long minute = (durationInMillis / (1000 * 60)) % 60;
                        long hour = (durationInMillis / (1000 * 60 * 60)) % 24;
                        document.append("sessionDuration", String.format("%02d:%02d:%02d", hour, minute, second));

                        if (userActions != null) {
                            if (userActions.getVideoStream() > 0) {
                                document.append("videoStream", userActions.getVideoStream());
                            }
                            if (userActions.getLoginCorrect() > 0) {
                                document.append("loginCorrect", userActions.getLoginCorrect());
                            }
                            if (userActions.getLoginError() > 0) {
                                document.append("loginError", userActions.getLoginError());
                            }
                            if (userActions.getLicenseExpired() > 0) {
                                document.append("licenseExpired", userActions.getLicenseExpired());
                            }
                            if (userActions.getPlaylistTv() > 0) {
                                document.append("newPlaylistTv", userActions.getPlaylistTv());
                            }
                            if (userActions.getVideoDownload() > 0) {
                                document.append("videoDownload", userActions.getVideoDownload());
                            }
                            if (userActions.getWatchlist() > 0) {
                                document.append("newWatchlist", userActions.getWatchlist());
                            }
                            if (userActions.getDownloadXmlJson() > 0) {
                                document.append("downloadXmlJson", userActions.getDownloadXmlJson());
                            }
                            if (userActions.getExport() > 0) {
                                document.append("export", userActions.getExport());
                            }
                            if (userActions.getTotal() > 0) {
                                document.append("totalActions", userActions.getTotal());
                            }
                        }

                        for (String requestLink : sessionRequestList.keySet()) {
                            // correggo tutti quei link tipo 
                            // /sicstv/user/home_htm;jsessionid=377DECB4E455ABD8F7BAF491B8757C7D
                            String correctRequestLink = StringUtils.substringBefore(requestLink, ";");
                            if (!requestToExclude.contains(correctRequestLink)) {
                                document.append(correctRequestLink.replaceAll("\\.", "_"), sessionRequestList.get(correctRequestLink));
                            }
                        }

                        insertDocument(collection, document);
                        // evito che inserisca 2 volte lo stesso documento, quindi per sicurezza
                        // butto via il log così se anche dovesse rientrare qua non inserisce nulla
                        session.removeAttribute(GlobalHelper.kRequestsList);
                    } else {
                        logger.log(Level.WARNING, "MongoHelper.saveSession: skipping session because sessionRequestList is empty ({0})", curUser.getId());
                    }

                    // tengo solo i dati dell'ultimo mese (per evitare che diventi logdata 2.0)
                    getMongoDatabase().getCollection(collection).deleteMany(lt("creation", DateUtils.addDays(new Date(), -30)));
                } else {
                    logger.log(Level.WARNING, "MongoHelper.saveSession: curUser is null");
                }
            }
        } catch (Exception ex) {
            logger.log(Level.WARNING, "MongoHelper.saveSession: exception occured. {0}", ex.getLocalizedMessage());
        }
    }

    public static Document toDocument(Object object) {
        Document result = null;
        if (object != null) {
            result = Document.parse(serializeToJson(object));
        }
        return result;
    }

    public static String serializeToJson(Object object) {
        initIfNeeded();
        try {
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException ex) {
            GlobalHelper.reportError(ex);
        }
        return null;
    }

    public static MongoDatabase getMongoDatabase() {
        initIfNeeded();
        return mongoDatabase;
    }

    private static void initIfNeeded() {
        if (mongoDatabase == null || session == null || !session.isConnected()) {
            initServer();
        }
    }
}
