package sics.helper;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.List;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

/**
 *
 * <AUTHOR>
 */
public class PDFHelper {

    private static int currentPage;

    /**
     * @param pdfLink contains the link of the PDF
     * @param playerName must be in the format "[NUMBER] - [KNOWN_NAME]"
     * @return the extracted page if exists (otherwise null) as PDDocument
     */
    public static PDDocument getPlayerPageFromMatchStudio(String pdfLink, final String playerName) {
        try {
            currentPage = 0;
            URL url = new URL(pdfLink);
            byte[] pdfBytes;
            try (InputStream is = url.openStream()) {
                ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                int nRead;
                byte[] data = new byte[16384];
                while ((nRead = is.read(data, 0, data.length)) != -1) {
                    buffer.write(data, 0, nRead);
                }
                buffer.flush();
                pdfBytes = buffer.toByteArray();
            }
            // Crea un'istanza della classe PDFTextStripper
            try (PDDocument document = Loader.loadPDF(pdfBytes)) {

                // Crea un'istanza della classe PDFTextStripper
                PDFTextStripper stripper = new PDFTextStripper() {
                    StringBuilder textBuilder = new StringBuilder();
                    boolean mustBeCorrect = false, found = false;

                    @Override
                    public void processPage(PDPage page) throws IOException {
                        // Sovrascrivi il metodo processPage per memorizzare l'informazione sulla pagina corrente
                        if (!found) {
                            currentPage++;
                            super.processPage(page);
                        }
                    }

                    @Override
                    protected void processTextPosition(TextPosition text) {
                        // Accumula il testo estratto da ogni posizione
                        if (!found && text.getFontSize() == 16.0f) {
                            int currentIndex = textBuilder.length();
                            int maxIndex = playerName.length();

                            if (currentIndex < maxIndex) {
                                if (text.getUnicode().charAt(0) == playerName.charAt(currentIndex)) {
                                    textBuilder.append(text.getUnicode());
                                    if (textBuilder.length() == 1) {
                                        mustBeCorrect = true;
                                    }
                                } else if (mustBeCorrect) {
//                                    System.out.println("Invalid because textBuilder is " + textBuilder.toString() + " and current char is " + text.getUnicode());
                                    mustBeCorrect = false;
                                    textBuilder = new StringBuilder();
                                }
                            } else {
                                found = true;
//                                System.out.println("CORRECT PAGE: " + currentPage + " textBuilder: " + textBuilder.toString() + " font size is " + text.getFontSize());
                            }
                        }
                    }

                    @Override
                    protected void writeString(String text, List<TextPosition> textPositions) throws IOException {
                        // Sovrascrivi il metodo writeString per poter processare il testo estratto
                        super.writeString(text, textPositions);
                    }
                };  // Imposta il raggio di pagine su cui eseguire la ricerca (in questo caso, tutte le pagine)
                stripper.setStartPage(1);
                stripper.setEndPage(document.getNumberOfPages());
                // Esegue l'estrazione del testo e la ricerca del testo specificato
                stripper.getText(document);
                // Chiude il documento
            }

            // System.out.println("Should i extract page " + currentPage + " ???");
            return createSubPdf(pdfLink, currentPage - 1);
        } catch (IOException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }

    public static PDDocument createSubPdf(String pdfLink, int pageNumber) {
        try {
            PDDocument extractedDocument;
            URL url = new URL(pdfLink);
            byte[] pdfBytes;
            try (InputStream is = url.openStream()) {
                ByteArrayOutputStream buffer = new ByteArrayOutputStream();
                int nRead;
                byte[] data = new byte[16384];
                while ((nRead = is.read(data, 0, data.length)) != -1) {
                    buffer.write(data, 0, nRead);
                }
                buffer.flush();
                pdfBytes = buffer.toByteArray();
            }
            // Creo un nuovo documento per le pagine estratte
            PDDocument document = Loader.loadPDF(pdfBytes); // Creo un nuovo documento per le pagine estratte
            extractedDocument = new PDDocument();
            extractedDocument.addPage(document.getPage(pageNumber));
            // Salva il nuovo documento con le pagine estratte  
//                extractedDocument.save(new File("C:\\Users\\<USER>\\Downloads\\tmp\\test-sub.pdf"));
//            extractedDocument.save(new File("C:\\Users\\<USER>\\Downloads\\tmp\\test-sub.pdf"));
            return extractedDocument;
        } catch (IOException ex) {
            GlobalHelper.reportError(ex);
            return null;
        }
    }
}
