package sics.helper;

import javax.servlet.ServletContext;
import org.springframework.beans.BeansException;
import org.springframework.web.context.ServletContextAware;

public class SpringServletContextHelper implements ServletContextAware {

    private static ServletContext mContext;

    /**
     * This method is called from within the ApplicationContext once it is done
     * starting up, it will stick a reference to itself into this bean.
     *
     * @param context a reference to the ApplicationContext.
     */
    public void setServletContext(ServletContext context) throws BeansException {
        mContext = context;
    }

    public static ServletContext getServletContext() {
        return mContext;
    }

    public static String getRealPath(String pathfile) {

        String basePath = mContext.getRealPath("");
        basePath = StringHelper.replaceString(basePath, "\\", "/");
        String ret = null;

        if (basePath.substring(basePath.length() - 1).equalsIgnoreCase("/")) {
            ret = basePath + pathfile;
        } else {
            ret = basePath + "/" + pathfile;
        }

        return ret;
    }

    public static String getInitParameter(String parameter) {
        return mContext.getInitParameter(parameter);
    }

}
