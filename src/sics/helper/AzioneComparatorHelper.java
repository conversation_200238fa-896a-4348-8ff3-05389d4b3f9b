/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.helper;

import java.util.Comparator;
import java.util.Objects;
import sics.domain.Event;
import sics.model.Azione;

/**
 *
 * <AUTHOR>
 */
public class AzioneComparatorHelper implements Comparator {

	@Override
	public int compare(Object a, Object b) {
		Azione a1 = (Azione) a;
		Azione b1 = (Azione) b;
		if (a1.getmStart() > b1.getmStart()) {
			return 1;
		} else if (Objects.equals(a1.getmStart(), b1.getmStart())) {
			return a1.getmIdEvent().compareTo(b1.getmIdEvent());//A parità di tempo inizio si segue l'ordine alfabetico
		} else {
			return -1;
		}
	}
}
