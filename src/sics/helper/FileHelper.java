package sics.helper;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileInputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class FileHelper {
    
    /** Creates a new instance of FileHelper */
    public FileHelper() {
    }
    
    public static boolean fileExist( String pathFile ) {
		
		boolean retVal = false;
        if( pathFile.toLowerCase().startsWith("http") || pathFile.toLowerCase().startsWith("https") ) {
			try {
				URL url = new URL(pathFile);
				HttpURLConnection huc = (HttpURLConnection) url.openConnection();
				retVal = true;
			} catch( Exception ex) {
			}
			
        } else {
			File fileCheck = new File( pathFile );
			retVal = fileCheck.isFile();
        }
		
		return retVal;
    }   
    
    public static void copyFile(String from, String to) throws Exception {
        
        File in = new File(from);
        File out = new File(to);
    
        FileInputStream fis  = new FileInputStream(in);
        FileOutputStream fos = new FileOutputStream(out);
        byte[] buf = new byte[1024];
        int i = 0;
        while((i=fis.read(buf))!=-1) {
            fos.write(buf, 0, i);
        }
        fis.close();
        fos.close();
        
    }    
    
    public static boolean moveFile(String from, String to) throws Exception {

        copyFile(from, to);
        
        File in = new File(from);
        
        return in.delete();
        
    }    
    
}
