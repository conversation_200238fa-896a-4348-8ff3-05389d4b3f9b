/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.helper;

import java.util.ArrayList;
import sics.domain.Atleta;
import sics.analysis.Punto;

/**
 *
 * <AUTHOR>
 */
public class ModuleHelper {

    public static void setPositionPlayersByModule(ArrayList<Atleta> listPlayers, String module) {

        for (int i = 0; i < listPlayers.size() && i < 11; i++) {
            listPlayers.get(i).setmPositionPunto(getPosPlayerByModule(module, i));
        }
    }

    public static void setPositionPlayersByModuleReport(ArrayList<Atleta> listPlayers, String module) {
        for (int i = 0; i < listPlayers.size() && i < 11; i++) {
            listPlayers.get(i).setmPositionPunto(getPosPlayerByModuleReport(module, i));
        }
    }

    public static Punto getPosPlayerByModule(String module, Integer i) {
        Punto point = new Punto((float) -1, (float) -1);
        switch (module) {
            case "3412":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.5f;
                        point.y = 2.5f;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 4;
                        break;
                    case 2:
                        point.x = 2.5f;
                        point.y = 5.5f;
                        break;

                    case 8:
                        point.x = 4.8f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 4.2f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.8f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 5.5f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 3;
                        break;
                    case 10:
                        point.x = 6.5f;
                        point.y = 5;
                        break;
                }
                break;
            case "3421":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.5f;
                        point.y = 2.5f;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 4;
                        break;
                    case 2:
                        point.x = 2.5f;
                        point.y = 5.5f;
                        break;

                    case 8:
                        point.x = 4.8f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 4.2f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5;
                        break;
                    case 5:

                        point.x = 4.8f;
                        point.y = 7;
                        break;
                    case 10:
                        point.x = 5.5f;
                        point.y = 3;
                        break;
                    case 9:
                        point.x = 5.5f;
                        point.y = 5;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 4;
                        break;
                }
                break;
            case "343":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.5f;
                        point.y = 2.5f;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 4;
                        break;
                    case 2:
                        point.x = 2.5f;
                        point.y = 5.5f;
                        break;

                    case 8:
                        point.x = 4.8f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 4.2f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.8f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6.2f;
                        point.y = 2;
                        break;
                    case 10:
                        point.x = 6.5f;
                        point.y = 4;
                        break;
                    case 9:
                        point.x = 6.2f;
                        point.y = 6;
                        break;
                }
                break;
            case "3511":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.5f;
                        point.y = 2.5f;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 4;
                        break;
                    case 2:
                        point.x = 2.5f;
                        point.y = 5.5f;
                        break;

                    case 9:
                        point.x = 4.8f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4.2f;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.8f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5.5f;
                        break;
                    case 5:
                        point.x = 4.8f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 5.5f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 4;
                        break;
                }
                break;
            case "352":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.5f;
                        point.y = 2.5f;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 4;
                        break;
                    case 2:
                        point.x = 2.5f;
                        point.y = 5.5f;
                        break;

                    case 9:
                        point.x = 4.8f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4.2f;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.8f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5.5f;
                        break;
                    case 5:
                        point.x = 4.8f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 6.5f;
                        point.y = 5;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 3;
                        break;
                }
                break;
            case "4141":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 6:
                        point.x = 3.8f;
                        point.y = 4;
                        break;

                    case 10:
                        point.x = 4.8f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 4.8f;
                        point.y = 3;
                        break;
                    case 8:
                        point.x = 4.8f;
                        point.y = 5;
                        break;
                    case 7:
                        point.x = 4.8f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 4;
                        break;
                }
                break;
            case "4132":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 6:
                        point.x = 4;
                        point.y = 4;
                        break;
                    // 3
                    case 9:
                        point.x = 5;
                        point.y = 2;
                        break;
                    case 8:
                        point.x = 5.8f;
                        point.y = 4;
                        break;
                    case 7:
                        point.x = 5;
                        point.y = 6;
                        break;
                    // 2
                    case 11:
                        point.x = 7;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 7;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "4222":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 7:
                        point.x = 4.2f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5;
                        break;

                    case 9:
                        point.x = 5.5f;
                        point.y = 3;
                        break;
                    case 8:
                        point.x = 5.5f;
                        point.y = 5;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 3;
                        break;
                    case 10:
                        point.x = 6.5f;
                        point.y = 5;
                        break;
                }
                break;
            case "4231":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 7:
                        point.x = 4.2f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5;
                        break;

                    case 10:
                        point.x = 5.5f;
                        point.y = 1.5f;
                        break;
                    case 9:
                        point.x = 5.5f;
                        point.y = 4;
                        break;
                    case 8:
                        point.x = 5.5f;
                        point.y = 6.5f;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 4;
                        break;
                }
                break;
            case "442":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4.8f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4.2f;
                        point.y = 3;
                        break;
                    case 7:
                        point.x = 4.2f;
                        point.y = 5;
                        break;
                    case 6:
                        point.x = 4.8f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 3;
                        break;
                    case 10:
                        point.x = 6.5f;
                        point.y = 5;
                        break;
                }
                break;
            case "4411":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4.8f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4.2f;
                        point.y = 3;
                        break;
                    case 7:
                        point.x = 4.2f;
                        point.y = 5;
                        break;
                    case 6:
                        point.x = 4.8f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 5.5f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 4;
                        break;
                }
                break;
            case "451":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 4.8f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 4.2f;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 3.8f;
                        point.y = 4;
                        break;
                    case 7:
                        point.x = 4.2f;
                        point.y = 5.5f;
                        break;
                    case 6:
                        point.x = 4.8f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 4;
                        break;
                }
                break;
            case "433":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4.2f;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.8f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5.5f;
                        break;

                    case 11:
                        point.x = 6.2f;
                        point.y = 2;
                        break;
                    case 10:
                        point.x = 6.5f;
                        point.y = 4;
                        break;
                    case 9:
                        point.x = 6.2f;
                        point.y = 6;
                        break;
                }
                break;
            case "4312":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4.2f;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.8f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5.5f;
                        break;

                    case 9:
                        point.x = 5.5f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 3;
                        break;
                    case 10:
                        point.x = 6.5f;
                        point.y = 5;
                        break;
                }
                break;
            case "4321":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4.2f;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.8f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5.5f;
                        break;

                    case 10:
                        point.x = 5.5f;
                        point.y = 3;
                        break;
                    case 9:
                        point.x = 5.5f;
                        point.y = 5;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 4;
                        break;
                }
                break;
            case "532":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 6:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 5:
                        point.x = 2.5f;
                        point.y = 2.5f;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 4;
                        break;
                    case 3:
                        point.x = 2.5f;
                        point.y = 5.5f;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4.2f;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 3.8f;
                        point.y = 4;
                        break;
                    case 7:
                        point.x = 4.2f;
                        point.y = 5.5f;
                        break;

                    case 11:
                        point.x = 6.5f;
                        point.y = 3;
                        break;
                    case 10:
                        point.x = 6.5f;
                        point.y = 5;
                        break;
                }
                break;
            case "541":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 6:
                        point.x = 2.7f;
                        point.y = 1;
                        break;
                    case 5:
                        point.x = 2.5f;
                        point.y = 2.5f;
                        break;
                    case 4:
                        point.x = 2.2f;
                        point.y = 4;
                        break;
                    case 3:
                        point.x = 2.5f;
                        point.y = 5.5f;
                        break;
                    case 2:
                        point.x = 2.7f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 4.8f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 4.2f;
                        point.y = 3;
                        break;
                    case 8:
                        point.x = 4.2f;
                        point.y = 5;
                        break;
                    case 7:
                        point.x = 4.8f;
                        point.y = 7;
                        break;
                    case 11:
                        point.x = 6.5f;
                        point.y = 4;
                        break;
                }
                break;
            default:
                point = null;
                break;
        }
        return point;
    }

    //restituisce le posizione dei moduli per il campetto dei report che ha i pallini dei giocatori piu grandi e si sovrappongono
    public static Punto getPosPlayerByModuleReport(String module, Integer i) {
        Punto point = new Punto((float) -1, (float) -1);
        switch (module) {
            case "3412":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 4;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 8:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "3421":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 4;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 8:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 5.7f;
                        point.y = 3;
                        break;
                    case 9:
                        point.x = 5.7f;
                        point.y = 5;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 4;
                        break;
                }
                break;
            case "343":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 4;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 8:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 2;
                        break;
                    case 10:
                        point.x = 6;
                        point.y = 4;
                        break;
                    case 9:
                        point.x = 6;
                        point.y = 6;
                        break;
                }
                break;
            case "3511":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 4;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.8f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4;
                        point.y = 5.5f;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 5.2f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 4;
                        break;
                }
                break;
            case "352":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 4;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.8f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4;
                        point.y = 5.5f;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 6;
                        point.y = 3;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 5;
                        break;
                }
                break;
            case "4141":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 6:
                        point.x = 2.5f;
                        point.y = 4;
                        break;

                    case 10:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 8:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 7:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 5.5f;
                        point.y = 4;
                        break;
                }
                break;
            case "4222":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 7:
                        point.x = 3.7f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.7f;
                        point.y = 5;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 2.7f;
                        break;
                    case 8:
                        point.x = 4.3f;
                        point.y = 5.3f;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "4231":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 6:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 10:
                        point.x = 5.7f;
                        point.y = 1.5f;
                        break;
                    case 9:
                        point.x = 5.7f;
                        point.y = 4;
                        break;
                    case 8:
                        point.x = 5.7f;
                        point.y = 6.5f;
                        break;

                    case 11:
                        point.x = 7.5f;
                        point.y = 4;
                        break;
                }
                break;
            case "442":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4.2f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 3;
                        break;
                    case 10:
                        point.x = 6;
                        point.y = 5;
                        break;
                }
                break;
            case "4411":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4.2f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 4;
                        break;
                }
                break;
            case "451":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 3.8f;
                        point.y = 4;
                        break;
                    case 7:
                        point.x = 4;
                        point.y = 5.5f;
                        break;
                    case 6:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "433":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4.2f;
                        point.y = 2;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 6;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 2;
                        break;
                    case 10:
                        point.x = 6;
                        point.y = 4;
                        break;
                    case 9:
                        point.x = 6;
                        point.y = 6;
                        break;
                }
                break;
            case "4312":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4.2f;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 5.5f;
                        break;

                    case 9:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 3;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5;
                        break;
                }
                break;
            case "4321":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4.2f;
                        point.y = 2;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4.2f;
                        point.y = 6;
                        break;

                    case 10:
                        point.x = 5.7f;
                        point.y = 2.7f;
                        break;
                    case 9:
                        point.x = 5.7f;
                        point.y = 5.3f;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 4;
                        break;
                }
                break;
            case "532":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 6:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 5:
                        point.x = 2.1f;
                        point.y = 2.5f;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 4;
                        break;
                    case 3:
                        point.x = 2.1f;
                        point.y = 5.5f;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4.2f;
                        point.y = 2;
                        break;
                    case 8:
                        point.x = 3.9f;
                        point.y = 4;
                        break;
                    case 7:
                        point.x = 4.2f;
                        point.y = 6;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 3;
                        break;
                    case 10:
                        point.x = 6;
                        point.y = 5;
                        break;
                }
                break;
            case "541":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 6:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 5:
                        point.x = 2.1f;
                        point.y = 2.5f;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 4;
                        break;
                    case 3:
                        point.x = 2.1f;
                        point.y = 5.5f;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 4.2f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 8:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 7:
                        point.x = 4.2f;
                        point.y = 7;
                        break;
                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
        }

        if (!module.equals("4231")) {
            // per tutti gli altri, spazio un pò (+15%)
            if (i != 0) {
                point.x = (float) (point.x + (point.x * 0.15));
            }
        }

        return point;
    }

    public static Punto getPosPlayerByModuleForField(String module, Integer i) {
        Punto point = new Punto((float) -1, (float) -1);
        switch (module) {
            case "3412":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 8:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "3421":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 8:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 6;
                        point.y = 2.5f;
                        break;
                    case 9:
                        point.x = 6;
                        point.y = 5.5f;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 4;
                        break;
                }
                break;
            case "343":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 8:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 4;
                        break;
                    case 9:
                        point.x = 6;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "3511":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "352":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                }
                break;
            case "4141":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 6:
                        point.x = 2.5f;
                        point.y = 4;
                        break;

                    case 10:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 7:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "4222":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 7:
                        point.x = 3.7f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 4.3f;
                        point.y = 5.3f;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "4231":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 7:
                        point.x = 3.7f;
                        point.y = 3;
                        break;

                    case 10:
                        point.x = 6;
                        point.y = 2;
                        break;
                    case 9:
                        point.x = 5.7f;
                        point.y = 4;
                        break;
                    case 8:
                        point.x = 5.7f;
                        point.y = 6.5f;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "442":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 6:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "4411":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 6:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "451":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 6:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "433":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 2;
                        break;
                    case 10:
                        point.x = 6;
                        point.y = 4;
                        break;
                    case 9:
                        point.x = 5.7f;
                        point.y = 6.5f;
                        break;
                }
                break;
            case "4312":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 9:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "4321":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 4f;
                        point.y = 5.5f;
                        break;

                    case 10:
                        point.x = 5.7f;
                        point.y = 2.5f;
                        break;
                    case 9:
                        point.x = 5.7f;
                        point.y = 5.5f;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "532":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 6:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 5:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 6;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "541":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 6:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 5:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 6;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 7:
                        point.x = 4.3f;
                        point.y = 7;
                        break;
                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
        }

//        if (!module.equals("4231")) {
//            // per tutti gli altri, spazio un pò (+15%)
//            if (i != 0) {
//                point.x = (float) (point.x + (point.x * 0.15));
//            }
//        }
        return point;
    }

    public static Punto getPosPlayerBasket(int i) {
        Punto point = new Punto((float) -1, (float) -1);
        switch (i + 1) {
            case 1:
                point.x = 3.2f;
                point.y = 4;
                break;
            case 2:
                point.x = 2.4f;
                point.y = 1.2f;
                break;
            case 3:
                point.x = 0.9f;
                point.y = 6.7f;
                break;
            case 4:
                point.x = 1.3f;
                point.y = 2.4f;
                break;
            case 5:
                point.x = 1.8f;
                point.y = 5.6f;
                break;

            case 6:
                point.x = 0.23f;
                point.y = 7.7f;
                break;
            case 7:
                point.x = 0.98f;
                point.y = 7.7f;
                break;
            case 8:
                point.x = 1.73f;
                point.y = 7.7f;
                break;
            case 9:
                point.x = 2.48f;
                point.y = 7.7f;
                break;
            case 10:
                point.x = 3.23f;
                point.y = 7.7f;
                break;
            case 11:
                point.x = 3.98f;
                point.y = 7.7f;
                break;
            case 12:
                point.x = 4.73f;
                point.y = 7.7f;
                break;
        }
        return point;
    }

}
