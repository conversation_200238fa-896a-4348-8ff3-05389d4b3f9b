/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.helper;

import java.util.Comparator;
import sics.domain.Event;

/**
 *
 * <AUTHOR>
 */
class EventComparatorHelper implements Comparator {

	public int compare(Object a, Object b) {
		Event a1 = (Event) a;
		Event b1 = (Event) b;
		if (a1.getStart() > b1.getStart()) {
			return 1;
		} else if (a1.getStart() == b1.getStart()) {
			return a1.getIdevent().compareTo(b1.getIdevent());//A parità di tempo inizio si segue l'ordine alfabetico
		} else {
			return -1;
		}
	}
}
