package sics.helper;

import java.util.Vector;
import java.util.StringTokenizer;
import java.text.NumberFormat;

public class StringHelper {

    private static final char CODE_PADCHAR = '0';
    private static final char CODE_PADLEN = 5;
    private static final int NUM_DECIMALI = 2;

    public static String formatDouble(String value) {
        return formatDouble(value, NUM_DECIMALI);
    }

    public static String formatDouble(String value, int numDecimali) {

        double valDouble = 0;
        String retVal = "";
        if (value != null) {
            valDouble = Double.valueOf(StringHelper.getStringaDecimali(value));
        }
        NumberFormat nf = NumberFormat.getNumberInstance();
        if (numDecimali == 0) {
            valDouble = Math.round(valDouble);
        }
        nf.setMinimumFractionDigits(numDecimali);
        retVal = nf.format(valDouble);

        return retVal;
    }

    public static String getFormattedCodice(Long id) {
        String codice = null;
        codice = paddingString(id.toString(), CODE_PADLEN, CODE_PADCHAR, true);
        return codice;
    }

    public static String getCodiceSenzaPunti(String value) {
        String codice = null;
        codice = replaceString(value, ".", "");
        return codice;
    }

    public static String getStringaDecimali(String value) {
        String codice = null;
        // elimina punti
        codice = replaceString(value, ",", ".");
        return codice;
    }

    public static String replaceString(String input, String find, String replace) {

        final StringBuffer result = new StringBuffer();
        int startIdx = 0;
        int idxOld = 0;
        while ((idxOld = input.indexOf(find, startIdx)) >= 0) {
            result.append(input.substring(startIdx, idxOld));
            result.append(replace);
            startIdx = idxOld + find.length();
        }
        result.append(input.substring(startIdx));

        return result.toString();
    }

    public static String[] split(String str, String delim) {

        // Use a Vector to hold the splittee strings
        Vector v = new Vector();

        // Use a StringTokenizer to do the splitting
        StringTokenizer tokenizer = new StringTokenizer(str, delim);
        while (tokenizer.hasMoreTokens()) {
            v.addElement(tokenizer.nextToken());
        }

        String[] ret = new String[v.size()];
        for (int i = 0; i < ret.length; i++) {
            ret[i] = (String) v.elementAt(i);
        }

        return ret;
    }

    /**
     ** pad a string S with a size of N with char C * on the left (True) or on
     * the right(flase)
     *
     */
    public static String paddingString(String s, int n, char c, boolean paddingLeft) {
        StringBuffer str = new StringBuffer(s);
        int strLength = str.length();
        if (n > 0 && n > strLength) {
            for (int i = 0; i <= n; i++) {
                if (paddingLeft) {
                    if (i < n - strLength) {
                        str.insert(0, c);
                    }
                } else {
                    if (i > strLength) {
                        str.append(c);
                    }
                }
            }
        }
        return str.toString();
    }

    public static String getRight(String value, int pos) {
        return value.substring(value.length() - pos);
    }

    public static String truncateString(String string, int val) {
        String result;
        if (string.length() > 2) {
            result = string.substring(string.length() - 2, string.length());
        } else {
            result = string;
        }
        return result;
    }

    public static String capitalize(String value) {
        String result = value;
        if (!value.isEmpty()) {
            result = value.substring(0, 1).toUpperCase() + value.substring(1).toLowerCase();
        }
        return result;
    }

    // converte dal formato 00:00:00.00 in millisecondi
    public static int getMillisecondsFromTimeString(String msg) {
        int ms = 0;
        try {
            String[] splitted = msg.split(":");
            if (splitted.length == 3) {
                int hour = Integer.parseInt(splitted[0]);
                int minutes = Integer.parseInt(splitted[1]);
                int seconds = 0;
                int milliseconds = 0;
                String[] splitSeconds = splitted[2].split("\\.");
                if (splitSeconds.length == 2) {
                    seconds = Integer.parseInt(splitSeconds[0]);
                    milliseconds = Integer.parseInt(splitSeconds[1]);
                } else {
                    seconds = Integer.parseInt(splitted[2]);
                }
                ms = (hour * 3600 + minutes * 60 + seconds) * 1000 + milliseconds * 10;
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return ms;
    }

}
