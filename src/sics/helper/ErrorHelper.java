/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.helper;

/**
 *
 * <AUTHOR>
 */
public class ErrorHelper {
	static void reportError(String errore) {
		
		try {
			String contenuto = errore;
			MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp"), SpringApplicationContextHelper.getMessage("email.smtp.port"), SpringApplicationContextHelper.getMessage("email.smtp.user"), SpringApplicationContextHelper.getMessage("email.smtp.pwd") );
			mail.sendMail(SpringApplicationContextHelper.getMessage("email.from"), "<EMAIL>", null, null, null, null, "Errore sics.tv", contenuto, null,"");
		} catch( Exception ex) {
			ex.printStackTrace();
		}
	}
}
