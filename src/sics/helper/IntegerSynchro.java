package sics.helper;

public class IntegerSynchro {

    private int value;

    public IntegerSynchro() {
        value = 0;
    }

    public synchronized int value() {
        return value;
    }

    public synchronized void setValue(int newValue) {
        value = newValue;
    }

    public synchronized void increment() {
        value = value + 1;
        this.notifyAll();
    }

    public synchronized void decrement() {
        value = value - 1;
        this.notifyAll();
    }

}
