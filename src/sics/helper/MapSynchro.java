package sics.helper;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

public class MapSynchro<K, V> {

    private LinkedHashMap<K, V> map;

    public MapSynchro() {
        map = new LinkedHashMap<>();
    }

    public Set<K> keySet() {
        synchronized (map) {
            return ((LinkedHashMap<K, V>) map.clone()).keySet();
        }
    }

    public Collection<V> values() {
        synchronized (map) {
            return map.values();
        }
    }

    public boolean isEmpty() {
        synchronized (map) {
            return map.isEmpty();
        }
    }

    public V put(K key, V value) {
        synchronized (map) {
            return map.put(key, value);
        }
    }

    public V putIfAbsent(K key, V value) {
        synchronized (map) {
            return map.putIfAbsent(key, value);
        }
    }

    public void putAll(Map<? extends K, ? extends V> m) {
        synchronized (map) {
            map.putAll(m);
        }
    }

    public V get(K key) {
        synchronized (map) {
            return map.get(key);
        }
    }

    public V replace(K key, V value) {
        synchronized (map) {
            return map.replace(key, value);
        }
    }

    public void clear() {
        synchronized (map) {
            map.clear();
        }
    }

    public boolean containsKey(K key) {
        synchronized (map) {
            return map.containsKey(key);
        }
    }

    public boolean containsValue(V value) {
        synchronized (map) {
            return map.containsValue(value);
        }
    }

    public V remove(K key) {
        synchronized (map) {
            return map.remove(key);
        }
    }

    public int size() {
        synchronized (map) {
            return map.size();
        }
    }
}