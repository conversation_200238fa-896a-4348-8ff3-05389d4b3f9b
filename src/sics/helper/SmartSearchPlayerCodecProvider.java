package sics.helper;

import org.bson.codecs.Codec;
import org.bson.codecs.configuration.CodecProvider;
import org.bson.codecs.configuration.CodecRegistry;
import sics.domain.Cache;

/**
 *
 * <AUTHOR>
 */
public class SmartSearchPlayerCodecProvider implements CodecProvider {
    
    private final CodecProvider pojoCodecProvider;
    
    public SmartSearchPlayerCodecProvider(CodecProvider pojoCodecProvider) {
        this.pojoCodecProvider = pojoCodecProvider;
    }
    
    @Override
    public <T> Codec<T> get(Class<T> clazz, CodecRegistry registry) {
        if (clazz.equals(Cache.class)) {
            Codec<Cache> defaultCodec = (Codec<Cache>) pojoCodecProvider.get(clazz, registry);
            return (Codec<T>) new SmartSearchPlayerCodec(defaultCodec, registry);
        }
        return null;
    }
}