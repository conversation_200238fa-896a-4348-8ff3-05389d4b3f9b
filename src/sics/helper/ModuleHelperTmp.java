/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.helper;

import sics.analysis.Punto;

/**
 *
 * <AUTHOR>
 */
public class ModuleHelperTmp {

    public static Punto getPosPlayerByModuleForField(String module, Integer i) {
        Punto point = new Punto((float) -1, (float) -1);
        switch (module) {
            case "3412":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 8:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "3421":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 8:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 9:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "343":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 8:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 2;
                        break;
                    case 10:
                        point.x = 6;
                        point.y = 4;
                        break;
                    case 9:
                        point.x = 5.7f;
                        point.y = 6.5f;
                        break;
                }
                break;
            case "3511":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "352":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 4:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 2:
                        point.x = 2.2f;
                        point.y = 6;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 5:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                }
                break;
            case "4141":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 6:
                        point.x = 2.5f;
                        point.y = 4;
                        break;

                    case 10:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 7:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "4222":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 7:
                        point.x = 3.7f;
                        point.y = 3;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 4.3f;
                        point.y = 5.3f;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "4231":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 7:
                        point.x = 3.7f;
                        point.y = 3;
                        break;

                    case 10:
                        point.x = 6;
                        point.y = 2;
                        break;
                    case 9:
                        point.x = 5.7f;
                        point.y = 4;
                        break;
                    case 8:
                        point.x = 5.7f;
                        point.y = 6.5f;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "442":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 6:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "4411":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 6:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "451":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 6:
                        point.x = 4.3f;
                        point.y = 7;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "433":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 2;
                        break;
                    case 10:
                        point.x = 6;
                        point.y = 4;
                        break;
                    case 9:
                        point.x = 5.7f;
                        point.y = 6.5f;
                        break;
                }
                break;
            case "4312":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 9:
                        point.x = 5.7f;
                        point.y = 4;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "4321":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 5:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 1.9f;
                        point.y = 5;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 8:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 7:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 6:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 10:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 9:
                        point.x = 5.7f;
                        point.y = 4f;
                        break;

                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
                break;
            case "532":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 6:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 5:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 6;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 2.5f;
                        point.y = 4;
                        break;
                    case 7:
                        point.x = 3.9f;
                        point.y = 5;
                        break;

                    case 11:
                        point.x = 6.3f;
                        point.y = 2.5f;
                        break;
                    case 10:
                        point.x = 6.3f;
                        point.y = 5.5f;
                        break;
                }
                break;
            case "541":
                switch (i + 1) {
                    case 1:
                        point.x = 0.5f;
                        point.y = 4;
                        break;

                    case 6:
                        point.x = 2.3f;
                        point.y = 1;
                        break;
                    case 5:
                        point.x = 2.2f;
                        point.y = 2;
                        break;
                    case 4:
                        point.x = 1.9f;
                        point.y = 3;
                        break;
                    case 3:
                        point.x = 2.2f;
                        point.y = 6;
                        break;
                    case 2:
                        point.x = 2.3f;
                        point.y = 7;
                        break;

                    case 10:
                        point.x = 4.3f;
                        point.y = 1;
                        break;
                    case 9:
                        point.x = 4;
                        point.y = 2.5f;
                        break;
                    case 8:
                        point.x = 3.9f;
                        point.y = 5;
                        break;
                    case 7:
                        point.x = 4.3f;
                        point.y = 7;
                        break;
                    case 11:
                        point.x = 6;
                        point.y = 4;
                        break;
                }
        }
        
        if (!module.equals("4231")) {
            // per tutti gli altri, spazio un pò (+15%)
            if (i != 0) {
                point.x = (float) (point.x + (point.x * 0.15));
            }
        }
        
        return point;
    }
}
