/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.helper;

import java.util.Comparator;

import org.apache.commons.lang3.StringUtils;
import sics.domain.EventDescription;

/**
 *
 * <AUTHOR>
 */
public class DescriptionEventComparatorHelper implements Comparator {

	private String language;

	public DescriptionEventComparatorHelper(String language) {
		if (StringUtils.startsWith(language, "_")) {
			language = StringUtils.replace(language, "_", "");
		}
		if (StringUtils.isBlank(language)) {
			language = "it";
		}
		this.language = language;
	}
	public DescriptionEventComparatorHelper() {
		this.language = "it";
	}

	public int compare(Object a, Object b) {
		EventDescription a1 = (EventDescription) a;
		EventDescription b1 = (EventDescription) b;
		return a1.descTrim(language).compareTo(b1.descTrim(language));
	}
}
