package sics.helper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import sics.domain.Atleta;
import sics.domain.Game;
import sics.domain.PlayerStudioItem;
import sics.domain.PlayerStudioTableItem;
import sics.domain.Tags;
import sics.model.Azione;

/**
 *
 * <AUTHOR>
 */
public class PlayerStudioHelper {

    public static List<PlayerStudioTableItem> getFirstZone(List<Azione> events, Game fixture, Atleta player, Locale requestLocale) {
        List<PlayerStudioTableItem> result = new ArrayList<>();
        long positionId = player.getIdRuolo();

        if (positionId == 2) { // DIFENSORE
            int passaggiRiusciti = 0, passaggiRiusciti1T = 0, passaggiRiusciti2T = 0, passaggiTotali = 0, passaggiTotali1T = 0, passaggiTotali2T = 0;
            int percPassaggiRiusciti = 0, percPassaggiRiusciti1T = 0, percPassaggiRiusciti2T = 0;
            int passaggiChiave = 0, passaggiChiave1T = 0, passaggiChiave2T = 0, assist = 0, assist1T = 0, assist2T = 0;
            int passaggiChiaveRicevuti = 0, passaggiChiaveRicevuti1T = 0, passaggiChiaveRicevuti2T = 0;
            int tocchiPallaPropriaArea = 0, tocchiPallaPropriaArea1T = 0, tocchiPallaPropriaArea2T = 0, tocchiPallaTerzoDifensivo = 0, tocchiPallaTerzoDifensivo1T = 0, tocchiPallaTerzoDifensivo2T = 0;
            int tocchiPallaAreaAvversaria = 0, tocchiPallaAreaAvversaria1T = 0, tocchiPallaAreaAvversaria2T = 0, tocchiPallaTerzoOffensivo = 0, tocchiPallaTerzoOffensivo1T = 0, tocchiPallaTerzoOffensivo2T = 0;
            int palleLateraliRiuscite = 0, palleLateraliRiuscite1T = 0, palleLateraliRiuscite2T = 0, palleLateraliTotali = 0, palleLateraliTotali1T = 0, palleLateraliTotali2T = 0;
            int percPalleLateraliRiuscite = 0, percPalleLateraliRiuscite1T = 0, percPalleLateraliRiuscite2T = 0;
            int dribblingVinti = 0, dribblingVinti1T = 0, dribblingVinti2T = 0, dribblingTotali = 0, dribblingTotali1T = 0, dribblingTotali2T = 0, dribblingSubiti = 0, dribblingSubiti1T = 0, dribblingSubiti2T = 0;
            int tiriDaArea = 0, tiriDaArea1T = 0, tiriDaArea2T = 0, tiriTotali = 0, tiriTotali1T = 0, tiriTotali2T = 0;
            int palleRecuperate = 0, palleRecuperate1T = 0, palleRecuperate2T = 0;
            int palleRecuperateMetaOffensiva = 0, palleRecuperateMetaOffensiva1T = 0, palleRecuperateMetaOffensiva2T = 0, pallePerseMetaOffensiva = 0, pallePerseMetaOffensiva1T = 0, pallePerseMetaOffensiva2T = 0;
            int duelliVinti = 0, duelliVinti1T = 0, duelliVinti2T = 0, duelliTotali = 0, duelliTotali1T = 0, duelliTotali2T = 0, percDuelliVinti = 0, percDuelliVinti1T = 0, percDuelliVinti2T = 0;
            int duelliVintiMetaOffesiva = 0, duelliVintiMetaOffesiva1T = 0, duelliVintiMetaOffesiva2T = 0, duelliVintiTerzoOffensivo = 0, duelliVintiTerzoOffensivo1T = 0, duelliVintiTerzoOffensivo2T = 0;

            for (Azione event : events) {
                if (StringUtils.isNotBlank(event.getmType())) {
                    boolean isFirstHalf = GlobalHelper.isActionInFirstHalf(event);
                    float distanzaDalFondo = GlobalHelper.calcolaDistanzaDalFondo(event, fixture.getTeamSx(), fixture.getTeamSx2(), fixture);

                    if (isPlayerInEvent(event, player.getId())) {
                        // Passaggi Riusciti/Totali
                        if (StringUtils.equals(event.getmType(), "PASS")) {
                            if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                                for (Tags tag : event.getmTags()) {
                                    if (StringUtils.equals(tag.getCode(), "PASS-0")) {
                                        passaggiRiusciti++;
                                        if (isFirstHalf) {
                                            passaggiRiusciti1T++;
                                        } else {
                                            passaggiRiusciti2T++;
                                        }
                                        break;
                                    }
                                }
                            }
                            passaggiTotali++;
                            if (isFirstHalf) {
                                passaggiTotali1T++;
                            } else {
                                passaggiTotali2T++;
                            }
                        }

                        // Passaggi Chiave
                        if (StringUtils.equals(event.getmType(), "PCF")) {
                            passaggiChiave++;
                            if (isFirstHalf) {
                                passaggiChiave1T++;
                            } else {
                                passaggiChiave2T++;
                            }
                        }

                        // Assist
                        if (StringUtils.equals(event.getmType(), "ASS")) {
                            assist++;
                            if (isFirstHalf) {
                                assist1T++;
                            } else {
                                assist2T++;
                            }
                        }

                        if (isToccoPalla(event)) {
                            // Tocchi Palla Propria Area
                            if (GlobalHelper.checkInArea(event.getmActionPos())
                                    && distanzaDalFondo <= (GlobalHelper.kLatoLungoCampo / 3D)) {
                                tocchiPallaPropriaArea++;
                                if (isFirstHalf) {
                                    tocchiPallaPropriaArea1T++;
                                } else {
                                    tocchiPallaPropriaArea2T++;
                                }
                            }
                            // Tocchi Palla Terzo Difensivo
                            if (distanzaDalFondo <= (GlobalHelper.kLatoLungoCampo / 3D)) {
                                tocchiPallaTerzoDifensivo++;
                                if (isFirstHalf) {
                                    tocchiPallaTerzoDifensivo1T++;
                                } else {
                                    tocchiPallaTerzoDifensivo2T++;
                                }
                            }
                            // Tocchi Palla Area Avversaria
                            if (GlobalHelper.checkInArea(event.getmActionPos())
                                    && distanzaDalFondo > ((GlobalHelper.kLatoLungoCampo / 3D) * 2)) {
                                tocchiPallaAreaAvversaria++;
                                if (isFirstHalf) {
                                    tocchiPallaAreaAvversaria1T++;
                                } else {
                                    tocchiPallaAreaAvversaria2T++;
                                }
                            }
                            // Tocchi Palla Terzo Offensivo
                            if (distanzaDalFondo > ((GlobalHelper.kLatoLungoCampo / 3D) * 2)) {
                                tocchiPallaTerzoOffensivo++;
                                if (isFirstHalf) {
                                    tocchiPallaTerzoOffensivo1T++;
                                } else {
                                    tocchiPallaTerzoOffensivo2T++;
                                }
                            }
                        }

                        if (isPallaLaterale(event)) {
                            // Palle Laterali Totali
                            palleLateraliTotali++;
                            if (isFirstHalf) {
                                palleLateraliTotali1T++;
                            } else {
                                palleLateraliTotali2T++;
                            }

                            // Palle Laterali Riuscite
                            if (event.getmPlayerTo() != null && !event.getmPlayerTo().isEmpty()) {
                                palleLateraliRiuscite++;
                                if (isFirstHalf) {
                                    palleLateraliRiuscite1T++;
                                } else {
                                    palleLateraliRiuscite2T++;
                                }
                            }
                        }

                        // Dribbling Totali
                        if (StringUtils.equals(event.getmType(), "DRF") || StringUtils.equals(event.getmType(), "DRS")) {
                            dribblingTotali++;
                            if (isFirstHalf) {
                                dribblingTotali1T++;
                            } else {
                                dribblingTotali2T++;
                            }

                            if (StringUtils.equals(event.getmType(), "DRF")) {
                                // Dribbling Vinti
                                dribblingVinti++;
                                if (isFirstHalf) {
                                    dribblingVinti1T++;
                                } else {
                                    dribblingVinti2T++;
                                }
                            } else if (StringUtils.equals(event.getmType(), "DRS")) {
                                // Dribbling Subiti
                                dribblingSubiti++;
                                if (isFirstHalf) {
                                    dribblingSubiti1T++;
                                } else {
                                    dribblingSubiti2T++;
                                }
                            }
                        }

                        // Tiri Totali
                        if (StringUtils.equals(event.getmType(), "TIF")) {
                            tiriTotali++;
                            if (isFirstHalf) {
                                tiriTotali1T++;
                            } else {
                                tiriTotali2T++;
                            }

                            if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                                for (Tags tag : event.getmTags()) {
                                    if (StringUtils.equals(tag.getCode(), "TIF-13")) {
                                        tiriDaArea++;
                                        if (isFirstHalf) {
                                            tiriDaArea1T++;
                                        } else {
                                            tiriDaArea2T++;
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                        // Palle Recuperate
                        if (StringUtils.equals(event.getmType(), "REC")) {
                            palleRecuperate++;
                            if (isFirstHalf) {
                                palleRecuperate1T++;
                            } else {
                                palleRecuperate2T++;
                            }

                            if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 2D)) {
                                // Palle Recuperate Metà Offensiva
                                palleRecuperateMetaOffensiva++;
                                if (isFirstHalf) {
                                    palleRecuperateMetaOffensiva1T++;
                                } else {
                                    palleRecuperateMetaOffensiva2T++;
                                }
                            }
                        }

                        // Palle Perse Metà Offensiva
                        if (StringUtils.equals(event.getmType(), "PER")) {
                            if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 2D)) {
                                // Palle Recuperate Metà Offensiva
                                pallePerseMetaOffensiva++;
                                if (isFirstHalf) {
                                    pallePerseMetaOffensiva1T++;
                                } else {
                                    pallePerseMetaOffensiva2T++;
                                }
                            }
                        }

                        if (StringUtils.equals(event.getmType(), "DLL")) {
                            // Duelli Totali
                            duelliTotali++;
                            if (isFirstHalf) {
                                duelliTotali1T++;
                            } else {
                                duelliTotali2T++;
                            }

                            // Duelli Vinti
                            duelliVinti++;
                            if (isFirstHalf) {
                                duelliVinti1T++;
                            } else {
                                duelliVinti2T++;
                            }

                            if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 2D)) {
                                // Duelli Vinti Metà Offensiva
                                duelliVintiMetaOffesiva++;
                                if (isFirstHalf) {
                                    duelliVintiMetaOffesiva1T++;
                                } else {
                                    duelliVintiMetaOffesiva2T++;
                                }
                            }
                            if (distanzaDalFondo > ((GlobalHelper.kLatoLungoCampo / 3D) * 2)) {
                                // Duelli Vinti Terzo Offensivo
                                duelliVintiTerzoOffensivo++;
                                if (isFirstHalf) {
                                    duelliVintiTerzoOffensivo1T++;
                                } else {
                                    duelliVintiTerzoOffensivo2T++;
                                }
                            }
                        }

                        if (StringUtils.equals(event.getmType(), "DLS")) {
                            // Duelli Totali
                            duelliTotali++;
                            if (isFirstHalf) {
                                duelliTotali1T++;
                            } else {
                                duelliTotali2T++;
                            }
                        }
                    } else if (Integer.compare(event.getPlayerToId(), player.getId()) == 0) {
                        // Passaggi Chiave Ricevuti
                        if (StringUtils.equals(event.getmType(), "PCF")) {
                            passaggiChiaveRicevuti++;
                            if (isFirstHalf) {
                                passaggiChiaveRicevuti1T++;
                            } else {
                                passaggiChiaveRicevuti2T++;
                            }
                        }
                    }
                }
            }

            percPassaggiRiusciti = (passaggiTotali > 0 ? Math.round(1f * passaggiRiusciti / passaggiTotali * 100) : 0);
            percPassaggiRiusciti1T = (passaggiTotali1T > 0 ? Math.round(1f * passaggiRiusciti1T / passaggiTotali1T * 100) : 0);
            percPassaggiRiusciti2T = (passaggiTotali2T > 0 ? Math.round(1f * passaggiRiusciti2T / passaggiTotali2T * 100) : 0);

            percPalleLateraliRiuscite = (palleLateraliTotali > 0 ? Math.round(1f * palleLateraliRiuscite / palleLateraliTotali * 100) : 0);
            percPalleLateraliRiuscite1T = (palleLateraliTotali1T > 0 ? Math.round(1f * palleLateraliRiuscite1T / palleLateraliTotali1T * 100) : 0);
            percPalleLateraliRiuscite2T = (palleLateraliTotali2T > 0 ? Math.round(1f * palleLateraliRiuscite2T / palleLateraliTotali2T * 100) : 0);

            percDuelliVinti = (duelliTotali > 0 ? Math.round(1f * duelliVinti / duelliTotali * 100) : 0);
            percDuelliVinti1T = (duelliTotali1T > 0 ? Math.round(1f * duelliVinti1T / duelliTotali1T * 100) : 0);
            percDuelliVinti2T = (duelliTotali2T > 0 ? Math.round(1f * duelliVinti2T / duelliTotali2T * 100) : 0);

            PlayerStudioTableItem row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.1", requestLocale), passaggiRiusciti + "/" + passaggiTotali, passaggiRiusciti1T + "/" + passaggiTotali1T, passaggiRiusciti2T + "/" + passaggiTotali2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.2", requestLocale), percPassaggiRiusciti + "%", percPassaggiRiusciti1T + "%", percPassaggiRiusciti2T + "%");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.3", requestLocale), passaggiChiave + "/" + assist, passaggiChiave1T + "/" + assist1T, passaggiChiave2T + "/" + assist2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.4", requestLocale), passaggiChiaveRicevuti + "", passaggiChiaveRicevuti1T + "", passaggiChiaveRicevuti2T + "");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.5", requestLocale), tocchiPallaPropriaArea + "/" + tocchiPallaTerzoDifensivo, tocchiPallaPropriaArea1T + "/" + tocchiPallaTerzoDifensivo1T, tocchiPallaPropriaArea2T + "/" + tocchiPallaTerzoDifensivo2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.6", requestLocale), tocchiPallaAreaAvversaria + "/" + tocchiPallaTerzoOffensivo, tocchiPallaAreaAvversaria1T + "/" + tocchiPallaTerzoOffensivo1T, tocchiPallaAreaAvversaria2T + "/" + tocchiPallaTerzoOffensivo2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.7", requestLocale), palleLateraliRiuscite + "/" + palleLateraliTotali, palleLateraliRiuscite1T + "/" + palleLateraliTotali1T, palleLateraliRiuscite2T + "/" + palleLateraliTotali2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.8", requestLocale), percPalleLateraliRiuscite + "%", percPalleLateraliRiuscite1T + "%", percPalleLateraliRiuscite2T + "%");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.9", requestLocale), dribblingVinti + "/" + dribblingTotali + "/" + dribblingSubiti, dribblingVinti1T + "/" + dribblingTotali1T + "/" + dribblingSubiti1T, dribblingVinti2T + "/" + dribblingTotali2T + "/" + dribblingSubiti2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.10", requestLocale), tiriDaArea + "/" + tiriTotali, tiriDaArea1T + "/" + tiriTotali1T, tiriDaArea2T + "/" + tiriTotali2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.11", requestLocale), palleRecuperate + "", palleRecuperate1T + "", palleRecuperate2T + "");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.12", requestLocale), palleRecuperateMetaOffensiva + "/" + pallePerseMetaOffensiva, palleRecuperateMetaOffensiva1T + "/" + pallePerseMetaOffensiva1T, palleRecuperateMetaOffensiva2T + "/" + pallePerseMetaOffensiva2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.13", requestLocale), percDuelliVinti + "%", percDuelliVinti1T + "%", percDuelliVinti2T + "%");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.difensore.14", requestLocale), duelliVintiMetaOffesiva + "/" + duelliVintiTerzoOffensivo, duelliVintiMetaOffesiva1T + "/" + duelliVintiTerzoOffensivo1T, duelliVintiMetaOffesiva2T + "/" + duelliVintiTerzoOffensivo2T);
            result.add(row);
        } else if (positionId == 3) { // CENTROCAMPISTSA
            int passaggiRiusciti = 0, passaggiRiusciti1T = 0, passaggiRiusciti2T = 0, passaggiTotali = 0, passaggiTotali1T = 0, passaggiTotali2T = 0;
            int percPassaggiRiusciti = 0, percPassaggiRiusciti1T = 0, percPassaggiRiusciti2T = 0;
            int passaggiChiave = 0, passaggiChiave1T = 0, passaggiChiave2T = 0, assist = 0, assist1T = 0, assist2T = 0;
            int passaggiRicevuti = 0, passaggiRicevuti1T = 0, passaggiRicevuti2T = 0;
            int passaggiChiaveRicevuti = 0, passaggiChiaveRicevuti1T = 0, passaggiChiaveRicevuti2T = 0;
            int triangolazioniFatte = 0, triangolazioniFatte1T = 0, triangolazioniFatte2T = 0, triangolazioniChiuse = 0, triangolazioniChiuse1T = 0, triangolazioniChiuse2T = 0;
            int tocchiPallaPropriaArea = 0, tocchiPallaPropriaArea1T = 0, tocchiPallaPropriaArea2T = 0, tocchiPallaTerzoDifensivo = 0, tocchiPallaTerzoDifensivo1T = 0, tocchiPallaTerzoDifensivo2T = 0;
            int palleLateraliRiuscite = 0, palleLateraliRiuscite1T = 0, palleLateraliRiuscite2T = 0, palleLateraliTotali = 0, palleLateraliTotali1T = 0, palleLateraliTotali2T = 0;
            int percPalleLateraliRiuscite = 0, percPalleLateraliRiuscite1T = 0, percPalleLateraliRiuscite2T = 0;
            int dribblingVinti = 0, dribblingVinti1T = 0, dribblingVinti2T = 0, dribblingTotali = 0, dribblingTotali1T = 0, dribblingTotali2T = 0;
            int tiriDaArea = 0, tiriDaArea1T = 0, tiriDaArea2T = 0, tiriTotali = 0, tiriTotali1T = 0, tiriTotali2T = 0;
            int palleRecuperate = 0, palleRecuperate1T = 0, palleRecuperate2T = 0;
            int palleRecuperateMetaOffensiva = 0, palleRecuperateMetaOffensiva1T = 0, palleRecuperateMetaOffensiva2T = 0, pallePerseMetaOffensiva = 0, pallePerseMetaOffensiva1T = 0, pallePerseMetaOffensiva2T = 0;
            int duelliVinti = 0, duelliVinti1T = 0, duelliVinti2T = 0, duelliTotali = 0, duelliTotali1T = 0, duelliTotali2T = 0, percDuelliVinti = 0, percDuelliVinti1T = 0, percDuelliVinti2T = 0;

            for (Azione event : events) {
                if (StringUtils.isNotBlank(event.getmType())) {
                    boolean isFirstHalf = GlobalHelper.isActionInFirstHalf(event);
                    float distanzaDalFondo = GlobalHelper.calcolaDistanzaDalFondo(event, fixture.getTeamSx(), fixture.getTeamSx2(), fixture);

                    if (isPlayerInEvent(event, player.getId())) {
                        // Passaggi Riusciti/Totali
                        if (StringUtils.equals(event.getmType(), "PASS")) {
                            if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                                for (Tags tag : event.getmTags()) {
                                    if (StringUtils.equals(tag.getCode(), "PASS-0")) {
                                        passaggiRiusciti++;
                                        if (isFirstHalf) {
                                            passaggiRiusciti1T++;
                                        } else {
                                            passaggiRiusciti2T++;
                                        }
                                        break;
                                    }
                                }
                            }
                            passaggiTotali++;
                            if (isFirstHalf) {
                                passaggiTotali1T++;
                            } else {
                                passaggiTotali2T++;
                            }
                        }

                        // Passaggi Chiave
                        if (StringUtils.equals(event.getmType(), "PCF")) {
                            passaggiChiave++;
                            if (isFirstHalf) {
                                passaggiChiave1T++;
                            } else {
                                passaggiChiave2T++;
                            }
                        }

                        // Assist
                        if (StringUtils.equals(event.getmType(), "ASS")) {
                            assist++;
                            if (isFirstHalf) {
                                assist1T++;
                            } else {
                                assist2T++;
                            }
                        }

                        // Triangolazioni Fatte
                        if (StringUtils.equals(event.getmType(), "TRF")) {
                            triangolazioniFatte++;
                            if (isFirstHalf) {
                                triangolazioniFatte1T++;
                            } else {
                                triangolazioniFatte2T++;
                            }
                        }

                        if (isToccoPalla(event)) {
                            // Tocchi Palla Propria Area
                            if (GlobalHelper.checkInArea(event.getmActionPos())
                                    && distanzaDalFondo <= (GlobalHelper.kLatoLungoCampo / 3D)) {
                                tocchiPallaPropriaArea++;
                                if (isFirstHalf) {
                                    tocchiPallaPropriaArea1T++;
                                } else {
                                    tocchiPallaPropriaArea2T++;
                                }
                            }
                            // Tocchi Palla Terzo Difensivo
                            if (distanzaDalFondo <= (GlobalHelper.kLatoLungoCampo / 3D)) {
                                tocchiPallaTerzoDifensivo++;
                                if (isFirstHalf) {
                                    tocchiPallaTerzoDifensivo1T++;
                                } else {
                                    tocchiPallaTerzoDifensivo2T++;
                                }
                            }
                        }

                        if (isPallaLaterale(event)) {
                            // Palle Laterali Totali
                            palleLateraliTotali++;
                            if (isFirstHalf) {
                                palleLateraliTotali1T++;
                            } else {
                                palleLateraliTotali2T++;
                            }

                            // Palle Laterali Riuscite
                            if (event.getmPlayerTo() != null && !event.getmPlayerTo().isEmpty()) {
                                palleLateraliRiuscite++;
                                if (isFirstHalf) {
                                    palleLateraliRiuscite1T++;
                                } else {
                                    palleLateraliRiuscite2T++;
                                }
                            }
                        }

                        // Dribbling Totali
                        if (StringUtils.equals(event.getmType(), "DRF") || StringUtils.equals(event.getmType(), "DRS")) {
                            dribblingTotali++;
                            if (isFirstHalf) {
                                dribblingTotali1T++;
                            } else {
                                dribblingTotali2T++;
                            }

                            if (StringUtils.equals(event.getmType(), "DRF")) {
                                // Dribbling Vinti
                                dribblingVinti++;
                                if (isFirstHalf) {
                                    dribblingVinti1T++;
                                } else {
                                    dribblingVinti2T++;
                                }
                            }
                        }

                        // Tiri Totali
                        if (StringUtils.equals(event.getmType(), "TIF")) {
                            tiriTotali++;
                            if (isFirstHalf) {
                                tiriTotali1T++;
                            } else {
                                tiriTotali2T++;
                            }

                            if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                                for (Tags tag : event.getmTags()) {
                                    if (StringUtils.equals(tag.getCode(), "TIF-13")) {
                                        tiriDaArea++;
                                        if (isFirstHalf) {
                                            tiriDaArea1T++;
                                        } else {
                                            tiriDaArea2T++;
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                        // Palle Recuperate
                        if (StringUtils.equals(event.getmType(), "REC")) {
                            palleRecuperate++;
                            if (isFirstHalf) {
                                palleRecuperate1T++;
                            } else {
                                palleRecuperate2T++;
                            }

                            if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 2D)) {
                                // Palle Recuperate Metà Offensiva
                                palleRecuperateMetaOffensiva++;
                                if (isFirstHalf) {
                                    palleRecuperateMetaOffensiva1T++;
                                } else {
                                    palleRecuperateMetaOffensiva2T++;
                                }
                            }
                        }

                        // Palle Perse Metà Offensiva
                        if (StringUtils.equals(event.getmType(), "PER")) {
                            if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 2D)) {
                                // Palle Recuperate Metà Offensiva
                                pallePerseMetaOffensiva++;
                                if (isFirstHalf) {
                                    pallePerseMetaOffensiva1T++;
                                } else {
                                    pallePerseMetaOffensiva2T++;
                                }
                            }
                        }

                        if (StringUtils.equals(event.getmType(), "DLL")) {
                            // Duelli Totali
                            duelliTotali++;
                            if (isFirstHalf) {
                                duelliTotali1T++;
                            } else {
                                duelliTotali2T++;
                            }

                            // Duelli Vinti
                            duelliVinti++;
                            if (isFirstHalf) {
                                duelliVinti1T++;
                            } else {
                                duelliVinti2T++;
                            }
                        }

                        if (StringUtils.equals(event.getmType(), "DLS")) {
                            // Duelli Totali
                            duelliTotali++;
                            if (isFirstHalf) {
                                duelliTotali1T++;
                            } else {
                                duelliTotali2T++;
                            }
                        }
                    } else if (Integer.compare(event.getPlayerToId(), player.getId()) == 0) {
                        // Passaggi Ricevuti
                        if (StringUtils.equals(event.getmType(), "PASS")) {
                            boolean isImpreciso = false;
                            if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                                for (Tags tag : event.getmTags()) {
                                    if (StringUtils.equals(tag.getCode(), "PASS-1")) {
                                        isImpreciso = true;
                                    }
                                }
                            }

                            if (!isImpreciso) {
                                passaggiRicevuti++;
                                if (isFirstHalf) {
                                    passaggiRicevuti1T++;
                                } else {
                                    passaggiRicevuti2T++;
                                }
                            }
                        }

                        // Passaggi Chiave Ricevuti
                        if (StringUtils.equals(event.getmType(), "PCF")) {
                            passaggiChiaveRicevuti++;
                            if (isFirstHalf) {
                                passaggiChiaveRicevuti1T++;
                            } else {
                                passaggiChiaveRicevuti2T++;
                            }
                        }

                        // Triangolazioni Chiuse
                        if (StringUtils.equals(event.getmType(), "TRF")) {
                            triangolazioniChiuse++;
                            if (isFirstHalf) {
                                triangolazioniChiuse1T++;
                            } else {
                                triangolazioniChiuse2T++;
                            }
                        }
                    }
                }
            }

            percPassaggiRiusciti = (passaggiTotali > 0 ? Math.round(1f * passaggiRiusciti / passaggiTotali * 100) : 0);
            percPassaggiRiusciti1T = (passaggiTotali1T > 0 ? Math.round(1f * passaggiRiusciti1T / passaggiTotali1T * 100) : 0);
            percPassaggiRiusciti2T = (passaggiTotali2T > 0 ? Math.round(1f * passaggiRiusciti2T / passaggiTotali2T * 100) : 0);

            percPalleLateraliRiuscite = (palleLateraliTotali > 0 ? Math.round(1f * palleLateraliRiuscite / palleLateraliTotali * 100) : 0);
            percPalleLateraliRiuscite1T = (palleLateraliTotali1T > 0 ? Math.round(1f * palleLateraliRiuscite1T / palleLateraliTotali1T * 100) : 0);
            percPalleLateraliRiuscite2T = (palleLateraliTotali2T > 0 ? Math.round(1f * palleLateraliRiuscite2T / palleLateraliTotali2T * 100) : 0);

            percDuelliVinti = (duelliTotali > 0 ? Math.round(1f * duelliVinti / duelliTotali * 100) : 0);
            percDuelliVinti1T = (duelliTotali1T > 0 ? Math.round(1f * duelliVinti1T / duelliTotali1T * 100) : 0);
            percDuelliVinti2T = (duelliTotali2T > 0 ? Math.round(1f * duelliVinti2T / duelliTotali2T * 100) : 0);

            PlayerStudioTableItem row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.1", requestLocale), passaggiRiusciti + "/" + passaggiTotali, passaggiRiusciti1T + "/" + passaggiTotali1T, passaggiRiusciti2T + "/" + passaggiTotali2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.2", requestLocale), percPassaggiRiusciti + "%", percPassaggiRiusciti1T + "%", percPassaggiRiusciti2T + "%");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.3", requestLocale), passaggiChiave + "/" + assist, passaggiChiave1T + "/" + assist1T, passaggiChiave2T + "/" + assist2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.4", requestLocale), passaggiRicevuti + "", passaggiRicevuti1T + "", passaggiRicevuti2T + "");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.5", requestLocale), passaggiChiaveRicevuti + "", passaggiChiaveRicevuti1T + "", passaggiChiaveRicevuti2T + "");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.6", requestLocale), triangolazioniFatte + "/" + triangolazioniChiuse, triangolazioniFatte1T + "/" + triangolazioniChiuse1T, triangolazioniFatte2T + "/" + triangolazioniChiuse2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.7", requestLocale), tocchiPallaPropriaArea + "/" + tocchiPallaTerzoDifensivo, tocchiPallaPropriaArea1T + "/" + tocchiPallaTerzoDifensivo1T, tocchiPallaPropriaArea2T + "/" + tocchiPallaTerzoDifensivo2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.8", requestLocale), palleLateraliRiuscite + "/" + palleLateraliTotali, palleLateraliRiuscite1T + "/" + palleLateraliTotali1T, palleLateraliRiuscite2T + "/" + palleLateraliTotali2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.9", requestLocale), percPalleLateraliRiuscite + "%", percPalleLateraliRiuscite1T + "%", percPalleLateraliRiuscite2T + "%");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.10", requestLocale), dribblingVinti + "/" + dribblingTotali, dribblingVinti1T + "/" + dribblingTotali1T, dribblingVinti2T + "/" + dribblingTotali2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.11", requestLocale), tiriDaArea + "/" + tiriTotali, tiriDaArea1T + "/" + tiriTotali1T, tiriDaArea2T + "/" + tiriTotali2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.12", requestLocale), palleRecuperate + "", palleRecuperate1T + "", palleRecuperate2T + "");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.13", requestLocale), palleRecuperateMetaOffensiva + "/" + pallePerseMetaOffensiva, palleRecuperateMetaOffensiva1T + "/" + pallePerseMetaOffensiva1T, palleRecuperateMetaOffensiva2T + "/" + pallePerseMetaOffensiva2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.centrocampista.14", requestLocale), percDuelliVinti + "%", percDuelliVinti1T + "%", percDuelliVinti2T + "%");
            result.add(row);
        } else if (positionId == 4) { // ATTACCANTE
            int passaggiRiuscitiMetaOffensiva = 0, passaggiRiuscitiMetaOffensiva1T = 0, passaggiRiuscitiMetaOffensiva2T = 0, passaggiTotaliMetaOffensiva = 0, passaggiTotaliMetaOffensiva1T = 0, passaggiTotaliMetaOffensiva2T = 0;
            int passaggiChiave = 0, passaggiChiave1T = 0, passaggiChiave2T = 0, assist = 0, assist1T = 0, assist2T = 0;
            int tocchiPallaPropriaArea = 0, tocchiPallaPropriaArea1T = 0, tocchiPallaPropriaArea2T = 0, tocchiPallaTerzoDifensivo = 0, tocchiPallaTerzoDifensivo1T = 0, tocchiPallaTerzoDifensivo2T = 0;
            int passaggiRicevuti = 0, passaggiRicevuti1T = 0, passaggiRicevuti2T = 0;
            int passaggiChiaveRicevuti = 0, passaggiChiaveRicevuti1T = 0, passaggiChiaveRicevuti2T = 0;
            int triangolazioniFatte = 0, triangolazioniFatte1T = 0, triangolazioniFatte2T = 0, triangolazioniChiuse = 0, triangolazioniChiuse1T = 0, triangolazioniChiuse2T = 0;
            int palleLateraliRicevute = 0, palleLateraliRicevute1T = 0, palleLateraliRicevute2T = 0;
            int dribblingVinti = 0, dribblingVinti1T = 0, dribblingVinti2T = 0, dribblingTotali = 0, dribblingTotali1T = 0, dribblingTotali2T = 0;
            int tiriDaArea = 0, tiriDaArea1T = 0, tiriDaArea2T = 0, tiriTotali = 0, tiriTotali1T = 0, tiriTotali2T = 0;
            int occasioniGoal = 0, occasioniGoal1T = 0, occasioniGoal2T = 0, azioniPromettenti = 0, azioniPromettenti1T = 0, azioniPromettenti2T = 0;
            int goal = 0, goal1T = 0, goal2T = 0, percRealizzazioneGoal = 0, percRealizzazioneGoal1T = 0, percRealizzazioneGoal2T = 0;
            int palleRecuperateMetaOffensiva = 0, palleRecuperateMetaOffensiva1T = 0, palleRecuperateMetaOffensiva2T = 0;
            int duelliVintiMetaOffensiva = 0, duelliVintiMetaOffensiva1T = 0, duelliVintiMetaOffensiva2T = 0, duelliPersiMetaOffensiva = 0, duelliPersiMetaOffensiva1T = 0, duelliPersiMetaOffensiva2T = 0;
            int tocchiPallaMetaDifensiva = 0, tocchiPallaMetaDifensiva1T = 0, tocchiPallaMetaDifensiva2T = 0;

            for (Azione event : events) {
                if (StringUtils.isNotBlank(event.getmType())) {
                    boolean isFirstHalf = GlobalHelper.isActionInFirstHalf(event);
                    float distanzaDalFondo = GlobalHelper.calcolaDistanzaDalFondo(event, fixture.getTeamSx(), fixture.getTeamSx2(), fixture);

                    if (isPlayerInEvent(event, player.getId())) {
                        // Passaggi Riusciti Metà Campo Offensiva / Totali
                        if (StringUtils.equals(event.getmType(), "PASS")) {
                            if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 2D)) {
                                if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                                    for (Tags tag : event.getmTags()) {
                                        if (StringUtils.equals(tag.getCode(), "PASS-0")) {
                                            passaggiRiuscitiMetaOffensiva++;
                                            if (isFirstHalf) {
                                                passaggiRiuscitiMetaOffensiva1T++;
                                            } else {
                                                passaggiRiuscitiMetaOffensiva2T++;
                                            }
                                            break;
                                        }
                                    }
                                }

                                passaggiTotaliMetaOffensiva++;
                                if (isFirstHalf) {
                                    passaggiTotaliMetaOffensiva1T++;
                                } else {
                                    passaggiTotaliMetaOffensiva2T++;
                                }
                            }
                        }

                        // Passaggi Chiave
                        if (StringUtils.equals(event.getmType(), "PCF")) {
                            passaggiChiave++;
                            if (isFirstHalf) {
                                passaggiChiave1T++;
                            } else {
                                passaggiChiave2T++;
                            }
                        }

                        // Assist
                        if (StringUtils.equals(event.getmType(), "ASS")) {
                            assist++;
                            if (isFirstHalf) {
                                assist1T++;
                            } else {
                                assist2T++;
                            }
                        }

                        // Triangolazioni Fatte
                        if (StringUtils.equals(event.getmType(), "TRF")) {
                            triangolazioniFatte++;
                            if (isFirstHalf) {
                                triangolazioniFatte1T++;
                            } else {
                                triangolazioniFatte2T++;
                            }
                        }

                        if (isToccoPalla(event)) {
                            // Tocchi Palla Propria Area
                            if (GlobalHelper.checkInArea(event.getmActionPos())
                                    && distanzaDalFondo <= (GlobalHelper.kLatoLungoCampo / 3D)) {
                                tocchiPallaPropriaArea++;
                                if (isFirstHalf) {
                                    tocchiPallaPropriaArea1T++;
                                } else {
                                    tocchiPallaPropriaArea2T++;
                                }
                            }
                            // Tocchi Palla Terzo Difensivo
                            if (distanzaDalFondo <= (GlobalHelper.kLatoLungoCampo / 3D)) {
                                tocchiPallaTerzoDifensivo++;
                                if (isFirstHalf) {
                                    tocchiPallaTerzoDifensivo1T++;
                                } else {
                                    tocchiPallaTerzoDifensivo2T++;
                                }
                            }
                            // Tocchi Palla Metà Campo Difensiva
                            if (distanzaDalFondo <= (GlobalHelper.kLatoLungoCampo / 2D)) {
                                tocchiPallaMetaDifensiva++;
                                if (isFirstHalf) {
                                    tocchiPallaMetaDifensiva1T++;
                                } else {
                                    tocchiPallaMetaDifensiva2T++;
                                }
                            }
                        }

                        // Dribbling Totali
                        if (StringUtils.equals(event.getmType(), "DRF") || StringUtils.equals(event.getmType(), "DRS")) {
                            dribblingTotali++;
                            if (isFirstHalf) {
                                dribblingTotali1T++;
                            } else {
                                dribblingTotali2T++;
                            }

                            if (StringUtils.equals(event.getmType(), "DRF")) {
                                // Dribbling Vinti
                                dribblingVinti++;
                                if (isFirstHalf) {
                                    dribblingVinti1T++;
                                } else {
                                    dribblingVinti2T++;
                                }
                            }
                        }

                        // Tiri Totali
                        if (StringUtils.equals(event.getmType(), "TIF")) {
                            tiriTotali++;
                            if (isFirstHalf) {
                                tiriTotali1T++;
                            } else {
                                tiriTotali2T++;
                            }

                            if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                                for (Tags tag : event.getmTags()) {
                                    if (StringUtils.equals(tag.getCode(), "TIF-13")) {
                                        tiriDaArea++;
                                        if (isFirstHalf) {
                                            tiriDaArea1T++;
                                        } else {
                                            tiriDaArea2T++;
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                        if (StringUtils.equals(event.getmType(), "ATT")) {
                            if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                                for (Tags tag : event.getmTags()) {
                                    if (StringUtils.equals(tag.getCode(), "ATT-1")) {
                                        // Occasioni Goal
                                        occasioniGoal++;
                                        if (isFirstHalf) {
                                            occasioniGoal1T++;
                                        } else {
                                            occasioniGoal2T++;
                                        }
                                        break;
                                    }
                                    if (StringUtils.equals(tag.getCode(), "ATT-3")) {
                                        // Azioni Promettenti
                                        azioniPromettenti++;
                                        if (isFirstHalf) {
                                            azioniPromettenti1T++;
                                        } else {
                                            azioniPromettenti2T++;
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                        // Palle Recuperate
                        if (StringUtils.equals(event.getmType(), "REC")) {
                            if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 2D)) {
                                // Palle Recuperate Metà Offensiva
                                palleRecuperateMetaOffensiva++;
                                if (isFirstHalf) {
                                    palleRecuperateMetaOffensiva1T++;
                                } else {
                                    palleRecuperateMetaOffensiva2T++;
                                }
                            }
                        }

                        if (StringUtils.equals(event.getmType(), "TIF")) {
                            if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                                for (Tags tag : event.getmTags()) {
                                    if (StringUtils.equals(tag.getCode(), "TIF-2")) {
                                        // Goal
                                        goal++;
                                        if (isFirstHalf) {
                                            goal1T++;
                                        } else {
                                            goal2T++;
                                        }
                                        break;
                                    }
                                }
                            }
                        }

                        if (StringUtils.equals(event.getmType(), "DLL")) {
                            if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 2D)) {
                                // Duelli Vinti Metà Offensiva
                                duelliVintiMetaOffensiva++;
                                if (isFirstHalf) {
                                    duelliVintiMetaOffensiva1T++;
                                } else {
                                    duelliVintiMetaOffensiva2T++;
                                }
                            }
                        }

                        if (StringUtils.equals(event.getmType(), "DLS")) {
                            if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 2D)) {
                                // Duelli Persi Metà Offensiva
                                duelliPersiMetaOffensiva++;
                                if (isFirstHalf) {
                                    duelliPersiMetaOffensiva1T++;
                                } else {
                                    duelliPersiMetaOffensiva2T++;
                                }
                            }
                        }
                    } else if (Integer.compare(event.getPlayerToId(), player.getId()) == 0) {
                        if (isPallaLaterale(event)) {
                            // Palle Laterali Ricevute
                            palleLateraliRicevute++;
                            if (isFirstHalf) {
                                palleLateraliRicevute1T++;
                            } else {
                                palleLateraliRicevute2T++;
                            }
                        }

                        // Passaggi Ricevuti
                        if (StringUtils.equals(event.getmType(), "PASS")) {
                            boolean isImpreciso = false;
                            if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                                for (Tags tag : event.getmTags()) {
                                    if (StringUtils.equals(tag.getCode(), "PASS-1")) {
                                        isImpreciso = true;
                                        break;
                                    }
                                }
                            }

                            if (!isImpreciso) {
                                passaggiRicevuti++;
                                if (isFirstHalf) {
                                    passaggiRicevuti1T++;
                                } else {
                                    passaggiRicevuti2T++;
                                }
                            }
                        }

                        // Passaggi Chiave Ricevuti
                        if (StringUtils.equals(event.getmType(), "PCF")) {
                            passaggiChiaveRicevuti++;
                            if (isFirstHalf) {
                                passaggiChiaveRicevuti1T++;
                            } else {
                                passaggiChiaveRicevuti2T++;
                            }
                        }

                        // Triangolazioni Chiuse
                        if (StringUtils.equals(event.getmType(), "TRF")) {
                            triangolazioniChiuse++;
                            if (isFirstHalf) {
                                triangolazioniChiuse1T++;
                            } else {
                                triangolazioniChiuse2T++;
                            }
                        }
                    }
                }
            }

            percRealizzazioneGoal = (tiriTotali > 0 ? Math.round(1f * goal / tiriTotali * 100) : 0);
            percRealizzazioneGoal1T = (tiriTotali1T > 0 ? Math.round(1f * goal1T / tiriTotali1T * 100) : 0);
            percRealizzazioneGoal2T = (tiriTotali2T > 0 ? Math.round(1f * goal2T / tiriTotali2T * 100) : 0);

            PlayerStudioTableItem row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.1", requestLocale), passaggiRiuscitiMetaOffensiva + "/" + passaggiTotaliMetaOffensiva, passaggiRiuscitiMetaOffensiva1T + "/" + passaggiTotaliMetaOffensiva1T, passaggiRiuscitiMetaOffensiva2T + "/" + passaggiTotaliMetaOffensiva2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.2", requestLocale), passaggiChiave + "/" + assist, passaggiChiave1T + "/" + assist1T, passaggiChiave2T + "/" + assist2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.3", requestLocale), tocchiPallaPropriaArea + "/" + tocchiPallaTerzoDifensivo, tocchiPallaPropriaArea1T + "/" + tocchiPallaTerzoDifensivo1T, tocchiPallaPropriaArea2T + "/" + tocchiPallaTerzoDifensivo2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.4", requestLocale), passaggiRicevuti + "", passaggiRicevuti1T + "", passaggiRicevuti2T + "");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.5", requestLocale), passaggiChiaveRicevuti + "", passaggiChiaveRicevuti1T + "", passaggiChiaveRicevuti2T + "");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.6", requestLocale), triangolazioniFatte + "/" + triangolazioniChiuse, triangolazioniFatte1T + "/" + triangolazioniChiuse1T, triangolazioniFatte2T + "/" + triangolazioniChiuse2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.7", requestLocale), palleLateraliRicevute + "", palleLateraliRicevute1T + "", palleLateraliRicevute2T + "");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.8", requestLocale), dribblingVinti + "/" + dribblingTotali, dribblingVinti1T + "/" + dribblingTotali1T, dribblingVinti2T + "/" + dribblingTotali2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.9", requestLocale), tiriDaArea + "/" + tiriTotali, tiriDaArea1T + "/" + tiriTotali1T, tiriDaArea2T + "/" + tiriTotali2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.10", requestLocale), occasioniGoal + "/" + azioniPromettenti, occasioniGoal1T + "/" + azioniPromettenti1T, occasioniGoal2T + "/" + azioniPromettenti2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.11", requestLocale), percRealizzazioneGoal + "%", percRealizzazioneGoal1T + "%", percRealizzazioneGoal2T + "%");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.12", requestLocale), palleRecuperateMetaOffensiva + "", palleRecuperateMetaOffensiva1T + "", palleRecuperateMetaOffensiva2T + "");
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.13", requestLocale), duelliVintiMetaOffensiva + "/" + duelliPersiMetaOffensiva, duelliVintiMetaOffensiva1T + "/" + duelliPersiMetaOffensiva1T, duelliVintiMetaOffensiva2T + "/" + duelliPersiMetaOffensiva2T);
            result.add(row);
            row = new PlayerStudioTableItem(SpringApplicationContextHelper.getMessage("player.studio.attaccante.14", requestLocale), tocchiPallaMetaDifensiva + "", tocchiPallaMetaDifensiva1T + "", tocchiPallaMetaDifensiva2T + "");
            result.add(row);
        }

        return result;
    }

    public static PlayerStudioItem getSecondZone(List<Azione> events, Game fixture, Atleta player, Locale requestLocale) {
        PlayerStudioItem result = new PlayerStudioItem();

        List<Azione> validEvents = new ArrayList<>();
        Map<Integer, TableValue> rows = new LinkedHashMap<>();
        for (Azione event : events) {
            if (StringUtils.isNotBlank(event.getmType())) {
                boolean isFirstHalf = GlobalHelper.isActionInFirstHalf(event);

                if (isPlayerInEvent(event, player.getId()) && event.getPlayerToId() > 0) {
                    // Passaggi Riusciti
                    if (StringUtils.equals(event.getmType(), "PASS")) {
                        if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                            for (Tags tag : event.getmTags()) {
                                if (StringUtils.equals(tag.getCode(), "PASS-0")) {
                                    rows.putIfAbsent(event.getPlayerToId(), new TableValue());
                                    TableValue values = rows.get(event.getPlayerToId());
                                    if (event.getPlayerTo() != null) {
                                        if (event.getPlayerTo().getNumero() != null) {
                                            values.setNumber(event.getPlayerTo().getNumero().toString());
                                        }
                                        if (StringUtils.isNotBlank(event.getPlayerTo().getKnown_name())) {
                                            values.setPlayerName(event.getPlayerTo().getKnown_name());
                                        }
                                    }
                                    values.setTotal(values.getTotal() + 1);
                                    if (isFirstHalf) {
                                        values.setFirstTime(values.getFirstTime() + 1);
                                    } else {
                                        values.setSecondTime(values.getSecondTime() + 1);
                                    }

                                    validEvents.add(event);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
        }

        result.setEvents(validEvents);
        if (!rows.isEmpty()) {
            result.setTableRows(new ArrayList<PlayerStudioTableItem>());

            List<TableValue> sortedValues = new ArrayList<>(rows.values());
            Collections.sort(sortedValues, new Comparator<TableValue>() {
                @Override
                public int compare(TableValue o1, TableValue o2) {
                    if (Integer.compare(o2.getTotal(), o1.getTotal()) == 0) {
                        return o1.getPlayerName().toLowerCase().compareTo(o2.getPlayerName().toLowerCase());
                    } else {
                        return o2.getTotal().compareTo(o1.getTotal());
                    }
                }
            });

            for (TableValue row : sortedValues) {
                if (StringUtils.isNotBlank(row.getNumber())
                        && StringUtils.isNotBlank(row.getPlayerName())) {
                    result.getTableRows().add(new PlayerStudioTableItem(row.getNumber(), row.getPlayerName(), row.getTotal().toString(), row.getFirstTime().toString(), row.getSecondTime().toString()));
                }
            }
        }

        return result;
    }

    public static PlayerStudioItem getThirdZone(List<Azione> events, Game fixture, Atleta player, Locale requestLocale) {
        PlayerStudioItem result = new PlayerStudioItem();

        List<Azione> validEvents = new ArrayList<>();
        Map<Integer, TableValue> rows = new LinkedHashMap<>();
        for (Azione event : events) {
            if (StringUtils.isNotBlank(event.getmType())) {
                boolean isFirstHalf = GlobalHelper.isActionInFirstHalf(event);

                if (Integer.compare(event.getPlayerToId(), player.getId()) == 0) {
                    // Passaggi Ricevuti
                    if (StringUtils.equals(event.getmType(), "PASS")) {
                        if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                            boolean isImpreciso = false;
                            for (Tags tag : event.getmTags()) {
                                if (StringUtils.equals(tag.getCode(), "PASS-1")) {
                                    isImpreciso = true;
                                    break;
                                }
                            }

                            if (!isImpreciso) {
                                rows.putIfAbsent(event.getPlayerId(), new TableValue());
                                TableValue values = rows.get(event.getPlayerId());
                                if (event.getPlayer() != null && !event.getPlayer().isEmpty()) {
                                    if (event.getPlayer().get(event.getPlayer().keySet().iterator().next()).getNumero() != null) {
                                        values.setNumber(event.getPlayer().get(event.getPlayer().keySet().iterator().next()).getNumero().toString());
                                    }
                                    if (StringUtils.isNotBlank(event.getPlayer().get(event.getPlayer().keySet().iterator().next()).getKnown_name())) {
                                        values.setPlayerName(event.getPlayer().get(event.getPlayer().keySet().iterator().next()).getKnown_name());
                                    }
                                }
                                values.setTotal(values.getTotal() + 1);
                                if (isFirstHalf) {
                                    values.setFirstTime(values.getFirstTime() + 1);
                                } else {
                                    values.setSecondTime(values.getSecondTime() + 1);
                                }

                                validEvents.add(event);
                            }
                        }
                    }
                }
            }
        }

        result.setEvents(validEvents);
        if (!rows.isEmpty()) {
            result.setTableRows(new ArrayList<PlayerStudioTableItem>());

            List<TableValue> sortedValues = new ArrayList<>(rows.values());
            Collections.sort(sortedValues, new Comparator<TableValue>() {
                @Override
                public int compare(TableValue o1, TableValue o2) {
                    if (Integer.compare(o2.getTotal(), o1.getTotal()) == 0) {
                        return o1.getPlayerName().toLowerCase().compareTo(o2.getPlayerName().toLowerCase());
                    } else {
                        return o2.getTotal().compareTo(o1.getTotal());
                    }
                }
            });

            for (TableValue row : sortedValues) {
                if (StringUtils.isNotBlank(row.getNumber())
                        && StringUtils.isNotBlank(row.getPlayerName())) {
                    result.getTableRows().add(new PlayerStudioTableItem(row.getNumber(), row.getPlayerName(), row.getTotal().toString(), row.getFirstTime().toString(), row.getSecondTime().toString()));
                }
            }
        }

        return result;
    }

    public static PlayerStudioItem getFourthZone(List<Azione> events, Game fixture, Atleta player, Locale requestLocale) {
        PlayerStudioItem result = new PlayerStudioItem();

        List<Azione> validEvents = new ArrayList<>();
        Map<Integer, TableValue> rows = new LinkedHashMap<>();
        for (Azione event : events) {
            if (StringUtils.isNotBlank(event.getmType())) {
                if (isPlayerInEvent(event, player.getId())) {
                    // Duelli Vinti
                    if (StringUtils.equals(event.getmType(), "DLL")) {
                        Azione relativeEvent = getRelativeAction(events, event);
                        if (relativeEvent != null) {
                            rows.putIfAbsent(relativeEvent.getPlayerId(), new TableValue());
                            TableValue values = rows.get(relativeEvent.getPlayerId());
                            if (relativeEvent.getPlayer() != null) {
                                if (relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getNumero() != null) {
                                    values.setNumber(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getNumero().toString());
                                }
                                if (StringUtils.isNotBlank(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getKnown_name())) {
                                    values.setPlayerName(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getKnown_name());
                                }
                            }
                            values.setFirstValue(values.getFirstValue() + 1);
                            values.setSecondValue(values.getSecondValue() + 1);

                            validEvents.add(event);
                        }
                    } else if (StringUtils.equals(event.getmType(), "DLS")) {
                        // Duelli Persi
                        Azione relativeEvent = getRelativeAction(events, event);
                        if (relativeEvent != null) {
                            rows.putIfAbsent(relativeEvent.getPlayerId(), new TableValue());
                            TableValue values = rows.get(relativeEvent.getPlayerId());
                            if (relativeEvent.getPlayer() != null) {
                                if (relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getNumero() != null) {
                                    values.setNumber(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getNumero().toString());
                                }
                                if (StringUtils.isNotBlank(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getKnown_name())) {
                                    values.setPlayerName(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getKnown_name());
                                }
                            }
                            values.setSecondValue(values.getSecondValue() + 1);

                            validEvents.add(event);
                        }
                    }

                    // Dribbling Vinti
                    if (StringUtils.equals(event.getmType(), "DRF")) {
                        Azione relativeEvent = getRelativeAction(events, event);
                        if (relativeEvent != null) {
                            rows.putIfAbsent(relativeEvent.getPlayerId(), new TableValue());
                            TableValue values = rows.get(relativeEvent.getPlayerId());
                            if (relativeEvent.getPlayer() != null) {
                                if (relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getNumero() != null) {
                                    values.setNumber(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getNumero().toString());
                                }
                                if (StringUtils.isNotBlank(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getKnown_name())) {
                                    values.setPlayerName(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getKnown_name());
                                }
                            }
                            values.setThirdValue(values.getThirdValue() + 1);
                            values.setFourthValue(values.getFourthValue() + 1);

                            validEvents.add(event);
                        }
                    } else if (StringUtils.equals(event.getmType(), "DRS")) {
                        // Dribbling Persi
                        Azione relativeEvent = getRelativeAction(events, event);
                        if (relativeEvent != null) {
                            rows.putIfAbsent(relativeEvent.getPlayerId(), new TableValue());
                            TableValue values = rows.get(relativeEvent.getPlayerId());
                            if (relativeEvent.getPlayer() != null) {
                                if (relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getNumero() != null) {
                                    values.setNumber(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getNumero().toString());
                                }
                                if (StringUtils.isNotBlank(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getKnown_name())) {
                                    values.setPlayerName(relativeEvent.getPlayer().get(relativeEvent.getPlayer().keySet().iterator().next()).getKnown_name());
                                }
                            }
                            values.setFourthValue(values.getFourthValue() + 1);

                            validEvents.add(event);
                        }
                    }
                }
            }
        }

        result.setEvents(validEvents);
        if (!rows.isEmpty()) {
            result.setTableRows(new ArrayList<PlayerStudioTableItem>());

            List<TableValue> sortedValues = new ArrayList<>(rows.values());
            Collections.sort(sortedValues, new Comparator<TableValue>() {
                @Override
                public int compare(TableValue o1, TableValue o2) {
                    Integer firstElementValue = o1.getFirstValue() + o1.getSecondValue();
                    Integer secondElementValue = o2.getFirstValue() + o2.getSecondValue();
                    if (Integer.compare(firstElementValue, secondElementValue) == 0) {
                        return o1.getPlayerName().toLowerCase().compareTo(o2.getPlayerName().toLowerCase());
                    } else {
                        return secondElementValue.compareTo(firstElementValue);
                    }
                }
            });

            for (TableValue row : sortedValues) {
                if (StringUtils.isNotBlank(row.getNumber())
                        && StringUtils.isNotBlank(row.getPlayerName())) {
                    result.getTableRows().add(new PlayerStudioTableItem(row.getNumber(), row.getPlayerName(), row.getFirstValue() + "/" + row.getSecondValue(), row.getThirdValue() + "/" + row.getFourthValue(), ""));
                }
            }
        }

        return result;
    }

    public static PlayerStudioItem getFifthZone(List<Azione> events, Game fixture, Atleta player, Locale requestLocale) {
        PlayerStudioItem result = new PlayerStudioItem();

        List<Azione> validEvents = new ArrayList<>();
        Map<Integer, TableValue> rows = new LinkedHashMap<>();
        rows.putIfAbsent(1, new TableValue());
        rows.get(1).setPlayerName(SpringApplicationContextHelper.getMessage("player.studio.tocchi.1", requestLocale));
        rows.putIfAbsent(2, new TableValue());
        rows.get(2).setPlayerName(SpringApplicationContextHelper.getMessage("player.studio.tocchi.2", requestLocale));
        rows.putIfAbsent(3, new TableValue());
        rows.get(3).setPlayerName(SpringApplicationContextHelper.getMessage("player.studio.tocchi.3", requestLocale));
        rows.putIfAbsent(4, new TableValue());
        rows.get(4).setPlayerName(SpringApplicationContextHelper.getMessage("player.studio.tocchi.4", requestLocale));
        rows.putIfAbsent(5, new TableValue());
        rows.get(5).setPlayerName(SpringApplicationContextHelper.getMessage("player.studio.tocchi.5", requestLocale));
        rows.putIfAbsent(6, new TableValue());
        rows.get(6).setPlayerName(SpringApplicationContextHelper.getMessage("player.studio.tocchi.6", requestLocale));
        rows.putIfAbsent(7, new TableValue());
        rows.get(7).setPlayerName(SpringApplicationContextHelper.getMessage("player.studio.tocchi.7", requestLocale));
        rows.putIfAbsent(8, new TableValue());
        rows.get(8).setPlayerName(SpringApplicationContextHelper.getMessage("player.studio.tocchi.8", requestLocale));
        rows.putIfAbsent(9, new TableValue());
        rows.get(9).setPlayerName(SpringApplicationContextHelper.getMessage("player.studio.tocchi.9", requestLocale));
        rows.putIfAbsent(10, new TableValue());
        rows.get(10).setPlayerName(SpringApplicationContextHelper.getMessage("player.studio.tocchi.10", requestLocale));

        for (Azione event : events) {
            if (StringUtils.isNotBlank(event.getmType())) {
                if (isPlayerInEvent(event, player.getId())) {
                    if (isToccoPalla(event)) {
                        boolean isFirstHalf = GlobalHelper.isActionInFirstHalf(event);
                        float distanzaDalFondo = GlobalHelper.calcolaDistanzaDalFondo(event, fixture.getTeamSx(), fixture.getTeamSx2(), fixture);
                        float distanzaInOrizzontale = GlobalHelper.calcolaDistanzaDaLatoCorto(event, fixture.getTeamSx(), fixture.getTeamSx2(), fixture);

                        if (GlobalHelper.checkInArea(event.getmActionPos())
                                && distanzaDalFondo <= (GlobalHelper.kLatoLungoCampo / 3D)) {
                            // Propria Area
                            TableValue values = rows.get(1);
                            values.setTotal(values.getTotal() + 1);
                            if (isFirstHalf) {
                                values.setFirstTime(values.getFirstTime() + 1);
                            } else {
                                values.setSecondTime(values.getSecondTime() + 1);
                            }
                        }
                        if (GlobalHelper.checkInArea(event.getmActionPos())
                                && distanzaDalFondo > ((GlobalHelper.kLatoLungoCampo / 3D) * 2)) {
                            // Area Avversaria
                            TableValue values = rows.get(2);
                            values.setTotal(values.getTotal() + 1);
                            if (isFirstHalf) {
                                values.setFirstTime(values.getFirstTime() + 1);
                            } else {
                                values.setSecondTime(values.getSecondTime() + 1);
                            }
                        }
                        if (distanzaDalFondo <= (GlobalHelper.kLatoLungoCampo / 3D)) {
                            // Terzo Difensivo
                            TableValue values = rows.get(3);
                            values.setTotal(values.getTotal() + 1);
                            if (isFirstHalf) {
                                values.setFirstTime(values.getFirstTime() + 1);
                            } else {
                                values.setSecondTime(values.getSecondTime() + 1);
                            }
                        }
                        if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 3D) && distanzaDalFondo < (GlobalHelper.kLatoLungoCampo / 3D * 2)) {
                            // Terzo Centrale
                            TableValue values = rows.get(4);
                            values.setTotal(values.getTotal() + 1);
                            if (isFirstHalf) {
                                values.setFirstTime(values.getFirstTime() + 1);
                            } else {
                                values.setSecondTime(values.getSecondTime() + 1);
                            }
                        }
                        if (distanzaDalFondo > (GlobalHelper.kLatoLungoCampo / 3D * 2)) {
                            // Terzo Offensivo
                            TableValue values = rows.get(5);
                            values.setTotal(values.getTotal() + 1);
                            if (isFirstHalf) {
                                values.setFirstTime(values.getFirstTime() + 1);
                            } else {
                                values.setSecondTime(values.getSecondTime() + 1);
                            }
                        }
                        if (distanzaInOrizzontale <= (GlobalHelper.kLatoCortoCampo / 5D)) {
                            // Canale 1
                            TableValue values = rows.get(6);
                            values.setTotal(values.getTotal() + 1);
                            if (isFirstHalf) {
                                values.setFirstTime(values.getFirstTime() + 1);
                            } else {
                                values.setSecondTime(values.getSecondTime() + 1);
                            }
                        } else if (distanzaInOrizzontale <= ((GlobalHelper.kLatoCortoCampo / 5D) * 2)) {
                            // Canale 2
                            TableValue values = rows.get(7);
                            values.setTotal(values.getTotal() + 1);
                            if (isFirstHalf) {
                                values.setFirstTime(values.getFirstTime() + 1);
                            } else {
                                values.setSecondTime(values.getSecondTime() + 1);
                            }
                        } else if (distanzaInOrizzontale <= ((GlobalHelper.kLatoCortoCampo / 5D) * 3)) {
                            // Canale 3
                            TableValue values = rows.get(8);
                            values.setTotal(values.getTotal() + 1);
                            if (isFirstHalf) {
                                values.setFirstTime(values.getFirstTime() + 1);
                            } else {
                                values.setSecondTime(values.getSecondTime() + 1);
                            }
                        } else if (distanzaInOrizzontale <= ((GlobalHelper.kLatoCortoCampo / 5D) * 4)) {
                            // Canale 4
                            TableValue values = rows.get(9);
                            values.setTotal(values.getTotal() + 1);
                            if (isFirstHalf) {
                                values.setFirstTime(values.getFirstTime() + 1);
                            } else {
                                values.setSecondTime(values.getSecondTime() + 1);
                            }
                        } else {
                            // Canale 5
                            TableValue values = rows.get(10);
                            values.setTotal(values.getTotal() + 1);
                            if (isFirstHalf) {
                                values.setFirstTime(values.getFirstTime() + 1);
                            } else {
                                values.setSecondTime(values.getSecondTime() + 1);
                            }
                        }

                        validEvents.add(event);
                    }
                }
            }
        }

        result.setEvents(validEvents);
        if (!rows.isEmpty()) {
            result.setTableRows(new ArrayList<PlayerStudioTableItem>());

            List<TableValue> sortedValues = new ArrayList<>(rows.values());
            for (TableValue row : sortedValues) {
                if (StringUtils.isNotBlank(row.getPlayerName())) {
                    result.getTableRows().add(new PlayerStudioTableItem(row.getPlayerName(), row.getTotal().toString(), row.getFirstTime().toString(), row.getSecondTime().toString()));
                }
            }
        }

        return result;
    }

    public static String getSixthZone(List<Azione> events, Game fixture, Atleta player, Locale requestLocale) {
        String result = null;

        List<TableValue> tocchiSquadraList = new ArrayList<>();
        int durataTempo = 45 * 60 * 1000; // in millisecondi
        int intervalloTempo = durataTempo / 3;

        // calcolo primo e secondo tempo
        for (int i = 1; i <= 6; i++) {
            int totalePlayerValidi = 0; // uso per calcolare la media

            int maxStartAzione, minStartAzione = 0;
            minStartAzione = intervalloTempo * (i - 1);
            maxStartAzione = intervalloTempo * i;
            totalePlayerValidi = 10; // dovrei calcolarlo ma bisogna fare un sacco di codice

            int totaleSquadra = 0;
            int totalePlayer = 0;
            for (Azione tmpAzione : events) {
                if (player.getTeamPlayer() != null && !player.getTeamPlayer().isEmpty()) {
                    if (tmpAzione.getIdTeam() == player.getTeamPlayer().get(0).getId()) {
                        if (isToccoPalla(tmpAzione)) {
                            if (tmpAzione.getmStart() >= minStartAzione && tmpAzione.getmStart() <= maxStartAzione) {
                                totaleSquadra++;
                            }
                        }
                    }
                }
            }
            for (Azione tmpAzione : events) {
                if (player.getTeamPlayer() != null && !player.getTeamPlayer().isEmpty()) {
                    if (isPlayerInEvent(tmpAzione, player.getId()) && isToccoPalla(tmpAzione)) {
                        if (tmpAzione.getmStart() >= minStartAzione && tmpAzione.getmStart() <= maxStartAzione) {
                            totalePlayer++;
                        }
                    }
                }
            }

            int mediaSquadra = totaleSquadra / totalePlayerValidi;
            TableValue tocchiSquadra = new TableValue();
            tocchiSquadra.setNumber(String.valueOf(intervalloTempo * i / 60 / 1000));
            tocchiSquadra.setFirstValue(mediaSquadra);
            tocchiSquadra.setSecondValue(totalePlayer);
            tocchiSquadraList.add(tocchiSquadra);
        }

        String data = "[";
        int counter = 0;
        for (int i = 0; i < tocchiSquadraList.size(); i++) {
            TableValue tocchiSquadraValue = tocchiSquadraList.get(i);
            int tocchiPlayerNextValue = -1;
            if (tocchiSquadraList.size() > i + 1) {
                TableValue tocchiSquadraNextValue = tocchiSquadraList.get(i + 1);
                tocchiPlayerNextValue = tocchiSquadraNextValue.getSecondValue();
            }

            if (counter > 0) {
                data += ",";
            }
            data += "{";
            data += "\"year\": \"" + tocchiSquadraValue.getNumber() + "'\",";
            data += "\"value\": " + tocchiSquadraValue.getFirstValue() + "";
            if (tocchiSquadraValue.getSecondValue() > 0 || tocchiPlayerNextValue > 0) {
                data += ",\"value2\": " + tocchiSquadraValue.getSecondValue() + "";
            }
            data += "}";
            counter++;
        }
        data += "]";
        result = data;

        return result;
    }

    private static boolean isToccoPalla(Azione event) {
        boolean isToccoPalla = false;

        if (StringUtils.equals(event.getmType(), "PASS")) {
            isToccoPalla = true;
        } else if (StringUtils.equals(event.getmType(), "INT")) {
            isToccoPalla = true;
        } else if (StringUtils.equals(event.getmType(), "TIF")) {
            isToccoPalla = true;
        } else if (StringUtils.equals(event.getmType(), "DLL")) {
            isToccoPalla = true;
        } else if (StringUtils.equals(event.getmType(), "DLS")) {
            isToccoPalla = true;
        } else if (StringUtils.equals(event.getmType(), "COND")) {
            isToccoPalla = true;
        } else if (StringUtils.equals(event.getmType(), "GKP")) {
            isToccoPalla = true;
        } else if (StringUtils.equals(event.getmType(), "BBC")) {
            isToccoPalla = true;
        }

        return isToccoPalla;
    }

    private static boolean isPallaLaterale(Azione event) {
        boolean isPallaLaterale = false;

        if (StringUtils.equals(event.getmType(), "PLF")) {
            isPallaLaterale = true;
            if (event.getmTags() != null && !event.getmTags().isEmpty()) {
                for (Tags tag : event.getmTags()) {
                    if (StringUtils.equals(tag.getCode(), "PLF-12")) {
                        isPallaLaterale = false;
                        break;
                    }
                    if (StringUtils.equals(tag.getCode(), "PLF-8")) {
                        isPallaLaterale = false;
                        break;
                    }
                }
            }
        } else if (StringUtils.equals(event.getmType(), "CRF")) {
            isPallaLaterale = true;
        }

        return isPallaLaterale;
    }

    private static boolean isPlayerInEvent(Azione event, Integer playerId) {
        boolean isInEvent = false;

        if (event.getPlayer() != null && !event.getPlayer().isEmpty()) {
            for (Integer eventPlayerId : event.getPlayer().keySet()) {
                if (Integer.compare(eventPlayerId, playerId) == 0) {
                    isInEvent = true;
                    break;
                }
            }
        } else if (Integer.compare(event.getPlayerId(), playerId) == 0) {
            isInEvent = true;
        }

        return isInEvent;
    }

    private static Azione getRelativeAction(List<Azione> events, Azione baseEvent) {
        if (events != null && !events.isEmpty()) {
            for (Azione event : events) {
                if (event.getmActionPos().x == baseEvent.getmActionPos().x
                        && event.getmActionPos().y == baseEvent.getmActionPos().y) {
                    if (StringUtils.equals(baseEvent.getmType(), "DLL")) {
                        if (StringUtils.equals(event.getmType(), "DLS")) {
                            return event;
                        }
                    } else if (StringUtils.equals(baseEvent.getmType(), "DLS")) {
                        if (StringUtils.equals(event.getmType(), "DLL")) {
                            return event;
                        }
                    } else if (StringUtils.equals(baseEvent.getmType(), "DRF")) {
                        if (StringUtils.equals(event.getmType(), "DRS")) {
                            return event;
                        }
                    } else if (StringUtils.equals(baseEvent.getmType(), "DRS")) {
                        if (StringUtils.equals(event.getmType(), "DRF")) {
                            return event;
                        }
                    }
                }
            }
        }
        return null;
    }

    private static class TableValue {

        private String number;
        private String playerName;
        private Integer total;
        private Integer firstTime, secondTime;

        private Integer firstValue, secondValue;
        private Integer thirdValue, fourthValue;

        public TableValue() {
            this.total = 0;
            this.firstTime = 0;
            this.secondTime = 0;

            this.firstValue = 0;
            this.secondValue = 0;
            this.thirdValue = 0;
            this.fourthValue = 0;
        }

        public String getNumber() {
            return number;
        }

        public void setNumber(String number) {
            this.number = number;
        }

        public String getPlayerName() {
            return playerName;
        }

        public void setPlayerName(String playerName) {
            this.playerName = playerName;
        }

        public Integer getTotal() {
            return total;
        }

        public void setTotal(Integer total) {
            this.total = total;
        }

        public Integer getFirstTime() {
            return firstTime;
        }

        public void setFirstTime(Integer firstTime) {
            this.firstTime = firstTime;
        }

        public Integer getSecondTime() {
            return secondTime;
        }

        public void setSecondTime(Integer secondTime) {
            this.secondTime = secondTime;
        }

        public Integer getFirstValue() {
            return firstValue;
        }

        public void setFirstValue(Integer firstValue) {
            this.firstValue = firstValue;
        }

        public Integer getSecondValue() {
            return secondValue;
        }

        public void setSecondValue(Integer secondValue) {
            this.secondValue = secondValue;
        }

        public Integer getThirdValue() {
            return thirdValue;
        }

        public void setThirdValue(Integer thirdValue) {
            this.thirdValue = thirdValue;
        }

        public Integer getFourthValue() {
            return fourthValue;
        }

        public void setFourthValue(Integer fourthValue) {
            this.fourthValue = fourthValue;
        }
    }
}
