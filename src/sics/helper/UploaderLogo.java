package sics.helper;

import com.amazonaws.event.ProgressEvent;
import com.amazonaws.event.ProgressEventType;
import com.amazonaws.event.ProgressListener;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.StorageClass;
import java.io.File;

public class UploaderLogo {

    private static final String bucketLogo = "it.sics.logo";
    private static final String bucketThumb = "it.sics.svs.thumb";
    private static Integer index = 0;
//
    private static final ProgressEventType UPLOADING = ProgressEventType.TRANSFER_STARTED_EVENT;
    private static final ProgressEventType COMPLETED = ProgressEventType.TRANSFER_COMPLETED_EVENT;
    private static final ProgressEventType ERROR = ProgressEventType.TRANSFER_FAILED_EVENT;

    private Integer id;
    private String filePath = "";
    private String desc;

    private Boolean completo = false;
    private Float avanzamento = 0f;
    private String keyName = "";

    private File mFile;
    // -1 thumb 0 player, 1 competition, 2 team, 3 country
    private int mType = 0;
    private int mCount = 0;

    public String upload(File file, final Long groupsetId, int type, final String thumbKeyName) {
        try {
            mFile = file;
            mType = type;
            GlobalHelper.initS3ClientLogo();
            if (GlobalHelper.s3LogoClient == null) {
                // LoggerHelper.print("upload: errore creazione s3LogoClient", false);
                return "";
            }
            String fileName = "";
            String bucket = bucketLogo;
            boolean thumb = false;
            switch (mType) {
                case -1:
                    bucket = bucketThumb;
                    if (thumbKeyName != null && !thumbKeyName.isEmpty()) {
                        thumb = true;
                    }
                    fileName = "";
                    break;
                case 0:
                    fileName = "120/players/";
                    break;
                case 1:
                    fileName = "120/competitionslogo/";
                    break;
                case 2:
                    fileName = "120/teamlogo/";
                    break;
                case 3:
                    fileName = "120/country/";
                    break;
                case 4:
                    fileName = "120/agentlogo/";
                    break;
                default:
                    break;
            }
            String name = file.getName();
//            if (!GlobalHelper.INTERNO) {
//                if (thumb) {
//                    name = thumbKeyName.replace(".mp4", ".png");
//                } else {
//                    name = GlobalHelper.localUser.getGroupset_id() + "/" + (thumb ? GlobalHelper.localUser.getId() + "/VM/" : "") + name;
//                }
//            }
            if (Long.compare(groupsetId, 2) == 0) {
                keyName = fileName + name;
            } else {
                keyName = fileName + groupsetId + "/" + name;
            }

            final PutObjectRequest putObjectRequest = new PutObjectRequest(bucket, keyName, file).withStorageClass(StorageClass.Standard);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType("image/png");
            putObjectRequest.withMetadata(metadata);

            // You can ask the upload for its progress, or you can 
            // add a ProgressListener to your request to receive notifications 
            // when bytes are transferred.
            putObjectRequest.setGeneralProgressListener(new ProgressListener() {
                @Override
                public void progressChanged(ProgressEvent progressEvent) {
                    ProgressEventType type = progressEvent.getEventType();
                    if (type.equals(UPLOADING)) {
                    } else if (type.equals(ERROR)) {
                        //###### ERRORE ######
                        mCount++;
                        // retry upload
                        if (mCount < 10) {
                            try {
                                Thread.sleep(5000);
                            } catch (InterruptedException ex) {
                            }
                            upload(mFile, groupsetId, mType, thumbKeyName);
                        }
                    }
                }

            });
            GlobalHelper.s3LogoClient.putObject(putObjectRequest);
            return keyName;
        } catch (com.amazonaws.SdkClientException ex) {
            GlobalHelper.reportError(ex);
            // 2022/06 aggiunto ritenta al SdkClientException Exception
            mCount++;
            // retry upload
            if (mCount < 10) {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException ex2) {
                }
                return upload(mFile, groupsetId, mType, thumbKeyName);
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return "";
    }

    public Integer getId() {
        return id;
    }

    public String getFilePath() {
        return filePath;
    }

    public String getDesc() {
        return desc;
    }

    public Float getAvanzamento() {
        return avanzamento;
    }
}
