package sics.helper;

import java.util.HashMap;
import org.bson.BsonDocumentReader;
import org.bson.BsonReader;
import org.bson.BsonWriter;
import org.bson.Document;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.DocumentCodec;
import org.bson.codecs.EncoderContext;
import org.bson.codecs.configuration.CodecRegistry;
import sics.domain.Cache;
import sics.domain.Statistic;

/**
 *
 * <AUTHOR>
 */
public class SmartSearchPlayerCodec implements Codec<Cache> {

    private final Codec<Cache> defaultCodec;
    private final CodecRegistry codecRegistry;

    public SmartSearchPlayerCodec(Codec<Cache> defaultCodec, CodecRegistry registry) {
        this.defaultCodec = defaultCodec;
        this.codecRegistry = registry;
    }

    @Override
    public void encode(BsonWriter writer, Cache value, EncoderContext encoderContext) {
    }

    @Override
    public Cache decode(BsonReader reader, DecoderContext decoderContext) {
        // prima parso tutti i campi base
        Document document = new DocumentCodec().decode(reader, decoderContext);
        Cache cache = defaultCodec.decode(new BsonDocumentReader(document.toBsonDocument(Cache.class, codecRegistry)), decoderContext);
        for (String fieldName : document.keySet()) {
            if (fieldName.equalsIgnoreCase("playerSeasons")) {
                cache.setSeasons(document.getString(fieldName));
            } else if (fieldName.startsWith("statsType")) {
                Long typeId = Long.valueOf(fieldName.replace("statsType", "").replace("P90", "").replace("Percentage", ""));
                Statistic statistic = new Statistic();
                statistic.setId(typeId);
                statistic.setName(typeId.toString());
                
                if (fieldName.contains("P90")) {
                    statistic.setValueP90(document.getDouble(fieldName));
                    if (cache.getPlayerStatistics() == null) {
                        cache.setPlayerStatistics(new HashMap<Long, Statistic>());
                    }
                    if (cache.getPlayerStatistics().containsKey(typeId)) {
                        cache.getPlayerStatistics().get(typeId).setValueP90(statistic.getValueP90());
                    } else {
                        cache.getPlayerStatistics().put(typeId, statistic);
                    }
                } else if (fieldName.contains("Percentage")) {
                    statistic.setValuePercentage(document.getDouble(fieldName));
                    if (cache.getPlayerStatistics() == null) {
                        cache.setPlayerStatistics(new HashMap<Long, Statistic>());
                    }
                    if (cache.getPlayerStatistics().containsKey(typeId)) {
                        cache.getPlayerStatistics().get(typeId).setValuePercentage(statistic.getValuePercentage());
                    } else {
                        cache.getPlayerStatistics().put(typeId, statistic);
                    }
                } else {
                    statistic.setValue(document.getDouble(fieldName));
                    if (cache.getPlayerStatistics() == null) {
                        cache.setPlayerStatistics(new HashMap<Long, Statistic>());
                    }
                    if (cache.getPlayerStatistics().containsKey(typeId)) {
                        cache.getPlayerStatistics().get(typeId).setValue(statistic.getValue());
                    } else {
                        cache.getPlayerStatistics().put(typeId, statistic);
                    }
                }
            }
        }
        
//        reader.readStartDocument();
//        while (reader.readBsonType() != BsonType.END_OF_DOCUMENT) {
//            String fieldName = reader.readName();
//            if (fieldName.startsWith("statsType")) {
//                Long typeId = Long.valueOf(fieldName.replace("statsType", ""));
//                Statistic statistic = new Statistic();
//                statistic.setId(typeId);
//                statistic.setName(typeId.toString());
//                
//                if (fieldName.contains("P90")) {
//                    statistic.setValueP90(reader.readDouble());
//                    if (cache.getPlayerStatistics() == null) {
//                        cache.setPlayerStatistics(new HashMap<Long, Statistic>());
//                    }
//                    if (cache.getPlayerStatistics().containsKey(typeId)) {
//                        cache.getPlayerStatistics().get(typeId).setValueP90(statistic.getValue());
//                    } else {
//                        cache.getPlayerStatistics().put(typeId, statistic);
//                    }
//                } else if (fieldName.contains("Percentage")) {
//                    statistic.setValuePercentage(reader.readDouble());
//                    if (cache.getPlayerStatistics() == null) {
//                        cache.setPlayerStatistics(new HashMap<Long, Statistic>());
//                    }
//                    if (cache.getPlayerStatistics().containsKey(typeId)) {
//                        cache.getPlayerStatistics().get(typeId).setValuePercentage(statistic.getValue());
//                    } else {
//                        cache.getPlayerStatistics().put(typeId, statistic);
//                    }
//                } else {
//                    statistic.setValue(reader.readDouble());
//                    if (cache.getPlayerStatistics() == null) {
//                        cache.setPlayerStatistics(new HashMap<Long, Statistic>());
//                    }
//                    if (cache.getPlayerStatistics().containsKey(typeId)) {
//                        cache.getPlayerStatistics().get(typeId).setValue(statistic.getValue());
//                    } else {
//                        cache.getPlayerStatistics().put(typeId, statistic);
//                    }
//                }
//            } else {
//                // questi campi dovrebbero già essere stati processati sopra dal defaultCodec
//                reader.skipValue();
//            }
//        }
//        reader.readEndDocument();
        return cache;
    }

    // Returns an instance of the v class, since Java cannot infer the class type
    @Override
    public Class<Cache> getEncoderClass() {
        return Cache.class;
    }
}
