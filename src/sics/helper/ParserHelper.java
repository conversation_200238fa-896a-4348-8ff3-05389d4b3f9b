/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.helper;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Set;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.JDOMException;
import org.jdom2.input.SAXBuilder;
import sics.domain.Atleta;
import sics.domain.Button;
import sics.domain.EventDescription;
import sics.domain.Filtro;
import sics.domain.Game;
import sics.domain.Tags;
import sics.model.Azione;

/**
 *
 * <AUTHOR>
 */
public class ParserHelper {

    private Long tempoPartita[] = new Long[5];
    private String squadra1;
    private String squadra2;
    private Map<String, List> mapEventDescription = new HashMap();
    private List listaFinale = new ArrayList();
    private ArrayList<Atleta> formazione1 = new ArrayList();
    private ArrayList<Atleta> formazione2 = new ArrayList();

    public void parserizeFilter(String nomeXml, String team, String azione, String sorgente, String tempo, String giocatore, boolean isAia, Long groupSetId, Game game) throws Exception {
        try {
            parserizeTotal(nomeXml, isAia, groupSetId, game);
            List listaTmp = new ArrayList();
            ListIterator it = listaFinale.listIterator();
            //se la stringa è vuota crea una stringa vuota nell'array
            String[] listaTeam = team.split("##");
            String[] listaAzione = azione.split("##");
            String[] listaSorgente = sorgente.split("##");
            String[] listaTempo = tempo.split("##");
            String[] listaGiocatore = giocatore.split("##");

            //Genero l'oggetto contenente i filtri delle azioni
            HashMap<String, Filtro> filtriAzioni = new HashMap();//Lista di "Filtro"
            for (int i = 0; i < listaAzione.length; i++) {
                String[] azioni = listaAzione[i].split("@@");
                Filtro filtro = null;
                if (azioni.length == 2) {//Se è una azione normale
                    filtro = new Filtro();
                    filtro.setAzione(azioni[1]);
                    filtriAzioni.put(azioni[1], filtro);
                }
                if (azioni.length == 3) {//Se è un tag
                    filtro = filtriAzioni.get(azioni[1]);
                    if (filtro == null) {//Se il filtro non esiste già lo creo e inserisco
                        filtro = new Filtro();
                        filtro.setAzione(azioni[1]);
                        filtriAzioni.put(azioni[1], filtro);
                    }
                    if (filtro.getTag() == null) {
                        filtro.setTag(new ArrayList());
                    }
                    filtro.getTag().add(azioni[2]);
                }
                //Se non è ne due ne tre allora è vuoto e non bisogna fare niente
            }

            while (it.hasNext()) {
                Azione item = (Azione) it.next();
                int var = 0;
                for (int i = 0; i < listaTeam.length; i++) {
                    if (listaTeam[i].length() == 0 || item.getmTeam().equalsIgnoreCase(listaTeam[i])) {
                        var++;
                    }
                }

                if (filtriAzioni.isEmpty()) {//se è vuoto la condizione è vera
                    var++;
                } else {
                    if (item.getButton() != null) {
                        String a = item.getButton().getCod();
                        Filtro filtroCorrente = filtriAzioni.get(a);
                        if (filtroCorrente == null) {//Se trovo quella azione nei filtri la controllo
                            //Non viene inserita l'azione
                        } else {
                            if (filtroCorrente.getTag() == null) {//Caso in cui non ci sono tag o non ne ho selezionati
                                var++;
                            } else {//Caso in cui devo trattare i tag
                                if (item.getButton().getTag() != null) {
                                    boolean trovato = false;
                                    if (filtroCorrente.getTag().size() != item.getButton().getTag().size()) {
                                        List tagsAzione = item.getmTags();
                                        for (int i = 0; i < tagsAzione.size(); i++) {
                                            List tagsFiltro = filtroCorrente.getTag();
                                            for (int j = 0; j < tagsFiltro.size(); j++) {
                                                String cod1 = ((Tags) tagsAzione.get(i)).getCode();
                                                String cod2 = (String) tagsFiltro.get(j);
                                                if (cod1.equalsIgnoreCase(cod2)) {
                                                    trovato = true;
                                                }
                                            }
                                        }
                                        if (trovato == true) {
                                            var++;
                                        }
                                    } else {//Se ho tutti i tag allora passo tutte le azioni di questo tipo
                                        var++;
                                    }
                                }
                            }
                        }
                    }
                }

                for (int i = 0; i < listaSorgente.length; i++) {
                    if (listaSorgente[i].length() == 0 || item.getGroupsetId().equalsIgnoreCase(listaSorgente[i])) {
                        var++;
                    }
                }
                for (int i = 0; i < listaTempo.length; i++) {
                    if (listaTempo[i].length() == 0 || item.getHalf() == Integer.parseInt(listaTempo[i])) {
                        var++;
                    }
                }
                boolean trovato = false;
                for (int i = 0; i < listaGiocatore.length; i++) {
                    if (listaGiocatore[i].equalsIgnoreCase(item.getTeam())) {
                        var++;
                    } else if (listaGiocatore[i].isEmpty()) {
                        var++;
                    } else {
                        for (Atleta atl : item.getPlayer().values()) {
                            if (atl.getKnown_name().toLowerCase().trim().equalsIgnoreCase(listaGiocatore[i].trim())) {
                                var++;
                                trovato = true;
                                break;
                            }
                        }
                        if (trovato) {
                            break;
                        }
                    }
                }
                if (var == 5) {//Controllo che tutte e tre le condizioni siano rispettate
                    listaTmp.add(item);
                }
            }
            listaFinale = listaTmp;
        } catch (Exception e) {
            throw (e);
        }
    }

    public void parserizeTotal(String nomeXml, boolean isAia, Long groupSetId, Game game) throws Exception {
        try {
            List listaEventUser = new ArrayList();
//			List listaEventSics = new ArrayList();

            //Se è un utente normale
            if (!isAia) {
                listaEventUser = parserizeFile(nomeXml, isAia, groupSetId, game);
//				String nomeXmlSICS = nomeXml.replaceAll(".xml", "_02.xml");
//				listaEventSics = parserizeFile(nomeXmlSICS, mApplication, groupSetId, game);
            } else {
                //Qui non assegno le azioni che ricevo dalla funzione, però la uso per leggere la prima riga dell'intestazione
                parserizeFile(nomeXml, isAia, groupSetId, game);
//				String nomeXmlSICS = nomeXml.replaceAll("_04.xml", "_02.xml");
//				listaEventSics = parserizeFile(nomeXmlSICS, mApplication, groupSetId, game);
            }

            //Esegui il merge degli eventi
            if (listaEventUser != null) {
                listaFinale.addAll(listaEventUser);
            }
//			if (listaEventSics != null) {
//				listaFinale.addAll(listaEventSics);
//			}
            Collections.sort(listaFinale, new AzioneComparatorHelper());
        } catch (Exception e) {
            throw (e);
        }
    }

    private List parserizeFile(String nomeXml, boolean isAia, Long groupSetId, Game game) throws Exception {
        List listaEvent = new ArrayList();
        Map<String, Map<String, Button>> listaPanel = new HashMap();
        Map<String, Button> listaButton;
        SAXBuilder builderPartita = new SAXBuilder();
        String percorsoFile = "";
        String cartella = "";
        //Se non ho un file SICS
        if (!nomeXml.contains("_02.xml")) {

            //Se ho un utente arbitro
            if (isAia) {
                percorsoFile = SpringServletContextHelper.getInitParameter("webRootXmlDataFolder") + File.separator + "3" + File.separator + nomeXml;
                cartella = "3";
            } else {
                percorsoFile = SpringServletContextHelper.getInitParameter("webRootXmlDataFolder") + groupSetId + File.separator + nomeXml;
                cartella = String.valueOf(groupSetId.longValue());
            }

        } else if (nomeXml.contains("_02.xml")) {
            percorsoFile = SpringServletContextHelper.getInitParameter("webRootXmlDataFolder") + "0" + File.separator + nomeXml;
            cartella = "0";
        }

        File xmlFile = new File(percorsoFile);
        if (!xmlFile.exists()) {
            Logger.getLogger(ParserHelper.class.getName()).log(Level.SEVERE, ("File not found " + percorsoFile));
            return listaEvent;
        }
        try {

            Document documentoPartita = (Document) builderPartita.build(xmlFile);
            Element rootNode = documentoPartita.getRootElement();
            //Valore attributi dal nodo radice

            if (!percorsoFile.contains("_02.xml")) {
                tempoPartita[1] = Long.parseLong(rootNode.getAttributeValue("starttime1"));
                tempoPartita[2] = Long.parseLong(rootNode.getAttributeValue("starttime2"));
                tempoPartita[3] = Long.parseLong(rootNode.getAttributeValue("starttime3"));
                tempoPartita[4] = Long.parseLong(rootNode.getAttributeValue("starttime4"));
                squadra1 = rootNode.getAttributeValue("team1");
                squadra2 = rootNode.getAttributeValue("team2");
            }

            // lettura giocatori
//            if (rootNode.getChild("PLAYERS") != null) {
//                Element playersNode = rootNode.getChild("PLAYERS");
//                List players = playersNode.getChildren("PLAYER");
//                for (Object playerNode : players) {
//                    Element player = (Element) playerNode;
//                    String nome = player.getAttributeValue("nome");
//                    String numero = player.getAttributeValue("numero");
//                    String ruolo = player.getAttributeValue("ruolo");
//                    String team = player.getAttributeValue("team");
//                    Atleta atleta = new Atleta();
//                    atleta.setKnown_name(nome);
//                    atleta.setNumero(Integer.parseInt(numero));
//                    atleta.setRuolo(ruolo);
//                    atleta.setTeam(Integer.parseInt(team));
//                    if (team.equalsIgnoreCase("1")) {
//                        atleta.setTeamName(squadra1);
//                        if (atleta.getMatchNumber() != null && !game.getFormazione1().containsKey(atleta.getMatchNumber())) {
//                            game.getFormazione1().put(atleta.getMatchNumber(), atleta);
//                        }
//                    } else {
//                        atleta.setTeamName(squadra2);
//                        if (atleta.getMatchNumber() != null && !game.getFormazione2().containsKey(atleta.getMatchNumber())) {
//                            game.getFormazione2().put(atleta.getMatchNumber(), atleta);
//                        }
//                    }
//                }
//            }
            // fine lettura giocatori
            if (!(nomeXml.contains("_04.xml") && isAia)) {//Per gli arbitri non leggo le azioni

                //Recupero gli ecenti dal file xml
                List listaElementi = rootNode.getChildren("EVENT");

                for (int i = 0; i < listaElementi.size(); i++) {

                    Element nodo = (Element) listaElementi.get(i);
                    if (Short.parseShort(nodo.getAttributeValue("rev")) != -1) {

//						Event item = new Event();
                        Azione azione = new Azione();

                        String nomeFileXml = nodo.getAttributeValue("config");

                        //Aggiungo alla lista di panel un nuovo file se non è ancora stato trovato
                        listaButton = listaPanel.get(nomeFileXml);
                        if (listaButton == null) {//Non esiste ancora la lista collegata al file relativo al config, la creo ora
                            listaButton = parserPanel(nomeFileXml, cartella);
                            listaPanel.put(nomeFileXml, listaButton);
                        }

                        if (listaButton != null) {

                            //Inizializzazione Oggetto
                            azione.setButton(listaButton.get(nodo.getAttributeValue("type")));
                            //Imposto la lista dei tag che mi servono
                            //Se esiste il button
                            ArrayList<Tags> TagUsati = new ArrayList<Tags>();
//							if (item.getButton() != null && nodo.getChild("TAGS") != null) {
                            if (azione.getButton() != null && nodo.getChild("TAGS") != null) {
                                List attributiUsati = nodo.getChild("TAGS").getChildren("TAG");
                                List tuttiTag = azione.getButton().getTag();
                                //Ciclo sugli attributi usati usati
                                for (int iterTagUsati = 0; iterTagUsati < attributiUsati.size(); iterTagUsati++) {
                                    String codiceAttributo = ((Element) attributiUsati.get(iterTagUsati)).getAttributeValue("cod");
                                    if (tuttiTag != null) {
                                        for (int iterTag = 0; iterTag < tuttiTag.size(); iterTag++) {
                                            Tags tag = (Tags) tuttiTag.get(iterTag);
                                            if (tag.getCode().equalsIgnoreCase(codiceAttributo)) {
                                                TagUsati.add(tag);
                                            }
                                        }
                                    }
                                }
                            }
                            azione.setmTags(TagUsati);
                            azione.setConfig(nodo.getAttributeValue("config"));
                            azione.setmEnd(Long.parseLong(nodo.getAttributeValue("end")));
                            azione.setHalf(Short.parseShort(nodo.getAttributeValue("half")));
                            azione.setIdEvent(nodo.getAttributeValue("idevent"));

                            azione.setIndexTeam(Short.parseShort(nodo.getAttributeValue("idteam")));
                            azione.setmPlayer(nodo.getAttributeValue("player"));
                            //item.setRev(Short.parseShort(nodo.getAttributeValue("rev")));
                            azione.setRilevante(nodo.getAttributeValue("rilevante"));
                            //item.setRole(nodo.getAttributeValue("role"));
                            //item.setRole_en(nodo.getAttributeValue("role_en"));
                            azione.setGroupsetId(nodo.getAttributeValue("role_id"));
                            azione.setmStart(Long.parseLong(nodo.getAttributeValue("start")));
                            // DAFARE: generare un int dall'idEvent???
                            azione.setId(azione.getmStart() + i);
                            azione.setType(nodo.getAttributeValue("type"));
                            azione.setUser(nodo.getAttributeValue("user"));

                            //tempi di gioco espressi in valori corretti
                            ArrayList InizioFine = calcoloMinutiInizioFine(tempoPartita[azione.getHalf()], azione.getmStart(), azione.getmEnd());
                            azione.setPeriod_start_minute(Integer.parseInt(InizioFine.get(0).toString()));
                            azione.setPeriod_end_minute(Integer.parseInt(InizioFine.get(1).toString()));
                            azione.setDurataAzione((String) InizioFine.get(2));
                            azione.setPeriod_start_second(Integer.parseInt(InizioFine.get(3).toString()));
                            azione.setPeriod_end_second(Integer.parseInt(InizioFine.get(4).toString()));
                            azione.setTempoAzioneVideo((String) InizioFine.get(5));
                            azione.setVideoId(game.getVideoId());
                            azione.setVideoName(game.getVideoName());
                            if (game.getVideoName() != null) {
                                azione.setVideoPathS3(game.getVideoPathS3());
                            }
                            azione.setProvider(game.getProviderId());

                            if (azione.getIndexTeam() == 1) {
                                azione.setmTeam(squadra1);
                            } else {
                                azione.setmTeam(squadra2);
                            }
                            //Inserisco il nuovo evento nella lista finale
                            listaEvent.add(azione);
                        }

                    }
                }

                GeneratoreListeEventi(listaPanel, game.getVideoId(), groupSetId);

                //Estraggo i giocatori se il file è di tipo _02.xml
                if (percorsoFile.contains("_02.xml") == true) {
                    Element contenitorePlayers = rootNode.getChild("PLAYERS");
                    if (contenitorePlayers != null) {
                        List players = contenitorePlayers.getChildren("PLAYER");
                        if (players != null) {
                            for (int i = 0; i < players.size(); i++) {
                                Atleta player = new Atleta();
                                player.setKnown_name(((Element) players.get(i)).getAttributeValue("nome"));
                                player.setNumero(Integer.parseInt(((Element) players.get(i)).getAttributeValue("numero")));
                                player.setRuolo(((Element) players.get(i)).getAttributeValue("ruolo"));
                                player.setTeam(Integer.parseInt(((Element) players.get(i)).getAttributeValue("team")));
                                if (player.getTeam() == 1) {
                                    formazione1.add(player);
                                } else {
                                    formazione2.add(player);
                                }
                            }
                        }
                        //Se è nella prima formazione lo metto nella prima squadra
                    }
                }
            }
        } catch (Exception e) {
            throw (e);
        }

        return listaEvent;
    }

    private void GeneratoreListeEventi(Map<String, Map<String, Button>> listaPanel, Long videoId, Long groupSetId) throws Exception {
        try {
            //Per ogni pannello genero la sua lista di eventi
            //Iterator iteratorePanel = listaPanel.values().iterator();
            Set<String> ChiaviPanel = listaPanel.keySet();
            Iterator iteratoreChiaviPanel = ChiaviPanel.iterator();
            //while (iteratorePanel.hasNext()) {
            while (iteratoreChiaviPanel.hasNext()) {
                //ListIterator itEsterno = listaButton.get(i);
                String chiave = (String) iteratoreChiaviPanel.next();
                Map<String, Button> panelCorrente = listaPanel.get(chiave);
                if (panelCorrente == null) {
                    ErrorHelper.reportError("Il panel " + chiave + " è impostato a null per il match con id" + videoId + " con groupSetId uguale a " + groupSetId);
                    return;
                }
                Iterator iteratoreButton = panelCorrente.values().iterator();
                List listaEventiPanel = new ArrayList();
                //Map<String, Button> a=listaPanel.values().iterator();
                while (iteratoreButton.hasNext()) {
                    //Button item = listaPanel.get(i).get(iter);
                    Button ButtonCorrente = ((Button) iteratoreButton.next());

                    if (ButtonCorrente != null) {//Se esiste la descrizione per questo item allora inserisco la descrizione evento se serve
                        EventDescription nuovaDescrizione = new EventDescription();
                        nuovaDescrizione.setCode(ButtonCorrente.getCod());
                        nuovaDescrizione.setDescription(ButtonCorrente.descTrim("it"));
                        nuovaDescrizione.setDescriptionEn(ButtonCorrente.descTrim("en"));
                        nuovaDescrizione.setDescriptionFr(ButtonCorrente.descTrim("fr"));
                        nuovaDescrizione.setDescriptionEs(ButtonCorrente.descTrim("es"));
                        nuovaDescrizione.setDescriptionRu(ButtonCorrente.descTrim("ru"));
                        //inserisco i tag del panel nell'evento
                        nuovaDescrizione.setTag(ButtonCorrente.getTag());
                        listaEventiPanel.add(nuovaDescrizione);
                    }
                }
                Collections.sort(listaEventiPanel, new DescriptionEventComparatorHelper());
                mapEventDescription.put(chiave, listaEventiPanel);
            }
        } catch (Exception e) {
            throw e;
        }
    }
    //Ritorna una mappa contenente il button e il relativo codice

    private Map<String, Button> parserPanel(String nomeFileXml, String cartella) throws Exception {

        try {

            Map<String, Button> listaButton = new HashMap();
            try {

                boolean foundFile = true;
                String percorsoFile = SpringServletContextHelper.getInitParameter("webRootXmlDataFolder") + System.getProperty("file.separator") + cartella + System.getProperty("file.separator") + "panel" + System.getProperty("file.separator") + nomeFileXml;
                SAXBuilder builderSics = new SAXBuilder();
                File xmlPanel = new File(percorsoFile);
                if (!xmlPanel.exists()) {

                    percorsoFile = percorsoFile.toUpperCase();
                    percorsoFile = percorsoFile.replaceAll(".XML", ".xml");
                    xmlPanel = new File(percorsoFile);
                    if (!xmlPanel.exists()) {

                        percorsoFile = SpringServletContextHelper.getInitParameter("webRootXmlDataFolder") + System.getProperty("file.separator") + "2" + System.getProperty("file.separator") + "panel" + System.getProperty("file.separator") + nomeFileXml;
                        xmlPanel = new File(percorsoFile);
                        if (!xmlPanel.exists()) {
                            percorsoFile = percorsoFile.toUpperCase();
                            percorsoFile = percorsoFile.replaceAll(".XML", ".xml");
                            xmlPanel = new File(percorsoFile);
                            if (!xmlPanel.exists()) {
                                foundFile = false;
                            }
                        }
                    }
                }
                if (!foundFile) {
                    Logger.getLogger(ParserHelper.class.getName()).log(Level.SEVERE, ("File xml not found " + nomeFileXml));
                    return null;
                }

                //Parserizzo il file con le descrizioni degli eventi
                Document documentoSics;
                documentoSics = (Document) builderSics.build(xmlPanel);

                Element rootNodeSics = documentoSics.getRootElement();
                Element eventi = rootNodeSics.getChild("EVENTS");
                //Estraggo gli eventi
                List listaElementi = eventi.getChildren("EVENT");
                for (int i = 0; i < listaElementi.size(); i++) {
                    Button item = new Button();
                    Element nodo = (Element) listaElementi.get(i);
                    //Inizializzazione Oggetto
                    item.setCod(nodo.getAttributeValue("cod"));
                    item.setDesc(nodo.getAttributeValue("desc"));

                    item.setEndesc(nodo.getAttributeValue("endesc"));
//				item.setSh(nodo.getAttributeValue("sh"));
//				item.setVis(nodo.getAttributeValue("vis"));
//				item.setAssociato(nodo.getAttributeValue("associato"));
//				item.setAvversario(Boolean.parseBoolean(nodo.getAttributeValue("avversario")));
                    listaButton.put(item.getCod(), item);
                    //Parserizzo i tag dell'evento per SICS2014
                    Element tags = ((Element) listaElementi.get(i)).getChild("TAGS");
                    if (tags != null) {
                        List listaTag = tags.getChildren("TAG");
                        //Se esistono i TAG li inserisco nel button
                        List listaTagParserizzati = new ArrayList();
                        if (listaTag != null) {
                            for (int iterTag = 0; iterTag < listaTag.size(); iterTag++) {
                                Tags itemTag = new Tags();
                                Element nodoTag = (Element) listaTag.get(iterTag);
                                //itemTag.setAssociato(nodoTag.getAttributeValue("associato"));
                                itemTag.setCode(nodoTag.getAttributeValue("cod"));
                                itemTag.setDesc(nodoTag.getAttributeValue("desc"));
                                itemTag.setEndesc(nodoTag.getAttributeValue("endesc"));
                                listaTagParserizzati.add(itemTag);
                            }
                        }
                        //Aggiungo i tag alla lista nel button
                        item.setTag(listaTagParserizzati);
                    }
                }
            } catch (IOException io) {
                System.out.println(io.getMessage());
            } catch (JDOMException jdomex) {
                System.out.println(jdomex.getMessage());
            }
            return listaButton;

        } catch (Exception e) {
            throw e;
        }
    }

    private ArrayList calcoloMinutiInizioFine(Long InizioTempo, Long InizioEvento, Long FineEvento) throws Exception {
        try {
            Long InizioReale = InizioEvento - InizioTempo;
            Long FineReale = FineEvento - InizioTempo;
            int minutiInizio = (int) ((InizioReale / 1000) / 60);
            int secondiInizio = (int) (InizioReale / 1000) % 60;
            int minutiFine = (int) ((FineReale / 1000) / 60);
            int secondiFine = (int) (FineReale / 1000) % 60;
            int tempoAzione = ((minutiFine * 60) + secondiFine) - ((minutiInizio * 60) + secondiInizio);
            int minutiAzione = (int) (tempoAzione / 60);
            int secondiAzione = (int) (tempoAzione % 60);

//			String inizio;
//			//Aggiungo uno 0 davanti ai secondi se sono a una sola cifra
//			if (secondiInizio < 10) {
//				inizio = minutiInizio + ":0" + secondiInizio;
//			} else {
//				inizio = minutiInizio + ":" + secondiInizio;
//			}
//			if (minutiInizio < 10) {
//				inizio = "0" + inizio;
//			}
//			String fine;
//			if (secondiFine < 10) {
//				fine = minutiFine + ":0" + secondiFine;
//			} else {
//				fine = minutiFine + ":" + secondiFine;
//			}
//			if (minutiFine < 10) {
//				fine = "0" + fine;
//			}
            String durataAzione;
            if (secondiAzione < 10) {
                durataAzione = minutiAzione + ":0" + secondiAzione;
            } else {
                durataAzione = minutiAzione + ":" + secondiAzione;
            }
            if (minutiAzione < 10) {
                durataAzione = "0" + durataAzione;
            }
//			int secStartAzioneInt = ((int) (InizioReale / 1000)) + ((int) (InizioTempo / 1000));
//			String secStartAzione;
//			secStartAzione = Integer.toString(secStartAzioneInt);
//			String secEndAzione;
//			int secEndAzioneInt = ((int) (FineReale / 1000)) + ((int) (InizioTempo / 1000));
//			secEndAzione = Integer.toString(secEndAzioneInt);
            ArrayList ritorno = new ArrayList();
            String tempoAzioneString = Integer.toString(tempoAzione);
            ritorno.add(minutiInizio);
            ritorno.add(minutiFine);
            ritorno.add(durataAzione);
            ritorno.add(secondiInizio);
            ritorno.add(secondiFine);
            ritorno.add(tempoAzioneString);
            return ritorno;
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * @return the listaEventDescription
     */
    public Map<String, List> getListaEventDescription() {
        return mapEventDescription;
    }

    /**
     * @return the listaTotale
     */
    public List getListaFinale() {
        return listaFinale;
    }

    /**
     * @return the formazione1
     */
    public ArrayList<Atleta> getFormazione1() {
        return formazione1;
    }

    /**
     * @return the formazione2
     */
    public ArrayList<Atleta> getFormazione2() {
        return formazione2;
    }
}
