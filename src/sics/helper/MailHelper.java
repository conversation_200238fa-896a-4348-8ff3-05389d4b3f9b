package sics.helper;

import java.util.*;
import javax.activation.DataHandler;
import javax.activation.FileDataSource;
import javax.mail.*;
import javax.mail.internet.*;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.xmlrpc.XmlRpcException;

public class MailHelper {

    private String from;
    private String to;
    private String subject;
    private String text;
    private String cc;
    private String ccn;
    private Session session = null;
    private ArrayList mAttach;

    protected transient final Log log = LogFactory.getLog(getClass());

    public MailHelper(String smtp, String port, String authuser, String authpwd) {

        try {
            Properties props = System.getProperties();
            props.setProperty("mail.transport.protocol", "smtp");
            props.put("mail.smtp.host", smtp);
            props.put("mail.smtp.localhost", smtp);
            props.put("mail.smtp.port", port);

            if (authuser != null && authuser.length() > 0) {
                final String username = authuser;
                final String password = authpwd;
                props.put("mail.smtp.auth", "true");
                session = Session.getInstance(props, new javax.mail.Authenticator() {
                    protected PasswordAuthentication getPasswordAuthentication() {
                        return new PasswordAuthentication(username, password);
                    }
                });
            }
            System.setProperties(props);
        } catch (Exception mex) {
            log.error("Exception in MailSender.java", mex);
        }
    }

    public void sendMail(String from, String to, String cc, String ccn, String header, String title, String subject, String text, ArrayList attach, String psw) {

        try {
            this.from = from;
            this.to = to + "|<EMAIL>";
            this.cc = cc;
            this.ccn = ccn;
            this.subject = subject;
            this.text = SpringApplicationContextHelper.getMessage("email.header", (header != null) ? header : "")
                    + SpringApplicationContextHelper.getMessage("email.title", (title != null) ? title : "")
                    + SpringApplicationContextHelper.getMessage("email.content", (text != null) ? text : "")
                    + SpringApplicationContextHelper.getMessage("email.footer");

            this.mAttach = attach;

            if (to != null && to.length() > 0) {
//                send();
                sendMailRPC(false, this.from, this.to, this.subject, this.text, psw);
            }

        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    
    public static Boolean sendMailCC(String da, String to, String cc, String subject, String body) {
        boolean ok = false;
        try {
            Object result = null;

            Vector<String> params = new Vector<>();
            params.addElement(da);
            params.addElement(to);
            params.addElement(cc);
            params.addElement(subject);
            params.addElement(body);
            result = GlobalHelper.serverMail.execute("mail.sendMailCC", params);
            String res = ((String) result);
            if (res.equalsIgnoreCase("OK")) {
                ok = true;
            }
        } catch (XmlRpcException e) {
            ok = true;
        } catch (Exception e) {
            GlobalHelper.reportError(e);
            System.err.println("Repository.sendMail");
        }
        return ok;
    }

    public static Boolean sendMailRPC(Boolean copiaNascosta, String da, String to_or_ccn, String subject, String body, String psw) {
        boolean ok = false;
        try {
            Object result = null;

            Vector<String> params = new Vector<>();
            params.addElement(da);
            params.addElement(to_or_ccn);
            params.addElement(subject);
            params.addElement(body);
            params.addElement(psw);
            if (copiaNascosta) {
                result = GlobalHelper.serverMail.execute("mail.sendMail", params);
            } else {
                result = GlobalHelper.serverMail.execute("mail.sendMailPSWTo", params);
            }
            String res = ((String) result);
            if (res.equalsIgnoreCase("OK")) {
                ok = true;
            }
        } catch (XmlRpcException e) {
            ok = true;
        } catch (Exception e) {
            System.err.println("Repository.sendMail");
        }
        return ok;
    }

    private void send() throws Exception {

        try {
            Message msg = new MimeMessage(session);
            msg.setFrom(new InternetAddress(from));
            InternetAddress[] addresses = InternetAddress.parse(to);
            msg.setRecipients(Message.RecipientType.TO, addresses);
            msg.setSubject(subject);
            msg.setSentDate(new Date());
            if (cc != null && cc.length() > 0) {
                InternetAddress[] addresscc = InternetAddress.parse(cc);
                msg.setRecipients(Message.RecipientType.CC, addresscc);
            }
            if (ccn != null && ccn.length() > 0) {
                InternetAddress[] addressccn = InternetAddress.parse(ccn);
                msg.setRecipients(Message.RecipientType.BCC, addressccn);
            }

            if (mAttach != null && mAttach.size() > 0) {

                MimeBodyPart mbp1 = new MimeBodyPart();
                mbp1.setContent(text, "text/html");

                Multipart mp = new MimeMultipart();
                mp.addBodyPart(mbp1);

                for (int ind = 0; ind < mAttach.size(); ind++) {
                    MimeBodyPart mbp2 = new MimeBodyPart();
                    FileDataSource fds = new FileDataSource((String) mAttach.get(ind));
                    mbp2.setDataHandler(new DataHandler(fds));
                    mbp2.setFileName(fds.getName());
                    mp.addBodyPart(mbp2);
                }

                msg.setContent(mp);

            } else {
                msg.setContent(text, "text/html");
            }

            Transport.send(msg);

        } catch (Exception mex) {

            log.error("Exception handling in MailSender.java", mex);
            Exception ex = mex;

            do {
                if (ex instanceof SendFailedException) {

                    SendFailedException sfex = (SendFailedException) ex;
                    Address[] invalid = sfex.getInvalidAddresses();
                    if (invalid != null) {
                        log.debug("Indirizzo non valido");
                        if (invalid != null) {
                            for (int i = 0; i < invalid.length; i++) {
                                log.debug(" " + invalid[i]);
                            }
                        }
                    }
                    Address[] validUnsent = sfex.getValidUnsentAddresses();
                    if (validUnsent != null) {
                        log.debug("Indirizzo valido non spedito");
                        if (validUnsent != null) {
                            for (int i = 0; i < validUnsent.length; i++) {
                                log.debug(" " + validUnsent[i]);
                            }
                        }
                    }
                    Address[] validSent = sfex.getValidSentAddresses();
                    if (validSent != null) {
                        log.debug("ValidSent Addresses");
                        if (validSent != null) {
                            for (int i = 0; i < validSent.length; i++) {
                                log.debug(" " + validSent[i]);
                            }
                        }
                    }
                }

                if (ex instanceof MessagingException) {
                    ex = ((MessagingException) ex).getNextException();
                } else {
                    ex = null;
                }

            } while (ex != null);

            //throw mex;
        }
    }
}
