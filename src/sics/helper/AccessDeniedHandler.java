package sics.helper;

import java.io.IOException;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.access.AccessDeniedHandlerImpl;
import org.springframework.stereotype.Service;
import sics.domain.User;
import sics.listener.SessionListener;

/**
 *
 * <AUTHOR>
 */
@Service
public class AccessDeniedHandler extends AccessDeniedHandlerImpl {

    private final String LOGIN_PAGE = "/sicstv/auth/login.htm";
    private final String HOME_PAGE = "/sicstv/user/home.htm";

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException ex) throws IOException, ServletException {
        // solo per pagina login faccio redirect a homepage
        if (StringUtils.equalsIgnoreCase(request.getRequestURI(), LOGIN_PAGE)) {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && !(auth instanceof AnonymousAuthenticationToken)) {
                try {
                    HttpSession session = request.getSession(false); // faccio ritornare null se non c'è
                    if (session != null) {
                        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
                        if (curUser != null) {
                            boolean userLoggedIn = false;
                            List<HttpSession> userSessions = SessionListener.getUserSessions(curUser.getId());
                            if (userSessions != null && !userSessions.isEmpty()) {
                                for (HttpSession tmpSession : userSessions) {
                                    if (tmpSession.equals(session)) {
                                        userLoggedIn = true;
                                        break;
                                    }
                                }
                            }

                            if (userLoggedIn) {
                                response.sendRedirect(HOME_PAGE);
                            }
                        } else {
                            // nel caso di problemi meglio sempre fare un clear del Context e rimandare alla pagina login
                            SecurityContextHolder.clearContext();
                            response.sendRedirect(LOGIN_PAGE);
                        }
                    } else {
                        // nel caso di problemi meglio sempre fare un clear del Context e rimandare alla pagina login
                        SecurityContextHolder.clearContext();
                        response.sendRedirect(LOGIN_PAGE);
                    }
                } catch (Exception exception) {
                    GlobalHelper.reportError(exception);
                }
            }

            SecurityContextHolder.clearContext();
            response.sendRedirect(LOGIN_PAGE);
//            if (ex == null) {
//                super.handle(request, response, ex);
//            } else {
//                // nel caso di problemi meglio sempre fare un clear del Context e rimandare alla pagina login
//                SecurityContextHolder.clearContext();
//                response.sendRedirect(LOGIN_PAGE);
//            }
        }
    }
}
