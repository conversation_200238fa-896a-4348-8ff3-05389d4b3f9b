package sics.helper;

import com.amazonaws.event.ProgressEvent;
import com.amazonaws.event.ProgressEventType;
import com.amazonaws.event.ProgressListener;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.StorageClass;
import com.amazonaws.services.s3.transfer.Upload;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;
import org.apache.commons.io.FilenameUtils;
import sics.domain.Playlist;
import sics.export.ExportInfo;

public class Uploader {

//    private static final String accessKey = "********************";
//    private static final String secretKey = "xueIdIoMTwcPINMCFjSOfsuuBsxtRSr6t0auk8Nw";
    private static final String bucketName = "it.sics.svs.playlist";
    private static final String bucketThumbName = "it.sics.svs.thumb";
    private static final String bucketTmp = "it.sics.tmp";
//
    private static final ProgressEventType UPLOADING = ProgressEventType.TRANSFER_STARTED_EVENT;
//    public static final ProgressEventType PAUSED = ProgressEventType.;
    private static final ProgressEventType COMPLETED = ProgressEventType.TRANSFER_COMPLETED_EVENT;
    private static final ProgressEventType CANCELLED = ProgressEventType.TRANSFER_CANCELED_EVENT;
    private static final ProgressEventType ERROR = ProgressEventType.TRANSFER_FAILED_EVENT;

    private Integer id;
    private String filePath = "";
    private String desc;
    private Long medialength;

    private String stato;
    private Float avanzamento = 0f;
    private String keyName = "";

    private Long bytesTransferred = 0L;
    private Long bytesTotal = 0L;
    private Upload upload;

    public static String uploadThumbnail(String fileThumb, String keyName) {
        try {
            GlobalHelper.initS3Client();
            final PutObjectRequest putObjectRequest = new PutObjectRequest(bucketThumbName, keyName, new File(fileThumb)).withStorageClass(StorageClass.Standard);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType("image/png");
            putObjectRequest.withMetadata(metadata);
            GlobalHelper.s3PlaylistClient.putObject(putObjectRequest);
            return keyName;
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return "";
    }

    public static String uploadTmpThumbnail(String fileThumb, String keyName) {
        try {
            GlobalHelper.initS3Client();
            final PutObjectRequest putObjectRequest = new PutObjectRequest(bucketTmp, keyName, new File(fileThumb)).withStorageClass(StorageClass.Standard);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType("image/octet-stream");
            putObjectRequest.withMetadata(metadata);
            GlobalHelper.s3PlaylistClient.putObject(putObjectRequest);
            return keyName;
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return "";
    }

    public static String upload(final ExportInfo expInfo, File file, final Playlist playlist) {
        try {
            GlobalHelper.initS3Client();

            Date date = Calendar.getInstance().getTime();
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd-HHmmss_");
            format.setTimeZone(TimeZone.getTimeZone("Europe/Berlin"));
            String time = format.format(date);

            String fileName = file.getName();
            String ext = FilenameUtils.getExtension(file.getAbsolutePath());
            Integer indiceExt = fileName.indexOf(ext);
            fileName = fileName.substring(0, indiceExt);
            // rimpiazza qualsiasi carattere non alfanumerico
            fileName = fileName.replaceAll("[^a-zA-Z0-9]+", "");
            // rimette l'estensione al fileName
            fileName += "." + ext;

            String keyName = time + playlist.getUploadedBy() + "_" + fileName;
            playlist.setVideoName(keyName);
            expInfo.setFileSize(Math.round(file.length() / (1024d * 1024d)));

            // For more advanced uploads, you can create a request object 
            // and supply additional request parameters (ex: progress listeners,
            // canned ACLs, etc.)
            final PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, keyName, file).withStorageClass(StorageClass.Standard);
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType("application/octet-stream");
            putObjectRequest.withMetadata(metadata);

            // You can ask the upload for its progress, or you can 
            // add a ProgressListener to your request to receive notifications 
            // when bytes are transferred.
            putObjectRequest.setGeneralProgressListener(new ProgressListener() {
                @Override
                public void progressChanged(ProgressEvent progressEvent) {
                    ProgressEventType type = progressEvent.getEventType();
                    expInfo.increaseUploaded(progressEvent.getBytesTransferred());

                    if (type.equals(UPLOADING)) {
                    } else if (type.equals(COMPLETED)) {

                    }
                }

            });
            GlobalHelper.s3PlaylistClient.putObject(putObjectRequest);
            return keyName;
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return "";
    }

    public Integer getId() {
        return id;
    }

    public String getFilePath() {
        return filePath;
    }

    public String getDesc() {
        return desc;
    }

    public String getStato() {
        return stato;
    }

    public Float getAvanzamento() {
        return avanzamento;
    }

    public Upload getUpload() {
        return upload;
    }

}
