/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.helper;

import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.input.SAXBuilder;
import sics.analysis.FasciaResult;
import sics.analysis.OutputResultAnalysis;
import sics.domain.Atleta;
import sics.domain.Fascia;
import sics.domain.Game;

/**
 *
 * <AUTHOR>
 */
public class AnalysisHelper {

	
	// intervalli in frame
	public final static Integer kInterval5 = 7500;
	public final static Integer kInterval15 = 22500;

	public final static String[] interval5 = {"0-5", "5-10", "10-15", "15-20", "20-25", "25-30", "30-35", "35-40", "40-45", "REC 1°T", "45-50", "50-55", "55-60", "60-65", "65-70", "70-75", "75-80", "80-85", "85-90", "REC 2°T"};
	public final static String[] interval15 = {"0-15", "15-30", "30-45", "REC 1°T", "45-60", "60-75", "75-90", "REC 2°T"};
	public final static String[] intervalHalf = {"1°T", "2°T"};

	public static void getEvents(Game game) {
		Integer frame = 0;
		try {

			String fileName = game.getDataPath() + File.separator + "PRINTDATA" + File.separator + "PlaylistF.xml";
			SAXBuilder builder = new SAXBuilder();

			Document document = builder.build(fileName);
			Element root = document.getRootElement();
			List<Element> listEvents = root.getChildren("EVENTO");

//            ArrayList<Evento> events = mMatchData.getmEvents();
//            String team1 = mMatchData.getmTeam1();
//            String teamSx = "";
			for (Element e : listEvents) {
//                Integer tempo = Integer.valueOf(e.getAttributeValue("tempo"));

				String eventogioco = e.getAttributeValue("eventogioco");
				if (eventogioco != null) {
					switch (eventogioco) {
						case "1KOF":
							game.setKOF1(Integer.valueOf(e.getAttributeValue("frame")));
							break;
						case "2KOF":
							game.setKOF2(Integer.valueOf(e.getAttributeValue("frame")));
							break;
						case "HTIM":
							game.setHTIM(Integer.valueOf(e.getAttributeValue("frame")));
							break;
						case "FTIM":
							game.setFTIM(Integer.valueOf(e.getAttributeValue("frame")));
							break;

					}
				}
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}

	public static void processResearch(String pathDati, HashMap<Integer, ArrayList<Atleta>> mapPlayers, String intervalId, String outputCode, Long rangeId, Game game,
			Fascia fascia, Atleta atleta, OutputResultAnalysis resultObj, boolean aggregate, String nameAggregate) {
		try {
			String path = (atleta.getTeam().equals(game.getHomeTeamId()) ? "1" : "2") + (atleta.getMatchNumber() > 9 ? atleta.getMatchNumber() : "0" + atleta.getMatchNumber()) + ".bin";
			path = pathDati + System.getProperty("file.separator") + "BIN_VM" + System.getProperty("file.separator")
					+ path;
			atleta.setmNewBinFile(new File(path));
			atleta.setGame(game);
			ArrayList<Atleta> listPl = mapPlayers.get(atleta.getTeam());
			if (listPl == null) {
				listPl = new ArrayList<Atleta>();
				mapPlayers.put(atleta.getTeam(), listPl);
			}
			listPl.add(atleta);
			switch (intervalId) {
				case "5":
					Integer startInterval = game.getKOF1();
					Integer endInterval;
					int count = 0;
					while (startInterval < game.getHTIM()) {
						endInterval = startInterval + kInterval5;
						if (endInterval >= game.getHTIM()) {
							endInterval = game.getHTIM();
						}
						FasciaResult result = new FasciaResult(fascia);
						if (!outputCode.equalsIgnoreCase("occ")) {
							atleta.filtriSVS(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
						} else {
							atleta.filtriTest(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
						}
						switch (outputCode) {
							case "dist":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.mLength), interval5[count]);
								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.mLength), interval5[count]);
								}
								break;
							case "occ":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.numOccorrenze), interval5[count]);
								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.numOccorrenze), interval5[count]);
								}
								break;
							case "time":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.time), interval5[count]);
								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.time), interval5[count]);
								}
								break;
						}
						startInterval = endInterval;
						count++;
					}
					startInterval = game.getKOF2();

					while (startInterval < game.getFTIM()) {
						endInterval = startInterval + kInterval5;
						if (endInterval >= game.getFTIM()) {
							endInterval = game.getFTIM();
						}
						FasciaResult result = new FasciaResult(fascia);
						if (!outputCode.equalsIgnoreCase("occ")) {
							atleta.filtriSVS(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
						} else {
							atleta.filtriTest(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
						}
						switch (outputCode) {
							case "dist":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.mLength), interval5[count]);
								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.mLength), interval5[count]);

								}
								break;
							case "occ":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.numOccorrenze), interval5[count]);

								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.numOccorrenze), interval5[count]);
								}
								break;
							case "time":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.time), interval5[count]);

								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.time), interval5[count]);

								}
								break;
						}
						startInterval = endInterval;
						count++;
					}

					break;
				case "15":
					startInterval = game.getKOF1();

					count = 0;
					while (startInterval < game.getHTIM()) {
						endInterval = startInterval + kInterval15;
						if (endInterval >= game.getHTIM()) {
							endInterval = game.getHTIM();
						}
						FasciaResult result = new FasciaResult(fascia);
						if (!outputCode.equalsIgnoreCase("occ")) {
							atleta.filtriSVS(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
						} else {
							atleta.filtriTest(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
						}

						switch (outputCode) {
							case "dist":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.mLength), interval15[count]);
								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.mLength), interval15[count]);
								}
								break;
							case "occ":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.numOccorrenze), interval15[count]);
								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.numOccorrenze), interval15[count]);
								}
								break;
							case "time":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.time), interval15[count]);
								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.time), interval15[count]);
								}
								break;
						}
						startInterval = endInterval;
						count++;
					}
					startInterval = game.getKOF2();

					while (startInterval < game.getFTIM()) {
						endInterval = startInterval + kInterval15;
						if (endInterval >= game.getFTIM()) {
							endInterval = game.getFTIM();
						}
						FasciaResult result = new FasciaResult(fascia);
						if (!outputCode.equalsIgnoreCase("occ")) {
							atleta.filtriSVS(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
						} else {
							atleta.filtriTest(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
						}

						switch (outputCode) {
							case "dist":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.mLength), interval15[count]);
								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.mLength), interval15[count]);
								}
								break;
							case "occ":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.numOccorrenze), interval15[count]);
								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.numOccorrenze), interval15[count]);
								}
								break;
							case "time":
								if (aggregate) {
									resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.time), interval15[count]);
								} else {
									resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.time), interval15[count]);
								}
								break;
						}
						startInterval = endInterval;
						count++;
					}

					break;
				case "half":
					startInterval = game.getKOF1();
					endInterval = game.getHTIM();

					FasciaResult result = new FasciaResult(fascia);
					if (!outputCode.equalsIgnoreCase("occ")) {
						atleta.filtriSVS(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
					} else {
						atleta.filtriTest(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
					}
					switch (outputCode) {
						case "dist":
							if (aggregate) {
								resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.mLength), intervalHalf[0]);
							} else {
								resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.mLength), intervalHalf[0]);
							}
							break;
						case "occ":
							if (aggregate) {
								resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.numOccorrenze), intervalHalf[0]);
							} else {
								resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.numOccorrenze), intervalHalf[0]);
							}
							break;
						case "time":
							if (aggregate) {
								resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.time), intervalHalf[0]);
							} else {
								resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.time), intervalHalf[0]);
							}
							break;
					}

					startInterval = game.getKOF2();
					endInterval = game.getFTIM();
					result = new FasciaResult(fascia);
					if (!outputCode.equalsIgnoreCase("occ")) {
						atleta.filtriSVS(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
					} else {
						atleta.filtriTest(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
					}
					switch (outputCode) {
						case "dist":
							if (aggregate) {
								resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.mLength), intervalHalf[1]);
							} else {
								resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.mLength), intervalHalf[1]);
							}
							break;
						case "occ":
							if (aggregate) {
								resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.numOccorrenze), intervalHalf[1]);
							} else {
								resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.numOccorrenze), intervalHalf[1]);
							}
							break;
						case "time":
							if (aggregate) {
								resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.time), intervalHalf[1]);
							} else {
								resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.time), intervalHalf[1]);
							}
							break;
					}

					break;
				case "match":
					String partita = (game.getHomeTeam() + "-" + game.getAwayTeam()).toUpperCase();
					startInterval = game.getKOF1();
					endInterval = game.getFTIM();

					result = new FasciaResult(fascia);
					if (!outputCode.equalsIgnoreCase("occ")) {
						atleta.filtriSVS(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
					} else {
						atleta.filtriTest(new Long(startInterval), new Long(endInterval), rangeId.intValue() - 1, result);
					}

					switch (outputCode) {
						case "dist":
							if (aggregate) {
								resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.mLength), partita);
							} else {
								resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.mLength), partita);
							}
							break;
						case "occ":
							if (aggregate) {
								resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.numOccorrenze), partita);
							} else {
								resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.numOccorrenze), partita);
							}
							break;
						case "time":
							if (aggregate) {
								resultObj.incrementAggregateValueToMap(nameAggregate, Math.round(result.time), partita);
							} else {
								resultObj.incrementAggregateValueToMap(atleta.getId().toString(), Math.round(result.time), partita);
							}
							break;
					}
					break;
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
}
