package sics.helper;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.HttpMethod;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.BucketAccelerateConfiguration;
import com.amazonaws.services.s3.model.BucketAccelerateStatus;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.SetBucketAccelerateConfigurationRequest;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.time.DateUtils;
import sics.domain.User;

/**
 *
 * <AUTHOR>
 */
public class AWSHelper {

    public static AmazonS3 s3Client = null, s3ClientAccelerated = null;

    public static void initS3Client() {
        try {
            String accessKey = "********************";
            String secretKey = "o6NX+eY//6Os/SgPt/4YxfAMfjLcnEfCBE8fjM+9";
            AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
            ClientConfiguration clientConfig = new ClientConfiguration();
            clientConfig.setProtocol(Protocol.HTTPS);
            // DAFARE test per problema nel download
            clientConfig.setSocketTimeout(240 * 1000);
            s3Client = new AmazonS3Client(credentials, clientConfig);
            Region usWest1 = Region.getRegion(Regions.EU_WEST_1);
            s3Client.setRegion(usWest1);

            // Crea una configurazione di accelerazione del bucket
            BucketAccelerateConfiguration accelerateConfiguration = new BucketAccelerateConfiguration(BucketAccelerateStatus.Enabled);
            SetBucketAccelerateConfigurationRequest request = new SetBucketAccelerateConfigurationRequest(GlobalHelper.kBucketUploadFast, accelerateConfiguration);
            s3ClientAccelerated = new AmazonS3Client(credentials, clientConfig);
            s3ClientAccelerated.setRegion(usWest1);

//            final CountDownLatch countDownLatch = new CountDownLatch(1);
//            // wrap the call in a thread since we make a network call.
//            new Thread(new Runnable() {
//                @Override
//                public void run() {
//                    s3ClientAccelerated.setBucketAccelerateConfiguration(GlobalHelper.kBucketUploadFast,
//                            new BucketAccelerateConfiguration(BucketAccelerateStatus.Enabled));
//                    countDownLatch.countDown();
//                }
//            }).start();
//
//            try {
//                countDownLatch.await();
//            } catch (InterruptedException ex) {
//                GlobalHelper.reportError(ex);
//            }
            s3ClientAccelerated.setS3ClientOptions(S3ClientOptions.builder().setAccelerateModeEnabled(true).build());
//            s3ClientAccelerated.setEndpoint("https://itsicsupload.s3-accelerate.amazonaws.com");
//            s3ClientAccelerated.setEndpoint("itsicsupload.s3-accelerate.amazonaws.com");
            s3ClientAccelerated.setBucketAccelerateConfiguration(request);
        } catch (IllegalArgumentException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static String getPresignedUrlForVideoUpload(User user, String fileName, String contentType, String randomUUID) {
        if (s3Client == null) {
            initS3Client();
        }

        URL presignedUrl = null;
        if (s3Client != null && user != null) {
            //s3Client.setBucketAccelerateConfiguration(fileName, accelerateConfiguration);
            String bucket = GlobalHelper.kBucketUpload + "/" + user.getGroupsetId() + "/" + user.getId() + "/" + randomUUID;
            // sistemo il nome del file togliendo caratteri speciali
            String baseName = FilenameUtils.getBaseName(fileName);
            String extension = FilenameUtils.getExtension(fileName);
            baseName = GlobalHelper.removeUnwantedCharacter(baseName);
            String key = baseName + "." + extension;
            Date expirationDate = DateUtils.addDays(new Date(), 1);

            GeneratePresignedUrlRequest presignedUrlRequest = new GeneratePresignedUrlRequest("/" + bucket, key);
            presignedUrlRequest.setMethod(HttpMethod.PUT); // Default.
            presignedUrlRequest.setContentType(contentType);
            presignedUrlRequest.setExpiration(expirationDate);

            presignedUrl = s3Client.generatePresignedUrl(presignedUrlRequest);
        }

        return (presignedUrl != null ? presignedUrl.toString() : "");
    }

    public static String getPresignedUrlAcceleratedForVideoUpload(User user, String fileName, String contentType, String randomUUID) {
        if (s3ClientAccelerated == null) {
            initS3Client();
        }

        URL presignedUrl = null;
        if (s3ClientAccelerated != null && user != null) {
            //s3Client.setBucketAccelerateConfiguration(fileName, accelerateConfiguration);
            String bucket = GlobalHelper.kBucketUploadFast + "/" + user.getGroupsetId() + "/" + user.getId() + "/" + randomUUID;
            // sistemo il nome del file togliendo caratteri speciali
            String baseName = FilenameUtils.getBaseName(fileName);
            String extension = FilenameUtils.getExtension(fileName);
            baseName = GlobalHelper.removeUnwantedCharacter(baseName);
            String key = baseName + "." + extension;
            Date expirationDate = DateUtils.addDays(new Date(), 1);

            GeneratePresignedUrlRequest presignedUrlRequest = new GeneratePresignedUrlRequest("/" + bucket, key);
            presignedUrlRequest.setMethod(HttpMethod.PUT); // Default.
            presignedUrlRequest.setContentType(contentType);
            presignedUrlRequest.setExpiration(expirationDate);

//            presignedUrl = s3ClientAccelerated.generatePresignedUrl(presignedUrlRequest);
            presignedUrl = s3ClientAccelerated.generatePresignedUrl(GlobalHelper.kBucketUploadFast, user.getGroupsetId() + "/" + user.getId() + "/" + randomUUID + "/" + key, expirationDate, HttpMethod.PUT);
//            URL test = s3ClientAccelerated.generatePresignedUrl(GlobalHelper.kBucketUploadFast, key, expirationDate, HttpMethod.PUT);
//            System.out.println("ok");
        }

        return (presignedUrl != null ? presignedUrl.toString() : "");
    }

    public static List<String> getUploadFileLinks(User user, List<String> fileNames, String randomUUID) {
        if (s3Client == null) {
            initS3Client();
        }

        List<String> links = new ArrayList<>();
        if (s3Client != null && user != null) {
            for (String fileName : fileNames) {
                String bucket = GlobalHelper.kBucketUpload + "/" + user.getGroupsetId() + "/" + user.getId() + "/" + randomUUID;
                // sistemo il nome del file togliendo caratteri speciali
                String baseName = FilenameUtils.getBaseName(fileName);
                String extension = FilenameUtils.getExtension(fileName);
                baseName = GlobalHelper.removeUnwantedCharacter(baseName);
                String key = baseName + "." + extension;
                Date expirationDate = DateUtils.addDays(new Date(), 6);

                GeneratePresignedUrlRequest presignedUrlRequest = new GeneratePresignedUrlRequest("/" + bucket, key);
                presignedUrlRequest.setMethod(HttpMethod.GET);
                presignedUrlRequest.setExpiration(expirationDate);
                links.add(s3Client.generatePresignedUrl(presignedUrlRequest).toExternalForm());
            }
        }

        return links;
    }

    public static List<String> getUploadAcceleratedFileLinks(User user, List<String> fileNames, String randomUUID) {
        if (s3ClientAccelerated == null) {
            initS3Client();
        }

        List<String> links = new ArrayList<>();
        if (s3ClientAccelerated != null && user != null) {
            for (String fileName : fileNames) {
                String bucket = GlobalHelper.kBucketUploadFast + "/" + user.getGroupsetId() + "/" + user.getId() + "/" + randomUUID;
                // sistemo il nome del file togliendo caratteri speciali
                String baseName = FilenameUtils.getBaseName(fileName);
                String extension = FilenameUtils.getExtension(fileName);
                baseName = GlobalHelper.removeUnwantedCharacter(baseName);
                String key = baseName + "." + extension;
                Date expirationDate = DateUtils.addDays(new Date(), 6);

                GeneratePresignedUrlRequest presignedUrlRequest = new GeneratePresignedUrlRequest("/" + bucket, key);
                presignedUrlRequest.setMethod(HttpMethod.GET);
                presignedUrlRequest.setExpiration(expirationDate);
                //links.add(s3ClientAccelerated.generatePresignedUrl(presignedUrlRequest).toExternalForm());
                links.add(s3ClientAccelerated.generatePresignedUrl(GlobalHelper.kBucketUploadFast, user.getGroupsetId() + "/" + user.getId() + "/" + randomUUID + "/" + key, expirationDate, HttpMethod.GET).toExternalForm());
            }
        }

        return links;
    }
}
