package sics.helper;

import com.google.api.client.googleapis.json.GoogleJsonResponseException;
import com.google.api.client.http.HttpRequestInitializer;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.drive.Drive;
import com.google.api.services.drive.Drive.Files.Export;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import static org.apache.poi.ss.usermodel.CellType.NUMERIC;
import static org.apache.poi.ss.usermodel.CellType.STRING;
import org.apache.poi.ss.usermodel.Color;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFColor;
import sics.domain.AgentPayment;
import sics.domain.ExcelCell;

/**
 *
 * <AUTHOR>
 */
public class AgentExcelReader {

//    private static final String CREDENTIALS_FILE_PATH = "/sics/resources/credentials-account-servizio.json";
    private static final String CREDENTIALS_FILE_PATH = "/sics/resources/credentials-account-servizio-michele.json";
    // IL FILE_ID E' PRESENTE NEL LINK QUANDO SI VA IN VISUALIZZAZIONE
    // https://docs.google.com/spreadsheets/d/1v9BmA5hX5nLd0qHtK5U9-_p53WhXuNvh/edit#gid=**********
//    private static final String EXCEL_FILE_ID = "1o_iC2VyDLrwTbYX-xSDak8zElav3J3HEoDjJZeQt2XI";
    private static final String EXCEL_FILE_ID = "1Trv8Ij7RdmZIWyNxWtM29vaup-BKhl7_MsrU13_BeaE";
    private static final String MAIN_SHEET_NAME = "General";

    private static final String EXCEL_FILE_BASE_PATH = System.getProperty("java.io.tmpdir") + "/excel";
    private static final String EXCEL_FILE_PATH = EXCEL_FILE_BASE_PATH + "/temporary.xlsx";

    private static final Integer MAX_COLUMNS = 14;
    private static final List<Integer> COLUMN_INDEX_WITH_DECIMALS = Arrays.asList(8, 10);

    public AgentPayment read(String agentName) {
        AgentPayment tableContent = null;
        try {
            boolean downloaded = downloadExcelFromDrive();

            if (downloaded) {
                try (FileInputStream fis = new FileInputStream(EXCEL_FILE_PATH); Workbook workbook = WorkbookFactory.create(fis)) {

                    // Cerca lo sheet target
                    Sheet targetSheet = null;
                    for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                        Sheet sheet = workbook.getSheetAt(i);
                        if (sheet.getSheetName().equalsIgnoreCase(MAIN_SHEET_NAME)) {
                            targetSheet = sheet;
                            break;
                        }
                    }

                    if (targetSheet != null) {
                        // Ottiene la lista degli header
                        List<String> headerList = getHeaderList(targetSheet);
                        if (headerList.size() > MAX_COLUMNS) {
                            headerList = headerList.subList(0, MAX_COLUMNS);
                        }

                        // Ottiene la mappa con il contenuto delle celle
                        Map<Integer, List<ExcelCell>> cellContentMap = getCellContentMap(targetSheet, agentName, MAX_COLUMNS);

                        for (Integer rowNumber : cellContentMap.keySet()) {
                            List<ExcelCell> content = cellContentMap.get(rowNumber);
                            int contentSize = content.size();
                            if (contentSize < headerList.size()) {
                                for (int i = 0; i < headerList.size() - contentSize; i++) {
                                    content.add(new ExcelCell());
                                }
                            }
                        }

                        tableContent = new AgentPayment();
                        tableContent.setHeaders(headerList);
                        tableContent.setRows(cellContentMap);
//                        // Stampa gli header
//                        System.out.println("Header:");
//                        System.out.println(headerList);
//
//                        // Stampa il contenuto delle celle
//                        System.out.println("Contenuto delle celle:");
//                        for (Map.Entry<Integer, List<String>> entry : cellContentMap.entrySet()) {
//                            System.out.println("Riga " + entry.getKey() + ": " + entry.getValue());
//                        }
                    } else {
                        System.out.println("Sheet non trovato: " + MAIN_SHEET_NAME);
                    }
                }
            } else {
                System.out.println("Excel non scaricato. Impossibile continuare");
            }

        } catch (IOException e) {
            GlobalHelper.reportError(e);
        }

        return tableContent;
    }

    private boolean downloadExcelFromDrive() {
        boolean completed = false;

        try {
            GoogleCredentials credentials = ServiceAccountCredentials.fromStream(getClass().getResourceAsStream(CREDENTIALS_FILE_PATH))
                    .createScoped(Arrays.asList("https://www.googleapis.com/auth/drive.readonly"));

            HttpRequestInitializer requestInitializer = new HttpCredentialsAdapter(credentials);

            // Build a new authorized API client service.
            Drive service = new Drive.Builder(new NetHttpTransport(),
                    GsonFactory.getDefaultInstance(),
                    requestInitializer)
                    .setApplicationName("Drive Downloader")
                    .build();

            try {
                OutputStream outputStream = new ByteArrayOutputStream();

                // specifico file excel
                Export exportRequest = service.files().export(EXCEL_FILE_ID, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                exportRequest.executeMediaAndDownloadTo(outputStream);

                File file = new File(EXCEL_FILE_BASE_PATH);
                file.mkdirs();  // se non esistono le cartelle le creo
                try (FileOutputStream fos = new FileOutputStream(new File(EXCEL_FILE_PATH))) {
                    // Leggi i dati dall'outputStream e scrivili nel file
                    ByteArrayOutputStream byteOutputStream = (ByteArrayOutputStream) outputStream;
                    byteOutputStream.writeTo(fos);

                    completed = true;
                }
            } catch (GoogleJsonResponseException e) {
                GlobalHelper.reportError(e);
            }
        } catch (IOException ex) {
            GlobalHelper.reportError(ex);
        }

        return completed;
    }

    private static List<String> getHeaderList(Sheet sheet) {
        Row headerRow = sheet.getRow(0);
        List<String> headerList = new ArrayList<>();

        for (Cell cell : headerRow) {
            headerList.add(cell.getStringCellValue());
        }

        return headerList;
    }

    private static Map<Integer, List<ExcelCell>> getCellContentMap(Sheet sheet, String targetAgent, int maxSize) {
        Map<Integer, List<ExcelCell>> cellContentMap = new LinkedHashMap<>();

        int rowCount = sheet.getPhysicalNumberOfRows();

        for (int i = 1; i < rowCount; i++) { // Inizia da 1 per evitare l'header
            Row row = sheet.getRow(i);
            if (row.getCell(0) != null
                    && row.getCell(0).getCellType() == STRING
                    && (StringUtils.equalsIgnoreCase(row.getCell(0).getStringCellValue(), targetAgent)
                    || StringUtils.equalsIgnoreCase(targetAgent, "All"))) {

                List<ExcelCell> cellValues = new ArrayList<>();

                int cellCount = row.getPhysicalNumberOfCells();
                boolean hasNonEmptyCell = false;

                for (int j = 0; j < cellCount; j++) {
                    if (cellValues.size() < maxSize) {
                        Cell cell = row.getCell(j);
                        String cellValue = "";

                        ExcelCell excelCell = new ExcelCell();

                        if (cell != null) {
                            switch (cell.getCellType()) {
                                case STRING:
                                    cellValue = cell.getStringCellValue();
                                    break;
                                case NUMERIC:
                                    Date dateValue = cell.getDateCellValue();
                                    Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("Europe/Paris"));
                                    cal.setTime(dateValue);
                                    if (cal.get(Calendar.YEAR) >= 2023) {
                                        // valido
                                        SimpleDateFormat df = new SimpleDateFormat("dd/MM/yyyy");
                                        cellValue = df.format(dateValue);
                                    } else {
                                        if (COLUMN_INDEX_WITH_DECIMALS.contains(j)) {
                                            // prendo valore con i decimali, ma se c'è "300.0" prendo solo "300"
                                            cellValue = String.valueOf(cell.getNumericCellValue()).replace(".0", "");
                                        } else {
                                            cellValue = String.valueOf(Math.round(cell.getNumericCellValue()));
                                        }
                                    }
                                    break;
                            }
                            CellStyle cellStyle = cell.getCellStyle();
                            Color color = cellStyle.getFillForegroundColorColor();

                            // colore
                            if (color instanceof XSSFColor) { // XSSFColor è utilizzato nei file xlsx
                                XSSFColor xssfColor = (XSSFColor) color;
                                excelCell.setColor(xssfColor.getARGBHex().substring(2));
                            }
                            // allineamento orizzontale
                            HorizontalAlignment alignment = cellStyle.getAlignment();
                            if (alignment != null) {
                                if (alignment == HorizontalAlignment.CENTER) {
                                    excelCell.setAlign("center");
                                }
                            }

                            if (!cellValue.isEmpty()) {
                                hasNonEmptyCell = true;
                            }
                        }

                        excelCell.setContent(cellValue);
                        cellValues.add(excelCell);
                    } else {
                        break;
                    }
                }

                if (hasNonEmptyCell) {
                    cellContentMap.put(i, cellValues);
                }
            }
        }

        return cellContentMap;
    }
}
