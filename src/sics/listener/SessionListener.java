package sics.listener;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Timer;
import java.util.TimerTask;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletContext;
import sics.helper.GlobalHelper;
import javax.servlet.http.HttpSession;
import javax.servlet.http.HttpSessionListener;
import javax.servlet.http.HttpSessionEvent;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.time.DateUtils;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeansException;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;
import sics.dao.service.ManagerService;
import sics.domain.User;
import sics.helper.MongoHelper;

public class SessionListener implements HttpSessionListener {

    private final static HashMap<Long, ArrayList<HttpSession>> sessions = new HashMap<>();
    protected transient final Log log = LogFactory.getLog(getClass());
    protected static Timer timer = new Timer("Cache Reloader");

    static {
        TimerTask task = new TimerTask() {
            @Override
            public void run() {
                try {
                    checkSessions();
                } catch (Exception ex) {
                    GlobalHelper.reportError(ex);
                }
            }
        };

        long period = 30000L;
        timer.scheduleAtFixedRate(task, 0, period); // ogni 30 secondi controllo se le sessioni sono valide e non scadute
    }

    public static void killTimer() {
        if (timer != null) {
            timer.cancel();
            timer.purge();
            timer = null;
        }
    }

    @Override
    public synchronized void sessionCreated(HttpSessionEvent evt) {
    }

    public static void addSession(User curUser, HttpSession session) {
        synchronized (sessions) {
            if (curUser != null) {
                if (sessions.containsKey(curUser.getId())) {
                    if (!sessions.get(curUser.getId()).contains(session)) {
                        sessions.get(curUser.getId()).add(session);
                    }
                } else {
                    ArrayList<HttpSession> sessionArray = new ArrayList<>();
                    sessionArray.add(session);
                    sessions.put(curUser.getId(), sessionArray);
                }
            }
        }
    }

    @Override
    public void sessionDestroyed(HttpSessionEvent evt) {
        synchronized (sessions) {
            HttpSession session = evt.getSession();

            if (session != null) {
                try {
                    User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
                    if (curUser != null) {
                        destroySession(session, curUser.getId());
                        System.out.println("SESSION_DESTROYED --> TOTALI:" + sessions.size() + " | UTENTE: " + (sessions.get(curUser.getId()) != null ? sessions.get(curUser.getId()).size() : 0));
                    }
                } catch (Exception ex) {
                    log.error("logging out: " + ex.getMessage());
                    // probabilmente già invalidata (timeout della sessione)
                    destroySession(session, null);
                } finally {
                    // questa riga killa la sessione attuale quindi se arrivo dal tasto "Forza Login"
                    // killa la mia sessione e non va bene. Bisognerebbe filtrare questa funzione e fare
                    // il clearContext() solo quando non arrivo dal forza login
                    // SecurityContextHolder.clearContext(); // Rimuove l'utente autenticato
                }
            }
        }
    }

    public static boolean destroyAllSessions() {
        boolean error = false;
        synchronized (sessions) {
            try {
                HashMap<Long, ArrayList<HttpSession>> tmpSessions = new HashMap<>();
                for (Long userId : sessions.keySet()) {
                    ArrayList<HttpSession> clonedList = new ArrayList<>(sessions.get(userId));
                    tmpSessions.put(userId, clonedList);
                }

                for (Long userId : tmpSessions.keySet()) {
                    for (HttpSession session : tmpSessions.get(userId)) {
                        destroySession(session, userId);
                    }
                }
            } catch (Exception ex) {
                GlobalHelper.reportError(ex);
                error = true;
            }
        }

        return error;
    }

    public static boolean destroySession(HttpSession session, Long userId) {
        return destroySession(session, userId, false);
    }

    // lightDestroy imposta la scadenza della sessione a 1 secondo così da essere sicuro che viene
    // invalidata senza interrompere qualche chiamata che potrebbe causare errore 403 perenne
    public static boolean destroySession(HttpSession session, Long userId, Boolean lightDestroy) {
        synchronized (sessions) {
            boolean done = false;
            try {
                try {
                    MongoHelper.saveSession(session);
                } catch (Exception ex) {
                    GlobalHelper.reportError(ex);
                }
                if (BooleanUtils.isNotTrue(lightDestroy)) {
                    session.invalidate();
                } else {
                    session.setMaxInactiveInterval(1);
                }
                done = true;
            } catch (IllegalStateException ex) {
                // suppressed
                GlobalHelper.sendExceptionMail(null, ex);
                Logger.getLogger(SessionListener.class.getName()).log(Level.SEVERE, "/destroySession. can't invalidate session. Already invalidated ?");
                done = false;
            } catch (Exception ex) {
                GlobalHelper.sendExceptionMail(null, ex);
                Logger.getLogger(SessionListener.class.getName()).log(Level.SEVERE, "/destroySession. can't invalidate session. Generic error");
                done = false;
            } finally {
                if (userId != null) {
                    if (sessions.get(userId) != null && sessions.get(userId).contains(session)) {
                        sessions.get(userId).remove(session);
                        if (sessions.get(userId).isEmpty()) {
                            sessions.remove(userId);
                        }

                        User curUser = null;
//                        if (session.getAttribute(GlobalHelper.kBeanUtente) != null) {
//                            curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
//                        }
                        // carico tramite manager per evitare exception
                        try {
                            ServletContext servletContext = session.getServletContext();
                            WebApplicationContext ctx = WebApplicationContextUtils.getWebApplicationContext(servletContext);
                            ManagerService manager = (ManagerService) ctx.getBean(GlobalHelper.kBeanAppManager);
                            curUser = manager.getUser(userId);
                        } catch (BeansException | SQLException ex) {
                            GlobalHelper.sendExceptionMail(null, ex);
                            Logger.getLogger(SessionListener.class.getName()).log(Level.SEVERE, "/destroySession. can't get userById.");
                        } finally {
                            // il log deve arrivarmi SEMPRE
                            GlobalHelper.writeLogData(session, GlobalHelper.kActionSessionDestroyed, (curUser != null ? curUser.getUsername() : null), (curUser != null ? curUser.getPassword() : null), userId);
                        }
                        done = true;
                    }
                }

                // sistemo tutte quelle rotte...
                checkSessions();
            }

            return done;
        }
    }

    public static void checkSessions() {
        synchronized (sessions) {
            if (!sessions.isEmpty()) {
                // creo clone -> non funziona se fai new HashMap<>(sessions);
                // probabilmente perchè contiene una lista e quindi si porta i riferimenti agli oggetti
                HashMap<Long, ArrayList<HttpSession>> tmpSessions = new HashMap<>();
                for (Long userId : sessions.keySet()) {
                    ArrayList<HttpSession> clonedList = new ArrayList<>(sessions.get(userId));
                    tmpSessions.put(userId, clonedList);
                }

                for (Long userId : tmpSessions.keySet()) {
                    for (HttpSession session : tmpSessions.get(userId)) {
                        try {
                            // controllo se la sessione è scaduta
                            if (session.getAttribute(GlobalHelper.kLastRequestDate) != null) { // per sicurezza
                                Date lastRequest = (Date) session.getAttribute(GlobalHelper.kLastRequestDate);
                                int secondsInactivity = session.getMaxInactiveInterval();
                                if (DateUtils.addSeconds(lastRequest, secondsInactivity).before(new Date())) {
                                    sessions.get(userId).remove(session);
                                    if (sessions.get(userId).isEmpty()) {
                                        sessions.remove(userId);
                                    }
                                    GlobalHelper.writeLogData(session, GlobalHelper.kActionSessionInactivity, null, null, userId);
                                }
                            }

                            User user = (User) session.getAttribute(GlobalHelper.kBeanUtente); // questo manda exception se la sessione è invalidata
                            if (user == null) {
                                sessions.get(userId).remove(session);
                                if (sessions.get(userId).isEmpty()) {
                                    sessions.remove(userId);
                                }
                                GlobalHelper.writeLogData(session, GlobalHelper.kActionSessionDestroyed, null, null, userId);
                            }
                        } catch (Exception ex) {
                            // sessione già invalidata
                            sessions.get(userId).remove(session);
                            if (sessions.get(userId).isEmpty()) {
                                sessions.remove(userId);
                            }
                            GlobalHelper.writeLogData(session, GlobalHelper.kActionSessionDestroyed, null, null, userId);
                        }
                    }
                }
            }
        }
    }

    public static HashMap<Long, ArrayList<HttpSession>> getSessions() {
        synchronized (sessions) {
            return sessions;
        }
    }

    public static ArrayList<HttpSession> getUserSessions(Long userId) {
        synchronized (sessions) {
            return sessions.get(userId);
        }
    }

    public synchronized static long getUserMinSessionCreationTime(Long userId) {
        synchronized (sessions) {
            long min = Long.MAX_VALUE;
            for (HttpSession ses : sessions.get(userId)) {
                min = Math.min(min, ses.getCreationTime());
            }
            return min;
        }
    }

    public static void cleanUp() {
        synchronized (sessions) {
            sessions.clear();
        }
    }

}
