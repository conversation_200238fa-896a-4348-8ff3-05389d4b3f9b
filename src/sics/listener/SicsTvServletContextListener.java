package sics.listener;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import sics.controller.UserController;
import sics.helper.MongoHelper;

/**
 *
 * <AUTHOR>
 */
public class SicsTvServletContextListener implements ServletContextListener {

    private static ServletContext context;

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        context = sce.getServletContext();
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        UserController.killTimer();
        SessionListener.killTimer();
        MongoHelper.closeConnection();
        context = null;
    }

    public static ServletContext getContext() {
        return context;
    }

}
