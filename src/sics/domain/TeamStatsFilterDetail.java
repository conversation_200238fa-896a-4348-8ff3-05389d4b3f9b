package sics.domain;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class TeamStatsFilterDetail implements Serializable {

    public TeamStatsFilterDetail() {
    }

    public TeamStatsFilterDetail(Long statsTypeFilterId, Long statsTypeId, String columnName, Integer order) {
        this.statsTypeFilterId = statsTypeFilterId;
        this.statsTypeId = statsTypeId;
        this.columnName = columnName;
        this.order = order;
    }

    private Long id;
    private Long statsTypeFilterId;
    private Long statsTypeId;
    private String columnName;
    private Integer order;
    private Boolean sort; // true = asc; false = desc; null = no sorting

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStatsTypeFilterId() {
        return statsTypeFilterId;
    }

    public void setStatsTypeFilterId(Long statsTypeFilterId) {
        this.statsTypeFilterId = statsTypeFilterId;
    }

    public Long getStatsTypeId() {
        return statsTypeId;
    }

    public void setStatsTypeId(Long statsTypeId) {
        this.statsTypeId = statsTypeId;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Boolean getSort() {
        return sort;
    }

    public void setSort(Boolean sort) {
        this.sort = sort;
    }
}
