package sics.domain;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class CalendarRange implements Serializable {
    
    private Long minMatchDay;
    private Long maxMatchDay;

    public CalendarRange() {
    }
    
    /**
     * @return the minDay
     */
    public Long getMinMatchDay() {
        return minMatchDay;
    }

    /**
     * @param minDay the minDay to set
     */
    public void setMinMatchDay(Long minDay) {
        this.minMatchDay = minDay;
    }

    /**
     * @return the maxDay
     */
    public Long getMaxMatchDay() {
        return maxMatchDay;
    }

    /**
     * @param maxDay the maxDay to set
     */
    public void setMaxMatchDay(Long maxDay) {
        this.maxMatchDay = maxDay;
    }
}
