package sics.domain;

import com.google.gson.Gson;
import java.io.*;
import java.util.List;

public class Team implements Serializable {

    private Long id, mainTeamId;
    private String name;
    private String logo;
    private Long countryId;
    private String color;
    private String textColor;
    private Boolean nationalTeam;
    private Boolean visible;
    private Long groupsetId;
    private int vittorie = 0;
    private int vittorieInCasa = 0;
    private int pareggi = 0;
    private int pareggiInCasa = 0;
    private int sconfitte = 0;
    private int sconfitteInCasa = 0;

    private int golFatti = 0;
    private int golSubiti = 0;
    
    // campi usati per la ricerca presente nell'header
    private String seasonName;
    private List<String> seasonList;
    private String countryName;
    
    // extra fields
    private Long playerId;
    private Long playerPositionId;
    private String playerRoleDescription;
    private String playerDetailRoleDescription;
    private Long playerFootId;
    private String playerFoot;
    private Long groupId;       // id girone
    private String groupName;   // nome girone
    private Integer groupOrder; // ordine girone

    public Team() {
    }

    /**
     * @return the id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(Long id) {
        this.id = id;
    }

    public Long getMainTeamId() {
        return mainTeamId;
    }

    public void setMainTeamId(Long mainTeamId) {
        this.mainTeamId = mainTeamId;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }
    
    /**
     * @return the countryId
     */
    public Long getCountryId() {
        return countryId;
    }

    /**
     * @param countryId the countryId to set
     */
    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public Boolean getNationalTeam() {
        return nationalTeam;
    }

    public void setNationalTeam(Boolean nationalTeam) {
        this.nationalTeam = nationalTeam;
    }

    public Boolean getVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public Long getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    public int getVittorie() {
        return vittorie;
    }

    public void setVittorie(int vittorie) {
        this.vittorie = vittorie;
    }

    public int getVittorieInCasa() {
        return vittorieInCasa;
    }

    public void setVittorieInCasa(int vittorieInCasa) {
        this.vittorieInCasa = vittorieInCasa;
    }

    public int getPareggi() {
        return pareggi;
    }

    public void setPareggi(int pareggi) {
        this.pareggi = pareggi;
    }

    public int getPareggiInCasa() {
        return pareggiInCasa;
    }

    public void setPareggiInCasa(int pareggiInCasa) {
        this.pareggiInCasa = pareggiInCasa;
    }
    
    public int getSconfitte() {
        return sconfitte;
    }

    public void setSconfitte(int sconfitte) {
        this.sconfitte = sconfitte;
    }

    public int getSconfitteInCasa() {
        return sconfitteInCasa;
    }

    public void setSconfitteInCasa(int sconfitteInCasa) {
        this.sconfitteInCasa = sconfitteInCasa;
    }

    public int getGolFatti() {
        return golFatti;
    }

    public void setGolFatti(int golFatti) {
        this.golFatti = golFatti;
    }

    public int getGolSubiti() {
        return golSubiti;
    }

    public void setGolSubiti(int golSubiti) {
        this.golSubiti = golSubiti;
    }

    public String getDiffGoal() {
        int diffReti = golFatti - golSubiti;
        return (diffReti > 0 ? "+" : "") + diffReti;
    }

    public String getSeasonName() {
        return seasonName;
    }

    public void setSeasonName(String seasonName) {
        this.seasonName = seasonName;
    }
    
    public List<String> getSeasonList() {
        return seasonList;
    }

    public void setSeasonList(List<String> seasonList) {
        this.seasonList = seasonList;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }

    public Long getPlayerPositionId() {
        return playerPositionId;
    }

    public void setPlayerPositionId(Long playerPositionId) {
        this.playerPositionId = playerPositionId;
    }

    public String getPlayerRoleDescription() {
        return playerRoleDescription;
    }

    public void setPlayerRoleDescription(String playerRoleDescription) {
        this.playerRoleDescription = playerRoleDescription;
    }

    public String getPlayerDetailRoleDescription() {
        return playerDetailRoleDescription;
    }

    public void setPlayerDetailRoleDescription(String playerDetailRoleDescription) {
        this.playerDetailRoleDescription = playerDetailRoleDescription;
    }

    public Long getPlayerFootId() {
        return playerFootId;
    }

    public void setPlayerFootId(Long playerFootId) {
        this.playerFootId = playerFootId;
    }

    public String getPlayerFoot() {
        return playerFoot;
    }

    public void setPlayerFoot(String playerFoot) {
        this.playerFoot = playerFoot;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getGroupOrder() {
        return groupOrder;
    }

    public void setGroupOrder(Integer groupOrder) {
        this.groupOrder = groupOrder;
    }
    
    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
