package sics.domain;

/**
 *
 * <AUTHOR>
 */
public class FixturePlaylistSize {

    protected Long fixtureSize = 0l;
    protected Long numFixture = 0l;
    protected Long playlistSize = 0l;
    protected Long numPlaylist = 0l;

    /**
     * @return the fixtureSize
     */
    public Long getFixtureSize() {
        return fixtureSize;
    }

    /**
     * @param fixtureSize the fixtureSize to set
     */
    public void setFixtureSize(Long fixtureSize) {
        this.fixtureSize = fixtureSize;
    }

    /**
     * @return the numFixture
     */
    public Long getNumFixture() {
        return numFixture;
    }

    /**
     * @param numFixture the numFixture to set
     */
    public void setNumFixture(Long numFixture) {
        this.numFixture = numFixture;
    }

    /**
     * @return the playlistSize
     */
    public Long getPlaylistSize() {
        return playlistSize;
    }

    /**
     * @param playlistSize the playlistSize to set
     */
    public void setPlaylistSize(Long playlistSize) {
        this.playlistSize = playlistSize;
    }

    /**
     * @return the numPlaylist
     */
    public Long getNumPlaylist() {
        return numPlaylist;
    }

    /**
     * @param numPlaylist the numPlaylist to set
     */
    public void setNumPlaylist(Long numPlaylist) {
        this.numPlaylist = numPlaylist;
    }
}
