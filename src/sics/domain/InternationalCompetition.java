package sics.domain;

import java.io.*;

public class InternationalCompetition implements Serializable {

    private Long id;
    private String name;
    private String logo;
    private Boolean internationalCompetition;
    private boolean visible;

    public InternationalCompetition() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }

    public Boolean getInternationalCompetition() {
        return internationalCompetition;
    }

    public void setInternationalCompetition(Boolean value) {
        this.internationalCompetition = value;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    
    
}
