package sics.domain;

/**
 *
 * <AUTHOR>
 */
public class Settings {
    
    private Long id;
    private Long userId;
    private Integer application;
    private Long eventAdditionalStart;
    private Long eventAdditionalEnd;
    private String tvLanguage;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getApplication() {
        return application;
    }

    public void setApplication(Integer application) {
        this.application = application;
    }

    public Long getEventAdditionalStart() {
        return eventAdditionalStart;
    }
    
    public Long getEventAdditionalStartSec() {
        return eventAdditionalStart / 1000L;
    }

    public void setEventAdditionalStart(Long eventAdditionalStart) {
        this.eventAdditionalStart = eventAdditionalStart;
    }

    public Long getEventAdditionalEnd() {
        return eventAdditionalEnd;
    }
    
    public Long getEventAdditionalEndSec() {
        return eventAdditionalEnd / 1000L;
    }

    public void setEventAdditionalEnd(Long eventAdditionalEnd) {
        this.eventAdditionalEnd = eventAdditionalEnd;
    }

    public String getTvLanguage() {
        return tvLanguage;
    }

    public void setTvLanguage(String tvLanguage) {
        this.tvLanguage = tvLanguage;
    }
}
