package sics.domain;

import java.io.*;
import java.util.List;

public class Country implements Serializable {

    private Long id;
    private String name;
    private String logo;
    private Boolean internationalCompetition;
    private boolean visible;
    private Long competitionId;
    
    // extra
    private Integer gameAmount;
    
    // campi usati per la ricerca presente nell'header
    private String seasonName;
    private List<String> seasonList;

    public Country() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }

    public Boolean getInternationalCompetition() {
        return internationalCompetition;
    }

    public void setInternationalCompetition(Boolean value) {
        this.internationalCompetition = value;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public Long getCompetitionId() {
        return competitionId;
    }

    public void setCompetitionId(Long competitionId) {
        this.competitionId = competitionId;
    }

    public Integer getGameAmount() {
        return gameAmount;
    }

    public void setGameAmount(Integer gameAmount) {
        this.gameAmount = gameAmount;
    }

    public String getSeasonName() {
        return seasonName;
    }

    public void setSeasonName(String seasonName) {
        this.seasonName = seasonName;
    }

    public List<String> getSeasonList() {
        return seasonList;
    }

    public void setSeasonList(List<String> seasonList) {
        this.seasonList = seasonList;
    }
}
