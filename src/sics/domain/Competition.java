package sics.domain;

import java.io.*;
import java.util.List;

public class Competition implements Serializable {

    private Long id;
    private String name;
    private String logo;
    private Long panelId;
    private Long sportId;
    private boolean visible;
    private Long countryId;
    private Long internationalCompetitionId;
    private Boolean ranking;
    private Long groupsetId;
    
    // campi usati per la ricerca presente nell'header
    private String seasonName;
    private List<String> seasonList;
    
    // extra fields
    private Long groupId;
    private String groupName;
    private Integer gameAmount;

    public Competition() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }

    public Long getPanelId() {
        return panelId;
    }

    public void setPanelId(Long panelId) {
        this.panelId = panelId;
    }

    public Long getSportId() {
        return sportId;
    }

    public void setSportId(Long sportId) {
        this.sportId = sportId;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public Long getInternationalCompetitionId() {
        return internationalCompetitionId;
    }

    public void setInternationalCompetitionId(Long internationalCompetitionId) {
        this.internationalCompetitionId = internationalCompetitionId;
    }

    public Boolean getRanking() {
        return ranking;
    }

    public void setRanking(Boolean ranking) {
        this.ranking = ranking;
    }

    public Long getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    public String getSeasonName() {
        return seasonName;
    }

    public void setSeasonName(String seasonName) {
        this.seasonName = seasonName;
    }

    public List<String> getSeasonList() {
        return seasonList;
    }

    public void setSeasonList(List<String> seasonList) {
        this.seasonList = seasonList;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public Integer getGameAmount() {
        return gameAmount;
    }

    public void setGameAmount(Integer gameAmount) {
        this.gameAmount = gameAmount;
    }
}
