package sics.domain;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class TeamStatsFilter implements Serializable {
    
    private Long id;
    private String tableType;
    private Long userId;
    private Long groupsetId;
    private String name;
    private Integer order;
    private Boolean isPreferred;
    
    private Boolean isSics;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTableType() {
        return tableType;
    }

    public void setTableType(String tableType) {
        this.tableType = tableType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Boolean getIsPreferred() {
        return isPreferred;
    }

    public void setIsPreferred(Boolean isPreferred) {
        this.isPreferred = isPreferred;
    }

    public Boolean getIsSics() {
        return isSics;
    }

    public void setIsSics(Boolean isSics) {
        this.isSics = isSics;
    }
}