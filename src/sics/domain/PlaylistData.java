package sics.domain;

import java.io.*;
import java.util.Date;
import sics.helper.DateHelper;
import sics.helper.GlobalHelper;

public class PlaylistData implements Serializable {

	private Long id;
	private String match;
	private Date date;
	private String type;
	private Long duration;
	private Long position;
	private Long half;
	private Long minute;
	private String note;

	public PlaylistData() {
	}

	/**
	 * @return the id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the match
	 */
	public String getMatch() {
		return match;
	}

	/**
	 * @param match the match to set
	 */
	public void setMatch(String match) {
		this.match = match;
	}

	/**
	 * @return the date
	 */
	public Date getDate() {
		return date;
	}

	/**
	 * @param date the date to set
	 */
	public void setDate(Date date) {
		this.date = date;
	}

	/**
	 * @return the type
	 */
	public String getType() {
		return type;
	}

	/**
	 * @param type the type to set
	 */
	public void setType(String type) {
		this.type = type;
	}

	/**
	 * @return the duration
	 */
	public Long getDuration() {
		return duration;
	}

	/**
	 * @param duration the duration to set
	 */
	public void setDuration(Long duration) {
		this.duration = duration;
	}

	/**
	 * @return the half
	 */
	public Long getHalf() {
		return half;
	}

	/**
	 * @param half the half to set
	 */
	public void setHalf(Long half) {
		this.half = half;
	}

	/**
	 * @return the minute
	 */
	public Long getMinute() {
		return minute;
	}

	/**
	 * @param minute the minute to set
	 */
	public void setMinute(Long minute) {
		this.minute = minute;
	}

	/**
	 * @return the note
	 */
	public String getNote() {
		return note;
	}

	/**
	 * @param note the note to set
	 */
	public void setNote(String note) {
		this.note = note;
	}

	public String getDateString() {
		return (date !=null) ? DateHelper.toString(date) : "";
	}

	/**
	 * @return the position
	 */
	public Long getPosition() {
		return position;
	}

	/**
	 * @param position the position to set
	 */
	public void setPosition(Long position) {
		this.position = position;
	}

	public Long getPositionEnd() {
		return position+duration;
	}

	public String getPositionString() {
		return GlobalHelper.formatTime(position);
	}

	public String getPositionEndString() {
		return GlobalHelper.formatTime(position+duration);
	}

	public String getDurationString() {
		return GlobalHelper.formatTimeSeconds(duration);
	}

}
