package sics.domain;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.commons.lang.BooleanUtils;

/**
 *
 * <AUTHOR>
 */
public class TeamStats implements Serializable {

    private Long id;
    private Long statsTypeId;
    private Long teamId;
    private String teamName;
    private String teamLogo;
    private String eventDescription;
    private Long divider;
    private Double eventAmount;
    private Long averageStatsTypeId;
    private String filter;
    private Boolean statsTypeIsOpposite;
    private Boolean statsTypeVisible;

    private String homeTeam;
    private String awayTeam;
    private Long competitionId;
    private String competitionName;
    private String competitionLogo;
    private String countryLogo;
    private Integer matchDay;
    private Long gameId;
    private Long fixtureId;
    private Date gameDate;
    private Long seasonId;
    private String seasonName;

    private Long playerId;
    private Integer playerJerseyNumber;             // numero maglietta
    private String playerFirstName;
    private String playerLastName;
    private String playerKnownName;
    private Long playerRoleId;
    private String playerRoleCode;
    private String playerRoleDescription;
    private String playerDetailRoleDescription;
    private String playerPhoto;
    private String playerFoot;
    private String playerCountry;
    private String playerBornDate;
    private Long playerHeight;

    private Boolean visibleForCompetitionPlayers;
    private Boolean visibleForTeams;
    private Boolean visibleForMatches;
    private Boolean visibleForPlayers;
    private Long averagePlayerAmount;

    private Long fixtureAmount;
    private Long fixturesDuration;
    private Long inLineupsAmount;
    private Long totalPlayers;
    private Long groupId;       // id girone

    // extra
    private Integer index;                          // usato per stabilire l'ordinamento nel report del player
    private Double eventAmountAverage90Minutes;
    private Double eventAmountAverage;
    private Double eventAmountPercentage;
    private Long tmpEventAmount;
    private Long tmpFixtureAmount;
    private Boolean isGrouped;                      // usato quando viene richiesto di raggruppare nelle watchlist

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStatsTypeId() {
        return statsTypeId;
    }

    public void setStatsTypeId(Long statsTypeId) {
        this.statsTypeId = statsTypeId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getTeamLogo() {
        return teamLogo;
    }

    public void setTeamLogo(String teamLogo) {
        this.teamLogo = teamLogo;
    }

    public String getEventDescription() {
        return eventDescription;
    }

    public void setEventDescription(String eventDescription) {
        this.eventDescription = eventDescription;
    }

    public Long getDivider() {
        return divider;
    }

    public void setDivider(Long divider) {
        this.divider = divider;
    }

    public Double getEventAmount() {
        return eventAmount;
    }

    public void setEventAmount(Double eventAmount) {
        if (this.divider != null) {
            this.eventAmount = 1D * eventAmount / this.divider;
        } else {
            this.eventAmount = eventAmount;
        }
//        if (this.statsTypeId != null && Long.compare(this.statsTypeId, 1) == 0) {
//            // arrivano secondi al posto dei minuti
//            this.eventAmount = Math.round(eventAmount / 60D);
//        } else {
//            this.eventAmount = eventAmount;
//        }
    }

    public String getEventAmountFormatted() {
        // niente decimali per tutti i valori che non hanno il divisore e per i minuti giocati
        if (this.divider != null && this.divider != 1L && Long.compare(this.statsTypeId, 1) != 0) {
            return String.format("%.1f", eventAmount).replace(",", ".");
        } else {
            return String.format("%.0f", eventAmount).replace(",", ".");
        }
    }

    public String getEventAmountForTable() {
        if (eventAmount == null || eventAmount == 0) {
            return "-";
        }

        return getEventAmountFormatted();
    }

    public Long getAverageStatsTypeId() {
        return averageStatsTypeId;
    }

    public void setAverageStatsTypeId(Long averageStatsTypeId) {
        this.averageStatsTypeId = averageStatsTypeId;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public String getFilterEncoded() {
        try {
            return URLEncoder.encode(filter, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(TeamStats.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "Error";
    }

    public Boolean getStatsTypeIsOpposite() {
        return statsTypeIsOpposite;
    }

    public void setStatsTypeIsOpposite(Boolean statsTypeIsOpposite) {
        this.statsTypeIsOpposite = statsTypeIsOpposite;
    }

    public Boolean getStatsTypeVisible() {
        return statsTypeVisible;
    }

    public void setStatsTypeVisible(Boolean statsTypeVisible) {
        this.statsTypeVisible = statsTypeVisible;
    }

    public String getHomeTeam() {
        return homeTeam;
    }

    public void setHomeTeam(String homeTeam) {
        this.homeTeam = homeTeam;
    }

    public String getAwayTeam() {
        return awayTeam;
    }

    public void setAwayTeam(String awayTeam) {
        this.awayTeam = awayTeam;
    }

    public Long getCompetitionId() {
        return competitionId;
    }

    public void setCompetitionId(Long competitionId) {
        this.competitionId = competitionId;
    }

    public String getCompetitionName() {
        return competitionName;
    }

    public void setCompetitionName(String competitionName) {
        this.competitionName = competitionName;
    }

    public String getCompetitionLogo() {
        return competitionLogo;
    }

    public void setCompetitionLogo(String competitionLogo) {
        this.competitionLogo = competitionLogo;
    }

    public String getCountryLogo() {
        return countryLogo;
    }

    public void setCountryLogo(String countryLogo) {
        this.countryLogo = countryLogo;
    }

    public Integer getMatchDay() {
        return matchDay;
    }

    public void setMatchDay(Integer matchDay) {
        this.matchDay = matchDay;
    }

    public Long getGameId() {
        return gameId;
    }

    public void setGameId(Long gameId) {
        this.gameId = gameId;
    }

    public Long getFixtureId() {
        return fixtureId;
    }

    public void setFixtureId(Long fixtureId) {
        this.fixtureId = fixtureId;
    }

    public Date getGameDate() {
        return gameDate;
    }

    public String getGameDateString() {
        if (gameDate == null) {
            return "";
        }

        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
        return formatter.format(gameDate);
    }

    public void setGameDate(Date gameDate) {
        this.gameDate = gameDate;
    }

    public Long getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(Long seasonId) {
        this.seasonId = seasonId;
    }

    public String getSeasonName() {
        return seasonName;
    }

    public void setSeasonName(String seasonName) {
        this.seasonName = seasonName;
    }

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }

    public Integer getPlayerJerseyNumber() {
        return playerJerseyNumber;
    }

    public void setPlayerJerseyNumber(Integer playerJerseyNumber) {
        this.playerJerseyNumber = playerJerseyNumber;
    }

    public String getPlayerFirstName() {
        return playerFirstName;
    }

    public void setPlayerFirstName(String playerFirstName) {
        this.playerFirstName = playerFirstName;
    }

    public String getPlayerLastName() {
        return playerLastName;
    }

    public void setPlayerLastName(String playerLastName) {
        this.playerLastName = playerLastName;
    }

    public String getPlayerKnownName() {
        return playerKnownName;
    }

    public void setPlayerKnownName(String playerKnownName) {
        this.playerKnownName = playerKnownName;
    }

    public Long getPlayerRoleId() {
        return playerRoleId;
    }

    public void setPlayerRoleId(Long playerRoleId) {
        this.playerRoleId = playerRoleId;
    }

    public String getPlayerRoleCode() {
        return playerRoleCode;
    }

    public void setPlayerRoleCode(String playerRoleCode) {
        this.playerRoleCode = playerRoleCode;
    }

    public String getPlayerRoleDescription() {
        return playerRoleDescription;
    }

    public void setPlayerRoleDescription(String playerRoleDescription) {
        this.playerRoleDescription = playerRoleDescription;
    }

    public String getPlayerDetailRoleDescription() {
        return playerDetailRoleDescription;
    }

    public void setPlayerDetailRoleDescription(String playerDetailRoleDescription) {
        this.playerDetailRoleDescription = playerDetailRoleDescription;
    }

    public String getPlayerPhoto() {
        return playerPhoto;
    }

    public void setPlayerPhoto(String playerPhoto) {
        this.playerPhoto = playerPhoto;
    }

    public String getPlayerFoot() {
        return playerFoot;
    }

    public void setPlayerFoot(String playerFoot) {
        this.playerFoot = playerFoot;
    }

    public String getPlayerCountry() {
        return playerCountry;
    }

    public void setPlayerCountry(String playerCountry) {
        this.playerCountry = playerCountry;
    }

    public String getPlayerBornDate() {
        return playerBornDate;
    }

    public void setPlayerBornDate(String playerBornDate) {
        this.playerBornDate = playerBornDate;
    }

    public String getPlayerAge() {
        if (!playerBornDate.isEmpty()) {
            int bornYear = Integer.parseInt(playerBornDate.split("-")[0]);
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);
            return String.valueOf(currentYear - bornYear);
        }
        return "";
    }

    public String getPlayerBornDateYearLast2Digit() {
        if (!playerBornDate.isEmpty()) {
            String year = playerBornDate.split("-")[0];
            if (year.length() == 4) {
                return "'" + year.substring(2);
            } else {
                return year; // per sicurezza
            }
        }
        return "";
    }

    public Long getPlayerHeight() {
        return playerHeight;
    }

    public void setPlayerHeight(Long playerHeight) {
        this.playerHeight = playerHeight;
    }

    public Boolean getVisibleForCompetitionPlayers() {
        return visibleForCompetitionPlayers;
    }

    public void setVisibleForCompetitionPlayers(Boolean visibleForCompetitionPlayers) {
        this.visibleForCompetitionPlayers = visibleForCompetitionPlayers;
    }

    public Boolean getVisibleForTeams() {
        return visibleForTeams;
    }

    public void setVisibleForTeams(Boolean visibleForTeams) {
        this.visibleForTeams = visibleForTeams;
    }

    public Boolean getVisibleForMatches() {
        return visibleForMatches;
    }

    public void setVisibleForMatches(Boolean visibleForMatches) {
        this.visibleForMatches = visibleForMatches;
    }

    public Boolean getVisibleForPlayers() {
        return visibleForPlayers;
    }

    public void setVisibleForPlayers(Boolean visibleForPlayers) {
        this.visibleForPlayers = visibleForPlayers;
    }

    public Long getAveragePlayerAmount() {
        return averagePlayerAmount;
    }

    public void setAveragePlayerAmount(Long averagePlayerAmount) {
        this.averagePlayerAmount = averagePlayerAmount;
    }

    public Long getFixtureAmount() {
        return fixtureAmount;
    }

    public void setFixtureAmount(Long fixtureAmount) {
        this.fixtureAmount = fixtureAmount;
    }

    public Long getFixturesDuration() {
        return fixturesDuration;
    }

    public void setFixturesDuration(Long fixturesDuration) {
        this.fixturesDuration = Math.round(fixturesDuration / 60D);
    }

    public Long getInLineupsAmount() {
        return inLineupsAmount;
    }

    public void setInLineupsAmount(Long inLineupsAmount) {
        this.inLineupsAmount = inLineupsAmount;
    }

    public Long getTotalPlayers() {
        return totalPlayers;
    }

    public void setTotalPlayers(Long totalPlayers) {
        this.totalPlayers = totalPlayers;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Integer getIndex() {
        return index;
    }

    public String getIndexForTable() {
        if (eventAmountAverage90Minutes == null && statsTypeId != 1) {
            return "-"; // per Minuti Giocati va bene riportare l'index
        }
        return index + "°";
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Double getEventAmountAverage90Minutes() {
        return eventAmountAverage90Minutes;
    }

    public void setEventAmountAverage90Minutes(Double eventAmountAverage90Minutes) {
        this.eventAmountAverage90Minutes = eventAmountAverage90Minutes;
    }

    public String getEventAmountAverage90Minutes2Decimals() {
        if (eventAmountAverage90Minutes == null) {
            return "";
        }

        return String.format("%.2f", eventAmountAverage90Minutes);
    }

    public String getEventAmountAverage90Minutes1Decimal() {
        if (eventAmountAverage90Minutes == null) {
            return "-";
        }

        return String.format("%.1f", eventAmountAverage90Minutes);
    }

    public String getEventAmountAverage90MinutesFormatted() {
        if (eventAmountAverage90Minutes == null) {
            return "-";
        }
        if (statsTypeId == 1) {
            return getEventAmountFormatted();
        }

        return String.format(Locale.GERMAN, "%,.1f", eventAmountAverage90Minutes).replace(",", ".");
    }

    public Double getEventAmountAverage() {
        return eventAmountAverage;
    }

    public void setEventAmountAverage(Double eventAmountAverage) {
        this.eventAmountAverage = eventAmountAverage;
    }

    public String getEventAmountAverage2Decimals() {
        if (eventAmountAverage == null) {
            return "";
        }

        return String.format("%.2f", eventAmountAverage);
    }

    public String getEventAmountAverage1Decimal() {
        if (eventAmountAverage == null) {
            return "-";
        }

        return String.format("%.1f", eventAmountAverage);
    }

    public String getEventAmountAverageFormatted() {
        if (eventAmountAverage == null) {
            return "-";
        }

        return String.format(Locale.GERMAN, "%,.1f", eventAmountAverage).replace(",", ".");
    }

    public Double getEventAmountPercentage() {
        return eventAmountPercentage;
    }

    public void setEventAmountPercentage(Double eventAmountPercentage) {
        this.eventAmountPercentage = eventAmountPercentage;
    }

    public String getEventAmountPercentageFormatted() {
        if (eventAmountPercentage == null) {
            return getEventAmountFormatted();
        }

        return String.format(Locale.GERMAN, "%,.1f", eventAmountPercentage).replace(",", ".") + "%";
    }

    public Long getTmpEventAmount() {
        return tmpEventAmount;
    }

    public void setTmpEventAmount(Long tmpEventAmount) {
        this.tmpEventAmount = tmpEventAmount;
    }

    public Long getTmpFixtureAmount() {
        return tmpFixtureAmount;
    }

    public void setTmpFixtureAmount(Long tmpFixtureAmount) {
        this.tmpFixtureAmount = tmpFixtureAmount;
    }

    public Boolean getIsGrouped() {
        return isGrouped;
    }

    public void setIsGrouped(Boolean isGrouped) {
        this.isGrouped = isGrouped;
    }

    /*
    
        EXTRA STUFF

     */
    public String encodeString(String url) {
        try {
            return URLEncoder.encode(url, StandardCharsets.UTF_8.toString());
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger(TeamStats.class.getName()).log(Level.SEVERE, null, ex);
        }
        return "Error";
    }

    public String valueWithoutDecimals(double value) {
        return String.format("%.0f", value);
    }

    public String valueWith1Decimal(double value) {
        if (value < 0.1) { // non posso mostrare 0.0
            value = 0.1;
        }
        return String.format("%.1f", value);
    }

    public String valueWith2Decimals(double value) {
        return String.format("%.2f", value);
    }

    // questa funzione prende in mano una mappa formata così
    // Map<TeamId, Map<StatsId, TeamStats>>
    // e ritorna la lista dei TeamId dopo aver ordinato per un determinato StatsId
    public Map<Long, Integer> getStatsSortedByStatsId(Map<Long, Map<Long, TeamStats>> datas, long statId, Long teamIdRequired) {
        List<TeamStats> sortedList = new ArrayList<>();

        for (Long tmpTeamId : datas.keySet()) {
            if (datas.get(tmpTeamId).get(statId) != null) {
                sortedList.add(datas.get(tmpTeamId).get(statId));
            }
        }
        Collections.sort(sortedList, new Comparator<TeamStats>() {
            @Override
            public int compare(TeamStats o1, TeamStats o2) {
                return o2.getEventAmount().compareTo(o1.getEventAmount());
            }
        });

        Map<Long, Integer> teamIdsSorted = new LinkedHashMap<>();
        int requiredTeamIndex = -1;
        for (TeamStats stat : sortedList) {
            if (teamIdsSorted.size() < 20) {
                teamIdsSorted.put(stat.getTeamId(), sortedList.indexOf(stat));
            }

            if (teamIdRequired != null && stat.getTeamId() != null
                    && Long.compare(stat.getTeamId(), teamIdRequired) == 0) {
                requiredTeamIndex = sortedList.indexOf(stat);
            }
        }

        if (teamIdRequired != null) {
            // mi assicuro che sia almeno nei primi 20
            if (!teamIdsSorted.containsKey(teamIdRequired)) {
                teamIdsSorted.remove((Long) teamIdsSorted.keySet().toArray()[teamIdsSorted.size() - 1]);
                teamIdsSorted.put(teamIdRequired, requiredTeamIndex);
            }
        }

        return teamIdsSorted;
    }

    public List<Long> getPlayerStatsSortedByStatsId(Map<Long, Map<Long, TeamStats>> datas, long statId) {
        List<TeamStats> sortedList = new ArrayList<>();

        if (datas != null) {
            for (Long tmpTeamId : datas.keySet()) {
                if (datas.get(tmpTeamId).get(statId) != null) {
                    sortedList.add(datas.get(tmpTeamId).get(statId));
                }
            }
            Collections.sort(sortedList, new Comparator<TeamStats>() {
                @Override
                public int compare(TeamStats o1, TeamStats o2) {
                    return o2.getEventAmount().compareTo(o1.getEventAmount());
                }
            });
        }

        List<Long> playerIdsSorted = new ArrayList<>();
        for (TeamStats stat : sortedList) {
            playerIdsSorted.add(stat.getPlayerId());
        }

        if (playerIdsSorted.size() > 20) {
            playerIdsSorted = playerIdsSorted.subList(0, 20);
        }

        return playerIdsSorted;
    }

    public long getEventPercentage90Minutes(Double maxStatValue, Double minStatValue) {
        if (maxStatValue == null) {
            maxStatValue = 1D;
        }
        if (minStatValue == null) {
            minStatValue = 0D;
        }
        if (Double.compare(minStatValue, maxStatValue) == 0) {
            if (BooleanUtils.isTrue(statsTypeIsOpposite)) {
                return 5L;
            } else {
                return 100L;
            }
        }
        double statValue = (eventAmountAverage90Minutes == null ? 0 : eventAmountAverage90Minutes);
        long percentage = Math.round((statValue - minStatValue) / (maxStatValue - minStatValue) * 100);
        if (percentage < 5L && statValue > 0) {
            percentage = 5L;
        }

        return percentage;
    }

    public long getEventPercentage(Double maxStatValue, Double minStatValue) {
        if (maxStatValue == null) {
            maxStatValue = 1D;
        }
        if (minStatValue == null) {
            minStatValue = 0D;
        }
        if (Double.compare(minStatValue, maxStatValue) == 0) {
            if (BooleanUtils.isTrue(statsTypeIsOpposite)) {
                return 5L;
            } else {
                return 100L;
            }
        }
        double statValue = (eventAmountAverage == null ? 0 : eventAmountAverage);
        long percentage = Math.round((statValue - minStatValue) / (maxStatValue - minStatValue) * 100);
        if (percentage < 5L && statValue > 0) {
            percentage = 5L;
        }

        return percentage;
    }

    public long getPercentageByValues(Double statValue, Double maxStatValue, Double minStatValue) {
        if (maxStatValue == null) {
            maxStatValue = 1D;
        }
        if (minStatValue == null) {
            minStatValue = 0D;
        }
        if (Double.compare(minStatValue, maxStatValue) == 0) {
            if (BooleanUtils.isTrue(statsTypeIsOpposite)) {
                return 0L;
            } else {
                return 100L;
            }
        }
        return Math.round((statValue - minStatValue) / (maxStatValue - minStatValue) * 100);
    }
}
