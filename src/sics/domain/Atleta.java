package sics.domain;

import com.google.gson.Gson;
import com.google.gson.annotations.Expose;
import java.io.File;
import java.io.FileInputStream;
import java.nio.FloatBuffer;
import java.nio.channels.FileChannel;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;

import sics.analysis.DatoDS;
import sics.analysis.FasciaResult;
import sics.analysis.Punto;
import sics.analysis.Traccia;
import sics.helper.DateHelper;
import sics.helper.GlobalHelper;

/**
 *
 * <AUTHOR>
 */
public class Atleta implements Comparable {

    private Integer idFixture;
    private Long idPosition;
    private Integer id;
    private Long personalId;

    private int fromPeriod = 0;
    private int toPeriod = 0;
    private int fromMinute = 0;
    private int toMinute = 0;

    @Expose
    private String known_name = "";
    private String first_name = "";
    private String last_name = "";
    private Integer numero;
    @Expose
    private Integer matchNumber;
    private Long idRuolo;
    private String ruolo;
    private String descrizione_ruolo_it;
    private String descrizione_ruolo_en;
    private String descrizione_ruolo_fr;
    private String descrizione_ruolo_es;
    private String descrizione_ruolo_ru;
    private Integer team;
    private String teamName, teamLogo;
    private Game game;
    private String bornDate = "";

    private String module;
    private Integer module_position;
    private Long userId;
    private String userName;
    private Integer groupset_id;
    private String photo;

    private Punto mPosition = new Punto(-1, -1);
    private int redCard;
    private ArrayList<Integer> ammonito = new ArrayList<Integer>();
    private int espulso = -1;
    private int sos = -1;
    private int sub = -1;
    private int reti = 0;
    private int autorete = 0;
    private String height = "";
    private String country_it = "";
    private String country_en = "";
    private String country_logo = "";
    private Long footId;
    private String foot_it = "";
    private String foot_en = "";
    private String foot_fr = "";
    private String foot_es = "";
    private String foot_ru = "";
    private String position_detail_it = "";
    private String position_detail_en = "";
    private String position_detail_fr = "";
    private String position_detail_es = "";
    private String position_detail_ru = "";
    private String position_secondary_en = "";
    private String position_secondary_it = "";
    private String position_secondary_fr = "";
    private String position_secondary_es = "";
    private String position_secondary_ru = "";

//    private Long mPlayerOut;
//    private Long mPlayerIn;
    private static Boolean mRotate = false;

    private File mNewBinFile = null;
    // è una lista ma al momento ha un solo oggetto al suo interno perché le passo un solo inizio e una sola fine
    private ArrayList<FasciaResult> filtriPhy = new ArrayList<FasciaResult>();

    private static AtletaComparator atlComparator;

    private ArrayList<Team> teamPlayer = new ArrayList<Team>();
    private Integer actionAmount;           // campo usato nella pagina video.jsp per stabilire quante azioni sono state fatte da un determinato atleta
    // usato anche per popolare il campetto presente nel profilo del giocatore, per sapere quante volte ha giocato dove

    // campi usati per la ricerca presente nell'header
    private String seasonName;
    private List<String> seasonList;
    private Long seasonId;

    private Integer index, sort;
    private Boolean shared;

    private Long agentId;
    private String agentName;               // arriva da tabella player_agent
    private Long marketValue;
    private Date contractExpires;           // scandeza contratto
    private Long playtime;
    private String competitionIds;          // lista di competizioni giocate da un giocatore, usato per "Full Roaster" in pagina team

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getPersonalId() {
        return personalId;
    }

    public void setPersonalId(Long personalId) {
        this.personalId = personalId;
    }

    public Long getIdPosition() {
        return idPosition;
    }

    public void setIdPosition(Long idPosition) {
        this.idPosition = idPosition;
    }

    public String getKnown_name() {
        return known_name;
    }

    public void setKnown_name(String known_name) {
        this.known_name = known_name;
    }

    public String getFirst_name() {
        return first_name;
    }

    public void setFirst_name(String first_name) {
        this.first_name = first_name;
    }

    public String getLast_name() {
        return last_name;
    }

    public void setLast_name(String last_name) {
        this.last_name = last_name;
    }

    public Integer getNumero() {
        return numero;
    }

    public void setNumero(Integer numero) {
        this.numero = numero;
    }

    public Integer getMatchNumber() {
        return matchNumber;
    }

    public void setMatchNumber(Integer matchNumber) {
        this.matchNumber = matchNumber;
    }

    public Long getIdRuolo() {
        return idRuolo;
    }

    public void setIdRuolo(Long idRuolo) {
        this.idRuolo = idRuolo;
    }

    public String getRuolo() {
        return ruolo;
    }

    public void setRuolo(String ruolo) {
        this.ruolo = ruolo;
    }

    public String getDescrizione_ruolo_it() {
        return descrizione_ruolo_it;
    }

    public void setDescrizione_ruolo_it(String descrizione_ruolo_it) {
        this.descrizione_ruolo_it = descrizione_ruolo_it;
    }

    public String getDescrizione_ruolo_en() {
        return descrizione_ruolo_en;
    }

    public void setDescrizione_ruolo_en(String descrizione_ruolo_en) {
        this.descrizione_ruolo_en = descrizione_ruolo_en;
    }

    public String getDescrizione_ruolo_fr() {
        return descrizione_ruolo_fr;
    }

    public void setDescrizione_ruolo_fr(String descrizione_ruolo_fr) {
        this.descrizione_ruolo_fr = descrizione_ruolo_fr;
    }

    public String getDescrizione_ruolo_es() {
        return descrizione_ruolo_es;
    }

    public void setDescrizione_ruolo_es(String descrizione_ruolo_es) {
        this.descrizione_ruolo_es = descrizione_ruolo_es;
    }

    public String getDescrizione_ruolo_ru() {
        return descrizione_ruolo_ru;
    }

    public void setDescrizione_ruolo_ru(String descrizione_ruolo_ru) {
        this.descrizione_ruolo_ru = descrizione_ruolo_ru;
    }

    public Integer getTeam() {
        return team;
    }

    public void setTeam(Integer team) {
        this.team = team;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getTeamLogo() {
        return teamLogo;
    }

    public void setTeamLogo(String teamLogo) {
        this.teamLogo = teamLogo;
    }

    public File getmNewBinFile() {
        return mNewBinFile;
    }

    public void setmNewBinFile(File mNewBinFile) {
        this.mNewBinFile = mNewBinFile;
    }

    public Game getGame() {
        return game;
    }

    public void setGame(Game game) {
        this.game = game;
    }

    public int getFromPeriod() {
        return fromPeriod;
    }

    public void setFromPeriod(int fromPeriod) {
        this.fromPeriod = fromPeriod;
    }

    public int getToPeriod() {
        return toPeriod;
    }

    public void setToPeriod(int toPeriod) {
        this.toPeriod = toPeriod;
    }

    public int getFromMinute() {
        return fromMinute;
    }

    public void setFromMinute(int fromMinute) {
        this.fromMinute = fromMinute;
    }

    public int getToMinute() {
        return toMinute;
    }

    public void setToMinute(int toMinute) {
        this.toMinute = toMinute;
    }

    public Punto getmPosition() {
        return mPosition;
    }

    public void setmPositionPunto(Punto p) {
        this.mPosition = p;
    }

    public void setmPosition(String position) {
        String[] splitPos = position.split(";");
        if (splitPos.length == 2) {
            this.mPosition = new Punto(Float.parseFloat(splitPos[0].replace(",", ".")), Float.parseFloat(splitPos[1].replace(",", ".")));
        } else {
            this.mPosition = new Punto(-1, -1);
        }
    }

    public int getRedCard() {
        return redCard;
    }

    public void setRedCard(int redCard) {
        this.redCard = redCard;
    }

    public ArrayList<Integer> getAmmonito() {
        return ammonito;
    }

    public void addAmmonito(int minAmm) {
        this.ammonito.add(minAmm);
    }

    public int getEspulso() {
        return espulso;
    }

    public void setEspulso(int minEspulso) {
        this.espulso = minEspulso;
    }

    public int getSos() {
        return sos;
    }

    public void setSos(int minSos) {
        this.sos = minSos;
    }

    public int getSub() {
        return sub;
    }

    public void setSub(int minSub) {
        this.sub = minSub;
    }

    public int getReti() {
        return reti;
    }

    public void addRete() {
        this.reti += 1;
    }

    public int getAutorete() {
        return autorete;
    }

    public void addAutorete() {
        this.autorete += 1;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public Integer getModule_position() {
        return module_position;
    }

    public void setModule_position(Integer module_position) {
        this.module_position = module_position;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getGroupset_id() {
        return groupset_id;
    }

    public void setGroupset_id(Integer groupset_id) {
        this.groupset_id = groupset_id;
    }

    private ArrayList<Traccia> filtroNew(Long inizio, Long fine, Float min, Float max, Integer type) {
        ArrayList<Traccia> tracce = new ArrayList<Traccia>();
        try {
            DatoDS dato = null;
            Punto point = null;
            if (type.equals(0)) {
                min = min / 3.6f;
                max = max / 3.6f;
            }

            Boolean continuaTraccia = false;
            Traccia tracciaCorrente = new Traccia();
//            Traccia tracciaPrecedente = null;
//            Integer KOF2 = DSFilter.getmMatchData().getKOF2();

            Boolean trovato = false;

            Long byteLinea = inizio * 28;
            Long length = mNewBinFile.length();
            Long byteFine = fine * 28 + 28;
            if (byteFine > length) {
                byteFine = length;
            }
            Long dimensione = byteFine - byteLinea;
            if (dimensione < 0) {
                return tracce;
            }

            Integer frame = inizio.intValue();
            float x;
            float y;
            float dis;
            float vel;
            float acc;
            float pow;

//            long start = System.nanoTime();
            FileInputStream inFile = new FileInputStream(mNewBinFile);
            FileChannel fc = inFile.getChannel();

            FloatBuffer floatBuf = fc.map(FileChannel.MapMode.READ_ONLY, byteLinea, dimensione).asFloatBuffer();

            Integer index = 0;
            Integer indexFine = dimensione.intValue() / 4; //Numero di oggetti float

            while (index < indexFine) {
                frame += 1;

                index += 1;
                x = floatBuf.get(index);
                index += 1;
                y = floatBuf.get(index);
                index += 1;
                dis = floatBuf.get(index);
                index += 1;
                vel = floatBuf.get(index);
                index += 1;
                acc = floatBuf.get(index);
                index += 1;
                pow = floatBuf.get(index);
                index += 1;
                if (x == -999) {
                    continue;
                }
                switch (type) {
                    case 0: {
                        if (vel >= min && vel < max) {
                            trovato = true;
                        }
                        break;
                    }
                    case 1: {
                        if (acc >= min && acc < max) {
                            trovato = true;
                        }
                        break;
                    }
                    case 2: {
                        if (pow >= min && pow < max) {
                            trovato = true;
                        }
                        break;
                    }
                }

                if (trovato) {
                    point = new Punto(x, y);
                    dato = new DatoDS(frame, point, dis, vel, acc, pow);
                    if (continuaTraccia) {
                        tracciaCorrente.getmPosizioni().add(dato);
                    } else {
                        tracciaCorrente = new Traccia();
                        tracciaCorrente.getmPosizioni().add(dato);
                        tracce.add(tracciaCorrente);
//                        if (tracce.size() > 1){
//                            tracciaPrecedente = tracce.get(tracce.size() - 2);
//                            Integer frame_end = tracciaPrecedente.getmPosizioni().get(tracciaPrecedente.getmPosizioni().size() - 1).getmFrame();
//                            if (frame_end > KOF2 && frame < KOF2) {
//                                tracciaCorrente.recupero = "";
//                            } else {
//                                tracciaCorrente.recupero = GlobalHelper.msecToMinSec(GlobalHelper.frameToMilliseconds(frame - frame_end), false, true);
//                            }
//                        } else {
//                            tracciaCorrente.recupero = "";
//                        }
                        continuaTraccia = true;
                    }
                } else {
                    continuaTraccia = false;
                }
                trovato = false;
            }
            floatBuf.clear();
            fc.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return tracce;
    }

    // type = 0 speed, type = 1 acc, type = 2 power
    public void filtri(ArrayList<Long> listStart, ArrayList<Long> listStop, Integer type) {
        try {

            for (int i = 0; i < listStart.size(); i++) {
                Long start = listStart.get(i);
                Long stop = listStop.get(i);

                Integer KOF2 = game.getKOF2();
                Traccia tracciaPrecedente;

                for (FasciaResult result : filtriPhy) {
                    Fascia f = result.getmFascia();
                    ArrayList<Traccia> tracce = filtroNew(start, stop, f.getValMin(), f.getValMax(), type);

                    tracciaPrecedente = null;

                    ArrayList<Traccia> tracceOk = new ArrayList<Traccia>();
                    for (Traccia traccia : tracce) {

                        Float length = 0f;
                        for (DatoDS d : traccia.getmPosizioni()) {
                            length += d.getmDis();
                        }
                        traccia.setmLength(length);
                        result.mLength += length;
                        result.time += traccia.getmPosizioni().size() / 25;

                        // DAFARE verificare la durata minima delle tracce (1s)
                        if (traccia.getmPosizioni().size() >= 25) {
                            tracceOk.add(traccia);
                            if (tracceOk.size() > 1) {
                                tracciaPrecedente = tracceOk.get(tracceOk.size() - 2);
                                Integer frame_end = tracciaPrecedente.getmPosizioni().get(tracciaPrecedente.getmPosizioni().size() - 1).getmFrame();
                                Integer frame = traccia.getmPosizioni().get(0).getmFrame();
                                if (frame_end < KOF2 && frame > KOF2) {
                                    traccia.recupero = "";
                                } else {
                                    traccia.recupero = GlobalHelper.msecToMinSec(GlobalHelper.frameToMilliseconds(frame - frame_end), false, true, false);
                                }
                            } else {
                                traccia.recupero = "";
                            }
                        }
                    }
                    result.getmTracce().addAll(tracceOk);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // type = 0 speed, type = 1 acc, type = 2 power
    public void filtriSVS(Long start, Long stop, Integer type, FasciaResult result) {
        try {
            Integer KOF2 = game.getKOF2();
            Traccia tracciaPrecedente;

            Fascia f = result.getmFascia();
            ArrayList<Traccia> tracce = filtroNew(start, stop, f.getValMin(), f.getValMax(), type);

            tracciaPrecedente = null;

            ArrayList<Traccia> tracceOk = new ArrayList<Traccia>();
            for (Traccia traccia : tracce) {

                Float length = 0f;
                for (DatoDS d : traccia.getmPosizioni()) {
                    length += d.getmDis();
                }
                traccia.setmLength(length);
                result.mLength += length;
                result.time += traccia.getmPosizioni().size() / 25;

                // DAFARE verificare la durata minima delle tracce (1s)
                if (traccia.getmPosizioni().size() >= 25) {
                    tracceOk.add(traccia);
                    if (tracceOk.size() > 1) {
                        tracciaPrecedente = tracceOk.get(tracceOk.size() - 2);
                        Integer frame_end = tracciaPrecedente.getmPosizioni().get(tracciaPrecedente.getmPosizioni().size() - 1).getmFrame();
                        Integer frame = traccia.getmPosizioni().get(0).getmFrame();
                        if (frame_end < KOF2 && frame > KOF2) {
                            traccia.recupero = "";
                        } else {
                            traccia.recupero = GlobalHelper.msecToMinSec(GlobalHelper.frameToMilliseconds(frame - frame_end), false, true, false);
                        }
                    } else {
                        traccia.recupero = "";
                    }
                }
            }
            result.getmTracce().addAll(tracceOk);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public ArrayList<FasciaResult> getFiltriPhy() {
        return filtriPhy;
    }

    public void setFiltriPhy(ArrayList<FasciaResult> filtriPhy) {
        this.filtriPhy = filtriPhy;
    }

    public void filtriTest(Long start, Long stop, Integer type, FasciaResult fr) {
        try {
            long startTime = System.nanoTime();

//            DSFilter filter = WindowMain.getmPanelAnalysis().getmDSFilter();
            ArrayList<FasciaResult> fasceResult = new ArrayList<>();
            fasceResult.add(fr);
            //ArrayList<Integer> indiciFasceResult = new ArrayList<>();
//            ArrayList<Fascia> fasce = new ArrayList<>();
//            switch (type) {
//                case 0: {
//                    fasce = filter.getFasceVel();
//                    break;
//                }
//                case 1: {
//                    fasce = filter.getFasceAcc();
//                    break;
//                }
//                case 2: {
//                    fasce = filter.getFascePow();
//                    break;
//                }
//            }

//			for (int i = 0; i < filtriPhy.size(); i++) {
//				FasciaResult fr = filtriPhy.get(i);
//				if (fasceResult.isEmpty()) {
//					if (fr.getmFascia().getValMin().equals(fascia.getValMin())) {
//						fasceResult.add(fr);
//					} else {
//						// se la fascia selezionata non parte da 0, inserisce una facia da 0 al minimo della fascia selezionata
//						fasceResult.add(new FasciaResult(new Fascia("", fascia.getValMin(), fr.getmFascia().getValMin())));
//					}
//				} else {
//					fasceResult.add(fr);
//				}
//			}
//			float maxValOfLast = fasceResult.get(fasceResult.size() - 1).getmFascia().getValMax();
            float maxValOfLast = fr.getmFascia().getValMax();
            if (maxValOfLast < Float.MAX_VALUE) {
                // aggiunge una fascia che va dal limite massimo al massimo valore float
                fasceResult.add(new FasciaResult(new Fascia("", maxValOfLast, Float.MAX_VALUE)));
            }

            // calcolo occorrenze
            ArrayList<DatoDS> righe = readRighe(start, stop);
            // lista occorrenze attualmente aperte
            ArrayList<Integer> occorrenze = new ArrayList<>();

            Long fine = stop - start;
            if (fine >= righe.size()) {
                fine = righe.size() - 1l;
            }

            for (int i = 25; i <= fine; i++) {
                // indici delle fasce entro cui rientra l'intervallo di 1sec che finisce con i
                ArrayList<Integer> indici_fasce = verificaFasce(righe, i, fasceResult, type);
                // aggiorna la lista occorrenze tenendo solo quelle che erano aperte già in precedenza e sono ancora in corso
                ArrayList<Integer> occ_analisi = new ArrayList<>();
                for (Integer occ : occorrenze) {
                    if (indici_fasce.contains(occ)) {
                        occ_analisi.add(occ);
                    }
                }
                occorrenze = occ_analisi;

                // quelle che sono occorrenze solo da ora vanno aggiunte alla lista occorrenze
                for (Integer indice_fascia : indici_fasce) {
                    if (!occorrenze.contains(indice_fascia)) {
                        // incrementa contatore, nuova occorrenza
                        occorrenze.add(indice_fascia);
                        fasceResult.get(indice_fascia).numOccorrenze += 1;
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // ritorna la lista di fasce (gli indici delle fasce) in cui cade l'intervallo che finisce con indice "fine"
    private ArrayList<Integer> verificaFasce(ArrayList<DatoDS> righe, int fine, ArrayList<FasciaResult> fasceResult, Integer type) {
        ArrayList<Integer> fasce = new ArrayList<>();
        try {
            // ogni elemento indica una fascia, il valore è il num di righe che stanno in quella fascia
            ArrayList<Integer> occorrenze = new ArrayList<>();
            for (int i = 0; i < fasceResult.size(); i++) {
                occorrenze.add(0);
            }
            Integer inizio = fine - 25 + 1;

            //lista delle fasce in cui rientra la riga
            ArrayList<Integer> indici_riga;
            for (int i = inizio; i <= fine; i++) {
                // lista delle fasce in cui rientra quella riga
                indici_riga = indiciFasce(righe.get(i), fasceResult, type);
                for (Integer indice_riga : indici_riga) {
                    occorrenze.set(indice_riga, occorrenze.get(indice_riga) + 1);
                }
            }

            for (int i = 0; i < occorrenze.size(); i++) {
                if (occorrenze.get(i) == 25) {
                    // se num di occorrenze = intervalli => dentro la fascia senza "buchi"
                    fasce.add(i);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return fasce;
    }

    private ArrayList<Integer> indiciFasce(DatoDS ds, ArrayList<FasciaResult> fasceResult, Integer type) {
        ArrayList<Integer> indici = new ArrayList<>();
        if (ds == null) {
            return indici;
        }
        try {
            Float value = 0f;
            switch (type) {
                case 0: {
                    value = ds.mVel;
                    break;
                }
                case 1: {
                    value = ds.mAcc;
                    break;
                }
                case 2: {
                    value = ds.mPow;
                    break;
                }
            }
            Integer j = 0;
            Fascia f;
            while (j < fasceResult.size()) {
                f = fasceResult.get(j).getmFascia();
                if (type == 1) {
                    // Accelerazione
                    if (value >= 0) {
                        if (f.getValMin() >= 0) {
                            if (value >= f.getValMin()) {
                                indici.add(j);
                            }
                        }
                    } else {
                        if (f.getValMax() <= 0) {
                            if (value <= f.getValMax()) {
                                indici.add(j);
                            }
                        }
                    }
                } else {
                    if (value >= 0) {
                        // valore positivo
                        if (f.getValMin() == 0) {
                            // solo fascia 0 - x
                            if (value < f.getValMax()) {
                                indici.add(j);
                            }
                        } else if (f.getValMin() > 0) {
                            if (value >= f.getValMin()) {
                                indici.add(j);
                            }
                        }
                    } else {
                        // valore negativo
                        if (f.getValMax() == 0) {
                            // fascia -x - 0
                            if (value > f.getValMin()) {
                                indici.add(j);
                            }
                        } else if (f.getValMax() < 0) {
                            if (value <= f.getValMax()) {
                                indici.add(j);
                            }
                        }
                    }
                }
                j += 1;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return indici;
    }

    private ArrayList<DatoDS> readRighe(Long inizio, Long fine) {
        ArrayList<DatoDS> righe = new ArrayList<>();
        try {
            DatoDS dato = null;
            Punto point = null;

            Long byteLinea = inizio * 28;
            Long length = mNewBinFile.length();
            Long byteFine = fine * 28 + 28;
            if (byteFine > length) {
                byteFine = length;
            }
            Long dimensione = byteFine - byteLinea;
            if (dimensione < 0) {
                return righe;
            }

            Integer frame = inizio.intValue();
            float x;
            float y;
            float dis;
            float vel;
            float acc;
            float pow;

            FileInputStream inFile = new FileInputStream(mNewBinFile);
            FileChannel fc = inFile.getChannel();

            FloatBuffer floatBuf = fc.map(FileChannel.MapMode.READ_ONLY, byteLinea, dimensione).asFloatBuffer();

            Integer index = 0;
            Integer indexFine = dimensione.intValue() / 4; //Numero di oggetti float

            while (index < indexFine) {
                frame += 1;

                index += 1;
                x = floatBuf.get(index);
                index += 1;
                y = floatBuf.get(index);
                index += 1;
                dis = floatBuf.get(index);
                index += 1;
                vel = floatBuf.get(index) * 3.6f;
                index += 1;
                acc = floatBuf.get(index);
                index += 1;
                pow = floatBuf.get(index);
                index += 1;
                if (x == -999) {
                    continue;
                } else {
                    point = new Punto(x, y);
                    dato = new DatoDS(frame, point, dis, vel, acc, pow);
                }
                righe.add(dato);
            }
            floatBuf.clear();
            fc.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return righe;
    }

    public String getBornDate() {
        return bornDate;
    }

    public void setBornDate(String bornDate) {
        this.bornDate = bornDate;
    }

    public String getBornDateYear() {
        if (!bornDate.isEmpty()) {
            return bornDate.split("-")[0];
        }
        return "";
    }

    public String getBornDateYearLast2Digit() {
        if (!bornDate.isEmpty()) {
            String year = bornDate.split("-")[0];
            if (year.length() == 4) {
                return "'" + year.substring(2);
            } else {
                return year; // per sicurezza
            }
        }
        return "";
    }

    public String getBornDatefull() {
        if (!bornDate.isEmpty()) {
            return DateHelper.toString(DateHelper.toDateUsaFromStringUsa(bornDate));
        }
        return "";
    }

    public String getAge() {
        if (!bornDate.isEmpty()) {
            try {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                return String.valueOf(Period.between(
                        formatter.parse(bornDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                        new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
                ).getYears());
            } catch (ParseException ex) {
                GlobalHelper.reportError(ex);
                return "";
            }
        }
        return "";
    }

    public ArrayList<Team> getTeamPlayer() {
        return teamPlayer;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    @Override
    public int compareTo(Object o) {
        Atleta atl2 = (Atleta) o;
        if (this.getMatchNumber() > atl2.getMatchNumber()) {
            return 1;
        }
        return -1;
    }

    public Punto getPosReport(Long time, Integer w, Integer h, boolean home) {
        return getPosReport(time, w, h, home, false);
    }

    public Punto getPosReport(Long time, Integer w, Integer h, boolean home, boolean dividiFormazioni) {
        Punto point = new Punto();
        if (dividiFormazioni) {
//            w = w / 2;
            if (!home) {
                point.setX((10 - mPosition.getX()) * (w / 2) / 10 + w / 2 - w / 15); // da modificare
                point.setY((8 - mPosition.getY()) * h / 8); // da modificare
            } else {
                point.setX(mPosition.getX() * (w / 2) / 10 + w / 15);// da modificare
                point.setY(mPosition.getY() * h / 8); // da modificare
            }
        } else {
            if ((mPosition.getX() != -1 && mPosition.getX() != 0) /*&& time > mPlayerIn && mPlayerOut > -1 && time < mPlayerOut*/) {

                if (home) {
                    point.setX(mPosition.getX() * w / 8 - w / 16);// da modificare
                    point.setY(mPosition.getY() * h / 8); // da modificare
                } else {
                    point.setX((8 - mPosition.getX()) * w / 8 + w / 16);// da modificare
                    point.setY((8 - mPosition.getY()) * h / 8); // da modificare
                }
            }
        }
        return point;
    }

    public static AtletaComparator getAtletaComparator() {
        if (atlComparator == null) {
            atlComparator = new AtletaComparator();
        }
        return atlComparator;
    }

    static class AtletaComparator implements Comparator<Atleta> {

        @Override
        public int compare(Atleta o1, Atleta o2) {
            return o1.getModule_position().compareTo(o2.getModule_position());
        }

    }

    /**
     * @return the height
     */
    public String getHeight() {
        return height;
    }

    /**
     * @param height the height to set
     */
    public void setHeight(String height) {
        if (height == null) {
            this.height = null;
        } else {
            this.height = height.toUpperCase() + " cm";
        }
    }

    public void setHeightNoCm(String height) {
        this.height = height.toUpperCase();
    }

    /**
     * @return the country_it
     */
    public String getCountry_it() {
        return country_it;
    }

    /**
     * @param country_it the country_it to set
     */
    public void setCountry_it(String country_it) {
        if (country_it == null) {
            this.country_it = country_it;
        } else {
            this.country_it = country_it.toUpperCase();
        }
    }

    /**
     * @return the country_en
     */
    public String getCountry_en() {
        return country_en;
    }

    /**
     * @param country_en the country_en to set
     */
    public void setCountry_en(String country_en) {
        if (country_en == null) {
            this.country_en = country_en;
        } else {
            this.country_en = country_en.toUpperCase();
        }
    }

    public String getCountry_logo() {
        return country_logo;
    }

    public void setCountry_logo(String country_logo) {
        this.country_logo = country_logo;
    }

    public Long getFootId() {
        return footId;
    }

    public void setFootId(Long footId) {
        this.footId = footId;
    }

    /**
     * @return the foot_it
     */
    public String getFoot_it() {
        return foot_it;
    }

    /**
     * @param foot_it the foot_it to set
     */
    public void setFoot_it(String foot_it) {
        if (foot_it == null) {
            this.foot_it = foot_it;
        } else {
            this.foot_it = foot_it.toUpperCase();
        }
    }

    /**
     * @return the foot_en
     */
    public String getFoot_en() {
        return foot_en;
    }

    /**
     * @param foot_en the foot_en to set
     */
    public void setFoot_en(String foot_en) {
        if (foot_en == null) {
            this.foot_en = foot_en;
        } else {
            this.foot_en = foot_en.toUpperCase();
        }
    }

    public String getFoot_fr() {
        return foot_fr;
    }

    public void setFoot_fr(String foot_fr) {
        this.foot_fr = foot_fr;
    }

    public String getFoot_es() {
        return foot_es;
    }

    public void setFoot_es(String foot_es) {
        this.foot_es = foot_es;
    }

    public String getFoot_ru() {
        return foot_ru;
    }

    public void setFoot_ru(String foot_ru) {
        this.foot_ru = foot_ru;
    }

    /**
     * @return the position_detail_it
     */
    public String getPosition_detail_it() {
        return position_detail_it;
    }

    /**
     * @param position_detail_it the position_detail_it to set
     */
    public void setPosition_detail_it(String position_detail_it) {
        if (position_detail_it == null) {
            this.position_detail_it = position_detail_it;
        } else {
            this.position_detail_it = position_detail_it.toUpperCase();
        }
    }

    /**
     * @return the position_detail_en
     */
    public String getPosition_detail_en() {
        return position_detail_en;
    }

    /**
     * @param position_detail_en the position_detail_en to set
     */
    public void setPosition_detail_en(String position_detail_en) {
        if (position_detail_en == null) {
            this.position_detail_en = position_detail_en;
        } else {
            this.position_detail_en = position_detail_en.toUpperCase();
        }
    }

    public String getPosition_detail_fr() {
        return position_detail_fr;
    }

    public void setPosition_detail_fr(String position_detail_fr) {
        this.position_detail_fr = position_detail_fr;
    }

    public String getPosition_detail_es() {
        return position_detail_es;
    }

    public void setPosition_detail_es(String position_detail_es) {
        this.position_detail_es = position_detail_es;
    }

    public String getPosition_detail_ru() {
        return position_detail_ru;
    }

    public void setPosition_detail_ru(String position_detail_ru) {
        this.position_detail_ru = position_detail_ru;
    }

    /**
     * @return the position_secondary_en
     */
    public String getPosition_secondary_en() {
        return position_secondary_en;
    }

    /**
     * @param position_secondary_en the position_secondary_en to set
     */
    public void setPosition_secondary_en(String position_secondary_en) {
        if (position_secondary_en == null) {
            this.position_secondary_en = position_secondary_en;
        } else {
            this.position_secondary_en = position_secondary_en.toUpperCase();
        }
    }

    /**
     * @return the position_secondary_it
     */
    public String getPosition_secondary_it() {
        return position_secondary_it;
    }

    /**
     * @param position_secondary_it the position_secondary_it to set
     */
    public void setPosition_secondary_it(String position_secondary_it) {
        if (position_secondary_it == null) {
            this.position_secondary_it = position_secondary_it;
        } else {
            this.position_secondary_it = position_secondary_it.toUpperCase();
        }
    }

    public String getPosition_secondary_fr() {
        return position_secondary_fr;
    }

    public void setPosition_secondary_fr(String position_secondary_fr) {
        this.position_secondary_fr = position_secondary_fr;
    }

    public String getPosition_secondary_es() {
        return position_secondary_es;
    }

    public void setPosition_secondary_es(String position_secondary_es) {
        this.position_secondary_es = position_secondary_es;
    }

    public String getPosition_secondary_ru() {
        return position_secondary_ru;
    }

    public void setPosition_secondary_ru(String position_secondary_ru) {
        this.position_secondary_ru = position_secondary_ru;
    }

    public Integer getActionAmount() {
        return actionAmount;
    }

    public void setActionAmount(Integer actionAmount) {
        this.actionAmount = actionAmount;
    }

    public String getSeasonName() {
        return seasonName;
    }

    public void setSeasonName(String seasonName) {
        this.seasonName = seasonName;
    }

    public List<String> getSeasonList() {
        return seasonList;
    }

    public void setSeasonList(List<String> seasonList) {
        this.seasonList = seasonList;
    }

    public Long getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(Long seasonId) {
        this.seasonId = seasonId;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Boolean getShared() {
        return shared;
    }

    public void setShared(Boolean shared) {
        this.shared = shared;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public Long getMarketValue() {
        return marketValue;
    }

    public String getMarketValueFormatted() {
        return (marketValue != null ? GlobalHelper.formatNumber(marketValue) : "");
    }

    public void setMarketValue(Long marketValue) {
        this.marketValue = marketValue;
    }

    public Date getContractExpires() {
        return contractExpires;
    }

    public String getContractExpiresString() {
        return (contractExpires != null ? DateHelper.toString(contractExpires) : "");
    }

    public void setContractExpires(Date contractExpires) {
        this.contractExpires = contractExpires;
    }

    public Long getPlaytime() {
        return playtime;
    }

    public void setPlaytime(Long playtime) {
        this.playtime = playtime;
    }

    public String getCompetitionIds() {
        return competitionIds;
    }

    public boolean containsCompetitionId(Long id) {
        if (competitionIds == null) {
            return false;
        }
        // split competitionIds for comma and check if the id exists
        List<String> ids = Arrays.asList(competitionIds.split(","));
        return ids.contains(id.toString());
    }

    public void setCompetitionIds(String competitionIds) {
        this.competitionIds = competitionIds;
    }

    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
