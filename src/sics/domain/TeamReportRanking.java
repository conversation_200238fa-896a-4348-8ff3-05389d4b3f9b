package sics.domain;

/**
 *
 * <AUTHOR>
 */
public class TeamReportRanking {

    private Long teamId;
    private String teamName;
    private String teamLogo;
    
    private Integer totalPoints;
    private Integer totalMatchPlayed;
    private Integer penalityPoints;
    private Integer totalWin;
    private Integer totalEqual;
    private Integer totalLost;
    private Integer totalGoal;
    private Integer totalGoalSuffered;
    private Integer totalGoalDifference;
    
    private Long competitionId;
    private String competitionName;
    private String competitionLogo;
    private Boolean competitionRanking;

    // extra columns
    private Integer index;
    
    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getTeamLogo() {
        return teamLogo;
    }

    public void setTeamLogo(String teamLogo) {
        this.teamLogo = teamLogo;
    }

    public Integer getTotalPoints() {
        return totalPoints;
    }

    public void setTotalPoints(Integer totalPoints) {
        this.totalPoints = totalPoints;
    }

    public Integer getTotalMatchPlayed() {
        return totalMatchPlayed;
    }

    public void setTotalMatchPlayed(Integer totalMatchPlayed) {
        this.totalMatchPlayed = totalMatchPlayed;
    }

    public Integer getPenalityPoints() {
        return penalityPoints;
    }

    public void setPenalityPoints(Integer penalityPoints) {
        this.penalityPoints = penalityPoints;
    }

    public Integer getTotalWin() {
        return totalWin;
    }

    public void setTotalWin(Integer totalWin) {
        this.totalWin = totalWin;
    }

    public Integer getTotalEqual() {
        return totalEqual;
    }

    public void setTotalEqual(Integer totalEqual) {
        this.totalEqual = totalEqual;
    }

    public Integer getTotalLost() {
        return totalLost;
    }

    public void setTotalLost(Integer totalLost) {
        this.totalLost = totalLost;
    }

    public Integer getTotalGoal() {
        return totalGoal;
    }

    public void setTotalGoal(Integer totalGoal) {
        this.totalGoal = totalGoal;
    }

    public Integer getTotalGoalSuffered() {
        return totalGoalSuffered;
    }

    public void setTotalGoalSuffered(Integer totalGoalSuffered) {
        this.totalGoalSuffered = totalGoalSuffered;
    }

    public Integer getTotalGoalDifference() {
        return totalGoalDifference;
    }

    public void setTotalGoalDifference(Integer totalGoalDifference) {
        this.totalGoalDifference = totalGoalDifference;
    }

    public Long getCompetitionId() {
        return competitionId;
    }

    public void setCompetitionId(Long competitionId) {
        this.competitionId = competitionId;
    }

    public String getCompetitionName() {
        return competitionName;
    }

    public void setCompetitionName(String competitionName) {
        this.competitionName = competitionName;
    }

    public String getCompetitionLogo() {
        return competitionLogo;
    }

    public void setCompetitionLogo(String competitionLogo) {
        this.competitionLogo = competitionLogo;
    }

    public Boolean getCompetitionRanking() {
        return competitionRanking;
    }

    public void setCompetitionRanking(Boolean competitionRanking) {
        this.competitionRanking = competitionRanking;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }
}
