/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.domain;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Event implements Serializable {

    private Long idfixture;
    private String idevent;
    private String descr;
    private String type;
    private long start;
    private long end;
    private short half;
    private short idteam;
    private String player;
    private String playerTo;
    private String playerId;
    private String playerToId;
    private String note;
    private String rilevante;
    private String config;
    private String user;
//	private String role;
//	private String role_en;
    private String role_id;
//	private short rev;
    private String minutoInizio;
    private String minutoFine;

    private String secStartAzione;
    private String secEndAzione;

    private String durataAzione;
    private String tempoAzioneVideo;
    private String nomeSquadra;
    private Button button;
    //Stringhe di listTags con scritto dentro i codici dei listTags associati a questo evento
    private List listTags;
    // valore che mi serve per la lettura da database
    private String tag;
    private String tagCode;
    private String videoName;
    private Long videoId = 0l;
    private Long provider = 0l;
    private Long startHalf = 0l;

    /**
     * @return the idevent
     */
    public String getIdevent() {
        return idevent;
    }

    /**
     * @param idevent the idevent to set
     */
    public void setIdevent(String idevent) {
        this.idevent = idevent;
    }

    /**
     * @return the type
     */
    public String getType() {
        return type;
    }

    /**
     * @param type the type to set
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * @return the start
     */
    public long getStart() {
        return start;
    }

    /**
     * @param start the start to set
     */
    public void setStart(long start) {
        this.start = start;
    }

    /**
     * @return the end
     */
    public long getEnd() {
        return end;
    }

    /**
     * @param end the end to set
     */
    public void setEnd(long end) {
        this.end = end;
    }

    /**
     * @return the half
     */
    public int getHalf() {
        return half;
    }

    /**
     * @param half the half to set
     */
    public void setHalf(short half) {
        this.half = half;
    }

    /**
     * @return the idteam
     */
    public int getIdteam() {
        return idteam;
    }

    /**
     * @param idteam the idteam to set
     */
    public void setIdteam(short idteam) {
        this.idteam = idteam;
    }

    /**
     * @return the player
     */
    public String getPlayer() {
        return player;
    }

    /**
     * @param player the player to set
     */
    public void setPlayer(String player) {
        this.player = player;
    }

    /**
     * @return the note
     */
    public String getNote() {
        return note;
    }

    /**
     * @param note the note to set
     */
    public void setNote(String note) {
        this.note = note;
    }

    /**
     * @return the rilevante
     */
    public String getRilevante() {
        return rilevante;
    }

    /**
     * @param rilevante the rilevante to set
     */
    public void setRilevante(String rilevante) {
        this.rilevante = rilevante;
    }

    /**
     * @return the config
     */
    public String getConfig() {
        return config;
    }

    /**
     * @param config the config to set
     */
    public void setConfig(String config) {
        this.config = config;
    }

    /**
     * @return the user
     */
    public String getUser() {
        return user;
    }

    /**
     * @param user the user to set
     */
    public void setUser(String user) {
        this.user = user;
    }
//
//	/**
//	 * @return the role
//	 */
//	public String getRole() {
//		return role;
//	}
//
//	/**
//	 * @param role the role to set
//	 */
//	public void setRole(String role) {
//		this.role = role;
//	}
//
//	/**
//	 * @return the role_en
//	 */
//	public String getRole_en() {
//		return role_en;
//	}
//
//	/**
//	 * @param role_en the role_en to set
//	 */
//	public void setRole_en(String role_en) {
//		this.role_en = role_en;
//	}
//
//	/**
//	 * @return the role_id
//	 */

    public String getRole_id() {
        return role_id;
    }
//
//	/**
//	 * @param role_id the role_id to set
//	 */

    public void setRole_id(String role_id) {
        this.role_id = role_id;
    }
//
//	/**
//	 * @return the rev
//	 */
//	public int getRev() {
//		return rev;
//	}
//
//	/**
//	 * @param rev the rev to set
//	 */
//	public void setRev(short rev) {
//		this.rev = rev;
//	}

    /**
     * @return the button
     */
    public Button getButton() {
        return button;
    }

    /**
     * @param button the button to set
     */
    public void setButton(Button button) {
        this.button = button;
    }

    /**
     * @return the minutoInizio
     */
    public String getMinutoInizio() {
        return minutoInizio;
    }

    /**
     * @param minutoInizio the minutoInizio to set
     */
    public void setMinutoInizio(String minutoInizio) {
        this.minutoInizio = minutoInizio;
    }

    /**
     * @return the minutoFine
     */
    public String getMinutoFine() {
        return minutoFine;
    }

    /**
     * @param minutoFine the minutoFine to set
     */
    public void setMinutoFine(String minutoFine) {
        this.minutoFine = minutoFine;
    }

    /**
     * @return the nomeSquadra1
     */
    public String getNomeSquadra() {
        return nomeSquadra;
    }

    /**
     * @param nomeSquadra1 the nomeSquadra1 to set
     */
    public void setNomeSquadra(String nomeSquadra) {
        this.nomeSquadra = nomeSquadra;
    }

    /**
     * @return the durataAzione
     */
    public String getDurataAzione() {
        return durataAzione;
    }

    /**
     * @param durataAzione the durataAzione to set
     */
    public void setDurataAzione(String durataAzione) {
        this.durataAzione = durataAzione;
    }

    /**
     * @return the listTags
     */
    public List getListTags() {
        return listTags;
    }

    /**
     * @param listTags the listTags to set
     */
    public void setListTags(List listTags) {
        this.listTags = listTags;
    }

    public String halfTrim() {
        String tempoGioco = null;
        switch (half) {
            case 1:
                tempoGioco = "1t";
                break;
            case 2:
                tempoGioco = "2t";
                break;
            case 3:
                tempoGioco = "1ts";
                break;
            case 4:
                tempoGioco = "2ts";
                break;
        }
        return tempoGioco;
    }

    public String playerTrim() {
        String lower = player;
        try {
            lower = player.toLowerCase();
        } catch (Exception ex) {
        }

        return lower;
    }

    /**
     * @return the secStartAzione
     */
    public String getSecStartAzione() {
        return secStartAzione;
    }

    /**
     * @param secStartAzione the secondiAzione to set
     */
    public void setSecStartAzione(String secStartAzione) {
        this.secStartAzione = secStartAzione;
    }

    /**
     * @return the tempoAzioneVideo
     */
    public String getTempoAzioneVideo() {
        return tempoAzioneVideo;
    }

    /**
     * @param tempoAzioneVideo the tempoAzioneVideo to set
     */
    public void setTempoAzioneVideo(String tempoAzioneVideo) {
        this.tempoAzioneVideo = tempoAzioneVideo;
    }

    /**
     * @return the secEndAzione
     */
    public String getSecEndAzione() {
        return secEndAzione;
    }

    /**
     * @param setSecEndAzione the setSecEndAzione to set
     */
    public void setSecEndAzione(String secEndAzione) {
        this.secEndAzione = secEndAzione;
    }

    public String getVideoName() {
        return videoName;
    }

    public void setVideoName(String videoName) {
        this.videoName = videoName;
    }

    public Long getVideoId() {
        return videoId;
    }

    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }

    public Long getProvider() {
        return provider;
    }

    public void setProvider(Long provider) {
        this.provider = provider;
    }

    public String getPlayerTo() {
        return playerTo;
    }

    public void setPlayerTo(String playerTo) {
        this.playerTo = playerTo;
    }

    public Long getIdfixture() {
        return idfixture;
    }

    public void setIdfixture(Long idfixture) {
        this.idfixture = idfixture;
    }

    public String getPlayerId() {
        return playerId;
    }

    public void setPlayerId(String playerId) {
        this.playerId = playerId;
    }

    public String getPlayerToId() {
        return playerToId;
    }

    public void setPlayerToId(String playerToId) {
        this.playerToId = playerToId;
    }

    public String getDescr() {
        return descr;
    }

    public void setDescr(String descr) {
        this.descr = descr;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getTagCode() {
        return tagCode;
    }

    public void setTagCode(String tagCode) {
        this.tagCode = tagCode;
    }

    public Long getStartHalf() {
        return startHalf;
    }

    public void setStartHalf(Long startHalf) {
        this.startHalf = startHalf;
    }
}
