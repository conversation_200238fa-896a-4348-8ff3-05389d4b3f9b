package sics.domain;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Cache implements Cloneable {
    
    // dati giocatore
    private Long playerId;
    private String playerFirstName;
    private String playerLastName;
    private String playerKnownName;
    private String playerPhoto;
    private Date playerBornDate;
    private Integer playerAge;
    private Long playerCountryId;
    private String playerCountryName;
    private String playerCountryLogo;
    private Integer playerHeight;
    private Long playerFootId;
    private String playerFoot;
    private Long playerLastTeamId;
    private String playerLastTeamName;
    private String playerLastTeamLogo;
    private Integer playerLastTeamGenere;
    private Long playerPositionId;
    private String playerPosition;                  // portiere, difensore, centrocampista, attaccante
    private Long playerPositionDetailId;
    private String playerPositionDetail;
    private Long playerValue;
    private List<String> playerCompetitions;        // contiene la lista di competizioni usate per calcolare le statistiche
    private List<String> playerTeams;               // contiene la lista di team usati per calcolare le statistiche
    private String seasons;                         // contiene la coppia o la singola stagione usata per calcolare i dati
    
    // dati partita
    private Long fixtureId;
    private Long fixtureHomeTeamId;
    private String fixtureHomeTeam;
    private Long fixtureAwayTeamId;
    private String fixtureAwayTeam;
    private Integer fixtureMatchDay;
    
    // dati team
    private Long teamId;
    private String teamName;
    private String teamLogo;
    private Integer teamGenere;                     // 0 = maschile, 1 = femminile
    
    // dati competizione
    private Long competitionId;
    private String competitionName;
    private String competitionLogo;
    private Integer competitionGenere;              // 0 = maschile, 1 = femminile
    
    // dati statistiche
    private Long statsTypeId;
    private String statsTypeDescription;
    private Integer statsTypeDivider;
    private Long statsTypeAverageTypeId;
    private Double statsTypeAmount;
    private Map<Long, Statistic> playerStatistics;
    
    // dati partite giocate
    private Integer modulePosition;
    private Long substitute;
    private Long red;
    private Boolean nationalTeam;
    private Integer totalMatches;
    private Integer totalMatchesTitolare;
    private Integer totalMatchesSubentrato;
    private Integer totalMatchesSostituito;
    private Integer totalMatchesForNational;
    
    // temporary fields
    private Integer firstValue;
    private Integer secondValue;
    private Integer thirdValue;
    private Integer fourthValue;
    private Integer fifthValue;
    private Integer sixthValue;
    
    // datatable fields
    private Integer totalRecords;
    private Integer filteredRecords;                // nel caso in cui viene implementata il search il numero sarà diverso dal totalRecords

    @Override
    public Cache clone() throws CloneNotSupportedException {
        Cache clone = (Cache) super.clone();
        // per qualche motivo viene fatto il clone delle mappe ma non delle liste
        if (clone.playerCompetitions != null) {
            clone.playerCompetitions = new ArrayList<>(clone.playerCompetitions);
        }
        if (clone.playerTeams != null) {
            clone.playerTeams = new ArrayList<>(clone.playerTeams);
        }
        
        return clone;
    }
    
    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }
    
    public String getPlayerFirstName() {
        return playerFirstName;
    }

    public void setPlayerFirstName(String playerFirstName) {
        this.playerFirstName = playerFirstName;
    }

    public String getPlayerLastName() {
        return playerLastName;
    }

    public void setPlayerLastName(String playerLastName) {
        this.playerLastName = playerLastName;
    }

    public String getPlayerKnownName() {
        return playerKnownName;
    }

    public void setPlayerKnownName(String playerKnownName) {
        this.playerKnownName = playerKnownName;
    }

    public String getPlayerPhoto() {
        return playerPhoto;
    }

    public void setPlayerPhoto(String playerPhoto) {
        this.playerPhoto = playerPhoto;
    }

    public Date getPlayerBornDate() {
        return playerBornDate;
    }

    public void setPlayerBornDate(Date playerBornDate) {
        this.playerBornDate = playerBornDate;
        
        // quando carico la data di nascita scrivo anche l'età
        if (this.playerBornDate != null) {
            int bornYear = this.playerBornDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getYear();
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);
            setPlayerAge(currentYear - bornYear);
        }
    }

    public Integer getPlayerAge() {
        return playerAge;
    }

    public void setPlayerAge(Integer playerAge) {
        this.playerAge = playerAge;
    }

    public Long getPlayerCountryId() {
        return playerCountryId;
    }

    public void setPlayerCountryId(Long playerCountryId) {
        this.playerCountryId = playerCountryId;
    }

    public String getPlayerCountryName() {
        return playerCountryName;
    }

    public void setPlayerCountryName(String playerCountryName) {
        this.playerCountryName = playerCountryName;
    }

    public String getPlayerCountryLogo() {
        return playerCountryLogo;
    }

    public void setPlayerCountryLogo(String playerCountryLogo) {
        this.playerCountryLogo = playerCountryLogo;
    }

    public Integer getPlayerHeight() {
        return playerHeight;
    }

    public void setPlayerHeight(Integer playerHeight) {
        this.playerHeight = playerHeight;
    }

    public Long getPlayerFootId() {
        return playerFootId;
    }

    public void setPlayerFootId(Long playerFootId) {
        this.playerFootId = playerFootId;
    }

    public String getPlayerFoot() {
        return playerFoot;
    }

    public void setPlayerFoot(String playerFoot) {
        this.playerFoot = playerFoot;
    }

    public Long getPlayerLastTeamId() {
        return playerLastTeamId;
    }

    public void setPlayerLastTeamId(Long playerLastTeamId) {
        this.playerLastTeamId = playerLastTeamId;
    }

    public String getPlayerLastTeamName() {
        return playerLastTeamName;
    }

    public void setPlayerLastTeamName(String playerLastTeamName) {
        this.playerLastTeamName = playerLastTeamName;
    }

    public String getPlayerLastTeamLogo() {
        return playerLastTeamLogo;
    }

    public void setPlayerLastTeamLogo(String playerLastTeamLogo) {
        this.playerLastTeamLogo = playerLastTeamLogo;
    }

    public Integer getPlayerLastTeamGenere() {
        return playerLastTeamGenere;
    }

    public void setPlayerLastTeamGenere(Integer playerLastTeamGenere) {
        this.playerLastTeamGenere = playerLastTeamGenere;
    }

    public Long getPlayerPositionId() {
        return playerPositionId;
    }

    public void setPlayerPositionId(Long playerPositionId) {
        this.playerPositionId = playerPositionId;
    }
    
    public String getPlayerPosition() {
        return playerPosition;
    }

    public void setPlayerPosition(String playerPosition) {
        this.playerPosition = playerPosition;
    }

    public Long getPlayerPositionDetailId() {
        return playerPositionDetailId;
    }

    public void setPlayerPositionDetailId(Long playerPositionDetailId) {
        this.playerPositionDetailId = playerPositionDetailId;
    }

    public String getPlayerPositionDetail() {
        return playerPositionDetail;
    }

    public void setPlayerPositionDetail(String playerPositionDetail) {
        this.playerPositionDetail = playerPositionDetail;
    }

    public Long getPlayerValue() {
        return playerValue;
    }

    public void setPlayerValue(Long playerValue) {
        this.playerValue = playerValue;
    }

    public List<String> getPlayerCompetitions() {
        return playerCompetitions;
    }

    public void setPlayerCompetitions(List<String> playerCompetitions) {
        this.playerCompetitions = playerCompetitions;
    }

    public List<String> getPlayerTeams() {
        return playerTeams;
    }

    public void setPlayerTeams(List<String> playerTeams) {
        this.playerTeams = playerTeams;
    }

    public String getSeasons() {
        return seasons;
    }

    public void setSeasons(String seasons) {
        this.seasons = seasons;
    }

    public Long getFixtureId() {
        return fixtureId;
    }

    public void setFixtureId(Long fixtureId) {
        this.fixtureId = fixtureId;
    }

    public Long getFixtureHomeTeamId() {
        return fixtureHomeTeamId;
    }

    public void setFixtureHomeTeamId(Long fixtureHomeTeamId) {
        this.fixtureHomeTeamId = fixtureHomeTeamId;
    }

    public String getFixtureHomeTeam() {
        return fixtureHomeTeam;
    }

    public void setFixtureHomeTeam(String fixtureHomeTeam) {
        this.fixtureHomeTeam = fixtureHomeTeam;
    }

    public Long getFixtureAwayTeamId() {
        return fixtureAwayTeamId;
    }

    public void setFixtureAwayTeamId(Long fixtureAwayTeamId) {
        this.fixtureAwayTeamId = fixtureAwayTeamId;
    }
    
    public String getFixtureAwayTeam() {
        return fixtureAwayTeam;
    }

    public void setFixtureAwayTeam(String fixtureAwayTeam) {
        this.fixtureAwayTeam = fixtureAwayTeam;
    }

    public Integer getFixtureMatchDay() {
        return fixtureMatchDay;
    }

    public void setFixtureMatchDay(Integer fixtureMatchDay) {
        this.fixtureMatchDay = fixtureMatchDay;
    }
    
    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getTeamLogo() {
        return teamLogo;
    }

    public void setTeamLogo(String teamLogo) {
        this.teamLogo = teamLogo;
    }

    public Integer getTeamGenere() {
        return teamGenere;
    }

    public void setTeamGenere(Integer teamGenere) {
        this.teamGenere = teamGenere;
    }

    public Long getCompetitionId() {
        return competitionId;
    }

    public void setCompetitionId(Long competitionId) {
        this.competitionId = competitionId;
    }

    public String getCompetitionName() {
        return competitionName;
    }

    public void setCompetitionName(String competitionName) {
        this.competitionName = competitionName;
    }

    public String getCompetitionLogo() {
        return competitionLogo;
    }

    public void setCompetitionLogo(String competitionLogo) {
        this.competitionLogo = competitionLogo;
    }

    public Integer getCompetitionGenere() {
        return competitionGenere;
    }

    public void setCompetitionGenere(Integer competitionGenere) {
        this.competitionGenere = competitionGenere;
    }

    public Long getStatsTypeId() {
        return statsTypeId;
    }

    public void setStatsTypeId(Long statsTypeId) {
        this.statsTypeId = statsTypeId;
    }

    public String getStatsTypeDescription() {
        return statsTypeDescription;
    }

    public void setStatsTypeDescription(String statsTypeDescription) {
        this.statsTypeDescription = statsTypeDescription;
    }

    public Integer getStatsTypeDivider() {
        return statsTypeDivider;
    }

    public void setStatsTypeDivider(Integer statsTypeDivider) {
        this.statsTypeDivider = statsTypeDivider;
    }

    public Long getStatsTypeAverageTypeId() {
        return statsTypeAverageTypeId;
    }

    public void setStatsTypeAverageTypeId(Long statsTypeAverageTypeId) {
        this.statsTypeAverageTypeId = statsTypeAverageTypeId;
    }
    
    public Double getStatsTypeAmount() {
        return statsTypeAmount;
    }

    public void setStatsTypeAmount(Double statsTypeAmount) {
        // uso questa funzione anche per sommare le righe, quindi divido per il divisore
        // solo quando statsTypeAmount non è inizializzato
        if (this.statsTypeDivider != null && this.statsTypeAmount == null) {
            this.statsTypeAmount = 1D * statsTypeAmount / this.statsTypeDivider;
        } else {
            this.statsTypeAmount = statsTypeAmount;
        }
    }

    public Map<Long, Statistic> getPlayerStatistics() {
        return playerStatistics;
    }

    public void setPlayerStatistics(Map<Long, Statistic> playerStatistics) {
        this.playerStatistics = playerStatistics;
    }

    public Integer getModulePosition() {
        return modulePosition;
    }

    public void setModulePosition(Integer modulePosition) {
        this.modulePosition = modulePosition;
    }

    public Long getSubstitute() {
        return substitute;
    }

    public void setSubstitute(Long substitute) {
        this.substitute = substitute;
    }

    public Long getRed() {
        return red;
    }

    public void setRed(Long red) {
        this.red = red;
    }

    public Boolean getNationalTeam() {
        return nationalTeam;
    }

    public void setNationalTeam(Boolean nationalTeam) {
        this.nationalTeam = nationalTeam;
    }

    public Integer getTotalMatches() {
        return totalMatches;
    }

    public void setTotalMatches(Integer totalMatches) {
        this.totalMatches = totalMatches;
    }

    public Integer getTotalMatchesTitolare() {
        return totalMatchesTitolare;
    }

    public void setTotalMatchesTitolare(Integer totalMatchesTitolare) {
        this.totalMatchesTitolare = totalMatchesTitolare;
    }

    public Integer getTotalMatchesSubentrato() {
        return totalMatchesSubentrato;
    }

    public void setTotalMatchesSubentrato(Integer totalMatchesSubentrato) {
        this.totalMatchesSubentrato = totalMatchesSubentrato;
    }

    public Integer getTotalMatchesSostituito() {
        return totalMatchesSostituito;
    }

    public void setTotalMatchesSostituito(Integer totalMatchesSostituito) {
        this.totalMatchesSostituito = totalMatchesSostituito;
    }

    public Integer getTotalMatchesForNational() {
        return totalMatchesForNational;
    }

    public void setTotalMatchesForNational(Integer totalMatchesForNational) {
        this.totalMatchesForNational = totalMatchesForNational;
    }
    
    public Integer getFirstValue() {
        return firstValue;
    }

    public void setFirstValue(Integer firstValue) {
        this.firstValue = firstValue;
    }

    public Integer getSecondValue() {
        return secondValue;
    }

    public void setSecondValue(Integer secondValue) {
        this.secondValue = secondValue;
    }

    public Integer getThirdValue() {
        return thirdValue;
    }

    public void setThirdValue(Integer thirdValue) {
        this.thirdValue = thirdValue;
    }

    public Integer getFourthValue() {
        return fourthValue;
    }

    public void setFourthValue(Integer fourthValue) {
        this.fourthValue = fourthValue;
    }

    public Integer getFifthValue() {
        return fifthValue;
    }

    public void setFifthValue(Integer fifthValue) {
        this.fifthValue = fifthValue;
    }

    public Integer getSixthValue() {
        return sixthValue;
    }

    public void setSixthValue(Integer sixthValue) {
        this.sixthValue = sixthValue;
    }

    public Integer getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(Integer totalRecords) {
        this.totalRecords = totalRecords;
    }

    public Integer getFilteredRecords() {
        return filteredRecords;
    }

    public void setFilteredRecords(Integer filteredRecords) {
        this.filteredRecords = filteredRecords;
    }
}
