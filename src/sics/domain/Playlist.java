package sics.domain;

import java.io.*;
import java.util.Date;
import sics.helper.DateHelper;
import sics.helper.GlobalHelper;

public class Playlist implements Serializable {

    protected boolean canDelete = false;
    private Long id = -1l;
    private String name;
    private Long videoId;
    private Date date;
    private java.sql.Timestamp dateSQL;
    private Long medialength;
    protected String note = "";
    private String noteArb;
    private String noteAa1;
    private String noteAa2;
    private String noteAa4;
    private Long userId;
    private Long userUploadId;
    private String uploadedBy;
    private Long groupsetId;
    private Long providerId;
    private String videoName;
    private String videoPathS3 = "";
    private String userFirstName;
    private String userLastName;
    private String uploadFirstName;
    private String uploadLastName;
    private String groupsetName;
    private String videoPublic;
    private String type_file;
    private boolean seen;
    private long sizeVideo;

    public Playlist() {
    }

    /**
     * @return the id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return the name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name the name to set
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return the videoId
     */
    public Long getVideoId() {
        return videoId;
    }

    /**
     * @param video the video to set
     */
    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }

    /**
     * @return the date
     */
    public Date getDate() {
        return date;
    }

    /**
     * @param date the date to set
     */
    public void setDate(Date date) {
        this.date = date;
        this.dateSQL = getDateSQL();
    }

    public Long getMedialength() {
        return medialength;
    }

    public void setMedialength(Long medialength) {
        this.medialength = medialength;
    }

    /**
     * @return the noteArb
     */
    public String getNoteArb() {
        return noteArb;
    }

    /**
     * @param noteArb the noteArb to set
     */
    public void setNoteArb(String noteArb) {
        this.noteArb = noteArb;
    }

    /**
     * @return the noteAa1
     */
    public String getNoteAa1() {
        return noteAa1;
    }

    /**
     * @param noteAa1 the noteAa1 to set
     */
    public void setNoteAa1(String noteAa1) {
        this.noteAa1 = noteAa1;
    }

    /**
     * @return the noteAa2
     */
    public String getNoteAa2() {
        return noteAa2;
    }

    /**
     * @param noteAa2 the noteAa2 to set
     */
    public void setNoteAa2(String noteAa2) {
        this.noteAa2 = noteAa2;
    }

    /**
     * @return the noteAa4
     */
    public String getNoteAa4() {
        return noteAa4;
    }

    /**
     * @param noteAa4 the noteAa4 to set
     */
    public void setNoteAa4(String noteAa4) {
        this.noteAa4 = noteAa4;
    }

    /**
     * @return the userId
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * @param userId the userId to set
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * @return the groupsetId
     */
    public Long getGroupsetId() {
        return groupsetId;
    }

    /**
     * @param groupsetId the groupsetId to set
     */
    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    /**
     * @return the providerId
     */
    public Long getProviderId() {
        return providerId;
    }

    /**
     * @param providerId the providerId to set
     */
    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }

    /**
     * @return the videoName
     */
    public String getVideoName() {
        return videoName;
    }

    /**
     * @param videoName the videoName to set
     */
    public void setVideoName(String videoName) {
        this.videoName = videoName;
    }

    public String getVideoPathS3() {
        if (videoPathS3 == null || videoPathS3.isEmpty()) {
            //videoPathS3 = GlobalHelper.pathS3(videoName, "video");
            // DAFARE 2021-01-29 imposto sempre cloudFront per "video"
            videoPathS3 = GlobalHelper.pathS3(videoName/*"LR VICENZA VIRTUS-CITTADELLA-02042021.mp4"*/, "playlist", true);
        }
        return videoPathS3/*="https://s3-eu-west-1.amazonaws.com/it.sics.test/LR VICENZA VIRTUS-CITTADELLA-02042021.mp4"*/;
    }

    public java.sql.Timestamp getDateSQL() {
        if (dateSQL == null) {
            dateSQL = new java.sql.Timestamp(date.getTime());
        }
        return dateSQL;
    }

    public String getDateString() {
        return (getDate() != null) ? DateHelper.toString(getDate()) : "";
    }

    public String getDateFullString() {
        return (getDate() != null) ? DateHelper.toStringExt(getDate()) : "";
    }

    public String getDateMonthString() {
        return (getDate() != null) ? DateHelper.toMonthExt(getDate()) : "";
    }

    public String getDateDayString() {
        return (getDate() != null) ? DateHelper.toDayExt(getDate()) : "";
    }

    public String getDateYearString() {
        return (getDate() != null) ? DateHelper.toYearExt(getDate()) : "";
    }

    public String getTimeString() {
        return (getDate() != null) ? DateHelper.toStringTime(getDate()) : "";
    }

    public Long getUserUploadId() {
        return userUploadId;
    }

    public void setUserUploadId(Long userUploadId) {
        this.userUploadId = userUploadId;
    }

    public String getUploadedBy() {
        return uploadedBy;
    }

    public void setUploadedBy(String uploadedBy) {
        this.uploadedBy = uploadedBy;
    }

    public String getUserFirstName() {
        return userFirstName;
    }

    public void setUserFirstName(String userFirstName) {
        this.userFirstName = userFirstName;
    }

    public String getUserLastName() {
        return userLastName;
    }

    public void setUserLastName(String userLastName) {
        this.userLastName = userLastName;
    }

    public String getUploadFirstName() {
        return uploadFirstName;
    }

    public void setUploadFirstName(String uploadFirstName) {
        this.uploadFirstName = uploadFirstName;
    }

    public String getUploadLastName() {
        return uploadLastName;
    }

    public void setUploadLastName(String uploadLastName) {
        this.uploadLastName = uploadLastName;
    }

    public String getGroupsetName() {
        return groupsetName;
    }

    public void setGroupsetName(String groupsetName) {
        this.groupsetName = groupsetName;
    }

    public String getVideoPublic() {
        return videoPublic;
    }

    public void setVideoPublic(String videoPublic) {
        this.videoPublic = videoPublic;
    }

    public String getType_file() {
        return type_file;
    }

    public void setType_file(String type_file) {
        this.type_file = type_file;
    }

    public boolean isSeen() {
        return seen;
    }

    public void setSeen(boolean seen) {
        this.seen = seen;
    }

    public boolean isZip() {
        return type_file.equals("zip");
    }

    /**
     * @return the canDelete
     */
    public boolean isCanDelete() {
        return canDelete;
    }

    /**
     * @param canDelete the canDelete to set
     */
    public void setCanDelete(boolean canDelete) {
        this.canDelete = canDelete;
    }

    /**
     * @return the note
     */
    public String getNote() {
        return note;
    }

    /**
     * @param note the note to set
     */
    public void setNote(String note) {
        this.note = note;
    }

    public String getThumbName() {
        return videoName.replace(".mp4", ".png");
    }

    /**
     * @return the sizeVideo
     */
    public long getSizeVideo() {
        return sizeVideo;
    }

    /**
     * @param sizeVideo the sizeVideo to set
     */
    public void setSizeVideo(long sizeVideo) {
        this.sizeVideo = sizeVideo;
    }
    
    public void setSizeVideoFromKb(long sizeVideo) {
        if (sizeVideo > 0 && sizeVideo <= 1024) {
            this.sizeVideo = 1;
        } else {
            this.sizeVideo = Math.round(sizeVideo / 1024);
        }
    }
}
