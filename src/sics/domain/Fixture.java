/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package sics.domain;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class Fixture implements Serializable {

	private long id;
	private long provider_id;
	private long video_name;

	/**
	 * @return the id
	 */
	public long getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(long id) {
		this.id = id;
	}

	/**
	 * @return the provider_id
	 */
	public long getProvider_id() {
		return provider_id;
	}

	/**
	 * @param provider_id the provider_id to set
	 */
	public void setProvider_id(long provider_id) {
		this.provider_id = provider_id;
	}

	/**
	 * @return the video_name
	 */
	public long getVideo_name() {
		return video_name;
	}

	/**
	 * @param video_name the video_name to set
	 */
	public void setVideo_name(long video_name) {
		this.video_name = video_name;
	}
}
