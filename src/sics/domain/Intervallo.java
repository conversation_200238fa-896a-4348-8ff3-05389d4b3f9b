/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.domain;

/**
 *
 * <AUTHOR>
 */
public class Intervallo {
	private String code;
	private String desc;
	private Integer typeIntervallo;
	
	public Intervallo(){}

	public Intervallo(String code, String desc, Integer typeIntervallo) {
		this.code = code;
		this.desc = desc;
		this.typeIntervallo = typeIntervallo;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getDesc() {
		return desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public Integer getTypeIntervallo() {
		return typeIntervallo;
	}

	public void setTypeIntervallo(Integer typeIntervallo) {
		this.typeIntervallo = typeIntervallo;
	}
	
}
