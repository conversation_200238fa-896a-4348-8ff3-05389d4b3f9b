package sics.domain;

import com.google.gson.Gson;

/**
 *
 * <AUTHOR>
 */
public class ShadowTeamDetail {

    private Long id;
    private Long shadowTeamId;
    private Long playerPersonalId;
    private Integer modulePosition;
    private String modulePositionCoords;
    private String modulePositionColor;
    private Integer sort;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShadowTeamId() {
        return shadowTeamId;
    }

    public void setShadowTeamId(Long shadowTeamId) {
        this.shadowTeamId = shadowTeamId;
    }

    public Long getPlayerPersonalId() {
        return playerPersonalId;
    }

    public void setPlayerPersonalId(Long playerPersonalId) {
        this.playerPersonalId = playerPersonalId;
    }

    public Integer getModulePosition() {
        return modulePosition;
    }

    public void setModulePosition(Integer modulePosition) {
        this.modulePosition = modulePosition;
    }

    public String getModulePositionCoords() {
        return modulePositionCoords;
    }

    public void setModulePositionCoords(String modulePositionCoords) {
        this.modulePositionCoords = modulePositionCoords;
    }

    public String getModulePositionColor() {
        return modulePositionColor;
    }

    public void setModulePositionColor(String modulePositionColor) {
        this.modulePositionColor = modulePositionColor;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
    
    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
