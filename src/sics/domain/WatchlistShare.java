package sics.domain;

/**
 *
 * <AUTHOR>
 */
public class WatchlistShare {
    
    private Long id;
    private Long userId;
    private Long watchlistId;
    private Boolean editable;

    // extra fields
    private User user; // usato per caricare la lista degli utenti a cui ho già condiviso la playlist ( mi serve nome e email )
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getWatchlistId() {
        return watchlistId;
    }

    public void setWatchlistId(Long watchlistId) {
        this.watchlistId = watchlistId;
    }

    public Boolean getEditable() {
        return editable;
    }

    public void setEditable(Boolean editable) {
        this.editable = editable;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}
