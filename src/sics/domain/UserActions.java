package sics.domain;

/**
 *
 * <AUTHOR>
 */
public class UserActions {

    private Integer videoStream = 0;
    private Integer loginCorrect = 0, loginError = 0, licenseExpired = 0;
    private Integer playlistTv = 0;
    private Integer videoDownload = 0;
    private Integer watchlist = 0;
    private Integer downloadXmlJson = 0;
    private Integer export = 0;

    private Integer total = 0;

    public Integer getVideoStream() {
        return videoStream;
    }

    public void setVideoStream(Integer videoStream) {
        this.videoStream = videoStream;
    }

    public Integer getLoginCorrect() {
        return loginCorrect;
    }

    public void setLoginCorrect(Integer loginCorrect) {
        this.loginCorrect = loginCorrect;
    }

    public Integer getLoginError() {
        return loginError;
    }

    public void setLoginError(Integer loginError) {
        this.loginError = loginError;
    }

    public Integer getLicenseExpired() {
        return licenseExpired;
    }

    public void setLicenseExpired(Integer licenseExpired) {
        this.licenseExpired = licenseExpired;
    }

    public Integer getPlaylistTv() {
        return playlistTv;
    }

    public void setPlaylistTv(Integer playlistTv) {
        this.playlistTv = playlistTv;
    }

    public Integer getVideoDownload() {
        return videoDownload;
    }

    public void setVideoDownload(Integer videoDownload) {
        this.videoDownload = videoDownload;
    }

    public Integer getWatchlist() {
        return watchlist;
    }

    public void setWatchlist(Integer watchlist) {
        this.watchlist = watchlist;
    }

    public Integer getDownloadXmlJson() {
        return downloadXmlJson;
    }

    public void setDownloadXmlJson(Integer downloadXmlJson) {
        this.downloadXmlJson = downloadXmlJson;
    }

    public Integer getExport() {
        return export;
    }

    public void setExport(Integer export) {
        this.export = export;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }
}
