/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.domain;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import sics.model.Azione;

/**
 *
 * <AUTHOR> 30
 */
public class FilterGameWrapper {
    private Map<Long, Game> games;
    private Map<Long, ArrayList<Azione>> resultActions;

    public FilterGameWrapper() {
        games = new HashMap<>();
        resultActions = new HashMap<>();
    }

    public FilterGameWrapper(Map<Long, Game> games, Map<Long, ArrayList<Azione>> resultActions) {
        this.games = games;
        this.resultActions = resultActions;
    }

    public Map<Long, Game> getGames() {
        return games;
    }

    public Map<Long, ArrayList<Azione>> getResultActions() {
        return resultActions;
    }

    public void setGames(Map<Long, Game> games) {
        this.games = games;
    }

    public void setResultActions(Map<Long, ArrayList<Azione>> resultActions) {
        this.resultActions = resultActions;
    }
    
    
}
