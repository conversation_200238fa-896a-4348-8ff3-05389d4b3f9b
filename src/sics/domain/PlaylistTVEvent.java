package sics.domain;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 *
 * <AUTHOR>
 */
public class PlaylistTVEvent implements Serializable {
    
    private Long id;
    private Long playlistId;
    private Long eventId;
    private String event_type_code;
    private Long event_period_start_msec;
    private Long event_period_end_msec;
    private Integer event_period_start_second;
    private Integer event_period_end_second;
    private Integer event_period_start_minute;
    private Integer event_period_end_minute;
    private Integer event_period_id;
    private String event_note;
    private String event_xml_idevent;
    private Long event_fixture_id;
    private Long event_tactical_start_msec;
    private String event_groupset_id;
    private Integer event_team_id;
    private String event_result;
    private String event_author;
    private String event_type_desc;
    private String event_type_desc_en;
    private String event_type_desc_fr;
    private String event_type_desc_es;
    private String event_type_desc_ru;
    private String tag_type_code;
    private String tag_type_desc;
    private String tag_type_desc_en;
    private String tag_type_desc_fr;
    private String tag_type_desc_es;
    private String tag_type_desc_ru;
    private String team_name;
    private String player_know_name;
    private String panel_name;
    private Boolean isTactical;
    private Long order;
    private Boolean deleted;
    private Long updateBy;
    private Timestamp updateDate;
    private String updateDescription;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPlaylistId() {
        return playlistId;
    }

    public void setPlaylistId(Long playlistId) {
        this.playlistId = playlistId;
    }

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getEvent_type_code() {
        return event_type_code;
    }

    public void setEvent_type_code(String event_type_code) {
        this.event_type_code = event_type_code;
    }

    public Long getEvent_period_start_msec() {
        return event_period_start_msec;
    }

    public void setEvent_period_start_msec(Long event_period_start_msec) {
        this.event_period_start_msec = event_period_start_msec;
    }

    public Long getEvent_period_end_msec() {
        return event_period_end_msec;
    }

    public void setEvent_period_end_msec(Long event_period_end_msec) {
        this.event_period_end_msec = event_period_end_msec;
    }

    public Integer getEvent_period_start_second() {
        return event_period_start_second;
    }

    public void setEvent_period_start_second(Integer event_period_start_second) {
        this.event_period_start_second = event_period_start_second;
    }

    public Integer getEvent_period_end_second() {
        return event_period_end_second;
    }

    public void setEvent_period_end_second(Integer event_period_end_second) {
        this.event_period_end_second = event_period_end_second;
    }

    public Integer getEvent_period_start_minute() {
        return event_period_start_minute;
    }

    public void setEvent_period_start_minute(Integer event_period_start_minute) {
        this.event_period_start_minute = event_period_start_minute;
    }

    public Integer getEvent_period_end_minute() {
        return event_period_end_minute;
    }

    public void setEvent_period_end_minute(Integer event_period_end_minute) {
        this.event_period_end_minute = event_period_end_minute;
    }

    public Integer getEvent_period_id() {
        return event_period_id;
    }

    public void setEvent_period_id(Integer event_period_id) {
        this.event_period_id = event_period_id;
    }

    public String getEvent_note() {
        return event_note;
    }

    public void setEvent_note(String event_note) {
        this.event_note = event_note;
    }

    public String getEvent_xml_idevent() {
        return event_xml_idevent;
    }

    public void setEvent_xml_idevent(String event_xml_idevent) {
        this.event_xml_idevent = event_xml_idevent;
    }

    public Long getEvent_fixture_id() {
        return event_fixture_id;
    }

    public void setEvent_fixture_id(Long event_fixture_id) {
        this.event_fixture_id = event_fixture_id;
    }

    public Long getEvent_tactical_start_msec() {
        return event_tactical_start_msec;
    }

    public void setEvent_tactical_start_msec(Long event_tactical_start_msec) {
        this.event_tactical_start_msec = event_tactical_start_msec;
    }

    public String getEvent_groupset_id() {
        return event_groupset_id;
    }

    public void setEvent_groupset_id(String event_groupset_id) {
        this.event_groupset_id = event_groupset_id;
    }

    public Integer getEvent_team_id() {
        return event_team_id;
    }

    public void setEvent_team_id(Integer event_team_id) {
        this.event_team_id = event_team_id;
    }

    public String getEvent_result() {
        return event_result;
    }

    public void setEvent_result(String event_result) {
        this.event_result = event_result;
    }

    public String getEvent_author() {
        return event_author;
    }

    public void setEvent_author(String event_author) {
        this.event_author = event_author;
    }

    public String getEvent_type_desc() {
        return event_type_desc;
    }

    public void setEvent_type_desc(String event_type_desc) {
        this.event_type_desc = event_type_desc;
    }

    public String getEvent_type_desc_en() {
        return event_type_desc_en;
    }

    public void setEvent_type_desc_en(String event_type_desc_en) {
        this.event_type_desc_en = event_type_desc_en;
    }

    public String getEvent_type_desc_fr() {
        return event_type_desc_fr;
    }

    public void setEvent_type_desc_fr(String event_type_desc_fr) {
        this.event_type_desc_fr = event_type_desc_fr;
    }

    public String getEvent_type_desc_es() {
        return event_type_desc_es;
    }

    public void setEvent_type_desc_es(String event_type_desc_es) {
        this.event_type_desc_es = event_type_desc_es;
    }

    public String getEvent_type_desc_ru() {
        return event_type_desc_ru;
    }

    public void setEvent_type_desc_ru(String event_type_desc_ru) {
        this.event_type_desc_ru = event_type_desc_ru;
    }

    public String getTag_type_code() {
        return tag_type_code;
    }

    public void setTag_type_code(String tag_type_code) {
        this.tag_type_code = tag_type_code;
    }

    public String getTag_type_desc() {
        return tag_type_desc;
    }

    public void setTag_type_desc(String tag_type_desc) {
        this.tag_type_desc = tag_type_desc;
    }

    public String getTag_type_desc_en() {
        return tag_type_desc_en;
    }

    public void setTag_type_desc_en(String tag_type_desc_en) {
        this.tag_type_desc_en = tag_type_desc_en;
    }

    public String getTag_type_desc_fr() {
        return tag_type_desc_fr;
    }

    public void setTag_type_desc_fr(String tag_type_desc_fr) {
        this.tag_type_desc_fr = tag_type_desc_fr;
    }

    public String getTag_type_desc_es() {
        return tag_type_desc_es;
    }

    public void setTag_type_desc_es(String tag_type_desc_es) {
        this.tag_type_desc_es = tag_type_desc_es;
    }

    public String getTag_type_desc_ru() {
        return tag_type_desc_ru;
    }

    public void setTag_type_desc_ru(String tag_type_desc_ru) {
        this.tag_type_desc_ru = tag_type_desc_ru;
    }

    public String getTeam_name() {
        return team_name;
    }

    public void setTeam_name(String team_name) {
        this.team_name = team_name;
    }

    public String getPlayer_know_name() {
        return player_know_name;
    }

    public void setPlayer_know_name(String player_know_name) {
        this.player_know_name = player_know_name;
    }

    public String getPanel_name() {
        return panel_name;
    }

    public void setPanel_name(String panel_name) {
        this.panel_name = panel_name;
    }

    public Boolean getIsTactical() {
        return isTactical;
    }

    public void setIsTactical(Boolean isTactical) {
        this.isTactical = isTactical;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Timestamp getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateDescription() {
        return updateDescription;
    }

    public void setUpdateDescription(String updateDescription) {
        this.updateDescription = updateDescription;
    }
}
