package sics.domain;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang.StringUtils;
import sics.helper.GlobalHelper;

public class User implements Serializable {

    private Long id;
    private String username;
    private String firstName;
    private String lastName;
    private String role;
    private String email;
    private String password;
    private Long groupsetId;
    private Long groupsetTeamId;
    private String groupsetName;
    private Long maxDownload;
    private Long savedSeason;
    private String levels;
    private Long savedSource;
    private Boolean isShare;
    private Long sportId;
    protected Long totalSpace;
    protected Boolean guest;
    //Numero download singolo utente
    private Long numDownload;
    private Long tvLimitAPI;
    private String language;
    private String tvLanguage;          // questa arriva da user_settings

    // Extra fields, usati per Manager
    private Long totalLogin;
    private Long totalAction;
    private Date lastLogin;
    private String vtigerOrganization;
    
    //Numero streaming singolo utente
    private Long numStreaming;

    private Date datascadenza;
    private String sn_sicstv;

    private List<Competition> availableCompetition;
    private List<Team> availableTeam;
    private List<UserCompetitionException> competitionException;
    private HashMap<String, EventDescription> mapUserPanels = null;

    private Long playerId;          // indica se l'user
    
    private Boolean multi_login;

    public User() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, 1970);

        datascadenza = c.getTime();
    }

    /**
     * @return the id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return the username
     */
    public String getUsername() {
        return username;
    }

    /**
     * @param username the username to set
     */
    public void setUsername(String username) {
        this.username = username;
    }

    /**
     * @return the firstName
     */
    public String getFirstName() {
        return firstName;
    }

    /**
     * @param firstName the firstName to set
     */
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    /**
     * @return the lastName
     */
    public String getLastName() {
        return lastName;
    }

    /**
     * @param lastName the lastName to set
     */
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    /**
     * @return the role
     */
    public String getRole() {
        return role;
    }

    /**
     * @param role the role to set
     */
    public void setRole(String role) {
        this.role = role;
    }

    /**
     * @return the email
     */
    public String getEmail() {
        return email;
    }

    /**
     * @param email the email to set
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * @return the password
     */
    public String getPassword() {
        return password;
    }

    /**
     * @param password the password to set
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * @return the groupsetId
     */
    public Long getGroupsetId() {
        return groupsetId;
    }

    /**
     * @param groupsetId the groupsetId to set
     */
    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    /**
     * @return the groupsetName
     */
    public String getGroupsetName() {
        return groupsetName;
    }

    /**
     * @param groupsetName the groupsetName to set
     */
    public void setGroupsetName(String groupsetName) {
        this.groupsetName = groupsetName;
    }

    /**
     * @return the NumDownload
     */
    public Long getnumDownload() {
        return numDownload;
    }

    /**
     * @param NumDownload the NumDownload to set
     */
    public void setnumDownload(Long numDownload) {
        this.numDownload = numDownload;
    }

    /**
     * @return the numStreaming
     */
    public Long getNumStreaming() {
        return numStreaming;
    }

    /**
     * @param numStreaming the numStreaming to set
     */
    public void setNumStreaming(Long numStreaming) {
        this.numStreaming = numStreaming;
    }

    /**
     * @return the maxDownload
     */
    public Long getMaxDownload() {
        return maxDownload;
    }

    /**
     * @param maxDownload the maxDownload to set
     */
    public void setMaxDownload(Long maxDownload) {
        this.maxDownload = maxDownload;
    }

    public Long getSavedSeason() {
        return savedSeason;
    }
    
    public String getSavedSeasonForQuery() {
        if (savedSeason == null) return "";
        
        if (savedSeason > 2000) {   // 2023 -> 2023,23,24
            // return savedSeason + "," + (savedSeason - 2000) + "," + (savedSeason - 1999);
            return savedSeason + "," + (savedSeason - 2000);
        } else {                    // 23   -> 23,2023
            return savedSeason + "," + (savedSeason + 2000);
        }
    }

    public void setSavedSeason(Long season) {
        this.savedSeason = season;
    }

    public Long getGroupsetTeamId() {
        return groupsetTeamId;
    }

    public void setGroupsetTeamId(Long groupsetTeamId) {
        this.groupsetTeamId = groupsetTeamId;
    }

    public boolean isAia() {
        return (groupsetId.equals(Long.valueOf("3"))) ? true : false;
    }

    public Date getDatascadenza() {
        return datascadenza;
    }

    public void setDatascadenza(Date datascadenza) {
        this.datascadenza = datascadenza;
    }

    public String getDatascadenzaString() {
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
        return formatter.format(datascadenza);
    }
    
    public boolean getAccessoScaduto() {
        if (this.datascadenza == null) return true;
        
        return this.datascadenza.before(new Date());
    }

    public String getSn_sicstv() {
        return sn_sicstv;
    }

    public void setSn_sicstv(String sn_sicstv) {
        this.sn_sicstv = sn_sicstv;
    }

    public boolean isExpired() {
        try {
            Date today = new Date();
            return datascadenza.before(today);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return false;
    }

    public boolean expireIn7Days() {
        try {
            Date today = new Date();
            long startTime = today.getTime();
            long endTime = datascadenza.getTime();
            long diffTime = endTime - startTime;
            long diffDays = diffTime / (1000 * 60 * 60 * 24);
            return diffDays <= 7;
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return false;
    }

    public void setAvailableCompetition(List<Competition> availableCompetition) {
        this.availableCompetition = availableCompetition;
    }

    public List<Competition> getAvailableCompetition() {
        return availableCompetition;
    }

    public void setAvailableTeam(List<Team> availableTeam) {
        this.availableTeam = availableTeam;
    }

    public List<UserCompetitionException> getCompetitionException() {
        return competitionException;
    }

    public void setCompetitionException(List<UserCompetitionException> competitionException) {
        this.competitionException = competitionException;
    }

    public boolean isCompetitionPermitted(Long idComp) {
        for (Object objComp : availableCompetition) {
            Competition comp = (Competition) objComp;
            if (comp.getId().intValue() == idComp.intValue()) {
                return true;
            }
        }
        return false;
    }

    public boolean isTeamPermitted(Long idTeam) {
        for (Object objTeam : availableTeam) {
            Team team = (Team) objTeam;
            if (Objects.equals(team.getId(), idTeam)) {
                return true;
            }
        }
        return false;
    }

    public Long getSportId() {
        return sportId;
    }

    public void setSportId(Long sportId) {
        this.sportId = sportId;
    }

    public boolean isUserAdmin() {
        return StringUtils.equals(this.role, "ROLE_ADMIN");
    }

    public HashMap<String, EventDescription> getMapUserPanels() {
        return mapUserPanels;
    }

    public void setMapUserPanels(HashMap<String, EventDescription> mapUserPanels) {
        this.mapUserPanels = mapUserPanels;
    }

    public Long getSavedSource() {
        return savedSource;
    }

    public void setSavedSource(Long savedSource) {
        this.savedSource = savedSource;
    }

    public Boolean getIsShare() {
        if (this.levels.contains("08")){
            return true;
        } else {
            return false;
        }
    }

    public void setIsShare(Boolean isShare) {
        this.isShare = isShare;
    }

    public String getLevels() {
        return levels;
    }

    public void setLevels(String levels) {
        this.levels = levels;
    }

    /**
     * @return the totalSpace
     */
    public Long getTotalSpace() {
        return totalSpace;
    }

    /**
     * @param totalSpace the totalSpace to set
     */
    public void setTotalSpace(Long totalSpace) {
        this.totalSpace = totalSpace;
    }

    /**
     * @return the guest
     */
    public Boolean getGuest() {
        return guest;
    }

    /**
     * @param guest the guest to set
     */
    public void setGuest(Boolean guest) {
        this.guest = guest;
    }

    public Boolean getMulti_login() {
        return multi_login;
    }

    public void setMulti_login(Boolean multi_login) {
        this.multi_login = multi_login;
    }

    public Long getTvLimitAPI() {
        return tvLimitAPI;
    }

    public void setTvLimitAPI(Long tvLimitAPI) {
        this.tvLimitAPI = tvLimitAPI;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getTvLanguage() {
        return tvLanguage;
    }

    public String getTvLanguageForQuery() {
        if (StringUtils.isBlank(tvLanguage)) {
            return "_en";
        } else {
            return "_" + tvLanguage;
        }
    }

    public void setTvLanguage(String tvLanguage) {
        this.tvLanguage = tvLanguage;
    }

    public Long getTotalLogin() {
        return totalLogin;
    }

    public void setTotalLogin(Long totalLogin) {
        this.totalLogin = totalLogin;
    }

    public Long getTotalAction() {
        return totalAction;
    }

    public void setTotalAction(Long totalAction) {
        this.totalAction = totalAction;
    }

    public Date getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(Date lastLogin) {
        this.lastLogin = lastLogin;
    }
    
    public String getLastLoginString() {
        if (lastLogin == null) return "";
        
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm");
        return formatter.format(lastLogin);
    }

    public String getVtigerOrganization() {
        return vtigerOrganization;
    }

    public void setVtigerOrganization(String vtigerOrganization) {
        this.vtigerOrganization = vtigerOrganization;
    }

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }
    
    public boolean isPlayerUser() {
        return playerId != null;
    }
}
