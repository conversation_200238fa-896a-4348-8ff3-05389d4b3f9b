/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package sics.domain;

import com.google.gson.annotations.Expose;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class Button implements Serializable {

    @Expose
    private String cod;
    private String desc;
    private String endesc;
    private String frdesc;
    private String esdesc;
    private String rudesc;
//	private String sh; 
//	private String vis;
//	private String associato;
//	private boolean avversario;
    //Lista di oggetto di tipo tag associati
    private List tag = new ArrayList<Tags>();
    private String config;
    private String configId;

    /**
     * @return the cod
     */
    public String getCod() {
        return cod;
    }

    /**
     * @param cod the cod to set
     */
    public void setCod(String cod) {
        this.cod = cod;
    }

    /**
     * @return the desc
     */
    public String getDesc() {
        return desc;
    }

    /**
     * @param desc the desc to set
     */
    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * @return the endesc
     */
    public String getEndesc() {
        return endesc;
    }

    /**
     * @param endesc the endesc to set
     */
    public void setEndesc(String endesc) {
        this.endesc = endesc;
    }

    public String getFrdesc() {
        return frdesc;
    }

    public void setFrdesc(String frdesc) {
        this.frdesc = frdesc;
    }

    public String getEsdesc() {
        return esdesc;
    }

    public void setEsdesc(String esdesc) {
        this.esdesc = esdesc;
    }

    public String getRudesc() {
        return rudesc;
    }

    public void setRudesc(String rudesc) {
        this.rudesc = rudesc;
    }

    public String descTrim(String lang) {
        String description = null;
        if (StringUtils.isNotBlank(lang)) {
            switch (lang) {
                case "it":
                    description = desc;
                    break;
                case "en":
                    description = endesc;
                    break;
                case "fr":
                    description = frdesc;
                    break;
                case "es":
                    description = esdesc;
                    break;
                case "ru":
                    description = rudesc;
                    break;
                default:
                    break;
            }
        }
        if (description != null) {
            String descTrim = description.replace("\\n", " ");
            descTrim = descTrim.replace("\n", " ");
            return descTrim;
        } else {
            if (StringUtils.isNotBlank(endesc)) {
                return endesc;
            }
            return "";
        }
    }
//	/**
//	 * @return the sh
//	 */
//	public String getSh() {
//		return sh;
//	}
//
//	/**
//	 * @param sh the sh to set
//	 */
//	public void setSh(String sh) {
//		this.sh = sh;
//	}
//
//	/**
//	 * @return the vis
//	 */
//	public String getVis() {
//		return vis;
//	}
//
//	/**
//	 * @param vis the vis to set
//	 */
//	public void setVis(String vis) {
//		this.vis = vis;
//	}
//
//	/**
//	 * @return the associato
//	 */
//	public String getAssociato() {
//		return associato;
//	}
//
//	/**
//	 * @param associato the associato to set
//	 */
//	public void setAssociato(String associato) {
//		this.associato = associato;
//	}
//
//	/**
//	 * @return the avversario
//	 */
//	public boolean getAvversario() {
//		return avversario;
//	}
//
//	/**
//	 * @param avversario the avversario to set
//	 */
//	public void setAvversario(boolean avversario) {
//		this.avversario = avversario;
//	}
//	

    /**
     * @return the tag
     */
    public List getTag() {
        return tag;
    }

    /**
     * @param tag the tag to set
     */
    public void setTag(List tag) {
        if (tag != null) {
            this.tag = tag;
        }
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

}
