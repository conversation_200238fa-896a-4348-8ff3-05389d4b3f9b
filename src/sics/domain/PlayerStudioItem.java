package sics.domain;

import java.util.List;
import sics.model.Azione;

/**
 *
 * <AUTHOR>
 */
public class PlayerStudioItem {

    private List<Azione> events;
    private List<PlayerStudioTableItem> tableRows;

    public List<Azione> getEvents() {
        return events;
    }

    public void setEvents(List<Azione> events) {
        this.events = events;
    }

    public List<PlayerStudioTableItem> getTableRows() {
        return tableRows;
    }

    public void setTableRows(List<PlayerStudioTableItem> tableRows) {
        this.tableRows = tableRows;
    }
}
