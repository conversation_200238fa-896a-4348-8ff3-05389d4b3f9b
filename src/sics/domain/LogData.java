package sics.domain;

import java.io.*;
import java.util.Date;
import sics.helper.DateHelper;

public class LogData implements Serializable {

	private Long id;
	private String loginname;
	private String password;
	private Long userId;
	private String video;
	private Date date;
	private String action;
	private String addressId;
	private String sessionId;
	private String application;
	private String device;
	
	//Campi dati per statistiche streaming e download
	private Long objectNum;
	private String homeTeam;
	private String awayTeam;
	private Date gameDate;
	//Numero download utente
	private Long numDownload;


	public LogData() {
	}

	/**
	 * @return the id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the loginname
	 */
	public String getLoginname() {
		return loginname;
	}

	/**
	 * @param loginname the loginname to set
	 */
	public void setLoginname(String loginname) {
		this.loginname = loginname;
	}

	/**
	 * @return the password
	 */
	public String getPassword() {
		return password;
	}

	/**
	 * @param password the password to set
	 */
	public void setPassword(String password) {
		this.password = password;
	}

	/**
	 * @return the userId
	 */
	public Long getUserId() {
		return userId;
	}

	/**
	 * @param userId the userId to set
	 */
	public void setUserId(Long userId) {
		this.userId = userId;
	}

	/**
	 * @return the video
	 */
	public String getVideo() {
		return video;
	}

	/**
	 * @param video the video to set
	 */
	public void setVideo(String video) {
		this.video = video;
	}

	/**
	 * @return the date
	 *
	 * public Date getDate() { return date; } /
	 *
	 * /*Ritorna la data formattata
	
	public String getDate() {
		//SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
		SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
		String today = sdf.format(date);
		return today;
	}
   */
	
	public Date getDate(){
		return date;
	}
    
    public String getDateString(){
        return (getDate() != null) ? DateHelper.toStringExt(getDate()) : "";
    }
	
	/**
	 * @param date the date to set
	 */
	public void setDate(Date date) {
		this.date = date;
	}

	/**
	 * @return the action
	 */
	public String getAction() {
		return action;
	}

	/**
	 * @param action the action to set
	 */
	public void setAction(String action) {
		this.action = action;
	}

	/**
	 * @return the addressId
	 */
	public String getAddressId() {
		return addressId;
	}

	/**
	 * @param addressId the addressId to set
	 */
	public void setAddressId(String addressId) {
		this.addressId = addressId;
	}

	/**
	 * @return the sessionId
	 */
	public String getSessionId() {
		return sessionId;
	}

	/**
	 * @param sessionId the sessionId to set
	 */
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	/**
	 * @return the application
	 */
	public String getApplication() {
		return application;
	}

	/**
	 * @param application the application to set
	 */
	public void setApplication(String application) {
		this.application = application;
	}

	/**
	 * @return the device
	 */
	public String getDevice() {
		return device;
	}

	/**
	 * @param device the device to set
	 */
	public void setDevice(String device) {
		this.device = device;
	}

	/**
	 * @return the num
	 */
	public Long getObjectNum() {
		return objectNum;
	}

	/**
	 * @param num the nVideoDownload to set
	 */
	public void setObjectNum(Long num) {
		this.objectNum = num;
	}

	/**
	 * @return the homeTeam
	 */
	public String getHomeTeam() {
		return homeTeam;
	}

	/**
	 * @param homeTeam the homeTeam to set
	 */
	public void setHomeTeam(String homeTeam) {
		this.homeTeam = homeTeam;
	}

	/**
	 * @return the awayTeam
	 */
	public String getAwayTeam() {
		return awayTeam;
	}

	/**
	 * @param awayTeam the awayTeam to set
	 */
	public void setAwayTeam(String awayTeam) {
		this.awayTeam = awayTeam;
	}

	
	public Date getGameDate() {
		return gameDate;
	}
    
    public String getGameDateString(){
        return (getGameDate() != null) ? DateHelper.toStringExt(getGameDate()) : "";
    }
	
	/**
	 * @param gameDate the gameDate to set
	 */
	public void setGameDate(Date gameDate) {
		this.gameDate = gameDate;
	}

	/**
	 * @return the numDownload
	 */
	public Long getNumDownload() {
		return numDownload;
	}

	/**
	 * @param numDownload the numDownload to set
	 */
	public void setNumDownload(Long numDownload) {
		this.numDownload = numDownload;
	}
    
    


}
