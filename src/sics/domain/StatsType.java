package sics.domain;

import com.google.gson.Gson;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class StatsType implements Serializable {
    
    private Long id;
    private String code;
    private String desc;
    private Long avgTypeId;
    
    private Boolean isOpposite;
    private Boolean visible;
    private Integer availability;               // 0 = team/player, 1=solo team, 2=solo player
    
    private <PERSON><PERSON><PERSON> goalkeeperValid;
    private <PERSON>ole<PERSON> defenderValid;
    private <PERSON>ole<PERSON> midfielderValid;
    private Boole<PERSON> strikerValid;
    
    private String groupDescription;            // usato per raggruppare le statistiche
    private String tabDescription;              // usato per raggruppare le statistiche
    private Long minValue;                      // usato nello smart search dei giocatori
    private Long maxValue;                      // usato nello smart search dei giocatori
    private String filter;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Long getAvgTypeId() {
        return avgTypeId;
    }

    public void setAvgTypeId(Long avgTypeId) {
        this.avgTypeId = avgTypeId;
    }

    public Boolean getIsOpposite() {
        return isOpposite;
    }

    public void setIsOpposite(Boolean isOpposite) {
        this.isOpposite = isOpposite;
    }

    public Boolean getVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public Integer getAvailability() {
        return availability;
    }

    public void setAvailability(Integer availability) {
        this.availability = availability;
    }

    public Boolean getGoalkeeperValid() {
        return goalkeeperValid;
    }

    public void setGoalkeeperValid(Boolean goalkeeperValid) {
        this.goalkeeperValid = goalkeeperValid;
    }

    public Boolean getDefenderValid() {
        return defenderValid;
    }

    public void setDefenderValid(Boolean defenderValid) {
        this.defenderValid = defenderValid;
    }

    public Boolean getMidfielderValid() {
        return midfielderValid;
    }

    public void setMidfielderValid(Boolean midfielderValid) {
        this.midfielderValid = midfielderValid;
    }

    public Boolean getStrikerValid() {
        return strikerValid;
    }

    public void setStrikerValid(Boolean strikerValid) {
        this.strikerValid = strikerValid;
    }

    public String getGroupDescription() {
        return groupDescription;
    }

    public void setGroupDescription(String groupDescription) {
        this.groupDescription = groupDescription;
    }

    public String getTabDescription() {
        return tabDescription;
    }

    public void setTabDescription(String tabDescription) {
        this.tabDescription = tabDescription;
    }

    public Long getMinValue() {
        return minValue;
    }

    public void setMinValue(Long minValue) {
        this.minValue = minValue;
    }

    public Long getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(Long maxValue) {
        this.maxValue = maxValue;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }
    
    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}