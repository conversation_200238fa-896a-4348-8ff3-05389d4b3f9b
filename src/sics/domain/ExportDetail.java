package sics.domain;

import java.util.Date;
import sics.helper.GlobalHelper;

/**
 *
 * <AUTHOR>
 */
public class ExportDetail {
    
    private Long id;
    private Long exportId;
    private Integer type;
    private Integer statusCode;
    private String message;
    private Integer clipIndex;
    private Long clipStartMs;
    private Long clipDurationMs;
    private String videoName;
    private Date startTime;
    private Long executionTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getExportId() {
        return exportId;
    }

    public void setExportId(Long exportId) {
        this.exportId = exportId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getClipIndex() {
        return clipIndex;
    }

    public void setClipIndex(Integer clipIndex) {
        this.clipIndex = clipIndex;
    }

    public Long getClipStartMs() {
        return clipStartMs;
    }
    
    public String getClipStartString() {
        if (clipStartMs != null) {
            return GlobalHelper.msecToMinSec(clipStartMs, false, true, false);
        } else {
            return "";
        }
    }

    public void setClipStartMs(Long clipStartMs) {
        this.clipStartMs = clipStartMs;
    }

    public Long getClipDurationMs() {
        return clipDurationMs;
    }
    
    public String getClipDurationString() {
        if (clipDurationMs != null) {
            return GlobalHelper.msecToMinSec(clipDurationMs, false, true, false);
        } else {
            return "";
        }
    }

    public void setClipDurationMs(Long clipDurationMs) {
        this.clipDurationMs = clipDurationMs;
    }

    public String getVideoName() {
        return videoName;
    }

    public void setVideoName(String videoName) {
        this.videoName = videoName;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Long getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }
    
    public String getExecutionTimeString() {
        return GlobalHelper.msecToMinSec(executionTime, false, true, false);
    }
}
