package sics.domain;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class ChartData {
    
    private Long userId;
    private String action;
    private String firstName;
    private String lastName;
    private Long groupsetId;
    private String groupsetName;
    
    private Long actions;
    
    private Integer year;
    private Integer month;
    private Integer day;
    private Date date;
    
    private String videoId;
    private String agentFirstName;
    private String agentLastName;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    
    public String getFullName() {
        String name = "";
        if (firstName != null) name += firstName + " ";
        if (lastName != null) name += lastName;
        
        return name.trim();
    }
    
    public String getFullNameFormatted() {
        String name = "";
        if (firstName != null) name += firstName + "\\n";
        if (lastName != null) name += lastName;
        
        return name.trim();
    }

    public Long getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    public String getGroupsetName() {
        return groupsetName;
    }

    public void setGroupsetName(String groupsetName) {
        this.groupsetName = groupsetName;
    }

    public Long getActions() {
        return actions;
    }

    public void setActions(Long actions) {
        this.actions = actions;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Integer getMonth() {
        return month;
    }

    public void setMonth(Integer month) {
        this.month = month;
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }
    
    public String getDateString() {
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
        return formatter.format(date);
    }
    
    public String getDateStringWithTime() {
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        return formatter.format(date);
    }

    public String getVideoId() {
        return videoId;
    }

    public void setVideoId(String videoId) {
        this.videoId = videoId;
    }

    public String getAgentFirstName() {
        return agentFirstName;
    }

    public void setAgentFirstName(String agentFirstName) {
        this.agentFirstName = agentFirstName;
    }

    public String getAgentLastName() {
        return agentLastName;
    }

    public void setAgentLastName(String agentLastName) {
        this.agentLastName = agentLastName;
    }
}
