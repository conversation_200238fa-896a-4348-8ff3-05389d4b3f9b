package sics.domain;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import java.awt.Color;
import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import sics.helper.DateHelper;
import sics.helper.GlobalHelper;
import sics.model.Azione;

public class Game implements Serializable {

    protected boolean canDelete = false;
    private Long id;
    private Long idFixture;
    @Expose
    private Integer homeTeamId;
    @Expose
    private Integer awayTeamId;
    private String homeTeam;
    private String awayTeam;
    private Short homeTeamScore;
    private Short awayTeamScore;
    protected Long ownerUserId;
    private Date date;
    private Long competitionId;
    private Long groupId;
    private Long providerId;
    private Long seasonId;
    private Short matchday;
    private Long videoId;
    private String videoName;
    private String videoPathS3 = "";
    private String videoPathHDS3 = "";
    protected String videoPathTACTS3 = "";
    private Boolean hd;
    private Boolean fhd;
    private String competitionName;
    private Boolean tacticalVideo;
    private Long groupsetId;
    private Long refereeId;
    private Long assistant1Id;
    private Long assistant2Id;
    private Long assistant4Id;
    private Long assistantGoal1Id;
    private Long assistantGoal2Id;
    private String refereeName;
    private String assistant1Name;
    private String assistant2Name;
    private String assistant4Name;
    private String assistantGoal1Name;
    private String assistantGoal2Name;
    private String fileproject;
    private String pathReport = "";
    private boolean existReport = false;
    private boolean existReportEN = false;
//	private List formazione1;
//	private List formazione2;

    private String dataPath;
    private Integer KOF1, KOF2, HTIM, FTIM;

    private String homeModule;
    private String awayModule;
    private Color homeColor;
    private Color awayColor;
    private String homeLogo;
    private String awayLogo;
    private int _idSeason;
    private Integer teamSx, teamSx2;
    private HashMap<Integer, Atleta> _formazione1 = new HashMap<Integer, Atleta>();
    private HashMap<Integer, Atleta> _formazione2 = new HashMap<Integer, Atleta>();
    private HashMap<String, Azione> _azioniHome = new HashMap<String, Azione>();
    private HashMap<String, Azione> _azioniAway = new HashMap<String, Azione>();
    protected String ownerName = "";
    private int tagged = 1;

    private boolean drawCampoTabellino = true;
    private boolean loadedOnDB = false;
    private Integer videoQuality;
    private Integer minVideoQuality;
    private Integer analysisLevel;
    
    private Integer counter;        // usato attualmente per contare il numero di partite con lo stesso modulo
    @Expose
    private FixtureDetails fixtureDetails;

    public Game() {
    }

    /**
     * @return the id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id the id to set
     */
    public void setId(Long id) {
        this.id = id;
    }

    public Long getIdFixture() {
        return idFixture;
    }

    public void setIdFixture(Long idFixture) {
        this.idFixture = idFixture;
    }

    /**
     * @return the homeTeamId
     */
    public Integer getHomeTeamId() {
        return homeTeamId;
    }

    /**
     * @param homeTeamId the homeTeamId to set
     */
    public void setHomeTeamId(Integer homeTeamId) {
        this.homeTeamId = homeTeamId;
    }

    /**
     * @return the awayTeamId
     */
    public Integer getAwayTeamId() {
        return awayTeamId;
    }
    
    /**
     * @param awayTeamId the awayTeamId to set
     */
    public void setAwayTeamId(Integer awayTeamId) {
        this.awayTeamId = awayTeamId;
    }

    /**
     * @return the homeTeam
     */
    public String getHomeTeam() {
        return homeTeam;
    }

    /**
     * @param homeTeam the homeTeam to set
     */
    public void setHomeTeam(String homeTeam) {
        this.homeTeam = homeTeam;
    }

    /**
     * @return the awayTeam
     */
    public String getAwayTeam() {
        return awayTeam;
    }

    /**
     * @param awayTeam the awayTeam to set
     */
    public void setAwayTeam(String awayTeam) {
        this.awayTeam = awayTeam;
    }

    /**
     * @return the homeTeamScore
     */
    public Short getHomeTeamScore() {
        return homeTeamScore;
    }

    /**
     * @param homeTeamScore the homeTeamScore to set
     */
    public void setHomeTeamScore(Short homeTeamScore) {
        this.homeTeamScore = homeTeamScore;
    }

    /**
     * @return the awayTeamScore
     */
    public Short getAwayTeamScore() {
        return awayTeamScore;
    }

    /**
     * @param awayTeamScore the awayTeamScore to set
     */
    public void setAwayTeamScore(Short awayTeamScore) {
        this.awayTeamScore = awayTeamScore;
    }

    /**
     * @return the date
     */
    public Date getDate() {
        return date;
    }

    public String getDateFormatted() {
        if (date != null) {
            return DateHelper.toString(date);
        } else {
            return "";
        }
    }

    /**
     * @param date the date to set
     */
    public void setDate(Date date) {
        this.date = date;
    }

    /**
     * @return the competitionId
     */
    public Long getCompetitionId() {
        return competitionId;
    }

    /**
     * @param competitionId the competitionId to set
     */
    public void setCompetitionId(Long competitionId) {
        this.competitionId = competitionId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    /**
     * @return the seasonId
     */
    public Long getSeasonId() {
        return seasonId;
    }

    /**
     * @param seasonId the seasonId to set
     */
    public void setSeasonId(Long seasonId) {
        this.seasonId = seasonId;
    }

    /**
     * @return the matchday
     */
    public Short getMatchday() {
        return matchday;
    }

    /**
     * @param matchday the matchday to set
     */
    public void setMatchday(Short matchday) {
        this.matchday = matchday;
    }

    /**
     * @return the videoId
     */
    public Long getVideoId() {
        return videoId;
    }

    /**
     * @param videoId the videoId to set
     */
    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }

    public Boolean getHd() {
        return hd;
    }

    public void setHd(Boolean val) {
        this.hd = val;
    }

    public Boolean getFhd() {
        return fhd;
    }

    public void setFhd(Boolean val) {
        this.fhd = val;
    }

    public String getDateString() {
        return (getDate() != null) ? DateHelper.toString(getDate()) : "";
    }

    public String getDateFullString() {
        return (getDate() != null) ? DateHelper.toStringExt(getDate()) : "";
    }

    public String getDateMonthString() {
        return (getDate() != null) ? DateHelper.toMonthExt(getDate()) : "";
    }

    public String getDateYearString() {
        return (getDate() != null) ? DateHelper.toYearExt(getDate()) : "";
    }

    public String getDateDayString() {
        return (getDate() != null) ? DateHelper.toDayExt(getDate()) : "";
    }

    /**
     * @return the refereeId
     */
    public Long getRefereeId() {
        return refereeId;
    }

    /**
     * @param refereeId the refereeId to set
     */
    public void setRefereeId(Long refereeId) {
        this.refereeId = refereeId;
    }

    /**
     * @return the assistant1Id
     */
    public Long getAssistant1Id() {
        return assistant1Id;
    }

    /**
     * @param assistant1Id the assistant1Id to set
     */
    public void setAssistant1Id(Long assistant1Id) {
        this.assistant1Id = assistant1Id;
    }

    /**
     * @return the assistant2Id
     */
    public Long getAssistant2Id() {
        return assistant2Id;
    }

    /**
     * @param assistant2Id the assistant2Id to set
     */
    public void setAssistant2Id(Long assistant2Id) {
        this.assistant2Id = assistant2Id;
    }

    /**
     * @return the assistant4Id
     */
    public Long getAssistant4Id() {
        return assistant4Id;
    }

    /**
     * @param assistant4Id the assistant4Id to set
     */
    public void setAssistant4Id(Long assistant4Id) {
        this.assistant4Id = assistant4Id;
    }

    public Long getAssistantGoal1Id() {
        return assistantGoal1Id;
    }

    public void setAssistantGoal1Id(Long assistantGoal1Id) {
        this.assistantGoal1Id = assistantGoal1Id;
    }

    public Long getAssistantGoal2Id() {
        return assistantGoal2Id;
    }

    public void setAssistantGoal2Id(Long assistantGoal2Id) {
        this.assistantGoal2Id = assistantGoal2Id;
    }

    public String getAssistantGoal1Name() {
        return assistantGoal1Name;
    }

    public void setAssistantGoal1Name(String assistantGoal1Name) {
        this.assistantGoal1Name = assistantGoal1Name;
    }

    public String getAssistantGoal2Name() {
        return assistantGoal2Name;
    }

    public void setAssistantGoal2Name(String assistantGoal2Name) {
        this.assistantGoal2Name = assistantGoal2Name;
    }

    /**
     * @return the refereeName
     */
    public String getRefereeName() {
        return refereeName;
    }

    /**
     * @param refereeName the refereeName to set
     */
    public void setRefereeName(String refereeName) {
        this.refereeName = refereeName;
    }

    /**
     * @return the assistant1Name
     */
    public String getAssistant1Name() {
        return assistant1Name;
    }

    /**
     * @param assistant1Name the assistant1Name to set
     */
    public void setAssistant1Name(String assistant1Name) {
        this.assistant1Name = assistant1Name;
    }

    /**
     * @return the assistant2Name
     */
    public String getAssistant2Name() {
        return assistant2Name;
    }

    /**
     * @param assistant2Name the assistant2Name to set
     */
    public void setAssistant2Name(String assistant2Name) {
        this.assistant2Name = assistant2Name;
    }

    /**
     * @return the assistant4Name
     */
    public String getAssistant4Name() {
        return assistant4Name;
    }

    /**
     * @param assistant4Name the assistant4Name to set
     */
    public void setAssistant4Name(String assistant4Name) {
        this.assistant4Name = assistant4Name;
    }

    /**
     * @return the fileproject
     */
    public String getFileproject() {
        return fileproject;
    }

    /**
     * @param fileproject the fileproject to set
     */
    public void setFileproject(String fileproject) {
        this.fileproject = fileproject;
    }

    /**
     * @return the providerId
     */
    public Long getProviderId() {
        return providerId;
    }

    /**
     * @param providerId the providerId to set
     */
    public void setProviderId(Long providerId) {
        this.providerId = providerId;
    }

    /**
     * @return the videoName
     */
    public String getVideoName() {
        return videoName;
    }

    /**
     * restituisce il nome del video a seconda del type (0=SD, 1=HD, 2=FHD)
     */
    public String getVideoName(Integer type) {
        switch (type) {
            case 0:
                return videoName;
            case 1:
                return videoName.replace(".mp4", "-HD.mp4");
            case 2:
                return videoName.replace(".mp4", "-FHD.mp4");
            case 3:
                return videoName.replace(".mp4", "-TACT.mp4");
            default:
                return videoName;
        }
    }

    /**
     * @param videoName the videoName to set
     */
    public void setVideoName(String videoName) {
        this.videoName = videoName;
    }

    public String getDataPath() {
        return dataPath;
    }

    public void setDataPath(String dataPath) {
        this.dataPath = dataPath;
    }

    public Integer getKOF1() {
        return KOF1;
    }

    public void setKOF1(Integer KOF1) {
        this.KOF1 = KOF1;
    }

    public Integer getKOF2() {
        return KOF2;
    }

    public void setKOF2(Integer KOF2) {
        this.KOF2 = KOF2;
    }

    public Integer getHTIM() {
        return HTIM;
    }

    public void setHTIM(Integer HTIM) {
        this.HTIM = HTIM;
    }

    public Integer getFTIM() {
        return FTIM;
    }

    public void setFTIM(Integer FTIM) {
        this.FTIM = FTIM;
    }

    public String getCompetitionName() {
        return competitionName;
    }

    public void setCompetitionName(String competitionName) {
        this.competitionName = competitionName;
    }

    public String getHomeModule() {
        return homeModule;
    }

    public void setHomeModule(String homeModule) {
        this.homeModule = homeModule;
    }

    public String getAwayModule() {
        return awayModule;
    }

    public void setAwayModule(String awayModule) {
        this.awayModule = awayModule;
    }

    public int getIdSeason() {
        return _idSeason;
    }

    public void setIdSeason(int _idSeason) {
        this._idSeason = _idSeason;
    }

    public Integer getTeamSx() {
        return teamSx;
    }

    public void setTeamSx(Integer teamSx) {
        this.teamSx = teamSx;
    }

    public Integer getTeamSx2() {
        return teamSx2;
    }

    public void setTeamSx2(Integer teamSx2) {
        this.teamSx2 = teamSx2;
    }

    public HashMap<Integer, Atleta> getFormazione1() {
        return _formazione1;
    }

    public void setFormazione1(HashMap<Integer, Atleta> _formazione1) {
        this._formazione1 = _formazione1;
    }

    public HashMap<Integer, Atleta> getFormazione2() {
        return _formazione2;
    }

    public void setFormazione2(HashMap<Integer, Atleta> _formazione2) {
        this._formazione2 = _formazione2;
    }

    public void addFormazione1(ArrayList<Atleta> _formazione1) {
        this._formazione1 = new HashMap<Integer, Atleta>();
        for (Atleta atleta : _formazione1) {
            this._formazione1.put(this._formazione1.size(), atleta);
        }
    }

    public void addFormazione2(ArrayList<Atleta> _formazione2) {
        this._formazione2 = new HashMap<Integer, Atleta>();
        for (Atleta atleta : _formazione2) {
            this._formazione2.put(this._formazione2.size(), atleta);
        }
    }

    public HashMap<String, Azione> getAzioniHome() {
        return _azioniHome;
    }

    public void setAzioniHome(HashMap<String, Azione> _azioniHome) {
        this._azioniHome = _azioniHome;
    }

    public HashMap<String, Azione> getAzioniAway() {
        return _azioniAway;
    }

    public void setAzioniAway(HashMap<String, Azione> _azioniAway) {
        this._azioniAway = _azioniAway;
    }

    public Color getHomeColor() {
        return homeColor;
    }

    public void setHomeColor(String homeColor) {
        this.homeColor = GlobalHelper.getColorFromString(homeColor);
    }

    public Color getAwayColor() {
        return awayColor;
    }

    public void setAwayColor(String awayColor) {
        this.awayColor = GlobalHelper.getColorFromString(awayColor);
    }

    public String getHomeLogo() {
        return homeLogo;
    }

    public void setHomeLogo(String homeLogo) {
        this.homeLogo = homeLogo;
    }

    public String getAwayLogo() {
        return awayLogo;
    }

    public void setAwayLogo(String awayLogo) {
        this.awayLogo = awayLogo;
    }

    public boolean isTagged() {
        return analysisLevel > 0 || isLoadedOnDB();
    }

    public void setTagged(int tagged) {
        this.tagged = tagged;
    }

    public boolean isLoadedOnDB() {
        return loadedOnDB;
    }

    public void setLoadedOnDB(boolean loadedOnDB) {
        this.loadedOnDB = loadedOnDB;
    }

    public String getVideoPathS3() {
        if (videoName.isEmpty()) {
            return "";
        }
        if (videoPathS3 == null || videoPathS3.isEmpty()) {
            //videoPathS3 = GlobalHelper.pathS3(videoName, "video");
            // DAFARE 2021-01-29 imposto sempre cloudFront per "video"
            videoPathS3 = GlobalHelper.pathS3(videoName/*"LR VICENZA VIRTUS-CITTADELLA-02042021.mp4"*/, "video", true);
        }
        return videoPathS3/*="https://s3-eu-west-1.amazonaws.com/it.sics.test/LR VICENZA VIRTUS-CITTADELLA-02042021.mp4"*/;
    }

    public void setVideoPathS3(Boolean cloudFront) {
        videoPathS3 = GlobalHelper.pathS3(videoName, "video", cloudFront);
    }

    public String getVideoPathHDS3() {
        if (videoName.isEmpty()) {
            return "";
        }
        if (videoPathHDS3 == null || videoPathHDS3.isEmpty()) {
            // DAFARE 2020/12/23 se videoPathS3 è su cloudfront videoPathHDS3 va solo rinominato
//            if (videoPathS3 != null && videoPathS3.contains("cloudfront")) {
//                videoPathHDS3 = videoPathS3.replace(".mp4", "-HD.mp4");
//            } else {
            String pathHd = videoName.replace(".mp4", "-HD.mp4");
//                videoPathHDS3 = GlobalHelper.pathS3(pathHd, "video");
            videoPathHDS3 = GlobalHelper.pathS3(pathHd, "video", true);
//            }
        }
        return videoPathHDS3;
    }

    public String getVideoPathTACTS3() {
        if (videoName.isEmpty()) {
            return "";
        }
        if (videoPathTACTS3 == null || videoPathTACTS3.isEmpty()) {
            // DAFARE 2020/12/23 se videoPathS3 è su cloudfront videoPathHDS3 va solo rinominato
//            if (videoPathS3 != null && videoPathS3.contains("cloudfront")) {
//                videoPathTACTS3 = videoPathS3.replace(".mp4", "-TACT.mp4");
//            } else {
            String pathTact = videoName.replace(".mp4", "-TACT.mp4");
//                videoPathTACTS3 = GlobalHelper.pathS3(pathTact, "video");
            videoPathTACTS3 = GlobalHelper.pathS3(pathTact, "video", true);
//            }
        }
        return videoPathTACTS3;
    }

    public void setVideoPathTACTS3(Boolean cloudFront) {
        videoPathTACTS3 = GlobalHelper.pathS3(videoName, "video", cloudFront);
    }

    public List getOrderedByNumberFormazione1() {
        ArrayList<Atleta> formazione1 = new ArrayList<Atleta>();
        for (Atleta atl : _formazione1.values()) {
            formazione1.add(atl);
        }

        Collections.sort(formazione1, Atleta.getAtletaComparator());
        return formazione1;
    }

    public List getOrderedByNumberFormazione2() {
        ArrayList<Atleta> formazione2 = new ArrayList<Atleta>();
        for (Atleta atl : _formazione2.values()) {
            formazione2.add(atl);
        }

        Collections.sort(formazione2);
        return formazione2;
    }

    public List getOrderedByFieldPosition(boolean home) {
        ArrayList<Atleta> team = new ArrayList<>();
        if (home) {
            for (Atleta atleta : getFormazione1().values()) {
                team.add(atleta);
            }
        } else {
            for (Atleta atleta : getFormazione2().values()) {
                team.add(atleta);
            }
        }
        Collections.sort(team, Atleta.getAtletaComparator());

        // vado ad ordinare i subentrati in ordine di subentro
        ArrayList<String> keys = new ArrayList<String>();
        HashMap<String, Atleta> mapTemp = new HashMap<String, Atleta>();
        for (int i = team.size() - 1; i > 10; i--) {
            Atleta at = team.get(i);
            Integer minute = at.getFromPeriod() == 2 ? 45 + at.getFromMinute() : at.getFromMinute();
            String key = (minute < 10 ? "00" : minute < 100 ? "0" : "") + minute + "-" + at.getKnown_name();
            keys.add(key);
            mapTemp.put(key, team.remove(i));
        }
        Collections.sort(keys);

        for (String k : keys) {
            team.add(mapTemp.get(k));
        }

        return team;
    }
//	
//	public String getPathReport(){
//		if(pathReport.isEmpty()){
//			pathReport = SpringServletContextHelper.getInitParameter("webRootReportFolder") + this.fileproject.replace(".xml", ".pdf");
//		}
//		return pathReport;
//	}

    public String getPathReport() {
        if (pathReport.isEmpty()) {
            pathReport = this.fileproject.replace(".xml", ".pdf");
        }
        return pathReport;
    }

    public String getPathReportEN() {
        if (pathReport.isEmpty()) {
            pathReport = this.fileproject.replace(".xml", "_en.pdf");
        }
        return pathReport;
    }

    public void setPathReport(String pathReport) {
        this.pathReport = pathReport;
    }

    public boolean isExistReport() {
        return existReport;
    }

    public void setExistReport(boolean existReport) {
        this.existReport = existReport;
    }

    public boolean isDrawCampoTabellino() {
        return drawCampoTabellino;
    }

    public void setDrawCampoTabellino(boolean drawCampoTabellino) {
        this.drawCampoTabellino = drawCampoTabellino;
    }

    public Boolean getTacticalVideo() {
        return tacticalVideo;
    }

    public void setTacticalVideo(Boolean tacticalVideo) {
        this.tacticalVideo = tacticalVideo;
    }

    /**
     * @return the ownerUserId
     */
    public Long getOwnerUserId() {
        return ownerUserId;
    }

    /**
     * @param ownerUserId the ownerUserId to set
     */
    public void setOwnerUserId(Long ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    /**
     * @return the canDelete
     */
    public boolean isCanDelete() {
        return canDelete;
    }

    /**
     * @param canDelete the canDelete to set
     */
    public void setCanDelete(boolean canDelete) {
        this.canDelete = canDelete;
    }

    /**
     * @return the ownerName
     */
    public String getOwnerName() {
        return ownerName;
    }

    /**
     * @param ownerName the ownerName to set
     */
    public void setOwnerName(String ownerName) {
        if (ownerName == null) {
            this.ownerName = "";
        } else {
            this.ownerName = ownerName;
        }
    }

    /**
     * @return the groupsetId
     */
    public Long getGroupsetId() {
        return groupsetId;
    }

    /**
     * @param groupsetId the groupsetId to set
     */
    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    public String getThumbName() {
        return videoName.replace(".mp4", ".png");
    }
    
    /**
     * @return the existReportEN
     */
    public boolean isExistReportEN() {
        return existReportEN;
    }

    /**
     * @param existReportEN the existReportEN to set
     */
    public void setExistReportEN(boolean existReportEN) {
        this.existReportEN = existReportEN;
    }

    public Integer getVideoQuality() {
        return videoQuality;
    }

    public void setVideoQuality(Integer videoQuality) {
        this.videoQuality = videoQuality;
    }

    /**
     * @return the minVideoQuality
     */
    public Integer getMinVideoQuality() {
        return minVideoQuality;
    }

    /**
     * @param minVideoQuality the minVideoQuality to set
     */
    public void setMinVideoQuality(Integer minVideoQuality) {
        this.minVideoQuality = minVideoQuality;
    }

    public Integer getAnalysisLevel() {
        return analysisLevel;
    }

    public void setAnalysisLevel(Integer analysisLevel) {
        this.analysisLevel = analysisLevel;
    }

    public Integer getCounter() {
        return counter;
    }

    public void setCounter(Integer counter) {
        this.counter = counter;
    }

    public FixtureDetails getFixtureDetails() {
        return fixtureDetails;
    }

    public void setFixtureDetails(FixtureDetails fixtureDetails) {
        this.fixtureDetails = fixtureDetails;
    }
    
    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
    
    public String getJsonForVideo() {
        Gson gson = new GsonBuilder().excludeFieldsWithoutExposeAnnotation().create();
        return gson.toJson(this);
    }
}
