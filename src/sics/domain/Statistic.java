package sics.domain;

/**
 *
 * <AUTHOR>
 */
public class Statistic {
    
    private Long id;
    private String name;
    private Double value;
    private Double valueP90;
    private Double valuePercentage;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }
    
    public String getValueNoDecimals() {
        if (value == null) return "";
        
        return String.format("%.0f", value);
    }
    
    public String getValue2Decimals() {
        if (value == null) return "";
        
        return String.format("%.2f", value);
    }

    public Double getValueP90() {
        return valueP90;
    }

    public void setValueP90(Double valueP90) {
        this.valueP90 = valueP90;
    }
    
    public String getValueP90NoDecimals() {
        if (valueP90 == null) return "";
        
        return String.format("%.0f", valueP90);
    }
    
    public String getValueP902Decimals() {
        if (valueP90 == null) return "";
        
        return String.format("%.2f", valueP90);
    }

    public Double getValuePercentage() {
        return valuePercentage;
    }

    public void setValuePercentage(Double valuePercentage) {
        this.valuePercentage = valuePercentage;
    }
    
    public String getValuePercentageNoDecimals() {
        if (valuePercentage == null) return "";
        
        return String.format("%.0f", valuePercentage);
    }
    
    public String getValuePercentage2Decimals() {
        if (valuePercentage == null) return "";
        
        return String.format("%.2f", valuePercentage);
    }
}
