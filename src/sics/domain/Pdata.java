package sics.domain;

import sics.service.UserService;
import java.io.*;
import java.util.Date;
import sics.helper.GlobalHelper;

public class Pdata implements Serializable {

	private Date datacreazione;
	private Date datascadenza;
	private String serialnumber;
	private String firstName;
	private String lastName;
	
	public Pdata() {
	}

	/**
	 * @return the firstName
	 */
	public String getFirstName() {
		return firstName;
	}

	/**
	 * @param firstName the firstName to set
	 */
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	/**
	 * @return the lastName
	 */
	public String getLastName() {
		return lastName;
	}

	/**
	 * @param lastName the lastName to set
	 */
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Date getDatacreazione() {
		return datacreazione;
	}

	public void setDatacreazione(Date datacreazione) {
		this.datacreazione = datacreazione;
	}

	public Date getDatascadenza() {
		return datascadenza;
	}

	public void setDatascadenza(Date datascadenza) {
		this.datascadenza = datascadenza;
	}

	public String getSerialnumber() {
		return serialnumber;
	}

	public void setSerialnumber(String serialnumber) {
		this.serialnumber = serialnumber;
	}

	
}
