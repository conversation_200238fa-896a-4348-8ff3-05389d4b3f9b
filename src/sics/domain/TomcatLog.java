package sics.domain;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class TomcatLog {
    
    private Date date;
    private String level;
    private String thread;
    private String loggedClass;
    private Long userId;
    private String userFirstName;
    private String link;
    private String duration;

    public Date getDate() {
        return date;
    }
    
    public String getDateString() {
        if (date == null) return "";
        
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        return formatter.format(date);
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getThread() {
        return thread;
    }

    public void setThread(String thread) {
        this.thread = thread;
    }

    public String getLoggedClass() {
        return loggedClass;
    }

    public void setLoggedClass(String loggedClass) {
        this.loggedClass = loggedClass;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserFirstName() {
        return userFirstName;
    }

    public void setUserFirstName(String userFirstName) {
        this.userFirstName = userFirstName;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }
}
