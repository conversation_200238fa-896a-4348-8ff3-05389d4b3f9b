package sics.domain;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class AgentPayment {
    
    private List<String> headers;
    private Map<Integer, List<ExcelCell>> rows;

    public List<String> getHeaders() {
        return headers;
    }

    public void setHeaders(List<String> headers) {
        this.headers = headers;
    }

    public Map<Integer, List<ExcelCell>> getRows() {
        return rows;
    }

    public void setRows(Map<Integer, List<ExcelCell>> rows) {
        this.rows = rows;
    }
}
