package sics.domain;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EventTagsFilter {
    
    private String eventTypeCode;
    private List<String> tagWithOr = new ArrayList<>();
    private List<String> tagWithAnd = new ArrayList<>();
    private List<String> tagWithNot = new ArrayList<>();

    public String getEventTypeCode() {
        return eventTypeCode;
    }

    public void setEventTypeCode(String eventTypeCode) {
        this.eventTypeCode = eventTypeCode;
    }

    public List<String> getTagWithOr() {
        return tagWithOr;
    }

    public void setTagWithOr(List<String> tagWithOr) {
        this.tagWithOr = tagWithOr;
    }

    public List<String> getTagWithAnd() {
        return tagWithAnd;
    }

    public void setTagWithAnd(List<String> tagWithAnd) {
        this.tagWithAnd = tagWithAnd;
    }

    public List<String> getTagWithNot() {
        return tagWithNot;
    }

    public void setTagWithNot(List<String> tagWithNot) {
        this.tagWithNot = tagWithNot;
    }
}
