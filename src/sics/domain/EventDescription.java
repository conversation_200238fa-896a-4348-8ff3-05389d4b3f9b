/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.domain;

import java.util.ArrayList;
import java.util.List;
import sics.helper.GlobalHelper;

/**
 *
 * <AUTHOR>
 */
public class EventDescription {

    private String code;
    private String description;
    private String descriptionEn;
    private String descriptionFr;
    private String descriptionEs;
    private String descriptionRu;
    private List<Tags> tag = new ArrayList<>();
    // flag che identifica se è abilitato oppure no nella pagina web
    private boolean enabled = true;

    private int countAction = 0;

    /**
     * @return the id
     */
    public String getCode() {
        return code;
    }

    /**
     * @param code the id to set
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * @return the description
     */
    public String getDescription() {
        return description;
    }

    /**
     * @param description the description to set
     */
    public void setDescription(String description) {
        this.description = description;
    }

    public String descTrim() {
        try {
            String descTrim = description.replace("\\n", " ");
            return descTrim;
        } catch (Exception ex) {
            System.out.println("ID errore: " + code);
            GlobalHelper.reportError(ex);
            return "";
        }
    }

    public String descTrim(String lang) {
        try {
            switch (lang) {
                case "it":
                {
                    String descTrim = description.replace("\\n", " ");
                    return descTrim;
                }
                case "fr":
                {
                    String descTrim = descriptionFr.replace("\\n", " ");
                    return descTrim;
                }
                case "es":
                {
                    String descTrim = descriptionEs.replace("\\n", " ");
                    return descTrim;
                }
                case "ru":
                {
                    String descTrim = descriptionRu.replace("\\n", " ");
                    return descTrim;
                }
                default:
                {
                    String descTrim = descriptionEn.replace("\\n", " ");
                    return descTrim;
                }
            }
        } catch (Exception ex) {
            System.out.println("ID errore: " + code);
            GlobalHelper.reportError(ex);
            return "";
        }
    }

    /**
     * @return the tag
     */
    public List<Tags> getTag() {    
        return tag;
    }

    /**
     * @param tag the tag to set
     */
    public void setTag(List<Tags> tag) {
        this.tag = tag;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getCountAction() {
        return countAction;
    }

    public void setCountAction(int countAction) {
        this.countAction = countAction;
    }

    /**
     * @return the descriptionEn
     */
    public String getDescriptionEn() {
        return descriptionEn;
    }

    /**
     * @param descriptionEn the descriptionEn to set
     */
    public void setDescriptionEn(String descriptionEn) {
        this.descriptionEn = descriptionEn;
    }

    public String getDescriptionFr() {
        return descriptionFr;
    }

    public void setDescriptionFr(String descriptionFr) {
        this.descriptionFr = descriptionFr;
    }

    public String getDescriptionEs() {
        return descriptionEs;
    }

    public void setDescriptionEs(String descriptionEs) {
        this.descriptionEs = descriptionEs;
    }

    public String getDescriptionRu() {
        return descriptionRu;
    }

    public void setDescriptionRu(String descriptionRu) {
        this.descriptionRu = descriptionRu;
    }
}
