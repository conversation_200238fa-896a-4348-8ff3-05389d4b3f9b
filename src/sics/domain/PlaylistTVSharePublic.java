package sics.domain;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class PlaylistTVSharePublic implements Serializable {
    
    private Long id;
    private Long playlistId;
    private String link;
    private Long click;         // click fatti fino ad adesso
    private Long maxClick;
    private Long userId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPlaylistId() {
        return playlistId;
    }

    public void setPlaylistId(Long playlistId) {
        this.playlistId = playlistId;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public Long getClick() {
        return click;
    }

    public void setClick(Long click) {
        this.click = click;
    }

    public Long getMaxClick() {
        return maxClick;
    }

    public void setMaxClick(Long maxClick) {
        this.maxClick = maxClick;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}