package sics.domain;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DatabaseEditor {
    
    private final String table;
    private final Boolean isAdminRequest;
    private final List<String> editableColumns;
    private final List<String> dateTypeColumns;
    private final List<String> sortColumns;
    private final Integer limit;
    private final String condition;
    
    private final String query;
    
    private DatabaseEditor(Builder builder) {
        this.table = builder.table;
        this.isAdminRequest = builder.isAdminRequest;
        this.editableColumns = builder.editableColumns;
        this.dateTypeColumns = builder.dateTypeColumns;
        this.sortColumns = builder.sortColumns;
        this.limit = builder.limit;
        this.condition = builder.condition;
        this.query = builder.query;
    }

    public String getTable() {
        return table;
    }

    public Boolean getIsAdminRequest() {
        return isAdminRequest;
    }

    public List<String> getEditableColumns() {
        return editableColumns;
    }

    public List<String> getDateTypeColumns() {
        return dateTypeColumns;
    }

    public List<String> getSortColumns() {
        return sortColumns;
    }

    public Integer getLimit() {
        return limit;
    }

    public String getCondition() {
        return condition;
    }

    public String getQuery() {
        return query;
    }
    
    @Override
    public String toString() {
        return query;
    }
    
    // Builder
    public static class Builder {
        
        private String table;
        private Boolean isAdminRequest;
        private List<String> editableColumns;
        private List<String> dateTypeColumns;
        private List<String> sortColumns;
        private Integer limit;
        private String condition;

        private String query;
        
        public Builder(String table) {
            this.table = table;
            this.isAdminRequest = false;    // default non si edita nulla
            this.limit = 5000;              // default LIMIT 5000
        }
        
        public Builder isAdminRequest(boolean value) {
            this.isAdminRequest = value;
            return this;
        }
        
        public Builder editableColumns(List<String> columns) {
            this.editableColumns = columns;
            return this;
        }
        
        public Builder addEditableColumn(String column) {
            if (this.editableColumns == null) {
                this.editableColumns = new ArrayList<>();
            }
            this.editableColumns.add(column);
            return this;
        }
        
        public Builder dateTypeColumns(List<String> columns) {
            this.dateTypeColumns = columns;
            return this;
        }
        
        public Builder addDateTypeColumn(String column) {
            if (this.dateTypeColumns == null) {
                this.dateTypeColumns = new ArrayList<>();
            }
            this.dateTypeColumns.add(column);
            return this;
        }
        
        public Builder sortColumns(List<String> columns) {
            this.sortColumns = columns;
            return this;
        }
        
        public Builder addSortColumn(String column) {
            if (this.sortColumns == null) {
                this.sortColumns = new ArrayList<>();
            }
            this.sortColumns.add(column);
            return this;
        }
        
        public Builder limit(Integer limit) {
            this.limit = limit;
            return this;
        }
        
        public Builder condition(String where) {
            this.condition = where;
            return this;
        }
        
        public DatabaseEditor build() {
            query = "SELECT " + table + ".* FROM ";
            query += table + " ";
            if (condition != null) {
                query += "WHERE " + condition + " ";
            }
            query += "LIMIT " + limit;
            
            return new DatabaseEditor(this);
        }
    }
}
