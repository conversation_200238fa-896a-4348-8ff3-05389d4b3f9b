package sics.domain;

import java.text.SimpleDateFormat;
import java.util.Date;
import sics.helper.GlobalHelper;

/**
 *
 * <AUTHOR>
 */
public class Export {
    
    private Long id;
    private Integer application; // 1=Sicstv, 2=VM
    private Long userId;
    private Long groupsetId;
    private String name;
    private Integer type; // 1=Concat, 2=Zip
    private Integer statusCode;
    private Integer clipAmount;
    private Date startTime;
    private Long totalTime;
    private Long totalBilled;
    private Long totalVideoDuration;
    private Long playlistId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getApplication() {
        return application;
    }

    public void setApplication(Integer application) {
        this.application = application;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    public Integer getClipAmount() {
        return clipAmount;
    }

    public void setClipAmount(Integer clipAmount) {
        this.clipAmount = clipAmount;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    
    public String getStartTimeString() {
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        return formatter.format(startTime);
    }

    public Long getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(Long totalTime) {
        this.totalTime = totalTime;
    }

    public Long getTotalBilled() {
        return totalBilled;
    }

    public void setTotalBilled(Long totalBilled) {
        this.totalBilled = totalBilled;
    }

    public Long getTotalVideoDuration() {
        return totalVideoDuration;
    }

    public void setTotalVideoDuration(Long totalVideoDuration) {
        this.totalVideoDuration = totalVideoDuration;
    }
    
    public String getTotalVideoDurationFormatted() {
        if (this.totalVideoDuration == null) return "";
        
        return GlobalHelper.msecToMinSec(totalVideoDuration, false, true, false);
    }

    public Long getPlaylistId() {
        return playlistId;
    }

    public void setPlaylistId(Long playlistId) {
        this.playlistId = playlistId;
    }
}
