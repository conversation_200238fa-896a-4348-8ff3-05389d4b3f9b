package sics.domain;

/**
 *
 * <AUTHOR>
 */
public class ShadowTeam {

    private Long id;
    private String name;
    private Integer module;
    private Long userId;
    private Long groupsetId;
    private Boolean shared;

    private String userName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getModule() {
        return module;
    }

    public String getModuleName() {
        if (module == null) {
            return "";
        }

        switch (module) {
            case 0:
                return "3412";
            case 1:
                return "3421";
            case 2:
                return "343";
            case 3:
                return "3511";
            case 4:
                return "352";
            case 5:
                return "4141";
            case 6:
                return "4222";
            case 7:
                return "4231";
            case 8:
                return "442";
            case 9:
                return "4411";
            case 10:
                return "451";
            case 11:
                return "433";
            case 12:
                return "4312";
            case 13:
                return "4321";
            case 14:
                return "532";
            case 15:
                return "541";
            default:
                return "";
        }
    }

    public void setModule(Integer module) {
        this.module = module;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    public Boolean getShared() {
        return shared;
    }

    public void setShared(Boolean shared) {
        this.shared = shared;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
