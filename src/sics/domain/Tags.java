/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.domain;

import com.google.gson.annotations.Expose;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class Tags implements Comparable {

    private int _id;
    private String desc, endesc, frdesc, esdesc, rudesc, codeAssociated = "";
    @Expose
    private String code;
    private String mGroup = "";
    private Tags mAssociated = null;
    private Boolean mDefault = false;
    private boolean enabled = true;
    private Long panelId;
    private String eventTypeCode;
    private Long eventTypeId;

    public Tags() {
        desc = endesc = frdesc = esdesc = rudesc = code = mGroup = codeAssociated = "";
    }

    public Tags(String tipo) {
        code = tipo;
    }

    public Tags(int id, String type) {
        _id = id;
        code = type;
    }

    public Tags(String cod, String desc) {
        this.desc = desc;
        code = cod;
    }

    public Tags(Integer tagId, String cod, String itdesc, String endesc, String frdesc, String esdesc, String rudesc) {
        this._id = tagId;
        this.code = cod;
        this.desc = itdesc;
        this.endesc = endesc;
        this.frdesc = frdesc;
        this.esdesc = esdesc;
        this.rudesc = rudesc;
    }

    @Override
    public String toString() {
        return code + " " + getDesc();
    }

    @Override
    public int compareTo(Object o) {
        Tags t = (Tags) o;
        Integer cod = Integer.parseInt(this.getCode().split("-")[1]);
        Integer cod2 = Integer.parseInt(t.getCode().split("-")[1]);
        if (cod > cod2) {
            return 1;
        }
        return -1;

    }

    @Override
    public boolean equals(Object o) {
        try {
            Tags item = (Tags) o;
            return this.code.equals(item.code);
        } catch (Exception ex) {
        }
        return false;
    }

    public String getDesc() {
        //return (GlobalHelper.kSettings.getLanguage().equalsIgnoreCase(GlobalHelper.kLanguageInglese)) ? endesc : desc ;
        return desc;
    }

    /**
     * @param desc the desc to set
     */
    public void setDesc(String desc) {
        this.desc = desc;
    }

    /**
     * @return the endesc
     */
    public String getEndesc() {
        return endesc;
    }

    /**
     * @param endesc the endesc to set
     */
    public void setEndesc(String endesc) {
        this.endesc = endesc;
    }

    public String getFrdesc() {
        return frdesc;
    }

    public void setFrdesc(String frdesc) {
        this.frdesc = frdesc;
    }

    public String getEsdesc() {
        return esdesc;
    }

    public void setEsdesc(String mEsDesc) {
        this.esdesc = mEsDesc;
    }

    public String getRudesc() {
        return rudesc;
    }

    public void setRudesc(String rudesc) {
        this.rudesc = rudesc;
    }

    public String descLan(String lang) {
        if (StringUtils.isBlank(lang)) {
            return "";          // safe...
        }
        switch (lang) {
            case "it":
                return desc;
            case "en":
                return endesc;
            case "fr":
                return StringUtils.defaultIfEmpty(frdesc, endesc);
            case "es":
                return StringUtils.defaultIfEmpty(esdesc, endesc);
            case "ru":
                return StringUtils.defaultIfEmpty(rudesc, endesc);
            default:
                return endesc;
        }
    }

    /**
     * @return the mCode
     */
    public String getCode() {
        return code;
    }

    /**
     * @param mCode the mCode to set
     */
    public void setCode(String mCode) {
        this.code = mCode;
    }

    /**
     * @return the codeAssociated
     */
    public String getCodeAssociated() {
        return codeAssociated;
    }

    /**
     * @param codeAssociated the codeAssociated to set
     */
    public void setCodeAssociated(String codeAssociated) {
        this.codeAssociated = codeAssociated;
    }

    /**
     * @return the mAssociated
     */
    public Tags getmAssociated() {
        return mAssociated;
    }

    /**
     * @param mAssociated the mAssociated to set
     */
    public void setmAssociated(Tags mAssociated) {
        this.mAssociated = mAssociated;
    }

    public String getmGroup() {
        return mGroup;
    }

    public void setmGroup(String mGroup) {
        this.mGroup = mGroup;
    }

    public Boolean ismDefault() {
        return mDefault;
    }

    public void setmDefault(Boolean mDefault) {
        this.mDefault = mDefault;
    }

    public Integer getId() {
        return _id;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public Long getPanelId() {
        return panelId;
    }

    public void setPanelId(Long panelId) {
        this.panelId = panelId;
    }

    public String getEventTypeCode() {
        return eventTypeCode;
    }

    public void setEventTypeCode(String eventTypeCode) {
        this.eventTypeCode = eventTypeCode;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }
}
