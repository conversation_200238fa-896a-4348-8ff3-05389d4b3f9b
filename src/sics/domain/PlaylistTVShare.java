package sics.domain;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class PlaylistTVShare implements Serializable {
    
    private Long id;
    private Long userId;
    private Long playlistId;
    private Boolean editable;
    private Boolean played;
    
    // extra fields
    private User user; // usato per caricare la lista degli utenti a cui ho già condiviso la playlist ( mi serve nome e email )

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getPlaylistId() {
        return playlistId;
    }

    public void setPlaylistId(Long playlistId) {
        this.playlistId = playlistId;
    }

    public Boolean getEditable() {
        return editable;
    }

    public void setEditable(Boolean editable) {
        this.editable = editable;
    }

    public Boolean getPlayed() {
        return played;
    }

    public void setPlayed(<PERSON><PERSON><PERSON> played) {
        this.played = played;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}