package sics.domain;

import java.io.Serializable;
import java.sql.Timestamp;
import sics.helper.DateHelper;

/**
 *
 * <AUTHOR>
 */
public class PlaylistTV implements Serializable {
    
    private Long id;
    private Long userId;
    private Long groupsetId;
    private String name;
    private String description;
    private Timestamp date;
    private Long updateBy;
    private Timestamp updateDate;
    private String updateDescription;

    // extra columns
    private Long clipAmount;
    private User user;
    private Boolean shared;     // indica se qualcuno ti sta condividendo la playlist
    private Boolean sharing;    // indica se TU stai condividendo
    private Long lastUpdateBy;
    private Timestamp lastUpdateDate;
    private String lastUpdateDescription;
    
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }
    
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Timestamp getDate() {
        return date;
    }

    public void setDate(Timestamp date) {
        this.date = date;
    }

    public Long getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public Timestamp getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    public String getUpdateDescription() {
        return updateDescription;
    }

    public void setUpdateDescription(String updateDescription) {
        this.updateDescription = updateDescription;
    }

    public Long getClipAmount() {
        return clipAmount;
    }

    public void setClipAmount(Long clipAmount) {
        this.clipAmount = clipAmount;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Boolean getShared() {
        return shared;
    }

    public void setShared(Boolean shared) {
        this.shared = shared;
    }

    public Boolean getSharing() {
        return sharing;
    }

    public void setSharing(Boolean sharing) {
        this.sharing = sharing;
    }

    public Long getLastUpdateBy() {
        return lastUpdateBy;
    }

    public void setLastUpdateBy(Long lastUpdateBy) {
        this.lastUpdateBy = lastUpdateBy;
    }

    public Timestamp getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Timestamp lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getLastUpdateDescription() {
        return lastUpdateDescription;
    }

    public void setLastUpdateDescription(String lastUpdateDescription) {
        this.lastUpdateDescription = lastUpdateDescription;
    }
    
    public String getDateString() {
        return (getDate() != null) ? DateHelper.toString(getDate()) : "";
    }

    public String getDateFullString() {
        return (getDate() != null) ? DateHelper.toStringExt(getDate()) : "";
    }

    public String getDateMonthString() {
        return (getDate() != null) ? DateHelper.toMonthExt(getDate()) : "";
    }

    public String getDateDayString() {
        return (getDate() != null) ? DateHelper.toDayExt(getDate()) : "";
    }

    public String getDateYearString() {
        return (getDate() != null) ? DateHelper.toYearExt(getDate()) : "";
    }

    public String getTimeString() {
        return (getDate() != null) ? DateHelper.toStringTime(getDate()) : "";
    }
}