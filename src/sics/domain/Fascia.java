/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package sics.domain;

import java.awt.Color;

/**
 *
 */
public class Fascia {
	
	private Integer mRangeId;
	private String mCode;
	private String mNomeFascia;
    private String mEtichetta;
    private Float mValMin; 
    private Float mValMax;
//    private Color mColor;

	public Fascia(){};
	
    public Fascia(String mEtichetta, Float mValMin, Float mValMax, Color mColor) {
        this.mEtichetta = mEtichetta;
        this.mValMin = mValMin;
        this.mValMax = mValMax;
//        this.mColor = mColor;
    }
    
    public Fascia(String mEtichetta, Float mValMin, Float mValMax) {
        this.mEtichetta = mEtichetta;
        this.mValMin = mValMin;
        this.mValMax = mValMax;
    }
    
     /**
     * @return the mEtichetta
     */
    public String getEtichetta() {
        return mEtichetta;
    }

    /**
     * @param mEtichetta the mEtichetta to set
     */
    public void setEtichetta(String mEtichetta) {
        this.mEtichetta = mEtichetta;
    }

    /**
     * @return the mValMin
     */
    public Float getValMin() {
        return mValMin;
    }

    /**
     * @param mValMin the mValMin to set
     */
    public void setValMin(Float mValMin) {
        this.mValMin = mValMin;
    }

    /**
     * @return the mValMax
     */
    public Float getValMax() {
        return mValMax;
    }

    /**
     * @param mValMax the mValMax to set
     */
    public void setValMax(Float mValMax) {
        this.mValMax = mValMax;
    }

//    public Color getmColor() {
//        return mColor;
//    }
//
//    public void setmColor(Color mColor) {
//        this.mColor = mColor;
//    }

	public Integer getmRangeId() {
		return mRangeId;
	}

	public void setmRangeId(Integer mRangeId) {
		this.mRangeId = mRangeId;
	}

	public String getmCode() {
		return mCode;
	}

	public void setmCode(String mCode) {
		this.mCode = mCode;
	}

	public String getmNomeFascia() {
		return mNomeFascia;
	}

	public void setmNomeFascia(String mNomeFascia) {
		this.mNomeFascia = mNomeFascia;
	}
	
	
}
