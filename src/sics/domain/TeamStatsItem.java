package sics.domain;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class TeamStatsItem {
    
    private String type;
    private Map<String, Map<String, TeamStats>> rows;
    private Map<Long, String> columnDescription;
    private List<String> columnNames;

    // extra fields
    private Boolean isTabellino;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Map<String, Map<String, TeamStats>> getRows() {
        return rows;
    }

    public void setRows(Map<String, Map<String, TeamStats>> rows) {
        this.rows = rows;
    }

    public Map<Long, String> getColumnDescription() {
        return columnDescription;
    }

    public void setColumnDescription(Map<Long, String> columnDescription) {
        this.columnDescription = columnDescription;
    }

    public List<String> getColumnNames() {
        return columnNames;
    }

    public void setColumnNames(List<String> columnNames) {
        this.columnNames = columnNames;
    }

    public Boolean getIsTabellino() {
        return isTabellino;
    }

    public void setIsTabellino(Boolean isTabellino) {
        this.isTabellino = isTabellino;
    }
}
