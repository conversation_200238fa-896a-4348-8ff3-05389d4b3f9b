package sics.domain;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class TeamStatsTable implements Serializable {

    private Map<String, Map<String, TeamStats>> rows;
    private List<String> columnNameList;
    private List<String> columnNameListFormatted;

    public Map<String, Map<String, TeamStats>> getRows() {
        return rows;
    }

    public void setRows(Map<String, Map<String, TeamStats>> rows) {
        this.rows = rows;
    }

    public List<String> getColumnNameList() {
        return columnNameList;
    }

    public void setColumnNameList(List<String> columnNameList) {
        this.columnNameList = columnNameList;
    }

    public List<String> getColumnNameListFormatted() {
        return columnNameListFormatted;
    }

    public void setColumnNameListFormatted(List<String> columnNameListFormatted) {
        this.columnNameListFormatted = columnNameListFormatted;
    }
}