### direct log messages to stdout ###
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{ABSOLUTE} %5p %c{1}:%L - %m%n

### direct messages to file ###
log4j.appender.file=org.apache.log4j.FileAppender
log4j.appender.file.File=sicstv.log
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{ABSOLUTE} %5p %c{1}\:%L - %m%n
#   1. DEBUG
#   2. INFO
#   3. WARN
#   4. ERROR
#   5. FATAL
#   6. OFF

log4j.rootLogger=info, file, stdout
log4j.logger.noModule=ERROR
log4j.logger.org.springframework=ERROR
log4j.logger.org.springframework.security=ERROR

log4j.logger.com.ibatis=DEBUG
log4j.logger.com.ibatis.common.jdbc.ScriptRunner=DEBUG
log4j.logger.com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate=DEBUG
log4j.logger.java.sql.Connection=DEBUG
log4j.logger.java.sql.Statement=DEBUG
log4j.logger.java.sql.PreparedStatement=DEBUG
#log4j.logger.java.sql.ResultSet=DEBUG
